<?php
/**
 * Name:    School Settings
 * Author:  <PERSON><PERSON><PERSON><PERSON>ulusu
 * Company: NextElement
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Settings
 */
class Settings {

  protected $config_file_name;
  protected $CI;
  // protected $loadType = 'db';
  private $profile_edit_columns;
  private $profile_display_columns;
  private $payroll;
	public function __construct() {
    $this->CI =& get_instance();
    $this->__loadConfigSettings();
    $this->parentModulesArr = json_decode($this->CI->config->schoolSettings['parent_modules']['value']);
    $this->modulesArr = json_decode($this->CI->config->schoolSettings['modules']['value']);
    if(!empty($this->CI->config->schoolSettings['profile_edit_columns']['value']))
      $this->profile_edit_columns = json_decode($this->CI->config->schoolSettings['profile_edit_columns']['value']);
    if(!empty($this->CI->config->schoolSettings['parent_profile_display_columns']['value']))
      $this->profile_display_columns = json_decode($this->CI->config->schoolSettings['parent_profile_display_columns']['value']);
    if(!empty($this->CI->config->schoolSettings['payroll']['value']))
      $this->payroll = json_decode($this->CI->config->schoolSettings['payroll']['value']);

    /*if ($this->loadType == 'file') {
      $this->parentModulesArr = $this->CI->config->item('parent_modules', $this->config_file_name);
      $this->modulesArr = $this->CI->config->item('modules', $this->config_file_name);
    } else {
      $this->parentModulesArr = json_decode($this->CI->config->schoolSettings['parent_modules']['value']);
      $this->modulesArr = json_decode($this->CI->config->schoolSettings['modules']['value']);
      if(!empty($this->CI->config->schoolSettings['profile_edit_columns']['value']))
      $this->profile_edit_columns = json_decode($this->CI->config->schoolSettings['profile_edit_columns']['value']);  
     if(!empty($this->CI->config->schoolSettings['payroll']['value']))
      $this->payroll = json_decode($this->CI->config->schoolSettings['payroll']['value']);  
    }*/
  }

  private function __loadConfigSettings () {
    //Changing the config settings load from file to db
    $this->CI->load->model('Config_model');
    $result = $this->CI->Config_model->get_config_settings();

    $rResult = array();
    foreach ($result as $row) {
      $name = $row['name'];
      $rResult[$name] = $row;
    }
    $this->CI->config->schoolSettings = $rResult; 

    /*if ($loadType == 'file') {
      $this->config_file_name = CONFIG_ENV['school_setting'];
      $this->CI->config->load($this->config_file_name, TRUE);
    } else {
      $result =  $this->CI->db->get('config')->result_array();

      $rResult = array();
      foreach ($result as $row) {
        $name = $row['name'];
        $rResult[$name] = $row;
      }
      $this->CI->config->schoolSettings = $rResult;  
    }*/
      //  echo '<pre>';print_r($rResult);die();
  }
  
  public function getSetting($item, $isPostLogin=1, $supressError=0) {
    if (isset($this->CI->config->schoolSettings[$item])) {
      $configItem = (object)$this->CI->config->schoolSettings[$item];
      switch ($configItem->type) {
        case 'string':
        case 'boolean':
        case 'image':
          return $this->CI->config->schoolSettings[$item]['value'];
          break;
        case 'multiple':
          return json_decode($this->CI->config->schoolSettings[$item]['value']);
          break;
        case 'json':
          $rawJson = $this->CI->config->schoolSettings[$item]['value'];
          $cleanJson = preg_replace('/[^\x20-\x7E]/', '', $rawJson);
          $returnData = json_decode($cleanJson);
          return $returnData;
          break;
        case 'array':
          return $this->__marshalJSONAsArray($this->CI->config->schoolSettings[$item]['value']);
      }
      
    } else {
      if($isPostLogin) {
        //Commenting this out for you as this is creating noise uncessarily
        // if($this->CI->authorization->isSuperAdmin()) {
        //   $this->CI->session->set_flashdata('flashError', 'Configuration required : '.$item);
        // } else {
        //   //Commenting this out for you as this is creating noise uncessarily
        //   //if (!$supressError)
        //   //  trigger_error(json_encode('Configuration required : '.$item));
        // }
      }
      
      return false;
    }

    /*if ($this->loadType != 'file') {
      if (isset($this->CI->config->schoolSettings[$item])) {
        $configItem = (object)$this->CI->config->schoolSettings[$item];
        switch ($configItem->type) {
          case 'string':
          case 'boolean':
            return $this->CI->config->schoolSettings[$item]['value'];
            break;
          case 'multiple':
            return json_decode($this->CI->config->schoolSettings[$item]['value']);
            break;
          case 'json':
            return json_decode($this->CI->config->schoolSettings[$item]['value']);
            break;
          case 'array':
            return $this->__marshalJSONAsArray($this->CI->config->schoolSettings[$item]['value']);
        }
        
      } else {
        if($isPostLogin) {
          if($this->CI->authorization->isSuperAdmin()) {
            $this->CI->session->set_flashdata('flashError', 'Configuration required : '.$item);
          } else {
          Commenting this out for you as this is creating noise uncessarily
          if (!$supressError)
            trigger_error(json_encode('Configuration required : '.$item));
          }
        }
        
        return false;
      }
    } else {
      return $this->CI->config->item($item, $this->config_file_name);
    }*/
  }

  //Function to get the JSON itself without processing
  public function getSettingJSON($item) {
    if (isset($this->CI->config->schoolSettings[$item])) {
      return $this->CI->config->schoolSettings[$item]['value'];
    } else {
      return '';
    }
  }

  private function __marshalJSONAsArray($json) {
    $retArray = array();

    $jsonArray = json_decode($json);
    if(!empty($jsonArray)){
      foreach ($jsonArray as $jsonEle) {
        $retArray[$jsonEle->value] = $jsonEle->name;
      }
    }
    return $retArray;
  }

  public function getSettingValue($item, $key) {
    $value = '';
    $keyValues = $this->getSetting($item);
    foreach ($keyValues as $k => $val) {
      if ($k == $key) {
        return $val;
      }
    }
  }

  public function isModuleEnabled($moduleName) {
    //echo '<pre>';print_r($modulesArr);die();

    $found = 0;
    foreach ($this->modulesArr as $modTemp) {
      if ($modTemp == $moduleName) {
        $found = 1;
        break;
      }
    }
    return $found;
  }

  public function isParentModuleEnabled($moduleName) {

    $found = 0;
    foreach ($this->parentModulesArr as $modTemp) {
      if ($modTemp == $moduleName) {
        $found = 1;
        break;
      }
    }
    return $found;
  }

  public function isProfile_edit_enabled($moduleName)
  {
    $found = 0;
    if (!empty($this->profile_edit_columns)) {
      foreach ($this->profile_edit_columns as $modTemp) {
        if ($modTemp == $moduleName) {
          $found = 1;
          break;
        }
      }
    }
   
    return $found;
  }

  public function isProfile_profile_enabled($moduleName)
  {
    $found = 0;
    if (!empty($this->profile_display_columns)) {
      foreach ($this->profile_display_columns as $modTemp) {
        if ($modTemp == $moduleName) {
          $found = 1;
          break;
        }
      }
    }
   
    return $found;
  }

  public function payrollColumn($moduleName)
  {
    $found = 0;
    if (!empty($this->payroll)) {
      foreach ($this->payroll as $modTemp) {
        if ($modTemp == $moduleName) {
          $found = 1;
          break;
        }
      }
    }
   
    return $found;
  }

}