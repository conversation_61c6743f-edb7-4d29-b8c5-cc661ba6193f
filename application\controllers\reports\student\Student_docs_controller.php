<?php

/**
 * Name:    Oxygen
 * Author:  Anish 
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: Controller for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

 /**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_docs_controller extends CI_Controller {
    function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('report/Student_report_model');
    $this->load->model('student/Student_Model');
    $this->load->model('examination/Assessment_model','assessment_model');
    $this->load->library('filemanager');
   
  }
  public $columnList = [
    [
      'dispaly_name'=>'Student First Name',
      'data_input'=>'text',
      'column_name'=>'first_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Last Name',
      'data_input'=>'text',
      'column_name'=>'last_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Middle Name',
      'data_input'=>'text',
      'column_name'=>'student_middle_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Admission Number',
      'data_input'=>'text',
      'column_name'=>'admission_no',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Enrollment Number',
      'data_input'=>'text',
      'column_name'=>'enrollment_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Date of Birth',
      'data_input'=>'text',
      'column_name'=>'dob',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Gender',
      'data_input'=>'text',
      'column_name'=>'gender',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Nationality',
      'data_input'=>'text',
      'column_name'=>'nationality',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Religion',
      'data_input'=>'text',
      'column_name'=>'religion',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Category',
      'data_input'=>'text',
      'column_name'=>'category',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'mother_tongue',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Preferred Contact Number',
      'data_input'=>'text',
      'column_name'=>'preferred_contact_no',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling type',
      'data_input'=>'text',
      'column_name'=>'sibling_type',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Date of joining',
      'data_input'=>'text',
      'column_name'=>'date_of_joining',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Birth Taluk',
      'data_input'=>'text',
      'column_name'=>'birth_taluk',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Birth District',
      'data_input'=>'text',
      'column_name'=>'birth_district',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Caste',
      'data_input'=>'text',
      'column_name'=>'caste',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Aadhar Number',
      'data_input'=>'text',
      'column_name'=>'aadhar_no',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Staff Kid',
      'data_input'=>'text',
      'column_name'=>'has_staff',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Transfer Student',
      'data_input'=>'text',
      'column_name'=>'transfer',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Transfer From School',
      'data_input'=>'text',
      'column_name'=>'transfer_from_school',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Admission Status',
      'data_input'=>'text',
      'column_name'=>'curr_status',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Class Admitted to',
      'data_input'=>'text',
      'column_name'=>'class_admitted_to',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'PEN Number',
      'data_input'=>'text',
      'column_name'=>'pen_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Admssion Year',
      'data_input'=>'text',
      'column_name'=>'admission_year',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Emergency Info',
      'data_input'=>'text',
      'column_name'=>'emergency_info',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification code',
      'data_input'=>'text',
      'column_name'=>'identification_code',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Second language Choice',
      'data_input'=>'text',
      'column_name'=>'second_language_choice',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Third language Choice',
      'data_input'=>'text',
      'column_name'=>'third_language_choice',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Has Transport',
      'data_input'=>'text',
      'column_name'=>'has_transport',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Stats Number',
      'data_input'=>'text',
      'column_name'=>'sts_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Email',
      'data_input'=>'text',
      'column_name'=>'email',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'RF Id Number',
      'data_input'=>'text',
      'column_name'=>'rfid_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Preferred Parent',
      'data_input'=>'text',
      'column_name'=>'preferred_parent',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Language Spoken ',
      'data_input'=>'text',
      'column_name'=>'language_spoken',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Quota',
      'data_input'=>'text',
      'column_name'=>'quota',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Life time fee mode',
      'data_input'=>'text',
      'column_name'=>'life_time_fee_mode',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Joined Helium',
      'data_input'=>'text',
      'column_name'=>'joined_helium',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Name',
      'data_input'=>'text',
      'column_name'=>'vaccination_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_status',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Last Submitted Date',
      'data_input'=>'text',
      'column_name'=>'vaccination_last_submitted_date',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Verification Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_verification_status',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Supporting Document',
      'data_input'=>'text',
      'column_name'=>'vaccination_supporting_document',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Registration no',
      'data_input'=>'text',
      'column_name'=>'registration_no',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Ration Card no',
      'data_input'=>'text',
      'column_name'=>'ration_card_number',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Ration Card type ',
      'data_input'=>'text',
      'column_name'=>'ration_card_type',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Caste Income Certificate Number',
      'data_input'=>'text',
      'column_name'=>'caste_income_certificate_number',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Extra Curricular Activities',
      'data_input'=>'text',
      'column_name'=>'extracurricular_activities',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Sub caste',
      'data_input'=>'text',
      'column_name'=>'student_sub_caste',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Mobile Number',
      'data_input'=>'text',
      'column_name'=>'student_mobile_no',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Medium of Instruction ',
      'data_input'=>'text',
      'column_name'=>'medium_of_instruction',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Blood Group',
      'data_input'=>'text',
      'column_name'=>'blood_group',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Biometric Code',
      'data_input'=>'text',
      'column_name'=>'student_biometric_code',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Curriculum Currently Studying',
      'data_input'=>'text',
      'column_name'=>'curriculum_currently_studying',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Remarks',
      'data_input'=>'text',
      'column_name'=>'student_remarks',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Signature',
      'data_input'=>'text',
      'column_name'=>'student_signature',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Nick Name',
      'data_input'=>'text',
      'column_name'=>'nick_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Nick Name',
      'data_input'=>'text',
      'column_name'=>'nick_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Indian Visa Number',
      'data_input'=>'text',
      'column_name'=>'student_indian_visa_number',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Indian Visa Expiry Date',
      'data_input'=>'text',
      'column_name'=>'student_indian_visa_expiry_date',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification mark1',
      'data_input'=>'text',
      'column_name'=>'identification_mark1',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification mark2',
      'data_input'=>'text',
      'column_name'=>'identification_mark2',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling1 Name',
      'data_input'=>'text',
      'column_name'=>'sibling1_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling1 Occupation',
      'data_input'=>'text',
      'column_name'=>'sibling1_occupation',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling1 Mobile Number',
      'data_input'=>'text',
      'column_name'=>'sibling1_mobile_num',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling2 Name',
      'data_input'=>'text',
      'column_name'=>'sibling2_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling2 Occupation',
      'data_input'=>'text',
      'column_name'=>'sibling2_occupation',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling2 Mobile Number',
      'data_input'=>'text',
      'column_name'=>'sibling2_mobile_num',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling3 Name',
      'data_input'=>'text',
      'column_name'=>'sibling3_name',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling3 Occupation',
      'data_input'=>'text',
      'column_name'=>'sibling3_occupation',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sibling3 Mobile Number',
      'data_input'=>'text',
      'column_name'=>'sibling3_mobile_num',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Whatsapp Number',
      'data_input'=>'text',
      'column_name'=>'student_whatsapp_num',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Is Single Child',
      'data_input'=>'text',
      'column_name'=>'is_single_child',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Is Minority',
      'data_input'=>'text',
      'column_name'=>'is_minority',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Current Nearest Location',
      'data_input'=>'text',
      'column_name'=>'current_nearest_location',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Passport Number',
      'data_input'=>'text',
      'column_name'=>'passport_number',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Passport Issued Place',
      'data_input'=>'text',
      'column_name'=>'passport_issued_place',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Passport Validity',
      'data_input'=>'text',
      'column_name'=>'passport_validity',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Parent Marriage Anniversary',
      'data_input'=>'text',
      'column_name'=>'parents_marriage_anniversary',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Point of contact',
      'data_input'=>'text',
      'column_name'=>'point_of_contact',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Living With',
      'data_input'=>'text',
      'column_name'=>'student_living_with',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Last TC Number',
      'data_input'=>'text',
      'column_name'=>'last_tc_num',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Attempt',
      'data_input'=>'text',
      'column_name'=>'attempt',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Last Hall Ticket Number',
      'data_input'=>'text',
      'column_name'=>'last_hallticket_num',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'First Name',
      'data_input'=>'text',
      'column_name'=>'first_name',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Last Name',
      'data_input'=>'text',
      'column_name'=>'last_name',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Blood Group',
      'data_input'=>'text',
      'column_name'=>'blood_group',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'DOB',
      'data_input'=>'text',
      'column_name'=>'dob',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Qualification ',
      'data_input'=>'text',
      'column_name'=>'qualification',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Occupation ',
      'data_input'=>'text',
      'column_name'=>'occupation',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mobile Number ',
      'data_input'=>'text',
      'column_name'=>'mobile_no',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Aadhar Number ',
      'data_input'=>'text',
      'column_name'=>'aadhar_no',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Annual Income',
      'data_input'=>'text',
      'column_name'=>'annual_income',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Company',
      'data_input'=>'text',
      'column_name'=>'company',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'mother_tongue',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Email',
      'data_input'=>'text',
      'column_name'=>'email',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification code',
      'data_input'=>'text',
      'column_name'=>'identification_code',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Language Spoken',
      'data_input'=>'text',
      'column_name'=>'language_spoken',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Designation',
      'data_input'=>'text',
      'column_name'=>'designation',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Name',
      'data_input'=>'text',
      'column_name'=>'vaccination_name',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_status',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Verification Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_verification_status',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Pan Number',
      'data_input'=>'text',
      'column_name'=>'pan_number',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Home City',
      'data_input'=>'text',
      'column_name'=>'home_city',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Employee Id',
      'data_input'=>'text',
      'column_name'=>'employee_id',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Office LandLine Number',
      'data_input'=>'text',
      'column_name'=>'office_landline_number',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Alternate Email ID',
      'data_input'=>'text',
      'column_name'=>'alternate_email_id',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Whatsapp Number',
      'data_input'=>'text',
      'column_name'=>'whatsapp_num',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Bank Account Number',
      'data_input'=>'text',
      'column_name'=>'bank_account_num',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'First Name',
      'data_input'=>'text',
      'column_name'=>'first_name',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Last Name',
      'data_input'=>'text',
      'column_name'=>'last_name',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Blood Group',
      'data_input'=>'text',
      'column_name'=>'blood_group',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'DOB',
      'data_input'=>'text',
      'column_name'=>'dob',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Qualification ',
      'data_input'=>'text',
      'column_name'=>'qualification',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Occupation ',
      'data_input'=>'text',
      'column_name'=>'occupation',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mobile Number ',
      'data_input'=>'text',
      'column_name'=>'mobile_no',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Aadhar Number ',
      'data_input'=>'text',
      'column_name'=>'aadhar_no',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Annual Income',
      'data_input'=>'text',
      'column_name'=>'annual_income',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Company',
      'data_input'=>'text',
      'column_name'=>'company',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'mother_tongue',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Email',
      'data_input'=>'text',
      'column_name'=>'email',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification code',
      'data_input'=>'text',
      'column_name'=>'identification_code',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Guardian Photo',
      'data_input'=>'text',
      'column_name'=>'picture_url',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Language Spoken',
      'data_input'=>'text',
      'column_name'=>'language_spoken',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Designation',
      'data_input'=>'text',
      'column_name'=>'designation',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Name',
      'data_input'=>'text',
      'column_name'=>'vaccination_name',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_status',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Verification Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_verification_status',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Pan Number',
      'data_input'=>'text',
      'column_name'=>'pan_number',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Home City',
      'data_input'=>'text',
      'column_name'=>'home_city',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Employee Id',
      'data_input'=>'text',
      'column_name'=>'employee_id',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Office LandLine Number',
      'data_input'=>'text',
      'column_name'=>'office_landline_number',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Alternate Email ID',
      'data_input'=>'text',
      'column_name'=>'alternate_email_id',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Whatsapp Number',
      'data_input'=>'text',
      'column_name'=>'whatsapp_num',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Bank Account Number',
      'data_input'=>'text',
      'column_name'=>'bank_account_num',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'First Name',
      'data_input'=>'text',
      'column_name'=>'first_name',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Last Name',
      'data_input'=>'text',
      'column_name'=>'last_name',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Blood Group',
      'data_input'=>'text',
      'column_name'=>'blood_group',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'DOB',
      'data_input'=>'text',
      'column_name'=>'dob',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Qualification ',
      'data_input'=>'text',
      'column_name'=>'qualification',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Occupation ',
      'data_input'=>'text',
      'column_name'=>'occupation',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mobile Number ',
      'data_input'=>'text',
      'column_name'=>'mobile_no',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Aadhar Number ',
      'data_input'=>'text',
      'column_name'=>'aadhar_no',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Annual Income',
      'data_input'=>'text',
      'column_name'=>'annual_income',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Driver Photo',
      'data_input'=>'text',
      'column_name'=>'picture_url',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Company',
      'data_input'=>'text',
      'column_name'=>'company',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'mother_tongue',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Email',
      'data_input'=>'text',
      'column_name'=>'email',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Identification code',
      'data_input'=>'text',
      'column_name'=>'identification_code',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Language Spoken',
      'data_input'=>'text',
      'column_name'=>'language_spoken',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Designation',
      'data_input'=>'text',
      'column_name'=>'designation',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Name',
      'data_input'=>'text',
      'column_name'=>'vaccination_name',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_status',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Verification Status',
      'data_input'=>'text',
      'column_name'=>'vaccination_verification_status',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Pan Number',
      'data_input'=>'text',
      'column_name'=>'pan_number',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Home City',
      'data_input'=>'text',
      'column_name'=>'home_city',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Employee Id',
      'data_input'=>'text',
      'column_name'=>'employee_id',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Office LandLine Number',
      'data_input'=>'text',
      'column_name'=>'office_landline_number',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Alternate Email ID',
      'data_input'=>'text',
      'column_name'=>'alternate_email_id',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Whatsapp Number',
      'data_input'=>'text',
      'column_name'=>'whatsapp_num',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Driving License Number',
      'data_input'=>'text',
      'column_name'=>'dl_number',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Driving License File Upload',
      'data_input'=>'text',
      'column_name'=>'dl_file',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Vaccination Name',
      'data_input'=>'text',
      'column_name'=>'vaccination_name',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Roll Number',
      'data_input'=>'text',
      'column_name'=>'roll_no',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Semister',
      'data_input'=>'text',
      'column_name'=>'semester',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Boarding',
      'data_input'=>'text',
      'column_name'=>'boarding',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Admission Type',
      'data_input'=>'text',
      'column_name'=>'admission_type',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Board',
      'data_input'=>'text',
      'column_name'=>'board',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Medium',
      'data_input'=>'text',
      'column_name'=>'medium',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Donor',
      'data_input'=>'text',
      'column_name'=>'donor',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student House',
      'data_input'=>'text',
      'column_name'=>'student_house',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Fee Mode',
      'data_input'=>'text',
      'column_name'=>'fee_mode',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Has Transport Km',
      'data_input'=>'text',
      'column_name'=>'has_transport_km',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Stop',
      'data_input'=>'text',
      'column_name'=>'stop',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Pickup Mode',
      'data_input'=>'text',
      'column_name'=>'pickup_mode',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'After School Sport',
      'data_input'=>'text',
      'column_name'=>'after_school_sport',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'After School Sport Days',
      'data_input'=>'text',
      'column_name'=>'after_school_sport_days',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Combination',
      'data_input'=>'text',
      'column_name'=>'combination',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Combination Name',
      'data_input'=>'text',
      'column_name'=>'combination_id',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Terminate Date',
      'data_input'=>'text',
      'column_name'=>'terminate_date',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Terminate Remarks',
      'data_input'=>'text',
      'column_name'=>'terminate_remarks',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Photo',
      'data_input'=>'text',
      'column_name'=>'picture_url',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Family Photo',
      'data_input'=>'text',
      'column_name'=>'family_picture_url',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Id',
      'data_input'=>'text',
      'column_name'=>'id',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Parent Photo',
      'data_input'=>'text',
      'column_name'=>'picture_url',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'TC Number',
      'data_input'=>'text',
      'column_name'=>'tc_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Hall Ticket Number',
      'data_input'=>'text',
      'column_name'=>'hall_ticket_num',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Alpha Roll Number',
      'data_input'=>'text',
      'column_name'=>'alpha_rollnum',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Profile Confirmed',
      'data_input'=>'text',
      'column_name'=>'profile_confirmed',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Profile Confirmed Date',
      'data_input'=>'text',
      'column_name'=>'profile_confirmed_date',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Is RTE',
      'data_input'=>'text',
      'column_name'=>'is_rte',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Drop Stop',
      'data_input'=>'text',
      'column_name'=>'drop_stop',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Present Address',
      'data_input'=>'text',
      'column_name'=>'present_address',
      'tabs'=>'address_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Permanent Address',
      'data_input'=>'text',
      'column_name'=>'permanent_address',
      'tabs'=>'address_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Unformatted Address',
      'data_input'=>'text',
      'column_name'=>'unformatted_address',
      'tabs'=>'address_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'School Name',
      'data_input'=>'text',
      'column_name'=>'school_name',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Class',
      'data_input'=>'text',
      'column_name'=>'class',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Board',
      'data_input'=>'text',
      'column_name'=>'board',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Board Other',
      'data_input'=>'text',
      'column_name'=>'board_other',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'School Address',
      'data_input'=>'text',
      'column_name'=>'school_address',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Total Marks',
      'data_input'=>'text',
      'column_name'=>'total_marks',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Total Marks Scored',
      'data_input'=>'text',
      'column_name'=>'total_marks_scored',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Total Percentage',
      'data_input'=>'text',
      'column_name'=>'total_percentage',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Medium of Instruction',
      'data_input'=>'text',
      'column_name'=>'medium_of_instruction',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Registartion',
      'data_input'=>'text',
      'column_name'=>'registration_no',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Subject wise percentage',
      'data_input'=>'text',
      'column_name'=>'sub_percent',
      'tabs'=>'previous_school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Sts Number',
      'data_input'=>'text',
      'column_name'=>'sts_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Admission Year',
      'data_input'=>'text',
      'column_name'=>'admission_acad_year_id',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Country Code',
      'data_input'=>'text',
      'column_name'=>'country_code',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Country Code',
      'data_input'=>'text',
      'column_name'=>'country_code',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Nationality',
      'data_input'=>'text',
      'column_name'=>'nationality',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Parent RFID',
      'data_input'=>'text',
      'column_name'=>'rfid_number',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Guardian RFID',
      'data_input'=>'text',
      'column_name'=>'rfid_number',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Driver RFID',
      'data_input'=>'text',
      'column_name'=>'rfid_number',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'School to home Distance',
      'data_input'=>'text',
      'column_name'=>'distance_from_school_to_home_in_km',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'caste',
      'data_input'=>'text',
      'column_name'=>'caste',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Religion',
      'data_input'=>'text',
      'column_name'=>'religion',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Mother Username',
      'data_input'=>'text',
      'column_name'=>'username',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Father Username',
      'data_input'=>'text',
      'column_name'=>'username',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Second Language Currently Studying',
      'data_input'=>'text',
      'column_name'=>'second_language_currently_studying',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'First Language Choice',
      'data_input'=>'text',
      'column_name'=>'first_language_choice',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Transportation Mode',
      'data_input'=>'text',
      'column_name'=>'transport_mode',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Transportation Additional Details',
      'data_input'=>'text',
      'column_name'=>'transportation_additional_details',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Udise Number',
      'data_input'=>'text',
      'column_name'=>'udise_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Enrolled in different institute Earlier',
      'data_input'=>'text',
      'column_name'=>'did_they_enrolled_in_different_institute_earlier',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Hobbies',
      'data_input'=>'text',
      'column_name'=>'student_hobbies',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Area of improvement',
      'data_input'=>'text',
      'column_name'=>'student_area_of_improvement',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Student Area of Strength',
      'data_input'=>'text',
      'column_name'=>'student_area_of_strength',
      'tabs'=>'personal_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Type of Organization',
      'data_input'=>'text',
      'column_name'=>'type_of_organization',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued By',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_by',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued By',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_by',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued Date',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_on',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued Date',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_on',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued remarks',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_remarks',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued remarks',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_remarks',
      'tabs'=>'guardian_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued Date',
      'data_input'=>'text',
      'column_name'=>'id_card_issue_on',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued By',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_by',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued remarks',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_remarks',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued Date',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_on',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued by',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_by',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Id card issued remarks',
      'data_input'=>'text',
      'column_name'=>'id_card_issued_remarks',
      'tabs'=>'driver_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Username',
      'data_input'=>'text',
      'column_name'=>'username',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Apaar ID',
      'data_input'=>'text',
      'column_name'=>'apaar_id',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'caste income certificate number',
      'data_input'=>'text',
      'column_name'=>'caste_income_certificate_number',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'previous school details',
      'data_input'=>'text',
      'column_name'=>'previous_schooling_details',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'High Quality URL',
      'data_input'=>'text',
      'column_name'=>'high_quality_picture_url',
      'tabs'=>'parent_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'High Quality URL',
      'data_input'=>'text',
      'column_name'=>'high_quality_picture_url',
      'tabs'=>'school_info',
      'default'=>1
    ],
    [
      'dispaly_name'=>'Section',
      'data_input'=>'text',
      'column_name'=>'class_section_id',
      'tabs'=>'school_info',
      'default'=>1
    ]
  ];

  public $studentProfileFields = [
    [
      'display_name'=>'Student Photo',
      'data_input'=>'text',
      'column_name'=>'STUDENT_PHOTO',
      'tabs'=>'student_info',
      'default'=>1
    ],
    [
      'display_name'=>'Admission Number',
      'data_input'=>'text',
      'column_name'=>'ADMISSION_NO',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Class Section',
      'data_input'=>'text',
      'column_name'=>'CLASS_SECTION',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mail',
      'data_input'=>'text',
      'column_name'=>'STUDENT_EMAIL',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Initial Password',
      'data_input'=>'text',
      'column_name'=>'INITIAL_PASSWORD',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'STUDENT_MOTHER_TONGUE',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Name',
      'data_input'=>'text',
      'column_name'=>'STUDENT_NAME',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Gender',
      'data_input'=>'text',
      'column_name'=>'STUDENT_GENDER',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Address',
      'data_input'=>'text',
      'column_name'=>'STUDENT_ADDRESS',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Blood Group',
      'data_input'=>'text',
      'column_name'=>'STUDENT_BLOOD_GROUP',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Nationality',
      'data_input'=>'text',
      'column_name'=>'STUDENT_NATIONALITY',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Category',
      'data_input'=>'text',
      'column_name'=>'CATEGORY',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Caste',
      'data_input'=>'text',
      'column_name'=>'STUDENT_CASTE',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Religion',
      'data_input'=>'text',
      'column_name'=>'STUDENT_RELIGION',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Aadhar',
      'data_input'=>'text',
      'column_name'=>'STUDENT_AADHAR',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Stop',
      'data_input'=>'text',
      'column_name'=>'STUDENT_STOP',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Pickup Mode',
      'data_input'=>'text',
      'column_name'=>'STUDENT_PICKUP_MODE',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'COMBINATION',
      'data_input'=>'text',
      'column_name'=>'COMBINATION',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Photo',
      'data_input'=>'text',
      'column_name'=>'FATHER_PHOTO',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Name',
      'data_input'=>'text',
      'column_name'=>'FATHER_NAME',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Address',
      'data_input'=>'text',
      'column_name'=>'FATHER_ADDRESS',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Qualification',
      'data_input'=>'text',
      'column_name'=>'FATHER_QUALIFICATION',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Occupation',
      'data_input'=>'text',
      'column_name'=>'FATHER_OCCUPATION',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Contact Number',
      'data_input'=>'text',
      'column_name'=>'FATHER_CONTACT_NO',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Email',
      'data_input'=>'text',
      'column_name'=>'FATHER_EMAIL',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Annual Income',
      'data_input'=>'text',
      'column_name'=>'FATHER_ANNUAL_INCOME',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Company',
      'data_input'=>'text',
      'column_name'=>'FATHER_COMPANY',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Aadhar',
      'data_input'=>'text',
      'column_name'=>'FATHER_AADHAR',
      'tabs'=>'father_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Photo',
      'data_input'=>'text',
      'column_name'=>'MOTHER_PHOTO',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Name',
      'data_input'=>'text',
      'column_name'=>'MOTHER_NAME',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Address',
      'data_input'=>'text',
      'column_name'=>'MOTHER_ADDRESS',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Qualification',
      'data_input'=>'text',
      'column_name'=>'MOTHER_QUALIFICATION',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Occupation',
      'data_input'=>'text',
      'column_name'=>'MOTHER_OCCUPATION',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Contact Number',
      'data_input'=>'text',
      'column_name'=>'MOTHER_CONTACT_NO',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Email',
      'data_input'=>'text',
      'column_name'=>'MOTHER_EMAIL',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Annual Income',
      'data_input'=>'text',
      'column_name'=>'MOTHER_ANNUAL_INCOME',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Aadhar',
      'data_input'=>'text',
      'column_name'=>'MOTHER_AADHAR',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Mother Company',
      'data_input'=>'text',
      'column_name'=>'MOTHER_COMPANY',
      'tabs'=>'mother_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Date of Birth',
      'data_input'=>'text',
      'column_name'=>'STUDENT_DOB',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Guardian Photo',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_PHOTO',
      'tabs'=>'guardian_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Guardian Name',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_NAME',
      'tabs'=>'guardian_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Guardian Contact Number',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_CONTACT_NO',
      'tabs'=>'guardian_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Guardian Email',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_EMAIL',
      'tabs'=>'guardian_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student House',
      'data_input'=>'text',
      'column_name'=>'STUDENT_HOUSE',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Remarks',
      'data_input'=>'text',
      'column_name'=>'STUDENT_REMARKS',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Family Photo',
      'data_input'=>'text',
      'column_name'=>'FAMILY_PHOTO',
      'tabs'=>'family_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Electives',
      'data_input'=>'text',
      'column_name'=>'ELECTIVES',
      'tabs'=>'Electives',
      'default'=>1
  
    ],
    [
      'display_name'=>'Name as Per Aadhar',
      'data_input'=>'text',
      'column_name'=>'NAME_AS_PER_AADHAR',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Enrollment Number',
      'data_input'=>'text',
      'column_name'=>'ENROLLMENT_NUMBER',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mobile Number',
      'data_input'=>'text',
      'column_name'=>'STUDENT_MOBILE_NUMBER',
      'tabs'=>'student_info',
      'default'=>1
  
    ]
  ];
  

    public function student_wise_student_document_report(){
        $data['class_list'] = $this->Student_report_model->get_student_category_class();
        $data['doc_types'] = $this->Student_report_model->get_document_types();
        $data['config_doc_names'] = array();
        if($this->settings->getSetting('student_documents_name')){
          $data['config_doc_names'] = json_decode($this->settings->getSetting('student_documents_name'));
          $data['config_doc_names'] = array_map('strtolower', $data['config_doc_names']);
        }
        $data['main_content'] = 'reports/student/student_wise_student_document_report';
        $this->load->view('inc/template', $data);
    }

    public function student_wise_aadhar_report(){
      $data['main_content'] = 'reports/student/student_wise_aadhar_report';
      $this->load->view('inc/template', $data);
    }

    public function student_wise_pancard_report(){
      $data['main_content'] = 'reports/student/student_wise_pancard_report';
      $this->load->view('inc/template', $data);
    }

    public function student_profile_display_fiedls(){
      $columnList = $this->columnList;
      $profile_conifg = [];
      foreach ($columnList as $tabs => $value) {
        $profile_conifg[$value['tabs']][$value['column_name']] = $value['default'];
      }

      $profile_displayName = [];
      foreach ($columnList as $tabs => $value) {
        $profile_displayName[$value['tabs']][$value['column_name']] = $value['dispaly_name'];
      }
      $data['student_fields'] = $profile_conifg;
      $data['student_fields_display_label'] = $profile_displayName;
      $selectedOptions = $this->Student_report_model->get_config_student_display_fileds();
     
  		$dbEnabed = [];
  		foreach ($selectedOptions as $key => $enabled) {
  			$dbEnabed = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields'] = (array) $dbEnabed;
      $data['main_content'] = 'reports/student/student_profile_display_fiedls';
      $this->load->view('inc/template', $data);
    }

    public function student_profile_edit_fiedls(){
      $columnList = $this->columnList;
      $profile_conifg = [];
      foreach ($columnList as $tabs => $value) {
        $profile_conifg[$value['tabs']][$value['column_name']] = $value['default'];
      }

      $profile_displayName = [];
      foreach ($columnList as $tabs => $value) {
        $profile_displayName[$value['tabs']][$value['column_name']] = $value['dispaly_name'];
      }
      $data['student_fields'] = $profile_conifg;
      $data['student_fields_display_label'] = $profile_displayName;
      $selectedOptions = $this->Student_report_model->get_config_student_edit_fileds();
     
  		$dbEnabed = [];
  		foreach ($selectedOptions as $key => $enabled) {
  			$dbEnabed = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields'] = (array) $dbEnabed;
      $data['main_content'] = 'reports/student/student_profile_edit_fiedls';
      $this->load->view('inc/template', $data);
    }

    public function student_document_types(){
      $data['main_content'] = 'reports/student/student_document_types_page';
      $this->load->view('inc/template', $data);
    }

    public function getSectionData() {
    $classId = $_POST['classId'];
    $sections = $this->assessment_model->getSectionData($classId);
    echo json_encode($sections);
  }

  public function get_document_by_student() {
    $class_id = $_POST['class_id'];
    $section_id = $_POST['section_id'];
    $admission_type = $_POST['admission_type'];
    $document_names = $_POST['document_names'];
    $admission_status = $_POST['admission_status'];
     $documents = $this->Student_report_model->get_document_by_student($class_id,$admission_type, $section_id,$document_names,$admission_status);
    echo json_encode($documents);
  }

   public function get_document_by_student_classwise() {
    $class_id = $_POST['class_id'];
    $admission_type = $_POST['admission_type'];
    $document_names = $_POST['document_names'];
    $admission_status = $_POST['admission_status'];
    $documents = $this->Student_report_model->get_document_by_student($class_id,$admission_type,'',$document_names,$admission_status);
    echo json_encode($documents);
  }

  public function get_class_section_names(){
    $result = $this->Student_report_model->get_class_names();
    echo json_encode($result);
   
  }

  public function get_class_names(){
    $result = $this->Student_report_model->get_class_list_all($this->acad_year->getAcadYearId());
    echo json_encode($result);
   
  }

  public function get_sections_names_by_class_id(){
    $result = $this->Student_report_model->get_sections_names_by_class_id($_POST['class_id']);
    echo json_encode($result);
  }

  public function get_class_section_student_data(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->Student_report_model->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function get_class_section_student_data_new(){
    $result = $this->Student_report_model->get_class_section_student_data($_POST['class_id'],$_POST['section_id']);
    echo json_encode($result);
  }

  public function get_aadar_documents(){
    $result=$this->Student_report_model->get_stu_parent_documents($_POST);
    echo json_encode($result);
  }

  public function get_student_documnets(){
    $result=$this->Student_report_model->get_student_documnets($_POST);
    // echo '<pre>';print_r($result);die();
    echo json_encode($result);
  }

  public function update_aadhar_status(){
    echo $this->Student_report_model->update_aadhar_status($_POST);
  }

  public function get_pan_card_documnets(){
    $result=$this->Student_report_model->get_pan_card_documnets($_POST);
    echo json_encode($result);
  }

  public function update_parent_pancard_status(){
    echo $this->Student_report_model->update_parent_pancard_status($_POST);
  }

  public function student_profile_display_config_append(){
    $result = $this->Student_report_model->insert_student_configure_fields();
	    if($result){
	      $this->session->set_flashdata('flashSuccess', 'Selected Fields Enabled Successfully.');
	      redirect('reports/student/Student_docs_controller/student_profile_display_fiedls');
	    }else{
	      $this->session->set_flashdata('flashError', 'Something went Wrong.');
	      redirect('reports/student/Student_docs_controller/student_profile_display_fiedls');
	    }
  }
  public function student_profile_edit_config_append(){
    $result = $this->Student_report_model->insert_student_edit_fields();
	    if($result){
	      $this->session->set_flashdata('flashSuccess', 'Selected Fields Enabled Successfully.');
	      redirect('reports/student/Student_docs_controller/student_profile_edit_fiedls');
	    }else{
	      $this->session->set_flashdata('flashError', 'Something went Wrong.');
	      redirect('reports/student/Student_docs_controller/student_profile_edit_fiedls');
	    }
  }

  public function add_student_document_types(){
    $result = $this->Student_report_model->insert_student_document_types($_POST);
    echo $result;
  }

  public function get_student_document_types(){
    $result = $this->Student_report_model->get_student_document_types();
    echo json_encode($result);
  }

  public function delete_document_type(){
    echo $this->Student_report_model->delete_document_type($_POST);
  }

  public function edit_student_document_types(){
    echo $this->Student_report_model->edit_student_document_types($_POST);
  }
  public function student_profile_display_required_fiedls()
    {
        $columnList = $this->columnList;
        $profile_conifg = [];
        foreach ($columnList as $tabs => $value) {
            $profile_conifg[$value['tabs']][$value['column_name']] = $value['default'];
        }

        $profile_displayName = [];
        foreach ($columnList as $tabs => $value) {
            $profile_displayName[$value['tabs']][$value['column_name']] = $value['dispaly_name'];
        }
        $data['student_fields'] = $profile_conifg;
        $data['student_fields_display_label'] = $profile_displayName;

        $selectedOptions = $this->Student_report_model->get_student_config_required_fields();
        $dbEnabed = [];
        foreach ($selectedOptions as $key => $enabled) {
            $dbEnabed = json_decode($enabled->value);
        }
        $data['selected_mandatory_fields'] = (array) $dbEnabed;
        // echo "<pre>"; print_r($data['selected_mandatory_fields']['school_info']);die();
        $data['main_content'] = 'reports/student/student_profile_display_required_fields.php';
        $this->load->view('inc/template', $data);
    }

    public function student_profile_display_required_config_append()
    {
        $result = $this->Student_report_model->insert_student_configure_required_fields();
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'Selected Fields Enabled Successfully.');
            redirect('reports/student/Student_docs_controller/student_profile_display_required_fiedls');
        } else {
            $this->session->set_flashdata('flashError', 'Something went Wrong.');
            redirect('reports/student/Student_docs_controller/student_profile_display_required_fiedls');
        }
    }

    public function manage_profile_confirm(){
      $data['sectionList'] = $this->Student_report_model->getClassNames();
      $data['main_content'] = 'reports/student/manage_profile_confirm_view';
      $this->load->view('inc/template', $data);
    }

    public function load_student_data(){
      $data=$this->Student_report_model->load_student_data($_POST);
      echo json_encode($data);
    }

    public function change_confirm_status(){
      if($_POST['status'] =='Unlock'){
        $old_val = 'Profile Status : Locked';
        $new_val = 'Profile Status : Unlocked';
      }else{
        $old_val = 'Profile Status : Unlocked';
        $new_val = 'Profile Status : Locked';
      }
      $this->Student_Model->store_edit_history($_POST['student_id'],$old_val,$new_val);
      echo $this->Student_report_model->change_confirm_status($_POST);
    }

    public function configure_parent_fields(){

      $studentProfileFields = $this->studentProfileFields;
      
      // Initialize arrays to store configuration and display names
      $profile_config = [];
      $profile_displayName = [];

      // Loop through each field in $studentProfileFields
      foreach ($studentProfileFields as $value) {
          // Extract relevant information
          $tabs = $value['tabs'];
          $column_name = $value['column_name'];
          $display_name = $value['display_name'];
          $default = $value['default'];

          // Populate $profile_config with default values
          $profile_config[$tabs][$column_name] = $default;

          // Populate $profile_displayName with display names
          $profile_displayName[$tabs][$column_name] = $display_name;
      }
      $data['student_fields_display_label'] = $profile_displayName;

      $selectedOptionsView = $this->Student_report_model->configure_parent_side_fields_view();
      $selectedOptionsEdit = $this->Student_report_model->configure_parent_side_fields_edit();
      $selectedOptionsMandatory = $this->Student_report_model->configure_parent_side_fields_Mandatory();
     
  		$dbEnabedView = [];
  		foreach ($selectedOptionsView as $key => $enabled) {
  			$dbEnabedView = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields_view'] = (array) $dbEnabedView;

      $dbEnabedEdit = [];
  		foreach ($selectedOptionsEdit as $key => $enabled) {
  			$dbEnabedEdit = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields_edit'] = (array) $dbEnabedEdit;

      $dbEnabedMandatory = [];
  		foreach ($selectedOptionsMandatory as $key => $enabled) {
  			$dbEnabedMandatory = json_decode($enabled->value);
  		}
  		$data['selected_enabled_fields_mandatory'] = (array) $dbEnabedMandatory;

      $data['student_fields'] = $profile_config;
      $data['student_fields_display_label'] = $profile_displayName;
      $data['main_content'] = 'reports/student/configure_parent_fields';
      $this->load->view('inc/template', $data);
    }

    public function get_all_student_profile_fields(){
      $data=$this->Student_report_model->get_all_student_profile_fields();
      echo $data;
    }
    public function student_profile_display_config(){
      $result = $this->Student_report_model->student_profile_display_config();
      if($result){
        $this->session->set_flashdata('flashSuccess', 'Selected Fields Enabled Successfully.');
        redirect('reports/student/Student_docs_controller/configure_parent_fields');
      }else{
          $this->session->set_flashdata('flashError', 'Something went Wrong.');
          redirect('reports/student/Student_docs_controller/configure_parent_fields');
        }
    }
}
?>