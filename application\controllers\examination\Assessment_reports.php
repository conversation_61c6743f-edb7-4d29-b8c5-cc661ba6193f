<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Assessment_reports extends CI_Controller {

  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
    $this->load->model('examination/Assessment_model','assessment_model');
    $this->load->model('examination/Assessment_reports_model','assessment_reports');
  }

  public function performanceAnalysis($class_id=1){
    $data['classList'] = $this->assessment_model->getClassess();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/performance_index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/performance_index_mobile';
    }else{
      $data['main_content'] = 'examination/reports/performance_index';
    }
    $this->load->view('inc/template', $data);
  }

  public function performanceAnalysisNew() {
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/performance/index';
    $this->load->view('inc/template', $data);
  }

  public function getSectionsEntitiesAssessmentsFromClass(){
    $classId = $_POST['classId'];
    // $is_checked = $_POST['is_checked'];
    $data['sections'] = $this->assessment_reports->getSectionsFromClass($classId);
    $data['assessments'] = $this->assessment_reports->getClsAssessments_new($classId, 'both');
    echo json_encode($data);
  }

  public function getSubjectsByAssessments(){
    $assIds = $_POST['assIds'];
    $data['groups'] = $this->assessment_reports->getSubjectsByAssessment_new($assIds, 'group');
    $data['components'] = $this->assessment_reports->getSubjectsByAssessment_new($assIds, 'component');
    $data['electives'] = $this->assessment_reports->getSubjectsByAssessment_new($assIds, 'elective');
    echo json_encode($data);
  }

  public function performanceReport() {
    $input = $this->input->post();
    $this->load->model('class_section');
    $data['class'] = $this->class_section->getClassName($input['classId']);
    // $data['assessments'] = $this->assessment_model->getAssDetails($input['assessment']);
    $perfData = $this->assessment_reports->getComponentPerformance($input['assessment'], $input['classId'], $input['components']);
    $data['perfData'] = array();
    $data['subjects'] = array();
    foreach ($perfData as $key => $value) {
      if(!array_key_exists($value->entity_id, $data['subjects'])) {
        $data['subjects'][$value->entity_id] = array();
        $data['subjects'][$value->entity_id]['name'] = $value->entity_name;
        $data['subjects'][$value->entity_id]['assessments'] = array();
      }
      $data['subjects'][$value->entity_id]['assessments'][$value->assId] = $value->assessment_name;
      $average = round($value->marksSum/($value->count - $value->absentees), 2);
      $data['perfData'][$value->entity_id][$value->csName][$value->assId] = array('average' => $average, 'max' => $value->highest, 'min' => $value->lowest, 'count' => $value->count, 'absent' => $value->absentees, 'belowAverage' => $value->belowAverage, 'total_marks' => $value->total_marks);
    }
    // echo '<pre>'; print_r($input);
    // echo '<pre>'; print_r($data); die();
    $data['main_content'] = 'examination/reports/performance/report';
    $this->load->view('inc/template_fee', $data);
  }

  public function getPerformanceData(){
    $input = $this->input->post();
    $assId = $input['assessment'];
    $data['average_percentage'] = isset($input['average_percentage']) ? $input['average_percentage'] : 35; // [IAIS] | [2024-09-11 22:41:24] | unknown | [error_statistics_string] | Undefined index: average_percentage (/home/<USER>/oxygenv2/application/controllers/examination/Assessment_reports.php:80) | (Notice:8) | *************:Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) 
    $data['assessment'] = $this->assessment_model->getAssessmentById($assId);
    if(empty($data['assessment'])) {
      redirect('examination/assessment_reports/performanceAnalysis');
    }
    if(!isset($input['components']) && !isset($input['groups'])) {
      $this->session->set_flashdata('flashError', 'Subjects not selected');
      redirect('examination/assessments/performanceAnalysis');
    }
    $classId = $input['classId'];
    // $data['class_teacher']= $this->assessment_model->get_class_teacher($classId);
    $data['class_id'] = $classId;
    $data['classSelected'] = $classId;
    $data['perfData'] = array();
    $belowAverage = array();
    if(isset($input['components'])) {
      $perfData = $this->assessment_reports->getComponentPerfData($assId, $classId, $input['components'], $data['average_percentage']);
      $belowAverage = $this->assessment_reports->getComponentBelowAvg($assId, $classId, $input['components'], $data['average_percentage']);
      
      foreach ($perfData as $key => $value) {
        $data['perfData'][$value->diplay_name][$value->csName] = array('class_teacher' => $value->class_teacher, 'average' => $value->average, 'max' => $value->highest, 'min' => $value->lowest, 'count' => $value->count, 'absent' => $value->absentees, 'belowAverage' => $value->belowAverage);
      }
    }

    $multiplier= $data['average_percentage'] / 100;
    $gData = array();
    $belowAvg = array();
    if(isset($input['groups'])) {
      
      $compIds = $this->assessment_reports->getComponentsByGids($input['groups']);
      
      $componentIds = array();
      foreach ($compIds as $key => $value) {
        array_push($componentIds, $value->id);
      }
      $perfData = $this->assessment_reports->getGroupPerfData($assId, $classId, $input['groups']);
      
      // echo '<pre>'; print_r($perfData); die();

      foreach ($perfData as $key => $value) {
        if(!array_key_exists($value->display_name, $gData))
          $gData[$value->display_name] = array();
        if(!array_key_exists($value->csName, $gData[$value->display_name]))
          $gData[$value->display_name][$value->csName] = array('class_teacher' => $value->class_teacher, 'sum' => 0, 'max' => 0, 'min' => $value->total_marks, 'absent' => 0, 'belowAverage' => 0, 'count' => 0, 'avg' => ($value->total_marks * $multiplier));
        if($value->count == $value->absent)
          $gData[$value->display_name][$value->csName]['absent'] += 1;

        if($value->sumMarks > $gData[$value->display_name][$value->csName]['max'])
          $gData[$value->display_name][$value->csName]['max'] = $value->sumMarks;

        if($value->sumMarks < $gData[$value->display_name][$value->csName]['min'] && $value->count != $value->absent)
          $gData[$value->display_name][$value->csName]['min'] = $value->sumMarks;

       
        if($value->sumMarks < $gData[$value->display_name][$value->csName]['avg']) {
         if($value->count != $value->absent) {
          $gData[$value->display_name][$value->csName]['belowAverage'] += 1;
         }
          if($value->count == $value->absent)
            $value->marks = 'Absent';
          else 
          array_push($belowAvg, $value);
        }

        $gData[$value->display_name][$value->csName]['sum'] += $value->sumMarks;
        $gData[$value->display_name][$value->csName]['count'] += 1;
      }

      // echo '<pre>'; print_r($gData); die();
      foreach ($gData as $key => $value) {
        foreach ($value as $k => $val) {
          $gData[$key][$k]['average'] = round($val['sum']/$val['count'], 2);
        }
      }
    }
    
    $data['belowAverage'] = array_merge($belowAverage, $belowAvg);
    $data['performanceData'] = array_merge($data['perfData'], $gData);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/assessment_performance_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/assessment_performance_mobile';
    }else{
      $data['main_content'] = 'examination/reports/assessment_performance';     	
    }    
    $this->load->view('inc/template', $data);
  }

  public function marksReport(){
    $data['sectionId'] = 0;
    $data['assId'] = 0;
    $data['reportTitle'] = '';
    $data['subjectType'] = 'component';
    $data['subject'] = array();
    $data['summary'] = 1;
    $data['display_mode'] = 'marks_grades';
    $data['round'] = 'no';
    if(isset($_POST['sectionId']))
      $data['sectionId'] = $_POST['sectionId'];
    if(isset($_POST['assessment']))
      $data['assId'] = $_POST['assessment'];
    if(isset($_POST['report_title']))
      $data['reportTitle'] = $_POST['report_title'];
    if(isset($_POST['subject']))
      $data['subject'] = $_POST['subject'];
    if(isset($_POST['subjectType']))
      $data['subjectType'] = $_POST['subjectType'];
    if(isset($_POST['summary']))
      $data['summary'] = $_POST['summary'];
    if(isset($_POST['display_mode']))
      $data['mode'] = $_POST['display_mode'];
    if(isset($_POST['round']))
      $data['round'] = $_POST['round'];

    $data['classList'] = $this->assessment_model->getClassSections();
    $data['main_content'] = 'examination/reports/marks_report';
    $this->load->view('inc/template', $data);
  }

  public function getSectionWiseAssessment() {
    $sectionId = $this->input->post('sectionId');
    $assessments = $this->assessment_reports->getSecAssessments($sectionId);
    echo json_encode($assessments);
  }

  public function getAssessmentWiseSub(){
    $mode = $_POST['mode'];
    echo json_encode($this->assessment_reports->getAssWiseSubjects($_POST['assId'], $mode));
  }

  public function getMarksReport(){
    $input = $this->input->post();
    $sectionId = $input['sectionId'];
    $data['classData'] = $this->assessment_model->getClassAndClassTeacher($sectionId);
    if(empty($data['classData']))
    {
      redirect('examination/assessment_reports/marksReport');
    }
    $subIds = $input['subject'];
    $assId = $input['assessment'];
    $subjectType = $input['subjectType'];
    $round = $input['round'];
    $data['reportTitle'] = $input['report_title'];
    $data['summaryRequired'] = $input['summary'];
    $mode = $input['mode'];
    $subjectIds = implode(',', $subIds);

    $data['sectionId'] = $input['sectionId'];
    $data['assessment'] = $input['assessment'];
    $data['display_mode'] = $input['mode'];
    $data['subjectType'] = $input['subjectType'];
    $data['summary'] = $input['summary'];
    $data['round'] = $input['round'];
    $data['report_title'] = $input['report_title'];
    $data['subject'] = $input['subject'];

    $data['isElective'] = 0;
    // $gNames = array();
    $subjects = array();
    if($subjectType == 'component') {
      $mData = $this->assessment_reports->getComponentWiseMarks($subjectIds, $assId, $sectionId);
    } else if($subjectType == 'group') {
      $mData = $this->assessment_reports->getMarksByGroup($subjectIds, $assId, $sectionId);
    } else if($subjectType == 'elective') {
      $mData = $this->assessment_reports->getElectiveWiseMarks($subjectIds, $assId, $sectionId);
      $data['isElective'] = 1;
    }
    $data['marksData'] = array();
    $failures = array();
    $order = array();
    $electiveGrp = array();
    foreach ($mData as $key => $value) {
      if(!array_key_exists($value->entity_name, $failures)) {
        $failures[$value->entity_name]['fail'] = 0;
        $failures[$value->entity_name]['count'] = 0;
      }
      if($subjectType == 'elective' && !in_array($value->entity_name, $electiveGrp)) {
        array_push($electiveGrp, $value->entity_name);
      }
      if(!array_key_exists($value->gName, $subjects)) {
        $order[$value->sorting_order] = $value->gName;
        $subjects[$value->gName] = array();
      }
      if(!in_array($value->entity_name, $subjects[$value->gName]))
        array_push($subjects[$value->gName], $value->entity_name);
      if(!array_key_exists($value->student_id, $data['marksData'])) {
        $data['marksData'][$value->student_id] = new stdClass();
        $data['marksData'][$value->student_id]->stdName = $value->stdName;
        $data['marksData'][$value->student_id]->admission_no = $value->admission_no;
        $data['marksData'][$value->student_id]->alpha_rollnum = $value->alpha_rollnum;
        $data['marksData'][$value->student_id]->roll_no = $value->roll_no;
        $data['marksData'][$value->student_id]->entities = array();
      }
      if($value->isAbsent == 'Yes' && ($value->marks == 0.00 || $value->marks == -1.00))
        // $marks = 'Absent';
        $val = array('marks' => 'Absent', 'styled_marks' => 'Absent');
      else if($value->marks < 0.00) 
        $marks = '';
      else 
        $marks = $this->__displayGradeOrMarks($value->marks,$value->total_marks, $mode, $value->aemId, $subjectType, $round);
      $percent = round(($value->marks/$value->total_marks)*100);
      if($marks == 'Absent' || $percent < 33) {
        $failures[$value->entity_name]['fail']++;
      }
      $failures[$value->entity_name]['count']++;
      $entName = $value->gName.'%%'.$value->entity_name;
      $data['marksData'][$value->student_id]->entities[$entName] = $marks;
    }
    usort($data['marksData'], function($a, $b) { return $a->roll_no - $b->roll_no; });
    $data['failures'] = $failures;
    ksort($order);
    $data['order'] = $order;
    $data['mode'] = $subjectType;
    $data['subjects'] = $subjects;
    $data['electiveGroups'] = $electiveGrp;
    $data['main_content'] = 'examination/reports/consolidationMarks_report';
    $this->load->view('inc/template', $data);
  }

  private function __displayGradeOrMarks_v2($marks,$total_marks, $type, $entityId, $level, $round='yes', $evaluation_type, $grading_system_over_subjects, $grading_system_id_over_subjects){
   
    if($marks === 'AB' || $marks === '-1.00') {
      return array('marks' => 'AB', 'styled_marks' => '<span style="color:#05088a !important;">AB</span>', 'grade' => 'AB');
    } else if($marks === 'NA') {
      return array('marks' => 'NA', 'styled_marks' => '<span style="color:#05088a !important;">NA</span>', 'grade' => 'NA');
    } else if($marks === 'TBD') {
      return array('marks' => 'TBD', 'styled_marks' => '<span style="color:#05088a !important;">TBD</span>', 'grade' => 'TBD');
    }
    // if($marks == 'TBD' || $marks == 'AB' || $marks == '-1.00' || $marks == 'NA') { // make marks 0 if marks not added or is absent
    //   $marks = 0;
    // }
    $not_rounded = $marks;
    if($round == 'yes') {
        $marks = round($marks);
    }

    // return 7777;
    
    $percentage = round(($marks/$total_marks)*100);
    $grade = $this->assessment_model->__calculateGradeNew_v2($percentage, $entityId, $level, 'short_name', $grading_system_over_subjects, $grading_system_id_over_subjects);

    if($type == 'marks') { // returning marks if only marks needed
      $m = $marks;
      if($round == 'yes')
        $m =  round($marks);
      if($evaluation_type == 'marks') {
        
        return array('marks' => $m, 'styled_marks' => '<span style="color:#05088a !important;">'.$m.'</span>', 'grade' => $grade);
      } else {
       
        return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $m);
      }
    }

    switch ($type) {
      case 'grades':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $marks);
        }
        break;
      
      case 'percentage':
        
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$percentage.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $marks);
        }
        break;

      case 'grade_pecentage':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$percentage .'</span><span style="padding-left:10px;color: #036f12 !important;">' . $grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">' . $marks.'</span>', 'grade' => $marks);
        }
        break;

      case 'marks_grades':
        
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'. $marks . '</span><span style="padding-left:10px;color: #036f12 !important;">' . $grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">' . $marks.'</span>', 'grade' => $marks);
        }
        break;

      case 'percentage_marks':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$marks . '</span><span style="padding-left:10px;color: #036f12 !important;">' . $percentage . '%</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">-%</span>', 'grade' => $marks);
        }
        break;
    }

  }

  private function __displayGradeOrMarks($marks,$total_marks, $type, $entityId, $level, $round='yes', $evaluation_type){
    if($marks === 'AB' || $marks === '-1.00') {
      return array('marks' => 'AB', 'styled_marks' => '<span style="color:#05088a !important;">AB</span>', 'grade' => 'AB');
    } else if($marks === 'NA') {
      return array('marks' => 'NA', 'styled_marks' => '<span style="color:#05088a !important;">NA</span>', 'grade' => 'NA');
    } else if($marks === 'TBD') {
      return array('marks' => 'TBD', 'styled_marks' => '<span style="color:#05088a !important;">TBD</span>', 'grade' => 'TBD');
    }
    // if($marks == 'TBD' || $marks == 'AB' || $marks == '-1.00' || $marks == 'NA') { // make marks 0 if marks not added or is absent
    //   $marks = 0;
    // }
    $not_rounded = $marks;
    if($round == 'yes') {
        $marks = round($marks);
    }

    // return 7777;
    
    $percentage = round(($marks/$total_marks)*100);
    $grade = $this->assessment_model->__calculateGradeNew($percentage, $entityId, $level);

    if($type == 'marks') { // returning marks if only marks needed
      $m = $marks;
      if($round == 'yes')
        $m =  round($marks);
      if($evaluation_type == 'marks') {
        
        return array('marks' => $m, 'styled_marks' => '<span style="color:#05088a !important;">'.$m.'</span>', 'grade' => $grade);
      } else {
       
        return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $m);
      }
    }

    switch ($type) {
      case 'grades':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $marks);
        }
        break;
      
      case 'percentage':
        
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$percentage.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $marks);
        }
        break;

      case 'grade_pecentage':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$percentage .'</span><span style="padding-left:10px;color: #036f12 !important;">' . $grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">' . $marks.'</span>', 'grade' => $marks);
        }
        break;

      case 'marks_grades':
        
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'. $marks . '</span><span style="padding-left:10px;color: #036f12 !important;">' . $grade.'</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">' . $marks.'</span>', 'grade' => $marks);
        }
        break;

      case 'percentage_marks':
       
        if($evaluation_type == 'marks') {
          return array('marks' => $marks, 'styled_marks' => '<span style="color:#05088a !important;">'.$marks . '</span><span style="padding-left:10px;color: #036f12 !important;">' . $percentage . '%</span>', 'grade' => $grade);
        } else {
          return array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span><span style="padding-left:10px;color: #036f12 !important;">-%</span>', 'grade' => $marks);
        }
        break;
    }

  }

  public function subjectWiseMarksReport(){
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/subject_wise_marks_report';
    $this->load->view('inc/template', $data);
  }

  public function getEntitiesAssSectionsFromClass(){
    $classId = $_POST['classId'];
    $subjectType = $_POST['subjectType'];
    
    if(isset($_POST['assType']))
      $assType = $_POST['assType']; // auto or manual or both
    else 
      $assType = 'both';
    $data['sections'] = $this->assessment_reports->getSectionsFromClass($classId);
    $data['assessments'] = $this->assessment_reports->getClsAssessments_new($classId, $assType);
    $data['subjects'] = $this->assessment_reports->getAssEntitiesOfClass($classId, $subjectType);
    echo json_encode($data);
  }

  public function getSubjectsForClassId() {
    $classId = $_POST['classId'];
    $subjectType = $_POST['subjectType'];
    $data['subjects'] = $this->assessment_reports->getAssEntitiesOfClass($classId, $subjectType);
    echo json_encode($data);
  }

  public function getSubjectWiseMarksReport() {
    $entityId = $_POST['subId'];
    $assIdsArr = $_POST['assIds'];
    $classId = $_POST['classId'];
    $sections = $_POST['sections'];
    $subjectType = $_POST['subjectType'];

    $assIds = implode(',', $assIdsArr);
    $sections = implode(',', $sections);

    $assessments = $this->db->select("ass.id as assId, ass.short_name as assShortName")
      ->from('assessments ass')
      ->where_in('ass.id', $assIdsArr)
      ->get()->result();

    $data['assessments'] = $assessments;
    $data['marksData'] = $this->__getSubjectMarks($entityId, $assIds, $classId, $sections, $subjectType);
    echo json_encode($data);
  }

  private function __getSubjectMarks($entityId, $assIds, $classId, $sections, $subjectType){
    
    if ($subjectType == 'component') {
        $marksList = $this->assessment_reports->getMarksForEntity($assIds, $sections, $entityId);
    } else {
        $marksList = $this->assessment_reports->getMarksForEntityGroup($assIds, $sections, $entityId);
    }

    $newMarksList = array();
    foreach ($marksList as $key => $marksObj) {
        $found = 0;
        foreach ($newMarksList as &$newMarksObj) {
          if ($newMarksObj->student_id == $marksObj->student_id) {
            $assId = 'ass' . $marksObj->assId;
            if($marksObj->marks < 0 && $marksObj->isAbsent > 0) {
              $newMarksObj->$assId = 'AB';
            }
            else {
              $newMarksObj->$assId = $this->__adjustMarks($marksObj->marks);
            }
            $found = 1;
          }
        }
        if (!$found) {
          $assId = 'ass' . $marksObj->assId;
          if($marksObj->marks < 0 && $marksObj->isAbsent > 0) {
            $newMarksObj->$assId = 'AB';
          }
          else {
            $marksObj->$assId = $this->__adjustMarks($marksObj->marks);
          }
          $newMarksList[] = $marksObj;
        }
    }
    return $newMarksList;
  }

  private function __adjustMarks($mark) {
    switch ($mark) {
    case null:
      return 0;
    case '':
      return 0;
    case -1.00:
      return 'AB';
    case -2.00:
      return 'TBD';
    case -3.00:
      return 'NA';
    default:
      return (float)$mark;
    }
  }

  public function consolidationReport(){
    $input = $this->input->post();
    $data['classId'] = 0;
    $data['sectionId'] = 0;
    $data['assId'] = 0;
    $data['reportTitle'] = '';
    $data['subjectType'] = '';
    $data['subject'] = '';
    $data['derived'] = '';
    if(isset($_POST['classId']))
      $data['classId'] = $_POST['classId'];
    if(isset($_POST['sectionId']))
      $data['sectionId'] = $_POST['sectionId'];
    if(isset($_POST['assessment']))
      $data['assId'] = $_POST['assessment'];
    if(isset($_POST['reportTitle']))
      $data['reportTitle'] = $_POST['reportTitle'];
    if(isset($_POST['subject']))
      $data['subject'] = $_POST['subject'];
    if(isset($_POST['derived']))
      $data['derived'] = $_POST['derived'];
    // echo "<pre>"; print_r($input); die();
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/consolidation_report';
    $this->load->view('inc/template', $data);
  }

  public function getSubjectWiseConsolReport() {
    $input = $this->input->post();
    $entityIds = $_POST['subject'];
    $consoleAssId = $_POST['assessment'];//consolidation assId
    $classId = $_POST['classId'];
    $sectionId = $_POST['section'];
    $subjectType = $_POST['subjectType'];
    $data['reportTitle'] = $_POST['report_title'];

    $data['subject'] = $_POST['subject'];
    $data['assessment'] = $_POST['assessment'];//consolidation assId
    $data['classId'] = $_POST['classId'];
    $data['sectionId'] = $_POST['section'];
    $data['subjectType'] = $_POST['subjectType'];
    $data['reportTitle'] = $_POST['report_title'];

    $derived = array();
    $data['derived'] = array();
    if(isset($_POST['derived'])) {
      $derived = $_POST['derived'];
      $data['derived'] = $_POST['derived'];
    }

    $data['classData'] = $this->assessment_model->getClassAndClassTeacher($sectionId);
    // $data['sectionData'] = $this->db->select("section_name, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName")->from('class_section cs')->join('staff_master sm', 'cs.class_teacher_id=sm.id')->where('cs.id', $sectionId)->get()->row();

    $data['subjects'] = $this->assessment_reports->getEntities($entityIds);

    $assFormula = $this->assessment_model->getAssessmentName($consoleAssId);
    $json = json_decode($assFormula->formula);
    $formula = ($json->merg_algorithm)->name;
    $merg_values = $json->merg_values;
    
    $assIdsArr = array($consoleAssId);
    foreach ($json->assessments as $key => $value) {
      array_push($assIdsArr, $value->id);
    }
    array_push($assIdsArr, $consoleAssId);

    $mergArr = array();
    foreach ($merg_values as $k => $valueSet) {
      $mergArr[$valueSet->entity_id] = array();
      foreach ($valueSet->value_set as $key => $value) {
        $mergArr[$valueSet->entity_id][$value->assId] = $value->value;
      }
    }
    $assIds = implode(',', $assIdsArr);

    $assessments = $this->db->select("ass.id as assId, ass.short_name as assShortName")
      ->from('assessments ass')
      ->where_in('ass.id', $assIdsArr)
      ->order_by('ass.sorting_order')
      ->get()->result();

    foreach ($assessments as $key => $value) {
      $assNames[$value->assId] = $value->assShortName;
    }
    $data['assOrder'] = $assNames;

    $individual = $this->__getSubjectWiseMarks($entityIds, $assIds, $classId, $sectionId, $subjectType);
    $consolidated = $this->__getSubjectWiseMarks($entityIds, $consoleAssId, $classId, $sectionId, $subjectType);

    foreach ($consolidated as $c => $consol) {
      foreach ($individual as $i => $ind) {
        if($ind->student_id == $consol->student_id) {
          $ass = 'ass'.$consoleAssId;
          $ind->grade = array();
          $ind->percent = array();
          unset($ind->marks);
          unset($ind->percentage);
          unset($ind->total_marks);
          unset($ind->assId);
          unset($ind->isAbsent);
          foreach ($entityIds as $e => $entId) {
            $ent = 'ent'.$entId;
            if(isset($ind->$ent)) {
              $ind->percent[$ent] = round(($consol->$ent[$ass]['marks']/$consol->$ent[$ass]['total'])*100);
              $ind->$ent[$ass]['marks'] = $consol->$ent[$ass]['marks'];
              $ind->$ent[$ass]['total'] = $consol->$ent[$ass]['total'];
              $ind->grade[$ent] = $this->assessment_model->__calculateGradeNew($ind->percent[$ent], $entId, $subjectType);
            }
          }
          break;
        }
      }
    }

    $summary = array();
    foreach ($data['subjects'] as $key => $value) {
      $summary[$value->id] = array('failed' => 0, 'total' => 0);
    }
    $assData = array();
    foreach ($individual as $i => $ind) {
      foreach ($mergArr as $entId => $merg) {
        $ent = 'ent'.$entId;
        if(isset($ind->$ent)) {
          $assData[$entId] = array();
          foreach ($merg as $aId => $value) {
            $ass = 'ass'.$aId;
            if(!array_key_exists($ass, $ind->$ent)) {
              $ind->$ent[$ass]['reducedMarks'] = '';
              $ind->$ent[$ass]['marks'] = '';
              $assData[$entId][$aId]['total'] = '';
              $assData[$entId][$aId]['percent'] = $value;
              $assData[$entId][$aId]['name'] = $assNames[$aId];
            }
            else {
              $marks = $ind->$ent[$ass]['marks'];
              $total = $ind->$ent[$ass]['total'];
              $reduced = $this->__getReducedMarks($marks, $total, $value);
              if($marks != 'AB'){
                $marks = round($marks, 1);
                $reduced = round($reduced, 1);
              }
              if(in_array($aId, $derived)) {
                $reduced = '<span style="color: #036f12 !important;">'.$marks.'&nbsp;&nbsp;&nbsp;</span><span style="color:#05088a !important;">'.$reduced. '</span>';
                $total = '<span style="color: #036f12 !important;">'.$total.'&nbsp;&nbsp;&nbsp;</span><span style="color:#05088a !important;">'.$value . '</span>';
              }
              $ind->$ent[$ass]['reducedMarks'] = $reduced;
              $ind->$ent[$ass]['percent'] = $total;
              $assData[$entId][$aId]['percent'] = $total;
              $assData[$entId][$aId]['name'] = $assNames[$aId];
            }
          }
          $ass = 'ass'. $consoleAssId;
          $aId = $consoleAssId;
          $reduced = $this->__getReducedMarks($ind->$ent[$ass]['marks'], $ind->$ent[$ass]['total'], 100);
          $ind->$ent[$ass]['reducedMarks'] = round($reduced);
          $assData[$entId][$aId]['total'] = 100;
          $assData[$entId][$aId]['percent'] = 100;
          $assData[$entId][$aId]['name'] = $assNames[$aId];
          $data['summaryAss'] = $assNames[$aId];
          $summary[$entId]['total']++;
          $percent = ($ind->$ent[$ass]['marks'] / $ind->$ent[$ass]['total']) * 100;
          if($percent <= 32) {
            $summary[$entId]['failed']++;
          }
        }
      }
    }
    usort($individual, function($a, $b) { return $a->roll_no - $b->roll_no; });
    $data['summary'] = $summary;
    $data['assessments'] = $assData;
    $data['marksData'] = $individual;
    $data['main_content'] = 'examination/reports/consolidationReport';
    $this->load->view('inc/template', $data);
  }

  private function __getSubjectWiseMarks($entityIds, $assIds, $classId, $sections, $subjectType){
    foreach ($entityIds as $key => $entityId) {
      $marksList[$entityId] = $this->assessment_reports->getMarksForEntity($assIds, $sections, $entityId);
    }

    $newMarksList = array();
    foreach ($marksList as $entId => $entMarks) {
      $ent = 'ent' . $entId;
      foreach ($entMarks as $key => $marksObj) {
        $found = 0;
        foreach ($newMarksList as &$newMarksObj) {
          if ($newMarksObj->student_id == $marksObj->student_id) {
            $assId = 'ass' . $marksObj->assId;
            $newMarksObj->$ent[$assId] = array();
            $newMarksObj->$ent[$assId]['total'] = $marksObj->total_marks;
            if($marksObj->marks < 0 && $marksObj->isAbsent > 0) {
              $newMarksObj->$ent[$assId]['marks'] = 'AB';
            }
            else {
              $newMarksObj->$ent[$assId]['marks'] = $this->__adjustMarks($marksObj->marks);
            }
            $found = 1;
          }
        }
        if (!$found) {
          $marksObj->$ent = array();
          $assId = 'ass' . $marksObj->assId;
          $marksObj->$ent[$assId] = array();
          $marksObj->$ent[$assId]['total'] = $marksObj->total_marks;
          if($marksObj->marks < 0 && $marksObj->isAbsent > 0) {
            $marksObj->$ent[$assId]['marks'] = 'AB';
          }
          else {
            $marksObj->$ent[$assId]['marks'] = $this->__adjustMarks($marksObj->marks);
          }
          $newMarksList[] = $marksObj;
        }
      }
    }

    return $newMarksList;
  }

  private function __getReducedMarks($marks, $total, $percent){
    if(!is_numeric($marks))
      return $marks;
    $percent = $percent/100;
    $reduced = ($marks/$total)*$percent*100;
    return round($reduced, 2);
  }

  public function getDerivedAssessments(){
    $assId = $_POST['assId'];
    $assData = $this->assessment_model->getAssessmentName($assId);
    $json = json_decode($assData->formula);
    echo json_encode($json->assessments);
  }

  public function term_summary_report() {
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/term_summary_selector';
    $this->load->view('inc/template', $data);
  }

  public function assessment_wise_analysis(){
    $input = $this->input->post();
    $data['report_title'] = '';
    $data['classId'] = 0;
    $data['sectionId'] = 0;
    $data['summaryAss'] = 0;
    $data['round'] = '';
    $data['auto'] = 1;
    $data['manual'] = 0;
    $data['show_total'] = 0;
    $data['student_wise_percentage'] = 0;
    $data['assessment'] = array();
    $data['groups'] = array();
    $data['components'] = array();
    $data['electives'] = array();
    $data['display_type'] = '';
    if(isset($_POST['classId']))
      $data['classId'] = $_POST['classId'];
    if(isset($_POST['report_title']))
      $data['report_title'] = $_POST['report_title'];
    if(isset($_POST['sectionId']))
      $data['sectionId'] = $_POST['sectionId'];
    if(isset($_POST['assessment']))
      $data['assessment'] = $_POST['assessment'];
    if(isset($_POST['display_type']))
      $data['display_type'] = $_POST['display_type'];
    if(isset($_POST['summaryAss']))
      $data['summaryAss'] = $_POST['summaryAss'];
    if(isset($_POST['round']))
      $data['round'] = $_POST['round'];
    if(isset($_POST['auto']))
      $data['auto'] = $_POST['auto'];
    if(isset($_POST['manual']))
      $data['manual'] = $_POST['manual'];
    if(isset($_POST['groups']))
      $data['groups'] = $_POST['groups'];
    if(isset($_POST['components']))
      $data['components'] = $_POST['components'];
    if(isset($_POST['electives']))
      $data['electives'] = $_POST['electives'];
    if(isset($_POST['show_total']))
      $data['show_total'] = $_POST['show_total'];
    if(isset($_POST['student_wise_percentage']))
      $data['student_wise_percentage'] = $_POST['student_wise_percentage'];
    if(isset($_POST['display_std_no']))
      $data['display_std_no'] = $_POST['display_std_no'];
    else 
      $data['display_std_no'] = 'roll_no';

    // echo "<pre>"; print_r($data);die();
    $data['classList'] = $this->assessment_model->getClassess();
    $data['grades'] = $this->assessment_reports->getGradingSystems();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/assessment_wise_index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/assessment_wise_index_mobile';
    }else{
      $data['main_content'] = 'examination/reports/assessment_wise_index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function summaryReport(){
    $input = $this->input->post();
    $data['report_title'] = '';
    $data['classId'] = 0;
    $data['sectionId'] = 0;
    $data['summaryAss'] = 0;
    $data['round'] = '';
    $data['auto'] = 1;
    $data['manual'] = 0;
    $data['show_total'] = 0;
    $data['assessment'] = array();
    $data['groups'] = array();
    $data['components'] = array();
    $data['electives'] = array();
    $data['display_type'] = '';
    if(isset($_POST['classId']))
      $data['classId'] = $_POST['classId'];
    if(isset($_POST['report_title']))
      $data['report_title'] = $_POST['report_title'];
    if(isset($_POST['sectionId']))
      $data['sectionId'] = $_POST['sectionId'];
    if(isset($_POST['assessment']))
      $data['assessment'] = $_POST['assessment'];
    if(isset($_POST['display_type']))
      $data['display_type'] = $_POST['display_type'];
    if(isset($_POST['summaryAss']))
      $data['summaryAss'] = $_POST['summaryAss'];
    if(isset($_POST['round']))
      $data['round'] = $_POST['round'];
    if(isset($_POST['auto']))
      $data['auto'] = $_POST['auto'];
    if(isset($_POST['manual']))
      $data['manual'] = $_POST['manual'];
    if(isset($_POST['groups']))
      $data['groups'] = $_POST['groups'];
    if(isset($_POST['components']))
      $data['components'] = $_POST['components'];
    if(isset($_POST['electives']))
      $data['electives'] = $_POST['electives'];
    if(isset($_POST['show_total']))
      $data['show_total'] = $_POST['show_total'];

    // echo "<pre>"; print_r($data);die();
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/report_generator';
    $this->load->view('inc/template', $data);
  }

  public function summary_report() {
    $input = $this->input->post();
    $assIdArr = $input['assessment'];
    // $data['reportTitle'] = $input['report_title'];
    $assIds = implode(",", $assIdArr);
    $data['students'] = $this->assessment_reports->getStudents($input['section']);
    $data['classData'] = $this->assessment_model->getClassAndClassTeacher($input['section']);
    $std_ids = array();
    foreach ($data['students'] as $key => $val) {
      $std_ids[] = $val->student_id;
    }

    $data['component_marks'] = array();
    $data['components'] = array();
    //check is components selected
    if(isset($input['components'])) { 
      $components = $input['components'];
      $isComponentEmpty = 'No';
      $componentIds = implode(",", $components);
      $entities = $this->assessment_reports->getAssessmentEntities($componentIds, $assIds);
      $componentMarks = $this->assessment_reports->getComponentMarks($componentIds, $assIds, $std_ids);
      foreach ($entities as $key => $entity) {
        $subs = $entity['entities'];
        foreach ($subs as $assEntId => $sub) {
          $data['components'][$sub['display_name']][$sub['assessment_name']] = array(
            'ass_entity_id' => $sub['ass_entity_id'],
            'assessment_id' => $sub['assessment_id'],
            'eval_type' => $sub['eval_type'],
            'total' => $sub['total']
          );
          if(!array_key_exists($sub['display_name'], $data['components'])) {
            $data['components'][$sub['display_name']] = array();
          }
          $noCount = 0;
          foreach ($componentMarks as $stdId => $mData) {
            if(array_key_exists($assEntId, $mData)) {
              $percentage = round(($mData[$assEntId]['marks']/$sub['total'])*100);
              $componentMarks[$stdId][$assEntId]['marks_grade'] = $this->assessment_model->__calculateGradeNew($percentage, $sub['entity_id'], 'component');
            } else {
              $noCount++;
            }
          }
          if($noCount == count($data['students'])) {
            unset($data['components'][$sub['display_name']]);
          }
        }
      }
      $data['component_marks'] = $componentMarks;
      // $data['components'] = $componentMarks;
      // echo '<pre>'; print_r($entities); 
      // echo '<pre>'; print_r($data); die();
      /*$cData = $this->__mergeMarksArray($componentMarks, 'component', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto); //components data
      $finalSub = $cData['subjects'];
      $failures = $cData['failures'];
      $finalMarks = $cData['marksData'];*/
    }
    $data['main_content'] = 'examination/reports/term_summary_report';
    $this->load->view('inc/template', $data);
  }

  public function assessment_wise_report(){
    $input = $this->input->post();

    // echo '<pre>'; print_r($input); die();

    // echo '<pre>'; print_r($input); die();
    $assIdArr = $input['assessment'];
    $data['reportTitle'] = $input['report_title'];
    $assIds = implode(",", $assIdArr);
    $assNames = $this->assessment_model->getAssDetails($assIdArr);
    $data['ass'] = array();
    $assTypeArr = array();
    foreach ($assNames as $key => $value) {
      if($value->display_name && strlen($value->display_name) > 0) {
        $data['ass'][$value->id] = $value->display_name;
      } else {
        $data['ass'][$value->id] = $value->short_name;
      }
      $assTypeArr[$value->id] = $value->generation_type;
    }
    $manual = 0;
    $auto = 0;
    $data['assCount'] = count($assIdArr);
    $data['classId'] = $input['classId'];
    $data['display_type'] = $input['display_type'];
    // echo '<pre>display_type2: '; print_r($data['display_type']); die();
    $data['display_type2'] = isset($input['display_type2']) ? $input['display_type2'] : 0;
    $data['display_std_no'] = $input['display_std_no'];
    $data['sectionId'] = $input['section'];
    $data['classData'] = $this->assessment_model->getClassAndClassTeacher($data['sectionId']);

    $data['round'] = $input['round'];
    $data['assessment'] = $input['assessment'];
    $data['summaryAss'] = $input['summaryAss'];
    $data['auto'] = 0;
    $data['manual'] = 0;
    $data['show_total'] = 0;
    $data['report_title'] = $input['report_title'];
    $data['groups'] = isset($input['groups'])?$input['groups']:array();
    $data['components'] = isset($input['components'])?$input['components']:array();
    $data['electives'] = isset($input['electives'])?$input['electives']:array();
    $data['enable_send_sms_functionality'] = $this->settings->getSetting('examination_enable_sms');
    $data['falure_percentage']= isset($input['falure_percentage']) ? $input['falure_percentage'] : '-1';
    if(isset($input['manual'])) {
      $data['manual'] = $input['manual'];
      // $data['manual'] = 1;
    }
    if(isset($input['auto'])) {
      $data['auto'] = $input['auto'];
      // $data['auto'] = 1;
    }
    if(isset($input['show_total'])) {
      $data['show_total'] = $input['show_total'];
    }

    $data['show_student_father']= 0;
    if(isset($input['show_student_father'])) {
      $data['show_student_father'] = $input['show_student_father'];
    }


// Derived fields impl
$show_operations= new stdClass();
$data['derived_fields']= isset($input['derived_fields']) ? $input['derived_fields'] : '';
// echo'<pre>'; print_r($data['derived_fields']); die();
$derived_fields_result= [];
$data['display_after_arr'] = [];
if(!empty($data['derived_fields'])) {
  foreach($data['derived_fields'] as $opkey => $opval) {
    $arr_op= [];
    $computed_id= explode('___', $opval);
    $derived_id= $computed_id[0];
    if($input["disp_after_$derived_id"]) {
      array_push($data['display_after_arr'], $input["disp_after_$derived_id"].$derived_id);
    }
    if(isset($input["derived_operation_$derived_id"])) {
      foreach($input["derived_operation_$derived_id"] as $lkey => $lval) {
        array_push($arr_op, $lval);
      }
    } else {
      array_push($arr_op, 'm');
    }
    $show_operations->$derived_id= $arr_op; 
    $data['show_operations']=$show_operations;
  }
  
// echo'<pre>'; print_r($data['display_after_arr']); die();

  $derived_fields_result= $this->assessment_reports->get_derived_fields_result($data['derived_fields'], $data['sectionId']);
  // echo'<pre>'; print_r($derived_fields_result); die();
}
$data['derived_fields_result']= [];
if(!empty($derived_fields_result)) {
  $data['derived_fields_result']= $derived_fields_result;
}
// end derived fields impl
    $data['grades']= array();
    if($data['show_total'] == '1') {
      $grades= $this->assessment_reports->get_grades($input['grading_system']);
      if($grades) {
        $data['grades']= json_decode($grades[0]->grades);
      }
    }

    $display_type = $input['display_type'];
    $classId = $input['classId'];
    $section = $input['section'];

    

    $round = $input['round'];
    $data['summaryAss'] = 'ass_'.$input['summaryAss'];
    $data['summaryRequired'] = $input['summaryAss'];
    

    $grading_system_over_subjects = isset($input['grading_system_over_subjects']) ? $input['grading_system_over_subjects'] : 0;
    $grading_system_id_over_subjects = isset($input['grading_system_id_over_subjects']) ? $input['grading_system_id_over_subjects'] : 0;
    
   
    $isGroupEmpty = 'Yes';
    $isComponentEmpty = 'Yes';
    $isElectiveEmpty = 'Yes';
    $failArr = array(); //failures
    $finalSub = array(); //all subjects selected and their total_marks
    $failures = array(); //array of failures and total count in each subject of summary Assessemnt
    $finalMarks = array(); // array that combines group, component and elective data
    if(isset($input['components'])) { //check is components selected
      $components = $input['components'];
      $isComponentEmpty = 'No';
      $componentIds = implode(",", $components);
      $componentMarks = $this->assessment_reports->getComponentWiseMarks($componentIds, $assIds, $section);
      // echo '<pre>'; print_r($componentMarks); die(); 
      $cData = $this->__mergeMarksArray($componentMarks, 'component', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto, $data['falure_percentage'], $grading_system_over_subjects, $grading_system_id_over_subjects); //components data
      $finalSub = $cData['subjects'];
      $failures = $cData['failures'];
      $finalMarks = $cData['marksData'];
    }
    if(isset($input['groups'])) { //check is groups selected
      $groups = $input['groups'];
      $isGroupEmpty = 'No';
      $groupIds = implode(",", $groups);
      $groupMarks = $this->assessment_reports->getGroupWiseMarks($groupIds, $assIds, $section);
      $gData = $this->__mergeMarksArray($groupMarks, 'group', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto, $data['falure_percentage'] , $grading_system_over_subjects, $grading_system_id_over_subjects); //group data
      if(!empty($finalMarks)) { //if components present merge the group and component data
        $finalSub = array_merge($finalSub, $gData['subjects']);
        $failures = array_merge($failures, $gData['failures']);
        $isElective = 0;
        $finalMarks = $this->_mergeGroupEntityElective($gData['marksData'], $finalMarks,$finalSub);
      } else {
        $finalSub = $gData['subjects'];
        $failures = $gData['failures'];
        $finalMarks = $gData['marksData'];
      }
    }




    if(isset($input['electives'])) { //check is electives selected
      $electives = $input['electives'];
      $isElectiveEmpty = 'No';
      $electiveIds = implode(",", $electives);
      $electiveMarks = $this->assessment_reports->getElectiveWiseMarks($electiveIds, $assIds, $section);

      // echo '<pre>'; print_r($electiveMarks); die();

      $eData = $this->__mergeMarksArray($electiveMarks, 'elective', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto, $data['falure_percentage'] , $grading_system_over_subjects, $grading_system_id_over_subjects); //elective data
      if(!empty($finalMarks)) { //if components, groups present merge the groups, components and electives data
        $finalSub = array_merge($finalSub, $eData['subjects']);
        $failures = array_merge($failures, $eData['failures']);
        $isElective = 0;
        $finalMarks = $this->_mergeGroupEntityElective($eData['marksData'], $finalMarks,$finalSub);
      } else {
        $finalSub = $eData['subjects'];
        $failures = $eData['failures'];
        $finalMarks = $eData['marksData'];
      }
    }

    // echo '<pre>'; print_r($finalMarks); die();


    uasort($finalSub, function($a, $b) { 
      $group_sub = $a['group_sorting_order'] - $b['group_sorting_order'];
      if ($group_sub == 0) {
        return $a['entity_sorting_order'] - $b['entity_sorting_order'];
      }
      return $group_sub;
    });

    $data['graph_exists'] = 0;
    $data['graph_data'] = array();
    if($data['summaryRequired'] && array_key_exists($data['summaryRequired'], $data['ass'])) {
      $grades = $this->assessment_reports->getGradingScaleValues($data['summaryRequired']);
      $graph_data = $this->_constructGradeGraphData($finalSub, $finalMarks, $data['summaryRequired']);
      foreach ($grades as $grade) {
        if(array_key_exists($grade, $graph_data)) {
          $data['graph_data'][] = array('grade' => $grade, 'count' => $graph_data[$grade]);
        } else {
          $data['graph_data'][] = array('grade' => $grade, 'count' => 0);
        }
        $absent_count = 0;
        if(array_key_exists('Absent', $graph_data)) {
          $absent_count = $graph_data['Absent'];
        }
      }
      $data['graph_data'][] = array('grade' => 'Absent', 'count' => isset($absent_count) ? $absent_count : 0);
    }
    $data['subjects'] = array();
    $ass_totals = array();
    // echo '<pre>'; print_r($finalSub); die();
    foreach ($finalSub as $entName => $value) {
      $sub_nm= explode('___', $entName);
      $sub_marks_grade= 'marks';
      if(count($sub_nm) === 4) {
        $sub_marks_grade= $sub_nm[3];
      }
      foreach ($data['ass'] as $assId => $val) {
        if(array_key_exists('ass'.$assId, $value)) {
          if(!array_key_exists('ass_'.$assId, $ass_totals)) {
            $ass_totals['ass_'.$assId] = 0;
          }
          if($sub_marks_grade == 'marks') {
            $ass_totals['ass_'.$assId] += $value['ass'.$assId];
          }
          $data['subjects'][$entName]['ass_'.$assId] = $value['ass'.$assId];
        }
      }
    }

    if($data['show_total']) {
      foreach ($data['ass'] as $assId => $val) {
          if(array_key_exists('ass_'.$assId, $ass_totals)) {
            $data['subjects']['  Total']['ass_'.$assId] = $ass_totals['ass_'.$assId];
          }
      }
    }

    
    // echo "<pre>"; print_r($finalMarks); die();
    $data['marksData'] = $finalMarks;
    $data['failures'] = $failures;
    foreach ($data['marksData'] as $k => $val) {
      $data['marksData'][$k]['  Total'] = array();
      foreach ($data['subjects'] as $sub => $ass) {
        if(!array_key_exists($sub, $val)) continue;
        foreach ($val[$sub] as $assName => $marks) {
          $ass_name = 'ass_'.substr($assName, 3);
          if(!array_key_exists($ass_name, $ass)) continue;
          if(!array_key_exists($assName, $data['marksData'][$k]['  Total'])) {
            $data['marksData'][$k]['  Total'][$assName]['styled_marks'] = 0.0;
            $data['marksData'][$k]['  Total'][$assName]['marks'] = 0.0;
          }
          if($marks['marks'] >= 0) {
            $data['marksData'][$k]['  Total'][$assName]['styled_marks'] += (gettype($marks['marks']) === 'double')?$marks['marks']:0;
            $data['marksData'][$k]['  Total'][$assName]['marks'] += (gettype($marks['marks']) === 'double')?$marks['marks']:0;
          }
        }
      }
    }

    
   
    $data['ass_types'] = $assTypeArr;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/assessment_wise_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/assessment_wise_report_mobile';
    }else{
      $data['main_content'] = 'examination/reports/assessment_wise_report';     	
    }
    $this->load->view('inc/template_fee', $data);
  }


  public function generateConsolidationReport(){
    $input = $this->input->post();
    $assIdArr = $input['assessment'];
    $data['reportTitle'] = $input['report_title'];
    $assIds = implode(",", $assIdArr);
    $assNames = $this->assessment_model->getAssDetails($assIdArr);
    $data['ass'] = array();
    $assTypeArr = array();
    foreach ($assNames as $key => $value) {
      $data['ass'][$value->id] = $value->short_name;
      $assTypeArr[$value->id] = $value->generation_type;
    }
    $manual = 0;
    $auto = 0;
    $data['assCount'] = count($assIdArr);
    $data['classId'] = $input['classId'];
    $data['display_type'] = $input['display_type'];
    $data['sectionId'] = $input['section'];
    $data['classData'] = $this->assessment_model->getClassAndClassTeacher($data['sectionId']);

    $data['round'] = $input['round'];
    $data['assessment'] = $input['assessment'];
    $data['summaryAss'] = $input['summaryAss'];
    $data['auto'] = 0;
    $data['manual'] = 0;
    $data['show_total'] = 0;
    $data['report_title'] = $input['report_title'];
    $data['groups'] = isset($input['groups'])?$input['groups']:array();
    $data['components'] = isset($input['components'])?$input['components']:array();
    $data['electives'] = isset($input['electives'])?$input['electives']:array();
    if(isset($input['manual'])) {
      $manual = $input['manual'];
      $data['manual'] = 1;
    }
    if(isset($input['auto'])) {
      $auto = $input['auto'];
      $data['auto'] = 1;
    }
    if(isset($input['show_total'])) {
      $data['show_total'] = $input['show_total'];
    }

    $display_type = $input['display_type'];
    $classId = $input['classId'];
    $section = $input['section'];
    $round = $input['round'];
    $data['summaryAss'] = 'ass'.$input['summaryAss'];
    $data['summaryRequired'] = $input['summaryAss'];

    $isGroupEmpty = 'Yes';
    $isComponentEmpty = 'Yes';
    $isElectiveEmpty = 'Yes';
    $failArr = array(); //failures
    $finalSub = array(); //all subjects selected and their total_marks
    $failures = array(); //array of failures and total count in each subject of summary Assessemnt
    $finalMarks = array(); // array that combines group, component and elective data
    if(isset($input['components'])) { //check is components selected
      $components = $input['components'];
      $isComponentEmpty = 'No';
      $componentIds = implode(",", $components);
      $componentMarks = $this->assessment_reports->getComponentWiseMarks($componentIds, $assIds, $section); 
      $cData = $this->__mergeMarksArray($componentMarks, 'component', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto); //components data
      $finalSub = $cData['subjects'];
      $failures = $cData['failures'];
      $finalMarks = $cData['marksData'];
    }
    if(isset($input['groups'])) { //check is groups selected
      $groups = $input['groups'];
      $isGroupEmpty = 'No';
      $groupIds = implode(",", $groups);
      $groupMarks = $this->assessment_reports->getGroupWiseMarks($groupIds, $assIds, $section);
      $gData = $this->__mergeMarksArray($groupMarks, 'group', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto); //group data
      if(!empty($finalMarks)) { //if components present merge the group and component data
        $finalSub = array_merge($finalSub, $gData['subjects']);
        $failures = array_merge($failures, $gData['failures']);
        $isElective = 0;
        $finalMarks = $this->_mergeGroupEntityElective($gData['marksData'], $finalMarks,$finalSub);
      } else {
        $finalSub = $gData['subjects'];
        $failures = $gData['failures'];
        $finalMarks = $gData['marksData'];
      }
    }
    if(isset($input['electives'])) { //check is electives selected
      $electives = $input['electives'];
      $isElectiveEmpty = 'No';
      $electiveIds = implode(",", $electives);
      $electiveMarks = $this->assessment_reports->getElectiveWiseMarks($electiveIds, $assIds, $section);
      $eData = $this->__mergeMarksArray($electiveMarks, 'elective', $display_type, $classId, $failArr, $data['summaryAss'], $round, $assTypeArr, $manual, $auto); //elective data
      if(!empty($finalMarks)) { //if components, groups present merge the groups, components and electives data
        $finalSub = array_merge($finalSub, $eData['subjects']);
        $failures = array_merge($failures, $eData['failures']);
        $isElective = 0;
        $finalMarks = $this->_mergeGroupEntityElective($eData['marksData'], $finalMarks,$finalSub);
      } else {
        $finalSub = $eData['subjects'];
        $failures = $eData['failures'];
        $finalMarks = $eData['marksData'];
      }
    }
    usort($finalMarks, function($a, $b) { return $a['roll_no'] - $b['roll_no']; });
    // if($finalMarks[0]->roll_no == 0) {
    //   usort($finalMarks, function($a, $b) { return strcmp($a['stdName'], $b['stdName']); });
    // }
    uasort($finalSub, function($a, $b) { return $a['sorting_order'] - $b['sorting_order']; });

    $data['graph_exists'] = 0;
    $data['graph_data'] = array();
    if($data['summaryRequired'] && array_key_exists($data['summaryRequired'], $data['ass'])) {
      $grades = $this->assessment_reports->getGradingScaleValues($data['summaryRequired']);
      $graph_data = $this->_constructGradeGraphData($finalSub, $finalMarks, $data['summaryRequired']);
      foreach ($grades as $grade) {
        if(array_key_exists($grade, $graph_data)) {
          $data['graph_data'][] = array('grade' => $grade, 'count' => $graph_data[$grade]);
        } else {
          $data['graph_data'][] = array('grade' => $grade, 'count' => 0);
        }
        $absent_count = 0;
        if(array_key_exists('Absent', $graph_data)) {
          $absent_count = $graph_data['Absent'];
        }
      }
      $data['graph_data'][] = array('grade' => 'Absent', 'count' => $absent_count);
    }
    $data['subjects'] = array();
    $ass_totals = array();
    foreach ($finalSub as $entName => $value) {
      foreach ($data['ass'] as $assId => $val) {
        if(array_key_exists('ass'.$assId, $value)) {
          if(!array_key_exists('ass'.$assId, $ass_totals)) {
            $ass_totals['ass'.$assId] = 0;
          }
          $ass_totals['ass'.$assId] += $value['ass'.$assId];
          // $data['subjects'][$entName] = array('ass'.$assId => $value['ass'.$assId]);
          $data['subjects'][$entName]['ass'.$assId] = $value['ass'.$assId];
        }
      }
    }

    
    // echo "<pre>"; print_r($finalMarks); die();
    $data['marksData'] = $finalMarks;
    $data['failures'] = $failures;
    foreach ($data['marksData'] as $k => $val) {
      $data['marksData'][$k]['  Total'] = array();
      foreach ($data['subjects'] as $sub => $ass) {
        foreach ($val[$sub] as $assName => $marks) {
          if(!array_key_exists($assName, $ass)) continue;
          if(!array_key_exists($assName, $data['marksData'][$k]['  Total'])) {
            $data['marksData'][$k]['  Total'][$assName]['styled_marks'] = 0.0;
            $data['marksData'][$k]['  Total'][$assName]['marks'] = 0.0;
          }
          if($marks['marks'] >= 0) {
            $data['marksData'][$k]['  Total'][$assName]['styled_marks'] += (gettype($marks['marks']) === 'double')?$marks['marks']:0;
            $data['marksData'][$k]['  Total'][$assName]['marks'] += (gettype($marks['marks']) === 'double')?$marks['marks']:0;
          }
        }
      }
    }
    if($data['show_total']) {
      foreach ($data['ass'] as $assId => $val) {
          // $data['subjects']['  Total'] = array('ass'.$assId => $ass_totals['ass'.$assId]);
          $data['subjects']['  Total']['ass'.$assId] = $ass_totals['ass'.$assId];
      }
    }
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/reports/console_report';
    $this->load->view('inc/template_fee', $data);
  }

  private function _constructGradeGraphData($subjects, $marksData, $assId) {
    $graph_data = array();
    foreach ($subjects as $sub => $val) {
      foreach ($marksData as $key => $mData) {
        if(array_key_exists($sub, $mData)) {
          if(array_key_exists('ass'.$assId, $mData[$sub])) {
            $grade = $mData[$sub]['ass'.$assId]['grade'];
            if(!array_key_exists($grade, $graph_data)) {
              $graph_data[$grade] = 0;
            }
            $graph_data[$grade]++;
          }
        }
      }
    }
    return $graph_data;
    // echo "<pre>"; print_r($graph_data); die();
  }

  public function send_marks_sms_to_parents() {
    // echo "<pre>"; print_r($_POST); die();
    $this->load->helper('texting_helper');
    $input = array();
    $input['source'] = 'Examination';

    $message_array = [];
    $i = 0;
    foreach ($_POST['sa_id'] as $sa_id) {
      $message_array[$sa_id] = $_POST['sms_message'][$i++];
    }
    $input['student_id_messages'] = $message_array;

    $status = 0;
    $error = 0;
    $text_send_to = $_POST['text_send_to'];
    $input['mode'] = $_POST['communication_mode'];
    if($input['mode'] == 'notification') {
      $input['send_to'] = 'Father';
      $res = sendUniqueText($input);
      if($res['success'] != '' && $res['success'] != null ) $status = 1;
      $error = $res['error'];
    } else {
      switch ($text_send_to) {
        case 'preferred':
          $input['send_to'] = 'preferred';
          break;
        case 'preferred_parent';
          $input['send_to'] = 'preferred';
          break;
        case 'Father':
          $input['send_to'] = 'Father';
          break;
        case 'Mother':
          $input['send_to'] = 'Mother';
          break;
        case 'Both':
          $input['send_to'] = 'Both';
          break;
        default:
          $input['send_to'] = 'Father';
          break;
      }
      $res = sendUniqueText($input);
      if($res['success'] != '' && $res['success'] != null) $status = 1;
      $error = $res['error'];

    }

    echo json_encode(['status' => $status, 'error' => $error]);
  }

  private function _mergeGroupEntityElective($Data1, $Data2, $finalSub){
    $finalData = array();
    foreach ($Data1 as $gKey => $gVal) {
      foreach ($Data2 as $cKey => $cVal) {
        if($gVal['student_id'] == $cVal['student_id']) {
          $data = array();
          $data['student_id'] = $gVal['student_id'];
          $data['sts_number'] = $gVal['sts_number'];
          $data['stdName'] = $gVal['stdName'];
          $data['father'] = $gVal['father'];
          $data['admission_no'] = $gVal['admission_no'];
          $data['roll_no'] = $gVal['roll_no'];
          $data['alpha_rollnum'] = $gVal['alpha_rollnum'];
          $data['enrollment_number'] = $gVal['enrollment_number'];
          $data['is_elective'] = $gVal['is_elective'];
          $data['csName'] = $gVal['csName'];
          foreach ($finalSub as $key => $sub) {
            if(array_key_exists($key, $gVal))
              $data[$key] = $gVal[$key];
            else if(array_key_exists($key, $cVal))
              $data[$key] = $cVal[$key];
          }
          // $finalData[] = $data;
          array_push($finalData, $data);
          break;
        }
      }
    }

    return $finalData;
  }

  private function __mergeMarksArray($subArr, $type, $display_type, $classId, $failArr, $summaryAss, $round, $assTypeArr, $manual, $auto, $failure_percentage = 33, $grading_system_over_subjects = 0, $grading_system_id_over_subjects = 1){
   

    $pre = '';
    if($type == 'group') {
      $pre = 'g_';
      $column = 'group_id';
      $level = 'group';
    } else if($type == 'component'){
      $pre = 'c_';
      $column = 'aemId';
      $level = 'component';
    } else if($type == 'elective') {
      $pre = 'e_';
      $column = 'group_id';
      $level = 'group';
    }

    if($failure_percentage == '-1') {
      $failure_percentage= 33;
    } else {
      $failure_percentage= number_format(intval($failure_percentage));
    }

    $newArr = array();
    $subjectHead = array();
    // $assessments = array();

 

    foreach ($subArr as $marksObj) {
        $student_id= $marksObj->student_id;
        $ent_grp_id= isset($marksObj->ent_grp_id) ? $marksObj->ent_grp_id : 0;
        $elective_group_id= isset($marksObj->elective_group_id) ? $marksObj->elective_group_id : 0;
        $aemId= $marksObj->aemId;
      
        if($type == 'group') {
          $entityName = $pre.$marksObj->diplay_name. '___' .$ent_grp_id. '___' .$ent_grp_id;
        } else if($type == 'component'){
          $entityName = $pre.$marksObj->diplay_name. '___' .$ent_grp_id. '___' .$aemId;
        } else if($type == 'elective') {
          $entityName = $pre.$marksObj->diplay_name;
        }

        $entityName .= "___" .$marksObj->evaluation_type;

      $found = 0;

     
     
      $assId = 'ass' . $marksObj->assId;
      if(!array_key_exists($entityName, $subjectHead)) {
          $subjectHead[$entityName] = array();
          $failArr[$entityName] = array('fail'=>0, 'count'=>0);
          $subjectHead[$entityName]['entity_sorting_order'] = $marksObj->entity_sorting_order;
          $subjectHead[$entityName]['group_sorting_order'] = $marksObj->group_sorting_order;
          if($type == 'elective')
            $subjectHead[$entityName]['ele'] = 'sub';
      }
      $subjectHead[$entityName][$assId] = $marksObj->total_marks;

      //display only marks for normal assessments and display marks and grade for consolidated assessment
      $dType = $display_type;
      if($assTypeArr[$marksObj->assId] == 'Manual' && $manual == 0)
        $dType = 'marks';
      if($assTypeArr[$marksObj->assId] == 'Auto' && $auto == 0)
        $dType = 'marks';


       


      foreach ($newArr as &$newMarksObj) {
       
        
        if (isset($newMarksObj['student_id']) && $newMarksObj['student_id'] == $marksObj->student_id) {
          $marks = $this->__adjustMarks($marksObj->marks);
          // echo '<pre>'; print_r($marksObj->evaluation_type); die();
          if($marksObj->evaluation_type == 'grade') {
              $grade = $marksObj->grade;
              if($grade === 'AB') {
                $grade = 'Absent';
              }
              if($marksObj->evaluation_type == 'marks') {
                $val = array('marks' => $grade, 'styled_marks' => '<span style="color:#05088a !important;">'.$grade.'</span>', 'grade' => $grade);
              } else {
                $val = array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $grade);
              }
          } else {
            if($marks === 'AB' || $marks <= -1.00) {
              // $val = 'Absent';
              $val = array('marks' => 'Absent', 'styled_marks' => 'Absent', 'grade' => 'Absent');
            } else {
              $val = $this->__displayGradeOrMarks_v2($marks, $marksObj->total_marks, $dType, $marksObj->{$column}, $level, $round, $marksObj->evaluation_type, $grading_system_over_subjects, $grading_system_id_over_subjects);
             
            }
          }
          
          $newMarksObj[$entityName][$assId] = $val;
          
          
          if(str_replace('_', '', $summaryAss) == $assId) {
            if($marks < 0 || $marks === 'TBD' || $marks === 'AB' || $marksObj->total_marks == 0) 
            $percent = 0;
            else
              $percent = round(($marks/$marksObj->total_marks)*100);
            $failArr[$entityName]['count']++;
            if($percent <= $failure_percentage) {
              $failArr[$entityName]['fail']++;
            }
              
          }
          if($type == 'elective')
            $newMarksObj[$entityName]['ele'] = $marksObj->ele;
          $found = 1;
        }
      }

      

      if (!$found) {
        $marksObj->$entityName = array();
        $marks = $this->__adjustMarks($marksObj->marks);
        $arr = array();
        $arr['student_id'] = $marksObj->student_id;
        $arr['stdName'] = $marksObj->stdName;
        $arr['father'] = $marksObj->father;
        $arr['admission_no'] = $marksObj->admission_no;
        $arr['sts_number'] = $marksObj->sts_number;
        $arr['roll_no'] = $marksObj->roll_no;
        $arr['alpha_rollnum'] = $marksObj->alpha_rollnum;
        $arr['enrollment_number'] = $marksObj->enrollment_number;
        $arr['csName'] = $marksObj->csName;
        $arr['is_elective'] = $marksObj->is_elective;
        $arr['ele'] = $marksObj->ele;

       

        
        if($marksObj->evaluation_type == 'grade') {
         
          $grade = $marksObj->grade;
              if($grade === 'AB') {
                $grade = 'Absent';
              }
              if($marksObj->evaluation_type == 'marks') {
                $val = array('marks' => $grade, 'styled_marks' => '<span style="color:#05088a !important;">'.$grade.'</span>', 'grade' => $grade);
              } else {
                $val = array('marks' => '-', 'styled_marks' => '<span style="color:#05088a !important;">-</span>', 'grade' => $grade);
              }
        } else {
         

          if($marks === 'AB' || $marks <= -1.00) {
            $val = array('marks' => 'Absent', 'styled_marks' => 'Absent', 'grade' => 'Absent');
          } else {
            $val = $this->__displayGradeOrMarks_v2($marks, $marksObj->total_marks, $dType, $marksObj->{$column}, $level, $round, $marksObj->evaluation_type, $grading_system_over_subjects, $grading_system_id_over_subjects);
          }
        }

        $arr[$entityName][$assId] = $val;
        // echo'<pre>'; print_r($assId); 
        //   die();
        if(str_replace('_', '', $summaryAss) == $assId) {
          if($marks < 0 || $marks === 'TBD' || $marks === 'AB' || $marksObj->total_marks == 0) 
              $percent = 0;
          else
              $percent = round(($marks/$marksObj->total_marks)*100);
          $failArr[$entityName]['count']++;
          
          if($percent <= $failure_percentage) {
            $failArr[$entityName]['fail']++;
          }
            
        }
        if($type == 'elective')
          $arr[$entityName]['ele'] = $marksObj->ele;
        array_push($newArr, $arr);
      }

      

    }

    // die();
    // echo '<pre>'; print_r($newArr); die();
    return array('marksData' => $newArr, 'subjects' => $subjectHead, 'failures' => $failArr);
  }

  public function stdReport(){
    $data['classList'] = $this->assessment_model->getClassess();
    $data['main_content'] = 'examination/reports/student_report';
    $this->load->view('inc/template', $data);
  }

  public function getSectionStudentsAndAssessments() {
    $this->load->model('examination/Assessment_marks_model','assessment_marks');
    $data['students'] = $this->assessment_marks->getSectionStudents($_POST['section_id'], 'sa.first_name');
    $data['assessments'] = $this->assessment_reports->getSectionAssessments($_POST['section_id'], $_POST['assessment_type']);
    echo json_encode($data);
  }

  private function _getStudentPerformanceData($student, $assessments) {
    $acad_year_id = $this->acad_year->getAcadYearId();
    $classId = $student->classId;
    $sectionId = $student->class_section_id;
    $studentId = $student->stdId;
    // $assessments = $this->Student_Model->getAssessments($classId, $sectionId, $acad_year_id);
    $assessments = $this->assessment_reports->getAssessments($assessments);
    $assIds = array();
    foreach ($assessments as $key => $value) {
      array_push($assIds, $value->assId);
    }
    $data['subWiseStatus'] = array();
    $data['assessments'] = array();
    $this->load->model('student/Student_Model');
    
    $subWiseData = $this->Student_Model->getSubWiseReport($classId, $studentId, $assIds);
    if(!empty($subWiseData)) {
      $subWiseAvgData = $this->Student_Model->getSubWiseAvgReport($classId, $studentId, $assIds);

      $subWise = array();
      foreach ($subWiseData as $key => $value) {
        foreach ($subWiseAvgData as $k => $avg) {
          if($value->gId == $avg->gId && $value->assId == $avg->assId) {
            // echo "<pre>"; print_r($value);print_r($avg);die();
            if($value->tMarks != 0 && $avg->tMarks != 0) {
            $percentage = round(($value->marks/$value->tMarks)*100);
            $avgPercent = round(($avg->marks/$avg->tMarks)*100); 
            } else {
              $percentage = 0;
              $avgPercent = 0;
            }
            if(!array_key_exists($value->entity_name, $subWise)) {
              $subWise[$value->entity_name] = array();
              $subWise[$value->entity_name]['name'] = $value->entity_name;
              $subWise[$value->entity_name]['subId'] = $value->gId;
              $subWise[$value->entity_name]['total'] = 0;
              $subWise[$value->entity_name]['aboveAvg'] = 0;
              $subWise[$value->entity_name]['belowAvg'] = 0;
              $subWise[$value->entity_name]['Average'] = 0;
            }
            $subWise[$value->entity_name]['total']++;
            /*if($value->entity_name == 'HPE')
              echo $percentage.' - '.$avgPercent.'<br>';*/
            if($percentage >= 0) {
              if($percentage == $avgPercent) {
                $subWise[$value->entity_name]['Average']++;
              } else if($percentage > $avgPercent) {
                $subWise[$value->entity_name]['aboveAvg']++;
              } else if($percentage < $avgPercent) {
                $subWise[$value->entity_name]['belowAvg']++;
              }
            }
          }
        }
      }
      $statusWise = array();
      foreach ($subWise as $key => &$value) {
        if($value['total'] == $value['aboveAvg']) {
          $value['status'] = 'Above Average';
        }
        else if($value['total'] == $value['belowAvg']) {
          $value['status'] = 'Below Average';
        }
        else if($value['total'] == $value['Average']) {
          $value['status'] = 'Average';
        }
        else if(($value['belowAvg'] + $value['Average']) >= $value['aboveAvg']) {
          if($value['Average'] > $value['belowAvg'])
            $value['status'] = 'Average';
          else 
            $value['status'] = 'Below Average';
        }
        else if(($value['belowAvg'] + $value['Average']) < $value['aboveAvg']) {
            $value['status'] = 'Above Average';
        }

        $statusWise[$value['status']][] = $value;
      }
      // ksort($statusWise);
      $data['subWiseStatus'] = $statusWise;
      $data['assessments'] = $assessments;
    }
    return $data;
  }

  public function studentWiseReprt(){
    $this->load->model('class_section');
    $data['sections'] = $this->class_section->getAllClassSections();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/student_wise/report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/student_wise/report_mobile';
    }else{
      $data['main_content'] = 'examination/reports/student_wise/report';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function studentWiseMarks(){
    $data['student'] = $this->assessment_model->getStdDetails($_POST['student_id']);
    $perfData = $this->_getStudentPerformanceData($data['student'], $_POST['assIds']);
    $data['subWiseStatus'] = $perfData['subWiseStatus'];
    $data['assessments'] = $perfData['assessments'];
    $marksData = $this->assessment_reports->studentMarks($_POST['student_id'], $_POST['assIds']);
    $data['marks'] = $marksData['subjects'];
    $data['assessment_data'] = $marksData['assessments'];
    // echo '<pre>'; print_r($data);die();
    echo json_encode($data);
  }

  public function getSectionWiseStd(){
    echo json_encode($this->assessment_model->getStd($_POST['section']));
  }

  public function getStdWiseReport(){
    $subjects = $this->assessment_model->getSubjectsUnion($_POST['assId']);
    $assData = $this->assessment_model->getAssDetails($_POST['assId']);
    $repData = array();
    foreach ($subjects as $key => $value) {
      $repData[$value->name] = $this->assessment_model->getStdSubReport($value->id, $_POST['assId'], $_POST['stdId']);
    }
    $stdData = $this->assessment_model->getStdDetails($_POST['stdId']);
    $htmlStr = '';
    if(empty($repData)) {
      $htmlStr = 'Marks not added...';
    } else {
      $span = count($assData)*3 + 2;
      $htmlStr .= '<table id="customers2" class="table"><thead><tr><th colspan="'.$span.'" style="text-align: center;">'.$stdData->stdName.'('.$stdData->class_name.'/'.$stdData->section_name.') (Admission No:'.$stdData->admission_no.')</th></tr>';
      $htmlStr .= '<tr><th></th><th></th>';
      foreach ($assData as $key => $value) {
        $htmlStr .= '<th colspan="3">'.$value->short_name.'</th>';
      }
      $htmlStr .= '</tr>';
      $htmlStr .= '<tr><th>#</th><th>Subject</th>';
      $tData = array();
      foreach ($assData as $key => $value) {
        $htmlStr .= '<th>Marks Scored</th><th>Total Marks</th><th>Percentage</th>';
        $tData[$value->id] = ['total'=>0, 'marks'=>0];
      }
      $htmlStr .= '</tr></thead><tbody>';
      $i = 1;
      $total = 0.00;
      $max = 0.00;
      $assCount = count($assData);

      foreach ($repData as $subName => $data) {
        $htmlStr .= '<tr><td>'.$i++.'</td><td>'.$subName.'</td>';
        $j = 0;
        foreach ($data as $key => $value) {
          $j++;
          $marks = $value->marks;
          if($marks == '-2.00'){
            $marks = "Not Added";
            $percentage = '-';
          } else if($marks == '-1.00'){
            $marks = "Absent";
            $percentage = '-';
          } else {
            $tData[$value->assId]['marks'] += $marks;
            $tData[$value->assId]['total'] += $value->total_marks;
            $percentage = ($marks/$value->total_marks)*100;
          }
          $htmlStr .= '<td>'.$marks.'</td><td>'.$value->total_marks.'</td><td>'.round($percentage, 2).'</td>';
        }
        
        while($j < $assCount) {
          $htmlStr .= '<td>-</td><td>-</td>-<td>-</td>';
          $j++;
        }
        $htmlStr .= '</tr>';
      }
      $htmlStr .= '<tr><th></th><th>Total</th>';
      foreach ($tData as $key => $value) {
        $tPercent = 0;
        if($value['total'])
          $tPercent = ($value['marks']/$value['total'])*100;
        $htmlStr .= '<th>'.$value['marks'].'</th><th>'.$value['total'].'</th><th>'.round($tPercent, 2).'</th>';
      }
      $htmlStr .= '</tr></tbody></table>';
    }
    print_r(json_encode(array('html'=>$htmlStr, 'assName'=>$assData)));
  }

  public function rankingReport() {
    $data['classes'] = $this->assessment_model->getClassess();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/ranking_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/ranking_report_mobile';
    }else{
      $data['main_content'] = 'examination/reports/ranking_report';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function getRankList() {
    $class_id = $_POST['class_id'];
    $section_id = $_POST['section_id'];
    $assessment_id = $_POST['assessment_id'];
    // #6 => subjects can not be empty
    $subject_id = $_POST['subject_id'];
    $groups_id = $_POST['groups_id'];
    $electives_id = $_POST['electives_id'];
    if( (!isset($_POST['subject_id']) || empty($_POST['subject_id'])) && (!isset($_POST['groups_id']) || empty($_POST['groups_id'])) && (!isset($_POST['electives_id']) || empty($_POST['electives_id'])) ) {
      echo json_encode(array());
    }
    $data = $this->assessment_reports->getRankingList($class_id, $section_id, $assessment_id, $subject_id, $groups_id, $electives_id);
    echo json_encode($data);
  }

  // complete start
  public function subject_wise_analysis(){
    $input = $this->input->post();
    $data['report_title'] = '';
    $data['classId'] = 0;
    $data['sectionId'] = 0;
    $data['summaryAss'] = 0;
    $data['round'] = '';
    $data['auto'] = 1;
    $data['manual'] = 0;
    $data['show_total'] = 0;
    $data['assessment'] = array();
    $data['groups'] = array();
    $data['components'] = array();
    $data['electives'] = array();
    $data['display_type'] = '';
    if(isset($_POST['classId']))
      $data['classId'] = $_POST['classId'];
    if(isset($_POST['report_title']))
      $data['report_title'] = $_POST['report_title'];
    if(isset($_POST['sectionId']))
      $data['sectionId'] = $_POST['sectionId'];
    if(isset($_POST['assessment']))
      $data['assessment'] = $_POST['assessment'];
    if(isset($_POST['display_type']))
      $data['display_type'] = $_POST['display_type'];
    if(isset($_POST['summaryAss']))
      $data['summaryAss'] = $_POST['summaryAss'];
    if(isset($_POST['round']))
      $data['round'] = $_POST['round'];
    if(isset($_POST['auto']))
      $data['auto'] = $_POST['auto'];
    if(isset($_POST['manual']))
      $data['manual'] = $_POST['manual'];
    if(isset($_POST['groups']))
      $data['groups'] = $_POST['groups'];
    if(isset($_POST['components']))
      $data['components'] = $_POST['components'];
    if(isset($_POST['electives']))
      $data['electives'] = $_POST['electives'];
    if(isset($_POST['show_total']))
      $data['show_total'] = $_POST['show_total'];
    if(isset($_POST['display_std_no']))
      $data['display_std_no'] = $_POST['display_std_no'];
    else 
      $data['display_std_no'] = 'roll_no';

    $data['classList'] = $this->assessment_model->getClassess();

    $data['grades'] = $this->assessment_reports->getGradingSystems();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/subject_wise_index_tablet';  
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/reports/subject_wise_index_mobile';  
    }else{
      $data['main_content'] = 'examination/reports/subject_wise_index';  	
    }    
    
    $this->load->view('inc/template', $data);
  }

   public function subject_wise_report(){
    $input = $this->input->post();
   
    $sub_id_arr= [];
    $sub_id_arr_e= [];

    if($input['sub_count'] > 0)
    for($i= 0; $i< $input['sub_count']; $i++) {
      $id= 'sub_id'.$i;
       array_push($sub_id_arr,$input[$id]);
      
    }
    if($input['sub_count_e'] > 0)
    for($i= 0; $i< $input['sub_count_e']; $i++) {
      $id= 'sub_id_e'.$i;
       array_push($sub_id_arr_e,$input[$id]);
    }
   
      // Storing Class Section and Assessments
      $data['class']= $this->assessment_reports->get_class_name($input['classId']);
      $data['section']= $this->assessment_reports->get_section_name($input['section']);
      $data['assessment_name']= $this->assessment_reports->get_assessment_name($input['assessment']);

      $data['reports']= array();
      $data['elective_reports']= array();
    if($input['sub_count_e'] > 0) {
      $data['elective_reports']= $this->assessment_reports->getSubjectMarks_for_subjectwise_analysis_in_elective($input['assessment'], $input['classId'], $input['section'], $sub_id_arr_e, $input['grading_system']);
    }
    if($input['sub_count'] > 0) {
      $data['reports']= $this->assessment_reports->getSubjectMarks_for_subjectwise_analysis($input['assessment'], $input['classId'], $input['section'], $sub_id_arr, $input['grading_system']);
    }

    // Get Dynamic ranges by grading system
    $grades= $this->assessment_reports->get_grades($input['grading_system']);
    $data['grades_arr']= array();
    if(!empty($grades)) {
        $data['grades_arr']= json_decode($grades[0]->grades);
    }

    // Totals range wise 
    $data['std_count_range_wise']= $this->assessment_reports->get_std_count_range_wise($input['assessment'], $input['classId'], $input['section'], $sub_id_arr, $sub_id_arr_e, $data['grades_arr']);

    $data['main_content'] = 'examination/reports/subject_wise_marks_for_analysis'; 	
    $this->load->view('inc/template_fee', $data);
  }

  public function getSubjectsByAssessment_subjectwise_analysis(){
    $assIds = $_POST['ass_id'];
    $class_id = $_POST['classId'];
    $data['components'] = $this->assessment_reports->getSubjectsByAssessment_analysis($assIds, 'component', $class_id);
    $data['electives'] = $this->assessment_reports->getSubjectsByAssessment_analysis($assIds, 'elective', $class_id);
    echo json_encode($data);
  }

  public function get_student_details_range_wise(){
    $data = $this->assessment_reports->get_student_details_range_wise($this->input->post());
    echo json_encode($data);
  }

  public function get_student_details_overall_subject_range_wise(){
    $data = $this->assessment_reports->get_student_details_overall_subject_range_wise($this->input->post());
    echo json_encode($data);
  }

  public function get_derived_fields_class_wise(){
    $data = $this->assessment_reports->get_derived_fields_class_wise($this->input->post());
    echo json_encode($data);
  }

  public function save_selected_filters(){
    $data = $this->assessment_reports->save_selected_filters($this->input->post());
    echo json_encode($data);
  }

  public function get_all_saved_filters(){
    $data = $this->assessment_reports->get_all_saved_filters();
    echo json_encode($data);
  }

  public function onchange_saved_filter(){
    $data = $this->assessment_reports->onchange_saved_filter();
    // echo '<pre>'; print_r($data); die();
    echo json_encode($data);
  }

  public function get_subjects_class_and_assessment_wise(){
    $data = $this->assessment_reports->get_subjects_class_and_assessment_wise();
    echo json_encode($data);
  }

  public function delete_selected_filters(){
    $data = $this->assessment_reports->delete_selected_filters();
    echo json_encode($data);
  }

  public function getSectionsEntitiesAssessmentsFromClass1(){
    $classId = $_POST['classId'];
    $is_checked = $_POST['is_checked'];
    $data['sections'] = $this->assessment_reports->getSectionsFromClass($classId);
    $data['assessments'] = $this->assessment_reports->getClsAssessments_new1($classId, $is_checked);
    echo json_encode($data);
  }

  public function get_sections_from_class() {
    $classId = $_POST['classId'];
    $data = $this->assessment_reports->getSectionsFromClass($classId);
    echo json_encode($data);
  }

  public function grade_wise_result_analysis(){
    $data['acad_years'] = $this->assessment_reports->get_academic_years();
    $data['gradings'] = $this->assessment_reports->get_grading_systems();
    $data['main_content'] = 'examination/reports/grade_wise_result_analysis';
    $this->load->view('inc/template', $data);
  }

  public function get_classes_acad_year_wise() {
    $data = $this->assessment_reports->get_classes_acad_year_wise($this->input->post());
    echo json_encode($data);
  }

  public function get_assessments_class_wise() {
    $data = $this->assessment_reports->get_assessments_class_wise_v2();
    echo json_encode($data);
  }

  public function get_yearly_graph_data() {
    $inputs= $this->input->post();
    $grading_system= $inputs['grading_system'];
    $pass_percentage= $inputs['pass_percentage'];
    $grading_systems= $this->assessment_reports->__get_grading_systems_by_id($grading_system);
    $final_result= new stdClass();
    foreach($inputs['acad_year_ids'] as $key => $val) {
      $key_name= $val."_".$inputs['assessments_ids'][$key];
      $final_result->$key_name = $this->assessment_reports->get_yearly_graph_data($val, $inputs['class_ids'][$key], $inputs['assessments_ids'][$key], $pass_percentage, $grading_systems);
    }

    // echo '<pre>'; print_r($final_result); die();
    echo json_encode($final_result);
  }

  public function assessment_wise_subject_analysis_filter(){
    $data['acad_years'] = $this->assessment_reports->get_academic_years();
    $data['gradings'] = $this->assessment_reports->get_grading_systems();
    $data['main_content'] = 'examination/reports/assessment_wise_subject_analysis_filter';
    $this->load->view('inc/template', $data);
  }

  function assessmentWiseSubjectAnalysis() {
    $class_id= $this->input->post('class_id');
    $data['classDetails']= $this->assessment_reports->getClassAndAcadYear($class_id);
    $data['graphycalData']= $this->assessment_reports->getDataForAssessmentWiseSubjectAnalysis();
    $data['assessment_ids']= isset($input['assessment_id']) ? $input['assessment_id'] : [];

    $groups= isset($_POST['groups']) ? $_POST['groups'] : [];
    $components= isset($_POST['components']) ? $_POST['components'] : [];
    $electives= isset($_POST['electives']) ? $_POST['electives'] : [];

    // echo '<pre>'; print($groups); die();

    $data['subjectCounts']= count($groups) + count($components) + count($electives);

    // echo '<pre>'; print($data['subjectCounts']); die();

    if(empty($data['graphycalData'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong OR students marks are not added');
    } else {
      // $this->session->set_flashdata('flashSuccess', 'Necessary data found, Please Wait....!');
    }
    $data['main_content'] = 'examination/reports/assessment_wise_subject_analysis_report';
    $this->load->view('inc/template', $data);
  }

  public function student_wise_multi_year_analysis_filter(){
    $data['classList'] = $this->assessment_reports->get_classLists();
    $data['subjectMasterLists'] = $this->assessment_reports->get_subjectMasterLists();
    $data['main_content'] = 'examination/reports/student_wise_multi_year_analysis_filter';
    $this->load->view('inc/template', $data);
  }

  public function getClassSectionWiseStudents() {
    $data = $this->assessment_reports->getClassSectionWiseStudents();
    echo json_encode($data);
  }

  public function getStudentWiseAcadYears() {
    $data = $this->assessment_reports->getStudentWiseAcadYears();
    echo json_encode($data);
  }

  public function getAssessmentsByStudentsAcadYearAndStudentId() {
    $data = $this->assessment_reports->getAssessmentsByStudentsAcadYearAndStudentId();
    echo json_encode($data);
  }

  function studentWiseMultiYearAnalysisReport() {
    $data['graphycalData']= $this->assessment_reports->getDataForStudentWiseMultiYearAnalysis();
    $data['main_content'] = 'examination/reports/studentWiseMultiYearAnalysisReport';
    $this->load->view('inc/template', $data);
  }

  function map_subject_master_over_the_examination_subjects() {
    $data['acadYears']= $this->assessment_reports->getAcadYears();
    $data['subjectMasterLists'] = $this->assessment_reports->get_subjectMasterLists();
    $data['currentAcadYear']= $this->settings->getSetting('academic_year_id');
    if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) { 
      $data['main_content'] = 'examination/reports/map_subject_master_over_the_examination_subjects_mobile';
    } else {
      $data['main_content'] = 'examination/reports/map_subject_master_over_the_examination_subjects';
    }
    $this->load->view('inc/template', $data);
  }

  function getClasses() {
    $data = $this->assessment_reports->getClasses();
    echo json_encode($data);
  }

  function getMappedSubjectsClassWise() {
    $data = $this->assessment_reports->getMappedSubjectsClassWise();
    // echo '<pre>'; print_r($data); die();
    echo json_encode($data);
  }

  function getAcadYearWiseClassWiseSubjects() {
    $data = $this->assessment_reports->getAcadYearWiseClassWiseSubjects();
    // echo '<pre>'; print_r($data); die();
    echo json_encode($data);
  }

  function submit_subjectExaminations_to_subjectMAster() {
    $data = $this->assessment_reports->submit_subjectExaminations_to_subjectMAster();
    // echo '<pre>'; print_r($data); die();
    echo ($data);
  }

  public function staff_performance_index() {
    if (!$this->authorization->isAuthorized('EXAMINATION.STAFF_PERFORMANCE_INDEX')) {
      $this->session->set_flashdata('flashError', 'You do not have permission to access this resource');
      redirect('examination/Assessments/index', 'refresh');
    }

    $data['classList'] = $this->assessment_model->getClassess();
    
    // Get saved filter values from cookies
    $data['selectedClass'] = $this->input->cookie('staff_performance_index_class') ?: '';
    $data['selectedSection'] = $this->input->cookie('staff_performance_index_section') ?: '';
    $data['selectedAssessment'] = $this->input->cookie('staff_performance_index_assessment') ?: '';
    $data['hideAuto'] = $this->input->cookie('staff_performance_index_hide_auto') ?: false;
    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/staff_performance_index_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'examination/reports/staff_performance_index_mobile';
    } else {
      $data['main_content'] = 'examination/reports/staff_performance_index';
    }

    $this->load->view('inc/template', $data);
  }

  public function staff_performance_index_result() {
  if (!$this->authorization->isAuthorized('EXAMINATION.STAFF_PERFORMANCE_INDEX')) {
      $this->session->set_flashdata('flashError', 'You do not have permission to access this resource');
      redirect('examination/Assessments/index', 'refresh');
    }

    $classId = $this->input->post('classId');
    $section = $this->input->post('section');
    $assessment = $this->input->post('assessment');

    // Get class and section details
    $classData = $this->assessment_reports->getClassAndSectionDetails($classId, $section);
    if (!$classData) {
        $this->session->set_flashdata('flashError', 'Invalid class or section selected');
        redirect('examination/assessment_reports/staff_performance_index');
        return;
    }

    // Get assessment details
    $assessmentDetails = $this->assessment_reports->getAssessmentDetails($assessment);
    if (!$assessmentDetails) {
        $this->session->set_flashdata('flashError', 'Invalid assessment selected');
        redirect('examination/assessment_reports/staff_performance_index');
        return;
    }

    //Get subjects
    $class_subjects = $this->assessment_reports->getClassSubjects($assessment);

    // Prepare data for view
    $data = array(
        'classData' => (object)array(
            'teacher_name' => $classData['sectionData']->classteacherName,
            'class_name' => $classData['sectionData']->className,
            'section_name' => $classData['sectionData']->sectionName,
            'principal_name' => $classData['sectionData']->principalName,
            'coordinator_name' => $classData['sectionData']->coordinatorName,
            'classId' => $classData['sectionData']->classId,
            'section' => $classData['sectionData']->id
          ),
        'acad_year' => $this->acad_year->getAcadYear(),
        'assessmentDetails' => $assessmentDetails,
        'subjects' => $class_subjects,
    );

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/reports/staff_performance_index_result_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'examination/reports/staff_performance_index_result_mobile';
    } else {
      $data['main_content'] = 'examination/reports/staff_performance_index_result';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_individual_staff_performance_data() {
    $ae_id = $this->input->post('ae_id');
    $classId = $this->input->post('classId');
    $section = $this->input->post('section');
    $assessment = $this->input->post('assessment');

    // Get performance data for the specific staff
    $performanceData = $this->assessment_reports->get_individual_staff_performance_data($classId, $section, $assessment, $ae_id);
    // echo '<pre>'; print_r($performanceData); die();
    // Validate performance data
    if (!$performanceData) {
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => false,
                'message' => 'No performance data found for this subject'
            ]));
        return;
    }

    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode([
            'success' => true,
            'data' => $performanceData
        ]));
  }

}