<div class="modal fade" id="add_book_resources" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 50%; margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius: 1rem;border-top-left-radius: 1rem;">
                <h4 class="modal-title">Add Book Reference for Subject - <span id="book_resources_subject_name">NA</span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="book_resources">Book Name <font style="color: red;">*</font></label>
                    <select onchange="" class="form-control" id="book_resources" name="book_resources">
                        <option value="">Select Book name</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 29%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <span id="bookResourcesError" style="display:none"></span>
                </div>
                <div class="form-group">
                    <label for="book_page_reference">Page Reference <font style="color: red;">*</font></label>
                    <input class="form-control" name="book_page_reference" id="book_page_reference" placeholder="Page Reference">
                    <span id="bookPageReferenceError" style="display:none"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_books_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_books_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: 1rem;border-bottom-right-radius: 1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button class="btn btn-primary mt-0" onclick="bookResources()">Update</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $("#add_book_resources").on("shown.bs.modal", e => {
        const subjectName=$("#select_subject option:selected").text();
        $("#book_resources_subject_name").text(subjectName);
        getBookReferences();
    })

    $("#book_resources,#book_page_reference").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#bookResourcesError, #bookPageReferenceError").text("").css("color","red").hide();
            if($("#book_resources").val() == ""){
                $("#bookResourcesError").text("Please Select Book Name").css("color","red").show();
                return false;
            }
            if($("#book_page_reference").val() == ""){
                $("#bookPageReferenceError").text("Please Enter Page Reference").show();
                return false;
            }
            confirmBookResources();
            loadBooks();
        }
    })

    function confirmBookResources() {
        var book_resources = $('#book_resources').val();
        var book_page_reference = $('#book_page_reference').val();
        const student_visibility = $('#visible_books_to_students').is(":checked") && 1 || 0;
        if (book_resources > 0) {
            $.ajax({
                url: '<?php echo site_url('academics/lesson_plan/save_books_resources_details') ?>',
                type: 'post',
                data: { session_id, book_resources, book_page_reference, student_visibility },
                success: function (data) {
                    let parsedData = JSON.parse(data);
                    if (parsedData) {
                        $('#add_book_resources').modal('hide');
                        Swal.fire({
                            icon: "success",
                            title: "Activity saved",
                            text: "Activity saved successfully!",
                        }).then(() => {
                            getSessionData(session_id);
                            loadBooks();
                            showMainModal();
                        });
                    } else {
                        $('#add_book_resources').modal('hide');
                        Swal.fire({
                            icon: "error",
                            title: "Failed",
                            text: "Failed to save activity!",
                        }).then(() => {
                            $('#add_book_resources').modal('show');
                        });
                    }
                },
                error: function (data) {
                    console.log(data);
                    $('#add_book_resources').modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed",
                        text: "Failed to save activity!",
                    }).then(() => {
                        $('#add_book_resources').modal('show');
                    });
                }
            })
        }
    }

    function getBookReferences() {
        const grade = $("#select_grade").val();
        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/viewAllBooksByGrade') ?>',
            type: 'post',
            data: { grade },
            success: function (data) {
                const books = JSON.parse(data);
                let options;
                const { viewBooks } = books;

                if (viewBooks.length) {
                    options = `<option value="">Select Book Name</option>`
                    viewBooks.forEach(b => {
                        options += `<option value=${b.id}>${b.name}</option>`
                    })
                } else {
                    options = `<option value="">Book not found</option>`
                }
                $("#book_resources").html(options);
            }
        })
    }

    function bookResources() {
        $("#bookResourcesError, #bookPageReferenceError").text("").hide();
        if($("#book_resources").val() == ""){
            $("#bookResourcesError").text("Please Select Book Name").css("color","red").show();
            return false;
        }
        if($("#book_page_reference").val() == ""){
            $("#bookPageReferenceError").text("Please Enter Page Reference").css("color","red").show();
            return false;
        }
        confirmBookResources();
        loadBooks();
    }
</script>