
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('admission_process');?>">Applications</a></li>
  <li><a href="<?php echo site_url('admission_process/admission_settings');?>">Admission Settings</a></li>
  <li class="active">Admission settings add</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admission_process') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Form Settings
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
        <form enctype="multipart/form-data" method="post" id="application-form" action="<?php echo site_url('admission_process/submit_admission_settings');?>" data-parsley-validate="" class="form-horizontal">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Academic Year applied for  <font style="color:red">*</font></label>
                    <div class="col-md-6 col-xs-12">
                        <select class="form-control" name="acad_year" id="acad_year" required>
                            <option value="">Select Year</option>
                            <?php foreach ($acad_year as $key => $val) { ?>
                                <option value="<?php echo $val->id ?>"><?php echo $val->acad_year ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Form Year</label>
                    <div class="col-md-6 col-xs-12">                      
                        <input type="text" class="form-control" value="" name="form_year">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Admission header Logo</label>
                    <div class="col-md-6 col-xs-12">           
                        <input type="file" class="form-control" value="" name="admission_logo">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Admission background Logo</label>
                    <div class="col-md-6 col-xs-12">           
                        <input type="file" class="form-control" value="" name="admission_bg_logo">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Application Fee Amount</label>
                    <div class="col-md-6 col-xs-12">           
                        <input type="text" class="form-control" name="admission_fee_amount">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">Form Name</label>
                    <div class="col-md-6 col-xs-12">           
                        <input type="text" class="form-control" value="" name="form_name">
                        <span class="help-block">Ex: Grade 1 to 10 for 2020-21 </span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">School Full Name</label>
                    <div class="col-md-6 col-xs-12">                   
                        <input type="text" class="form-control" value="" name="school_full_name">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label">School Short Name</label>
                    <div class="col-md-6 col-xs-12">
                        <input type="text" class="form-control" value="" name="school_short_name">
                    </div>
                </div>

                <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">School Address</label>
                            <div class="col-md-6 col-xs-12">
                            	
                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="line1" name="school_address[line1]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="line2" name="school_address[line2]">
									</div>
                            	</div>

                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="line3" name="school_address[line3]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="line4" name="school_address[line4]">
									</div>
                            	</div>

                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="fax" name="school_address[fax]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="" placeholder="email" name="school_address[email]">
									</div>
                            	</div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" value="" placeholder="Phone" name="school_address[phone]">
                                    </div>
                                </div>

                            </div>
                        </div>
                        <?php  
			              $arr = array();
			              foreach ($classList as $key => $class) { 
			                $arr[$class->class_name] = $class->class_name; 
			              }
			            ?>

                        <div class="form-group">
                        	<label class="col-md-3 col-xs-12 control-label"> Class Applied for <font style="color:red">*</font></label>
                        	<div class="col-md-6 col-xs-12">
	                		 	<select name="className[]" id="classId" multiple title="Select Classes" class="form-control select" required>
			                      	<?php foreach ($arr as $key => $class) { ?>
			                        	<option <?php if(in_array($key, $selectedClasses)) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
			                      	<?php } ?>
			                    </select>
		                	</div>
                        </div>

                        <div class="form-group">
                        	<label class="col-md-3 col-xs-12 control-label"> Documents Upload Version </label>
                        	<div class="col-md-6 col-xs-12">
	                		 	<select name="documents_input_version" id="documents_input_version" class="form-control" onchange="change_document_selection()">
			                      <option value="V1">V1</option>
                                  <option value="V2">V2</option>
			                    </select>
		                	</div>
                        </div>

                        <div class="form-group" id="document_version_v1">
			             	<label class="col-md-3 col-xs-12 control-label"> Documents </label>
		                  	<div class="col-md-5 col-xs-12">
			                    <table id="documentTable" style="width: 100%">
			                        <tr>
			                            <td>
			                                <input name="documents[]" class="form-control" placeholder="Document Name" type="text" id="documentId" />
			                            </td>
			                            <td onclick="deleteRow(this)">
			                                <a href="javascript:void(0)"><span style="color:red; font-size:20px;" class="glyphicon glyphicon-remove"></span></a>
			                            </td>
			                        </tr>
			                    </table> 
                                <span class="help-block">Ex: Aadhar Card, Birth Certificate </span>
		                 	</div>
		                  	<div class="col-md-1">
		                   		<a href="javascript:void(0)" data-placement='top' class="btn" data-toggle='tooltip' data-original-title='Add Row' onclick="insRow()" id="addmorePOIbutton"  ><i style="font-size: 20px;" class="fa fa-plus-square"></i></a>
		                  	</div>
		                </div>

                        <div class="form-group" id="document_version_v2" style="display:none">
			             	<label class="col-md-3 col-xs-12 control-label"> Documents </label>
		                  	<div class="col-md-6">
                                <select name="documents_v2[]" class="form-control selectpicker" id="documents" multiple title="Select Documents">
                                    <?php if(!empty($documents_list)) { 
                                        foreach($documents_list as $key => $val) { ?>
                                            <option value="<?= $val->id ?>"><?= $val->document_name ?></option>
                                    <?php } } ?>
                                </select>
		                 	</div>
		                </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Annual Income Option </label>
                            <div class="col-md-6">
                                <textarea name="annual_income_options" class="form-control" id="annual_income_options"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Guidelines</label>
                            <div class="col-md-6">
                                <textarea name="guidelines" class="summernote"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Instructions</label>
                            <div class="col-md-6">
                                <textarea name="instructions" class="summernote"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Final Description</label>
                            <div class="col-md-6">
                                <textarea name="final_description" class="summernote"></textarea>
                            </div>
                        </div>

                        

            </div>
            <div class="col-md-12" style="margin-top: 3rem;">
            <center>                                                                       
                        <button class="btn btn-primary">Save Changes <span class="fa fa-floppy-o fa-right"></span></button>
                        <a href="<?php echo site_url('admission_process/admission_settings');?>" class="btn btn-warning">Cancel</a>
                    </center>
            </div>
        </form>

        </div>
    </div>
</div>

<style>
    .form-horizontal .form-group{
        margin-right: -10px;
        margin-left: -10px;
    }
</style>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<script>
  function insRow() {
    var subCatTable = document.getElementById('documentTable');
    var new_row = subCatTable.rows[0].cloneNode(true);
    subCatTable.appendChild(new_row);
  }

  $(document).ready(function() {
    
    $('#documents').selectpicker({
        liveSearch: true,
        liveSearchPlaceholder: 'Search fields...'
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
});

  function deleteRow(cell) {
    var rowIndex = cell.parentNode.rowIndex;
    if (rowIndex == 0) {
      alert('Row cannot be deleted');
    } else {
      document.getElementById('documentTable').deleteRow(rowIndex);
    }
  }

    function deleterowpayment(row){
        var i=row.parentNode.parentNode.rowIndex;
        if(i==1){
          alert('you can not delete');
        }else{
         document.getElementById('paymode').deleteRow(i);
        } 
    }

    function insrowpayment(){
        var x=document.getElementById('paymode');
        var new_rowp = x.rows[1].cloneNode(true);
        var plen = x.rows.length;
        new_rowp.cells[0].innerHTML = plen;
        var pinp = new_rowp.cells[1].getElementsByTagName('input')[0];
        pinp.id += plen;
        pinp.value += '';
        var pyinp = new_rowp.cells[2].getElementsByTagName('select')[0];
        pyinp.id += plen;
        pyinp.value += '';
        x.appendChild(new_rowp ); 
    }

    function change_document_selection(){
        var documents_input_version = $('#documents_input_version').val();
        if(documents_input_version == 'V1'){
            $('#document_version_v2').hide();
            $('#document_version_v1').show();
        }else{
            $('#document_version_v1').hide();
            $('#document_version_v2').show();
        }
    }
</script>
