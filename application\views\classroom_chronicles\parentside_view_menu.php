<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></li>
</ul>
<hr>
    <?php 
    
        if ($this->mobile_detect->isTablet()) {
          $this->load->view('classroom_chronicles/parent_index_tablet.php');
        }else if($this->mobile_detect->isMobile()){
          $this->load->view('classroom_chronicles/parent_index_mobile.php');
        }else{
          $this->load->view('classroom_chronicles/parent_index_desktop.php');       
        }
 ?>


<style>
ul.panel-controls > li > a.control-primary {
    color: #737373;
    font-size: 15px;
    border-radius: 50%;
}
html{
    background: white;
}

.btn-primary {
    background-color: #6893ca;
    border-color: #6893ca;
    border-radius: 20px;
}
</style>


<script type="text/javascript">
  $(document).ready(function() {
    var maxDate = new Date();
    maxDate.setDate(maxDate.getDate());
    var minDate = new Date();
    minDate.setFullYear( minDate.getFullYear() - 1);
    var dateNow = new Date();

    $('#datePicker,#select_date,#datetimepicker1').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
  });

  function Click() {
    var q = document.getElementById("name").value;
    if(q != 0) {
      swal("Loading !....", { 
        button: false,
      });  
    }
  }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     //'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>

<script type="text/javascript">

 function generate_chronicles_report(){
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $('#fromDate').html(from_date);
    $('#toDate').html(to_date);
    $.ajax({
        url: '<?php echo site_url('parent_controller/get_chronicles_report_parent'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date},
        success: function(data) {
          var echr_data = JSON.parse(data);
          console.log(echr_data);
          if (echr_data.length > 0) {
            $(".chronicles").html(prepare_chronicles_table(echr_data));            
          }else{
             $(".chronicles").html('<div class="no-data-display">Result not found</div>');
          }
          

        }
    });
  }

  function prepare_chronicles_table(chr_data) {
    var html = '';
  var total = 0;
  var header = '';
  var thead = '';
  var body = '';
  var i=1;

  thead +='<thead>';
  thead +='<tr>';
  thead +='<th>#</th>';
  thead +='<th>Date</th>';
  thead +='<th>View</th>';

  for(var key in chr_data){


  body += '<tr>'
      body += '<td>'+i+'</td>';
      body += '<td>'+chr_data[key].made_date+'</td>';
      body += '<td><a href="#" onclick="view_chronicles('+chr_data[key].id+')" data-target="#view_chronicles"  data-toggle="modal" id="view_button'+i+'" class="btn btn-primary">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></td>';
      i++;
       }
 
  header +='<table class="table table-bordered">';

  html += header + thead + body;

  return html;
}

function view_chronicles(id) {
  $.ajax({
    url: '<?php echo site_url('parent_controller/get_chronicles_by_id'); ?>',
    type: 'post',
    data: {
      'id': id,
    },
    success: function(data) {
      var resData = $.parseJSON(data);
      $('#chronicles-detail').html(construct_view_chronicles(resData));
    }
  });
}

function construct_view_chronicles(resData) {
  var viewhtml = '';
  viewhtml += '<div class="content-div" style="height:350px; overflow-y: auto;""> <b> Contant : </b> <br>' +resData.chronicle_content+ '</div><br>';
  viewhtml += '<br>';
  if(resData.file_path != ''){
    viewhtml += '<b>Attachment: <b>';
    viewhtml += '<br>';
    viewhtml += '<a onclick="download_chronicles('+resData.id+')" class="btn btn-primary"><i class="fa fa-download"> </i> Download </a>';
  }

  return viewhtml;
}

function download_chronicles(rowid) {
  window.location.href = '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/download_chronicles_by_rowid/'); ?>' + rowid;
}
</script>

<style type="text/css">
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
    max-height: 30%;
    overflow-y: scroll;
}
</style>

