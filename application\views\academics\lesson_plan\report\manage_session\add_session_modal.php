<div class="modal" tabindex="-1" role="dialog" id="add_session">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" style="font-size: 1.5rem;">Add New Session <span id="weekName"></span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 2.5rem;" onclick="resetForm('Add')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="session-form" data-parsley-validate>
                    <input type="hidden" name="current-week-name" id="current-week-name">
                    <div class="class-subject-section" style="">
                        <div class="" id="lesson_session" style="width: 100%;">
                            <label for="lesson_id">Choose Lesson <font style="color:red;">*</font></label>
                            <select class="form-control" name="lesson_id" id="lesson_id" onchange="getTopicsList()" required style="">
                                <option value="">Select Lesson</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 15%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>

                        <div class="" id="topic_session" style="width: 100%;">
                            <label for="topic_id">Choose Topic <font style="color:red;">*</font></label>
                            <select class="form-control" name="topic_id" id="topic_id" required style="" onchange="getDefaultSessionName()">
                                <option value="">Select Topic</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 33%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>

                        <div class="session_name" style="font-size: 13px;">
                            <label for="session_name">Enter Session Name <font style="color:red;">*</font></label>
                            <input type="text" name="session_name" id="session_name" class="form-control" required placeholder="Enter Session Name" style="width: 100%;border: 1px solid #d9d1d1;">
                        </div>

                        <div style="font-size: 13px;">
                            <label for="session_replicate_number">Choose Sessions Count</label>
                            <select class="form-control" name="session_replicate_number" id="session_replicate_number">
                                <option value="1" selected>1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="2">4</option>
                                <option value="5">5</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 71%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>

                        <div style="font-size: 13px;">
                            <label for="session_type">Choose Session Type</label>
                            <select class="form-control" name="session_type" id="session_type">
                                <option value="TEACHING" selected>Teaching</option>
                                <option value="ACTIVITY">Activity</option>
                                <option value="ASSESSMENT">Assessment</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 89%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetForm('Add')">Close</button>
                <button type="button" class="btn btn-primary mt-0" id="addSession" onClick="addLPSession()">Add Session</button>
            </div>
        </div>
    </div>
</div>

<script>
    // $(document).ready(e => {
    //     $('#add_session').modal("hide");
    // })

    let lessondata;
    // $('#add_session').modal({
    //     backdrop: 'static',
    //     keyboard: false
    // })

    $("#add_session").on("shown.bs.modal", function (e) {
        const dataSet = e.relatedTarget.dataset;
        lessondata = {
            class_master_id: dataSet.class_master_id,
            subject_id: dataSet.subject_id,
            lp_week_id: dataSet.lp_week_id,
            weekNo: dataSet.weekNo
        }
        $("#current-week-name").val(lessondata.weekNo);
        getSubjectLessons(lessondata)
    })

    $("#add_session").on("hidden.bs.modal", function (e) {
        console.log("modal closed")
        // $("#topic_id").ch
        const topicOptionChildren=document.querySelector("#topic_id").querySelectorAll("option");
        if(topicOptionChildren?.length>1){
            topicOptionChildren.forEach((o,i)=>{
                if(i){
                    o.remove();
                }
            })
        }

        $("#session_name").val("");

        const sessionCount=document.querySelector("#session_replicate_number").querySelectorAll("option");
        sessionCount.forEach((s,i)=>{
            s.removeAttribute("selected")
            if(i==0){
                s.setAttribute("selected","selected")
            }
        })

        const sessionType=document.querySelector("#session_type").querySelectorAll("option");
        sessionType.forEach((st,i)=>{
            st.removeAttribute("selected")
            if(i==0){
                st.setAttribute("selected","selected")
            }
        })
    })

    function getSubjectLessons({ subject_id }) {
        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/get_lessons') ?>',
            type: 'POST',
            data: { subject_id },
            success: function (data) {
                const lessonData = $.parseJSON(data);

                let html = `<option value="">Select Lesson</option>`;
                lessonData.forEach((l, i) => {
                    ++i;
                    html += `<option select value="${l.id}" lesson-index=${i}>${i}- ${l.lesson_name}</option>`
                })
                $("#lesson_id").html(html);
            }
        });
    }

    function getTopicsList() {
        const { subject_id } = lessondata;
        let lesson_id = $("#lesson_id").val();

        const currentLessonIndex = $("#lesson_id :selected")[0].getAttribute("lesson-index");
        // window.sessionStorage.setItem("lessonIndex", currentLessonIndex);

        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/view_lesson') ?>',
            type: 'POST',
            data: { subject_id },
            success: function (data) {
                const topicData = $.parseJSON(data).viewLessons;
                topicData.forEach(l => {
                    if (subject_id == l.lp_subject_id && lesson_id == l.lesson_id) {
                        let html = `<option value="">Select Topic</option>`;
                        l.sub_topic_arr.forEach((t, i) => {
                            // console.log(t)
                            ++i;
                            html += `<option value="${t.sub_topic_id}" topic-index="${currentLessonIndex}.${i}">${currentLessonIndex}.${i}- ${t.sub_topic_name}</option>`;
                        })
                        $("#topic_id").html(html);
                    }
                })
            }
        });
    }

    // async function gettingSessionIndexes() {
    //     let sessionIndexArray = [];
    //     const lpSubjectId = $("#subject_id_main").val();
    //     try {
    //         await $.ajax({
    //             url: '<?php //echo site_url('academics/ManageSubjects/get_used_lp_session_indexes') ?>',
    //             type: 'POST',
    //             data: { "subject_id": lpSubjectId },
    //             success: function (data) {
    //                 sessionIndexArray = JSON.parse(data);
    //             }
    //         })
    //         return sessionIndexArray;
    //     } catch (err) {
    //         throw err;
    //     }
    // }

    // getting lesson topic index to generate lp session index
    // async function getSessionIndexes() {
    //     try {
    //         const usedSessionIndex = await gettingSessionIndexes();
    //         const usedSessionIndexArray = [];
    //         if (usedSessionIndex?.length) {
    //             usedSessionIndex.forEach(s => {
    //                 usedSessionIndexArray.push(s.session_index)
    //             })
    //         }
    //         const maxSessionIndexes = 10;
    //         const lessonTopicIndex = $("#topic_id :selected")[0].getAttribute("topic-index");
    //         if (lessonTopicIndex) {
    //             let options = '<option value="0">Choose session index</option>';
    //             for (let i = 1; i <= maxSessionIndexes; i++) {
    //                 const currentIndex = `${lessonTopicIndex}.${i}`;
    //                 if (!usedSessionIndexArray.join(",").includes(currentIndex)) {
    //                     options += `<option value="${currentIndex}">${currentIndex}</option>`;
    //                 }
    //             }
    //             $("#sessionIndex").html(options);
    //         }
    //     } catch (err) {
    //         console.log(err.message)
    //     }
    // }

    function getDefaultSessionName(){
        const topicName=$("#topic_id :selected").text();
        $("#session_name").val(`Session - ${topicName}`);
    }
</script>

<style>
    .modal-dialog {
        width: 50%;
        margin: auto;
    }

    .form-control {
        margin-bottom: 10px;
    }

    ::placeholder {
        /* color: #b85b05; */
        opacity: 1;
    }

    :-ms-input-placeholder {
        color: red;
    }

    ::-ms-input-placeholder {
        color: red;
    }
</style>