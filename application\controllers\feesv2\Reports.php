<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  28 Feb 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fees_structure
 */
class Reports extends CI_Controller {


  public $columnList = [
    [
      'dispaly_name'=>'Receipt Number',
      'column_name'=>'receipt_number as receipt_no',
      'var_name'=>'receipt_no',
    ],
    [
      'dispaly_name'=>'Paid date',
      'column_name'=>'date_format(ft.paid_datetime,"%d-%m-%Y") as paid_date',
      'var_name'=>'paid_date',
    ],

    [
      'dispaly_name'=> 'Student Name',
      'column_name' => "CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name",
      'var_name'=>'student_name',

    ],

    [
      'dispaly_name'=>'Class',
      'column_name'=>"CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name",
      'var_name'=>'class_name',
    ],

    [
      'dispaly_name'=>'Concession',
      'column_name'=>'format(ft.concession_amount,2,"EN_IN") as concession',
      'var_name'=>'concession',
    ],

    [
      'dispaly_name'=>'Remarks',
      'column_name'=>'ftp.remarks',
      'var_name'=>'remarks',
    ],

    [
      'dispaly_name'=>'Paid Amount',
      'column_name'=>'format(ft.amount_paid,2,"EN_IN") as paid_amount',
      'var_name'=>'paid_amount',
    ],

    [
      'dispaly_name'=>'Payment Type',
      'column_name'=>'ftp.payment_type',
      'var_name'=>'payment_type',
    ],

    [
      'dispaly_name'=>'Fee Amount',
      'column_name'=>'',
      'var_name'=>'fee_amount',
    ],

    [
      'dispaly_name'=>'Installments',
      'column_name'=>'',
      'var_name'=>'installment',
    ],

    [
      'dispaly_name'=>'POS',
      'column_name'=>'ft.card_charge_amount as pos',
      'var_name'=>'pos',
    ],

    [
      'dispaly_name'=>'Fine Amount',
      'column_name'=>'ft.fine_amount as fine_amount',
      'var_name'=>'fine_amount',
    ],

    [
      'dispaly_name'=>'Discount Amount ',
      'column_name'=>'ft.discount_amount as discount_amount',
      'var_name'=>'discount_amount',
    ],

    [
      'dispaly_name'=>'Collected By',
      'column_name'=>'ft.collected_by as collected_by',
      'var_name'=>'collected_by',
    ],
    [
      'dispaly_name'=>'Components',
      'column_name'=>'',
      'var_name'=>'components',
    ],
    [
      'dispaly_name'=>'POS charges',
      'column_name'=>'ft.card_charge_amount as card_charge_amount',
      'var_name'=>'card_charge_amount',
    ],

    [
      'dispaly_name'=>'Refund Amount',
      'column_name'=>'ft.refund_amount as refund_amount',
      'var_name'=>'refund_amount',
    ],    

 ];  
  private $yearId;
	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEESV2')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/reports_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->helper('texting_helper');
    $this->load->helper('fees_helper');
    $this->load->model('avatar');
    $this->load->library('filemanager');
    $this->load->model('communication/emails_model', 'emails');
    $this->yearId = $this->acad_year->getAcadYearId();
  }

  public function columns($blueprint_id){
    $data['blueprint'] = $this->reports_model->get_blueprints_selection($blueprint_id);
    $data['selected_filter'] = $this->reports_model->get_selected_filters($blueprint_id);
    $data['selected_columns'] = $this->reports_model->get_selected_columns($blueprint_id);
    $data['blueprint_id'] = $blueprint_id;
    $data['main_content'] = 'feesv2/reports/columns';
    $this->load->view('inc/template', $data);
  }

  public function insert_blueprint_filters(){
    $input = $this->input->post();
    $result = $this->reports_model->insert_blueprint_report_filter($input);
    if ($result) {
       $this->session->set_flashdata('flashSuccess', 'Fee Blueprint report filter added Successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    redirect('feesv2/reports/columns/'.$input['blueprint_id']);
  }

  public function daily_transcation(){
    if (!$this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT')) {
      redirect('dashboard', 'refresh');
    }

    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();

    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;

    // Supported filters and columns by daily report
    $reportFilters = [
      'class', 'from_to_date', 'payment_mode',  'admission_type',  'is_rte', 'collectedBy', 'donors', 'medium', 'board','stop','kilometer','combination', 'payment_mode'
    ];

    $supportedColumns = [
      'paid_date', 'receipt_no', 'student_name', 'class_name', 'paid_amount', 'concession', 'components', 'remarks','card_charge_amount','payment_type','refund_amount', 'fine_amount','discount_amount' 
    ];

    $finalFilters = array();
    $finalColumns = array();
    $payment_modes = array();

    $selected_filter = $this->reports_model->get_selected_filters($fee_blueprints->id);
    $selected_columns = $this->reports_model->get_selected_columns($fee_blueprints->id);

    foreach ($reportFilters as $key => $rf) {
      if (!empty($selected_filter)) {
        foreach ($selected_filter as $key => $filter) {
          if ($rf === $filter) {
            $finalFilters[] = $rf;
          }
        }
      }
    }
    
    foreach ($supportedColumns as $key => $sc) {
      if (!empty($selected_columns)) {
        foreach ($selected_columns as $key => $columns) {
          if ($sc === $columns) {
            $finalColumns[] = $sc;
          }
        }
      }
    }
    $payment_modes = json_decode($fee_blueprints->allowed_payment_modes);
   
    $postData = $this->input->post();
    $filterMode = empty($postData);
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['stops'] = $this->fees_cohorts_model->get_fee_Stop_list();
    $data['kilometer'] = $this->fees_cohorts_model->get_fee_km_list();
    $data['combination'] = $this->Student_Model->getCombList();
    $data['payment_mode'] = $payment_modes;
    $data['payment_modeJSON'] = json_encode($payment_modes);
    // echo "<pre>"; print_r($data['payment_mode']); die();
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalFilters'] = $finalFilters;
    $data['finalColumns'] = $finalColumns;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'feesv2/reports/daily_transcation';
    $this->load->view('inc/template_fee', $data);
  }

  public function concessions_new($fee_type ='') {
    $data['fee_type'] = $fee_type;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/concessions_new';
    $this->load->view('inc/template', $data);
  }
  public function concessions_day_report($fee_type ='') {
    $data['fee_type'] = $fee_type;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/view_concession_day_report';
    $this->load->view('inc/template', $data);
  }

  public function get_fee_concession_student_count(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $fee_type = $_POST['fee_type'];
    $studentIds = $this->reports_model->get_concession_student_count($classSectionId,$fee_type,$classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_fee_concession_details(){
    $student_ids = $_POST['student_ids'];
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $classId = $_POST['classId'];
    $result = $this->reports_model->get_concession_list_new($student_ids, $fee_type, $classSectionId, $classId);
    echo json_encode($result);
  }
  public function get_fee_concession_day_details(){
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $classId = $_POST['classId'];
    $from_date=date('Y-m-d', strtotime($_POST['from_date']));
    // echo "<pre>"; print_r($from_date);die();
    $to_date=date('Y-m-d', strtotime($_POST['to_date']));
    $result = $this->reports_model->get_concession_day_list_new($fee_type, $classSectionId, $classId,$from_date,$to_date);
    echo json_encode($result);
  }

  public function concessions() {
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
     $data['concession_list'] = $this->reports_model->get_concession_list($data['fee_blueprints'][0]->id);
    }else{
      $data['concession_list'] = $this->reports_model->get_concession_list($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_type_id;
  
    // echo "<pre>"; print_r($data['concession_list']); die();
    $data['main_content'] = 'feesv2/reports/concessions';
    $this->load->view('inc/template_fee', $data);
  }

  public function generate_report_for_daily(){

    $selectedColumns = $this->input->post('selectedColumns');
    $fields = [];
    $display_name = [];
    $colString = '';
    foreach ($selectedColumns as $val) {
      foreach ($this->columnList as $column) {
        if ($val === $column['var_name'] ) {
          if ($colString != '') {
            $colString .= ', ';
          }
          $colString .= $column['column_name'];
          $display_name[]=$column['dispaly_name'];
          $var_name[] =$column['var_name'];
        }
      }
    }
    
    $clsId = $this->input->post('clsId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $kilometer = $this->input->post('kilometer');
    $combination = $this->input->post('combination');

    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    
    $dailyAcademicCollection = $this->reports_model->get_daily_fee_transcation($fee_type, $clsId,$admission_type,$paymentModes,$mediumId,$donorsId,$rte_nrteId,$created_byId,$from_date,$to_date, $colString, $stopId, $kilometer, $combination);
    $payment_mode = json_decode($this->input->post('payment_modeJSON'));

    $total_collected_amount = 0;
    $total_concession = 0;
    $car_charge_amount = 0;
    $tRefund_amount = 0;
    $tFine_amount = 0;
    $tDiscount_amount = 0;
    foreach ($dailyAcademicCollection as $key => $val) {
      $total_collected_amount +=str_replace(',', '', $val->paid_amount);
      $total_concession += str_replace(',', '', $val->concession);
      if (!empty($val->card_charge_amount)) {
        $car_charge_amount += $val->card_charge_amount;
      }
      if (!empty($val->refund_amount)) {
        $tRefund_amount += $val->refund_amount;
      }
      if (!empty($val->fine_amount)) {
        $tFine_amount += $val->fine_amount;
      }
      if (!empty($val->discount_amount)) {
        $tDiscount_amount += $val->discount_amount;
      }
      if (!empty($val->payment_type)) {
        if($val->payment_type =='10'){
          $val->payment_type ='Online Payment';
        }
      }

      foreach ($payment_mode as $key => $mode) {
        if (!empty($val->payment_type)) {
          if ($mode->value == $val->payment_type) {
            if ($val->reconciliation_status != 0) {
              if ($val->reconciliation_status == 1){
                $val->payment_type =strtoupper($mode->name) . '<span style="color:red;"> <b> (N/C) </b> <br> <b> Bank Name </b>: '.$val->bank_name.'<br> <b> Date </b>: '.$val->cheque_or_dd_date.'<br> <b> Number </b> : '.$val->cheque_dd_nb_cc_dd_number.' </span>';
              }elseif($val->reconciliation_status == 2){
                $val->payment_type = strtoupper($mode->name) . ' <span"><b> (C) </b> <br> <b> Bank Name </b> : '.$val->bank_name.'<br> <b> Date </b> : '.$val->cheque_or_dd_date.'<br> <b> Number </b> : '.$val->cheque_dd_nb_cc_dd_number.'</span>';
              }
            }else if($val->payment_type == '1'){
              $val->payment_type = strtoupper($mode->name) . ' <span"><b> (C) </b> <br> <b> Bank Name </b> : '.$val->bank_name.'<br> <b> Date </b> : '.$val->cheque_or_dd_date.'<br> <b> Number </b> : '.$val->cheque_dd_nb_cc_dd_number.'</span>';
            }else{
               $val->payment_type =strtoupper($mode->name);
            }
          }
        }

      }
    }
    $template = '';
    if (empty($dailyAcademicCollection)) {
      $template .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .= '<div>
        <label>Total Collected Amount : '.numberToCurrency_withoutSymbol($total_collected_amount).'</label><br>
        <label>Total Concession : '.numberToCurrency_withoutSymbol($total_concession).'</label><br>
        <label>POS Charges : '.numberToCurrency_withoutSymbol($car_charge_amount).'</label><br>
        <label>Fine Amount : '.numberToCurrency_withoutSymbol($tFine_amount).'</label><br>
        <label>Discount Amount : '.numberToCurrency_withoutSymbol($tDiscount_amount).'</label><br>
        <label>Refund Amount : '.numberToCurrency_withoutSymbol($tRefund_amount).'</label><br>
        </div>';
      $template .="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span>";

      $template .="<table class='table table-bordered fee_export_excel'>";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th>Sl #</th>';
      foreach ($display_name as $value) {
        $template .='<th>'.$value.'</th>';
      }

      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $i=1;
      foreach ($dailyAcademicCollection as $db_col => $res) {
        $template .='<tr>';
        $template .='<td>'.$i++.'</td>';
        if(!empty($var_name)){
          foreach ($var_name as $col) {
            $template .='<td>'.$res->{$col}.'</td>';
          }
        }
        $template .='</tr>';
      }
      $template .="</tbody>";
    $template .="</table>";
    }


    print($template);

  }
  private function numberToCurrency($number){


  }

  public function fee_balance(){
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;
    $data['installment_types'] = $this->reports_model->get_installments_types($data['selected_blueprint']);
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/balance';
    $this->load->view('inc/template_fee', $data);
  }

  public function installment_type_installment(){
    $installment_type = $_POST['installment_type'];
    $result = $this->reports_model->get_installamentsby_instype($installment_type);
    echo json_encode($result);
  }

  public function genearte_balance_report_student_list(){
    $clsId = $_POST['clsId'];
    $installment_type = $_POST['installment_type'];
    $installmentId = $_POST['installmentId'];
    $fee_type = $_POST['fee_type'];
    $due_cross = $_POST['due_cross'];
    $classSectionId = $_POST['classSectionId'];
    $rte_nrte =(isset($_POST['rte_nrte']))? $_POST['rte_nrte'] : 0;
    $students = $this->reports_model->get_installamentsfee_data_by_instype_student_length($clsId, $installment_type, $installmentId, $fee_type, $due_cross, $classSectionId, $rte_nrte);
    // echo "<pre>"; print_r($students); die();
    $student_ids = array_chunk($students, 100);
    echo json_encode($student_ids);
  }

  public function genearte_balance_report(){
    $clsId = $_POST['clsId'];
    $installment_type = $_POST['installment_type'];
    $installmentId = $_POST['installmentId'];
    $fee_type = $_POST['fee_type'];
    $due_cross = $_POST['due_cross'];
    $classSectionId = $_POST['classSectionId'];
    $rte_nrte =(isset($_POST['rte_nrte']))? $_POST['rte_nrte'] : 0;
    $student_ids = $_POST['student_ids'];

    $result = $this->reports_model->get_installamentsfee_data_by_instype($clsId, $installment_type, $installmentId, $fee_type, $due_cross,$classSectionId, $rte_nrte, $student_ids);
    echo json_encode($result);

  }

  public function genearte_balance_report_sms(){
    $clsId = $_POST['clsId'];
    $installment_type = $_POST['installment_type'];
    $installmentId = $_POST['installmentId'];
    $fee_type = $_POST['fee_type'];
    $due_cross = $_POST['due_cross'];
    $stdIds = $_POST['stdIds'];
    $classSectionId = $_POST['classSectionId'];
    $rte_nrte =(isset($_POST['rte_nrte']))? $_POST['rte_nrte'] : 0;
    $result = $this->reports_model->get_installamentsfee_data_by_instype_sms($clsId, $installment_type, $installmentId, $fee_type, $due_cross,$classSectionId, $stdIds, $rte_nrte);
    echo json_encode($result);
  }
  public function check_sms_credits() {
    $this->load->helper('texting_helper');
    echo checkCredits($_POST['message'], $_POST['sms_count'], 'parent');
  }

  public function send_balance_sms(){
    $input = $this->input->post();
    if ($input['communication_type'] == 'email') {
      $this->_send_fee_balance_email($input);
    }else{
      $arraymsg = array_values($input['std_id']);
      $sent_by = $this->authorization->getAvatarId();
      $input_arr = array();
      $this->load->helper('texting_helper');
      $input_arr['student_id_messages'] = $input['std_id'];
      $input_arr['source'] = 'Fee Balance';
      $text_send_to = $input['sent_to'];
      $input_arr['mode'] = $input['communication_type'];

      $input_arr['send_to'] = $text_send_to;
      $father = sendUniqueText($input_arr);
      if($father['success'] != ''){
        $insId1 = 1;
      } else {
        $insId1 = 0;
      }

      if($insId1 == 1) {
        $this->session->set_flashdata('flashSuccess', 'SMS Sent Successfully');
      } else{
        $this->session->set_flashdata('flashSuccess', 'Something went wrong.');
      }
      redirect('feesv2/reports/fee_balance');
    }

  }

  public function _send_fee_balance_email($input){
    $email = $this->reports_model->get_fee_balance_email_templatebyId($input['emailTemplate']);
    if (!empty($email)) {
      $stakeholder_ids = array_keys($input['std_id']);
      $master_data = array(
        'subject' => $email->email_subject,
        'body' => $email->content,
        'source' => 'Fee Balance Email UI',
        'sent_by' => $this->authorization->getAvatarId(),
        'recievers' => 'Students',
        'from_email' => $email->registered_email,
        'files' => '',
        'acad_year_id' => $this->yearId,
        'visible' => 1,
        'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
        'sending_status' => 'Initiated'
      );

      $master_id = $this->emails->saveEmail($master_data);
      $sent_data = array();
      if ($master_id) {
        $this->load->model('communication/texting_model', 'texting_model');
        $sent_data = $this->reports_model->getStudents_email_fee_balance($stakeholder_ids);
      }
      $status = $this->emails->save_sending_data($sent_data, $master_id);
      if ($status) {
        $this->load->helper('email_helper');
        $email = $this->emails->getEmailInfo($master_id);

        $email_ids = [];
        foreach ($sent_data as $key => $sent) {
          if($sent->email != '' && $sent->email != null) {
            array_push($email_ids, $sent->email);
          }
        }
        $email_response = sendEmail($email->body, $email->subject, $master_id, $email_ids, $email->from_email, json_decode($email->files));
      }
    }
    $this->session->set_flashdata('flashSuccess', 'Email Sent Successfully');
    redirect('feesv2/reports/fee_balance');
  }

  public function student_wise_fee_summary(){
    if (!$this->authorization->isAuthorized('FEESV2.FEE_SUMMARY')) {
      redirect('dashboard', 'refresh');
    }

    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();

    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;

    // Supported filters and columns by daily report
    $reportFilters = [
      'class', 'class_section', 'admission_type',  'is_rte', 'collectedBy', 'donors', 'medium', 'board', 'payment_status','category','academic_year_of_joining','combination'
    ];

    $supportedColumns = [
      'student_name','admission_no','father_name', 'father_mobile_no', 'class_name', 'fee_amount', 'paid_amount', 'concession', 'components', 'transaction','fine_amount','card_charge_amount','discount_amount','balance','sts_number','academic_year_of_joining','refund_amount'
    ];

    $finalFilters = array();
    $finalColumns = array();
    $payment_modes = array();

    $selected_filter = $this->reports_model->get_selected_filters($fee_blueprints->id);
    $selected_columns = $this->reports_model->get_selected_columns($fee_blueprints->id);

    foreach ($reportFilters as $key => $rf) {
      if (!empty($selected_filter)) {
        foreach ($selected_filter as $key => $filter) {
          if ($rf === $filter) {
            $finalFilters[] = $rf;
          }
        }
      }
    }

    foreach ($supportedColumns as $key => $sc) {
      if (!empty($selected_columns)) {
        foreach ($selected_columns as $key => $columns) {
          if ($sc === $columns) {
            $finalColumns[] = $sc;
          }
        }
      }
    }
    $payment_modes = json_decode($fee_blueprints->allowed_payment_modes);

    $postData = $this->input->post();
    $filterMode = empty($postData);
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['category'] = $this->settings->getSetting('category');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['payment_mode'] = $payment_modes;
    $data['payment_modeJSON'] = json_encode($payment_modes);
    $data['combination'] = $this->Student_Model->getCombList();
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalFilters'] = $finalFilters;
    $data['finalColumns'] = $finalColumns;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['main_content'] = 'feesv2/reports/student_fee_summary';
    $this->load->view('inc/template_fee', $data);
  }

  public function getStudentsForSummary() {
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $rte_nrteId =$this->input->post('rte_nrteId');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $students = $this->reports_model->get_fee_summary_student_list($payment_status, $fee_type, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId,$rte_nrteId, $acad_year_id,$combination);
    $student_ids = array_chunk($students, 100);
    $template = '<div class="text-center"><h3>Data Not Found</h3><br></div>';
    if(!empty($student_ids)) {
      $template = '<div id="total-summary">
        <label>Total Fee Amount : </label><br>
        <label>Total Collected Amount (without POS/Fine/Discount) : </label><br>
        <label>Total Concession : </label><br>
        <label>POS Charges : </label><br>
        <label>Fine Amount : </label><br>
        <label>Discount Amount : </label><br>
        <label>Total Balance : </label><br>
        <label>Total Collected Amount (with POS/Fine/Discount) : </label><br>';

      $template .="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span><br>";
      $template .= '</div>';
      $template .="<div class='table-responsive' style='width:100%'>";
      $template .="<table class='table table-bordered table-responsive fee_export_excel'>";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th rowspan="2" style="vertical-align: middle;">Sl #</th>';
      foreach ($selectedColumns as $value) {
        if ($value == 'card_charge_amount') {
          $value ='POS_charges';
        }
        $template .='<th rowspan="2" style="vertical-align: middle";>'. ucwords(str_replace('_', ' ', $value));
         if ($value == 'transaction') {
            $template .="<table style='margin: 0;' class='table table-bordered'>";
            $template .="<tr>";
            $template .='<th width="10%">Date</th>';
            $template .='<th width="30%">Receipt No.</th>';
            $template .='<th width="20%">Amount</th>';
            $template .='<th width="20%">Concession</th>';
            $template .='<th width="20%">Payment Type</th>';
            $template .="</tr>";
            $template .="</table>";
          }
        '</th>';
      }

      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody id='summary-data'>";
      $template .="</tbody>";
      $template .="</table>";
      $template .="</div>";
    }
    echo json_encode(array('student_ids' => $student_ids, 'template' => $template));
  }

  public function generate_report_for_fee_summary_std_wise(){
    $selectedColumns = $this->input->post('selectedColumns');
    $student_ids = $this->input->post('student_ids');
    $fee_type = $this->input->post('fee_type');
    $payment_status = $this->input->post('payment_status');
    $total_collected_amount_with_out = (double) $this->input->post('total_collected_amount_with_out');
    $total_collected_amount_with = (double) $this->input->post('total_collected_amount_with');
    $total_concession = (double) $this->input->post('total_concession');
    $total_fee_amount = (double) $this->input->post('total_fee_amount');
    $total_balance = (double) $this->input->post('total_balance');
    $fineAmount = (double) $this->input->post('fineAmount');
    $posCharge = (double) $this->input->post('posCharge');
    $totalDiscount = (double) $this->input->post('totalDiscount');
    $index = (double) $this->input->post('index');
    $dailyAcademicCollection = $this->reports_model->get_fee_summary_student_id_wise($student_ids, $payment_status, $fee_type);

    /*$total_collected_amount = 0;
    $total_concession = 0;
    $total_fee_amount = 0;
    $total_balance = 0;
    $fineAmount = 0;
    $posCharge = 0;
    $totalDiscount = 0;*/
    $i=1;
    $index = $index*100;
    $data['template'] = '';
    foreach ($dailyAcademicCollection as $key => $val) {
      $total_fee_amount += str_replace(',', '', $val->fee_amount);
      $total_collected_amount_with_out += str_replace(',', '', $val->paid_amount);
      $total_concession +=  str_replace(',', '', $val->concession);
      $total_balance += str_replace(',', '', $val->balance);
      if (!empty($val->fine_amount)) {
        $fineAmount += str_replace(',', '', $val->fine_amount);
      }
      if (!empty($val->discount_amount)) {
        $totalDiscount += str_replace(',', '', $val->discount_amount);
      }
      $posCharge += str_replace(',', '', $val->card_charge_amount);

      $data['template'] .='<tr>';
      $data['template'] .='<td>'.($index + $i++).'</td>';
      if(!empty($selectedColumns)){
        foreach ($selectedColumns as $col) {
          $data['template'] .='<td>'.$val->{$col}.'</td>';
        }
      }
      $data['template'] .='</tr>';
    }

    $data['total_fee_amount'] = $total_fee_amount;
    $data['total_collected_amount_with_out'] = $total_collected_amount_with_out;
    $data['total_concession'] = $total_concession;
    $data['total_balance'] = $total_balance;
    $data['fineAmount'] = $fineAmount;
    $data['totalDiscount'] = $totalDiscount;
    $data['posCharge'] = $posCharge;
    $data['total_collected_amount_with'] = $total_collected_amount_with_out + $posCharge + $totalDiscount + $fineAmount;
    $data['total_template'] = '';

    $data['total_template'] .= '<div>
      <label>Total Fee Amount : '.numberToCurrency_withoutSymbol($total_fee_amount).'</label><br>
      <label>Total Collected Amount ( Without POS/Fine/Discount) : '.numberToCurrency_withoutSymbol($total_collected_amount_with_out).'</label><br>
      <label>Total Concession : '.numberToCurrency_withoutSymbol($total_concession).'</label><br>
      <label>POS Charges : '.numberToCurrency_withoutSymbol($posCharge).'</label><br>
      <label>Fine Amount : '.numberToCurrency_withoutSymbol($fineAmount).'</label><br>
      <label>Discount Amount : '.numberToCurrency_withoutSymbol($totalDiscount).'</label><br>
      <label>Total Balance : '.numberToCurrency_withoutSymbol($total_balance).'</label><br>
      <label>Total Collected Amount ( includes POS/Fine/Discount): '.numberToCurrency_withoutSymbol($total_collected_amount_with_out + $posCharge + $totalDiscount + $fineAmount).'</label><br>';
    $data['total_template'] .="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span><br>";
    $data['total_template'] .= '</div>';

    echo json_encode($data);
  }

  public function generate_report_for_fee_summary(){

    $selectedColumns = $this->input->post('selectedColumns');

    // $fields = [];
    // $display_name = [];
    // $colString = '';
    // foreach ($selectedColumns as $val) {
    //   foreach ($this->columnList as $column) {
    //     if ($val === $column['var_name'] ) {
    //       if ($colString != '') {
    //         $colString .= ', ';
    //       }
    //       $colString .= $column['column_name'];
    //       $display_name[]=$column['dispaly_name'];
    //       $var_name[] =$column['var_name'];
    //     }
    //   }
    // }


    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $rte_nrteId =$this->input->post('rte_nrteId');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $dailyAcademicCollection = $this->reports_model->get_fee_summary_student_wise($payment_status, $fee_type, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId,$rte_nrteId, $acad_year_id,$combination);

    $total_collected_amount = 0;
    $total_concession = 0;
    $total_fee_amount = 0;
    $total_balance = 0;
    $fineAmount = 0;
    $posCharge = 0;
    $totalDiscount = 0;
    foreach ($dailyAcademicCollection as $key => $val) {
      $total_fee_amount += str_replace(',', '', $val->fee_amount);
      $total_collected_amount += str_replace(',', '', $val->paid_amount);
      $total_concession +=  str_replace(',', '', $val->concession);
      $total_balance += str_replace(',', '', $val->balance);
      if (!empty($val->fine_amount)) {
        $fineAmount += str_replace(',', '', $val->fine_amount);
      }
      if (!empty($val->discount_amount)) {
        $totalDiscount += str_replace(',', '', $val->discount_amount);
      }
      $posCharge += str_replace(',', '', $val->card_charge_amount);
    }

    $template = '';
    if (empty($dailyAcademicCollection)) {
      $template .= '<div>
        <h3>Not found</h3><br>
      </div>';
    }else{
      $template .= '<div>
        <label>Total Fee Amount : '.numberToCurrency_withoutSymbol($total_fee_amount).'</label><br>
        <label>Total Collected Amount : '.numberToCurrency_withoutSymbol($total_collected_amount).'</label><br>
        <label>Total Concession : '.numberToCurrency_withoutSymbol($total_concession).'</label><br>
        <label>POS Charges : '.numberToCurrency_withoutSymbol($posCharge).'</label><br>
        <label>Fine Amount : '.numberToCurrency_withoutSymbol($fineAmount).'</label><br>
        <label>Discount Amount : '.numberToCurrency_withoutSymbol($totalDiscount).'</label><br>
        <label>Total Balance : '.numberToCurrency_withoutSymbol($total_balance).'</label><br>';
      $template .="<span class='help-block pull-right'>Collected amount includes discount, POS charge and fine amount. </span><br>";
      $template .= '</div>';
      $template .="<div class='table-responsive' style='width:100%'>";
      $template .="<table class='table table-bordered table-responsive fee_export_excel'>";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th rowspan="2" style="vertical-align: middle;">Sl #</th>';
      foreach ($selectedColumns as $value) {
        if ($value == 'card_charge_amount') {
          $value ='POS_charges';
        }
        $template .='<th rowspan="2" style="vertical-align: middle";>'. ucwords(str_replace('_', ' ', $value));
         if ($value == 'transaction') {
            $template .="<table style='margin: 0;' class='table table-bordered'>";
            $template .="<tr>";
            $template .='<th width="30%">Date</th>';
            $template .='<th width="20%">Amount</th>';
            $template .='<th width="20%">Concession</th>';
            $template .='<th width="30%">Payment Type</th>';
            $template .="</tr>";
            $template .="</table>";
          }
        '</th>';
      }

      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $i=1;
      foreach ($dailyAcademicCollection as $db_col => $res) {
        $template .='<tr>';
        $template .='<td>'.$i++.'</td>';
        if(!empty($selectedColumns)){
          foreach ($selectedColumns as $col) {
            $template .='<td>'.$res->{$col}.'</td>';
          }
        }
        $template .='</tr>';
      }
      $template .="</tbody>";
      $template .="</table>";
      $template .="</div>";
    }
    print($template);
  }

  public function denominations($file = 'denominations.pdf')
  {
    $this->load->helper('download');
    $name = $file;
    $data = file_get_contents('./assets/docs/'.$file);
    force_download($name, $data);

    // $this->load->library('Pdf1');
    // $dompdf = new Dompdf();

    // //Following code is added for dompdf to access and display images connecting from a self-signed server http context
    // $contxt = stream_context_create([
    //     'ssl' => [
    //     'verify_peer' => FALSE,
    //     'verify_peer_name' => FALSE,
    //     'allow_self_signed'=> TRUE
    //   ]
    // ]);
    // //echo '<pre>';print_r($dompdf);die();
    // $dompdf->set_http_context($contxt);

    // $data = '<html><body>
    //     <style type="text/css">
    //         table tr th {
    //           vertical-align: middle;
    //           border: solid 1px #474747;
    //           border-collapse: collapse;
    //           word-wrap: break-word;
    //           background:#474747;
    //           color:#fff;
    //           padding:2px;
    //           font-size: 12px;
    //         }

    //         table tr td {
    //             vertical-align: middle;
    //             border: solid 1px #474747;
    //             border-collapse: collapse;
    //             word-wrap: break-word;
    //             padding:2px;
    //             font-size: 12px;
    //             height:20px;

    //         }
    //         table{
    //             border: solid 1px #474747;
    //             border-collapse: collapse;
    //             width:100%;
    //             margin-bottom: 1%;
    //         }
    //         .student_copy{
    //           border-width:2px;
    //           border-right-style:dashed !important;
    //           border-left-style:none !important;
    //           width: 31.33%;
    //           display: inline-block !important;
    //           padding: 0px 8px;
    //         }
    //         .table tr td{
    //           height:35px;
    //         }

    //     </style>
    //     <div class="student_copy">
    //     <h3 style="margin:0; padding:0;" >Denominations :</h3><br>
    //     <table class="table">
    //         <tr>
    //           <th style="vertical-align: middle;" ><b>Cash Notes</b></th>
    //           <th colspan="2" ><b><center>Amount</center></b></th>
    //         </tr>
    //         <tr>
    //           <th></th>
    //           <th>RS.</th>
    //           <th>P</th>
    //         </tr>
    //       <tr>
    //         <td><center>X2000</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X500</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X200</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X100</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X50</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X20</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X10</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X5</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>Total</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //     </table>
    //     <table style="border: none">
    //       <tr>
    //         <td style="border: none; font-size:16px"><center><b>Cheque Details</b></center></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Cheque No. & Date</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Drawee Bank</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none;font-size:14px"><b>Branch</b></td>
    //       </tr>
    //        <tr>
    //         <td style="border: none;height: 100px;">Please note Cheques subject to realisation</td>
    //       </tr>
    //     </table>
    //   </div>
    //   <div class="student_copy">
    //     <h3 style="margin:0; padding:0;" >Denominations :</h3><br>
    //     <table class="table">
    //         <tr>
    //           <th style="vertical-align: middle;" ><b>Cash Notes</b></th>
    //           <th colspan="2" ><b><center>Amount</center></b></th>
    //         </tr>
    //         <tr>
    //           <th></th>
    //           <th>RS.</th>
    //           <th>P</th>
    //         </tr>
    //       <tr>
    //         <td><center>X2000</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X500</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X200</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X100</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X50</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X20</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X10</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X5</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>Total</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //     </table>
    //     <table style="border: none">
    //       <tr>
    //         <td style="border: none; font-size:16px"><center><b>Cheque Details</b></center></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Cheque No. & Date</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Drawee Bank</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none;font-size:14px"><b>Branch</b></td>
    //       </tr>
    //        <tr>
    //         <td style="border: none;height: 100px;">Please note Cheques subject to realisation</td>
    //       </tr>
    //     </table>
    //   </div>
    //   <div class="student_copy">
    //     <h3 style="margin:0; padding:0;" >Denominations :</h3><br>
    //     <table class="table">
    //         <tr>
    //           <th style="vertical-align: middle;" ><b>Cash Notes</b></th>
    //           <th colspan="2" ><b><center>Amount</center></b></th>
    //         </tr>
    //         <tr>
    //           <th></th>
    //           <th>RS.</th>
    //           <th>P</th>
    //         </tr>
    //       <tr>
    //         <td><center>X2000</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X500</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X200</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X100</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X50</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X20</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X10</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>X5</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //       <tr>
    //         <td><center>Total</center></td>
    //         <td></td>
    //         <td></td>
    //       </tr>
    //     </table>
    //     <table style="border: none">
    //       <tr>
    //         <td style="border: none; font-size:16px"><center><b>Cheque Details</b></center></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Cheque No. & Date</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none; font-size:14px"><b>Drawee Bank</b></td>
    //       </tr>
    //       <tr>
    //         <td style="border: none;font-size:14px"><b>Branch</b></td>
    //       </tr>
    //        <tr>
    //         <td style="border: none;height: 100px;">Please note Cheques subject to realisation</td>
    //       </tr>
    //     </table>
    //   </div>';
    // $page = '';
    // $page = 'landscape';

    // $dompdf->load_html($data);
    // if($page != '') {
    //     $dompdf->set_paper('a4', $page);
    // }

    // $dompdf->render();
    // $dompdf->stream("denominations.pdf");
    // exit(0);
    // // $output = $dompdf->output();

    // $data['main_content'] = 'feesv2/receipts/denominations';
    // $this->load->view('inc/template', $data);
  }


  public function daily_transcation_summary(){
    $date = $this->input->post('date');
    if (empty($date)) {
      $data['cDate']= date('d-m-Y');
    }else{
      $data['cDate']=  $date;
    }
    $data['allDate'] = $this->reports_model->get_all_dates_transcation();

    $data['total_challanAmount'] = $this->reports_model->getChallanAmountforDaywise($data['cDate']);
    $data['total_bankAmount'] = $this->reports_model->getBankAmountforDaywise($data['cDate']);
    $data['main_content'] = 'feesv2/reports/daily_transcation_summary';
    $this->load->view('inc/template_fee', $data);
  }


  public function reconciled_report(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    $reconcilation = $this->input->post('reconcilation');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $data['selected_blueprint'] = $fee_type_id;
    $data['reconcilation'] = $reconcilation;
    $data['from_date'] = $from_date;
    $data['to_date'] = $to_date;
    $data['reconciled'] = $this->reports_model->get_reconciled_details($fee_type_id, $reconcilation, $from_date, $to_date);
    // echo "<pre>"; print_r($data['reconciled']); die();
    // $data['main_content'] = 'feesv2/reports/non_reconciled';
    $data['main_content'] = 'feesv2/student/non_reconciled';
    $this->load->view('inc/template', $data);
  }

  public function cheques_report(){
    $data['fee_blueprint']= $this->reports_model->get_fee_blueprint_cheques();
    $data['main_content'] = 'feesv2/student/cheques_report';
    $this->load->view('inc/template', $data);
  }

  public function fee_reonciled_data(){
    $reconcilation = $this->input->post('reconcilation');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $reconciled = $this->reports_model->get_fees_reconciled_details($reconcilation, $from_date, $to_date);
    echo json_encode($reconciled);
  }

  public function fee_cheque_data(){

    $cheque = $this->reports_model->get_fees_cheque_details($_POST);
    echo json_encode($cheque);
  }

  public function get_allowed_payment_modes(){
    $data = $this->reports_model->get_allowed_payment_modes($_POST);
    echo json_encode($data);
  }


 public function class_summary(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    $data['classSectionId'] = $this->input->post('class_section');
    $category = $this->input->post('category');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
      $blueprintsComponents = $this->reports_model->get_blueprint_comp($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
      $blueprintsComponents = $this->reports_model->get_blueprint_comp($fee_type_id);
    }
    // Supported filters and columns by daily report
    $reportFilters = [
      'category','components'
    ];

    if (empty($this->input->post('category'))) {
      $category = 0;
    }else{
      $category = $this->input->post('category');
    }

    if (empty($this->input->post('component'))) {
      $component = 0;
    }else{
      $component = $this->input->post('component');
    }
    $finalFilters = array();
    $payment_modes = array();

    $selected_filter = $this->reports_model->get_selected_filters($fee_blueprints->id);

    foreach ($reportFilters as $key => $rf) {
      if (!empty($selected_filter)) {
        foreach ($selected_filter as $key => $filter) {
          if ($rf === $filter) {
            $finalFilters[] = $rf;
          }
        }
      }
    }


    $data['category'] = $this->settings->getSetting('category');
    $data['finalFilters'] = $finalFilters;
    // echo "<pre>"; print_r($finalFilters); die();
    $data['blueprint_comp'] = $blueprintsComponents;
    $data['selected_blueprint'] = $fee_blueprints->id;
    $data['payment_modes'] = json_decode($fee_blueprints->allowed_payment_modes);
    $data['class_wise'] = $this->reports_model->get_class_wise_summary($data['selected_blueprint'], $category, $component,$data['classSectionId']);
    $data['main_content'] = 'feesv2/reports/class_summary';
    $this->load->view('inc/template', $data);
  }

  public function class_wise_date_wise_report(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/class_date_wise_summary';
    $this->load->view('inc/template_fee', $data);
  }

  public function class_summary_new(){
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['adjustment'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fineAmount'] = $this->settings->getSetting('fee_fine_amount');
    $data['feeRefund'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/class_summary_new';
    $this->load->view('inc/template', $data);
  }

  public function class_daily_summary_new(){
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['adjustment'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fineAmount'] = $this->settings->getSetting('fee_fine_amount');
    $data['feeRefund'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/class_daily_summary_new';
    $this->load->view('inc/template', $data);
  }

  public function get_fee_class_summary_student_count(){
    // Handle multiple fee types
    $fee_type = $this->input->post('fee_type');
    $classSectionId = $this->input->post('classSectionId');

    // Convert fee_type to array if it's a string
    if (!is_array($fee_type)) {
      $fee_type = [$fee_type];
    }

    $result = $this->reports_model->get_fee_class_summary_student_count($fee_type);

    // Return both class IDs and detailed class data
    echo json_encode([
      'class_chunks' => array_chunk($result['class_ids'], 2),
      'class_data' => $result['class_data']
    ]);
  }

  public function get_fee_class_summary_student_details(){
    $class_ids = $this->input->post('class_ids');
    $fee_type = $this->input->post('fee_type');
    $classSectionId = $this->input->post('classSectionId');

    // Convert fee_type to array if it's a string
    if (!is_array($fee_type)) {
      $fee_type = [$fee_type];
    }

    // Get detailed class summary data including grand totals
    $result = $this->reports_model->get_fee_class_summary_student_details($class_ids, $fee_type, $classSectionId);

    echo json_encode($result);
  }

  public function get_fee_class_daily_summary_student_count(){
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $classIds = $this->reports_model->get_fee_class_summary_student_count($fee_type);
    $classId = array_chunk($classIds['class_ids'], 2);
    echo json_encode($classId);
  }

  public function get_fee_class_daily_summary_student_details(){
    $class_ids = $_POST['class_ids'];
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->reports_model->get_fee_class_daily_summary_student_details($class_ids, $fee_type, $classSectionId, $from_date, $to_date);
    echo json_encode($result);
  }

  public function generated_class_wise_daily_tx(){
    $fee_type = $_POST['fee_type'];
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $classSectionId = $_POST['classSectionId'];
    $result = $this->reports_model->get_class_wise_dailyTxRepot($fee_type, $from_date, $to_date, $classSectionId);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }
  public function search_fee_type_wise_components(){
    $fee_type = $_POST['fee_type'];
    $result = $this->reports_model->getComponents_feeType($fee_type);
    echo json_encode($result);
  }

  public function receipt_canceled_report(){
    $data['main_content'] = 'feesv2/reports/receipt_cancellation';
    $this->load->view('inc/template', $data);
  }

  public function receipt_cancelled_html_view_popup(){
    $transId  = $_POST['transId'];
    $link =$this->reports_model->view_receipt_html_fee_receipt($transId);

    $dom = new DomDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML($link);
    $node = $dom->getElementsByTagName('div')->item(0); // your div to append to

    $fragment = $dom->createDocumentFragment();
    $fragment->appendXML('<h3 class="canceled" style="position: absolute;z-index: 99999;top: 155px; left:0;transform: rotate(332deg);font-size: 120px;opacity: 0.1;">Canceled</h3>');
    $node->appendChild($fragment);

    echo $dom->saveHTML();
    // echo $this->filemanager->getFilePath($link);
  }

  public function get_canceled_recipts_list(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->reports_model->getCanceled_receipts_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function fee_reciept_view_canceled($fTrans, $canceled = ''){
    $data['cancel'] = $canceled;
    // $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
    $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
        // echo "<pre>"; print_r($data['fee_trans']); die();
    if (empty($data['fee_trans']->no_of_ins->feev2_blueprint_id)) {
      redirect('feesv2/reports/canceled_receipt_trans/'.$fTrans.'/'.$canceled);
    }
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);
    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
    $data['payment_modes'] = json_decode($blue_print->allowed_payment_modes);
    // $receipt_for = $this->settings->getSetting('receipt_for');
    $data['main_content'] = 'feesv2/receipts/'.$blue_print->receipt_for;
    $this->load->view('inc/template_fee', $data);
  }

  public function canceled_receipt_trans($fTrans, $canceled){
    $data['cancel'] = $canceled;
    $data['fee_trans'] = $this->reports_model->generate_canceled_fee_receipt_transaction($fTrans);
      // echo "<pre>"; print_r($data['fee_trans']); die();
    $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);

    $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_comp->feev2_blueprint_id);

    $data['payment_modes'] = json_decode($blue_print->allowed_payment_modes);

    $data['main_content'] = 'feesv2/receipts/'.$blue_print->receipt_for;
    $this->load->view('inc/template_fee', $data);
  }

  public function consolidated_blueprint_wise_report(){
    $data['blueprints'] = $this->reports_model->get_blueprints_all();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['main_content'] = 'feesv2/reports/consolidated';
    $this->load->view('inc/template_fee', $data);
  }

  public function generate_consilated_blueprint()
  {
    $clsId = $_POST['clsId'];
    $stdIds = $_POST['stdIds'];
    $classSectionId = $_POST['classSectionId'];
    $blueprintId = $_POST['blueprintId'];
    $paymentOptions = $_POST['paymentOptions'];
    $acad_year_id = $_POST['acad_year_id'];
    $rte_nrteId = $_POST['rte_nrteId'];
    $result = $this->reports_model->get_consolidate_student_wise_report($clsId, $stdIds, $classSectionId, $blueprintId, $paymentOptions, $acad_year_id, $rte_nrteId);
    echo json_encode($result);
  }

  public function send_consolidated_sms(){
    $input = $this->input->post();
    $arraymsg = array_values($input['std_id']);
    $sent_by = $this->authorization->getAvatarId();
    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['student_id_messages'] = $input['std_id'];
    $input_arr['source'] = 'Fee Balance';
    $text_send_to = $this->settings->getSetting('text_send_to');
    $text_mode_to_use = $this->settings->getSetting('text_mode_to_use');
    $input_arr['mode'] = 'sms';
    if($text_mode_to_use) {
      $input_arr['mode'] = $text_mode_to_use;
    }

    if($text_send_to == 'preferred') {
      $input_arr['send_to'] = 'preferred';
      $preferred = sendUniqueText($input_arr);
      if($preferred['success'] != ''){
        $insId1 = 1;
      } else {
        $insId1 = 0;
      }
    } else if($text_send_to == 'preferred_parent') {
      $input_arr['send_to'] = 'preferred_parent';
      $preferred_parent = sendUniqueText($input_arr);
      if($preferred_parent['success'] != ''){
        $insId1 = 1;
      } else {
        $insId1 = 0;
      }
    } else {
      if($text_send_to == '' || $text_send_to == 'Father' || $text_send_to == 'Both') {
        //sending to father
        $input_arr['send_to'] = 'Father';
        $father = sendUniqueText($input_arr);
        if($father['success'] != ''){
          $insId1 = 1;
        } else {
          $insId1 = 0;
        }
      }
      if($text_send_to == '' || $text_send_to == 'Mother' || $text_send_to == 'Both') {
        //sending to mother
        $input_arr['send_to'] = 'Mother';
        $mother = sendUniqueText($input_arr);
        if($mother['success'] != '') {
          $insId2 = 1;
        } else {
          $insId2 = 0;
        }
      }
    }
    if($insId1 == 1 || $insId2 == 1) {
      $this->session->set_flashdata('flashSuccess', 'SMS Sent Successfully');
    } else{
      $this->session->set_flashdata('flashSuccess', 'Something went wrong.');
    }
    redirect('feesv2/reports/consolidated_blueprint_wise_report');
  }

  public function get_sms_template_for_balance_fee(){
    $result = $this->reports_model->get_sms_template_for_balance();
    echo json_encode($result);
  }

  public function get_sms_content_for_balance_fee(){
    $tempId = $_POST['tempId'];
    $result =  $this->reports_model->get_sms_content_for_balance($tempId);
    echo json_encode($result);
  }

  public function daily_transcation_prarthana(){
    if (!$this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT_PRARTHANA')) {
      redirect('dashboard', 'refresh');
    }
    $data['main_content'] = 'feesv2/reports/daily_transcation_prarthana';
    $this->load->view('inc/template_fee', $data);
  }

  public function daily_transcation_prarthana_new(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_all_branches();
    $data['main_content'] = 'feesv2/reports/daily_transcation_prarthana_new';
    $this->load->view('inc/template_fee', $data);
  }

  public function makeDateRangeChunks() {
    $chunk_size = $_POST['chunk_size'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $days = round((strtotime($to_date) - strtotime($from_date))/86400);
    // echo 'Test';
    // print_r($days);die();
    $chunks = [];
    if($days < 0) {
    } else if($days == 0) {
      array_push($chunks, array('from_date' => $from_date, 'to_date' => $to_date));
    } else {
      $chunks = [];
      while($days>=0) {
        $chunk = [];
        $ch = $days;
        if($days > 7) {
          $ch = $chunk_size;
        }
        $chunk['from_date'] = date('Y-m-d',strtotime($to_date." -".$ch." day"));
        $chunk['to_date'] = $to_date;
        $to_date = date('Y-m-d',strtotime($chunk['from_date']." -1 day"));
        $days = round((strtotime($to_date) - strtotime($from_date))/86400);
        array_push($chunks, $chunk);
      }
    }

    echo json_encode($chunks);
  }

  public function generate_report_for_daily_prarthana_count(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $studentCounts = $this->reports_model->get_daily_fee_transcation_prarthana_count($fee_type,$from_date,$to_date);
    $student_studentIds = array_chunk($studentCounts, 150);
    echo json_encode($student_studentIds);
  }

  public function generate_report_for_daily_prarthana(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $dailyAcademicCollection = $this->reports_model->get_daily_fee_transcation_prarthana($fee_type,$from_date,$to_date);
    echo json_encode($dailyAcademicCollection);
  }

  public function generate_report_for_daily_prarthana_new(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $studentIds = $_POST['studentIds'];
    $dailyAcademicCollection = $this->reports_model->get_daily_fee_transcation_prarthana_new($fee_type,$from_date,$to_date, $studentIds);
    echo json_encode($dailyAcademicCollection);
  }

  public function day_books(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/day_books';
    $this->load->view('inc/template_fee', $data);
  }

  public function day_books1(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['payment_mode'] = [];
    if(!empty($data['fee_blueprints'])){
      $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    }
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/day_books1';
    $this->load->view('inc/template_fee', $data);
  }

  public function get_predefined_filters() {
      $predefined_filters = $this->reports_model->get_predefined_filters();
      echo json_encode($predefined_filters);
  }

  public function get_predefined_filters_by_id() {
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));

      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }

      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id($filter_id);

      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }
  public function get_predefined_filters1() {//fee_detail_report
      $predefined_filters = $this->reports_model->get_predefined_filters1();
      echo json_encode($predefined_filters);
  }

  public function get_predefined_filters_by_id1() {//fee_detail_report
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));

      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }

      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id1($filter_id);

      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }
  public function get_predefined_filters2() {//fee_detail_report
      $predefined_filters = $this->reports_model->get_predefined_filters2();
      echo json_encode($predefined_filters);
  }

  public function get_predefined_filters_by_id2() {//fee_detail_report
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));

      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }

      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id2($filter_id);

      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }

  public function get_predefined_filters3() {//management_report
      $predefined_filters = $this->reports_model->get_predefined_filters3();
      echo json_encode($predefined_filters);
  }

  public function get_predefined_filters_by_id3() {//management_report
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));

      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }

      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id3($filter_id);

      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }

  public function get_predefined_filters4() {//fee summary detail report
      $predefined_filters = $this->reports_model->get_predefined_filters4();
      echo json_encode($predefined_filters);
  }
  
  public function get_predefined_filters_by_id4() {//fee summary detail report
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));
  
      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }
  
      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id4($filter_id);
  
      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }

    public function get_predefined_filters5() {//management_day_wise_summary
      $predefined_filters = $this->reports_model->get_predefined_filters5();
      echo json_encode($predefined_filters);
  }
  
  public function get_predefined_filters_by_id5() {//management_day_wise_summary
      // Get filter ID from request
      $filter_id = $this->security->xss_clean($this->input->post('filter_id'));
  
      // Validate input
      if (empty($filter_id)) {
          $this->output->set_status_header(400);
          echo json_encode([
              'success' => false,
              'message' => 'Invalid filter ID provided.'
          ]);
          return;
      }
  
      // Fetch filter data from model
      $result = $this->reports_model->get_predefined_filters_by_id5($filter_id);
  
      // If data is found, process it
      if (!empty($result)) {
          $result->filters_selected = !empty($result->filters_selected) ? json_decode($result->filters_selected, true) : [];
          echo json_encode([
              'success' => true,
              'filters_selected' => $result->filters_selected
          ]);
      } else {
          $this->output->set_status_header(404);
          echo json_encode([
              'success' => false,
              'message' => 'No predefined filters found for this ID.'
          ]);
      }
  }



  public function generate_report_for_day_book(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $headers = $this->reports_model->get_day_books_details_headers($fee_type);
    $dailyCollection = $this->reports_model->get_day_books_details_fees_tx($fee_type,$from_date,$to_date);

    $salesCollection = [];
    if (in_array('sales',$fee_type)) {
      $salesCollection = $this->reports_model->get_day_books_details_sale_tx($from_date,$to_date, $paymentModes='');
    }
    // echo "<pre>"; print_r($dailyCollection);
    // echo "<pre>"; print_r($salesCollection);
    //  die();
    $template = '';
    if (empty($dailyCollection) &&  empty($salesCollection)) {
      $template .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .="<table class='table table-bordered fee_export_excel'>";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th>Receipt Date</th>';
      $template .='<th>Receipt Number</th>';
      $template .='<th>Student Name</th>';
      foreach ($headers as $val) {
        $template .='<th class="vertical"><span>'.$val->name.'</span></th>';
      }
      $template .='<th>Total</th>';
      $template .='<th>Remarks</th>';
      $template .='<th>Payment Type</th>';
      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $feeTx = array();
      $grTotal = 0;
      foreach ($dailyCollection as $key => $res) {
        $template .='<tr>';
        $template .='<td>'.$res->receipt_date.'</td>';
        $template .='<td>'.$res->receipt_number.'</td>';
        $template .='<td>'.$res->student_name.'('.$res->class_name.')'.'</td>';
        $feeTxV = 0;
        foreach ($headers as $val) {
          if (!empty($val->bpCompId)) {
            if (!array_key_exists($val->bpCompId, $feeTx)) {
              $feeTx[$val->bpCompId] = 0;
            }
            if (array_key_exists($val->bpCompId, $res->Components)) {
              $feeTx[$val->bpCompId] += $res->Components[$val->bpCompId];
              $feeTxV += $res->Components[$val->bpCompId];
              $template .='<td>'.$res->Components[$val->bpCompId].'</td>';
            }else{
              $template .='<td> - </td>';
            }
          }else{
            $template .='<td> - </td>';
          }
        }
        $grTotal += $feeTxV;
        $template .='<th>'.$feeTxV.'</th>';
        $template .='<td></td>';
        $template .='<td>'.$this->_get_PaymentValue($res->payment_type, $res->bank_name,$res->cheque_or_dd_date, $res->cheque_dd_nb_cc_dd_number).'</td>';
        $template .='</tr>';
      }
      $salesTx = array();
      foreach ($salesCollection as $key => $res) {
        $found = 0;
        foreach ($dailyCollection as $key => $value) {
          if ($value->receipt_book_id == $res->receipt_book_id) {
            $found = 1;
          }
        }
        // if ($found) {
        $template .='<tr>';
        $template .='<td>'.$res->receipt_date.'</td>';
        $template .='<td>'.$res->receipt_number.'</td>';
        $template .='<td>'.$res->student_name.'('.$res->class_name.')'.'</td>';
        $salesTxV = 0;
        foreach ($headers as $val) {
          if (!empty($val->variantId)) {
            if (!array_key_exists($val->variantId, $salesTx)) {
              $salesTx[$val->variantId] = 0;
            }
            if (array_key_exists($val->variantId, $res->Components)) {
              $template .='<td>'.$res->Components[$val->variantId].'</td>';
              $salesTx[$val->variantId] += $res->Components[$val->variantId];
              $salesTxV += $res->Components[$val->variantId];
            }else{
              $template .='<td> - </td>';
            }
          }else{
            $template .='<td> - </td>';
          }
        }
        $grTotal += $salesTxV;
        $template .='<th>'.$salesTxV.'</th>';
        $template .='<td></td>';
        $template .='<td>'.$this->_get_PaymentValue($res->payment_type, $res->bank_name,$res->cheque_or_dd_date, $res->cheque_dd_nb_cc_dd_number).'</td>';
        $template .='</tr>';
        // }
      }
      $template .="</tbody>";
      $template .="<foot>";
      $template .="<tr>";
      $template .="<th colspan='3'>Total</th>";
      foreach ($headers as $val) {
        if (!empty($val->bpCompId)) {
          if (array_key_exists($val->bpCompId, $feeTx)) {
            $template .='<th>'.$feeTx[$val->bpCompId].'</th>';
          }else{
            $template .='<th> - </th>';
          }
        }
      }

      foreach ($headers as $val) {
        if (!empty($val->variantId)) {
          if (array_key_exists($val->variantId, $salesTx)) {
            $template .='<th>'.$salesTx[$val->variantId].'</th>';
          }else{
            $template .='<th> - </th>';
          }
        }
      }
      $template .='<th>'.$grTotal.'</th>';
      $template .='<td></td>';
      $template .="</tr>";
      $template .="</foot>";
      $template .="</table>";
    }
    print($template);
  }
  private function _get_PaymentValue($payment_type, $bankName, $chqDDDate, $chqDDCCNumber){
      switch ($payment_type) {
        case '1':
          $pValue = 'DD <br> <span"> <b> Bank Name </b> : '.$bankName.'<br> <b> Date </b> : '.$chqDDDate.'<br> <b> Number </b> : '.$chqDDCCNumber.'</span>';
          break;
        case '2':
          $pValue = 'Credit Card';
          break;
        case '3':
          $pValue = 'Debit Card';
          break;
        case '4':
          $pValue = 'Cheque <br> <span"> <b> Bank Name </b> : '.$bankName.'<br> <b> Date </b> : '.$chqDDDate.'<br> <b> Number </b> : '.$chqDDCCNumber.'</span>';
          break;
        case '5':
          $pValue = 'Wallet Payment';
          break;
        case '6':
          $pValue = 'Challan';
          break;
        case '7':
          $pValue = 'Card (POS)';
          break;
        case '8':
          $pValue = 'Net Banking <br> <span"> <b> Bank Name </b> : '.$bankName.'<br> <b> Date </b> : '.$chqDDDate.'<br> <b> Number </b> : '.$chqDDCCNumber.'</span>';
          break;
        case '9':
          $pValue = 'Cash';
          break;
        case '10':
          $pValue = 'Online Payment';
          break;
        case '11':
          $pValue = 'UPI Payment';
        break;
        case '12':
          $pValue = 'Loan';
          break;
        case '13':
          $pValue = 'Loan';
          break;
        case '20':
          $pValue = 'St.Marys A/C';
          break;
        case '21':
          $pValue = 'Mary Madha A/C';
          break;
        case '22':
          if ($school_short_name =='advitya' || $school_short_name =='advityadegree') {
            $pValue = 'QR SCAN';
          }else{
            $pValue = 'Adjusted';
          }
          break;
        case '999':
          $pValue = 'Excess Amount';
          break;
        case '777':
          $pValue = 'Online Challan Payment';
          break;
        case '30':
          $pValue = 'Transfer from Indus';
          break;
        case '31':
          $pValue = 'Bank Deposit';
          break;
        case '32':
          $pValue = 'HDFC  net banking';
          break;
        case '33':
          $pValue = 'BOB 145 net banking';
          break;
        case '34':
          $pValue = 'BOB 066 net banking';
          break;
        case '35':
          $pValue = 'Airpay';
          break;
        case '36':
          $pValue = 'Online (Manual)';
          break;
        case '66':
          $pValue = 'Grayquest';
          break;
        case '67':
          $pValue = 'DRCC';
          break;
        case '68':
          $pValue = 'CS';
          break;
        default:
           $pValue = '';
          break;
      }
      return $pValue;
    }

  public function day_books_account(){
    $data['main_content'] = 'feesv2/reports/day_book_account';
    $this->load->view('inc/template_fee', $data);
  }

  public function day_books_account_transaction(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/reports/day_book_account_transaction';
    $this->load->view('inc/template', $data);
  }

  public function generate_report_for_day_book_account(){
    $from_date = $this->input->post('from_date');

    $bp_accounts = $this->reports_model->get_blueprints_accounts();

    $transaction = $this->reports_model->get_transactions_bpWise($from_date);
    // echo "<pre>"; print_r($bp_accounts);
    // echo "<pre>"; print_r($transaction);
    // die();
    $opening_balance = $this->reports_model->get_openening_balance($from_date);
    // echo "<pre>"; print_r($transaction);  die();

    $bp_accounts_sales = $this->reports_model->get_blueprints_accounts_sales();

    $transaction_sales = $this->reports_model->get_transactions_bpWise_sales($from_date);
    // echo "<pre>"; print_r($bp_accounts_sales);
    // echo "<pre>"; print_r($transaction);
    // die();
    $opening_balance_sales = $this->reports_model->get_openening_balance_sales($from_date);

    $mergeTransaction = array_merge($transaction, $transaction_sales);
    $mergeOb = array_merge($opening_balance, $opening_balance_sales);
    $template = '';
    if (empty($bp_accounts) &&  empty($transaction)) {
      $template .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .="<table class='table table-bordered fee_export_excel'>";
      $template .="<h3>Fees Day wise Account details</h3>";
      $template .= $this->_table_header($bp_accounts);

      $template .="<tbody>";

      $template .= $this->_table_ob_body($bp_accounts, $mergeOb);

      $template .= $this->_table_trans_body($bp_accounts, $mergeTransaction);

      $template .='</tbody>';

      $template .='<tfoot>';
      // Vertical Total

      $template .= $this->_table_vertical_total($bp_accounts, $mergeTransaction, $mergeOb);

      // Grand Total
      $template .= $this->_table_vertical_grand_total($bp_accounts, $mergeTransaction, $mergeOb);

      $template .='</tfoot>';
      $template .='</table>';
    }


    // echo "<pre>"; print_r($transaction);  die();

    $template_sales = '';
    if (empty($bp_accounts_sales) &&  empty($transaction_sales)) {
      $template_sales .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template_sales .="<div style='page-break-before: always'>";
      $template_sales .="<table class='table table-bordered fee_export_excel'>";
      $template_sales .="<h3>Sales day wise account details</h3>";
      $template_sales .= $this->_table_header($bp_accounts_sales);

      $template_sales .="<tbody>";

      $template_sales .= $this->_table_ob_body($bp_accounts_sales, $opening_balance_sales);

      $template_sales .= $this->_table_trans_body($bp_accounts_sales, $transaction_sales);

      $template_sales .='</tbody>';

      $template_sales .='<tfoot>';
      // Vertical Total

      $template_sales .= $this->_table_vertical_total($bp_accounts_sales, $transaction_sales, $opening_balance_sales);

      // Grand Total
      $template_sales .= $this->_table_vertical_grand_total($bp_accounts_sales, $transaction_sales, $opening_balance_sales);

      $template_sales .='</tfoot>';
      $template_sales .='</table>';
      $template_sales .='</div>';
    }
    echo json_encode(array('fees'=>$template,'sales'=>$template_sales));
    // print($template);
  }

  private function _table_header($bp_accounts){
    $header = '';
    $header .="<thead>";
    $header .="<tr>";
    $header .='<th rowspan="2">#</th>';
    $header .='<th rowspan="2">Name of the Student</th>';
    $header .='<th rowspan="2">Class</th>';
    $header .='<th rowspan="2">Fees Payable</th>';
     foreach ($bp_accounts as $vendorCode => $val) {
      if (!empty($val->accounts)) {
        $count = count($val->accounts);
        $header .='<th style="text-align:center" colspan='.$count.'>'.$val->name.'</th>';
        $header .='<th rowspan="2">Total</th>';
      }
    }
    $header .='<th>Fees Paid</th>';
    $header .='<th></th>';
    $header .='<th></th>';
    $header .='<th></th>';
    $header .='</tr>';
    $header .='<tr>';
    foreach ($bp_accounts as $key => $val) {
      if (!empty($val->accounts)) {
        foreach ($val->accounts as $key => $value) {
          $header .='<th>A/C. NO <br>'.$value->account.'</th>';
        }
      }
    }
    $header .='<th>Grand Total</th>';
    $header .='<th>Concession</th>';
    $header .='<th>Final Due</th>';
    $header .='<th>Remarks</th>';
    $header .='</tr>';
    $header .="</thead>";
    return $header;
  }

  public function _table_ob_body($bp_accounts, $opening_balance){
    $ob = 0;
    if (!empty($opening_balance['ob'])) {
      $ob = $opening_balance['ob'];
    }

    $ob_Body ='<tr>';
    $ob_Body .='<th></th>';
    $ob_Body .='<th></th>';
    $ob_Body .='<th></th>';
    $ob_Body .='<th></th>';
    $horizontalOBTotal = 0;
    $horizontalOBtotal_Concession = 0;
    foreach ($bp_accounts as $bp) {
      $HorizontalOBTotal = 0;
      if (!empty($bp->accounts)) {
        foreach ($bp->accounts as $vendorCode => $v) {
          if (!empty($opening_balance['account_ob'])) {
            if (array_key_exists($vendorCode, $opening_balance['account_ob']['t_con'])) {
              $horizontalOBtotal_Concession += $opening_balance['account_ob']['t_con'][$vendorCode];
              }

            if (array_key_exists($vendorCode, $opening_balance['account_ob']['t_amount'])) {
              $HorizontalOBTotal += $opening_balance['account_ob']['t_amount'][$vendorCode];
              $horizontalOBTotal += $opening_balance['account_ob']['t_amount'][$vendorCode];
              $ob_Body .='<th>'.$opening_balance['account_ob']['t_amount'][$vendorCode].'</th>';
            }else{
              $ob_Body .='<th>-</th>';
            }
          }
        }
        $ob_Body .='<th>'.$HorizontalOBTotal.'</th>';
      }
    }
    $ob_Body .='<th>'.$horizontalOBTotal.'</th>';
    $ob_Body .='<th>'.$horizontalOBtotal_Concession.'</th>';
    $ob_Body .='<th></th>';
    $ob_Body .='<th></th>';
    $ob_Body .='</tr>';
    return $ob_Body;
  }
  public function _table_trans_body($bp_accounts, $transaction){
    $transBody = '';
    $i=1;
    $g_grandTotal = 0;
    // $vGrandTotal = 0;
    // $v_grandTotal = 0;
    // $v_totalConcession = 0;
    foreach ($transaction as $key => $val) {
      $horizontal_grandTotal = 0;
      // $vGrandTotal += ($val->fees_payable - $val->previousDateFeePaid);
      $horizontal_totalConcession = 0;
      $transBody .='<tr>';
      $transBody .='<td>'.($i++).'</td>';
      $transBody .='<td>'.$val->student_name.'</td>';
      $transBody .='<td>'.$val->class_name.'</td>';
      $transBody .='<td>'.($val->fees_payable - $val->previousDateFeePaid).'</td>';
      foreach ($bp_accounts as $bp) {
        $horizontal_total = 0;
        $salesCount = 0;
        if (!empty($bp->accounts)) {
          foreach ($bp->accounts as $vendorCode => $v) {
            if (array_key_exists($vendorCode, $val->concession)) {
              $horizontal_totalConcession += ($val->concession)[$vendorCode];
              if (array_key_exists('sales',$val->components)) {
                $salesCount += ($val->components)[$vendorCode];
              }
            }
            if (array_key_exists($vendorCode, $val->components)) {
              $horizontal_total += ($val->components)[$vendorCode];

              $horizontal_grandTotal += ($val->components)[$vendorCode];
              // $v_grandTotal += ($val->components)[$vendorCode];
              $transBody .='<td>'.($val->components)[$vendorCode].'</td>';
            }else{
              $transBody .='<td>-</td>';
            }
          }
          $transBody .='<td>'.$horizontal_total.'</td>';
        }
      }
      $finalDue = $val->fees_payable - $val->previousDateFeePaid - $horizontal_grandTotal - $horizontal_totalConcession + $salesCount;

      $g_grandTotal +=$horizontal_grandTotal;
      $transBody .='<td>'.$horizontal_grandTotal.'</td>';
      $transBody .='<td>'.$horizontal_totalConcession.'</td>';
      $transBody .='<td>'.$finalDue.'</td>';
      $transBody .='<td></td>';
      $transBody .='</tr>';
    }
    return $transBody;
  }

  public function _table_vertical_total($bp_accounts, $transaction, $opening_balance){
    $t_total = '';
    $vGrandTotal = 0;
    $v_grandTotal = 0;
    $v_totalConcession = 0;
    foreach ($transaction as $key => $val) {
      $vGrandTotal += ($val->fees_payable - $val->previousDateFeePaid);
      foreach ($bp_accounts as $bp) {
        if (!empty($bp->accounts)) {
          foreach ($bp->accounts as $vendorCode => $v) {
            if (array_key_exists($vendorCode, $val->concession)) {
              $v_totalConcession += ($val->concession)[$vendorCode];
            }
            if (array_key_exists($vendorCode, $val->components)) {
              $v_grandTotal += ($val->components)[$vendorCode];
            }
          }
        }
      }
    }
    $t_total .='<tr>';
    $t_total .='<th colspan="3"><b>Total<b></th>';
    $t_total .='<th>'.$vGrandTotal.'</th>';

    foreach ($bp_accounts as $key => $bp) {
      $v_total1 = 0;
      if (!empty($bp->accounts)) {
        foreach ($bp->accounts as $vendorCode => $v) {
          $v_total = 0;
          foreach ($transaction as $key => $val) {
            if (array_key_exists($vendorCode, $val->components)) {
              $v_total += ($val->components)[$vendorCode];
              $v_total1 += ($val->components)[$vendorCode];
            }
          }
          $t_total .='<th>'.$v_total.'</th>';
        }
        $t_total .='<th>'.$v_total1.'</th>';
      }
    }

    $t_total .='<th>'.$v_grandTotal.'</th>';
    $t_total .='<th>'.$v_totalConcession.'</th>';
    $t_total .='<th>'.($vGrandTotal - $v_grandTotal - $v_totalConcession).'</th>';
    $t_total .='<th></th>';
    $t_total .='</tr>';
    return $t_total;
  }

  public function _table_vertical_grand_total($bp_accounts, $transaction, $opening_balance){
    $ob = 0;
    if (!empty($opening_balance['ob'])) {
      $ob = $opening_balance['ob'];
    }
    $vGrandTotal = 0;
    $v_grandTotal = 0;
    $v_totalConcession = 0;
    foreach ($transaction as $key => $val) {
      $vGrandTotal += ($val->fees_payable - $val->previousDateFeePaid);
      foreach ($bp_accounts as $bp) {
        if (!empty($bp->accounts)) {
          foreach ($bp->accounts as $vendorCode => $v) {
            if (array_key_exists($vendorCode, $val->concession)) {
              $v_totalConcession += ($val->concession)[$vendorCode];
            }
            if (array_key_exists($vendorCode, $val->components)) {
              $v_grandTotal += ($val->components)[$vendorCode];
            }
          }
        }
      }
    }
    $g_total ='';
    $g_total .='<tr>';
    $g_total .='<th colspan="3"><b>Grand Total<b></th>';
    $g_total .='<th></th>';
    $VOBGTotal = 0;
    $VOBGtotal_Concession = 0;
    foreach ($bp_accounts as $key => $bp) {
      $v_total1 = 0;
      $VOBTotal1 = 0;
      $VOBtotal_Concession1 = 0;
      if (!empty($bp->accounts)) {
        foreach ($bp->accounts as $vendorCode => $v) {
          $v_total = 0;
          $VOBTotal = 0;
          $VOBtotal_Concession = 0;
          foreach ($transaction as $key => $val) {
            if (array_key_exists($vendorCode, $val->components)) {
              $v_total += ($val->components)[$vendorCode];
              $v_total1 += ($val->components)[$vendorCode];
            }
          }
          if (!empty($opening_balance['account_ob'])) {
            if (array_key_exists($vendorCode, $opening_balance['account_ob']['t_con'])) {
              $VOBtotal_Concession += $opening_balance['account_ob']['t_con'][$vendorCode];
              $VOBtotal_Concession1 += $opening_balance['account_ob']['t_con'][$vendorCode];
              $VOBGtotal_Concession += $opening_balance['account_ob']['t_con'][$vendorCode];
            }

            if (array_key_exists($vendorCode, $opening_balance['account_ob']['t_amount'])) {
              $VOBTotal += $opening_balance['account_ob']['t_amount'][$vendorCode];
              $VOBTotal1 += $opening_balance['account_ob']['t_amount'][$vendorCode];
              $VOBGTotal += $opening_balance['account_ob']['t_amount'][$vendorCode];
            }
          }

          $g_total .='<th>'.($v_total + $VOBTotal).'</th>';
        }
        $g_total .='<th>'.($v_total1 + $VOBTotal1).'</th>';
      }
    }

    $g_total .='<th>'.($v_grandTotal + $VOBGTotal).'</th>';
    $g_total .='<th>'.($v_totalConcession + $VOBGtotal_Concession).'</th>';
    $g_total .='<th></th>';
    $g_total .='<th></th>';
    $g_total .='</tr>';
    return $g_total;
  }

  public function generate_report_for_day_book_account_transaction(){
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $bp_accounts = $this->reports_model->get_blueprints_accounts();

    $fee_data = $this->reports_model->get_transactions_bpWisev1($from_date, $to_date, $classId);
    $transaction = $fee_data['result'];
    $totals = $fee_data['total'];
    // echo "<pre>"; print_r($bp_accounts);
    // echo "<pre>"; print_r($transaction);
    //  die();
    // $opening_balance = $this->reports_model->get_openening_balance($from_date);
    $templateTx = '';
    if (empty($bp_accounts) &&  empty($transaction)) {
      $templateTx .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $templateTx .="<table class='table table-bordered fee_export_excel'>";
      $templateTx .="<h3>Fees Day wise Account details</h3>";

      $templateTx .="<thead>";
      $templateTx .="<tr>";
      $templateTx .='<th rowspan="2">#</th>';
      $templateTx .='<th rowspan="2">Receipt Date</th>';
      $templateTx .='<th rowspan="2">Receipt No.</th>';
      $templateTx .='<th rowspan="2">Name of the Student</th>';
      $templateTx .='<th rowspan="2">Class</th>';
       foreach ($bp_accounts as $vendorCode => $val) {
        if (!empty($val->accounts)) {
          $count = count($val->accounts);
          $templateTx .='<th style="text-align:center" colspan='.$count.'>'.$val->name.'</th>';
          $templateTx .='<th rowspan="2">Total</th>';
        }
      }
      $templateTx .='<th>Fees Paid</th>';
      $templateTx .='<th></th>';
      $templateTx .='<th></th>';
      $templateTx .='</tr>';
      $templateTx .='<tr>';
      foreach ($bp_accounts as $key => $val) {
        if (!empty($val->accounts)) {
          foreach ($val->accounts as $key => $value) {
            $templateTx .='<th>A/C. NO <br>'.$value->account.'</th>';
          }
        }
      }
      $templateTx .='<th>Grand Total</th>';
      $templateTx .='<th>Concession</th>';
      $templateTx .='<th>Remarks</th>';
      $templateTx .='</tr>';
      $templateTx .="</thead>";

      $templateTx .="<thead>";
      $templateTx .="<tr>";
      $templateTx .="<td colspan='5' style='floar:right'><strong>Total</strong></th>";
      $bp_grandTotal = 0;
      $bp_grandTotalConcession = 0;
      foreach ($bp_accounts as $key => $val) {
        $bp_total = 0;
        if (!empty($val->accounts)) {
          foreach ($val->accounts as $vendorCode => $value) {
            if (array_key_exists($vendorCode, $totals)) {
              $templateTx .='<td>'.$totals[$vendorCode].'</td>';
              $bp_total += $totals[$vendorCode];
            }else{
              $templateTx .='<td>-</td>';
            }
          }
          $templateTx .='<td><strong>'.$bp_total.'</strong></td>';
          $bp_grandTotal += $bp_total;
          if (!empty($totals['concession'])) {
            $bp_grandTotalConcession += $totals['concession'];
          }

        }
      }
      $templateTx .="<td><strong>".$bp_grandTotal."</strong></td>";
      $templateTx .="<td><strong>".$bp_grandTotalConcession."</strong></td>";
      $templateTx .="<td></td>";
      $templateTx .="</tr>";
      $templateTx .="</thead>";

      $templateTx .="<tbody>";
      $i=1;
      $g_grandTotal = 0;
      foreach ($transaction as $key => $val) {
        $horizontal_grandTotal = 0;
        // $vGrandTotal += ($val->fees_payable - $val->previousDateFeePaid);
        $horizontal_totalConcession = 0;
        $templateTx .='<tr>';
        $templateTx .='<td>'.($i++).'</td>';
        $templateTx .='<td>'.$val->receipt_date.'</td>';
        $templateTx .='<td>'.$val->receipt_number.'</td>';
        $templateTx .='<td>'.$val->student_name.'</td>';
        $templateTx .='<td>'.$val->class_name.'</td>';
        foreach ($bp_accounts as $bp) {
          $horizontal_total = 0;
          $salesCount = 0;
          if (!empty($bp->accounts)) {
            foreach ($bp->accounts as $vendorCode => $v) {
              if (array_key_exists($vendorCode, $val->concession)) {
                $horizontal_totalConcession += ($val->concession)[$vendorCode];
                if (array_key_exists('sales',$val->components)) {
                  $salesCount += ($val->components)[$vendorCode];
                }
              }
              if (array_key_exists($vendorCode, $val->components)) {
                $horizontal_total += ($val->components)[$vendorCode];

                $horizontal_grandTotal += ($val->components)[$vendorCode];
                // $v_grandTotal += ($val->components)[$vendorCode];
                $templateTx .='<td>'.($val->components)[$vendorCode].'</td>';
              }else{
                $templateTx .='<td>-</td>';
              }
            }
            $templateTx .='<td>'.$horizontal_total.'</td>';
          }
        }

        $g_grandTotal +=$horizontal_grandTotal;
        $templateTx .='<td>'.$horizontal_grandTotal.'</td>';
        $templateTx .='<td>'.$horizontal_totalConcession.'</td>';
        $templateTx .='<td></td>';
        $templateTx .='</tr>';
      }

      $templateTx .='</tbody>';

      $templateTx .='</table>';
    }

    $bp_accounts_sales = $this->reports_model->get_blueprints_accounts_sales();

    $sales_data = $this->reports_model->get_transactions_bpWise_salesv1($from_date, $to_date);

    $salesTransaction = $sales_data['result'];
    $salesTotals = $sales_data['total'];
    // echo "<pre>"; print_r($salesTotals);
    // echo "<pre>"; print_r($salesTransaction);  die();
    $template_sales = '';
    if (empty($bp_accounts_sales) &&  empty($salesTransaction)) {
      $template_sales .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template_sales .="<table class='table table-bordered fee_export_excel'>";
      $template_sales .="<h3>Sales Day wise Account details</h3>";

      $template_sales .="<thead>";
      $template_sales .="<tr>";
      $template_sales .='<th rowspan="2">#</th>';
      $template_sales .='<th rowspan="2">Receipt Date</th>';
      $template_sales .='<th rowspan="2">Receipt No.</th>';
      $template_sales .='<th rowspan="2">Name of the Student</th>';
      $template_sales .='<th rowspan="2">Class</th>';
       foreach ($bp_accounts_sales as $vendorCode => $val) {
        if (!empty($val->accounts)) {
          $count = count($val->accounts);
          $template_sales .='<th style="text-align:center" colspan='.$count.'>'.$val->name.'</th>';
          $template_sales .='<th rowspan="2">Total</th>';
        }
      }
      $template_sales .='<th>Fees Paid</th>';
      $template_sales .='<th></th>';
      $template_sales .='<th></th>';
      $template_sales .='</tr>';
      $template_sales .='<tr>';
      foreach ($bp_accounts_sales as $key => $val) {
        if (!empty($val->accounts)) {
          foreach ($val->accounts as $key => $value) {
            $template_sales .='<th>A/C. NO <br>'.$value->account.'</th>';
          }
        }
      }
      $template_sales .='<th>Grand Total</th>';
      $template_sales .='<th>Concession</th>';
      $template_sales .='<th>Remarks</th>';
      $template_sales .='</tr>';
      $template_sales .="</thead>";

      $template_sales .="<thead>";
      $template_sales .="<tr>";
      $template_sales .="<td colspan='5' style='floar:right'><strong>Total</strong></th>";
      $bp_grandTotal = 0;
      foreach ($bp_accounts_sales as $key => $val) {
        $bp_total = 0;
        if (!empty($val->accounts)) {
          foreach ($val->accounts as $vendorCode => $value) {
            if (array_key_exists($vendorCode, $salesTotals)) {
              $template_sales .='<td>'.$salesTotals[$vendorCode].'</td>';
              $bp_total += $salesTotals[$vendorCode];
            }else{
              $template_sales .='<td>-</td>';
            }
          }
          $template_sales .='<td><strong>'.$bp_total.'</strong></td>';
          $bp_grandTotal += $bp_total;
        }
      }
      $template_sales .="<td><strong>".$bp_grandTotal."</strong></td>";
      $template_sales .="<td><strong>".$salesTotals['concession']."</strong></td>";
      $template_sales .="<td></td>";
      $template_sales .="</tr>";
      $template_sales .="</thead>";

      $template_sales .="<tbody>";
      $i=1;
      $g_grandTotal = 0;
      foreach ($salesTransaction as $key => $val) {
        $horizontal_grandTotal = 0;
        // $vGrandTotal += ($val->fees_payable - $val->previousDateFeePaid);
        $horizontal_totalConcession = 0;
        $template_sales .='<tr>';
        $template_sales .='<td>'.($i++).'</td>';
        $template_sales .='<td>'.$val->receipt_date.'</td>';
        $template_sales .='<td>'.$val->receipt_number.'</td>';
        $template_sales .='<td>'.$val->student_name.'</td>';
        $template_sales .='<td>'.$val->class_name.'</td>';
        foreach ($bp_accounts_sales as $bp) {
          $horizontal_total = 0;
          $salesCount = 0;
          if (!empty($bp->accounts)) {
            foreach ($bp->accounts as $vendorCode => $v) {
              if (array_key_exists($vendorCode, $val->concession)) {
                $horizontal_totalConcession += ($val->concession)[$vendorCode];
                if (array_key_exists('sales',$val->components)) {
                  $salesCount += ($val->components)[$vendorCode];
                }
              }
              if (array_key_exists($vendorCode, $val->components)) {
                $horizontal_total += ($val->components)[$vendorCode];

                $horizontal_grandTotal += ($val->components)[$vendorCode];
                // $v_grandTotal += ($val->components)[$vendorCode];
                $template_sales .='<td>'.($val->components)[$vendorCode].'</td>';
              }else{
                $template_sales .='<td>-</td>';
              }
            }
            $template_sales .='<td>'.$horizontal_total.'</td>';
          }
        }

        $g_grandTotal +=$horizontal_grandTotal;
        $template_sales .='<td>'.$horizontal_grandTotal.'</td>';
        $template_sales .='<td>'.$horizontal_totalConcession.'</td>';
        $template_sales .='<td></td>';
        $template_sales .='</tr>';
      }

      $template_sales .='</tbody>';

      $template_sales .='</table>';
    }
    echo json_encode(array('fees'=>$templateTx,'sales'=>$template_sales));
    // print($templateTx);
  }

  public function generate_report_for_day_bookV1(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $components = $this->input->post('components');
    $components_v_h = $this->input->post('components_v_h');
    $components_exclude = $this->input->post('components_exclude');
    $paymentModes = $this->input->post('paymentModes');
    $include_delete = $this->input->post('include_delete');
    $admission_type = $this->input->post('admission_type');
    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $fee_adjustment_amount = $this->settings->getSetting('fee_adjustment_amount');
    $fee_fine_amount = $this->settings->getSetting('fee_fine_amount');
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');

    $headers = $this->reports_model->get_day_books_details_headersv1($fee_type);
    $dailyCollection = $this->reports_model->get_day_books_details_fees_txv1($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type);
    $salesCollection = [];
    $sales = $this->authorization->isModuleEnabled('SALES');
    $admission = $this->authorization->isModuleEnabled('ADMISSION');
    if ($sales) {
      if (in_array('sales',$fee_type) || in_array('All',$fee_type)) {
        $salesCollection = $this->reports_model->get_day_books_details_sale_tx($from_date,$to_date, $paymentModes, $include_delete);
      }
    }
    $appCollections = [];
    if ($admission) {
      $appCollectiondata  = [];
      if (in_array('application',$fee_type) || in_array('All',$fee_type)) {
        $appCollectiondata = $this->reports_model->get_day_books_details_appliation_tx($from_date,$to_date, $paymentModes);
      }
      if ($paymentModes) {
        foreach ($appCollectiondata as $key => $val) {
          if ($val->payment_type == $paymentModes) {
            $appCollections[] = $val;
          }
        }
      }else{
        $appCollections = $appCollectiondata;
      }

    }

    $mergeAll = array_merge($dailyCollection, $salesCollection, $appCollections);

    foreach ($mergeAll as $key => $val) {
      $val->strtootime = strtotime($val->receipt_date);
    }
    array_multisort(array_column($mergeAll,'strtootime'), SORT_DESC, array_column($mergeAll, 'receipt_number'),SORT_DESC, $mergeAll);
    // echo "<pre>"; print_r($mergeAll); die();
    // echo "<pre>"; print_r($salesCollection);
    // echo "<pre>"; print_r($headers);
     // die();
    $dailyTemplate = '';
    $template = '';
    if (empty($mergeAll)) {
      $dailyTemplate .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .="<div class='fee_export_excel table-responsive'>";
      // $template .='<select class="col-sel">';
      // $template .='<option value="">Show All</option>';
      // $template .='<option value="1">Detailed</option>';
      // $template .='<option value="2">Summary</option>';
      // $template .='</select>';

      // $template .='Toggle column: <a class="toggle-vis" data-column="0">Name</a> - <a class="toggle-vis" data-column="1">Position</a> - <a class="toggle-vis" data-column="2">Office</a> - <a class="toggle-vis" data-column="3">Age</a> - <a class="toggle-vis" data-column="4">Start date</a> - <a class="toggle-vis" data-column="5">Salary</a>';

      $template .="<table class='table table-bordered' id='details_summary_table' >";
      $template .="<thead>";

      $template .="<tr>";
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Sl #</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Receipt Date</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Receipt Number</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Student Name</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Class</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Section</p></th>';
      if ($is_semester_scheme == 1) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Semester</p></th>';
      }
      foreach ($headers as $bpName => $value) {
        $bp_name = '<p>'.$bpName.'</p>';
        if ($components_exclude) {
          $count = count($value);
          $bp_name = $bpName;
        }else{
          $count = '';
        }
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" style="text-align:center" colspan="'.$count.'" >'.$bp_name.'</th>';
        // $template .='<th style="text-align:center">'.$bpName.'</th>';
      }
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Total</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Concession</p></th>';
      if ($fee_adjustment_amount) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Adjustment</p></th>';
      }
      if ($fee_fine_amount) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Fine</p></th>';
      }
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Discount</p></th>';
      if ($loan_column) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Loan Provider Charges</p></th>';
      }
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Collected Amount</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Payment Type</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Bank Name</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>DD/Cheque Date</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Reference No.</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Remarks</p></th>';

      $template .="</tr>";

      if ($components_exclude) {
        $template .="<tr>";
        foreach ($headers as $bpName => $value) {
          foreach ($value as $key => $val) {
            $template .='<th class="headerBottom verticalTableHeader_'.$components_v_h.'"><p>'.$val->name.'</p></th>';
          }
        }
        $template .="</tr>";
      }


      $template .="</thead>";
      $template .="<tbody>";
      $feeTx = array();
      $feeSumTx = array();
      $grTotal = 0;
      $gColletedTotal = 0;
      $componentsCount = array();
      $TotalConcession = 0;
      $TotalAdjustment = 0;
      $TotalFine = 0;
      $TotalDiscount = 0;
      $TotalLoanCharge = 0;
      $m=1;
      // echo "<pre>"; print_r($mergeAll); die();
      foreach ($mergeAll as $key => $res) {
        $color = '';
        $recNumber = $res->receipt_number;
        if ($res->soft_delete == 1) {
          $color ='#f71909';
          $recNumber = $res->receipt_number.'<br>(Cancelled)';
        }
        $template .='<tr style="color:'.$color.' ;">';
        $template .='<td>'.($m++).'</td>';
        $template .='<td>'.$res->receipt_date.'</td>';
        $template .='<td>'.$recNumber.'</td>';
        $template .='<td>'.$res->student_name.'</td>';
        $template .='<td>'.$res->class_name.'</td>';
        $template .='<td>'.$res->sectionName.'</td>';
        if ($is_semester_scheme == 1) {
          $template .='<td>'.$res->semester.'</td>';
        }
        $feeTxV = 0;

        foreach ($headers as $value) {
          $bpwiseAmount = 0;
          foreach ($value as $key => $val) {
            if (!empty($val->bpId)) {
              if (!array_key_exists($val->bpId, $feeSumTx)) {
                $feeSumTx[$val->bpId] = 0;
              }
            }
            if (!empty($val->bpCompId)) {
              if (!array_key_exists($val->bpCompId, $feeTx)) {
                $feeTx[$val->bpCompId] = 0;
              }
              if (!array_key_exists($val->bpCompId, $componentsCount)) {
                $componentsCount[$val->bpCompId] = 0;
              }
              if (array_key_exists($val->bpCompId, $res->Components)) {
                if ($res->Components[$val->bpCompId] != 0) {
                  $componentsCount[$val->bpCompId]++;
                }
                $feeTx[$val->bpCompId] += $res->Components[$val->bpCompId] + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
                $feeSumTx[$val->bpId] += $res->Components[$val->bpCompId] + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
                $bpwiseAmount += $res->Components[$val->bpCompId];
                $feeTxV += $res->Components[$val->bpCompId];
                if ($components_exclude) {
                  $template .='<td>'.numberToCurrency_withoutSymbol($res->Components[$val->bpCompId]).'</td>';
                }
              }else{
                // $bpwiseAmount = 0;
                if ($components_exclude) {
                  $template .='<td> - </td>';
                }
              }
            }else{
              // $bpwiseAmount = 0;
              if ($components_exclude) {
                $template .='<td> - </td>';
              }
            }
          }
          if ($components_exclude == 0) {
            $template .='<td>'.numberToCurrency_withoutSymbol($bpwiseAmount).'</td>';
          }
        }

        $grTotal += $feeTxV + $res->concession_amount + $res->adjustment_amount;
        $gColletedTotal += $feeTxV + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
        $TotalConcession += $res->concession_amount;
        $TotalAdjustment += $res->adjustment_amount;
        $TotalFine += $res->fine_amount;
        $TotalDiscount += $res->discount_amount;
        $TotalLoanCharge += $res->loan_provider_charges;
        $template .='<th>'.numberToCurrency_withoutSymbol($feeTxV + $res->concession_amount + $res->adjustment_amount).'</th>';
        $template .='<th> ( '.numberToCurrency_withoutSymbol($res->concession_amount).' )</th>';
        if ($fee_adjustment_amount) {
          $template .='<th>( '.numberToCurrency_withoutSymbol($res->adjustment_amount).' )</th>';
        }
        if ($fee_fine_amount) {
          $template .='<th>'.numberToCurrency_withoutSymbol($res->fine_amount).'</th>';
        }
        $template .='<th>'.numberToCurrency_withoutSymbol($res->discount_amount).'</th>';
        if ($loan_column) {
          $template .='<th>'.numberToCurrency_withoutSymbol($res->loan_provider_charges).'</th>';
        }
        $template .='<th>'.numberToCurrency_withoutSymbol($feeTxV + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount).'</th>';
        $template .='<td>'.$this->_get_PaymentTypeValue($res->payment_type, $res->reconciliation_status).'</td>';
        $template .='<td>'.$res->bank_name.'</td>';
        if ($res->payment_type == 1 || $res->payment_type == 4 || $res->payment_type == 8) {
          $template .='<td>'.$res->cheque_or_dd_date.'</td>';
        }else{
          $template .='<td></td>';
        }
        $template .='<td>'.$res->cheque_dd_nb_cc_dd_number.'</td>';
        $template .='<td>'.$res->remarks.'</td>';
        $template .='</tr>';
      }
      $template .="</tbody>";
      $template .="<tfoot>";
      $template .="<tr>";
      $colspan = '6';
      if ($is_semester_scheme == 1) {
        $colspan ='7';
      }
      $template .="<th class='text-right' colspan='".$colspan."'></th>";
      foreach ($headers as $value) {
        foreach ($value as $key => $val) {
          if (!empty($val->bpCompId)) {
            if (array_key_exists($val->bpCompId, $feeTx)) {
              $totalSumTx12 = $feeSumTx[$val->bpId];
              if ($components_exclude) {
                $template .='<th>'.numberToCurrency_withoutSymbol($feeTx[$val->bpCompId]).'</th>';
              }
            }else{
              if ($components_exclude) {
                $template .='<th> - </th>';
              }
            }
          }
        }
        if ($components_exclude == 0) {
          $template .= '<th>'.numberToCurrency_withoutSymbol($totalSumTx12).'</th>';
        }
      }
      // echo "<pre>"; print_r($headers); die();
      $template .='<th>'.numberToCurrency_withoutSymbol($grTotal).'</th>';
      $template .='<th>'.numberToCurrency_withoutSymbol($TotalConcession).'</th>';
      if ($fee_adjustment_amount) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalAdjustment).'</th>';
      }
      if ($fee_fine_amount) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalFine).'</th>';
      }
      $template .='<th>'.numberToCurrency_withoutSymbol($TotalDiscount).'</th>';
      if ($loan_column) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalLoanCharge).'</th>';
      }
      $template .='<th>'.numberToCurrency_withoutSymbol($gColletedTotal).'</th>';
      $template .="</tr>";
      $template .="</tfoot>";
      $template .="</table>";

      $template_summary1 ="<table class='table table-bordered'>";
      $template_summary1 .="<h3>Summary</h3>";
      $template_summary1 .="<thead>";
      $template_summary1 .='<tr>';
      foreach ($headers as $bpName => $value) {
        $template_summary1 .='<th>'.$bpName.'</th>';
      }
      $template_summary1 .='</tr>';
      $template_summary1 .="<thead>";
      $template_summary1 .="<tbody>";
      $template_summary1 .="<tr>";
      foreach ($headers as $value) {
        foreach ($value as $key => $val) {
          if (!empty($val->bpId)) {
            if (array_key_exists($val->bpId, $feeSumTx)) {
              $totalSumTx = $feeSumTx[$val->bpId];
            }
          }
        }
        $template_summary1 .= '<td>'.numberToCurrency_withoutSymbol($totalSumTx).'</td>';
      }
      $template_summary1 .="</tr>";
      $template_summary1 .="</tbody>";
      $template_summary1 .="</table>";

      $template_summary ='';
      if ($components) {
        $template_summary .="<table class='table table-bordered'>";
        // $template_summary .="<h3>Components Summary</h3>";

        foreach ($headers as $bpName => $value) {
          $template_summary .='<tr>';
          $template_summary .='<td style="border:none; font-size:16px; color:#ff9800" colspan='.count($value).' ><b style="border-bottom:2px solid #dad8d8">'.$bpName.'</b></td>';
          $template_summary .='</tr>';
          $template_summary .='<tr>';
          foreach ($value as $key => $val) {
            $template_summary .='<th>'.$val->name.' ('.$componentsCount[$val->bpCompId]. ')</th>';
          }
          $template_summary .='</tr>';

          $template_summary .='<tr>';
          foreach ($value as $key => $val) {
            if (!empty($val->bpCompId)) {
              if (array_key_exists($val->bpCompId, $feeTx)) {
                $template_summary .= '<th>'.numberToCurrency_withoutSymbol($feeTx[$val->bpCompId]).'</th>';
              }
            }
          }
          $template_summary .='</tr>';
        }
        $template_summary .="</table>";
      }


      $template .="</div>";

      $dailyTemplate = $template_summary1. $template_summary .$template;
    }
    print($dailyTemplate);
  }

  public function generate_report_for_day_book_summary(){

    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $components = $this->input->post('components');
    $components_v_h = $this->input->post('components_v_h');
    $components_exclude = $this->input->post('components_exclude');
    $paymentModes = $this->input->post('paymentModes');
    $include_delete = $this->input->post('include_delete');
    $recon = $this->input->post('recon');
    $admission_type = $this->input->post('admission_type');
    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $fee_adjustment_amount = $this->settings->getSetting('fee_adjustment_amount');
    $headers = $this->reports_model->get_day_books_details_headersv1($fee_type);
    // $dailyCollection = $this->reports_model->get_day_books_details_fees_txv1($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete);
    $dailyCollection = $this->reports_model->get_day_books_details_fees_summary($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type, $recon);
    $salesCollection = [];
    $sales = $this->authorization->isModuleEnabled('SALES');
    $admission = $this->authorization->isModuleEnabled('ADMISSION');
    if ($sales) {
      if (in_array('sales',$fee_type) || in_array('All',$fee_type)) {
        $salesCollection = $this->reports_model->get_day_books_details_sale_tx($from_date,$to_date, $paymentModes, $include_delete);
      }
    }
    $appCollections = [];
    if ($admission) {
      $appCollectiondata  = [];
      if (in_array('application',$fee_type) || in_array('All',$fee_type)) {
        $appCollectiondata = $this->reports_model->get_day_books_details_appliation_tx($from_date,$to_date, $paymentModes);
      }
      $appCollections = $appCollectiondata;
    }

    $additionalAmount = $this->reports_model->get_payment_options_additional_amount();
    $excess_collections = [];
    if ($additionalAmount) {
      $excessCollectiondata  = [];
      if (in_array('excess_amount',$fee_type) || in_array('All',$fee_type)) {
        $excessCollectiondata = $this->reports_model->get_day_books_details_excess_amount_tx($from_date,$to_date,$classId, $paymentModes, $admission_type);
      }
      $excess_collections = $excessCollectiondata;
    }

    $mergeAll = array_merge($dailyCollection, $salesCollection, $appCollections, $excess_collections);

    foreach ($mergeAll as $key => $val) {
      $val->strtootime = strtotime($val->receipt_date);
    }
    array_multisort(array_column($mergeAll,'strtootime'), SORT_DESC, array_column($mergeAll, 'receipt_number'),SORT_DESC, $mergeAll);
    echo json_encode($mergeAll);
  }

  public function generate_report_for_day_book_summary1(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $components = $this->input->post('components');
    $components_v_h = $this->input->post('components_v_h');
    $components_exclude = $this->input->post('components_exclude');
    $paymentModes = $this->input->post('paymentModes');
    $include_delete = $this->input->post('include_delete');
    $admission_type = $this->input->post('admission_type');
    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $fee_adjustment_amount = $this->settings->getSetting('fee_adjustment_amount');
    $headers = $this->reports_model->get_day_books_details_headersv1($fee_type);
    // $dailyCollection = $this->reports_model->get_day_books_details_fees_txv1($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete);
    $dailyCollection = $this->reports_model->get_day_books_details_fees_summary1($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type);
    $salesCollection = [];
    $sales = $this->authorization->isModuleEnabled('SALES');
    $admission = $this->authorization->isModuleEnabled('ADMISSION');
    if ($sales) {
      if (in_array('sales',$fee_type) || in_array('All',$fee_type)) {
        $salesCollection = $this->reports_model->get_day_books_details_sale_tx1($from_date,$to_date, $paymentModes, $include_delete);
      }
    }
    $appCollections = [];
    if ($admission) {
      $appCollectiondata  = [];
      if (in_array('application',$fee_type) || in_array('All',$fee_type)) {
        $appCollectiondata = $this->reports_model->get_day_books_details_appliation_tx1($from_date,$to_date, $paymentModes);
      }
      if ($paymentModes) {
        foreach ($appCollectiondata as $key => $val) {
          if ($val->payment_type == $paymentModes) {
            $appCollections[] = $val;
          }
        }
      }else{
        $appCollections = $appCollectiondata;
      }

    }

    $additionalAmount = $this->reports_model->get_payment_options_additional_amount();
    $excess_collections = [];
    if ($additionalAmount) {
      $excessCollectiondata  = [];
      if (in_array('excess_amount',$fee_type) || in_array('All',$fee_type)) {
        $excessCollectiondata = $this->reports_model->get_day_books_details_excess_amount_tx($from_date,$to_date,$classId, $paymentModes, $admission_type);
      }
      $excess_collections = $excessCollectiondata;
    }

    $mergeAll = array_merge($dailyCollection, $salesCollection, $appCollections, $excess_collections);

    foreach ($mergeAll as $key => $val) {
      $val->strtootime = strtotime($val->receipt_date);
    }
    array_multisort(array_column($mergeAll,'strtootime'), SORT_DESC, array_column($mergeAll, 'receipt_number'),SORT_DESC, $mergeAll);
    echo json_encode($mergeAll);
  }

  public function generate_report_for_installment_wise_day_bookV1(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $components = $this->input->post('components');
    $components_v_h = $this->input->post('components_v_h');
    $components_exclude = $this->input->post('components_exclude');
    $paymentModes = $this->input->post('paymentModes');
    $include_delete = $this->input->post('include_delete');
    $admission_type = $this->input->post('admission_type');
    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $fee_adjustment_amount = $this->settings->getSetting('fee_adjustment_amount');
    $fee_fine_amount = $this->settings->getSetting('fee_fine_amount');
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');

    // Get installment-wise headers instead of component-wise headers
    $headers = $this->reports_model->get_day_books_installment_headers($fee_type);
  
    $dailyCollection = $this->reports_model->get_day_books_installment_wise_fees_tx($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type);
    $salesCollection = [];
    $sales = $this->authorization->isModuleEnabled('SALES');
    $admission = $this->authorization->isModuleEnabled('ADMISSION');
    if ($sales) {
      if (in_array('sales',$fee_type) || in_array('All',$fee_type)) {
        $salesCollection = $this->reports_model->get_day_books_details_sale_tx($from_date,$to_date, $paymentModes, $include_delete);
      }
    }

    $appCollections = [];
    if ($admission) {
      $appCollectiondata  = [];
      if (in_array('application',$fee_type) || in_array('All',$fee_type)) {
        $appCollectiondata = $this->reports_model->get_day_books_details_appliation_tx($from_date,$to_date, $paymentModes);
      }
      if ($paymentModes) {
        foreach ($appCollectiondata as $key => $val) {
          if ($val->payment_type == $paymentModes) {
            $appCollections[] = $val;
          }
        }
      }else{
        $appCollections = $appCollectiondata;
      }
    }

    $mergeAll = array_merge($dailyCollection, $salesCollection, $appCollections);

    foreach ($mergeAll as $key => $val) {
      $val->strtootime = strtotime($val->receipt_date);
    }
    array_multisort(array_column($mergeAll,'strtootime'), SORT_DESC, array_column($mergeAll, 'receipt_number'),SORT_DESC, $mergeAll);

      echo "<pre>"; print_r($headers); die();

    $dailyTemplate = '';
    $template = '';
    if (empty($mergeAll)) {
      $dailyTemplate .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .="<div class='fee_export_excel table-responsive'>";
      $template .="<table class='table table-bordered' id='details_summary_table' >";
      $template .="<thead>";

      $template .="<tr>";
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Sl #</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Receipt Date</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Receipt Number</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Student Name</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Class</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Section</p></th>';
      if ($is_semester_scheme == 1) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Semester</p></th>';
      }
   
      // Display installment headers
      foreach ($headers as $bpName => $value) {
        $bp_name = '<p>'.$bpName.'</p>';
        if ($components_exclude) {
          $count = count($value);
          $bp_name = $bpName;
        }else{
          $count = '';
        }
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" style="text-align:center" colspan="'.$count.'" >'.$bp_name.'</th>';
      }

      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Total</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Concession</p></th>';
      if ($fee_adjustment_amount) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Adjustment</p></th>';
      }
      if ($fee_fine_amount) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Fine</p></th>';
      }
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Discount</p></th>';
      if ($loan_column) {
        $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Loan Provider Charges</p></th>';
      }
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Collected Amount</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Payment Type</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Bank Name</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>DD/Cheque Date</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Reference No.</p></th>';
      $template .='<th class="verticalTableHeader_'.$components_v_h.'" rowspan="2"><p>Remarks</p></th>';

      $template .="</tr>";

      // Second header row for installment names
      if ($components_exclude) {
        $template .="<tr>";
        foreach ($headers as $bpName => $value) {
          foreach ($value as $key => $val) {
            $template .='<th class="headerBottom verticalTableHeader_'.$components_v_h.'"><p>'.$val->name.'</p></th>';
          }
        }
        $template .="</tr>";
      }

      $template .="</thead>";
      $template .="<tbody>";

      $feeTx = array();
      $feeSumTx = array();
      $grTotal = 0;
      $gColletedTotal = 0;
      $installmentsCount = array();
      $TotalConcession = 0;
      $TotalAdjustment = 0;
      $TotalFine = 0;
      $TotalDiscount = 0;
      $TotalLoanCharge = 0;
      $m=1;

      // Process each transaction for installment-wise display
      foreach ($mergeAll as $key => $res) {
        $color = '';
        $recNumber = $res->receipt_number;
        if ($res->soft_delete == 1) {
          $color ='#f71909';
          $recNumber = $res->receipt_number.'<br>(Cancelled)';
        }
        $template .='<tr style="color:'.$color.' ;">';
        $template .='<td>'.($m++).'</td>';
        $template .='<td>'.$res->receipt_date.'</td>';
        $template .='<td>'.$recNumber.'</td>';
        $template .='<td>'.$res->student_name.'</td>';
        $template .='<td>'.$res->class_name.'</td>';
        $template .='<td>'.$res->sectionName.'</td>';
        if ($is_semester_scheme == 1) {
          $template .='<td>'.$res->semester.'</td>';
        }
        $feeTxV = 0;

        // Process installment-wise amounts
        foreach ($headers as $value) {
          $bpwiseAmount = 0;
          foreach ($value as $key => $val) {
            if (!empty($val->bpId)) {
              if (!array_key_exists($val->bpId, $feeSumTx)) {
                $feeSumTx[$val->bpId] = 0;
              }
            }
            if (!empty($val->installmentId)) {
              if (!array_key_exists($val->installmentId, $feeTx)) {
                $feeTx[$val->installmentId] = 0;
              }
              if (!array_key_exists($val->installmentId, $installmentsCount)) {
                $installmentsCount[$val->installmentId] = 0;
              }
              if (array_key_exists($val->installmentId, $res->Installments)) {
                if ($res->Installments[$val->installmentId] != 0) {
                  $installmentsCount[$val->installmentId]++;
                }
                $feeTx[$val->installmentId] += $res->Installments[$val->installmentId] + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
                $feeSumTx[$val->bpId] += $res->Installments[$val->installmentId] + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
                $bpwiseAmount += $res->Installments[$val->installmentId];
                $feeTxV += $res->Installments[$val->installmentId];
                if ($components_exclude) {
                  $template .='<td>'.numberToCurrency_withoutSymbol($res->Installments[$val->installmentId]).'</td>';
                }
              }else{
                if ($components_exclude) {
                  $template .='<td> - </td>';
                }
              }
            }else{
              if ($components_exclude) {
                $template .='<td> - </td>';
              }
            }
          }
          if ($components_exclude == 0) {
            $template .='<td>'.numberToCurrency_withoutSymbol($bpwiseAmount).'</td>';
          }
        }

        $grTotal += $feeTxV + $res->concession_amount + $res->adjustment_amount;
        $gColletedTotal += $feeTxV + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount;
        $TotalConcession += $res->concession_amount;
        $TotalAdjustment += $res->adjustment_amount;
        $TotalFine += $res->fine_amount;
        $TotalDiscount += $res->discount_amount;
        $TotalLoanCharge += $res->loan_provider_charges;
        $template .='<th>'.numberToCurrency_withoutSymbol($feeTxV + $res->concession_amount + $res->adjustment_amount).'</th>';
        $template .='<th> ( '.numberToCurrency_withoutSymbol($res->concession_amount).' )</th>';
        if ($fee_adjustment_amount) {
          $template .='<th>( '.numberToCurrency_withoutSymbol($res->adjustment_amount).' )</th>';
        }
        if ($fee_fine_amount) {
          $template .='<th>'.numberToCurrency_withoutSymbol($res->fine_amount).'</th>';
        }
        $template .='<th>'.numberToCurrency_withoutSymbol($res->discount_amount).'</th>';
        if ($loan_column) {
          $template .='<th>'.numberToCurrency_withoutSymbol($res->loan_provider_charges).'</th>';
        }
        $template .='<th>'.numberToCurrency_withoutSymbol($feeTxV + $res->fine_amount - $res->loan_provider_charges - $res->discount_amount).'</th>';
        $template .='<td>'.$this->_get_PaymentTypeValue($res->payment_type, $res->reconciliation_status).'</td>';
        $template .='<td>'.$res->bank_name.'</td>';
        if ($res->payment_type == 1 || $res->payment_type == 4 || $res->payment_type == 8) {
          $template .='<td>'.$res->cheque_or_dd_date.'</td>';
        }else{
          $template .='<td></td>';
        }
        $template .='<td>'.$res->cheque_dd_nb_cc_dd_number.'</td>';
        $template .='<td>'.$res->remarks.'</td>';
        $template .='</tr>';
      }

      $template .="</tbody>";
      $template .="<tfoot>";
      $template .="<tr>";
      $colspan = '6';
      if ($is_semester_scheme == 1) {
        $colspan ='7';
      }
      $template .="<th class='text-right' colspan='".$colspan."'></th>";

      // Footer totals for installments
      foreach ($headers as $value) {
        foreach ($value as $key => $val) {
          if (!empty($val->installmentId)) {
            if (array_key_exists($val->installmentId, $feeTx)) {
              $totalSumTx12 = $feeSumTx[$val->bpId];
              if ($components_exclude) {
                $template .='<th>'.numberToCurrency_withoutSymbol($feeTx[$val->installmentId]).'</th>';
              }
            }else{
              if ($components_exclude) {
                $template .='<th> - </th>';
              }
            }
          }
        }
        if ($components_exclude == 0) {
          $template .= '<th>'.numberToCurrency_withoutSymbol($totalSumTx12).'</th>';
        }
      }

      $template .='<th>'.numberToCurrency_withoutSymbol($grTotal).'</th>';
      $template .='<th>'.numberToCurrency_withoutSymbol($TotalConcession).'</th>';
      if ($fee_adjustment_amount) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalAdjustment).'</th>';
      }
      if ($fee_fine_amount) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalFine).'</th>';
      }
      $template .='<th>'.numberToCurrency_withoutSymbol($TotalDiscount).'</th>';
      if ($loan_column) {
        $template .='<th>'.numberToCurrency_withoutSymbol($TotalLoanCharge).'</th>';
      }
      $template .='<th>'.numberToCurrency_withoutSymbol($gColletedTotal).'</th>';
      $template .="</tr>";
      $template .="</tfoot>";
      $template .="</table>";

      // Summary section for installments
      $template_summary1 ="<table class='table table-bordered'>";
      $template_summary1 .="<h3>Installment Summary</h3>";
      $template_summary1 .="<thead>";
      $template_summary1 .='<tr>';
      foreach ($headers as $bpName => $value) {
        $template_summary1 .='<th>'.$bpName.'</th>';
      }
      $template_summary1 .='</tr>';
      $template_summary1 .="<thead>";
      $template_summary1 .="<tbody>";
      $template_summary1 .="<tr>";
      foreach ($headers as $value) {
        foreach ($value as $key => $val) {
          if (!empty($val->bpId)) {
            if (array_key_exists($val->bpId, $feeSumTx)) {
              $totalSumTx = $feeSumTx[$val->bpId];
            }
          }
        }
        $template_summary1 .= '<td>'.numberToCurrency_withoutSymbol($totalSumTx).'</td>';
      }
      $template_summary1 .="</tr>";
      $template_summary1 .="</tbody>";
      $template_summary1 .="</table>";

      $template_summary ='';
      if ($components) {
        $template_summary .="<table class='table table-bordered'>";

        foreach ($headers as $bpName => $value) {
          $template_summary .='<tr>';
          $template_summary .='<td style="border:none; font-size:16px; color:#ff9800" colspan='.count($value).' ><b style="border-bottom:2px solid #dad8d8">'.$bpName.'</b></td>';
          $template_summary .='</tr>';
          $template_summary .='<tr>';
          foreach ($value as $key => $val) {
            $template_summary .='<th>'.$val->name.' ('.$installmentsCount[$val->installmentId]. ')</th>';
          }
          $template_summary .='</tr>';

          $template_summary .='<tr>';
          foreach ($value as $key => $val) {
            if (!empty($val->installmentId)) {
              if (array_key_exists($val->installmentId, $feeTx)) {
                $template_summary .= '<th>'.numberToCurrency_withoutSymbol($feeTx[$val->installmentId]).'</th>';
              }
            }
          }
          $template_summary .='</tr>';
        }
        $template_summary .="</table>";
      }

      $template .="</div>";
      $dailyTemplate = $template_summary1. $template_summary .$template;
    }
    print($dailyTemplate);
  }

  public function _get_PaymentTypeValue($paymentType, $reconStatus){
    $NC = '';
    if ($reconStatus == 1) {
      $NC = '<span style="color:red;"> <b> (N/C) </b><span>';
    }else if($reconStatus == 2){
      $NC = '<span> <b> (C) </b><span>';
    }
    switch ($paymentType) {
      case '1':
        $pValue = 'DD '.$NC;
        break;
      case '2':
        $pValue = 'Credit Card '.$NC;
        break;
      case '3':
        $pValue = 'Debit Card'.$NC;
        break;
      case '4':
        $pValue = 'Cheque '.$NC;
        break;
      case '5':
        $pValue = 'Wallet Payment '.$NC;
        break;
      case '6':
        $pValue = 'Challan '.$NC;
        break;
      case '7':
        $pValue = 'Card (POS) '.$NC;
        break;
      case '8':
        $pValue = 'Net Banking '.$NC;
        break;
      case '9':
        $pValue = 'Cash';
        break;
      case '10':
        $pValue = 'Online Payment';
        break;
     case '11':
        $pValue = 'UPI';
        break;
      case '12':
        $pValue = 'Loan';
        break;
      case '13':
        $pValue = 'Loan';
        break;
      case '30':
        $pValue = 'Transfer from Indus';
        break;
      case '31':
        $pValue = 'Bank Deposit';
        break;
      case '999':
        $pValue = 'Excess Amount';
        break;
      case '777':
        $pValue = 'Online Challan Payment';
        break;
      case '55':
        $pValue = 'Debit card Rupay';
        break;
      case '66':
        $pValue = 'Grey quest';
        break;
      case '67':
        $pValue = 'DRCC';
        break;
      case '68':
        $pValue = 'CS';
        break;
      default:
        $pValue = '';
        break;
    }
    return $pValue;
  }
  public function student_wise_fees_details(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/reports/student_fees_summary';
    $this->load->view('inc/template', $data);
  }

  public function daily_transaction(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/daily_report';
    $this->load->view('inc/template', $data);
  }

  public function transportation_report(){
    $data['classes'] = $this->Student_Model->getClassNames();

    $data['km_details'] = $this->fees_student_model->get_km_details();

    $data['routes'] = $this->fees_student_model->get_route_details();

    $blueprint = $this->fees_student_model->get_transport_blueprint();
    $bpFilter = [];
    if(!empty($blueprint)){
      foreach ($blueprint as $key => $bp) {
        $bpFilter = json_decode($bp->filter_blueprint);
      }
    }
    $data['fitlers'] = $bpFilter;
    $data['main_content'] = 'feesv2/reports/transportation_report';
    $this->load->view('inc/template', $data);
  }

  public function fees_edit_history_report(){
    $data['main_content'] = 'feesv2/reports/fees_edit_history_report';
    $this->load->view('inc/template', $data);
  }

  public function get_transport_reports_details(){
    $clsId = $_POST['clsId'];
    $has_transport_km = $_POST['has_transport_km'];
    $pickup_mode = $_POST['pickup_mode'];
    $result = $this->fees_student_model->get_transaction_detail($clsId, $has_transport_km, $pickup_mode);
    echo json_encode($result);
  }

  public function get_online_settlemet_data(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    // $from_date = '2023-06-07';
    // $to_date = '2023-06-07';
    $fee_type = $_POST['fee_type'];
    $paymentModes = $_POST['paymentModes'];
    $result = $this->fees_student_model->get_online_settlemet_details($from_date, $to_date, $fee_type, $paymentModes);
    echo json_encode($result);
  }

  public function save_filters(){
    echo $this->fees_student_model->save_fliter_names($this->input->post());
  }

  public function update_filters(){
    echo $this->fees_student_model->update_fliter_names($this->input->post());
  }

  public function save_filters1(){
    echo $this->fees_student_model->save_fliter_names1($this->input->post());//fee_detail_report
  }

  public function update_filters1(){
    echo $this->fees_student_model->update_fliter_names1($this->input->post());//fee_detail_report
  }

  public function save_filters2(){
    echo $this->fees_student_model->save_fliter_names2($this->input->post());//balance_sms
  }

  public function update_filters2(){
    echo $this->fees_student_model->update_fliter_names2($this->input->post());//balance_sms
  }

  public function save_filters3(){
    echo $this->fees_student_model->save_fliter_names3($this->input->post());//management_summary
  }

  public function update_filters3(){
    echo $this->fees_student_model->update_fliter_names3($this->input->post());//management_summary
  }

  public function save_filters4(){
    echo $this->fees_student_model->save_fliter_names4($this->input->post());//fee summary detail report
  }

  public function update_filters4(){
    echo $this->fees_student_model->update_fliter_names4($this->input->post());//fee summary detail report
  }

  public function save_filters5(){
    echo $this->fees_student_model->save_fliter_names5($this->input->post());//management_day_wise_summary
  }

  public function update_filters5(){
    echo $this->fees_student_model->update_fliter_names5($this->input->post());//management_day_wise_summary
  }

  public function tally_report(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['payment_mode'] = [];
    if(!empty($data['fee_blueprints'])){
      $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    }
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/tailly_report_view';
    $this->load->view('inc/template_fee', $data);
  }

  public function generate_report_for_day_bookV1_for_tally(){
    $fee_type = $this->input->post('fee_type');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $classId = $this->input->post('classId');
    $components = $this->input->post('components');
    $components_v_h = $this->input->post('components_v_h');
    $components_exclude = $this->input->post('components_exclude');
    $components_exclude= 1;
    $paymentModes = $this->input->post('paymentModes');
    $include_delete = $this->input->post('include_delete');
    $admission_type = $this->input->post('admission_type');
    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $fee_adjustment_amount = $this->settings->getSetting('fee_adjustment_amount');
    $fee_fine_amount = $this->settings->getSetting('fee_fine_amount');
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme_tally_report');

    $headers = $this->reports_model->get_day_books_details_headersv1($fee_type);
    $dailyCollection = $this->reports_model->get_day_books_details_fees_txv1($fee_type,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type);
    $salesCollection = [];
    $sales = $this->authorization->isModuleEnabled('SALES');
    $admission = $this->authorization->isModuleEnabled('ADMISSION');
    if ($sales) {
      if (in_array('sales',$fee_type) || in_array('All',$fee_type)) {
        $salesCollection = $this->reports_model->get_day_books_details_sale_tx($from_date,$to_date, $paymentModes, $include_delete);
      }
    }
    $appCollections = [];
    if ($admission) {
      $appCollectiondata  = [];
      if (in_array('application',$fee_type) || in_array('All',$fee_type)) {
        $appCollectiondata = $this->reports_model->get_day_books_details_appliation_tx($from_date,$to_date, $paymentModes);
      }
      if ($paymentModes) {
        foreach ($appCollectiondata as $key => $val) {
          if ($val->payment_type == $paymentModes) {
            $appCollections[] = $val;
          }
        }
      }else{
        $appCollections = $appCollectiondata;
      }

    }

    $mergeAll = array_merge($dailyCollection, $salesCollection, $appCollections);
    foreach ($mergeAll as $key => $val) {
      $val->strtootime = strtotime($val->receipt_date);
    }

    array_multisort(array_column($mergeAll,'strtootime'), SORT_DESC, array_column($mergeAll, 'receipt_number'),SORT_DESC, $mergeAll);
    $dailyTemplate = '';
    $template = '';
    if (empty($mergeAll)) {
      $dailyTemplate .= '<div>
        <h3>No transaction for today</h3><br>
      </div>';
    }else{
      $template .="<div class='fee_export_excel' id='slider_table_id' style='overflow: auto; height: 300px;'>";
      $template .="<table class='table table-bordered' id='details_summary_table' >";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th>#</th>';
      $template .='<th>Booking Date</th>';
      $template .='<th>Booking Number</th>';
      $template .='<th>Student Name</th>';
      $template .='<th>Admission Number</th>';
      $template .='<th>Class</th>';
      $template .='<th>Section</th>';
      if ($is_semester_scheme == 1) {
        $template .='<th>Semester</th>';
      }
      $template .='<th>Remarks</th>';
      $template .='<th>Payment Type</th>';
      // $template .='<th>Bank Name</th>';
      // $template .='<th>DD/Cheque Date</th>';
      // $template .='<th>Reference No.</th>';

      $template .='<th>Total</th>';
      $template .='<th>Concession</th>';
      $template .='<th>Discount</th>';
      $template .='<th>Collected Amount</th>';
      foreach ($headers as $bpName => $value) {
        foreach ($value as $key => $val) {
          $template .='<th>'.$val->name.'</th>';
        }
      }
      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $m=1;
      // echo "<pre>"; print_r($mergeAll); die();
      $totalFees = 0;
      foreach ($mergeAll as $key => $res) {
        $color = '';
        $recNumber = $res->receipt_number;
        if ($res->soft_delete == 1) {
          $color ='#f71909';
          $recNumber = $res->receipt_number.'<br>(Cancelled)';
        }
        $template .='<tr style="color:'.$color.' ;">';
        $template .='<td>'.($m++).'</td>';
        $template .='<td>'.$res->receipt_date.'</td>';
        $template .='<td>'.$recNumber.'</td>';
        $template .='<td>'.$res->student_name.'</td>';
        $template .='<td>'.$res->admission_no.'</td>';
        $template .='<td>'.$res->class_name.'</td>';
        $template .='<td>'.$res->sectionName.'</td>';
        if ($is_semester_scheme == 1) {
          $template .='<td>'.$res->semester.'</td>';
        }
        $template .='<td>'.$res->remarks.'</td>';
        $template .='<td>'.$this->_get_PaymentTypeValue($res->payment_type, $res->reconciliation_status).'</td>';
        // $template .='<td>'.$res->bank_name.'</td>';
        // if ($res->payment_type == 1 || $res->payment_type == 4 || $res->payment_type == 8) {
        //   $template .='<td>'.$res->cheque_or_dd_date.'</td>';
        // }else{
        //   $template .='<td></td>';
        // }
        // $template .='<td>'.$res->cheque_dd_nb_cc_dd_number.'</td>';
        $totalFees = $res->amount_paid + $res->concession_amount + $res->discount_amount;
        $template .='<td>'.numberToCurrency_withoutSymbol($totalFees).'</td>';
        $template .='<th>'.numberToCurrency_withoutSymbol($res->concession_amount).'</th>';
        $template .='<th>'.numberToCurrency_withoutSymbol($res->discount_amount).'</th>';
        $template .='<th>'.numberToCurrency_withoutSymbol($res->amount_paid).'</th>';
        foreach ($headers as $value) {
          foreach ($value as $key => $val) {
            if(array_key_exists($val->bpCompId, $res->Components)){
              $template .='<td>'.numberToCurrency_withoutSymbol($res->Components[$val->bpCompId]).'</td>';
            }else{
              $template .='<td></td>';
            }
          }
        }
      $template .='</tr>';
      }
      $template .="</tbody>";
      $template .="</table>";
      $template .="</div>";
    }
    print($template);
  }

  public function tally_report_invoice(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['main_content'] = 'feesv2/reports/tailly_report_invoice';
    $this->load->view('inc/template_fee', $data);
  }


  public function generate_report_for_invoice_for_tally(){
    $fee_type = $this->input->post('fee_type');
    $classId = $this->input->post('invoice_classId');
    $admission_type = $this->input->post('admission_type');
    $headers = $this->reports_model->get_fee_invoice_details_headers($fee_type);
    $schoolName = $this->settings->getSetting('school_short_name');
    $fees_invoice = $this->reports_model->get_fees_invoice_details_fees($fee_type, $classId, $admission_type);
    // echo "<pre>"; print_r($fees_invoice); die();
    $template = '';
    if (empty($fees_invoice)) {
      $template .= '<div>
        <h3>No data found</h3><br>
      </div>';
    }else{
      $template .="<table class='table table-bordered' id='fees_invoice_table' >";
      $template .="<thead>";
      $template .="<tr>";
      $template .='<th>#</th>';
      $template .='<th>Booking Date</th>';
      $template .='<th>Booking  Number</th>';
      $template .='<th>Student Name</th>';
      $template .='<th>Admission Number</th>';
      $template .='<th>Class</th>';
      $template .='<th>Section</th>';
      $template .='<th>Semester</th>';
      $template .='<th>Total</th>';
      foreach ($headers as $bpName => $value) {
        foreach ($value as $key => $val) {
          $template .='<th>'.$val->name.'</th>';
        }
      }
      $template .="</tr>";
      $template .="</thead>";
      $template .="<tbody>";
      $m=1;
      foreach ($fees_invoice as $key => $res) {
        $template .='<tr>';
        $template .='<td>'.($m++).'</td>';
        $template .='<td>'.$res->booking_date.'</td>';
        $template .='<td>'.$schoolName.'-'.$res->student_schedule_id.'</td>';
        $template .='<td>'.$res->std_name.'</td>';
        $template .='<td>'.$res->admission_no.'</td>';
        $template .='<td>'.$res->className.'</td>';
        $template .='<td>'.$res->sectionName.'</td>';
        $template .='<td>NA</td>';
        if(!empty($res->components)){
          $template .='<td>'.array_sum($res->components).'</td>';
          foreach ($headers as $value) {
            foreach ($value as $key => $val) {
              if(array_key_exists($val->bpCompId, $res->components)){
                $template .='<td>'.numberToCurrency_withoutSymbol($res->components[$val->bpCompId]).'</td>';
              }else{
                $template .='<td></td>';
              }
            }
          }
        }else{
          $template .='<td></td>';
          foreach ($headers as $value) {
            foreach ($value as $key => $val) {
              $template .='<td></td>';
            }
          }
        }

      $template .='</tr>';
      }
      $template .="</tbody>";
      $template .="</table>";
      $template .="</div>";
    }
    print($template);
  }

}