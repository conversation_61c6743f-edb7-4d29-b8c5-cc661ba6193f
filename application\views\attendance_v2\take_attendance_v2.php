<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_v2/menu'); ?>">Student Attendance</a></li>
  <li>Take Attendance</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff" id="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_v2/menu'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Take Attendance
          </h3>
          <div>
            <?php if ($import_enabled) { ?>
              <button class="btn btn-secondary" onclick="sampleExcel()">Sample Excel &nbsp;&nbsp;<i
                  class="fa fa-file"></i></button>
            <?php } ?>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="row">
        <div class="col-md-2 col-12">
          <label class="pl-0" for="attendance">Attendance Date</label>
          <div class="input-group date" id="attendance_date_picker">
            <input autocomplete="off" type="text" value="<?php echo date('d-m-Y'); ?>" class="form-control"
              id="attendance_date" name="attendance_date">
            <span class="input-group-addon">
              <span class="glyphicon glyphicon-calendar"></span>
            </span>
          </div>
        </div>
        <div class="col-md-2 form-group">
          <label class="control-label">Section</label>
          <select class="form-control" id="class_section_id" onchange="<?php if ($is_semester_scheme != 1) {
            echo 'getSubjectOrSessions()';
          } else {
            echo 'getSemesters()';
          } ?>">
            <option value="0">Select section</option>
            <?php
            foreach ($sections as $key => $section) {
              $is_enabled = 'disabled style="color: lightgray"';
              if ($section->is_section_enabled) {
                $is_enabled = '';
              }
              $selected = ($section->id == $selected_section_id) ? 'selected' : '';
              if ($is_semester_scheme == 1) {
                echo '<option ' . $is_enabled . ' value="' . $section->id . '" ' . $selected . '><span>' . $section->class_name . '(' . $section->section_name . ')' . '</span></option>';
              } else {
                echo '<option ' . $is_enabled . ' value="' . $section->id . '"' . $selected . '>' . $section->class_name . '(' . $section->section_name . ')' . '</option>';
              }
            }
            ?>
          </select>
        </div>

        <?php if ($is_semester_scheme == 1) { ?>
          <div class="col-md-2 form-group">
            <label class="control-label">Semester</label>
            <select class="form-control" id="class_semester_id" onchange="getSubjectOrSessions()">
              <option value="0">Select Semester</option>

            </select>
          </div>
        <?php } ?>

      </div>

      <?php if ($isSortingEnabled) { ?>
        <div id="student-filter" style="width: 17rem;display: none;">
          <label for="active-student-filter-type">Choose Student Filter</label>
          <select class="form-control select2" name="" id="active-student-filter-type" onchange="filterStudents()">
            <option selected value="default" class="student-filter-option">
              <?php echo $prefix_student_name_formatted ?>
            </option>
            <option value="first_name" class="student-filter-option">First Name</option>
          </select>
        </div>
      <?php } ?>

      <div class="mt-3" id="info-block">

      </div>

      <div class="mt-3" id="student-list">

      </div>

      <div class="periods-table" id="periods-table-div">
        <div id="period_template">

        </div>
      </div>
      <div id="display_msg">

      </div>
    </div>
    <div class="color_codes" style="display: flex;padding-bottom: 10px;">
      <div style="display: flex;margin-left: 17px;">
        <div style="background:#aba3a3">
          <p style="opacity: 0;">Some Text</p>
        </div>
        <p>Not Taken</p>
      </div>

      <div style="display: flex;margin-left: 17px;">
        <div style="background:#46a946">
          <p style="opacity: 0;">Some Text</p>
        </div>
        <p>Present</p>
      </div>

      <div style="display: flex;margin-left: 17px;">
        <div style="background:#dd6d6d">
          <p style="opacity: 0;">Some Text</p>
        </div>
        <p>Absent</p>
      </div>

      <div style="display: flex;margin-left: 17px;">
        <div style="background:#ddc72b">
          <p style="opacity: 0;">Some Text</p>
        </div>
        <p>Late</p>
      </div>

    </div>
  </div>
  <div class="modal fade" id="students_attendance_names" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document" style="">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="att_modal_name">Students Name</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"
            style="font-size: 26px;color: red;">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="col-12 text-center loading-icon" id="loading_icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
          <div style="height:50vh;overflow: auto;" id="students_names_for_attendance">
          </div>
        </div>
        <div class="modal-footer" style="">
          <button type="button" class="btn btn-secondary btn-lg" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="audit-modal" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 50%;margin: auto;">
      <div class="modal-header">
        <h4 class="modal-title">History of <b id="student-name"></b></h4>
        <button type="button" class="close" data-dismiss="modal" style="font-size: 2rem;"><i
            class="fa fa-times"></i></button>
      </div>
      <div class="modal-body">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>#</th>
              <th>Status</th>
              <th>Action By</th>
              <th>Action On</th>
            </tr>
          </thead>
          <tbody id="audit-data">

          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="import-modal" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 50%;margin: auto;">
      <div class="modal-header">
        <h4 class="modal-title">Import attendance from excel/csv</h4>
        <button type="button" class="close" data-dismiss="modal" style="font-size: 2rem;"><i
            class="fa fa-times"></i></button>
      </div>
      <div class="modal-body">
        <div class="col-md-4 form-group">
          <label class="control-label">Class Duration</label>
          <input type="number" class="form-control" name="class_duration" id="class_duration"
            placeholder="Enter class duration in minutes">
          <span class="help-block">Enter class duration in minutes</span>
        </div>
        <div class="col-md-4 form-group">
          <label class="control-label">Minimum Duration</label>
          <input type="number" class="form-control" name="minimum_duration" id="minimum_duration"
            placeholder="Enter minimum duration to be present in minutes">
          <span class="help-block">Enter minimum duration to be present in minutes</span>
        </div>
        <div class="col-md-4 form-group">
          <label class="control-label">Select File</label>
          <input type="file" class="form-control" name="import_file" id="import_file"
            accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
          <span style="display: none;" class="help-block text-danger" id="file-required">Choose a file</span>
        </div>
      </div>
      <div class="modal-footer">
        <button style="width: 120px;" type="button" class="btn btn-warning my-0" data-dismiss="modal">Cancel</button>
        <button onclick="exportarExcel()" style="width: 120px;" type="button"
          class="btn btn-primary my-0">Import</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="notify-modal" role="dialog">
  <div class="modal-dialog">
    <form id="student-messages-form">
      <div class="modal-content" style="">
        <div class="modal-header">
          <h4 class="modal-title">Notify Absentees & Late-comers by Sms/Notification</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="notify-attendance-master-id" value="0">
          <div class="form-group" style="font-size: 16px;">
            <!-- <label class="control-label mr-3">Mode </label> -->
            <?php if ($notification_mode == 'notif-only' || $notification_mode == 'both') { ?>
              <label class="radio-inline" for="notification">
                <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="notification"
                  value="notification" <?php echo ($notification_mode == 'notif-only' || $notification_mode == 'both') ? 'checked=""' : '' ?>>&nbsp;Notification
              </label>
            <?php } ?>

            <?php if ($notification_mode == 'sms-only' || $notification_mode == 'both') { ?>
              <label class="radio-inline" for="sms">
                <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="sms" value="sms"
                  <?php echo ($notification_mode == 'sms-only') ? 'checked=""' : '' ?>>&nbsp;SMS
              </label>
            <?php } ?>

            <select id="text_send_to" class="form-control" name="text_send_to"
              style="margin: 0 0 0 15px;width: 12%;display:none">
              <option value="Father">Father</option>
              <option value="Mother">Mother</option>
              <option value="Both">Both</option>
              <option value="preferred">Preferred Parent</option>
            </select>
          </div>
          <div id="notify-content" style="overflow-y:auto;max-height:450px;">

          </div>
        </div>
        <div class="modal-footer">
          <button style="width: 120px;" type="button" class="btn btn-warning my-0" data-dismiss="modal">Cancel</button>
          <button id="confirmBtn" onclick="send_messages()" style="width: 120px;" type="button"
            class="btn btn-primary my-0">Confirm</button>
        </div>
      </div>
    </form>
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"
  integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g=="
  crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js"
  integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ=="
  crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
  integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script type="text/javascript">
  let classSectionId
  let isEdit = 0
  const can_edit_all_subjects = "<?php echo $can_edit_all_subjects; ?>";

  const msg = `
  <div style="color:red;text-align:center;
  color: #000;
  border: 2px solid #fffafa;
  text-align: center;
  border-radius: 6px;
  position: relative;
  margin-left: 14px;
  padding: 10px;
  font-size: 14px;
  margin-top: 15px;
  background: #ebf3ff;">
  Choose Section To Proceed
  </div>
  `;

  $("#student-messages-form").click(e => {
    const msgType = e.target;
    if (msgType.type === "radio") {
      if (msgType.id === "sms") {
        $("#text_send_to").css("display", "inline-block");
      } else {
        $("#text_send_to").css("display", "none");
      }
    }
  })

  // gather students data
  let studentsData = [];
  let subjectsData;
  let allStudentsData = {};

  function filterStudents() {
    const students = JSON.parse(JSON.stringify(studentsData));;
    const filterType = $("#active-student-filter-type").val();

    if (filterType !== "default") {
      students.sort((a, b) => a[filterType].localeCompare(b[filterType]));
    }

    destroyStudentList();

    const { type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type } = allStudentsData;

    constructStudentList(type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type, students, subjectsData);
  }

  function destroyStudentList() {
    const stdetLists = document.querySelectorAll(".student-name-list");

    if (stdetLists.length) {
      stdetLists.forEach(s => {
        s.remove();
      })
    }
  }

  function constructStudentList(type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type, students, subject) {
    // $("#active-student-filter-type").show();
    $("#student-filter").show();

    isEdit = is_edit
    period_no = period_no
    $("#periods-tables").hide();
    $("#periods-table-div").hide()
    $(".color_codes").hide()
    $("#info-block").html('');

    var date = $("#attendance_date").val();
    var section_id = $("#class_section_id").val();
    var section_name = $("#class_section_id option:selected").text();
    // var type_id = 0;
    var type_name = '';

    if (!isElective) {
      if (attendance_type == 'subject_wise') {
        // type_id = $("#subject_id_" + period_no).val();
        type_name = $("#subject_id_" + period_no + "_" + type_id + " option:selected").text();
      } else {
        type_id = $("#session_id").val();
        type_name = $("#session_id option:selected").text();
      }
    } else {
      type_id = $("#elective_subjects").val()
      type_name = $("#elective_subjects option:selected").text()
    }

    var parameter_is_elective = (isElective ? 1 : 0);
    var html = `
        <form id="attendance-form">
          <div class="col-md-12 mb-3 mr-0" style="position:relative;">
            <input type="hidden" id="attendance-source" name="source" value="Manual"/>
            <div style="display:flex;align-items: center;justify-content: end;">
              <button class="btn btn-success" id="save-attendance-btn" style="margin: 5px;" onclick="saveAttendance('${parameter_is_elective}','${timetable_id}',${type_id})" type="button">Save</button>
              <button class="btn btn-primary" id="save-attendance-btn" style="margin: 5px;" onclick="hideStudentDataTable()" type="button">Go Back</button>
            </div>
            ${(import_enabled && !is_edit) ? '<button class="btn btn-primary mr-3" style="float:right;" onclick="selectImport()" type="button">Import From Excel</button>' : ''}
          </div>`;
    html += `<div class="col-md-12">
                <input type="hidden" id="period_no" name="period_no" value="${period_no}">
                <input type="hidden" id="timetable_id" name="timetable_id" value="${timetable_id}">
                <input type="hidden" id="isElective" name="isElective" value="${isElective}">
                <input type="hidden" name="is_edit" value="${is_edit}" />
                <input type="hidden" name="attendance_master_id" value="${attendance_master_id}" />
                <input type="hidden" name="attendance_type" value="${attendance_type}" />
                <input type="hidden" name="attendance_type_id" value="${type_id}" />
                <input type="hidden" name="section_id" value="${section_id}" />
                <input type="hidden" name="subject_name" value="${subject}" />
                <input type="hidden" name="date" value="${date}" />
                <input type="hidden" id="max-class-duration" name="class_duration" value="" />`;
    html += `<table class="table table-bordered">
              <thead>
              <tr>
              <th colspan="8" style="font-size: 1.5rem;">${(is_edit) ? 'Edit' : 'Take'} Attendance: ${date} 
              <span>Subject : ${subject} </span> 
              <span>Period No : ${period_no} </span> 
              <span style="float: right;">${section_name}:${type_name}<span>
              </th>
              </tr>
              </thead>
              <thead>
              <tr>
              <th>#</th><th>Student</th>${import_enabled ? `<th>Duration <span id="max-duration">(${is_edit ? +students[0].duration + ' mins' : '-'})</span></th>` : ''}
              <th style="cursor:pointer;" data-toggle="tooltip" data-original-title="Click to make all not-taken" id="not-taken-all">Not Taken (<span id="not-taken-count"></span>) <span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to present" id="present-all">Present (<span id="present-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to absent" id="absent-all">Absent (<span id="absent-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to late" id="late-all">Late (<span id="late-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to absent with permission" id="absent-by-permission-all">Absent By Permission (<span id="absent-by-permission"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>`;
    if (is_edit) {
      html += `<th>History</th>`;
    }
    html += `</tr></thead><tbody id="student-list-body">`;
    for (var i = 0; i < students.length; i++) {
      var student_id = students[i].student_id;
      var status = (is_edit) ? students[i].status : 0;
      html += `<tr class="student-name-list">
                    <td>${i + 1}</td>
                    <td>${students[i].student_name}
                    <input type="hidden" id="old-status-${student_id}" name="old_status[${student_id}]" data-student_id="${student_id}" class="old-status" value="${status}"/>
                    <input type="hidden" id="new-status-${student_id}" name="new_status[${student_id}]" data-student_id="${student_id}" class="new-status" value="${status}"/>
                    </td>`;
      var minimum_duration = '';
      if (import_enabled) {
        var mins = '-';
        if (is_edit) {
          minimum_duration = students[i].attended_duration;
          mins = (minimum_duration == '-') ? '-' : minimum_duration + ' mins';
        }
        html += `<td class="min-duration">${mins}</td>`;
      }
      html += `<input type="hidden" name="minimum_duration[${student_id}]" class="min-duration-input" value="${minimum_duration}"/>`;
      html += `<td data-email="${students[i].email}" data-status="0" data-student_id="${student_id}" class="att-status not-taken ${(status == 0) ? 'active' : ''}">${(status == 0) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="1" data-student_id="${student_id}" class="att-status present ${(status == 1) ? 'active' : ''}">${(status == 1) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="2" data-student_id="${student_id}" class="att-status absent ${(status == 2) ? 'active' : ''}">${(status == 2) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="3" data-student_id="${student_id}" class="att-status late ${(status == 3) ? 'active' : ''}">${(status == 3) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="4" data-student_id="${student_id}" class="att-status absent_by_permission ${(status == 4) ? 'active' : ''}">${(status == 4) ? '<i class="fa fa-check"></i>' : ''}</td>`;
      if (is_edit) {
        html += `<th class="text-center;"><button type="button" id="att-${students[i].student_attendance_id}" data-student_id="${student_id}" data-student_name="${students[i].student_name}" onclick="getAttendanceAudit(${students[i].student_attendance_id})" class="btn btn-primary"><i class="fa fa-eye"></i></button></th>`;
      }
      html += `</tr>`;
    }
    html += `</tbody></div></form>`;
    $("#student-list").html(html);
    updateCounts();

    $(".att-status").click(function () {
      var student_id = $(this).data('student_id');
      var status = $(this).data('status');
      $("#new-status-" + student_id).val(status);
      var tr = $(this).parent();
      tr.children('.att-status').html('');
      tr.children('.att-status').removeClass('active');
      $(this).addClass('active');
      $(this).html('<i class="fa fa-check"></i>');
      updateCounts();
    });

    $("#not-taken-all").click(function () {
      $(".not-taken").click();
    });
    $("#present-all").click(function () {
      $(".not-taken.active").siblings(".present").click();
    });
    $("#absent-all").click(function () {
      $(".not-taken.active").siblings(".absent").click();
    });
    $("#late-all").click(function () {
      $(".not-taken.active").siblings(".late").click();
    });
    $("#absent-by-permission-all").click(function () {
      $(".not-taken.active").siblings(".absent_by_permission").click();
    });
  }


  const noTemplateMsg = `
  <div style="color:red;text-align:center;
  color: #000;
  border: 2px solid #fffafa;
  text-align: center;
  border-radius: 6px;
  position: relative;
  margin-left: 14px;
  padding: 10px;
  font-size: 14px;
  margin-top: 15px;
  background: #ebf3ff;">
  Template Not Available, Please Create The Attendance Template To Proceed
  </div>
  `

  // $("#periods-tables").hide()
  $(".color_codes").hide()
  $("#display_msg").html(msg)

  let period_no = "";
  var attendance_type = 'subject_wise';
  var import_enabled = 0;
  let absent_message = ``;
  let late_message = ``;
  let enable_notification = 0;
  $(document).ready(function () {
    attendance_type = '<?php echo $attendance_type; ?>';
    enable_notification = <?php echo $enable_notification ?>;
    import_enabled = <?php echo $import_enabled; ?>;
    absent_message = '<?php echo $notification_absentee_message; ?>';
    late_message = '<?php echo $notification_late_message; ?>';
  });
  $(function () {
    const can_staff_take_previous_date_attendance = "<?php echo $can_staff_take_previous_date_attendance; ?>";
    $('#attendance_date, #attendance_date_picker').datepicker({
      format: 'dd-mm-yyyy',
      autoclose: true,
      endDate: new Date(),
      startDate: `${can_staff_take_previous_date_attendance == 0 ? "-0m" : ""}`
    }).on('changeDate', function (ev) {
      $("#info-block").html('');
      $("#student-list").html('');
    });
  });


  $("#attendance_date_picker").change((e) => {
    getSubjectOrSessions()
  })

  function getSemesters() {
    var class_section_id = $("#class_section_id").val();
    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/get_class_semesters') ?>',
      type: 'post',
      data: { 'class_section_id': class_section_id },
      success: function (data) {
        var data = $.parseJSON(data);
        var semesters = data.semesters;
        var options = '<option value="">Select Semester</option>';
        for (var i = 0; i < semesters.length; i++) {
          options += `<option value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
        }
        $("#class_semester_id").html(options);
      }
    });
  }

  function getSemesterSubjectOrSessions() {
    var class_master_id = $("#class_section_id").val();
    var semester_main_screen_id = $("#class_semester_id").val() || '';
    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/get_subjects') ?>',
      type: 'post',
      data: { 'class_master_id': class_master_id, 'semester_main_screen_id': semester_main_screen_id },
      success: function (data) {
        var data = $.parseJSON(data);
        var viewSubjects = data.viewSubjects;

        if (viewSubjects.length) {
          var options = `<option value="0">Select Subject</option>`;
          for (var v of viewSubjects) {
            options += `<option ${v.is_subject_enabled == 1 ? '' : 'disabled style="color: lightgray"'} value="${v.subject_master_id}">${v.subject_name}</option>`;
          }
          $(".subject_id").html(options);

        }

      }
    });
  }

  function enableAttendance(periodNo) {
    $(`#get-students-btn_${periodNo}`).text("Take Attendance").prop("disabled", false)
  }

  function getSubjectOrSessions() {
    $("#attendance-form").hide();
    // $("#active-student-filter-type").hide();
    $("#student-filter").hide();

    const currentDate = $("#attendance_date").val().split("-").reverse().join("-")
    let date = currentDate.split(" ").splice(0, 5).join(" ")
    const weekDay = new Date(date).getDay()
    $("#periods-table-div").show()
    var section_id = $("#class_section_id").val();
    classSectionId = section_id
    $("#periods-tables").show()
    $(".color_codes").show()
    $("#display_msg").html('')

    if (attendance_type == 'subject_wise') {
      $(".subject_id").html('<option value="0">Select subject</option');
    } else {
      $("#session_id").html('<option value="0">Select session</option');
    }
    if (section_id == 0) {
      return false;
    }

    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/getSectionSubjectsORSessions'); ?>',
      type: 'post',
      data: {
        'section_id': section_id,
        'attendance_type': attendance_type,
        "weekDay": weekDay,
        "date": currentDate
      },
      success: function (data) {
        data = JSON.parse(data);
        const display_array = data.display_list;
        const subjects = data.subjects;

        if (display_array.length) {
          let period_templates = `
        <table class="table table-bordered text-center" id="periods-tables">
          <tr class="bg-light">
            <th>Timings</th>
            <th>Period</th>
            <th>Subject</th>
            <th>Taken By</th>
            <th>Taken On</th>
            <th width="12%">Attendance</th>
            <th>Communication</th>
            <th>Actions</th>
          </tr>
        `;

          function formatHour(time) {
            const hour = time.toString().slice(0, 5).split(":")[0] % 12 || 12;
            const minute = time.toString().slice(0, 5).split(":")[1];
            return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
          }

          display_array.forEach((period, i) => {
            $(".color_codes").show();
            const startHour = moment(period.start_time, "HH:mm").format("h:mm A");
            const endHour = moment(period.end_time, "HH:mm").format("h:mm A");

            if (!period.is_taken) {
              period_templates += `<tr>
             <td style="width:70px;">${startHour} <br>To<br> ${endHour}</td>
             <td style="width:70px;">
               <p id="${period.short_name}" value="${period.short_name}">${period.short_name}</p>
             </td>
             <td style="width: 258px;">
               <div id="subject-wise" class="">
                 <select class="form-control subject_id" id="subject_id_${period.short_name}" onchange="enableAttendance('${period.short_name}')">
                   <option value="0"> </option>
                 </select>
               </div>
             </td>
             <td id="taken_by_${period.short_name}">NA</td>
             <td id="taken_on_${period.short_name}" style="width: 129px;">NA</td>
             <td style="width: 133px;">NA</td>
             <td style="width:50px;">NA</td>
             <td class="d-flex" style="width: 100%;">
               <div class="d-flex">
                 <button style="margin-right: 5px;" disabled onclick="takeAttendance('${period.id}','${period.short_name}',${false},'','${startHour}','${endHour}')" id="get-students-btn_${period.short_name}" class="btn btn-primary" style="width: 120px;">Take Attendance</button>
               </div>
               </td>
               </tr>`
            } else {
              let getAttendanceBtn = `<button onclick="getAttendanceStatus('${period.type_id}','${period.class_section_id}','${period.type_id}','${period.short_name}')" id="getAttendanceStatus_${period.short_name}_${period.type_id}" class="btn btn-info getAttendanceStatus" style="width: 120px;">Get</button>`

              const sendNotification = `<button onclick="notify_students('${period.id}','${period.short_name}','${period.type_id}','','${startHour}','${endHour}')" class="btn btn-info btn-block">Send</button>`

              let editPermission = '<?php echo $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.EDIT_ATTENDANCE_SUBJECTWISE') ?>';

              let editBtn = `<button ${editPermission == 0 && "disabled"} onclick="getStudents('${period.type_id}',false,'${period.ttp_id}','${period.short_name}','1','${period.id}','${startHour}','${endHour}')" class="btn btn-warning btn-block">Edit</button>`

              period_templates += `<tr>
             <td style="width:70px;">${startHour} <br>To<br> ${endHour}</td>
             <td style="width:70px;">
               <p id="${period.short_name}" value="${period.short_name}">${period.short_name}</p>
             </td>
             <td style="width: 258px;">
               <div id="subject-wise" class="">
                 <select class="form-control" id="subject_id_${period.short_name}_${period.type_id}" onchange="enableAttendance('${period.short_name}')">
                   <option value="0">Subject removed for this Grade</option>
                 </select>
               </div>
             </td>
             <td id="taken_by_${period.short_name}">${period.taken_by_name}</td>
             <td id="taken_on_${period.short_name}" style="width: 129px;">${period.take_on_local}</td>
             <td style="width: 133px;">
               <div id="getAttendence_${period.short_name}">${getAttendanceBtn}</div>
               <label data-toggle="modal" data-target="#students_attendance_names" onClick="get_attendence_students_names('${period.id}','${period.short_name}','${period.type_id}',0)" style="background: #aba3a3;font-size: 13px;cursor:pointer;" class="label label-control" id="not_taken_${period.short_name}_${period.type_id}"></label>
               <label data-toggle="modal" data-target="#students_attendance_names" onClick="get_attendence_students_names('${period.id}','${period.short_name}','${period.type_id}',1)"  style="background: #46a946;font-size: 13px;cursor:pointer;" class="label label-control" id="present_${period.short_name}_${period.type_id}"></label>
               <label data-toggle="modal" data-target="#students_attendance_names" onClick="get_attendence_students_names('${period.id}','${period.short_name}','${period.type_id}',2)" style="background: #dd6d6d;font-size: 13px;cursor:pointer;" class="label label-control" id="absent_${period.short_name}_${period.type_id}"></label>
               <label data-toggle="modal" data-target="#students_attendance_names" onClick="get_attendence_students_names('${period.id}','${period.short_name}','${period.type_id}',3)" style="background: #ddc72b;font-size: 13px;cursor:pointer;" class="label label-control" id="late_${period.short_name}_${period.type_id}"></label>
               <label data-toggle="modal" data-target="#students_attendance_names" onClick="get_attendence_students_names('${period.id}','${period.short_name}','${period.type_id}',4)" style="background: #46a946;font-size: 13px;cursor:pointer;" class="label label-control" id="absent_by_permission_${period.short_name}_${period.type_id}"></label>
             </td>
             <td style="width:50px;">
               <div id="send_${period.short_name}">${sendNotification}</div>
             </td>
              <td class="d-flex" style="width: 100%;">
                    <div id="edit_${period.short_name}">${can_edit_all_subjects==1 && editBtn || period.login_staff_id == 0 && editBtn || period.taken_by == period.login_staff_id && editBtn || ""}</div>
                    <div id="take_elective_${period.short_name}_${period.type_id}"></div>
                    <div id="remove_att_${period.short_name}_${period.type_id}"></div>
                </td>
              </tr>`
            }
          })

          period_templates += `</table>`
          $("#period_template").html(period_templates)

          let isSuperAdmin = "<?php echo $user ?>";

          display_array.forEach((period, i) => {

            // console.log(period);


            if (period.is_taken) {
              let subjectName = "";
              subjects.forEach((subject, i) => {
                if (subject.id == period.type_id) {
                  subjectName = subject.subject_name;
                  $(`#subject_id_${period.short_name}_${period.type_id}`).html(`<option value=${period.type_id}>${subject.subject_name}</option>`).prop("disabled", true)
                }

                if (period.elective.length) {
                  let date = $("#attendance_date").val();
                  date = date.split("-").reverse().join("-")

                  let take_elective = `<button type="button" data-toggle="modal" data-target="#getElectiveSubjects_modal" 
                    data-class_section_id="${period.class_section_id}" data-date="${period.date}" data-period_no="${period.short_name}" data-ttp_id="${period.ttp_id}" data-elective_master_group_id="${period.elective[0].elective_master_group_id}" data-class_master_id="${subjects[i].class_master_id}" data-type_id="${period.type_id}" class="btn btn-danger btn-block">Take Elective</button>`
                  $(`#take_elective_${period.short_name}_${period.type_id}`).html(take_elective);
                }


                // console.log(isSuperAdmin);
                if (isSuperAdmin) {
                  let removeAttBtn = `<button class="btn btn-danger" onClick="removeAttendance('${period.id}','${period.short_name}','${subjectName}')">Remove Attendance</button>`;
                  $(`#remove_att_${period.short_name}_${period.type_id}`).html(removeAttBtn);
                }
              })
            }
          })

        } else {
          $(".color_codes").hide();
          const no_template_msg = `
  <div style="color:red;text-align:center;
  color: #000;
  border: 2px solid #fffafa;
  text-align: center;
  border-radius: 6px;
  position: relative;
  margin-left: 14px;
  padding: 10px;
  font-size: 14px;
  margin-top: 15px;
  background: #ebf3ff;">
  Attendance Template Not Assigned For This Section
  </div>
  `;
          $("#period_template").html(no_template_msg)
        }

        if (data.attendance_type == 'subject_wise') {
          if ('<?php echo $is_semester_scheme; ?>' == '1') {
            getSemesterSubjectOrSessions();
          } else {
            var options = '<option value="0">Select subjects</option>';
            for (var i = 0; i < subjects.length; i++) {
              options += `<option ${subjects[i].is_subject_enabled == 1 ? '' : 'disabled style="color: lightgray"'} value="${subjects[i].id}">${subjects[i].subject_name}</option>`;
              $(".subject_id").html(options);
            }
          }

        }

      }
    });
  }

  function removeAttendance(attendanceMasterId, periodNo, subjectName) {
    // console.log(attendanceMasterId);
    Swal.fire({
      title: 'Remove Attendance?',
      html: `<p>This will delete the attendance for <br>
          <b>${periodNo}</b> 
          <br>
          <b>${subjectName}</b>
          </p>`,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "<?php echo site_url('attendance_v2/attendance/remove_attendance') ?>",
          type: "POST",
          data: { attendanceMasterId },
          success(data) {
            if (data == 1) {
              $(function () {
                new PNotify({
                  title: 'Success',
                  text: `Attendance Removed successfully for <b>${periodNo} ${subjectName}</b>`,
                  type: 'success',
                });
              });
              getSubjectOrSessions();
            } else {
              $(function () {
                new PNotify({
                  title: 'Success',
                  text: `Something went wrong</b>`,
                  type: 'success',
                });
              });
            }
          }
        })
      }
    });

  }

  function getAttendanceStatus(type_id, classSectionId, subjectId, period_no) {
    var date = $("#attendance_date").val();
    date = date.split("-").reverse().join("-")
    $(`#getAttendanceStatus_${period_no}_${type_id}`).css("display", "none");

    $.ajax({
      url: "<?php echo site_url('attendance_v2/attendance/getAttendanceStatus') ?>",
      type: "POST",
      data: {
        "classSectionId": classSectionId,
        "subjectId": subjectId,
        "period_no": period_no,
        "date": date
      },
      success: function (data) {
        data = JSON.parse(data);
        data.forEach((data, i) => {
          $(`#not_taken_${period_no}_${type_id}`).text(`${data.not_taken}`)
          $(`#present_${period_no}_${type_id}`).text(`${data.present}`)
          $(`#absent_${period_no}_${type_id}`).text(`${data.absent}`)
          $(`#late_${period_no}_${type_id}`).text(`${data.late}`)
          $(`#absent_by_permission_${period_no}_${type_id}`).text(`${data.absent_by_permission}`)
        })
      }
    })
  }

  $("#class_section_id, .subject_id, #session_id").change(function () {
    $("#info-block").html('');
    $("#student-list").html('');
  });

  function takeAttendance(timetable_id, period_name, isElective = false, electiveTypeId, startHour, endHour) {
    window.sessionStorage.setItem("periodStartHour", startHour);
    window.sessionStorage.setItem("periodEndHour", endHour);
    window.sessionStorage.setItem("isFirstTimeAttendanceTaken", false);

    resetStudentFilter();

    isEdit = 0
    period_no = period_name
    let isPeriodExist = checkPeriodExists(period_no, classSectionId, timetable_id);
    isPeriodExist.then(data => {
      if (data.length) {
        if (!isElective) {
          $("#attendance-form").hide()
          // $("#active-student-filter-type").hide();
          $("#student-filter").hide();
          getSubjectOrSessions()
          $("#periods-tables").show()
          $("#periods-table-div").show()
          // return bootbox.alert("Already Taken Attendance For Period " + period_no)
          return bootbox.confirm({
            title: 'ALERT',
            message: 'Attendance already taken for this period ' + period_no,
            buttons: {
              cancel: {
                label: '<i class="fa fa-times" style="display:none">Cancel</i>'
              },
              confirm: {
                label: '<i class="fa fa-check"></i> OK'
              }
            },
            callback: function (result) {
              // console.log('This was logged in the callback: ' + result);
            }
          });
        }
      }
      $("#periods-tables").hide()
      $("#periods-table-div").hide()
      $(".color_codes").hide()

      $("#info-block").html('');
      $("#student-list").html('');

      var date = $("#attendance_date").val();
      var section_id = $("#class_section_id").val();
      if (section_id == 0) return false;
      var section_name = $("#class_section_id option:selected").text();
      var type_name = '';
      if (attendance_type == 'subject_wise') {
        var type_id = $("#subject_id_" + period_no).val();
        if (isElective) {
          type_id = electiveTypeId;
        }
        // alert(type_id);
        type_name = $("#subject_id" + period_no + " option:selected").text();
      } else {
        var type_id = $("#session_id").val();
        type_name = $("#session_id option:selected").text();
      }
      if (!type_id) return false;

      $.ajax({
        url: '<?php echo site_url('attendance_v2/attendance/checkAttendanceTaken'); ?>',
        type: 'post',
        data: {
          'date': date,
          'section_id': section_id,
          'type_id': type_id,
          'type': attendance_type,
          "timetable_id": timetable_id
        },
        success: function (data) {
          data = JSON.parse(data);
          $("#periods-table-div").show()
          $("#periods-table-div").hide()

          var startHour = window.sessionStorage.getItem("periodStartHour", startHour);
          var endHour = window.sessionStorage.getItem("periodEndHour", endHour);
          getStudents(type_id, isElective, timetable_id, period_no, 0, 0, startHour, endHour);
        }
      });
    })

  }

  function resetStudentFilter() {
    // reset the student filter default to selected
    const studentFilterOptions = document.querySelectorAll(".student-filter-option");
    studentFilterOptions.forEach(o => {
      o.removeAttribute("selected");
    });

    for (let i = 0; i < studentFilterOptions.length; i++) {
      if (studentFilterOptions[i].getAttribute("value") === "default") {
        studentFilterOptions[i].setAttribute("selected", true);
        break;
      }
    }
  }

  function getStudents(type_id, isElective, timetable_id = "0", period_no = "0", is_edit = 0, attendance_master_id = 0, startHour, endHour) {
    window.sessionStorage.setItem("periodStartHour", startHour);
    window.sessionStorage.setItem("periodEndHour", endHour);

    resetStudentFilter();

    isEdit = is_edit
    period_no = period_no
    $("#periods-tables").hide();
    $("#periods-table-div").hide()
    $(".color_codes").hide()
    $("#info-block").html('');

    // $("#active-student-filter-type").show();
    $("#student-filter").show();

    var date = $("#attendance_date").val();
    var section_id = $("#class_section_id").val();
    var section_name = $("#class_section_id option:selected").text();
    // var type_id = 0;
    var type_name = '';

    if (!isElective) {
      if (attendance_type == 'subject_wise') {
        // type_id = $("#subject_id_" + period_no).val();
        type_name = $("#subject_id_" + period_no + "_" + type_id + " option:selected").text();
      } else {
        type_id = $("#session_id").val();
        type_name = $("#session_id option:selected").text();
      }
    } else {
      type_id = $("#elective_subjects").val()
      type_name = $("#elective_subjects option:selected").text()
    }

    allStudentsData = { type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type }

    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/getSubjectSectionStudents'); ?>',
      type: 'post',
      data: {
        'date': date,
        'section_id': section_id,
        'attendance_type': attendance_type,
        'attendance_type_id': type_id,
        'attendance_master_id': attendance_master_id,
        'period_no': period_no,
      },
      success: function (data) {
        data = JSON.parse(data);
        var students = data.students;
        studentsData = students;
        const subject = data.subject;
        subjectsData = subject;

        var parameter_is_elective = (isElective ? 1 : 0);
        var html = `
        <form id="attendance-form">
          <div class="col-md-12 mb-3 mr-0" style="position:relative;">
            <input type="hidden" id="attendance-source" name="source" value="Manual"/>
            <div style="display:flex;align-items: center;justify-content: end;">
              <button class="btn btn-success" id="save-attendance-btn" style="margin: 5px;" onclick="saveAttendance('${parameter_is_elective}','${timetable_id}',${type_id})" type="button">Save</button>
              <button class="btn btn-primary" id="save-attendance-btn" style="margin: 5px;" onclick="hideStudentDataTable()" type="button">Go Back</button>
            </div>
            ${(import_enabled && !is_edit) ? '<button class="btn btn-primary mr-3" style="float:right;" onclick="selectImport()" type="button">Import From Excel</button>' : ''}
          </div>`;
        html += `<div class="col-md-12">
                <input type="hidden" id="period_no" name="period_no" value="${period_no}">
                <input type="hidden" id="timetable_id" name="timetable_id" value="${timetable_id}">
                <input type="hidden" id="isElective" name="isElective" value="${isElective}">
                <input type="hidden" name="is_edit" value="${is_edit}" />
                <input type="hidden" name="attendance_master_id" value="${attendance_master_id}" />
                <input type="hidden" name="attendance_type" value="${attendance_type}" />
                <input type="hidden" name="attendance_type_id" value="${type_id}" />
                <input type="hidden" name="section_id" value="${section_id}" />
                <input type="hidden" name="subject_name" value="${subject}" />
                <input type="hidden" name="date" value="${date}" />
                <input type="hidden" id="max-class-duration" name="class_duration" value="" />`;
        html += `<table class="table table-bordered">
              <thead>
              <tr>
              <th colspan="8" style="font-size: 1.5rem;">${(is_edit) ? 'Edit' : 'Take'} Attendance: ${date} 
              <span>Subject : ${subject} </span> 
              <span>Period No : ${period_no} </span> 
              <span style="float: right;">${section_name}:${type_name}<span>
              </th>
              </tr>
              </thead>
              <thead>
              <tr>
              <th>#</th><th>Student</th>${import_enabled ? `<th>Duration <span id="max-duration">(${is_edit ? +students[0].duration + ' mins' : '-'})</span></th>` : ''}
              <th style="cursor:pointer;" data-toggle="tooltip" data-original-title="Click to make all not-taken" id="not-taken-all">Not Taken (<span id="not-taken-count"></span>) <span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to present" id="present-all">Present (<span id="present-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to absent" id="absent-all">Absent (<span id="absent-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to late" id="late-all">Late (<span id="late-count"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>
              <th  style="cursor:pointer;" data-toggle="tooltip" data-original-title="Move not-taken to absent with permission" id="absent-by-permission-all">Absent By Permission (<span id="absent-by-permission"></span>)<span style="float:right;cursor:pointer;font-size: 1.5rem;"><i class="fa fa-check"></i></span></th>`;
        if (is_edit) {
          html += `<th>History</th>`;
        }
        html += `</tr></thead><tbody id="student-list-body">`;
        for (var i = 0; i < students.length; i++) {
          var student_id = students[i].student_id;
          var status = (is_edit) ? students[i].status : 0;
          html += `<tr class="student-name-list">
                    <td>${i + 1}</td>
                    <td>${students[i].student_name}
                    <input type="hidden" id="old-status-${student_id}" name="old_status[${student_id}]" data-student_id="${student_id}" class="old-status" value="${status}"/>
                    <input type="hidden" id="new-status-${student_id}" name="new_status[${student_id}]" data-student_id="${student_id}" class="new-status" value="${status}"/>
                    </td>`;
          var minimum_duration = '';
          if (import_enabled) {
            var mins = '-';
            if (is_edit) {
              minimum_duration = students[i].attended_duration;
              mins = (minimum_duration == '-') ? '-' : minimum_duration + ' mins';
            }
            html += `<td class="min-duration">${mins}</td>`;
          }
          html += `<input type="hidden" name="minimum_duration[${student_id}]" class="min-duration-input" value="${minimum_duration}"/>`;
          html += `<td data-email="${students[i].email}" data-status="0" data-student_id="${student_id}" class="att-status not-taken ${(status == 0) ? 'active' : ''}">${(status == 0) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="1" data-student_id="${student_id}" class="att-status present ${(status == 1) ? 'active' : ''}">${(status == 1) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="2" data-student_id="${student_id}" class="att-status absent ${(status == 2) ? 'active' : ''}">${(status == 2) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="3" data-student_id="${student_id}" class="att-status late ${(status == 3) ? 'active' : ''}">${(status == 3) ? '<i class="fa fa-check"></i>' : ''}</td>
            <td data-email="${students[i].email}" data-status="4" data-student_id="${student_id}" class="att-status absent_by_permission ${(status == 4) ? 'active' : ''}">${(status == 4) ? '<i class="fa fa-check"></i>' : ''}</td>`;
          if (is_edit) {
            html += `<th class="text-center;"><button type="button" id="att-${students[i].student_attendance_id}" data-student_id="${student_id}" data-student_name="${students[i].student_name}" onclick="getAttendanceAudit(${students[i].student_attendance_id})" class="btn btn-primary"><i class="fa fa-eye"></i></button></th>`;
          }
          html += `</tr>`;
        }
        html += `</tbody></div></form>`;
        $("#student-list").html(html);
        updateCounts();

        $(".att-status").click(function () {
          var student_id = $(this).data('student_id');
          var status = $(this).data('status');
          $("#new-status-" + student_id).val(status);
          var tr = $(this).parent();
          tr.children('.att-status').html('');
          tr.children('.att-status').removeClass('active');
          $(this).addClass('active');
          $(this).html('<i class="fa fa-check"></i>');
          updateCounts();
        });

        $("#not-taken-all").click(function () {
          $(".not-taken").click();
        });
        $("#present-all").click(function () {
          $(".not-taken.active").siblings(".present").click();
        });
        $("#absent-all").click(function () {
          $(".not-taken.active").siblings(".absent").click();
        });
        $("#late-all").click(function () {
          $(".not-taken.active").siblings(".late").click();
        });
        $("#absent-by-permission-all").click(function () {
          $(".not-taken.active").siblings(".absent_by_permission").click();
        });
      }
    });
  }

  async function notify_students(attendance_id, period_short_name, attendance_type_id, subjectName, start_hour, end_hour) {
    let date = $("#attendance_date").val();

    let subject = $(`#subject_id_${period_short_name}_${attendance_type_id} option:selected`).text() || subjectName;

    let school_name = '<?php echo $this->settings->getSetting('school_name') ?>';

    $("#notify-attendance-master-id").val(attendance_id);
    await $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/get_absentees_latecomers'); ?>',
      type: 'post',
      data: {
        'attendance_master_id': attendance_id
      },
      success: function (data) {
        let students = JSON.parse(data);
        if (students.length) {
          let html = '';
          $("#notify-modal").modal('show');
          html = `<table class="table"><thead><tr><th>#</th><th style="min-width: 120px;">Student</th><th>Section</th><th>Reason</th><th>Message</th></tr></thead><tbody>`;
          for (let i = 0; i < students.length; i++) {
            let std_msg = (students[i].status == '2') ? absent_message : late_message;
            std_msg = std_msg.replace('%%student_name%%', students[i].student_name);
            std_msg = std_msg.replace('%%class_section%%', students[i].class_name + '' + students[i].section_name);
            std_msg = std_msg.replace('%%class_name%%', students[i].class_name);
            std_msg = std_msg.replace('%%section_name%%', students[i].section_name);
            std_msg = std_msg.replace('%%date%%', date);

            if (subject?.length > 30) {
              subject = subject.substring(0, 27).padEnd(30, ".");
            }

            std_msg = std_msg.replace('%%subject%%', subject);

            if (std_msg.includes("%%start_hour%%")) {
              std_msg = std_msg.replace('%%start_hour%%', start_hour);
            }

            if (std_msg.includes("%%end_hour%%")) {
              std_msg = std_msg.replace('%%end_hour%%', end_hour);
            }

            std_msg = std_msg.replace('${school_name}', school_name);
            html += `
              <tr>
                  <td>${i + 1}</td>
                  <td>${students[i].student_name}</td>
                  <td>${students[i].class_name + '' + students[i].section_name}</td>
                  <td>${(students[i].status == '2') ? 'Absent' : 'Late'}</td>
                  <td>
                      ${std_msg}
                      <input type="hidden" name="student_messages[${students[i].student_id}]" value="${std_msg}" />
                  </td>
              </tr>
            `;
          }
          html += '</tbody></table>';
          $("#notify-content").html(html);
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  // get_present_students
  async function get_attendence_students_names(attendance_id, period_short_name, attendance_type_id, status) {
    await $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/get_attendence_students_names'); ?>',
      type: 'post',
      data: {
        'attendance_master_id': attendance_id,
        'status': status
      },
      beforeSend: function () {
        $("#students_names_for_attendance").hide()
        $('#loading_icon').show();
      },
      complete: function () {
        $('#loading_icon').hide();
        $("#students_names_for_attendance").show()
      },
      success: function (data) {
        data = JSON.parse(data);
        if (data.length) {
          let present = "#c7eac7"
          let absent = "#ffe6e6"
          let late = "#eaeac4"
          let absent_by_permission = "#00FF00"

          let background_color
          let students_belongs_to

          if (status == 1) {
            background_color = present
            students_belongs_to = "[Present]"
          } else if (status == 2) {
            background_color = absent
            students_belongs_to = "[Absentees]"
          } else if (status == 3) {
            background_color = late
            students_belongs_to = "[Late Comers]"
          } else if (status == 4) {
            background_color = absent_by_permission
            students_belongs_to = "[Absent With Permission]"
          }
          else {
            students_belongs_to = "[Attendance Not Taken]"
          }

          $("#att_modal_name").text(`Student Names [${data[0].class_name} ${data[0].section_name}] ${students_belongs_to}`)

          let student_names = `<table class="table table-bordered" style="background:${background_color}">
        <tr>
        <th style="width:36%;">#</th>
        <th>Student Name</th>
        </tr>`;

          data.forEach((student, i) => {
            student_names += `<tr>
        <td>${++i}</td>
        <td>${student.student_name}</td>
        </tr>`
          })

          student_names += `</table>`
          $("#students_names_for_attendance").html(student_names)
        } else {
          $("#att_modal_name").text(`Student Names`)
          $("#students_names_for_attendance").html("<center>Students Not Available</center>")
        }
      }
    })
  }

  function send_messages() {
    $("#confirmBtn").attr('disabled', true).html('Please Wait...');
    let formData = new FormData(document.getElementById('student-messages-form'));
    formData.append('attendance_master_id', $("#notify-attendance-master-id").val());
    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/send_messages'); ?>',
      type: 'post',
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function (data) {
        $("#notify-modal").modal('hide');
        let response = JSON.parse(data);
        if (response.status == 1) {
          $(function () {
            new PNotify({
              title: 'Success',
              text: 'Messages Sent Successfully',
              type: 'success',
            });
          });
        } else {
          if (response.error?.length) {
            $(function () {
              new PNotify({
                title: 'Error',
                text: response.error,
                type: 'error',
              });
            });
          }

          if (response.warning?.length) {
            $(function () {
              new PNotify({
                title: 'Warning',
                text: response.warning,
                type: 'warning',
              });
            });
          }
        }
        $("#confirmBtn").attr('disabled', false).html('Confirm');
      },
      error: function (err) {
        $("#notify-modal").modal('hide');
        console.log(err);
        $(function () {
          new PNotify({
            title: 'Error',
            text: 'Sending messages failed!!',
            type: 'error',
          });
        });
        $("#confirmBtn").attr('disabled', false).html('Confirm');
      }
    });
  }

  function selectImport() {
    $("#import-modal").modal('show');
  }

  function exportarExcel() {
    var class_duration = document.getElementById("class_duration").value;
    var minimum_duration = parseInt(document.getElementById("minimum_duration").value);
    var importer = document.getElementById("import_file");
    var required = 0;
    if (importer.files.length == 0) {
      $("#file-required").show();
      required = 1;
    }
    $("#file-required").hide();

    if (class_duration == '') {
      required = 1;
    }

    if (minimum_duration == '') {
      required = 1;
    }

    if (required) {
      return false;
    }

    $("#import-modal").modal('hide');
    $("#attendance-source").val('Import');
    $("#max-duration").html(`(${class_duration} mins)`);
    $("#max-class-duration").val(class_duration);
    var selectedFile = importer.files[0];
    if (selectedFile) {
      var fileReader = new FileReader();
      fileReader.readAsBinaryString(selectedFile);
      fileReader.onload = (event) => {
        var data = event.target.result;
        var workbook = XLSX.read(data, {
          type: "binary",
          cellDates: true
        });
        var sheet = workbook.SheetNames[0];
        var rows = XLSX.utils.sheet_to_json(workbook.Sheets[sheet]);
        for (var i = 0; i < rows.length; i++) {
          var email = (rows[i].email).trim();
          var duration = parseInt(rows[i].duration);
          if (duration >= minimum_duration) { //present
            var present = $(`.present[data - email = '${email}'] `);
            var parent = present.parent('tr');
            parent.find('.min-duration').html(`${duration}mins`);
            parent.find('.min-duration-input').val(duration);
            present.click();
          } else { //absent
            var absent = $(`.absent[data - email = '${email}'] `);
            var parent = absent.parent('tr');
            parent.find('.min-duration').html(`${duration}mins`);
            parent.find('.min-duration-input').val(duration);
            absent.click();
          }
        }
        $("#absent-all").click();
      }
    }
  }

  function updateCounts() {
    var not_taken = $(".not-taken.active").length;
    var present = $(".present.active").length;
    var absent = $(".absent.active").length;
    var late = $(".late.active").length;
    var absent_by_permission = $(".absent_by_permission.active").length;
    $("#not-taken-count").html(not_taken);
    $("#present-count").html(present);
    $("#absent-count").html(absent);
    $("#late-count").html(late);
    $("#absent-by-permission").html(absent_by_permission);

  }

  function getAttendanceAudit(student_attendance_id) {
    $("#audit-modal").modal('show');
    var student_name = $("#att-" + student_attendance_id).data('student_name');
    var student_id = $("#att-" + student_attendance_id).data('student_id');
    $("#student-name").html(student_name);
    $("#audit-data").html('<tr><td colspan="4"><i style="font-size: 1.5rem;" class="fa fa-spinner fa-spin"></i></td></tr>');

    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/getStudentAttendanceAudit'); ?>',
      type: 'post',
      data: {
        'student_attendance_id': student_attendance_id
      },
      success: function (data) {
        data = JSON.parse(data);
        var history = data.history;
        if (history.length == 0) {
          $("#audit-data").html('<tr><td colspan="4">No data available</td></tr>');
        } else {
          var status_array = {
            0: '<td class="not-taken active">Not Taken</td>',
            1: '<td class="present active">Present</td>',
            2: '<td class="absent active">Absent</td>',
            3: '<td class="late active">Late</td>'
          }
          var html = '';
          for (var i = 0; i < history.length; i++) {
            html += `<tr>
            <td> ${i + 1} </td>
            ${status_array[history[i].status]}
            <td> ${history[i].action_by} </td> 
            <td> ${history[i].action_on} </td> 
          </tr>`;
          }
          $("#audit-data").html(html);
        }
      }
    });
  }

  async function checkPeriodExists(period_no, classSectionId, timetable_id) {
    const currentDate = $("#attendance_date").val().split("-").reverse().join("-")
    let periodsLength
    await $.ajax({
      url: "<?php echo site_url('attendance_v2/attendance/checkPeriodExists') ?>",
      type: "POST",
      data: {
        "period_no": period_no,
        "class_section_id": classSectionId,
        "class_section_id": classSectionId,
        timetable_id,
        "date": currentDate
      },
      success: function (data) {
        data = JSON.parse(data)
        periodsLength = data
      }
    })
    return periodsLength
  }

  function saveAttendance(isElective, timetable_id, type_id) {
    if (isEdit) {
      $("#attendance-form").show();
      // $("#active-student-filter-type").show();
      $("#student-filter").show();
      $("#save-attendance-btn").html('Please Wait...').prop('disabled', true);
      var has_not_taken = 0;
      $(".new-status").each(function () {
        if ($(this).val() == 0) {
          has_not_taken++;
        }
      });
      if (has_not_taken) {
        Swal.fire({
          title: 'Save Attendance?',
          html: '<p>There are students whose status is <br><b>Not-Taken.</b><br>Still want to save?</p>',
          confirmButtonText: 'Confirm',
          showCancelButton: true,
          showLoaderOnConfirm: true
        }).then((result) => {
          if (result.isConfirmed) {
            saving_attendance();
          } else {
            $("#save-attendance-btn").html('Save').prop('disabled', false);
          }
        });
      } else {
        saving_attendance();
      }
      return
    }

    let isPeriodExist = checkPeriodExists(period_no, classSectionId, timetable_id);
    isPeriodExist.then(data => {
      if (data.length) {
        let sameElectiveSubject = false;

        data.forEach(e => {
          // console.log(e);
          if (e.type_id == type_id) {
            sameElectiveSubject = true;
          }
        })

        if (isElective == 0 || sameElectiveSubject) {
          $("#attendance-form").hide();
          // $("#active-student-filter-type").hide();
          $("#student-filter").hide();
          getSubjectOrSessions()
          $("#periods-tables").show()
          $("#periods-table-div").show()
          // return bootbox.alert("Already Taken Attendance For Period " + period_no)
          return bootbox.confirm({
            title: 'ALERT',
            message: 'Attendance already taken for this period ' + period_no,
            buttons: {
              cancel: {
                label: '<i class="fa fa-times" style="display:none">Cancel</i>'
              },
              confirm: {
                label: '<i class="fa fa-check"></i> OK'
              }
            },
            callback: function (result) {
              console.log('This was logged in the callback: ' + result);
            }
          });
        }
      }
      $("#periods-tables").hide()
      $("#periods-table-div").hide()
      $("#attendance-form").show()
      // $("#active-student-filter-type").show();
      $("#student-filter").show();
      $("#save-attendance-btn").html('Please Wait...').prop('disabled', true);
      var has_not_taken = 0;
      $(".new-status").each(function () {
        if ($(this).val() == 0) {
          has_not_taken++;
        }
      });

      const isFirstTimeAttendanceTaken = window.sessionStorage.getItem("isFirstTimeAttendanceTaken");

      if (has_not_taken) {
        Swal.fire({
          title: 'Save Attendance?',
          html: '<p>There are students whose status is <br><b>Not-Taken.</b><br>Still want to save?</p>',
          confirmButtonText: 'Confirm',
          showCancelButton: true,
          showLoaderOnConfirm: true
        }).then((result) => {
          if (result.isConfirmed) {
            if (isFirstTimeAttendanceTaken == "false") {
              saving_attendance();
              window.sessionStorage.setItem("isFirstTimeAttendanceTaken", true);
            }
          } else {
            $("#save-attendance-btn").html('Save').prop('disabled', false);
          }
        });
      } else {
        if (isFirstTimeAttendanceTaken == "false") {
          saving_attendance();
          window.sessionStorage.setItem("isFirstTimeAttendanceTaken", true);
        }
      }
    })
  }

  async function saving_attendance() {
    var form = $('#attendance-form')[0];
    var formData = new FormData(form);

    let subjectName = formData.get("subject_name")
    let period_short_name = formData.get("period_no")
    let attendance_type_id = formData.get("attendance_type_id")

    await $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/save_attndance_data'); ?>',
      type: 'post',
      data: formData,
      processData: false,
      contentType: false,
      success: function (master_id) {
        if (parseInt(master_id) != 0) {
          $(function () {
            new PNotify({
              title: 'Success',
              text: 'Attendance saved successfully',
              type: 'success',
            });
          });
          const startHour = window.sessionStorage.getItem("periodStartHour");
          const endHour = window.sessionStorage.getItem("periodEndHour");

          if (enable_notification) notify_students(parseInt(master_id), period_short_name, attendance_type_id, subjectName, startHour, endHour);
          hideStudentDataTable()
        } else {
          $(function () {
            new PNotify({
              title: 'Error',
              text: 'Something went wrong!!',
              type: 'error',
            });
          });
        }
      }
    });
  }

  function hideStudentDataTable() {
    // $("#active-student-filter-type").hide();
    $("#student-filter").hide();

    $("#info-block").html('');
    $("#student-list").html('');
    getSubjectOrSessions()
    $("#periods-table-div").show()
  }

  function sampleExcel() {
    swal.fire({
      title: 'Download Sample Excel File',
      showCancelButton: true,
      confirmButtonText: 'Download',
      showLoaderOnConfirm: true,
      preConfirm: () => {
        var wb = XLSX.utils.book_new();
        wb.Props = {
          Title: "NextElement",
          Subject: "Attendance",
          Author: "NextElement",
          CreatedDate: new Date()
        };

        wb.SheetNames.push("Attendance");
        var fields = ['name', 'email', 'duration'];
        var att = XLSX.utils.aoa_to_sheet([fields]);
        wb.Sheets["Attendance"] = att;
        var wbout = XLSX.write(wb, {
          bookType: 'xlsx',
          type: 'binary'
        });
        downloadSample(wbout);
        Swal.close()
      }
    })
  }

  function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;

  }

  function downloadSample(wbout) {
    saveAs(new Blob([s2ab(wbout)], {
      type: "application/octet-stream"
    }), 'attendanceSample.xlsx');
  };
</script>

<style type="text/css">
  .att-status {
    cursor: pointer;
    text-align: center;
  }

  .att-status>i {
    font-size: 1.5rem;
  }

  .att-status:hover {
    background: #ccc;
  }

  .absent.active {
    background-color: #ff9696 !important;
  }

  .present.active {
    background-color: #91fcb9 !important;
  }

  .late.active {
    background-color: #fffc96 !important;
  }

  .attendance-status {
    padding: 0.2rem 0.8rem;
    background-color: #ccc;
    border-radius: 1.2rem;
    font-size: 0.8rem;
    font-weight: 300;
  }

  .present-status {
    background-color: #DCFFDC;
  }

  .absent-status {
    background-color: #ffaeae;
  }

  .late-status {
    background-color: #fffc96;
  }

  p {
    padding: 0 10px;
    text-align: center;
    border-radius: 4px;
    margin: 5px;
  }

  .modal-dialog {
    width: 50%;
    margin: auto;
  }
</style>



<?php $this->load->view("attendance_v2/getElectiveSubjects_modal") ?>