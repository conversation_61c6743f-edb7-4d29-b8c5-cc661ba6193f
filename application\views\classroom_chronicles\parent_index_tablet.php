<div class="container" style="min-height:550px">
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style">
      <div class="row" style="margin-top:6rem;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h2 class="card-title panel_title_new_style text-center" style="margin-left:20rem">
            
            View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?>
          </h2>
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="col-md-12 " >
                    <div class="panel-body">
                <div class="col-md-10">
          <div class="form-group">
            <label class="control-label" for="student_id">Select Date</label>
            <div id="reportrange" class="dtrange form-control">  
              <span></span>                                          
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
              </div> 
          </div>
        </div>
      </div>
        <div class="col-md-4 " style="margin:auto;">
          <div class="form-group "> 
            <label class="control-label text-center"> </label>
            <button type="button"  class="btn btn-primary form-control " onclick="generate_chronicles_report()">Get Report</button>
          </div>
        </div>
      </div>
    </div>
  </div>

    <div class="modal-body">
      <div class="chronicles"></div>
    </div>
</div>
</div>

<div class="modal fade" id="view_chronicles" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:85%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></h4>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <div id="chronicles-detail">
                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>

