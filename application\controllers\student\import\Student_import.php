<?php

class Student_import extends CI_Controller {
	private $currentYearId;
	private $currentYear;
	private $yearIds = array(); 
    function __construct() {
        parent::__construct();
	    if (!$this->ion_auth->logged_in()) {
	      	redirect('auth/login', 'refresh');
	    }
	    if (!$this->authorization->isSuperAdmin()) {
	      redirect('dashboard', 'refresh');
	    }
	    $this->currentYearId = $this->acad_year->getAcadYearId();
	    $this->currentYear = $this->acad_year->getAcadYear();
	    $this->yearIds = $this->_formYearData();
	    $this->load->model('student/Student_Model');
	    $this->config->load('form_elements');
	    $this->load->library('filemanager');
    }

    private function _formYearData() {
		$years = $this->acad_year->getAllYearData();
		$yearIds = array();
		foreach ($years as $key => $value) {
			$yearIds[$value->acad_year] = $value->id;
		}
		return $yearIds;
	}

	private function __marshalClassId($id, $pClassName) {
      $classes = $this->db->select('id, class_name')
        ->from('class')
        ->where('acad_year_id', $this->currentYearId)
        ->get()->result();
        
      $found = 0;
      // echo $pClassName;
      // echo "<pre>"; print_r($classes);die();
      foreach ($classes as $class) {
        if ($class->class_name == $pClassName) {
          $found = $class->id;
          break;
        }
      }

      if ($found==0) {
        echo 'Problem found in Class; ';
        echo 'ID : ' . $id . '; Input value: ' . $pClassName;die();
      }
      return $found; //Return the promoted class Id.
    }

    private function _getSection($class_id, $section_name) {
    	$sections = $this->db->select('id, section_name')->where('class_id', $class_id)->get('class_section')->result();
    	$section_id = null;
    	foreach ($sections as $key => $section) {
    		if($section_name == $section->section_name) {
    			$section_id = $section->id;
    			break;
    		}
    	}
    	return $section_id;
    }

    private function _marshalBoard($board) {
      $boards = $this->settings->getSetting('board');
      foreach ($boards as $value => $name) {
        if($name == $board) {
          return $value;
        }
      }
      return 0;
    }

    private function _marshalMedium($medium) {
      $mediums = $this->settings->getSetting('medium');
      foreach ($mediums as $value => $name) {
        if($name == $medium) {
          return $value;
        }
      }
      return 1;
    }

    private function _marshalBoarding($boarding) {
      $boardings = $this->settings->getSetting('boarding');
      foreach ($boardings as $value => $name) {
        if($name == $boarding) {
          return $value;
        }
      }
      return 1;
    }

    private function _marshalRTE($rte) {
      $rtes = $this->settings->getSetting('rte');
      foreach ($rtes as $value => $name) {
        if($name == $rte) {
          return $value;
        }
      }
      return 2;
    }

    private function __marshalCategory($category) {
      $categories = $this->settings->getSetting('category');
      foreach ($categories as $value => $name) {
        if($name == $category) {
          return $value;
        }
      }
      return null;
    }

    private function _marshalStaffType($staffType){
      $staff_type = $this->settings->getSetting('staff_type');
      foreach ($staff_type as $value => $name) {
        if($name == $staffType) {
          return $value;
        }
      }
      return null;
    }

    public function importStudentData () {
      	$fromId = $this->input->post('from_id');
      	$toId = $this->input->post('to_id');
      	$tableName = $this->input->post('db_table');

      	if(!$this->db->table_exists($tableName)) {
      		$this->session->set_flashdata('flashError', 'Table: <b>'.$tableName.'</b> does not exist.');
    		redirect('csv/student');
      	} 

      // echo "<pre>"; print_r($this->input->post()); die();
      	$found = 0;
      	for ($id = $fromId; $id <= $toId; $id++) {
	        $rawData = $this->db->select('*')->from($tableName)->where('id',$id)->get()->row();
	        // echo "<pre>"; print_r($rawData);
	        if (empty($rawData)) continue;

          $check_std_data_exist = $this->Student_Model->check_student_data_exist($rawData->s_admission_no,$_POST['unique_by']);
          if($check_std_data_exist) continue;
	        $found = 1;
	        $result = new stdClass();
    			$result->f_userid = '';
    			$result->m_userid = '';
    			$result->f_source_from = '';
    			$result->m_source_from = '';

    			//Student Data
    			$result->acad_year = $this->currentYearId;
    			$result->admission_acad_year = ($rawData->s_admission_acad_year == '')?null:$this->yearIds[$rawData->s_admission_acad_year];
    			$result->acad_year_id = $this->currentYearId;
    			$result->cls_admitted_name = ($rawData->s_class_admitted_to == '')?null:$rawData->s_class_admitted_to;
    			$result->student_firstname = ucwords(strtolower($rawData->s_first_name));
    			$result->student_lastname = ($rawData->s_last_name == '')?null:ucwords(strtolower($rawData->s_last_name));
    			$result->s_email = $rawData->s_email;
    			$result->contact_no = ($rawData->s_contact_no == '')?null:$rawData->s_contact_no;
    			$result->student_dob = ($rawData->s_dob == '')?null:date('Y-m-d', strtotime($rawData->s_dob));
    			$result->birth_taluk = ($rawData->s_birth_taluk == '')?null:$rawData->s_birth_taluk;
    			$result->birth_district = ($rawData->s_birth_district == '')?null:$rawData->s_birth_district;
    			$result->nationality = ($rawData->s_nationality == '')?'Indian':$rawData->s_nationality;
    			$result->gender = ($rawData->s_gender == '')?'':$rawData->s_gender;
    			$result->religion = ($rawData->s_religion == '')?null:$rawData->s_religion;
    			$result->category = ($rawData->s_category == '')?null:$this->__marshalCategory($rawData->s_category);
    			$result->caste = ($rawData->s_caste == '')?null:$rawData->s_caste;
          $result->std_aadhar = ($rawData->s_aadhar_no == '')?null:$rawData->s_aadhar_no;
    			$result->mother_tongue = ($rawData->s_mother_tongue == '')?null:$rawData->s_mother_tongue;
    			$result->student_doj = ($rawData->s_date_of_joining == '')?null:date('Y-m-d', strtotime($rawData->s_date_of_joining));
    			if($rawData->s_admission_no != '') {
            $adm = $this->db->select('id')->from('student_admission')->where('admission_no',$rawData->s_admission_no)->get()->row();
            if(empty($adm)) {
    				  $result->admission_no = $rawData->s_admission_no;
            }
    			}
          $result->sts_number = ($rawData->s_sts_number == '')?null:$rawData->s_sts_number;
          $result->enrollment_number = ($rawData->s_enrollment_number == '')?null:$rawData->s_enrollment_number;
          $result->semester = ($rawData->s_semester == '')?null:$rawData->s_semester;
    			$result->rteid = $this->_marshalRTE($rawData->s_is_rte);
    			$result->board = $this->_marshalBoard($rawData->s_board);
    			$result->boardingid = $this->_marshalBoarding($rawData->s_boarding);
    			$result->medid = $this->_marshalMedium($rawData->s_medium);
    			$result->donor_name = null;
    			$result->classid = ($rawData->s_class == '')?null:$this->__marshalClassId($rawData->id, $rawData->s_class);
          $result->classsection = ($result->classid == null)?null:$this->_getSection($result->classid, $rawData->s_class_section);
          $result->combination = ($rawData->s_cources == null)?null: $rawData->s_cources;
    			$result->fee_mode = ($rawData->life_time_fee_mode == null)?null: $rawData->life_time_fee_mode;
    			$result->roll_num = ($rawData->s_roll_no == '')?null:$rawData->s_roll_no;
    			$result->house = ($rawData->s_student_house == '')?null:$rawData->s_student_house;
    			$result->newDonorItem = null;
    			$result->newHouseItem = null;

    			//Father Data
    			$result->f_email = $rawData->f_email;
    			$result->f_first_name = ($rawData->f_first_name == '')?'Father Of '.ucwords(strtolower($rawData->s_first_name)):ucwords(strtolower($rawData->f_first_name));
    			$result->f_last_name = ($rawData->f_last_name == '')?null:ucwords(strtolower($rawData->f_last_name));
    			// $result->f_qualification = ($rawData->f_qualification == '')?null:$rawData->f_qualification;
    			// $result->f_occupation = ($rawData->f_occupation == '')?null:$rawData->f_occupation;
    			// $result->f_company = ($rawData->f_company == '')?null:$rawData->f_company;
    			// $result->f_annual_income = ($rawData->f_annual_income == '')?null:$rawData->f_annual_income;
    			// $result->f_mobile_no = ($rawData->f_mobile_no == '')?null:$rawData->f_mobile_no;
          // $result->f_aadhar = ($rawData->f_aadhar_no == '')?null:$rawData->f_aadhar_no;
    			// $result->f_mother_tongue = ($rawData->f_mother_tongue == '')?null:$rawData->f_mother_tongue;

    			// $result->f_blood_group = ($rawData->f_blood_group == '')?null:$rawData->f_blood_group;
    			// $result->f_dob = ($rawData->f_dob == '')?null:$rawData->f_dob;
    			// $result->f_employee_id = ($rawData->f_employee_id == '')?null:$rawData->f_employee_id;
    			// $result->f_office_landline_number = ($rawData->f_office_landline_number == '')?null:$rawData->f_office_landline_number;
    			// $result->f_alternate_email_id = ($rawData->f_alternate_email_id == '')?null:$rawData->f_alternate_email_id;
    			// $result->f_whatsapp_num = ($rawData->f_whatsapp_num == '')?null:$rawData->f_whatsapp_num;
    			// $result->f_bank_account_num = ($rawData->f_bank_account_num == '')?null:$rawData->f_bank_account_num;

    			//Mother Data
    			$result->m_email = $rawData->m_email;
    			$result->m_first_name  = ($rawData->m_first_name == '')?'Mother Of '.ucwords(strtolower($rawData->s_first_name)):ucwords(strtolower($rawData->m_first_name));
    			$result->m_last_name  = ($rawData->m_last_name == '')?null:ucwords(strtolower($rawData->m_last_name));
    			// $result->m_qualification  = ($rawData->m_qualification == '')?null:$rawData->m_qualification;
    			// $result->m_occupation  = ($rawData->m_occupation == '')?null:$rawData->m_occupation;
    			// $result->m_company = ($rawData->m_company == '')?null:$rawData->m_company;
    			// $result->m_annual_income = ($rawData->m_annual_income == '')?null:$rawData->m_annual_income;
    			// $result->m_mobile_no = ($rawData->m_mobile_no == '')?null:$rawData->m_mobile_no;
    			// $result->m_aadhar = ($rawData->m_aadhar_no == '')?null:$rawData->m_aadhar_no;
          // $result->m_mother_tongue = ($rawData->m_mother_tongue == '')?null:$rawData->m_mother_tongue;

          $columns = [
            'f_qualification','f_occupation','f_company','f_annual_income','f_mobile_no','f_aadhar','f_mother_tongue',
            'f_blood_group','f_dob','f_employee_id','f_office_landline_number','f_alternate_email_id','f_whatsapp_num',
            'f_bank_account_num','m_qualification','m_occupation','m_company','m_annual_income','m_mobile_no','m_aadhar','m_mother_tongue','m_blood_group','m_dob','m_employee_id','m_office_landline_number','m_alternate_email_id','m_whatsapp_num','m_bank_account_num','custom1','custom2','custom3','custom4','custom5','custom6', 'custom7','nick_name', 'student_indian_visa_number', 'student_indian_visa_expiry_date', 'identification_mark1', 'identification_mark2', 'sibling1_name', 'sibling1_occupation', 'sibling1_mobile_num', 'sibling2_name', 'sibling2_occupation', 'sibling2_mobile_num', 
            'student_whatsapp_num', 'is_single_child', 'is_minority', 'current_nearest_location', 
            'passport_number', 'passport_issued_place', 'passport_validity', 
            'parents_marriage_anniversary', 'sibling3_name', 'sibling3_occupation', 
            'sibling3_mobile_num', 'point_of_contact', 'student_living_with', 'last_tc_num', 
            'last_hallticket_num', 'hall_ticket_num'
        ];
        

          // $result->m_blood_group = ($rawData->m_blood_group == '')?null:$rawData->m_blood_group;
    			// $result->m_dob = ($rawData->m_dob == '')?null:$rawData->m_dob;
    			// $result->m_employee_id = ($rawData->m_employee_id == '')?null:$rawData->m_employee_id;
    			// $result->m_office_landline_number = ($rawData->m_office_landline_number == '')?null:$rawData->m_office_landline_number;
    			// $result->m_alternate_email_id = ($rawData->m_alternate_email_id == '')?null:$rawData->m_alternate_email_id;
    			// $result->m_whatsapp_num = ($rawData->m_whatsapp_num == '')?null:$rawData->m_whatsapp_num;
    			// $result->m_bank_account_num = ($rawData->m_bank_account_num == '')?null:$rawData->m_bank_account_num;

          // $result->custom1 = (!isset($rawData->custom1) || $rawData->custom1 == '')?null:$rawData->custom1;
          // $result->custom2 = (!isset($rawData->custom2) || $rawData->custom2 == '')?null:$rawData->custom2;
          // $result->custom3 = (!isset($rawData->custom3) || $rawData->custom3 == '')?null:$rawData->custom3;
          // $result->custom4 = (!isset($rawData->custom4) || $rawData->custom4 == '')?null:$rawData->custom4;
          // $result->custom5 = (!isset($rawData->custom5) || $rawData->custom5 == '')?null:$rawData->custom5;
          // $result->custom6 = (!isset($rawData->custom6) || $rawData->custom6 == '')?null:$rawData->custom6;
          // $result->custom7 = (!isset($rawData->custom7) || $rawData->custom7 == '')?null:$rawData->custom7;

         
        foreach ($columns as $column) {
          $result->$column = (isset($rawData->$column) && $rawData->$column !== '') ? $rawData->$column : null;
        }
          // $result->nick_name = ($rawData->nick_name == '')?null:$rawData->nick_name;
          // $result->student_indian_visa_number = ($rawData->student_indian_visa_number == '')?null:$rawData->student_indian_visa_number;
          // $result->student_indian_visa_expiry_date = ($rawData->student_indian_visa_expiry_date == '')?null:$rawData->student_indian_visa_expiry_date;
          // $result->identification_mark1 = ($rawData->identification_mark1 == '')?null:$rawData->identification_mark1;
          // $result->identification_mark2 = ($rawData->identification_mark2 == '')?null:$rawData->identification_mark2;
          // $result->sibling1_name = ($rawData->sibling1_name == '')?null:$rawData->sibling1_name;
          // $result->sibling1_occupation = ($rawData->sibling1_occupation == '')?null:$rawData->sibling1_occupation;
          // $result->sibling1_mobile_num = ($rawData->sibling1_mobile_num == '')?null:$rawData->sibling1_mobile_num;
          // $result->sibling2_name = ($rawData->sibling2_name == '')?null:$rawData->sibling2_name;
          // $result->sibling2_occupation = ($rawData->sibling2_occupation == '')?null:$rawData->sibling2_occupation;
          // $result->sibling2_mobile_num = ($rawData->sibling2_mobile_num == '')?null:$rawData->sibling2_mobile_num;
          // $result->student_whatsapp_num = ($rawData->student_whatsapp_num == '')?null:$rawData->student_whatsapp_num;
          // $result->is_single_child = ($rawData->is_single_child == '')?null:$rawData->is_single_child;
          // $result->is_minority = ($rawData->is_minority == '')?null:$rawData->is_minority;
          // $result->current_nearest_location = ($rawData->current_nearest_location == '')?null:$rawData->current_nearest_location;
          // $result->passport_number = ($rawData->passport_number == '')?null:$rawData->passport_number;
          // $result->passport_issued_place = ($rawData->passport_issued_place == '')?null:$rawData->passport_issued_place;
          // $result->passport_validity = ($rawData->passport_validity == '')?null:$rawData->passport_validity;
          // $result->parents_marriage_anniversary = ($rawData->parents_marriage_anniversary == '')?null:$rawData->parents_marriage_anniversary;
          // $result->sibling3_name = ($rawData->sibling3_name == '')?null:$rawData->sibling3_name;
          // $result->sibling3_occupation = ($rawData->sibling3_occupation == '')?null:$rawData->sibling3_occupation;
          // $result->sibling3_mobile_num = ($rawData->sibling3_mobile_num == '')?null:$rawData->sibling3_mobile_num;
          // $result->point_of_contact = ($rawData->point_of_contact == '')?null:$rawData->point_of_contact;
          // $result->student_living_with = ($rawData->student_living_with == '')?null:$rawData->student_living_with;
          // $result->last_tc_num = ($rawData->last_tc_num == '')?null:$rawData->last_tc_num;
          // $result->last_hallticket_num = ($rawData->last_hallticket_num == '')?null:$rawData->last_hallticket_num;
          // $result->hall_ticket_num = ($rawData->hall_ticket_num == '')?null:$rawData->hall_ticket_num;

          $result->extracurricular_activities = '';
          

    			$result->add_status = 2;


          //Guardian 
          if (!empty($rawData->g_name)) {

            $result->g_first_name  = ($rawData->g_name == '')?'Mother Of '.ucwords(strtolower($rawData->s_first_name)):ucwords(strtolower($rawData->g_name));
            $result->g_occupation  = ($rawData->g_occupation == '')?null:$rawData->g_occupation;
            $result->g_mobile_no = ($rawData->g_mobile_no == '')?null:$rawData->g_mobile_no;
          }
    			$result = $this->__submitStudent($result);
    			if($result == 0) {
    				echo "Problem In Stduent Raw Id: ".$rawData->id;
    			}
      	}
      	if($found) {
      		$this->session->set_flashdata('flashSuccess', 'Imported Successfully from '.$fromId.' to '.$toId);
      	} else {
      		$this->session->set_flashdata('flashError', 'No Data from <b>'.$fromId.'</b> to <b>'.$toId.'</b>');
      	}
  	   redirect('csv/student');
    }

    private function _prepareStudentInput(&$input) {
      $return_data = [];
      foreach ($input as $k => $v) {
        $start_key = substr($k, 0, 2);
        if ($start_key == 'f_') {
          $key = str_replace("f_","",$k);
          $return_data['father'][$key] = $v;
        }
        elseif ($start_key == 'm_') {
          $key = str_replace("m_","",$k);
          $return_data['mother'][$key] = $v;
        }else if($start_key == 'g_'){
          $key = str_replace("m_","",$k);
          $return_data['guardian'][$key] = $v;
        }
        else {
          $return_data['student'][$k] = $v;
        }
      }

      //echo '<pre>';print_r($return_data);

      return $return_data;

    }    

    public function s3FileUpload($file) {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'profile');
    }

    private function _generateAdmissionNo($config_admission, $params = []) {

      $admission_number = '';

      switch ($config_admission->admission_generation_algo) {
        case 'NPSRNR':
          # code...
          break;
        case 'NH': {
            // Greater than 2 takes all the class from 1st to so on.
            if ($params['classid']->type >= 2) 
              $admission_number = $config_admission->infix.$params['number'];
            else 
              $admission_number = 'P'.$params['number'];            
          break;
        }
        case 'NEXTELEMENT': {
          $admission_number = $params['year_of_joining'].$config_admission->infix.$params['number'];
          break;
        }
        case 'YASHASVI': {
          $admission_number = $config_admission->infix.$params['number'];
          break;
        }
        case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
          switch ($params['classid']->type) {
            case 1:
              $classType = 'N';
              break;
            case 2:
              $classType = 'P';
              break;
            case 3:
              $classType = 'H';
              break;
          }
          $admission_number =$config_admission->infix.$classType.$params['number'];
          break;
      }

      return $admission_number;

    }

    private function __submitStudent($input_form) {
       
      $grouped_input = $this->_prepareStudentInput($input_form);
          //admission_no
      if(!isset($grouped_input['student']['admission_no'])) {
        $config_admission_number = $this->settings->getSetting('admission_number');
        // echo "<pre>"; print_r($config_admission_number); die();
        $lastRecord = $this->Student_Model->getLastStudentid(); 
        if (!$lastRecord) {
          $lastRecordId = $config_admission_number->index_offset + 1;
        } else {
          $lastRecordId = $config_admission_number->index_offset + ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0".$config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $grouped_input['student']['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($grouped_input['student']['classid']);
        $grouped_input['student']['admission_no'] = $this->_generateAdmissionNo($config_admission_number,$params);
      }

      //$this->db->trans_off();
      $this->db->trans_begin();

      $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'],null,$grouped_input['father']['userid']);
      if($student_uid == 0) {
        $this->db->trans_rollback();
        return 0;
      } else {

       $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'],$student_uid['stdAdmId'],'Father',null, $grouped_input['student']['student_firstname']);
      if (!$father_uid) {
        $this->db->trans_rollback();
        return 0;
      }

      
       $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'],$student_uid['stdAdmId'],'Mother',null, $grouped_input['student']['student_firstname'], $grouped_input['father']['mobile_no']);

        if (!$mother_uid) {
          $this->db->trans_rollback();
          return 0;
        }
        if (!empty($grouped_input['guardian'])) {
          $guardianuid = $this->Student_Model->addGuardianInfo($grouped_input['guardian'], $student_uid['stdAdmId'], 'Guardian');

          if (!$guardianuid) {
            $this->db->trans_rollback();
            return 0;
          }
        }
        if ($this->db->trans_status()) {
          $this->db->trans_commit();
        } else {
          $this->db->trans_rollback();
          return 0;
        }
        return 1;
      }
    }

    public function importStaffData () {
        $fromId = $this->input->post('from_id');
        $toId = $this->input->post('to_id');
        $tableName = $this->input->post('db_table');

        if(!$this->db->table_exists($tableName)) {
          $this->session->set_flashdata('flashError', 'Table: <b>'.$tableName.'</b> does not exist.');
          redirect('csv/staff');
        } 

      // echo "<pre>"; print_r($this->input->post()); die();
        $found = 0;
        for ($id = $fromId; $id <= $toId; $id++) {
          $rawData = $this->db->select('*')->from($tableName)->where('id',$id)->get()->row();
          // echo "<pre>"; print_r($rawData);
          if (empty($rawData)) continue;
          $found = 1;
          $result = new stdClass();
         
          //Student Data
          $result->first_name = ($rawData->first_name == '')?null:$rawData->first_name;
          $result->last_name = ($rawData->last_name == '')?null:$rawData->last_name;
          $result->short_name = ($rawData->short_name == '')?null:$rawData->short_name;
          $result->dob = ($rawData->dob == '')?null:$rawData->dob;
          $result->father_first_name = ($rawData->father_first_name == '')?null:$rawData->father_first_name;
          $result->mother_first_name = ($rawData->mother_first_name == '')?null:$rawData->mother_first_name;
          $result->gender = ($rawData->gender == '')?null:$rawData->gender;
          $result->contact_number = ($rawData->contact_number == '')?null:$rawData->contact_number;
          $result->marital_status = ($rawData->marital_status == '')?null:$rawData->marital_status;
          $result->email = ($rawData->mail_id == '')?null:$rawData->mail_id;
          $result->joining_date = ($rawData->joining_date == '')?null:$rawData->joining_date;
          $result->adhaar_number = ($rawData->adhaar_number == '')?null:$rawData->adhaar_number;
          $result->spouse_name = ($rawData->spouse_name == '')?null:$rawData->spouse_name;
          $result->nationality = ($rawData->nationality == '')?null:$rawData->nationality;
          $result->alternative_no = ($rawData->alternative_no == '')?null:$rawData->alternative_no;
          $result->spouse_contact_no = ($rawData->spouse_contact_no == '')?null:$rawData->spouse_contact_no;
          $result->emergency_info = ($rawData->emergency_info == '')?null:$rawData->emergency_info;
          $result->status = ($rawData->employee_status == 'active')?'2':'4';
          $result->staff_type = $this->_marshalStaffType($rawData->staff_type);
          $result->department_name = ($rawData->department_name == '')?null:$rawData->department_name;
          $result->qualification_name = ($rawData->qualification_name == '')?null:$rawData->qualification_name;
          $result->experience = ($rawData->experience == '')?null:$rawData->experience;
          $result->education_exp = ($rawData->education_exp == '')?null:$rawData->education_exp;
          $result->specialization = ($rawData->specialization == '')?null:$rawData->specialization;
          $result->blood_group = ($rawData->blood_group == '')?null:$rawData->blood_group;
          $result->employee_code = ($rawData->emplyee_code == '')?null:$rawData->emplyee_code;
          $result->designation_name = ($rawData->designation == '')?null:$rawData->designation;
          $result->religion = ($rawData->religion == '')?null:$rawData->religion;
          $result->boarding = ($rawData->boarding == '')?null:$rawData->boarding;
          $result->last_working_day = ($rawData->last_working_day == '')?null:$rawData->last_working_day;
          $result->father_last_name = ($rawData->father_last_name == '')?null:$rawData->father_last_name;
          $result->mother_last_name = ($rawData->mother_last_name == '')?null:$rawData->mother_last_name;
          $result->voter_id = ($rawData->voter_id == '')?null:$rawData->voter_id;
          $result->height = ($rawData->height == '')?null:$rawData->height;
          $result->weight = ($rawData->weight == '')?null:$rawData->weight;
          $result->allergies = ($rawData->allergies == '')?null:$rawData->allergies;
          $result->medical_issues = ($rawData->medical_issues == '')?null:$rawData->medical_issues;
          $result->identification_mark = ($rawData->indentification_mark == '')?null:$rawData->indentification_mark;
          $result->person_with_disability = ($rawData->person_with_disability == '')?null:$rawData->person_with_disability;
          $result->sports = ($rawData->sports == '')?null:$rawData->sports;
          $result->dramatics = ($rawData->dramatics == '')?null:$rawData->dramatics;
          $result->literary_interests = ($rawData->literary_interests == '')?null:$rawData->literary_interests;
          $result->music = ($rawData->music == '')?null:$rawData->music;
          $result->dance = ($rawData->dance == '')?null:$rawData->dance;
          // $result->landline_number = ($rawData->landline_number == '')?null:$rawData->landline_number;
          // $result->mobile_number_2 = ($rawData->mobile_number_2 == '')?null:$rawData->mobile_number_2;
          $result->passport_number = ($rawData->passport_number == '')?null:$rawData->passport_number;
          $result->passport_place_of_issue = ($rawData->place_of_issue == '')?null:$rawData->place_of_issue;
          $result->passport_date_of_issue = ($rawData->date_of_issue == '')?null:$rawData->date_of_issue;
          $result->passport_expiry_date = ($rawData->expiry_date == '')?null:$rawData->expiry_date;
          $result->visa_details = ($rawData->vsia_details == '')?null:$rawData->vsia_details;

          $result = $this->__submitStaff($result);
          if($result == 0) {
            echo "Problem In Stduent Raw Id: ".$rawData->id;
          }
        }
        if($found) {
          $this->session->set_flashdata('flashSuccess', 'Imported Successfully from '.$fromId.' to '.$toId);
        } else {
          $this->session->set_flashdata('flashError', 'No Data from <b>'.$fromId.'</b> to <b>'.$toId.'</b>');
        }
       redirect('csv/staff');
    }

     private function __submitStaff($input_form) {
      $input_form = (array)$input_form;
      $addStaff = $this->_prepareStaffData_import($input_form);
      $this->db->insert('staff_master', $addStaff);

      if ($this->db->affected_rows() != 1) {
        return 0;
      } else {
        $suid = $this->db->insert_id();
        $firstName = $input_form['first_name'];
        $lastName = $input_form['last_name'];
        $pUsername = $this->generateUsername_import($firstName, $lastName);
        $staff_userId = $this->_insertIntoUsers_import($pUsername, $input_form['email']);

        if (!$staff_userId) {
          return 0;
        }
        //Avatar Type for Staff is 4
        $avatarId = $this->_insertIntoAvatar_import($staff_userId, '4', $suid, $input_form['first_name'] . ' ' . $input_form['last_name']);
        if (!$avatarId) {
          return 0;
        }
        return $suid;
      }
    }

  private function _prepareStaffData_import($input){
    if (!empty($input['dob'])) {
      $dob =  date("Y-m-d", strtotime($input['dob']));
    } else {
      $dob = null;
    }
    if (!empty($input['joining_date'])) {
      $doj =  date("Y-m-d", strtotime($input['joining_date']));
    } else {
      $doj = null;
    }

    if (!empty($input['passport_date_of_issue'])) {
      $passport_date_of_issue =  date("Y-m-d", strtotime($input['passport_date_of_issue']));
    } else {
      $passport_date_of_issue = null;
    }
    if (!empty($input['passport_expiry_date'])) {
      $passport_expiry_date =  date("Y-m-d", strtotime($input['passport_expiry_date']));
    } else {
      $passport_expiry_date = null;
    }

    if (!empty($input['last_working_day'])) {
      $last_working_day =  date("Y-m-d", strtotime($input['last_working_day']));
    } else {
      $last_working_day = null;
    }

   return array(
      'nationality' => ($input['nationality'] == '') ? null : $input['nationality'],
      'staff_type' => ($input['staff_type'] == '') ? '' : $input['staff_type'],
      'short_name' => ($input['short_name'] == '') ? null : $input['short_name'],
      'department' => ($input['department_name'] == '') ? null : $input['department_name'],
      'designation' => ($input['designation_name'] == '') ? null : $input['designation_name'],
      'first_name' => $input['first_name'],
      'last_name' => ($input['last_name'] == '') ? null : $input['last_name'],
      'father_first_name' => ($input['father_first_name'] == '') ? null : $input['father_first_name'],
      'father_last_name' => ($input['father_last_name'] == '') ? null : $input['father_last_name'],
      'mother_first_name' => ($input['mother_first_name'] == '') ? null : $input['mother_first_name'],
      'mother_last_name' => ($input['mother_last_name'] == '') ? null : $input['mother_last_name'],
      'marital_status' => ($input['marital_status'] == '') ? '' : $input['marital_status'],
      'dob' => $dob,
      'gender' => ($input['gender'] == '') ? '' : $input['gender'],
      'contact_number' => ($input['contact_number'] == '') ? null : $input['contact_number'],
      'spouse_name' => ($input['spouse_name'] == '') ? null : $input['spouse_name'],
      'alternative_number' => ($input['alternative_no'] == '') ? null : $input['alternative_no'],
      'aadhar_number' => ($input['adhaar_number'] == '') ? null : $input['adhaar_number'],
      'qualification' => ($input['qualification_name'] == '') ? null : $input['qualification_name'],
      'subject_specialization' => ($input['specialization'] == '') ? null : $input['specialization'],
      'total_education_experience' => ($input['education_exp'] == '') ? null : $input['education_exp'],
      'total_experience' => ($input['experience'] == '') ? null : $input['experience'],
      'status' => $input['status'],
      'spouse_contact_no' => ($input['spouse_contact_no'] == '') ? null : $input['spouse_contact_no'],
      'emergency_info' => ($input['emergency_info'] == '') ? null : $input['emergency_info'],
      'employee_code' => ($input['employee_code'] == '') ? null : $input['employee_code'],
      'blood_group' => ($input['blood_group'] == '') ? null : $input['blood_group'],
      'boarding' => ($input['boarding'] == '') ? null : $input['boarding'],
      'last_working_day' => $last_working_day,
      'voter_id' => ($input['voter_id'] == '') ? null : $input['voter_id'],
      'height' => ($input['height'] == '') ? null : $input['height'],
      'weight' => ($input['height'] == '') ? null : $input['weight'],
      'allergies' => ($input['height'] == '') ? null : $input['allergies'],
      'medical_issues' => ($input['height'] == '') ? null : $input['medical_issues'],
      'identification_mark' => ($input['height'] == '') ? null : $input['identification_mark'],
      'person_with_disability' => ($input['height'] == '') ? null : $input['person_with_disability'],
      'sports' => ($input['height'] == '') ? null : $input['sports'],
      'dramatics' => ($input['dramatics'] == '') ? null : $input['dramatics'],
      'literary_interests' => ($input['literary_interests'] == '') ? null : $input['literary_interests'],
      'music' => ($input['music'] == '') ? null : $input['music'],
      'dance' => ($input['dance'] == '') ? null : $input['dance'],
      'landline_number' => ($input['landline_number'] == '') ? null : $input['landline_number'],
      'mobile_number_2' => ($input['mobile_number_2'] == '') ? null : $input['mobile_number_2'],
      'passport_number' => ($input['passport_number'] == '') ? null : $input['passport_number'],
      'passport_place_of_issue' => ($input['passport_place_of_issue'] == '') ? null : $input['passport_place_of_issue'],
      'passport_date_of_issue' => $passport_date_of_issue,
      'passport_expiry_date' => $passport_expiry_date,
      'joining_date' => $doj,
      'last_modified_by' =>  $this->authorization->getAvatarId()
    );
  }

  private function generateUsername_import($firstName, $lastName){
    $names = $this->db->select('username')->get('users')->result();
    $users = array();
    if (!empty($names)) {
      foreach ($names as $val) {
        array_push($users, $val->username);
      }
    }
    $firstName = preg_replace('/\s+/', '', $firstName);
    $lastName = preg_replace('/\s+/', '', $lastName);
    $firstName = preg_replace('/[^A-Za-z0-9]/', '', $firstName);
    $lastName = preg_replace('/[^A-Za-z0-9]/', '', $lastName);
    $name = '';
    $fullName = $firstName . $lastName;
    if ($firstName == '' && $lastName == '') {
      $username = $this->first_import($users);
    } else if ($firstName == '') {
      if (!in_array($lastName, $users)) {
        $username = substr($lastName, 0, 6);
      } else {
        $username = $this->second_import($lastName, $users);
      }
    } else if ($lastName == '') {
      if (!in_array($firstName, $users)) {
        $username = substr($firstName, 0, 6);
      } else {
        $username = $this->second_import($firstName, $users);
      }
    } else {
      $username = $this->third_import($firstName, $lastName, $users);
    }
    return $username;
  }

  private function first_import($users)
  {
    do {
      $username = $this->generateRandomCode_import(4, 0); //generating random string
      $username .= $this->generateRandomCode_import(2, 1); //generating random number
    } while (in_array($username, $users));

    return $username;
  }

  private function second_import($name, $users)
  {
    $name = substr($name, 0, 6);
    if (!in_array($name, $users))
      return $name;
    $len = strlen($name);
    $random = '';
    $num = 6 - $len;
    do {
      $times = pow(10, $num);
      for ($i = 0; $i < $times; $i++) {
        $random = $this->generateRandomCode_import($num, 1);
      }
      $num++;
    } while (in_array($name . $random, $users));
    return $name . $random;
  }

  private function third_import($firstName, $lastName, $users)
  {
    $username = substr($firstName, 0, 4) . substr($lastName, 0, 2);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 2) . substr($lastName, 0, 4);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 4) . substr($firstName, 0, 2);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 2) . substr($firstName, 0, 4);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 3) . substr($lastName, 0, 3);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 3) . substr($firstName, 0, 3);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 5) . substr($lastName, 0, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 1) . substr($lastName, 0, 5);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 5) . substr($firstName, 0, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 1) . substr($firstName, 0, 5);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 6);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 6);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 4) . substr($lastName, 0, 2) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 4) . substr($firstName, 0, 2) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 3) . substr($lastName, 0, 3) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 3) . substr($firstName, 0, 3) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 5) . substr($lastName, 0, 1) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 5) . substr($firstName, 0, 1) . $this->generateRandomCode_import(2, 1);
    if (!in_array($username, $users)) {
      return $username;
    } else {
      $username = substr($lastName, 0, 3) . substr($firstName, 0, 2) . $this->generateRandomCode_import(3, 1);
    }
    return $username;
  }

  private function generateRandomCode_import($length = 6, $isNumber = 1){
    if ($isNumber)
      return substr(str_shuffle(str_repeat($x = '1234567890', ceil($length / strlen($x)))), 1, $length);
    else
      return substr(str_shuffle(str_repeat($x = 'abcdefghijklmnopqrstuvwxyz', ceil($length / strlen($x)))), 1, $length);
  }

  private function _insertIntoUsers_import($username, $email){
    $ci = &get_instance();
    //Remove all space (left, right and in-between); convert to lower case
    $username = strtolower(str_replace(' ', '', $username));

    //username check for exists
    $flag = true;
    do {
      $this->db->where('username', $username);
      $query = $this->db->get('users');
      if ($query->num_rows() > 0) {
        $username = $username . rand(10, 99);
      } else {
        $flag = false;
      }
    } while ($flag);

    $userId = $ci->ion_auth->register_1($username, 'welcome123', $email);
    return $userId;
  }

  private function _insertIntoAvatar_import($student_userIdu, $avatar_type, $insert_id, $friendly_name){
    $ci = &get_instance();
    $param = array(
      'user_id' => $student_userIdu,
      'avatar_type' => $avatar_type,
      'stakeholder_id' => $insert_id,
      'friendly_name' => $friendly_name,
      'last_modified_by' => $this->authorization->getAvatarId()
    );

    return $ci->db->insert('avatar', $param);
  }

}