<ul class="breadcrumb">
    <li><a href="<?php echo base_url('avatars');?>">Dashboard</a></li>
    <li><a href="<?php echo base_url('student/student_menu');?>">Academics</a></li>
    <li class="active">Add Observation</li>
</ul>

<div class="col-md-12">
  <form enctype="multipart/form-data" method="post" class="form-horizontal" id="form_input">
  <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
        <h3 class="card-title panel_title_new_style"><strong>Add Observation</strong></h3>
    </div>     
    <div class="card-body pt-0">
      <div class="row">
        <div class="col-xs-12 mb-2 p-0">
        <label for="section_id" style="font-size: 14px;">Section <font color="red">*</font></label>
            <div>
              <select id="section_id" name="section_id" class="form-control input-md" required>
                  <option value=""><?php echo 'Select Section ' ?></option>
                  <?php foreach ($sectionList as $value) { ?>
                      <option value="<?php echo $value->id; ?>"><?php echo $value->class_name; ?></option>
                  <?php }?>
              </select>
            </div>
        </div>
        <div class="col-xs-12 mb-2 p-0">
        <label for="student_id">Student <font color="red">*</font></label>
            <div>
              <select title="Select Student" name="student_name[]" id="student_id" class="form-control select" multiple required>
              </select>
            </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
        <label for="category_id">Category <font color="red">*</font></label>
            <div>
            <select id="category_id" name="category_id" class="form-control input-md" onchange="fill_remarks(this.value)"  required>
                <option value=""><?php echo 'Select Category ' ?></option>
                <?php foreach ($category_types as $value) { ?>
                    <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
                <?php }?>
            </select>
            </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
        <label for="observation_date">Observed Date & Time</label>
            <div>
            <div class="input-group date" id="ob_date_picker"> 
            <input style="flex: none;" autocomplete="off" type="datetime-local" class="form-control" id="observation_date" name="date"  required>
            </div>
            </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
        <label for="staff_capacity_id">Observed as <font color="red">*</font></label>
            <div>
              <select id="staff_capacity_id" name="staff_capacity_id" class="form-control input-md " required>
                  <option value=""><?php echo 'Select type ' ?></option>
                  <?php foreach ($capacity_types as $value) { ?>
                      <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
                  <?php }?>
              </select>
            </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
          <label for="predefined_observation">Predefined observation <font color="red">*</font></label>
          <div>
            <select title="Select predefined observation" name="predefined_observation" id="predefined_observation" class="form-control" onchange="fill_observation_remarks(this.value)" >
            </select>
          </div>
        </div>


        <div class="col-xs-12 mb-2 p-0">
        <label for="observation">Observations <font color="red">*</font></label>
            <div>
              <textarea name="observation" id="observation" class="form-control input-md" rows="4"  required></textarea>
            </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
        <label for="action_taken">Action Taken</label>
            <div>
              <textarea name="action_taken" id="action_taken" class="form-control input-md" rows="4"></textarea>
            </div>
        </div>
        <div class="form-group" >
                  <label >File upload</label>
                  <div >
                    <input name="file_input" id="file_input" class="form-control input-md" style="padding:3px" type="file">
                  </div>
                </div> 

        <div class="col-xs-12 mb-2 p-0 mt-4">
        <div style="text-align: center;">
          <button type="submit" id="get" style="width: 95%;border-radius: 1.5rem;" class="btn btn-md btn-primary" onclick="add_single_student_observation()" >Add Observation
          </button>
          </div>
        </div>
      </div>     
    </div>
  </div>
</form>
</div>

  <a href="<?php echo site_url('student_observation/observation/my_observations');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>


<script type="text/javascript">

let btn_status;
$("#section_id").change(function(){
    var section_id = $("#section_id").val();
    $.post("<?php echo site_url('student_observation/observation/get_student');?>",{section_id:section_id},function(data){
      var resultData=$.parseJSON(data);
      var output1='';
      var stdName = resultData.stdname;

       for (var i=0,j=stdName.length; i < j; i++) {
         output1+='<option value="'+stdName[i].id+'">'+stdName[i].std_name+' </option>';
       }
        $("#student_id").html(output1);
        $("#student_id").selectpicker('refresh');
        
    });
});
// var currentDate = new Date();
// var formattedDate = currentDate.toISOString().slice(0, 16);
// document.getElementById("observation_date").setAttribute("max", formattedDate);
window.addEventListener('load', () => {
  var now = new Date();
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset());

  now.setMilliseconds(null)
  now.setSeconds(null)

  document.getElementById('observation_date').value = now.toISOString().slice(0, -1);
});
function add_single_student_observation(){
  var $form = $('#form_input');

  btn_status=document.getElementById("get");
  btn_status.disabled = true;
  btn_status.textContent="Please wait...";
  var $form = $('#form_input');
        if (!$form.parsley().validate()){
          return false;
        }
        const form = $('#form_input')[0];
const formData = new FormData(form);
$.ajax({
    url: '<?php echo site_url('student_observation/observation/add_single_student_observation'); ?>',
    type: 'post',
    data:formData,
    processData: false,
    contentType: false,
    cache: false,
    success: function(data) {
        parsed_data = $.parseJSON(data);
        if(parsed_data){
          Swal.fire({
              // position: "top-end",
              icon: "success",
              title: "New Observation has been added",
              showConfirmButton: false,
              timer: 1500
            });
            btn_status.disabled = true;
            btn_status.textContent="Add Observation";
            window.location.replace("<?php echo base_url('student_observation/observation/my_observations');?>");
        }
        else{
        console.log(err);
        }
        
    
    
    },
    error: function (err) {
        console.log(err);
    }
});
}


function fill_remarks(id){
  console.log(id);
  $.ajax({
    url: '<?php echo site_url('student_observation/observation/fill_remarks'); ?>',
    type: 'post',
    data:{id},
    success: function(data) {
        parsed_data = $.parseJSON(data);
        var output1=' <option value="" selected>Select observation</option>';
        parsed_data.forEach(element => {
          output1+='<option value="'+element.id+'">'+element.default_remarks+' </option>';
        });
        $("#predefined_observation").html(output1);
        $('#predefined_observation').selectpicker('refresh');
    },
    error: function (err) {
        console.log(err);
    }
  });
}
function fill_observation_remarks(id){
  // Get the selected option's text
  var selectedText = $("#predefined_observation option:selected").text();
  $('#observation').val(selectedText.trim());
}



</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>