<?php

/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 may 2025
 *
 * Description: Model for Payment Voucher Module. Entry point for Payment Voucher Module
 *
 * Requirements: PHP5 or above
 *
 */


class Payment_voucher_model extends CI_Model{
    private $yearId;
    public function __construct(){
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
    }

    function get_vendors() {
        return $this->db_readonly->select("id, vendor_name")->get('procurement_vendor_master')->result();
    }

    function get_vendor_wise_invoices($vendor_id) {
        return $this->db_readonly->select("pim.id, pim.invoice_number, pr.id as purchae_order_id, pr.request_number as purchase_order_number")
                ->from('procurement_invoice_master pim')
                ->join('procurement_requisition pr', 'pr.id = pim.purchase_order_id')
                ->where('pr.vendor_id', $vendor_id)
                // ->where("lower(invoice_status)", 'approved')
                // ->where("lower(payment_status) != ", 'paid')
                ->get()->result();

    }

    function get_invoices_details($invoices_id) {

        if(empty($invoices_id)) {
            return [];
        }

        $invoices = $this->db_readonly->select("pim.id as invoice_id, pim.invoice_number, 
                date_format(pim.invoice_date, '%d-%m-%Y') as invoice_date, pim.invoice_type, 
                pim.total_amount, 
                invItem.delivery_challan_type,
                ifnull(pim.sgst_amount, 0) as sgst_amount, 
                ifnull(pim.cgst_amount, 0) as cgst_amount, 
                ifnull(pim.igst_amount, 0) as igst_amount, 
                ifnull(pim.cess_amount, 0) as cess_amount, 
                ifnull(pim.gst_amount, 0) as gst_amount, 
                ifnull(pim.discount_amount, 0) as discount_amount, 
                ifnull(pim.invoice_remarks, '-') as narration, 
                sum(invItem.cgst_rate) as sum_cgst_rate, 
                sum(invItem.sgst_rate) as sum_sgst_rate, 
                sum(invItem.total_amount) as sum_total_amount")
            ->from('procurement_invoice_master pim')
            ->join('procurement_invoice_items invItem', 'invItem.invoice_master_id = pim.id')
            ->where("pim.voucher_created_flag !=", 2) 
            ->where_in('pim.id', $invoices_id)
            ->where('pim.invoice_status', 'Approved') // Create voucher only for approved invoice
            ->group_by('pim.id, pim.invoice_number, pim.invoice_date, pim.invoice_type, 
              pim.total_amount, pim.sgst_amount, pim.cgst_amount, pim.igst_amount, 
              pim.cess_amount, pim.gst_amount, pim.discount_amount, pim.invoice_remarks')
        ->get()->result();
        return $invoices;
    }

    function get_approvers() {
        $approvers= $this->settings->getSetting('procurement_vouchers_approvers');
        // echo '<pre>'; print_r($approvers); die();
        $aprObj= [];
        if($approvers && !empty($approvers)) {
            foreach($approvers as $key => $val) {
                $name= $this->db_readonly->select("sm.id as staff_id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as staff, upper('$key') as type, ifnull(sd.department, '-') as department")
                ->join('staff_departments sd', 'sd.id = sm.department', 'left')
                ->where('sm.id', $val)->get('staff_master sm')->row();
                if(!empty($name)) {
                    $obj= new stdClass();
                    $obj->staff_id= $name->staff_id;
                    $obj->name= $name->staff;
                    $obj->type= $name->type;
                    $obj->department= $name->department;

                    $aprObj[]= $obj;
                }
            }
        }

        return $aprObj;
    }

    function submit_voucher_form() {
        $input= $this->input->post();

        // echo '<pre>'; print_r($input); die();

        $insert_update_type= $input['insert_update_type']; 
        $voucher_master_id= $input['voucher_master_id']; 
        $voucher_type= $input['voucher_type']; 
        $vendor_id= $input['vendor_id']; 
        $voucher_date= $input['voucher_date'];
        $voucher_narration= $input['voucher_narration']; 

        $invoicesObj= $this->get_vendor_wise_invoices($vendor_id);
        $invoices_id= []; 
        if(!empty($invoicesObj)) {
            foreach($invoicesObj as $key => $val) {
                $invoices_id[]= $val->id;
            }
        }
        // echo '<pre>'; print_r($invoicesObj); die();

        $pa_number= $this->__get_pa_number();
        $invoice_details= $this->get_invoices_details($invoices_id);
        if(empty($invoice_details)) {
            return array(
                'status' => -1,
                'message' => "No invoices created for this vendor. Change the vendor to proceed further."
            );
        }
        $master_inert= array(
            'voucher_number' => $pa_number,
            'voucher_date' => date('Y-m-d', strtotime($voucher_date)),
            'voucher_type' => $voucher_type,
            'vendor_id' => $vendor_id,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s'),
            'remarks' => $voucher_narration
        );

        $this->db->trans_start();
            if($insert_update_type == 'Add') {
                $this->db->insert('procurement_voucher_master', $master_inert);
                $voucher_master_id= $this->db->insert_id();
            } else {
                unset($master_inert['voucher_number']);
                $this->db->where('id', $voucher_master_id)->update('procurement_voucher_master', $master_inert);
            }
            // // Insert invoice approvers
            if($insert_update_type == 'Add') {
                $approversStatus= $this->__add_approvers_in_paymentadvice_flow($voucher_master_id);
            }
        $this->db->trans_complete();
        $status= $this->db->trans_status();

        if(!$status) {
            $status= $this->db->trans_rollback();
            return array(
                'status' => 0,
                'message' => "Something went wrong",
                'voucher_master_id' => $voucher_master_id,
                'invoice_details' => $invoice_details
            );
        }
        return array(
                'status' => 1,
                'voucher_master_id' => $voucher_master_id,
                'invoice_details' => $invoice_details
            );
    }

    function save_invoices_details() {
         $input= $this->input->post();
         $voucher_master_id= $input['voucher_master_id'];
         $invoices_id= $input['invoicesIdsArr'];
         $tds_section= $input['tds_section'];
         $tds_amounts= $input['tds_amounts'];
         $net_invoice_amount= $input['net_invoice_amount'];
         $reference_net_invoice_amount= $input['reference_net_invoice_amount'];

        //  $invoice_details= $this->get_invoices_details($invoices_id);

        $payble= 0;
        $total= 0;
        if(!empty($invoices_id)) {
            $total_tds= 0;
            foreach($invoices_id as $key => $val) {
                
                $details_insert[]= array(
                    'voucher_id' => $voucher_master_id,
                    'invoice_id' => $val,
                    'tds_section' =>  $tds_section[$key],
                    'tds_amount' => $tds_amounts[$key],
                    'net_amount' => $net_invoice_amount[$key]
                );
                $payble += $net_invoice_amount[$key];
                $total += $reference_net_invoice_amount[$key];

                $update_invoices[]= array(
                    'id' => $val,
                    'payment_status' => 'Payment Processing',
                    'voucher_created_flag' => 2,
                    'associated_voucher_amounts' => $net_invoice_amount[$key]
                );
            }
            $this->db->trans_start();
            $this->db->insert_batch('procurement_voucher_details', $details_insert);
            // Update Invoice Statuses
            $this->db->update_batch('procurement_invoice_master', $update_invoices, 'id');
            $update_master= array(
                'total_amount' => $total,
                'voucher_status' => 'Pending'

            );
            $this->db->where('id', $voucher_master_id)->update('procurement_voucher_master', $update_master);

            // History
            $amountPayble= number_format(1*$payble);
             $history= array(
                'voucher_id' => $voucher_master_id,
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'action_type' => 'Voucher Created',
                'action_description' => "A voucher was successfully created with the total amount of ₹$amountPayble: and the associated invoice details have been recorded in the system.",
                'action_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_voucher_history', $history);

            

            $this->db->trans_complete();
        }
        if($this->db->trans_status()) {
            return array(
                'status' => 1,
                'voucher_master_id' => $voucher_master_id
            );
        }
        $this->db->trans_rollback();
        return array(
                'status' => 0,
                'voucher_master_id' => $voucher_master_id
            );
    }

    function __add_approvers_in_paymentadvice_flow($voucher_master_id) {
        $voucherApprovers= $this->get_approvers();
        if(!empty($voucherApprovers)) {
            foreach($voucherApprovers as $key => $val) {
                $voucherApproverInsert[]= array(
                    'voucher_id' => $voucher_master_id,
                    'approval_type' => $val->type,
                    'approver_id' => $val->staff_id,
                    'approval_status' => 'Pending'
                );
            }
            $this->db->insert_batch('procurement_voucher_approvals', $voucherApproverInsert);
        }
    }

    function __get_invoice_details($inv_ids_arr) {
        return $this->db->select("id, subtotal_amount, sgst_amount, cgst_amount, cess_amount, igst_amount, gst_amount, discount_amount, total_amount")
                ->where_in('id', $inv_ids_arr)
                ->get('procurement_invoice_master')->result();
    }

    function __get_pa_number() {
        $pa= $this->db_readonly->select("max(id) as id")->get('procurement_voucher_master')->row();
        $current= 0;
        if(!empty($pa)) {
            $current= $pa->id;
        }
        $getting_used= $current*1 + 1;
        return "PV-".strtoupper(date('dMY'))."-00$getting_used";
    }

    function get_payment_vouchers() {
        // echo '<pre>'; print_r($_POST); die();
        $input= $this->input->post();
        $vouchert_status= $input['vouchert_status'];
        $from_date= date('Y-m-d', strtotime($input['from_date']));
        $to_date= date('Y-m-d', strtotime($input['to_date']));
        $vendor_id= $input['vendor_id'];

        $this->db_readonly->select("ppm.id as pa_id, ppm.voucher_number, date_format(ppm.voucher_date, '%d-%m-%Y') as voucher_date, ppm.voucher_type, ppm.net_payable, ppm.voucher_status, voucher_status, ifnull(ppm.remarks, '-') as remarks, pvm.vendor_name, ifnull(pvm.vendor_code, '-') as vendor_code, if(ppm.created_by IS NOT NULL AND ppm.created_by > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff")
                ->from('procurement_voucher_master ppm')
                ->join('procurement_vendor_master pvm', 'pvm.id = ppm.vendor_id')
                ->join('staff_master sm', 'sm.id = ppm.created_by', 'left');

        if($vouchert_status != 'all') {
            $this->db_readonly->where('ppm.voucher_status', $vouchert_status);
        }
        if($vendor_id != 'all') {
            $this->db_readonly->where('ppm.vendor_id', $vendor_id);
        }

        $this->db_readonly->where("date_format(ppm.created_on, '%Y-%m-%d') between '$from_date' and '$to_date'");
        $data= $this->db_readonly->get()->result();
        // echo '<pre>'; print_r($this->db_readonly->last_query($data)); die();
        return $data;
    }

    function get_pa_details($pa_id) {
        $data= $this->db_readonly->select("ppm.id as pa_id, ppm.voucher_number, date_format(ppm.voucher_date, '%d-%m-%Y') as voucher_date, ppm.voucher_type, ppm.net_payable, ppm.voucher_status, ifnull(ppm.remarks, '-') as remarks, pvm.vendor_name, ifnull(pvm.vendor_code, '-') as vendor_code, if(ppm.created_by IS NOT NULL AND ppm.created_by > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff")
                ->from('procurement_voucher_master ppm')
                ->join('procurement_vendor_master pvm', 'pvm.id = ppm.vendor_id')
                ->join('staff_master sm', 'sm.id = ppm.created_by', 'left')
                ->where('ppm.id', $pa_id)
                ->get()->row();
        return $data;
    }

    function get_voucher_approvers_from_table($pa_id) {
        return $this->db_readonly->select("sm.id as staff_id, ppa.id as voucher_approvals_id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as staff, upper(ppa.approval_type) as type, ifnull(sd.department, '-') as department, ppa.approval_status, ifnull(ppa.approval_comments, '-') as approval_comments")
                ->from('procurement_voucher_approvals ppa')
                ->join('staff_master sm', 'sm.id = ppa.approver_id')
                ->join('staff_departments sd', 'sd.id = sm.department', 'left')
                ->where('ppa.voucher_id', $pa_id)
                ->get()->result();
    }

    // Document
    function add_document() {
        $file= $_FILES;
        // echo '<pre>'; print_r($_POST); die();
        $voucher_master_id= $this->input->post('voucher_master_id');
        $additional_description_notes= $this->input->post('additional_description_notes');
        if($file) {
            $this->load->library('filemanager');
            $additional_attachements = $this->s3FileUpload($_FILES['additional_attachements'],'voucher_documents');
            
            $insert_docs= array(
                'voucher_id' => $voucher_master_id,
                'file_name' => $this->input->post('fileName'),
                'document_type' => $this->input->post('invoice_document_type'),
                'file_type' => $this->input->post('fileExtentionType'),
                'file_size' => $this->input->post('fileSizeBytes'),
                'document_description' => $additional_description_notes,
                'uploaded_by' => $this->authorization->getAvatarStakeHolderId(),
                'file_path' => (isset($additional_attachements['file_name']) && $additional_attachements['file_name'] !='') ? $additional_attachements['file_name'] : NULL
            );
            $this->db->insert('procurement_voucher_documents', $insert_docs);
            $voucher_attachments_id= $this->db->insert_id();
             // History
             $file_name= $this->input->post('fileName');
             $history= array(
                'voucher_id' => $voucher_master_id,
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'action_type' => 'Voucher Document Added',
                'action_description' => "Voucher document has been added with the name $file_name. $additional_description_notes",
                'action_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_voucher_history', $history);

           
            
            $status= array(
                'status' => 1,
                'voucher_master_id' => $voucher_master_id,
                'voucher_attachments_id' => $voucher_attachments_id
            );
        } else {
            $status= array(
                'status' => 0,
                'voucher_master_id' => $voucher_master_id
            );
        }
        return $status;
    }

    public function s3FileUpload($file,$folder_name='Other_Documents') {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
    }

    function get_approver_details() {
        $voucher_master_id= $this->input->post('voucher_master_id');

        return $this->db_readonly->select("pva.id, if(pva.approver_id > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, ifnull(sd.department, '-') as department, pva.approval_type, pva.approval_status")
                ->from('procurement_voucher_approvals pva')
                ->join('staff_master sm', 'sm.id = pva.approver_id', 'left')
                ->join('staff_departments sd', 'sd.id = sm.department', 'left')
                ->where('pva.voucher_id', $voucher_master_id)
                ->get()->result();
    }

    function getDocumentURL($voucher_attachments_id) {
        $x= $this->db_readonly->select("file_path")->where('id', $voucher_attachments_id)->get('procurement_voucher_documents')->row();
        // echo '<pre>'; print_r($this->db_readonly->last_query($x)); die();
        return $x;
    }

    function remove_document() {
        $voucher_attachments_id= $this->input->post('voucher_attachments_id');
        return $this->db->where('id', $voucher_attachments_id)->delete('procurement_voucher_documents');
    }

    function get_pa_invoices($voucher_master_id) {
        return $this->db_readonly->select("pim.id as invoice_id, pim.invoice_number, pim.invoice_date, pim.invoice_type, pim.invoice_remarks as narration, pii.delivery_challan_type")
                ->from('procurement_voucher_details pvd')
                ->join('procurement_invoice_master pim', 'pim.id = pvd.invoice_id')
                ->join('procurement_invoice_items pii', 'pii.invoice_master_id = pim.id')
                ->where('pvd.voucher_id', $voucher_master_id)
                ->group_by('pim.id')
                ->get()->result();
    }

    function get_pa_attachments($voucher_master_id) {
        $docs = $this->db_readonly->select("id as voucher_attachements_id, document_type, file_name as name, ifnull(file_path, '-') as document, if(document_description is not null && TRIM(document_description) != '', document_description, '-') as remarks")
            ->where('voucher_id', $voucher_master_id)
            ->get('procurement_voucher_documents')->result();

        if (!empty($docs)) {
            $this->load->library('filemanager');
            foreach ($docs as $key => $val) {
                if ($val->document != '-') {
                    $url = $this->filemanager->getFilePath($val->document);
                    $val->url = $url;
                }
            }
        }
        return $docs;
    }

    function get_pa_history($voucher_master_id) {
        return $this->db_readonly->select("pvh.id as history_id, pvh.action_type, pvh.action_description, if(pvh.action_by > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, date_format(pvh.action_on, '%d-%m-%Y') as action_on")
                ->from('procurement_voucher_history pvh')
                ->join('staff_master sm', 'sm.id = pvh.action_by', 'left')
                ->where('pvh.voucher_id', $voucher_master_id)
                ->get()->result();
    }

    function reject_the_voucher() {
        $input= $this->input->post();
        $staff_id= $input['staff_id'];
        $voucher_approvals_id= $input['voucher_approvals_id'];
        $type= $input['type'];
        $staff_name= $input['staff_name'];
        $approval_status= $input['approval_status'];
        $rejection_remarks= $input['rejection_remarks'];
        $voucher_master_id= $input['voucher_master_id'];

        $this->db->trans_start();
            $approver_update= array(
                'approval_status' => 'Rejected',
                'approval_comments' => $rejection_remarks,
                'approved_on' => date('Y-m-d H:i:s')
            );
            $this->db->where('id', $voucher_approvals_id)->update('procurement_voucher_approvals', $approver_update);
            $this->db->where('id', $voucher_master_id)->update('procurement_voucher_master', array('voucher_status' => 'Rejected'));
            // History
             $history= array(
                'voucher_id' => $voucher_master_id,
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'action_type' => 'Voucher Rejected',
                'action_description' => "This voucher has been rejected by $type: $staff_name. Reason: $rejection_remarks",
                'action_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_voucher_history', $history);
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    function approve_the_voucher() {
        $input= $this->input->post();
        $staff_id= $input['staff_id'];
        $voucher_approvals_id= $input['voucher_approvals_id'];
        $type= $input['type'];
        $staff_name= $input['staff_name'];
        $approval_status= $input['approval_status'];
        $approved_remarks= $input['approved_remarks'];
        $voucher_master_id= $input['voucher_master_id'];
        $isFinallyApproed= $input['isFinallyApproed'];

        $this->db->trans_start();
            $approver_update= array(
                'approval_status' => 'Approved',
                'approval_comments' => $approved_remarks,
                'approved_on' => date('Y-m-d H:i:s')
            );
            $this->db->where('id', $voucher_approvals_id)->update('procurement_voucher_approvals', $approver_update);
            if($isFinallyApproed == '1') {
                $this->db->where('id', $voucher_master_id)->update('procurement_voucher_master', array('voucher_status' => 'Approved'));
                $getAllRelatedInvoices= $this->db->select("invoice_id")
                        ->where('voucher_id', $voucher_master_id)
                        ->get('procurement_voucher_details')->result();
                if(!empty($getAllRelatedInvoices)) {
                    $updateInvoiceMaster= [];
                    foreach($getAllRelatedInvoices as $key => $val) {
                        $updateInvoiceMaster[]= array(
                            'id' => $val->invoice_id,
                            'payment_status' => 'Payment Approved'
                        );
                    }
                    if(!empty($updateInvoiceMaster)) {
                        $this->db->update_batch('procurement_invoice_master', $updateInvoiceMaster, 'id');
                    }
                }
            }
            // History
             $history= array(
                'voucher_id' => $voucher_master_id,
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'action_type' => 'Voucher Approved',
                'action_description' => "This voucher has been approved by $type: $staff_name. Narration: $approved_remarks",
                'action_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_voucher_history', $history);
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    function log_failed_attempt() {
        $input= $this->input->post();
        $loggenIn_userId= $input['loggenIn_userId'];
        $type= $input['type'];
        $staff_name_associated= $input['staff_name'];
        $approval_type= $input['approval_type'];
        $voucher_master_id= $input['voucher_master_id'];

        $staff_name= 'Admin';
        if($loggenIn_userId > 0) {
            $staff= $this->db->select("concat(first_name, ' ', ifnull(last_name, '')) as staff")->where('id', $loggenIn_userId)->get('staff_master')->row();
            if(!empty($staff)) {
                $staff_name= $staff->staff;
            }
        }

        // History
             $history= array(
                'voucher_id' => $voucher_master_id,
                'action_by' => $this->authorization->getAvatarStakeHolderId(),
                'action_type' => 'Attempt Failed',
                'action_description' => "A staff member, $staff_name, from your firm attempted to $approval_type a voucher associated with the $type department, specifically linked to $staff_name_associated.",
                'action_on' => date('Y-m-d H:i:s')
            );
        return $this->db->insert('procurement_voucher_history', $history);
    }

}
?>