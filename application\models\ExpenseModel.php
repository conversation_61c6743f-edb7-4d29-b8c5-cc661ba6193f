<?php
class ExpenseModel extends CI_Model {

  public function __construct() {
    parent::__construct();
  }

    public function Kolkata_datetime(){
      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime();
      $date->setTimezone($timezone );
      $dtobj = $date->format('Y-m-d H:i:s');
      return $dtobj;
    }
  public function getExpenseReport() {
    $this->db->select("em.*, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ec.category_name, ivm.vendor_name");
    $this->db->join("staff_master sm", "sm.id=em.made_by", "left");
    $this->db->join("expense_category ec", "ec.id = em.category_id", "left");
    $this->db->join("inventory_vendor_master ivm", "ivm.id = em.vendor_id", "left");
    // $this->db->where('approve_status', 1);
    $this->db->where('em.made_on >=', date('Y-m-d',strtotime('today - 30 days')));
    $this->db->order_by('made_on','DESC');
    return $this->db->get("expense_master em")->result();
  }

  public function getExpenseReport1($from_date, $to_date, $payment_mode, $category_id) {
    
   
    $this->db->select("em.*, DATE_FORMAT(em.made_on, '%d-%m-%Y') as made_date, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, CONCAT(IFNULL(ac.name, ''), '-', IFNULL(ac.account, '')) AS acc_name, group_concat(ec.category_name) as category_name");
    $this->db->join("staff_master sm", "sm.id=em.made_by", "left");
    $this->db->join("expense_sub_category_tx esct", "esct.expense_master_id = em.id", "left");
    $this->db->join("expense_category ec", "ec.id = esct.category_id", "left");
    $this->db->join('accounts ac', 'ac.id=em.account_id', 'left');
    $this->db->where('em.approve_status', 1);
    // $this->db->where('em.acad_year_id', $this->acad_year->getAcadYearId());
    if ($from_date && $to_date) {
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
    $this->db->where('date_format(em.made_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db->order_by('made_on','DESC');
    if ($payment_mode !='-1') {
      $this->db->where_in('em.payment_type', $payment_mode);
    }
    if($category_id){
      $this->db->where_in('esct.category_id', $category_id);
    }
    $this->db->group_by('em.id');
    return  $this->db->get("expense_master em")->result();
  }

  public function get_staff_all_expense(){
    return $this->db->select("id as sId, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as sName, identification_code,'staff' as type")
    ->from('staff_master')
    ->where('status', 2)
    ->order_by('first_name','asc')
    ->get()->result();
  }

  public function getExpenses() {
   
    $this->db->select("em.transaction_mode,em.name,em.description,em.approve_status,em.amount,em.voucher_number,em.made_on,em.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, ec.category_name,ec.id as catID");
    $this->db->join("staff_master sm", "sm.id = em.made_by", "left");
    $this->db->join("expense_sub_category_tx esct", "esct.expense_master_id = em.id","left");
    $this->db->join("expense_category ec", "ec.id = esct.category_id", "left");
    $this->db->where('em.acad_year_id', $this->acad_year->getAcadYearId());
    // $this->db->where('em.approve_status !=','-1');
    // $this->db->where('em.cancelled_status','1');
    $this->db->order_by('made_on', 'DESC');
    $this->db->order_by('em.id', 'DESC'); // Secondary order for predictability
    $this->db->group_by('em.id'); // Secondary order for predictability
    return $this->db->get("expense_master em")->result();

  }

  public function edit_Expenses($id){
    $master = $this->db->select('*')
    ->from('expense_master em')
    ->where('em.id',$id)
    ->get()->row();
    
    $subCate = $this->db->select('esct.id, esc.id as subId, esc.sub_category as subCatName, esct.amount,esct.category_id,ec.category_name as CatName, ec.id as cat_id')
    ->from('expense_sub_category_tx esct')
    ->where('esct.expense_master_id',$id)
    ->join('expense_sub_category esc','esc.id=esct.sub_cat_id',"left")
    ->join('expense_category ec','ec.id=esct.category_id')
    ->get()->result();
    $result = [];
    foreach ($subCate as $item) {
        $result[$item->CatName][] = $item;
    }
    $cat_id = $this->db->select('category_id')->from('expense_sub_category_tx')->where('expense_master_id', $id)->order_by('expense_master_id')->get()->row();
    $master->subcategory = $result;
    $master->cat_id = 0;
    if(!empty($cat_id)){
      $master->cat_id = $cat_id->category_id;
    }
    return $master;

  }

  public function attached_voucher($id){
     return  $this->db->where('id',$id)->get("expense_master")->row()->voucher_url;
  }

  public function get_staff_list_search_expense(){
    return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
    ->from('staff_master sm')
    ->where('sm.status',2)
    ->get()->result();
  }

  public function expense_staff_reports() {
    $masterCount = 	$this->db->select("em.id, em.made_by, sum(em.amount) as staff_total, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name")
        // ->join('expense_category ec', 'ec.id = em.category_id', 'LEFT')  
        ->join("staff_master sm", "sm.id=em.made_by", "left")  
				->from('expense_master em')
        //->where('em.made_on >=', date('Y-m-d',strtotime('today - 30 days')))
        ->where('em.approve_status', 1)
				->group_by('made_by')
				->get()->result();
    
    return $masterCount; 
    echo $this->db->last_query();
    echo "<pre>"; print_r($masterCount);
    die();
  }

  public function expense_staff_reports1($from_date, $to_date,$staffId) {
    // switch ($option){
    //   case 1: $from = date('Y-m-d',strtotime('today')); $to = date('Y-m-d',strtotime('today')); break;
    //   case 2: $from = date('Y-m-d',strtotime('today - 7 days')); $to = date('Y-m-d',strtotime('today')); break;
    //   case 3: $from = date('Y-m-d',strtotime('today - 30 days')); $to = date('Y-m-d',strtotime('today')); break;
    //   case 4: $from = date('Y-m-d',strtotime('today - 60 days')); $to = date('Y-m-d',strtotime('today')); break;
    //   case 5: $from = date('Y-m-d',strtotime('today - 90 days')); $to = date('Y-m-d',strtotime('today')); break;
    //   // case 6: $from = date('Y-m-d',strtotime($from_date)); $to = date('Y-m-d',strtotime($to_date)); break;
    //   case 7: $from = date('Y-m-d',strtotime($from_date)); $to = date('Y-m-d',strtotime($to_date)); break;
    // }
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));
    $this->db->select("em.id, em.made_by, sum(em.amount) as staff_total, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name");
    $this->db->from('expense_master em');
    $this->db->join("staff_master sm", "sm.id=em.made_by", "left");
    $this->db->where('em.approve_status', 1);
    $this->db->group_by('em.made_by');
    
    if ($fromDate && $toDate) {
      $this->db->where('date_format(em.made_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    // if($from_date == 6)
    //   $this->db->where('em.acad_year_id', $this->acad_year->getAcadYearId());
    // else{
    //   $this->db->where('em.made_on >=', $from);
    //   $this->db->where('em.made_on <=', $to);
    // }

    if ($staffId) {
      $this->db->where('sm.id', $staffId);
    }

    // $this->db->order_by('made_on','DESC');
    return  $this->db->get()->result();
    //echo $this->db->last_query();
    //echo "<pre>"; print_r($return);
    //die();
    
  }
  public function expense_category_reports($from_date, $to_date) {
    $category = $this->db_readonly->select('ec.id as category_id, ec.category_name')
    ->from('expense_category ec')
    ->get()->result();
    
    $this->db_readonly->select('esct.category_id, sum(em.amount) as cat_total,em.name as sub_cat_name')
    ->from('expense_master em')
    ->join('expense_sub_category_tx esct','esct.expense_master_id=em.id')
    ->where('em.approve_status', 1)
    ->where('em.cancelled_status', 0)
    ->group_by('esct.category_id');
    if ($from_date && $to_date) {
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db_readonly->where('date_format(em.made_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $exmaster = $this->db_readonly->get()->result();
    $temp =array();
    foreach ($exmaster as $key => $exm) {
      $temp[$exm->category_id] = $exm;
    }
    foreach ($category as $key => $val) {
      $val->cat_total = 0;
      if (array_key_exists($val->category_id, $temp)) {
         $val->cat_total = $temp[$val->category_id]->cat_total;
      }
    }
   
    // $masterCount = 	$this->db->select('em.category_id, sum(em.amount) as cat_total, ec.category_name')
    // ->from('expense_category ec')
    // ->join('expense_master em','ec.id = em.category_id','left')
    // ->where('em.made_on >=', date('Y-m-d',strtotime('today - 30 days')))
    // ->where('em.approve_status', 1)
		// ->group_by('category_id')
		// ->get()->result();
    return $category; 
    // echo $this->db->last_query();
    // echo "<pre>"; print_r($masterCount);
     //die();
  }

  public function get_category_sub_caterogy(){
    $category = $this->db_readonly->select('ec.id as category_id, ec.category_name')
    ->from('expense_category ec')
    ->get()->result();
    return $category;
  }

  public function get_sub_cat($catID){
    $data= $this->db_readonly->select('esc.sub_category, esc.id')
    ->from('expense_sub_category esc')
    ->where('esc.cat_id', $catID)
    ->get()->result();
      return $data;
  }



  public function expense_category_reports1($option, $from_date, $to_date) {
    switch ($option){
      case 1: $from = date('Y-m-d',strtotime('today')); $to = date('Y-m-d',strtotime('today')); break;
      case 2: $from = date('Y-m-d',strtotime('today - 7 days')); $to = date('Y-m-d',strtotime('today')); break;
      case 3: $from = date('Y-m-d',strtotime('today - 30 days')); $to = date('Y-m-d',strtotime('today')); break;
      case 4: $from = date('Y-m-d',strtotime('today - 60 days')); $to = date('Y-m-d',strtotime('today')); break;
      case 5: $from = date('Y-m-d',strtotime('today - 90 days')); $to = date('Y-m-d',strtotime('today')); break;
      case 7: $from = date('Y-m-d',strtotime($from_date)); $to = date('Y-m-d',strtotime($to_date)); break;
    }
        
    $this->db->select('em.category_id, sum(em.amount) as cat_total, ec.category_name');
    $this->db->join('expense_category ec', 'ec.id = em.category_id', 'LEFT');
    $this->db->where('em.approve_status', 1);
    $this->db->group_by('em.category_id');
   
    if($option == 6)
      $this->db->where('em.acad_year_id', $this->acad_year->getAcadYearId());
    else{
      $this->db->where('em.made_on >=', $from);
      $this->db->where('em.made_on <=', $to);
    }

    return  $this->db->get("expense_master em")->result();
    echo $this->db->last_query();
    echo "<pre>"; print_r($return);
    die();
    
  }

  public function getExpensePurpose() {
     return $this->db->get("expense_purpose")->result();
   }

  public function get_voucher_lastIdNumber(){
    $exConfig = $this->settings->getSetting('expense_vocher_number_algorithm');
    
    $this->db->select('em.id,voucher_number');
    $this->db->from('expense_master em');
    if (!empty($exConfig) &&  $exConfig != 'simple') {
      $this->db->where('em.payment_type','9');
    }
    $this->db->order_by('id','desc');
    $result =  $this->db->get()->row();

    $voucher = 1;
    if (!empty($result)) {
      $voucher = $result->id + 1;
      if ($exConfig == 'payment_type') {
        $vocherNumber = str_replace('C', '', $result->voucher_number);
        $voucher = $vocherNumber + 1;
      }
    }
    if (!empty($exConfig)) {
      if ($exConfig == 'payment_type') {
        $voucher_number = sprintf("C"."%'.0".'6'."d",$voucher);
      }
    }else{
      $voucher_number = sprintf("%'.0".'6'."d",$voucher);
    }
    return $voucher_number;

  }

  public function get_voucher_number_receipt_book_byid($receiptBookId){
    $this->load->library('fee_library');
    $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$receiptBookId)->get()->row();
    if (!empty($receipt_book)) {
      return $this->fee_library->receipt_format_get_update($receipt_book);
    }else{
      return 0;
    }
    
  }

  public function insertExpenseData($path = NULL)
{
    $input = $this->input->post();
    
    $subcategoryData = $input['subcategoryid'];
    $subcategoryAmounts = $input['subcategoryamount'];

    $this->db->trans_start();

    $data = [
        'name'             => $input['expense_name'],
        'voucher_number'   => $input['voucher_number'],
        'made_on'          => date('Y-m-d', strtotime($input['date'])),
        'voucher_url'      => $path,
        'amount'           => $input['amount'],
        'vendor_id'        => ($input['vendor_id'] === 'others') ? '-1' : $input['vendor_id'],
        'vendor_other'     => empty($input['vendor_other']) ? null : $input['vendor_other'],
        'vendor_bill_no'   => empty($input['vendor_bill_no']) ? null : $input['vendor_bill_no'],
        'description'      => trim($input['description']),
        'approve_status'   => '0',
        'bank_name'        => $input['bank_name'],
        'cheque_number'    => $input['cheque_number'],
        'bank_date'        => date('Y-m-d', strtotime($input['bank_date'])),
        'acad_year_id'     => $this->acad_year->getAcadYearId(),
        'payment_type'     => $input['payment_type'],
        'made_by'          => $this->authorization->getAvatarStakeHolderId(),
        'account_id'       => $input['account_id'],
        'vendor_bill_amount' => $input['vendor_bill_amount'],
        'transaction_mode' => "DEBIT",
        'payment_category' => $input['payment_category'],
        'category_id' => $input['category_id'],
        'invoice_date' => $input['invoice_date'],
        "created_date" =>date('Y-m-d H:i:s')

    ];

    $this->db->insert('expense_master', $data);
    $lastId = $this->db->insert_id();
    if (!$lastId) {
        $this->db->trans_rollback();
        return FALSE;
    }
    $insert_data = [];
    foreach ($subcategoryData as $catId => $subcategories) {
      foreach ($subcategories as $index => $subId) {
          // Ensure amount exists
          $amount = isset($subcategoryAmounts[$catId][$index]) ? $subcategoryAmounts[$catId][$index] : null;
          if (!empty($amount)) {
              // Prepare data for database
              $insert_data[] = [
                  'category_id' => $catId,
                  'sub_cat_id' => $subId,
                  'amount' => $amount,
                  'expense_master_id'=>$lastId
              ];
          }
      }
    }

    if (!empty($insert_data)) {
        $this->db->insert_batch('expense_sub_category_tx', $insert_data);
    }

    $this->db->trans_complete();

    if ($this->db->trans_status() === FALSE) {
        return FALSE;
    }

    return $lastId;
}


   public function update_receipt_book_byid($receiptBookId, $paymentType){
      $receiptBook_id = $receiptBookId->payment_type_others;
      if ($paymentType == 9) {
        $receiptBook_id = $receiptBookId->payment_type_cash;
      }
      if ($paymentType == 4) {
        $receiptBook_id = isset($receiptBookId->payment_type_cheque) ? $receiptBookId->payment_type_cheque : $receiptBook_id;
      }
      $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$receiptBook_id)->get()->row();
      $this->db->where('id',$receiptBook_id);
      $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
   }

   public function updateExpenseData($path = NULL)
{
    $input = $this->input->post();
    $subcategoryData = $input['subcategoryid'];  // Array for subcategory IDs
    $subcategoryAmounts = $input['subcategoryamount']; // Array for subcategory amounts
    $this->db->trans_start();

    // Update the expense_master record
    $data = [
        'name'             => $input['expense_name'],
        'voucher_number'   => $input['voucher_number'],
        'made_on'          => !empty($input['date']) ? date('Y-m-d', strtotime($input['date'])) : null,
        'amount'           => $input['amount'],
        'vendor_id'        => ($input['vendor_id'] === 'others') ? '-1' : $input['vendor_id'],
        'vendor_other'     => empty($input['vendor_other']) ? null : $input['vendor_other'],
        'vendor_bill_no'   => empty($input['vendor_bill_no']) ? null : $input['vendor_bill_no'],
        'description'      => trim($input['description']),
        'bank_name'        => $input['bank_name'],
        'cheque_number'    => $input['cheque_number'],
        'bank_date'        => !empty($input['bank_date']) ? date('Y-m-d', strtotime($input['bank_date'])) : null,
        'acad_year_id'     => $this->acad_year->getAcadYearId(),
        'payment_type'     => $input['payment_type'],
        'made_by'          => $this->authorization->getAvatarStakeHolderId(),
        'account_id'       => $input['account_id'],
        'vendor_bill_amount' => $input['vendor_bill_amount'],
        'transaction_mode' => "DEBIT",
        'payment_category' => $input['payment_category'],
        'invoice_date' => $input['invoice_date']
    ];

    if ($path !== NULL) {
        $data['voucher_url'] = $path;
    }

    $this->db->where('id', $input['id']);
    $result = $this->db->update('expense_master', $data);

    if (!$result) {
        $this->db->trans_rollback();
        return FALSE;
    }

    // Delete existing expense_sub_category_tx records for this expense_master_id
    $this->db->where('expense_master_id', $input['id']);
    $this->db->delete('expense_sub_category_tx');

    // Prepare insert data for expense_sub_category_tx
    $insert_data = [];

    // Iterate over the subcategoryData array
    foreach ($subcategoryData as $catId => $subcategories) {
        if (is_array($subcategories)) {
            // Process if subcategories are an array
            foreach ($subcategories as $index => $subId) {
                $amount = isset($subcategoryAmounts[$catId][$index]) ? $subcategoryAmounts[$catId][$index] : null;
                if (!empty($amount)) {
                    $insert_data[] = [
                        'category_id'       => $catId,
                        'sub_cat_id'        => $subId,
                        'amount'            => $amount,
                        'expense_master_id' => $input['id'] // Use the provided ID
                    ];
                }
            }
        } else {
            // Process if subcategories are a single value (not array)
            $amount = isset($subcategoryAmounts[$catId]) ? $subcategoryAmounts[$catId] : null;
            if (!empty($amount)) {
                $insert_data[] = [
                    'category_id'       => $catId,
                    'sub_cat_id'        => $subcategories,
                    'amount'            => $amount,
                    'expense_master_id' => $input['id'] // Use the provided ID
                ];
            }
        }
    }

    // Insert new records into expense_sub_category_tx
    if (!empty($insert_data)) {
        $this->db->insert_batch('expense_sub_category_tx', $insert_data);
    }

    $this->db->trans_complete();

    // Check the transaction status
    if ($this->db->trans_status() === FALSE) {
        return FALSE;
    }

    return TRUE;
}


   

  public function get_expense_detailsbyId($expId){

    $master = $this->db->select("
              em.id as expId, 
              em.name, 
              em.made_on, 
              em.voucher_number, 
              em.amount,
              em.description,  
              em.vendor_bill_no, 
              em.vendor_other, 
              em.bank_name, 
              em.cheque_number, 
                CASE 
                WHEN em.bank_date = '1970-01-01' OR em.bank_date = '0000-00-00' THEN '-' 
                ELSE DATE_FORMAT(em.bank_date, '%d-%m-%Y') 
                END as bank_date, 
              group_concat(ec.category_name) as category_name, 
              ivm.vendor_name,
              em.payment_type, 
              (CASE WHEN em.payment_type = '9' THEN 'CASH' ELSE 'PAYMENT' END) as heading, 
              em.cancelled_status, 
              em.cancelled_remarks,
              em.invoice_date,
              DATE_FORMAT(em.created_date, '%d-%m-%Y') as created_date,
              ac.name as debited_of_account,
              CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as approved_by, ifnull(em.vendor_bill_amount,'') as vendor_bill_amount
          ")
          ->from('expense_master em')
          ->join('avatar a', 'a.id=em.approved_by', 'left')
          ->join('staff_master sm', 'sm.id=a.stakeholder_id AND a.avatar_type = 4', 'left')
          ->join('expense_sub_category_tx esct', 'esct.expense_master_id = em.id',"left")
          ->join('expense_category ec', 'ec.id=esct.category_id','left')
          ->join('inventory_vendor_master ivm', 'ivm.id=em.vendor_id', 'left')
          ->join('accounts ac', 'ac.id=em.account_id', 'left')
          ->where('em.id', $expId)
          ->get()
          ->row();
          if ($master) {
            $subCate = $this->db->select('esc.sub_category, esct.amount')
                ->from('expense_sub_category_tx esct')
                ->join('expense_sub_category esc', 'esct.sub_cat_id = esc.id')
                ->where('esct.expense_master_id', $expId)
                ->get()
                ->result();

            $master->subcategory = $subCate;
          }

    return $master;
  }

  public function insertExpensePurpose() {
  	$data = array(
     'purpose_name' => $this->input->post('name'),
     'start_date'   => date('Y-m-d',strtotime($this->input->post('date1'))),
     'end_date'     => date('Y-m-d',strtotime($this->input->post('date2'))),
     'owner'        => $this->input->post('owner'),
     'description'  => trim($this->input->post('description'))
  	);
  	return $this->db->insert('expense_purpose', $data);
   }

   public function insertExpenseCategory($name, $department_id) { 
    $this->db->where('category_name', $name);
    $query = $this->db->get('expense_category');

    if ($query->num_rows() > 0) {
        return -1;
    } else {
        $data = [
            'category_name' => $name,
            'department_id' => $department_id
        ];
        return $this->db->insert('expense_category', $data);
    }
}

   
  public function get_date($date) {   
     $this->db->select('*');
     $this->db->from('expense');
     $this->db->where('date', date('Y-m-d',strtotime($date)));
     $this->db->where(array('status'=>1));
  // $this->db->where('DATE_FORMAT(date,"%Y-%m-%d")', date('Y-m-d',strtotime($date)));
      return $this->db->get()->result(); 
   }
   
  public function getCategoriesList(){
    $this->db->select('*');
    $this->db->from('expense_category');
    return $this->db->get()->result();
  }

  public function getExpCategoriesandSubList(){
    $result = $this->db->select("ec.category_name,ec.id as catId,  esc.sub_category,esc.id as subId, ifnull(sd.department, '-') as department")
    ->from('expense_category ec')
    ->join('expense_sub_category esc','ec.id=esc.cat_id','left')
    ->join('staff_departments sd','sd.id=ec.department_id','left')
    ->order_by('ec.id','order_by')
    ->order_by('esc.sub_category','order_by')
    ->get()->result();
    
    $catArray = [];
    foreach ($result as $key => $val) {
      if (!array_key_exists($val->catId, $catArray)) {
        $catArray[$val->catId]['category_name'] = $val->category_name;
        $catArray[$val->catId]['department'] = $val->department;
        $catArray[$val->catId]['catId'] = $val->catId;
        $catArray[$val->catId]['subCat'] = [];
        // $object->catId = $val->catId;
        // $object->subCat = [];
      }
      if (!empty($val->subId)) {
        $catArray[$val->catId]['subCat'][] = array('sub'=>$val->sub_category,'subid'=>$val->subId);
      }
    }
    return $catArray;
  }

  public function getSubCategoriesList(){
    return $this->db->select('ec.category_name, esc.sub_category, esc.id')
    ->from('expense_category ec')
    ->join('expense_sub_category esc','ec.id=esc.cat_id')
    ->get()->result();
  }

  public function delete_sub_categories($id){
    $this->db->where('id',$id);
    return $this->db->delete('expense_sub_category');
  }
  public function getVendorList(){
    $this->db->select('id, vendor_name');
    $this->db->from('inventory_vendor_master');
    return $this->db->get()->result(); 
  }
   
  public function getPurposeList(){
    
    $today = date("Y-m-d 00:00:00");
    //echo $today; die();
    $this->db->select('purpose_name, DATE_FORMAT(start_date,"%d-%m-%Y") as start_date, DATE_FORMAT(end_date,"%d-%m-%Y") as end_date, owner, description') ;
    $this->db->from('expense_purpose');
    //$this->db->where('start_date <=', $today);
    //$this->db->where('end_date >=', $today);
    return $this->db->get()->result(); 
  }

  public function get_all_pending_approvals(){

    $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, em.*, ec.category_name");//
    $this->db->join("staff_master sm", "sm.id=em.made_by", "left");
    $this->db->join("expense_sub_category_tx esct", "esct.expense_master_id = em.id","left");
    $this->db->join("expense_category ec", "ec.id = esct.category_id", "left");
    $this->db->where("em.approve_status", 0);
    $this->db->group_by("em.id");
    $this->db->order_by('made_on', 'DESC');
    $this->db->order_by('em.id', 'DESC');
    
    return  $this->db->get("expense_master em")->result();
  }

  public function SaveApprovedExpense($expense_id){
    //echo $expese_id; die();

    $approver = $this->authorization->getAvatarId();

    $data = array(
        'approve_status' => 1,
        'approved_by' => $this->authorization->getAvatarId() 
    );
  // $this->db->set($data);
  // $this->db->set("approve_status", 1);
  $this->db->where("id", $expense_id);
  return  $this->db->update("expense_master", $data);
  // echo "<pre>"; print_r($return); 
  //   echo $this->db->last_query();
  //   die();
  }

  public function rejectExpense($expense_id, $comments){
    $data = array(
      'approve_status' => -1,
    );
    $this->db->where("id", $expense_id);
  $this->db->update("expense_master", $data);

  $this->db->set("status", 0);
  $this->db->where("expense_id", $expense_id);
  $this->db->update("expense_reject_comments");

  $insert_data = array(
    'expense_id' => $expense_id,
    'comments' => $comments,
    'status' => 1

  );
  return $this->db->insert("expense_reject_comments", $insert_data);
  }

  public function my_expenses(){
    $this->db->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staff_name, em.amount,em.description,em.name,em.approve_status, ec.category_name");//
    $this->db->join("staff_master sm", "sm.id=em.made_by", "left");
    $this->db->join("expense_sub_category_tx esct", "esct.expense_master_id=em.id");
    $this->db->join("expense_category ec", "ec.id = esct.category_id", "left");
    //$this->db->where("em.approve_status", 0);
    $this->db->where("sm.id", $this->authorization->getAvatarStakeHolderId());
    $this->db->where("em.acad_year_id", $this->acad_year->getAcadYearId());
    return $this->db->get("expense_master em")->result();
    
  }

  public function expense_details($expense_id){
    $this->db->select("em.*, ec.category_name, erc.comments");//
    $this->db->join("expense_category ec", "ec.id = em.category_id", "left");
    $this->db->join("expense_reject_comments erc", "erc.expense_id = em.id", "left");
    // $this->db->where("em.approve_status", 0);
    $this->db->where("erc.status", 1);
    $this->db->where("em.id", $expense_id);
    return  $this->db->get("expense_master em")->row_array();
  }

  // public function updateExpenseData($path = NULL) {
  //   $data = array(
  //     'name'        => $this->input->post('expense_name'),
  //     'made_on'=> date('Y-m-d',strtotime($this->input->post('date'))),
  //     'voucher_url' => $path,
  //     'amount'      => $this->input->post('amount'),
  //     'vendor_id'      => $this->input->post('vendor_id'),
  //     'description' => trim($this->input->post('description')),
  //     'approve_status'      => '0',
  //     'category_id' => $this->input->post('category_id'),
  //     "made_by" => $this->authorization->getAvatarId()
  //   );
      
  //   $this->db->where("id", $this->input->post('id'));
  //   return $this->db->update('expense_master', $data);
  //  }

  public function insert_sub_categories(){
    $subCate =[];
    foreach ($this->input->post('sub_category') as $key => $val) {
      $subCate[] = array(
        'sub_category'  => $val,
        'cat_id' => $this->input->post('cate_id'),
      );
    }
   return $this->db->insert_batch('expense_sub_category',$subCate);
  }

  public function get_subcatbyCatIdwise($catId){
    $master  = $this->db->select('sub_category,id')
                    ->from('expense_sub_category')
                    ->where_in('cat_id', $catId)
                    ->get()->result();
                   


    if(empty($master)){
        return 0;
    }else{
      return $master;
    }

  }
  public function get_categorieswisesubcatlist(){

    $master = $this->db->select('ec.id as cat_id, ec.category_name, esc.id as sub_id, esc.sub_category')
    ->from('expense_category ec')
    ->join('expense_sub_category esc', 'ec.id = esc.cat_id', 'left')
    ->get()
    ->result();

    $masterData = [];
    foreach ($master as $val) {
        if (!array_key_exists($val->cat_id, $masterData)) {
            $masterData[$val->cat_id] = [
                'name' => $val->category_name,
                'sub_categories' => []
            ];
        }
    
        // Handle null sub_id and sub_category for PHP 7.1
        $sub_id = isset($val->sub_id) ? $val->sub_id : 0;
        $sub_name = !empty($val->sub_category) ? $val->sub_category : $val->category_name;
    
        $masterData[$val->cat_id]['sub_categories'][$sub_id] = $sub_name;
    }
    
    return $masterData;

  }

//   public function get_subcatbyCatIdwise($catIds){
//     // Ensure $catIds is an array
//     if (!is_array($catIds)) {
//         // Handle the case where a single ID is passed instead of an array
//         $catIds = array($catIds);
//     }

//     // Initialize an empty array to store subcategories
//     $subCategories = array();

//     // Loop through each category ID
//     foreach ($catIds as $catId) {
//         // Fetch subcategories for each category ID
//         $result = $this->db->select('*')->where('cat_id', $catId)->get('expense_sub_category')->result();

//         // Add fetched subcategories to the array
//         $subCategories[$catId] = $result;
//     }

//     return $subCategories;
// }

  public function delete_sub_categories_expense($id){
    $this->db->where('id',$id);
   return $this->db->delete('expense_sub_category_tx');
  }

  public function get_account_details(){
    return $this->db->select('*')->from('accounts')->get()->result();
  }

  public function get_vocher_number_based_on_payment_type($paymentType){
   $exConfig = $this->settings->getSetting('expense_vocher_number_algorithm');
    $cash = ['9'];
    $others = ['8','4'];
    if ($paymentType != '9') {
      $modes = $others;
    }else{
      $modes = $cash;
    }
    $this->db_readonly->select('em.id,voucher_number');
    $this->db_readonly->from('expense_master em');
    if (!empty($exConfig) &&  $exConfig != 'simple') {
      $this->db_readonly->where_in('em.payment_type',$modes);
    }
    $this->db_readonly->order_by('id','desc');
    $result =  $this->db_readonly->get()->row();
    
    $voucher = 1;
    if (!empty($result)) {
      $voucher = $result->id + 1;
      if (!empty($exConfig)) {
        if ($exConfig == 'payment_type'  && $paymentType == '9') {
          $vocherNumber = str_replace('C', '', $result->voucher_number);
          $voucher = $vocherNumber + 1;
        }else{
          $vocherNumber = str_replace('B', '', $result->voucher_number);
          $voucher = $vocherNumber + 1;
        }
      }
    }

    if (!empty($exConfig)) {
      if ($exConfig == 'payment_type' && $paymentType == '9') {
        $voucher_number = sprintf("C"."%'.0".'6'."d",$voucher);
      }else{
        $voucher_number = sprintf("B%'.0".'6'."d",$voucher);
      }
    }else{
      $voucher_number = sprintf("%'.0".'6'."d",$voucher);
    }
    return $voucher_number;

  }

  public function vocher_delete_by_id($expId){
    $data = array(
      'cancelled_status'=> '1',
      'cancelled_on'=> $this->Kolkata_datetime(),
      'cancelled_by'=> $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->where('id', $expId);
   $result =$this->db->update('expense_master', $data);
    if($result){
      return true;
    }else{
      return false;
    }
  }

  public function get_account_transactions($from_date, $to_date, $accountId){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));

    $this->db_readonly->select("em.id, em.name,DATE_FORMAT(em.made_on, '%e-%b-%Y') AS made_on,em.approve_status,em.made_by, em.amount as credit_amount, em.amount, em.description, em.payment_type, em.voucher_number, em.vendor_bill_no, em.vendor_other,em.bank_name,em.cheque_number,DATE_FORMAT(em.bank_date, '%e-%b-%Y') AS bank_date,em.account_id, em.vendor_bill_amount, em.transaction_mode,CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS ApprovedStaffName, ivm.vendor_name,a.name as account_name,em.invoice_date,GROUP_CONCAT(DISTINCT ec.category_name SEPARATOR ', ') as category_name,GROUP_CONCAT(DISTINCT esc.sub_category SEPARATOR ', ') as sub_categories,em.payment_category");
    $this->db_readonly->from('expense_master em');
    $this->db_readonly->join('accounts a','em.account_id=a.id');
    $this->db_readonly->join('staff_master sm', 'sm.id = em.made_by', 'left');
    $this->db_readonly->join('expense_sub_category_tx esct', 'esct.expense_master_id = em.id', 'left');
    $this->db_readonly->join('inventory_vendor_master ivm', 'ivm.id = em.vendor_id', 'left');
    $this->db_readonly->join('expense_category ec', 'esct.category_id = ec.id', 'left');
    $this->db_readonly->join('expense_sub_category esc', 'esct.sub_cat_id = esc.id', 'left');
    $this->db_readonly->join('expense_reject_comments erc', 'erc.expense_id = em.id', 'left');  
    $this->db_readonly->where('em.cancelled_status !=',1);
    $this->db_readonly->where('em.approve_status =', 1);
    if ($fromDate && $toDate) {
      $this->db_readonly->where('em.made_on >=', $fromDate);
      $this->db_readonly->where('em.made_on <=', $toDate);
    }
    
    if ($accountId) {
     $this->db_readonly->where_in('a.id',$accountId);
    }
    $this->db_readonly->group_by('em.id');
    
    // $this->db_readonly->group_by('ec.category_name') ;
    $result =  $this->db_readonly->get()->result();
    //  echo "<pre>"; print_r($result); die();
    // return $exArry;
    // $expAccounts = [];
    // foreach ($result as $key => $val) {
    //   $expAccounts[$val->account_name] = $val;
    // }
    return $result;
  }

  public function getCatSummary($from_date, $to_date,$accountId){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));
    $this->db_readonly->select("ec.category_name,esc.sub_category,CASE WHEN em.transaction_mode = 'CREDIT' THEN em.amount ELSE 0 END AS total_credit,CASE WHEN em.transaction_mode = 'DEBIT' THEN esct.amount ELSE 0 END AS total_debit, CONCAT(IFNULL(a.name, ''), '-', IFNULL(a.account, '')) as account_number");
      $this->db_readonly->from('expense_master em');
      $this->db_readonly->join('accounts a', 'em.account_id = a.id', 'left');
      $this->db_readonly->join('expense_sub_category_tx esct', 'esct.expense_master_id = em.id', 'left');
      $this->db_readonly->join('expense_category ec', 'esct.category_id = ec.id', 'left');
      $this->db_readonly->join('expense_sub_category esc', 'esct.sub_cat_id = esc.id', 'left');
      $this->db_readonly->where('em.cancelled_status !=',1);
      $this->db_readonly->where('em.approve_status =', 1);

      if ($fromDate && $toDate) {
          $this->db_readonly->where('em.made_on >=', $fromDate);
          $this->db_readonly->where('em.made_on <=', $toDate);
      }

      if ($accountId) {
          $this->db_readonly->where_in('em.account_id', $accountId);
      }

      $this->db_readonly->order_by('ec.category_name, esc.sub_category');
      $result = $this->db_readonly->get()->result();
      // echo "<pre>"; print_r($result); die();
      return $result;
  }

  public function get_account_transactions_all($accountId) {
    $this->db_readonly->select('
        COALESCE(SUM(CASE WHEN em.transaction_mode = "CREDIT" THEN em.amount ELSE 0 END), 0) AS total_credit,
    COALESCE(SUM(CASE WHEN em.transaction_mode = "DEBIT" THEN em.amount ELSE 0 END), 0) AS total_debit,
        COALESCE(GROUP_CONCAT(DISTINCT a.name SEPARATOR ", "), "-") as names,
        COALESCE(GROUP_CONCAT(DISTINCT a.account SEPARATOR ", "), "-") as accounts,
        em.account_id
    ');
    $this->db_readonly->from('expense_master em');
    $this->db_readonly->join('accounts a', 'em.account_id = a.id', 'left');
    $this->db_readonly->where_in('em.account_id', $accountId);
    $this->db_readonly->where('em.cancelled_status !=', 1);
    $this->db_readonly->where('em.approve_status =', 1);
    $this->db_readonly->where('em.account_id !=', 0); 
    $this->db_readonly->group_by('em.account_id');

    $result = $this->db_readonly->get()->result();
    $expAccounts = [];
    foreach ($result as $row) {
        $expAccounts[] = [
            'total_credit' => number_format($row->total_credit, 2),
            'total_debit' => number_format($row->total_debit, 2),
            'names' => $row->names,
            'accounts' => $row->accounts,
            'account_id' => $row->account_id,
        ];
    }
    return $expAccounts;
}

     

  public function get_account_transactions_list($from_date, $to_date, $accountId){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));
    $this->db_readonly->select('em.*,  a.name, a.account');
    $this->db_readonly->from('expense_master em');
    $this->db_readonly->where('em.cancelled_status!=',1);
    $this->db_readonly->where('em.approve_status =', 1);
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(em.made_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->join('accounts a','em.account_id=a.id');
    if ($accountId) {
     $this->db_readonly->where_in('a.id',$accountId);
    }
    
    $result = $this->db_readonly->get()->result();
    return $result;
  }

  public function get_category_report($from_date, $to_date, $cat_name,$sub_cat_id){
 
    $to_date = date('Y-m-d', strtotime($to_date));
    $from_date = date('Y-m-d', strtotime($from_date)); 
    
   
    $this->db_readonly->select('em.amount as total,em.name,ec.category_name,GROUP_CONCAT(DISTINCT esc.sub_category SEPARATOR ", ") as sub_categories');
    $this->db_readonly->from('expense_master em');
    $this->db_readonly->join('expense_sub_category_tx esct','esct.expense_master_id=em.id');
    $this->db_readonly->join('expense_category ec', 'ec.id=esct.category_id');
    $this->db_readonly->join('expense_sub_category esc', 'esc.cat_id=esct.category_id');
    $this->db_readonly->where('em.made_on <=',$to_date);
    $this->db_readonly->where('em.made_on >=',$from_date);
    $this->db_readonly->where('em.approve_status', 1);
    $this->db_readonly->where('em.cancelled_status', 0);
      if(count($sub_cat_id) >= 1 &&  $sub_cat_id[0] != '99'){
        $this->db_readonly->where_in('esc.id', $sub_cat_id);
      }else{
        $this->db_readonly->where('esct.category_id', $cat_name);
      }
      $this->db_readonly->group_by('esct.category_id', $cat_name);
      $query = $this->db_readonly->get()->result();
      return $query;


  }

  public function check_expense_voucher_num($voucher_number){
    $this->db_readonly->Select('id');
    $this->db_readonly->from('expense_master');
    $this->db_readonly->where('voucher_number',$voucher_number);
    $this->db_readonly->where('approve_status',1);
    $this->db_readonly->where('transaction_mode','DEBIT');
    $count_voucher=$this->db_readonly->get()->row();

    if(!empty($count_voucher)){
      return $count_voucher->id;
    }else{
      return 0;
    }
  }

  public function get_expense_amendment_data(){
    $from_date=$this->input->post('from_date');
    $to_date=$this->input->post('to_date');
    
    $this->db_readonly->select("ea.id,em.id as em_id,em.voucher_number,ea.created_by,ea.created_on,ea.status,reject_approve_remarks,ea.description,ea.reject_approved_by,date_format(ea.reject_approved_on,'%d-%b-%Y %h:%i %p') as reject_approved_on,ec.category_name");
    $this->db_readonly->from('expense_master em');
    $this->db_readonly->join('expense_amendment ea','ea.expense_id=em.id');
    $this->db_readonly->join('expense_category ec','ec.id=em.category_id','left');
    if ($from_date && $to_date) {
        $createdfrom_date = date('Y-m-d',strtotime($from_date));
        $createdto_date = date('Y-m-d',strtotime($to_date));
        
        $this->db_readonly->where('date_format(created_on,"%Y-%m-%d") BETWEEN "'.$createdfrom_date. '" and "'.$createdto_date.'"');
    }
    $data= $this->db_readonly->get()->result();
    foreach ($data as $a => $b) {
      $b->created_by = $this->_getAvatarNameById($b->created_by);
      $b->reject_approved_by = $this->_getAvatarNameById($b->reject_approved_by);
      $b->created_on = local_time($b->created_on, 'd-M-Y h:i a');
    }
    return $data;
  }

  public function insert_expense_amendments($amendment_data,$expense_id,$remarks){
    
    $this->db->trans_start();
    $expense_amendment_data=array(
        'expense_id' => $expense_id,
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'status' => 'submitted',
        'description' => $remarks,
    );
    $this->db->insert('expense_amendment',$expense_amendment_data);
    $expense_amendment_id=$this->db->insert_id();

    $data = [];
    foreach ($amendment_data as $change) {
        $data[] = [
            'expense_amendment_id' => $expense_amendment_id,
            'field_name' => $change['field_name'],
            'old_value' => $change['old_value'],
            'new_value' => $change['new_value']
        ];
    }

    // Insert the data using CodeIgniter's insert_batch
    $this->db->insert_batch('expense_amendment_items', $data);
    $this->db->trans_complete();

    if($this->db->trans_status()){
        return 1;
    }else{
        return 0;
    }

  }

  private function _getAvatarNameById($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('sm.id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return 'Admin';
    }
  }

  public function get_expense_amendment_items($expense_id){
    $this->db_readonly->select('*');
    $this->db_readonly->from('expense_amendment_items');
    $this->db_readonly->where('expense_amendment_id',$expense_id);
    return $this->db_readonly->get()->result();
  }

  public function approve_reject_expense_amendment($input){
    $this->db->trans_start();
    if ($input['status'] == 'approved') {
      $fieldMap = [
          'Expense Name' => 'name',
          'Vendor Bill No' => 'vendor_bill_no',
          'Amount' => 'amount',
          'Bill/Expense Date' => 'made_on'
      ];
  
      $input_data = [];
      foreach ($input['tableData'] as $value) {
          if (isset($fieldMap[$value['fieldName']])) {
            if($value['fieldName']=='Bill/Expense Date'){
              $input_data[$fieldMap[$value['fieldName']]] = date('Y-m-d', strtotime($value['changedValue']));
            }else{
              $input_data[$fieldMap[$value['fieldName']]] = $value['changedValue'];
            }
          }
      }
      $this->db->where('id',$input['em_id']);
      $this->db->update('expense_master',$input_data);
    }

    $data= array(
      'reject_approved_by'=>$this->authorization->getAvatarStakeHolderId(),
      'reject_approved_on' => $this->Kolkata_datetime(),
      'status'=> $input['status'],
      'reject_approve_remarks'=> !empty ($input['text']) ? $input['text'] : '',
    );

    $this->db->where('id',$input['expense_amendment_id']);
    $this->db->update('expense_amendment',$data);
    $this->db->trans_complete();
    if($this->db->trans_status()){
        return 1;
    }else{
        return 0;
    }
  }

  function get_discription_amendment($amendment_id){
    $this->db_readonly->select("ifnull(description,' NA') as description");
    $this->db_readonly->from('expense_amendment');
    $this->db_readonly->where('id',$amendment_id);
    return $this->db_readonly->get()->row()->description;
  }

  public function get_account_list(){
    $acounts = $this->db_readonly->select('id,name,account')
        ->from('accounts')
        ->get()->result();
        return $acounts;

  }
  
  public function add_credit_expense(){
    $input=$this->input->post();
    $data = [
      'made_on'          => date('Y-m-d', strtotime($input['date'])),
      'amount'           => $input['amount'],
      'description'      => trim($input['description']),
      'approve_status'   => '0',
      'bank_name'        => $input['bank_name'],
      'cheque_number'    => $input['cheque_number'],
      'bank_date'        => date('Y-m-d', strtotime($input['bank_date'])),
      'acad_year_id'     => $this->acad_year->getAcadYearId(),
      'payment_type'     => $input['payment_type'],
      'made_by'          => $this->authorization->getAvatarStakeHolderId(),
      'account_id'       => $input['account'],
      'transaction_mode' => "CREDIT",
      'payment_category' => $input['payment_category'],
      "created_date" =>date('Y-m-d H:i:s')
  ];

      $result = $this->db->insert('expense_master', $data);
      if($result){
        return true;
      }else{
        return false;
      }
  }

  public function view_expence_details(){
    $id  = $this->input->post('expense_id');
    $this->db_readonly->select("em.id, em.name,DATE_FORMAT(em.made_on, '%e-%b-%Y') AS made_on,em.approve_status,em.made_by, em.amount, em.description, em.payment_type, em.voucher_number, em.vendor_bill_no, em.vendor_other,em.bank_name,em.cheque_number,DATE_FORMAT(em.bank_date, '%e-%b-%Y') AS bank_date,em.account_id, em.vendor_bill_amount, em.transaction_mode,CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staffName, ivm.vendor_name,em.transaction_mode,erc.comments as reject_comments");
      $this->db_readonly->from('expense_master em');
      $this->db_readonly->join('staff_master sm', 'sm.id = em.made_by', 'left');
      $this->db_readonly->join('expense_sub_category_tx esct', 'esct.expense_master_id = em.id', 'left');
      $this->db_readonly->join('inventory_vendor_master ivm', 'ivm.id = em.vendor_id', 'left');
      $this->db_readonly->join('expense_category ec', 'esct.category_id = ec.id', 'left');  
      $this->db_readonly->join('expense_sub_category esc', 'esct.sub_cat_id = esc.id', 'left');  
      $this->db_readonly->join('expense_reject_comments erc', 'erc.expense_id = em.id', 'left');  
      $this->db_readonly->where('em.id', $id);  
      $this->db_readonly->group_by('em.id');
      $exp_full= $this->db_readonly->get()->row();
      $this->db_readonly->select("ec.category_name,esct.amount,esc.sub_category");
      $this->db_readonly->from('expense_master em');
      $this->db_readonly->join('expense_sub_category_tx esct', 'esct.expense_master_id = em.id', 'left');
      $this->db_readonly->join('expense_category ec', 'esct.category_id = ec.id', 'left'); 
      $this->db_readonly->join('expense_sub_category esc', 'esct.sub_cat_id = esc.id', 'left'); 
      $this->db_readonly->where('em.id', $id); 
      $get_sub_cat_full =  $this->db_readonly->get()->result();
      return ["full_deatils"=> $exp_full, "full_sub_cat"=> $get_sub_cat_full];

  }

  public function edit_credit_expense(){
    $credit_id = $this->input->post('credit_id');
    $this->db_readonly->select("id,made_on,approve_status,made_by,amount,description,payment_type,bank_name,cheque_number,bank_date,account_id");
    $this->db_readonly->from('expense_master');
    $this->db_readonly->where('id',$credit_id);
    $this->db_readonly->where('approve_status',0);
    return $this->db_readonly->get()->row();
  }

  public function update_credit_expense(){
    $input=$this->input->post();
    $data = [
      'made_on'          => date('Y-m-d', strtotime($input['edit_date'])),
      'amount'           => $input['edit_credit_amount'],
      'description'      => trim($input['edit_description']),
      'bank_name'        => $input['edit_bank_name'],
      'cheque_number'    => $input['edit_cheque_number'],
      'bank_date'        => date('Y-m-d', strtotime($input['edit_bank_date'])),
      'acad_year_id'     => $this->acad_year->getAcadYearId(),
      'payment_type'     => $input['edit_payment_type'],
      'made_by'          => $this->authorization->getAvatarStakeHolderId(),
      'account_id'       => $input['edit_account'],
      'transaction_mode' => "CREDIT",
      'payment_category' => $input['payment_category'],
  ];

      $this->db->where('expense_master.id', $input['expence_id']); 
      $result = $this->db->update('expense_master', $data); 
      
      if ($result) {
          return true; 
      } else {
          return false; 
      }
  }


  public function get_credit_details($from_date, $to_date, $accountId) {
    if (empty($accountId)) {
        return []; // No accounts to process
    }

    // Convert `$to_date` to its inclusive format
    $end_date = date('Y-m-d', strtotime($from_date));

    // Fetch Opening Balances (up to $to_date)
    $opening_balance_obj = $this->db->select("
        em.account_id,
        SUM(CASE WHEN em.transaction_mode = 'CREDIT' AND em.made_on < '" . $end_date . "' THEN em.amount ELSE 0 END) -
        SUM(CASE WHEN em.transaction_mode = 'DEBIT' AND em.made_on < '" . $end_date . "' THEN em.amount ELSE 0 END) AS opening_balance,
        CONCAT(IFNULL(a.name, ''), '-', IFNULL(a.account, '')) AS acc_name
    ")
    ->from("expense_master em")
    ->join("accounts a", "em.account_id = a.id", "right")
    ->where_in("a.id", $accountId)
    ->where("em.approve_status", 1)
    ->where("em.cancelled_status", 0)
    ->group_by("a.id")
    ->get()
    ->result();

    // Prepare Opening Balances
    $op_bal = [];
    $op_name = [];
    foreach ($opening_balance_obj as $val) {
        $op_bal[$val->account_id] = ($val->opening_balance) ? $val->opening_balance :  0;
        $op_name[$val->account_id] = ($val->acc_name) ? $val->acc_name :  '-';
    }
    $this->db->select("
    a.id AS account_id,
    CONCAT(IFNULL(a.name, ''), '-', IFNULL(a.account, '')) AS acc_name
");
$this->db->from('accounts a');
$this->db->where_in('a.id', $accountId);
$query = $this->db->get();
$account_data = $query->result_array();

$account_names = [];
foreach ($account_data as $acc) {
    $account_names[$acc['account_id']] = $acc['acc_name'];
}
    // Fetch Transactions within the Date Range
    $this->db->select("
        COALESCE(SUM(CASE WHEN em.transaction_mode = 'CREDIT' THEN em.amount ELSE 0 END), 0) AS total_credit,
        COALESCE(SUM(CASE WHEN em.transaction_mode = 'DEBIT' THEN em.amount ELSE 0 END), 0) AS total_debit,
        a.id AS account_id,
        CONCAT(IFNULL(a.name, ''), '-', IFNULL(a.account, '')) AS acc_name
    ");
    $this->db->from('accounts a');
    $this->db->join('expense_master em', 'a.id = em.account_id AND em.approve_status = 1 AND em.cancelled_status = 0', 'left');
    $this->db->where('em.made_on >=', date('Y-m-d', strtotime($from_date)));
    $this->db->where('em.made_on <=', date('Y-m-d', strtotime($to_date)));
    $this->db->where_in('a.id', $accountId);
    $this->db->group_by('a.id');
    $this->db->order_by('a.id', 'ASC');

    $results = $this->db->get()->result_array();

$this->db->select("
    a.id AS account_id,
    CONCAT(IFNULL(a.name, ''), '-', IFNULL(a.account, '')) AS acc_name
");
$this->db->from('accounts a');
$this->db->where_in('a.id', $accountId);
$query = $this->db->get();
$account_data = $query->result_array();

$account_names = [];
foreach ($account_data as $acc) {
    $account_names[$acc['account_id']] = $acc['acc_name'];
}

foreach ($accountId as $id) {
    $record = '';
    foreach ($results as $r) {
        if ($r['account_id'] == $id) {
            $record = $r;
            break; 
        }
    }

    $total_credit = is_array($record) && isset($record['total_credit']) ? $record['total_credit'] : 0;
    $total_debit = is_array($record) && isset($record['total_debit']) ? $record['total_debit'] : 0;
    $opening_balance = isset($op_bal[$id]) ? $op_bal[$id] : 0;
    $closing_balance = $opening_balance + $total_credit - $total_debit;

    $acc_name = isset($account_names[$id]) ? $account_names[$id] : 'Default Account Name'; 

    $final_results[] = [
        'account_id' => $id,
        'acc_name' => $acc_name,
        'opening_balance' => $opening_balance,
        'total_credit' => $total_credit,
        'total_debit' => $total_debit,
        'closing_balance' => $closing_balance,
    ];
}


    return $final_results;
}

public function add_columns_db(){
  $expense_master_data = $this->db->get('expense_master')->result();
  foreach ($expense_master_data as $expense_master) {
    $this->db->where('expense_master_id', $expense_master->id);
    $existing_record = $this->db->get('expense_sub_category_tx')->row();
    $data = array(
      'category_id' => $expense_master->category_id,
    );
    if ($existing_record) {
      $this->db->where('id', $existing_record->id);
      $this->db->update('expense_sub_category_tx', $data);
    } else {
      $data['sub_cat_id'] = 0;
      $data['amount'] = $expense_master->amount;
      $data['expense_master_id'] = $expense_master->id;
      $this->db->insert('expense_sub_category_tx', $data);
    }
  }
}

function getDepartments() {
  return $this->db_readonly->where('status', 1)->order_by('department')->get('staff_departments')->result();
}

}



?>