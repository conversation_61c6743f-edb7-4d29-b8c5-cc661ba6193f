<div class="modal" tabindex="-1" role="dialog" id="view_assessmentType">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 80%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Assessments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_assessmentType_data">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('add_assessment', 'view_assessmentType')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Add More</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function loadAssessments() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ assessmentType } = data);
                let html = `<table class="table table-bordered">
                        <tr class="bg-light">
                            <th>#</th>
                            <th>Assessment Type</th>
                            <th>Assessment Remarks</th>
                            <th style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Delete</th>
                        </tr>`

                assessmentType.forEach((r, i) => {
                    html += `
                        <tr>
                            <td>${++i}</td>
                            <td id="assess_${r.id}">${r.name}</td>
                            <td>${r.assessment_remarks}</td>
                            <td style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;"><a onClick="delete_assessment('${r.id}')" class="remove btn btn-danger "><i class="fa fa-trash-o mr-0"></i></a></td>
                        </tr>`;
                })
                html + `</table>`
                $("#view_assessmentType_data").html(html);
            }
        })
    }

    $("#view_assessmentType").on("shown.bs.modal", e => {
        loadAssessments();
    })

    function delete_assessment(assTypeId) {
        const assessment_name = $(`#assess_${assTypeId}`).text();
        Swal.fire({
            title: 'Confirm Assessment Deletion',
            text: `Are you sure you want to delete "${assessment_name}" ?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: '<?php echo site_url('academics/lesson_plan/delete_assessment_type_by_id') ?>',
                    type: 'post',
                    data: { 'assTypeId': assTypeId },
                    success: function (data) {
                        let parsedData = JSON.parse(data);
                        if (data) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted',
                                text: 'Resource deleted successfully',
                                showConfirmButton: false,
                                timer: 1500,
                            }).then(() => {
                                loadAssessments();
                                getSessionData(session_id);
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500,
                            });
                        }
                    },
                    error: function (data) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Something went wrong',
                            showConfirmButton: false,
                            timer: 1500,
                        });
                    }
                });
            }
        });
    }

</script>