<div class="modal" tabindex="-1" role="dialog" id="edit_session">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" style="font-size: 1.5rem;">Edit Session <span id="weekName"></span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 2.5rem;" onclick="resetForm('Edit')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit_session-form" data-parsley-validate>
                    <input type="hidden" name="edit-class-master-id" id="edit-class-master-id">
                    <input type="hidden" name="edit-subject-id" id="edit-subject-id">
                    <input type="hidden" name="edit-lp-week_id" id="edit-lp-week_id">
                    <input type="hidden" name="edit-current-week-no" id="edit-current-week-no">
                    <div class="class-subject-section" style="">
                        <div class="" id="edit_lesson_session" style="width: 100%;">
                            <lable>Lesson</lable>
                            <select class="form-control" name="edit_lesson_id" id="edit_lesson_id" required
                                onchange="editGetTopicsList()" onClick="editGetTopicsList()" style="" disabled>
                                <option value="">Select Lesson</option>
                            </select>
                        </div>

                        <div class="" id="topic_session" style="width: 100%;">
                            <lable>Topic</lable>
                            <select class="form-control" name="edit_topic_id" id="edit_topic_id" required style="" disabled>
                                <option value="">Select Topic</option>
                            </select>
                        </div>

                        <div class="session_name">
                            <lable>Session Name <font style="color: red;">*</font></lable>
                            <input type="text" name="edit_session_name" id="edit_session_name" class="form-control" required placeholder="Enter Session Name" style="width: 100%;">
                        </div>

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetForm('Edit')">Close</button>
                <button type="button" class="btn btn-primary mt-0" id="editLPSession" onClick="editLPSession()">Edit Session</button>
            </div>
        </div>
    </div>
</div>


<script>
    let sessionEditData;
    $("#edit_session").on("shown.bs.modal", function (e) {
        const dataSet = e.relatedTarget.dataset;
        sessionEditData = {
            lesson_id: +dataSet.lesson_id,
            topic_id: +dataSet.topic_id,
            session_name: dataSet.session_name,
            class_master_id: dataSet.class_master_id,
            subject_id: dataSet.subject_id,
            lp_week_id: dataSet.lp_week_id,
            lp_program_weeks_session_id: dataSet.lp_program_weeks_session_id,
            lp_session_id: dataSet.lp_session_id,
            weekNo: dataSet.weekNo,
        }

        $("#edit-class-master-id").val(sessionEditData.class_master_id);
        $("#edit-subject-id").val(sessionEditData.subject_id);
        $("#edit-lp-week_id").val(sessionEditData.lp_week_id);
        $("#edit-current-week-no").val(sessionEditData.weekNo);

        editGetSubjectLessons(sessionEditData);
        $("#edit_session_name").val(sessionEditData.session_name);
    })

    async function editGetSubjectLessons({ subject_id }) {
        await $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/get_lessons') ?>',
            type: 'POST',
            data: { subject_id },
            success: function (data) {
                const lessonData = $.parseJSON(data);

                let html = `<option value="">Select Lesson</option>`;
                lessonData.forEach(l => {
                    html += `
                    <option value="${l.id}" ${l.id == sessionEditData.lesson_id && "Selected"}>${l.lesson_name}</option>
                    `
                })
                $("#edit_lesson_id").html(html);
                $("#edit_lesson_id").trigger("click");
            }
        });
    }

    function editGetTopicsList() {
        const { subject_id } = sessionEditData;
        let lesson_id = $("#edit_lesson_id").val() || sessionEditData.lesson_id;

        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/view_lesson') ?>',
            type: 'POST',
            data: { subject_id },
            success: function (data) {
                const topicData = $.parseJSON(data).viewLessons;
                topicData.forEach(l => {
                    if (subject_id == l.lp_subject_id && lesson_id == l.lesson_id) {
                        let html = `<option value="">Select Topic</option>`;
                        l.sub_topic_arr.forEach(t => {
                            html += `<option value="${t.sub_topic_id}" ${t.sub_topic_id == sessionEditData.topic_id && "Selected"}>${t.sub_topic_name}</option>`;
                        })
                        $("#edit_topic_id").html(html);
                    }
                })
            }
        });
    }
</script>

<style>
    .modal-dialog {
        width: 50%;
        margin: auto;
    }

    .form-control {
        margin-bottom: 10px;
    }

    ::placeholder {
        /* color: #b85b05; */
        opacity: 1;
    }

    :-ms-input-placeholder {
        color: red;
    }

    ::-ms-input-placeholder {
        color: red;
    }
</style>