<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('management/Expense/') ?>">Expenses</a></li>
  <li class="active"> Account-wise Transaction Report </li>
</ul> 
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('management/Expense/');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
          Account-wise Transaction Report
          </h3>
        </div>

        

      </div>
    </div>
    <style type="text/css">
      p{
        margin-bottom: .5rem;
      }
      input[type=checkbox]{
        margin: 0px 4px;
      }
    </style>
    <div class="card-body">
      <div class="row" style="margin: 0px">
      <div class="col-lg-2 form-group" id="classSection">
  <select name="account[]" id="accountId" multiple class="form-control select2" title="Select Accounts">
    <?php 
      foreach ($accounts as $key => $acc) {
        echo '<option value="' . $acc->id . '">' . $acc->name . ' ( ' . $acc->account . ' )' . '</option>';
      }
    ?>
  </select>
</div>

      
        <div class="col-md-2">
          <div id="reportrange" class="dtrange">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
          <small class="col-md-12 help-text" style="padding:0;color:#aba6a6">Filter is based on Expense date</small>
        </div>

        <div class="col-md-1" >
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
        </div>
      </div>
      
    </div>
    
  </div>
</div>


<div class="col-md-12" id="display_of_data"style="padding-top: 19px;display:none">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <!-- <div class="row" style="margin: 0px">
        <div class="col-md-3 d-flex align-items-center justify-content-end ">
          <a href="#" class="circleButton_noBackColor backgroundColor_organge mr-2" data-toggle="tooltip" data-placement="top" title="" data-original-title="Export" onclick="exportToExcel_daily()">
            <span class="fa fa-file-excel-o" style="font-size: 19px;"></span>
          </a>          
          <a href="#" class="circleButton_noBackColor backgroundColor_organge" data-toggle="tooltip" data-placement="top" title="" data-original-title="Print" onClick="printProfile();">
            <span class="fa fa-print" style="font-size: 19px;"></span>
          </a>
        </div>        
      </div>-->
    </div> 
    <div class="card-body">
      <div id="printArea">
      
        <div class="day_book_account_tx  table-responsive hidden-xs">
        </div>
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div>
    </div>
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>



<script type="text/javascript">
   $(document).ready(function() {

    $('#accountId').select2({
        placeholder: "Select Account",
        allowClear: true
    });

  });
  function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });

    $('#salesInclude').change(function(){
      if(this.checked) {
        $('#classSection').hide();
       }else{
        $('#classSection').show();
      }
    });
   
    $('#search').on('click',function(){
      $('#search').val("Please Wait...").prop("disabled", true);
      var from_date = $('#from_date').val();
      var to_date = $('#to_date').val();
      var accountId = $('#accountId').val();
      var payment_type = $('#payment_type').val();
      if(!accountId){
        Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please select Account Details!',
    });
    $('#search').val("Get report").prop("disabled", false);
    return false;

      }
       $(".day_book_account_tx").html('');    
      $(".loading-icon").show();
      $('.prarthana_daily_reports').show();
      
      $('#fromDate').html(from_date);
      $('#toDate').html(to_date);
      $.ajax({
        url: '<?php echo site_url('management/expense/expense_account_transactions'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date,'accountId':accountId},
        success: function(data) {
          $('#search').val("Get report").prop("disabled", false);
          var rData =JSON.parse(data);
          var expense =rData.expense;
          $(".loading-icon").hide();
          $("#display_of_data").show();
          $(".day_book_account_tx").html(expense); 
          var schoolName = '<?php echo $this->settings->getSetting('school_name'); ?>';
          var options = { day: '2-digit', month: 'short', year: 'numeric' };
          var presentDate = new Date().toLocaleDateString('en-GB', options).replace(',', '');

           let table = $('#accountreporttable').DataTable({
            ordering: false,
            paging: false,
            dom: 'Bfrtip',
            info: false,
            buttons: [
              {
                extend: 'pdfHtml5',
                text: '<button class="btn btn-info"><span class="fa fa-file-pdf-o" aria-hidden="true"></span> PDF</button>',
                title: 'Expense Report', 
                orientation: 'landscape',
                pageSize: 'A4',
                footer:true,
                customize: function (doc) {
                 let tableIndex = doc.content.findIndex(item => item.table);
            if (tableIndex == -1) return; // Exit if no table is found
            
            let table = doc.content[tableIndex].table;
            let lastRow = table.body.length - 1; // Get footer row

            if (lastRow >= 0) {
                // for (let i = 0; i < table.body[lastRow].length; i++) {
                //     table.body[lastRow][i].text = ''; 
                // }

                // Place "Total" in the 8th column
                table.body[lastRow][7].text = 'Total';
                table.body[lastRow][7].bold = true;
                table.body[lastRow][7].alignment = 'left';

                // Align totals in 9th and 10th columns
                table.body[lastRow][8].alignment = 'center'; 
                table.body[lastRow][9].alignment = 'center'; 
            }
                    doc.content.splice(0, 0, {
                        text: schoolName,
                        fontSize: 16,
                        bold: true,
                        alignment: 'center',
                        margin: [0, 0, 0, 5]
                    });

                    doc.content.splice(1, 0, {
                        text: 'Expense Report Cash / Netbanking Report As On' + presentDate ,
                        fontSize: 12,
                        alignment: 'center',
                        margin: [0, 0, 0, 5]
                    });
                    doc.content.splice(2, 0, {
                      text: from_date + ' To ' + to_date ,
                      fontSize: 12,
                      alignment: 'center',
                      margin: [0, 0, 0, 10]
                  });
                  var catsummary = $('#catsummary').clone();
                  var catsummaryData = [];
                
                var headerRow = [];
                catsummary.find('thead tr th').each(function () {
                    headerRow.push({ text: $(this).text().trim(), bold: true, fillColor: '#343a40', alignment: 'center',color: 'white' });
                });
                catsummaryData.push(headerRow);

                catsummary.find('tbody tr').each(function () {
                    var rowData = [];
                    $(this).find('td').each(function () {
                        rowData.push({ text: $(this).text().trim(), alignment: 'right' });
                    });
                    catsummaryData.push(rowData);
                });

                doc.content.splice(3, 0, {
                    table: {
                        headerRows: 1,
                        widths: ['20%', '20%', '20%', '20%', '20%'], 
                        body: catsummaryData
                    },
                    layout: {
                        fillColor: function (rowIndex) {
                            return rowIndex % 2 === 0 ? '#f9f9f9' : null; 
                        },
                        hLineWidth: function () { return 0.5; },
                        vLineWidth: function () { return 0.5; }
                    },
                    margin: [0, 0, 0, 10]
                });

                doc.styles.tableHeader = {
                    fillColor: '#343a40', 
                    color: 'white',
                    bold: true,
                    fontSize: 10,
                    alignment: 'center'
                };

                doc.styles.tableBodyEven = {
                    fillColor: '#f2f2f2'
                };
                doc.styles.tableBodyOdd = {
                    fillColor: 'white' 
                };

                doc.content.forEach(function (table) {
                    if (table.table) {
                        table.layout = 'lightHorizontalLines';
                    }
                });

                  var summaryTable = $('#summary').clone();
                  var summaryData = [];
                
                var headerRow = [];
                summaryTable.find('thead tr th').each(function () {
                    headerRow.push({ text: $(this).text().trim(), bold: true, fillColor: '#343a40', alignment: 'center',color: 'white' });
                });
                summaryData.push(headerRow);

                summaryTable.find('tbody tr').each(function () {
                    var rowData = [];
                    $(this).find('td').each(function () {
                        rowData.push({ text: $(this).text().trim(), alignment: 'right' });
                    });
                    summaryData.push(rowData);
                });

                doc.content.splice(3, 0, {
                    table: {
                        headerRows: 1,
                        widths: ['20%', '20%', '20%', '20%', '20%'], 
                        body: summaryData
                    },
                    layout: {
                        fillColor: function (rowIndex) {
                            return rowIndex % 2 === 0 ? '#f9f9f9' : null; 
                        },
                        hLineWidth: function () { return 0.5; },
                        vLineWidth: function () { return 0.5; }
                    },
                    margin: [0, 0, 0, 10]
                });

                doc.styles.tableHeader = {
                    fillColor: '#343a40', 
                    color: 'white',
                    bold: true,
                    fontSize: 10,
                    alignment: 'center'
                };

                doc.styles.tableBodyEven = {
                    fillColor: '#f2f2f2'
                };
                doc.styles.tableBodyOdd = {
                    fillColor: 'white' 
                };

                doc.content.forEach(function (table) {
                    if (table.table) {
                        table.layout = 'lightHorizontalLines';
                    }
                });

               

                }
            },
              {
              text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              title: 'Expense report',
              action: function () {
                exportToExcel(from_date,to_date);
              },
            },
            ],
          });
            }
          });
        });
      });

      function exportToExcel(from_date,to_date) {
        var schoolName = '<?php echo $this->settings->getSetting('school_name'); ?>';
          var options = { day: '2-digit', month: 'short', year: 'numeric' };
          var presentDate = new Date().toLocaleDateString('en-GB', options).replace(',', '');
    var uri = "data:application/vnd.ms-excel;base64,";
    var template = `<html xmlns:o="urn:schemas-microsoft-com:office:office" 
                            xmlns:x="urn:schemas-microsoft-com:office:excel" 
                            xmlns="http://www.w3.org/TR/REC-html40">
                        <head>
                            <!--[if gte mso 9]>
                            <xml>
                                <x:ExcelWorkbook>
                                    <x:ExcelWorksheets>
                                        <x:ExcelWorksheet>
                                            <x:Name>Expense Report</x:Name>
                                            <x:WorksheetOptions>
                                                <x:DisplayGridlines/>
                                            </x:WorksheetOptions>
                                        </x:ExcelWorksheet>
                                    </x:ExcelWorksheets>
                                </x:ExcelWorkbook>
                            </xml>
                            <![endif]-->
                            <meta http-equiv="content-type" content="text/plain; charset=UTF-8"/>
                            <style>
                                table { border-collapse: collapse; width: 100%; }
                                th, td { border: 1px solid black; padding: 5px; text-align: left; }
                                th { background-color: #f2f2f2; }
                                .title { text-align: center; font-weight: bold; font-size: 16px; }
                            </style>
                        </head>
                        <body>
                            <div class="title">${schoolName}</div>
                            <div class="title">Expense Report Cash / Netbanking Report As On ${presentDate}</div>
                            <div class="title">(Period: ${from_date} To ${to_date} )</div>
                            <br>
                            <table>{table}</table>
                        </body>
                    </html>`;

    var base64 = function (s) {
        return window.btoa(unescape(encodeURIComponent(s)));
    };

    var format = function (s, c) {
        return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
        });
    };

    var summaryTable = $("#summary").clone();
    var catsummary = $("#catsummary").clone();
    var mainTable = $("#accountreporttable").clone();
    mainTable.find(".dt-buttons").remove();

    var htmls = summaryTable.prop("outerHTML") + "<br></br>" + catsummary.prop("outerHTML") + "<br><br>" + mainTable.prop("outerHTML");

    var ctx = { worksheet: "Expense Report", table: htmls };

    if (navigator.msSaveOrOpenBlob) {
        var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
        var fileName = `${schoolName}.xls`;
        navigator.msSaveOrOpenBlob(blob, fileName);
    } else {
        var link = document.createElement("a");
        link.download = "Expense Report.xls";
        link.href = uri + base64(format(template, ctx));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

</script>




<style type="text/css">
  .select2-container--default .select2-selection--multiple {
      padding-right: 30px; /* Make space for the dropdown symbol */
    }
    .select2-container--default .select2-selection__arrow {
      top: 50%; /* Vertically center the arrow */
      transform: translateY(-50%);
    }
   .dt-button-collection .buttons-columnVisibility:before {
  content: ' ';
  margin-top: -6px;
  margin-left: 10px;
  border: 1px solid black;
  border-radius: 3px;
}

.dt-button-collection .buttons-columnVisibility:before, .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
}

.dt-button-collection .buttons-columnVisibility span {
  margin-left: 20px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
  content: '\2714'; /* Unicode checkmark character */
  margin-top: -13px;
  margin-left: 12px;
  text-align: center;
  text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
}

#fee_summary_data_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }

div.dt-button-collection {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: .75em 0;
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: white;
    overflow: hidden;
    z-index: 2002;
    border-radius: 5px;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
}
div.dt-button-collection .dt-button {
    padding: 1px 1rem;
}
input[type = 'range'] {
    margin: 0 auto;
    width: 100px;
  }

  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }

 
	.autocomplete-items {
		position: absolute;
		overflow-y:auto;
		border-bottom: none;
		border-top: none;
		height:300px;
		margin:0px 15px;
		z-index: 99;
		/*position the autocomplete items to be the same width as the container:*/
		top: 100%;
		left: 0;
		right: 0;
	}
	.autocomplete-items div {
		padding: 10px;
		cursor: pointer;
		background-color: #fff; 
		border-bottom: 1px solid #d4d4d4; 
	}
	.autocomplete-items div:hover {
		/*when hovering an item:*/
		background-color: #e9e9e9; 
	}
	.autocomplete-active {
		/*when navigating through the items using the arrow keys:*/
		background-color: DodgerBlue !important; 
		color: #ffffff; 
	}

	.data-container {
		width: 100%;
	}
  
.buttons-print{
    padding: 2px  !important;
}
.buttons-colvis{
    padding: 2px !important;
}

.buttons-excel{
  border: none !important;
  background: none  !important;
  padding: 0  !important;
  margin: 0  !important;
}
.dt-button{
  border: none !important;
  background: none  !important;
}
.btn-info{
    border-radius: 8px !important; 
}
.dt-button .btn{
  line-height:20px;
}
.dt-buttons{
  text-align: right;
  float:right;
  /*position: absolute;*/
}
</style>

 <script>
  function printProfile(){
    var restorepage = document.body.innerHTML;
    $('#print_visible').css('display','block');
    $('table.fee_export_excel').css('box-sizing','border-box');
    $('table.fee_export_excel').css('border-collapse','collapse');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('border','1px solid #ddd');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('position','relative');
    $('.fee_export_excel tr, .fee_export_excel td, .fee_export_excel th').css('padding','10px');
    $('.vertical').css('padding','87px 0px !important');
    $('.fee_export_excel th span').css('transform-origin','0 50%');
    $('.fee_export_excel th span').css('transform','rotate(-90deg)');
    $('.fee_export_excel th span').css('white-space','nowrap');
    $('.fee_export_excel th span').css('display','block');
    $('.fee_export_excel th span').css('position','absolute');
    $('.fee_export_excel th span').css('bottom','0');
    $('.fee_export_excel th span').css('left','50%');
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

  // function exportToExcel_daily(){
  //   var htmls = "";
  //   var uri = 'data:application/vnd.ms-excel;base64,';
  //   var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
  //   var base64 = function(s) {
  //       return window.btoa(unescape(encodeURIComponent(s)))
  //   };

  //   var format = function(s, c) {
  //       return s.replace(/{(\w+)}/g, function(m, p) {
  //           return c[p];
  //       })
  //   };
    
  //   var dataTable = $('#accountreporttable').DataTable();

  //   var tableHtml = "<table border='1'><thead><tr>";
    
  //   $('#accountreporttable thead th').each(function() {
  //       tableHtml += "<th>" + $(this).text() + "</th>"; // Add each header
  //   });
    
  //   tableHtml += "</tr></thead><tbody>";
    
  //   dataTable.rows().every(function() {
  //       var row = this.data(); // Get row data
  //       tableHtml += "<tr>";
        
  //       for (var i = 0; i < row.length; i++) {
  //           tableHtml += "<td>" + row[i] + "</td>"; // Add each cell data
  //       }
        
  //       tableHtml += "</tr>";
  //   });

  //   tableHtml += "</tbody></table>";

  //   var summaryTable = $("#print_visible").html();
  //   var mainTable = $(".day_book_account_tx").html();
  //   var salesTable = $(".day_book_account_sales").html();
   

  //   htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable +'<br><br>' + tableHtml;
  //   var ctx = {
  //     worksheet : 'Spreadsheet',
  //     table : htmls
  //   }

  //   var link = document.createElement("a");
  //   link.download = "export.xls";
  //   link.href = uri + base64(format(template, ctx));
  //   link.click();

  // }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  function exportToExcel_fee_status() {
  var htmls = "";
  var uri = "data:application/vnd.ms-excel;base64,";
  var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="(link unavailable)">' +
    '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
    '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
  var base64 = function (s) {
    return window.btoa(unescape(encodeURIComponent(s)));
  };
  var format = function (s, c) {
    return s.replace(/{(\w+)}/g, function (m, p) {
      return c[p];
    });
  };
  var report_type = $("input[name='report_type']:checked").val();
  var mainTable = report_type == 1 ? $(".fee_balance").clone() : $(".fees_balance_summary").clone();
  var summaryTable = $(".total_summary").clone();
  var hiddenElements = $(".print_hide");
  hiddenElements.removeClass("print_hide");

  mainTable.find(".print_hide").remove();
  mainTable.find("#fee_summary_data_filter").remove();
  mainTable.find("#fee_summary_data_summary_filter").remove();
  mainTable.find(".dt-buttons").remove();
  var titleRow = '';
  htmls = titleRow + summaryTable.prop("outerHTML") + "<br><br>" + mainTable.prop("outerHTML");
  var ctx = { worksheet: "Spreadsheet", table: htmls };
  if (navigator.msSaveOrOpenBlob) {
    var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
    navigator.msSaveOrOpenBlob(blob, "export.xls");
  } else {
    var link = document.createElement("a");
    link.download = "fees balance report.xls";
    link.href = uri + base64(format(template, ctx));
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  hiddenElements.addClass("print_hide");
}

</script>