<?php
class Certificates_controller extends CI_Controller
{

  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_CERTIFICATE')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_CERTIFICATE.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Certificates_Model');
    $this->load->model('student/Student_Model');
    $this->load->library('filemanager');
  }

  public function index() {
    $site_url = site_url();

    $data['tiles'] = array(
      [
        'title' => 'Manage Certificate Templates',
        'sub_title' => 'Manage Certificate Templates',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student/certificates_controller/certificates_index',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_CERTIFICATE.MANAGE_CERTIFICATES_TEMPLATE')
      ],
      [
        'title' => 'Issue Certificate',
        'sub_title' => 'Issue Certificate',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student/certificates_controller/issue_certificate',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_CERTIFICATE.ISSUE_CERTIFICATES')
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Certificate Report',
        'sub_title' => 'Certificate Report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'student/certificates_controller/certificate_report',
        'permission' =>  $this->authorization->isAuthorized('STUDENT_CERTIFICATE.CERTIFICATES_REPORT')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['main_content']    = 'student_certificates/menu';
    $this->load->view('inc/template', $data);
  }

  private function convert_number($number)
  {
    if (($number < 0) || ($number > 999999999)) {
      throw new Exception("Number is out of range");
    }
    $Gn = floor($number / 1000000);
    /* Millions (giga) */
    $number -= $Gn * 1000000;
    $kn = floor($number / 1000);
    /* Thousands (kilo) */
    $number -= $kn * 1000;
    $Hn = floor($number / 100);
    /* Hundreds (hecto) */
    $number -= $Hn * 100;
    $Dn = floor($number / 10);
    /* Tens (deca) */
    $n = $number % 10;
    /* Ones */
    $res = "";
    if ($Gn) {
      $res .= $this->convert_number($Gn) .  "Million";
    }
    if ($kn) {
      $res .= (empty($res) ? "" : " ") . $this->convert_number($kn) . " Thousand";
    }
    if ($Hn) {
      $res .= (empty($res) ? "" : " ") . $this->convert_number($Hn) . " Hundred";
    }
    $ones = array("", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen");
    $tens = array("", "", "Twenty", "Thirty", "Fourty", "Fifty", "Sixty", "Seventy", "Eigthy", "Ninety");
    if ($Dn || $n) {
      if (!empty($res)) {
        $res .= " and ";
      }
      if ($Dn < 2) {
        $res .= $ones[$Dn * 10 + $n];
      } else {
        $res .= $tens[$Dn];
        if ($n) {
          $res .= " " . $ones[$n];
        }
      }
    }
    if (empty($res)) {
      $res = "zero";
    }
    return $res;
  }

  //Landing function to Print Certificates
  public function print_certificates($stdId, $flag = 0)
  { if ($flag == 1){
    $this->session->set_flashdata('flashSuccess', 'Certificate generated.');

  }
    $data['student_uid'] = $stdId;
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['certificates'] = $this->Certificates_Model->getCertificates($stdId);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 
    // $url = $this->filemanager->getFilePath($data['certificates']);
    // $data['base_path'] = file_get_contents($url);
    // echo "<pre>"; print_r($data['certificates']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/certificates/student_certificates_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/certificates/student_certificates_mobile';
    }else{
      $data['main_content']    = 'student/certificates/student_certificates';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function get_student_issued_certificates(){
    $_string_stdId=$this->input->post();
    if(strpos($_string_stdId['studentId'], '_') !== false){
      $splitStudent =  explode('_', $_string_stdId['studentId']);
      $staff_student =  explode(',', $splitStudent[1]);
      $stdId = $staff_student[0];
    }else{
      $stdId=$this->input->post('studentId');
    }

    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($stdId);
    $data['certificates'] = $this->Certificates_Model->getCertificates($stdId);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps')?$this->settings->getSetting('display_names_caps'):0; 

    echo json_encode($data);

  }

  public function get_student_issued_certificates_admission_no(){
    $data['certificates'] = $this->Certificates_Model->getCertificates_admission_no();
    $data['student_id'] = $this->Certificates_Model->getStudent_id_admission_no();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_id']);

    echo json_encode($data);

  }

  public function generate_ceritificates($stdId)
  { 
    $data['school_logo'] =  $this->Certificates_Model->get_school_logo();

    //echo "<pre>"; print_r($data); die();
    $data['student_uid'] = $stdId;
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['stdFatherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Father", array());
    $data['stdMotherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Mother", array());
    //echo "<pre>"; print_r($data['stdData']); die();
    $a = date("d-m-Y", strtotime($data['stdData']->dob));
    $date = explode("-", $a);
    $d = $this->convert_number($date[0]);
    $m = $this->convert_number($date[1]);
    $y = $this->convert_number($date[2]);
    $data['dob_in_words'] = $d . ' - ' . $m . ' - ' . $y;
    $time = time();

    $year = date('Y', $time);
    if (date('n', $time) < 6)
      $ayear = ($year - 1) . '/' . $year;
    else
      $ayear = ($year) . '/' . ($year + 1);
    $data['academic_year'] = $ayear;
    // $data['pretemplates'] = $this->Certificates_Model->getPreTemplatesList();
    // $data['custemplates'] = $this->Certificates_Model->getCusTemplatesList();
    $data['certificates'] = $this->Certificates_Model->getCertificates_all();
    //echo  "<pre>"; print_r($data['certificates']);die();
    $data['main_content']    = 'student/certificates/certificates';
    $this->load->view('inc/template', $data);
  }

  public function view_issued_ceritificates($stdId, $scId)
  {
    $data['issued_certificate'] = $this->Certificates_Model->get_issued_certificates_template($stdId, $scId);
    $data['main_content']    = 'student/certificates/student_certificate/view_certificate';
    $this->load->view('inc/template', $data);
  }


  //VISA Letter form
  public function visa_letter($stdId)
  {
    $data['student_uid'] = $stdId;
    $data['main_content']    = 'student/certificates/visa_letter_form';
    $this->load->view('inc/template', $data);
  }

  //Get VISA Letter Details
  public function create_visa_letter()
  {
    $data['student_uid'] = $this->uri->segment(4);
    //echo "<pre>"; print_r($data['student_uid']); die();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['stdFatherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Father", array());
    $data['stdMotherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Mother", array());
    $data['closed_for'] = $this->input->post('closed_for');
    $data['closed_from'] = $this->input->post('closed_from');
    $data['closed_to'] = $this->input->post('closed_to');
    $data['reopen_after'] = $data['closed_for'];
    $data['reopen_on'] = date('Y-m-d', strtotime($data['closed_to'] . ' + 1 day'));
    $a = date("d-m-Y", strtotime($data['stdData']->dob));
    $date = explode("-", $a);
    $d = $this->convert_number($date[0]);
    $m = $this->convert_number($date[1]);
    $y = $this->convert_number($date[2]);
    $data['dob_in_words'] = $d . ' - ' . $m . ' - ' . $y;
    // echo "<pre>"; print_r($data['reopen_after']); die();

    $time = time();

    $year = date('Y', $time);
    if (date('n', $time) < 6)
      $ayear = ($year - 1) . '/' . $year;
    else
      $ayear = ($year) . '/' . ($year + 1);
    $data['academic_year'] = $ayear;
    //echo "<pre>"; print_r($data['academic_year']); die();
    $data['main_content']    = 'student/certificates/visa_letter';
    $this->load->view('inc/template', $data);
  }


  public function template_form($stdId, $template_id = 0)
  {
    // $data['student_uid'] = $this->uri->segment(4);
    $data['student_uid'] = $stdId;
    $data['template_id'] = $template_id;
    $data['copy_content'] = '';
    if ($template_id) {
      $data['copy_content'] = $this->Certificates_Model->getTemplateContent($template_id);
    }
    //echo "<pre>"; print_r($data['student_uid']); die();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['main_content']    = 'student/certificates/templates/create_template';
    $this->load->view('inc/template', $data);
  }


  public function create_template($stdId)
  {
    $data['student_uid'] = $stdId;
    $status = $this->Certificates_Model->create_cert_template();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Template Created Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('student/Certificates_controller/print_certificates/' . $data['student_uid']);
  }
  //Get Template
  public function template($stdId, $tempId)
  {
    $data['school_logo'] =  $this->Certificates_Model->get_school_logo();
    $data['school_seal'] =  $this->Certificates_Model->get_school_seal();
    $data['signature'] =  $this->Certificates_Model->get_signature();
    // echo "<pre>"; print_r( $data['school_seal']); die();
    $data['student_uid'] = $stdId;
    $data['template_id'] = $tempId;
    $data['template'] = $this->Certificates_Model->getTemplate($data['template_id']);
    preg_match_all("/%%.*?%%/", $data['template']->html_content, $matched);
    $data['placeholders'] = array_unique($matched[0]);
    $data['manualfields'] = [];
    $data['disable_for_edit_fields']=['admission_no','student_name','father_name','mother_name','nationality','caste','dob','dob_in_words','class_name'];
    foreach ($matched as $match) {
      foreach ($match as $matchStr) {
        $value = str_replace("%%", "", $matchStr);
        $name = ucwords(str_replace("_", " ", $value));
        $data['manualfields'][] = ['name' => $name, 'value' => $value];
      }
    }
    $data['acad_year'] = $this->acad_year->getAcadYear();
    // echo "<pre>"; print_r($data['template']); die();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    if($data['stdData']->admission_type == 1){
      $data['stdData']->admission_type = 'Re-Admission';
    }else if($data['stdData']->admission_type == 2){
      $data['stdData']->admission_type = 'New Admission';
    }
    $data['std_adm_data'] = $this->Certificates_Model->get_std_adm_data($data['student_uid']);
    $data['std_fees_data'] = $this->Certificates_Model->getFullStudentFeesDataById($data['student_uid']);
    $data['first_joined_class'] = $this->Certificates_Model->get_first_joined_class($data['student_uid']);
    $data['stdFatherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Father");
    $data['stdMotherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Mother");
    $data['stdGuardianData'] = $this->Certificates_Model->getGuardianDataById($data['student_uid'], "Guardian");
    $data['student_documents'] = $this->Certificates_Model->getFullStudentDocumentDataById($data['student_uid']);
    $data['student_previous'] = $this->Certificates_Model->getFullStudentPreviousSchoolDataById($data['student_uid']);
    $data['application_no'] = $this->Certificates_Model->get_application_no_from_application_form($data['student_uid']);
    $data['admission_from_data'] = $this->Certificates_Model->get_admission_form_data($data['student_uid']);
    $data['staff_id'] = $this->authorization->getAvatarId();
    $data['get_issued_by'] = $this->avatar->getAvatarById($data['staff_id']);
    $data['custom_fields'] = $this->settings->getSetting('student_admission_custom_fields');
    //echo "<pre>"; print_r($data['get_issued_by']); die();
    $a = date("d-F-Y", strtotime($data['stdData']->dob));
    $date = explode("-", $a);
    $d = $this->convert_number($date[0]);
    $m = $date[1];
    $y = $this->convert_number($date[2]);
    $data['dob_in_words'] = $d . ' - ' . $m . ' - ' . $y;
    // echo "<pre>"; print_r($data); die();

    $time = time();

    $year = date('Y', $time);
    if (date('n', $time) < 6)
      $ayear = ($year - 1) . '/' . $year;
    else
      $ayear = ($year) . '/' . ($year + 1);
    $data['academic_year'] = $ayear;

    // echo "<pre>"; print_r($data); die();
    $data['date_format']=$data['template']->date_format ? $data['template']->date_format : 'd-m-Y';

    $data['main_content']    = 'student/certificates/templates/template';
    $this->load->view('inc/template', $data);
  }

  public function update_template($stdId)
  {
    $template_id = $this->input->post('template_id');
    $status = $this->Certificates_Model->update_cert_template();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Template Created Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('student/Certificates_controller/template/' . $stdId . '/' . $template_id);
  }

  public function save_andprint_certificates()
  {
    echo $this->Certificates_Model->save_certificate_for_students();
  }
  public function certificates()
  {
    $data['preCertificates'] = $this->Certificates_Model->getPreTemplatesList();
    $data['customCertificates'] = $this->Certificates_Model->getCusTemplatesList();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/certificates/index_new_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/certificates/index_new_mobile';
    }else{
      $data['main_content']    = 'student/certificates/index_new';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function certificates_index()
  {
    $data['preCertificates'] = $this->Certificates_Model->getPreTemplatesList();
    $data['customCertificates'] = $this->Certificates_Model->getCusTemplatesList();
    $data['title'] = 'Manage Certificate Templates';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else{
      $data['main_content']    = 'student/certificates/certificates_index_view';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function issue_certificate($id=''){
    $data['student_id']=$id;
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['all_names'] = $this->Certificates_Model->get_all_staff_student_names_approved_only();
    $data['title'] = 'Issue Certificate';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else{
      $data['main_content']    = 'student/certificates/issue_certificate_view';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function certificate_report(){
    $data['classes'] = $this->Student_Model->get_class_section_names();
    $data['templates'] = $this->Student_Model->get_certificate_templates();
    $data['title'] = 'Certificate Report';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/certificates/certificate_mobile_tablet';
    }else{
      $data['main_content']    = 'student/certificates/certificate_report_view';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function certificate($id){
    $data['certificate'] = $this->Certificates_Model->getCertificateTemplate($id);
    // echo '<pre>'; print_r($data); die();
    $data['main_content']    = 'student/certificates/view_certificate';
    $this->load->view('inc/template', $data);
  }

  public function create_certificate($id = 0){
    $data['isEdit'] = false;
    $data['certificate'] = array();
    if ($id) {
      $data['certificate'] = $this->Certificates_Model->getCertificateTemplate($id);
      $data['isEdit'] = true;
    }
    $data['reciept_book']=  $this->Certificates_Model->getAllRecieptBook();
    $data['staff']=  $this->Certificates_Model->get_all_staff();
    $data['main_content'] = 'student/certificates/templates/add_certificate';
    $data['accessable_by_staff']='';
    if($id != 0){
      $data['accessable_by_staff']= $this->Certificates_Model->get_staff_accessable_template($id);
    }
    // echo "<pre>"; print_r($data['accessable_by_staff']); die();
    $this->load->view('inc/template', $data);
  }

  public function new_certificate()
  {
   
    $status = $this->Certificates_Model->addCertificate();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Certificate Created Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('student/Certificates_controller/certificates_index');
  }

  public function update_certificate($id)
  {
    $status = $this->Certificates_Model->updateCertificate($id);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Certificate Updated Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('student/Certificates_controller/certificate/' . $id);
  }

  public function clone_certificate($id)
  {
    $data['cloning'] = true;
    $data['isEdit'] = false;
    $data['certificate'] = $this->Certificates_Model->getCertificateTemplate($id);
    $data['reciept_book']=  $this->Certificates_Model->getAllRecieptBook();
    $data['staff']=  $this->Certificates_Model->get_all_staff();

    $data['main_content']    = 'student/certificates/templates/add_certificate';
    $this->load->view('inc/template', $data);
  }

  public function generate_certificates()
  {
    $input = $this->input->post();
    if (!empty($input)) {
      $data['std_certificate'] = $this->Certificates_Model->search_std_class_wise_certificate($input['classId'], $input['certificate_id']);
    } else {
      $data['std_certificate'] = array();
    }
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['preCertificates'] = $this->Certificates_Model->getALLTemplatesList();
    $data['main_content']    = 'student/certificates/student_certificate/index';
    $this->load->view('inc/template', $data);
  }

  public function get_student_details_for_certificate()
  {
    $mode = $_POST['mode'];
    $certificate_id = $_POST['certificate_id'];
    switch ($mode) {
      case 'class':
        $classId = $_POST['classId'];
        $stdData = $this->Certificates_Model->search_std_class_wise_certificate($classId, $certificate_id);
        break;
      case 'ad_no':
        $adNo = $_POST['ad_no'];
        $stdData = $this->Certificates_Model->search_std_adm_no_wise_certificate($adNo, $certificate_id);
        break;
    }
    echo json_encode($stdData);
  }

  public function generate_pdf_certificate(){
    $inp= isset($_POST['manual_feilds_array']) ? $_POST['manual_feilds_array'] :'';
    $obj= new stdClass();
    if(!empty($inp)){
      foreach($inp as $k =>$v) {
        foreach($v as $key => $val) {
          $obj->$key=$val;
        }
      }
    }
    

    $edited_data = json_encode($obj);
    $certificate_data = $_POST['certificate_data'];
    $template_id = $_POST['template_id'];
    $student_id = $_POST['student_id'];
    $receipt_number_id = $_POST['receipt_number_id'];
    $issued_by = $this->authorization->getAvatarId();
    
    $school = CONFIG_ENV['main_folder'];
    $path = $school . '/certificates/' . uniqid() . '-' . time() . ".pdf";

    if(! empty($receipt_number_id)){
      $reciept_number = $this->Certificates_Model->get_reciept_number_certificate($receipt_number_id);
    }else{
      $reciept_number=0;
    }
    if($reciept_number){
      $certificate_data = str_replace('%%reference_number%%', '<span>' . $reciept_number . '</span>', $certificate_data);
    }else{
      $certificate_data = str_replace('%%reference_number%%', '', $certificate_data);
    }
    $bucket = $this->config->item('s3_bucket');
    // $status = $this->Certificates_Model->updateCertificatePath($path, $certificate_data);
    $this->db->insert('student_certificate', ['certificate_id' => $template_id, 'student_id' => $student_id, 'issued_by' => $issued_by , 'publish_status' => 'ISSUED',  'pdf_path' => $path, 'pdf_status' => 0,  'edited_data' => $edited_data, 'certificate_data' => $certificate_data,'receipt_number' => $reciept_number  ]);
    // $this->db->where('id', $certificate_id)->update('student_certificate', ['path' => $path, 'pdf_status' => 0]);
    $certificate_id =  $this->db->insert_id();
       //echo $certificate_id; die();
    if(! empty($certificate_id) && $receipt_number_id!=0){
      $this->Certificates_Model->certificate_update_running_number($receipt_number_id);
    }
   
    $curl = curl_init();
    $postData = urlencode($certificate_data);
    
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url() . 'Callback_Controller/updateCertificatePdfLink';
    
    
    



    curl_setopt_array($curl, array(
      CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => "",
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_USERPWD => $username . ":" . $password,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => "POST",
      CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&data=" . $postData . "&return_url=" . $return_url,
      CURLOPT_HTTPHEADER => array(
        "Accept: application/json",
        "Cache-Control: no-cache",
        "Content-Type: application/x-www-form-urlencoded",
        "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
      ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    // echo $curl; die();

    // $pdf_link = $this->Certificates_Model->getPDFLink($certificate_id);
    echo json_encode($this->filemanager->getFilePath($path));

    //redirect('student/Certificates_controller/generate_certificates');


  }

  public function _create_template_certificates_student($stdData, $stdFatherData, $stdMotherData, $stdAddress, $template, $acad_year)
  {
    $stdImage =  ($stdData->picture_url == '') ? $this->config->item('images')['male'] : $this->filemanager->getFilePath($stdData->picture_url);
    $todayDate = date('d-m-Y');
    $header = '';
    $footer = '';
    $header = '<html><body>';
    $template = str_replace('%%student_name%%', $stdData->stdName, $template);
    $template = str_replace('%%student_name%%', $stdData->stdName, $template);
    $template = str_replace('%%academic_year%%', $acad_year, $template);
    $template = str_replace('%%class%%', $stdData->className . '/' . $stdData->sectionName, $template);
    $template = str_replace('%%student_image%%', $stdImage, $template);
    $template = str_replace('%%father_name%%', $stdFatherData->pName, $template);
    $template = str_replace('%%address%%', $stdAddress->address, $template);
    $template = str_replace('%%date%%', $todayDate, $template);
    $footer = '</body></html>';
    $a = $header . $template . $footer;
    return $a;
  }

  // private function __generateCertifcate_pdf_certificate($html, $certificate_id, $stdId)
  // {
    // $school = CONFIG_ENV['main_folder'];
    // $path = $school . '/certificates/' . uniqid() . '-' . time() . ".pdf";

    // $bucket = $this->config->item('s3_bucket');

    // $status = $this->Certificates_Model->updateCertificatePath($path, $certificate_id, $stdId);
    // $page = 'portrait';
    // $page_size = 'A4';
    // $curl = curl_init();
    // $postData = urlencode($html);
    // $username = CONFIG_ENV['job_server_username'];
    // $password = CONFIG_ENV['job_server_password'];
    // $return_url = site_url() . 'Callback_Controller/updateCertificatePdfLink';

    // curl_setopt_array($curl, array(
    //   CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
    //   CURLOPT_RETURNTRANSFER => true,
    //   CURLOPT_ENCODING => "",
    //   CURLOPT_MAXREDIRS => 10,
    //   CURLOPT_TIMEOUT => 30,
    //   CURLOPT_USERPWD => $username . ":" . $password,
    //   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //   CURLOPT_CUSTOMREQUEST => "POST",
    //   CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
    //   CURLOPT_HTTPHEADER => array(
    //     "Accept: application/json",
    //     "Cache-Control: no-cache",
    //     "Content-Type: application/x-www-form-urlencoded",
    //     "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
    //   ),
    // ));

    // $response = curl_exec($curl);
    // $err = curl_error($curl);
    // curl_close($curl);
  // }


  // public function download_certifcate_per_student($id)
  // {
  //   $link = $this->Certificates_Model->download_Ceritifcate($id);
  //   $url = $this->filemanager->getFilePath($link);
  //   $data = file_get_contents($url);
  //   $this->load->helper('download');
  //   force_download('Certificate.pdf', $data, TRUE);
  // }
  
  public function pdf_download($certificate_id, $template_name, $student_name){
    $link = $this->Certificates_Model->getPDFLink($certificate_id);
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $cert_name = $template_name. ' of '. $student_name.'.pdf';
    // echo "data".$data; die();
    $this->load->helper('download');
    force_download($cert_name, $data, TRUE);
  }


  public function delete_certificate($certificate_id){
    // echo "hi";
    $result = $this->Certificates_Model->delete_certificate($certificate_id);
  }


  public function inactive_certificate($certificate_id){
    // echo "hi";
    $result = $this->Certificates_Model->inactive_certificate($certificate_id);
  }


  public function active_certificate($certificate_id){
    // echo "hi";
    $result = $this->Certificates_Model->active_certificate($certificate_id);
  }

  public function update_issued_certificate($tempid){
    $data['issued_data'] =  $this->Certificates_Model->get_issued_Certificates($tempid);
    $data['student_uid'] = $data['issued_data']->student_id;
    $data['date_format']=$data['issued_data']->date_format ? $data['issued_data']->date_format : 'd-m-Y';
    $data['revised_certificate']=false;

    //echo "<pre>"; print_r($data['issued_data']); die();
    preg_match_all("/%%.*?%%/", $data['issued_data']->html_content, $matched);
    $data['manualfields'] = [];
    foreach ($matched as $match) {
      foreach ($match as $matchStr) {
        $value = str_replace("%%", "", $matchStr);
        $name = ucwords(str_replace("_", " ", $value));
        $data['manualfields'][] = ['name' => $name, 'value' => $value];
      }
    }
    $data['acad_year'] = $this->acad_year->getAcadYear();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['std_fees_data'] = $this->Certificates_Model->getFullStudentFeesDataById($data['student_uid']);
    $data['first_joined_class'] = $this->Certificates_Model->get_first_joined_class($data['student_uid']);
    if(empty($data['first_joined_class'])){
      $data['first_joined_class']='';
    }
    $data['stdFatherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Father");
    $data['stdMotherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Mother");
    $data['stdGuardianData'] = $this->Certificates_Model->getGuardianDataById($data['student_uid'], "Guardian");
    $data['student_documents'] = $this->Certificates_Model->getFullStudentDocumentDataById($data['student_uid']);
    $data['student_previous'] = $this->Certificates_Model->getFullStudentPreviousSchoolDataById($data['student_uid']);
    $data['application_no'] = $this->Certificates_Model->get_application_no_from_application_form($data['student_uid']);
    $data['admission_from_data'] = $this->Certificates_Model->get_admission_form_data($data['student_uid']);
    $data['disable_for_edit_fields']=['admission_no','student_name','father_name','mother_name','nationality','caste','dob','dob_in_words','class_name'];
    // echo "<pre>"; print_r($data['stdGuardianData']); die();
    $a = date("d-F-Y", strtotime($data['stdData']->dob));
    $date = explode("-", $a);
    $d = $this->convert_number($date[0]);
    $m = $date[1];
    $y = $this->convert_number($date[2]);
    $data['dob_in_words'] = $d . ' - ' . $m . ' - ' . $y;
    // echo "<pre>"; print_r($data); die();

    $time = time();

    $year = date('Y', $time);
    if (date('n', $time) < 6)
      $ayear = ($year - 1) . '/' . $year;
    else
      $ayear = ($year) . '/' . ($year + 1);
    $data['academic_year'] = $ayear;
    $data['main_content']    = 'student/certificates/templates/re_template';

    $this->load->view('inc/template', $data);

  }

  public function get_certificate_report(){
    $result=$this->Certificates_Model->get_certificate_report();
    echo json_encode($result);
  }

  public function toggle_parent_visibility(){
    $result=$this->Certificates_Model->toggle_parent_visibility();
    echo json_encode($result);
  }

  public function toggle_pdf_status(){
    $result=$this->Certificates_Model->toggle_pdf_status();
    echo json_encode($result);
  }

  public function download_all_certificate_report(){
    $result=$this->Certificates_Model->get_certificate_report();
    $url_paths_arr = [];
    if(!empty($result)){
        foreach($result as $individual_student){
            $url_paths= new stdClass();
            $url = $individual_student->pdf_path;
            $url_paths->url= $url;
            $url_paths->filename= $individual_student->name.' certificate.pdf';
            $url_paths_arr[] = $url_paths;
        }
    }
    echo json_encode($url_paths_arr);
  }

  public function reissue_certificate_new($tempid) {
 
    $data['issued_data'] =  $this->Certificates_Model->get_issued_Certificates($tempid);
    $data['date_format']=$data['issued_data']->date_format ? $data['issued_data']->date_format : 'd-m-Y';
    // templateA contains edited data
    // templateB contains new template(original)
    $original = $data['issued_data']->html_content;
    $old_template = $data['issued_data']->certificate_data;
    
    // Create new_issued by selectively replacing placeholders
    $new_issued = $original; // Start with original template
    // Extract all placeholders from original template
    preg_match_all('/id="([^"]+)"[^>]*>(.*?)<\/(?:span|div|p|td|label|b)>/s', $old_template, $matches, PREG_SET_ORDER);
    
    foreach ($matches as $match) {

        $field = $match[1];
        $value = preg_replace('/\s+/', ' ', $match[2]); // Replace newlines/tabs with space
        $value = trim(ltrim($value, ': ')); // Remove leading colon and extra space
        
        // Only replace if value is not empty
        if (!empty($value)) {
            $placeholder = "%%{$field}%%";
            $new_issued = str_replace($placeholder, $value, $new_issued);
        }
    }
    $data['issued_data']->certificate_data = $new_issued;
    $data['student_uid'] = $data['issued_data']->student_id;
    $data['revised_certificate']=true;
    preg_match_all("/%%.*?%%/", $data['issued_data']->html_content, $matched);
    $data['manualfields'] = [];
    foreach ($matched as $match) {
      foreach ($match as $matchStr) {
        $value = str_replace("%%", "", $matchStr);
        $name = ucwords(str_replace("_", " ", $value));
        $data['manualfields'][] = ['name' => $name, 'value' => $value];
      }
    }
    $data['acad_year'] = $this->acad_year->getAcadYear();
    $data['stdData'] = $this->Certificates_Model->getFullStudentDataById($data['student_uid']);
    $data['std_fees_data'] = $this->Certificates_Model->getFullStudentFeesDataById($data['student_uid']);
    $data['first_joined_class'] = $this->Certificates_Model->get_first_joined_class($data['student_uid']);
    if(empty($data['first_joined_class'])){
      $data['first_joined_class']='';
    }
    $data['stdFatherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Father");
    $data['stdMotherData'] = $this->Certificates_Model->getParentDataById($data['student_uid'], "Mother");
    $data['stdGuardianData'] = $this->Certificates_Model->getGuardianDataById($data['student_uid'], "Guardian");
    $data['student_documents'] = $this->Certificates_Model->getFullStudentDocumentDataById($data['student_uid']);
    $data['student_previous'] = $this->Certificates_Model->getFullStudentPreviousSchoolDataById($data['student_uid']);
    $data['application_no'] = $this->Certificates_Model->get_application_no_from_application_form($data['student_uid']);
    $data['admission_from_data'] = $this->Certificates_Model->get_admission_form_data($data['student_uid']);
    $data['disable_for_edit_fields']=['admission_no','student_name','father_name','mother_name','nationality','caste','dob','dob_in_words','class_name'];
    
    $a = date("d-F-Y", strtotime($data['stdData']->dob));
    $date = explode("-", $a);
    $d = $this->convert_number($date[0]);
    $m = $date[1];
    $y = $this->convert_number($date[2]);
    $data['dob_in_words'] = $d . ' - ' . $m . ' - ' . $y;

    $time = time();

    $year = date('Y', $time);
    if (date('n', $time) < 6)
      $ayear = ($year - 1) . '/' . $year;
    else
      $ayear = ($year) . '/' . ($year + 1);
    $data['academic_year'] = $ayear;
    $data['main_content']    = 'student/certificates/templates/re_template';

    $this->load->view('inc/template', $data);
  }

}
