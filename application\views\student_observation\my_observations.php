<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student_observation/menu');?>">Student Observation Menu</a></li>
  <li>My Observations</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0" style="margin-top: 5px;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student_observation/menu') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a> 
            My Observations
          </h3>
        </div>
        <div class="col-md-3 pr-0">
          <ul class="panel-controls">
            <li>
              <a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target="#resource-uploader">
                <span class="fa fa-plus" style="font-size: 19px;"></span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="col-md-3 form-group">
      <label for="fromdateId" class="control-label">Select Date range</label>
          <div id="reportrange" class="dtrange custom-select " style="width:  100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>
        
        <div class="col-md-2 form-group pt-3" style="padding-top: 0rem!important;">
        <label class="control-label">Select Class</label>
            <select multiple="" class="form-control select " title="All" name="class_section_id" id="class_section_id" multiple>
              <?php foreach ($sectionList as $value) { ?>
                <option value="<?php echo $value->id; ?>"><?php echo $value->class_name; ?></option>
              <?php }?>
            </select>
        </div>
      <div class="col-md-2 form-group pt-3">
          <button id="get_button" class="btn btn-primary mt-3" onclick="get_single_staff_observation_data()">Get</button>
      </div>
      <div id="observation_table"class="panel-body table-responsive hidden-xs">
      </div>
    </div>
    
  </div>

  <div class="modal fade" id="resource-uploader" tabindex="-1" role="dialog" style="width:auto;margin:auto;top:7%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">

      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Add Student Observation</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>

      <div class="modal-body">
        <form enctype="multipart/form-data" method="post" class="form-horizontal" id="input_form">
        <div class="col-md-6" style="white-space: nowrap">
            <div class="form-group " style="padding-bottom: 8px;">
              <label class="control-label col-md-3" for="section_id" style="margin-right: 13px;">Section <font color="red">*</font></label>
              <div class="col-md-8">
                <select id="section_id" name="section_id" class="form-control input-md select" required>
                    <option value=""><?php echo 'Select Section ' ?></option>
                    <?php foreach ($sectionList as $value) { ?>
                        <option value="<?php echo $value->id; ?>"><?php echo $value->class_name; ?></option>
                    <?php }?>
                </select>
              </div>
            </div>
            <div class="form-group" style="padding-bottom: 8px;">
              <label class="control-label col-md-3" for="student_id" style="margin-right: 13px;">Student <font color="red">*</font></label>
              <div class="col-md-8">
                <select title="Select Student" name="student_name[]" id="student_id" class="form-control select" required multiple>
                </select>
              </div>
            </div>

            <div class="form-group " style="padding-bottom: 8px;">
              <label class="control-label col-md-3" for="category_id" style="margin-right: 13px;">Category <font color="red">*</font></label>
              <div class="col-md-8">
              <select id="category_id" name="category_id" class="form-control input-md select" required onchange="fill_remarks(this.value)">
                  <option value=""><?php echo 'Select Category ' ?></option>
                  <?php foreach ($category_types as $value) { ?>
                      <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
                  <?php }?>
              </select>
              </div>
            </div>

              <div class="form-group " style="padding-bottom: 8px;">
                <label for="observation_date" class="control-label col-md-3" style="margin-right: 13px;">Observed Date & Time <font color="red">*</font></label>
                <div class="col-md-8">
                  <div class="input-group date" id="ob_date_picker"> 
                    <input style="flex: none;" autocomplete="off" type="datetime-local" class="form-control" id="observation_date" name="date"  required>
                      <!-- <span class="input-group-addon">
                          <span class="glyphicon glyphicon-calendar"></span>
                      </span> -->
                  </div>
                </div>
              </div>

              <div class="form-group " style="padding-bottom: 8px;">
                  <label class="control-label col-md-3" for="staff_capacity_id" style="margin-right: 13px;">Observed as <font color="red">*</font></label>
                  <div class="col-md-8">
                    <select id="staff_capacity_id" name="staff_capacity_id" class="form-control input-md select" required>
                        <option value=""><?php echo 'Select type ' ?></option>
                        <?php foreach ($capacity_types as $value) { ?>
                            <option value="<?php echo $value->id; ?>"><?php echo $value->name; ?></option>
                        <?php }?>
                    </select>
                  </div>
                </div>

                <div class="form-group" style="padding-bottom: 8px;">
                  <label class="control-label col-md-3" for="predefined_observation" style="margin-right: 13px;">Predefined observation <font color="red">*</font></label>
                  <div class="col-md-8">
                    <select title="Select predefined observation" name="predefined_observation" id="predefined_observation" class="form-control select" onchange="fill_observation_remarks(this.value)" >
                    </select>
                  </div>
                </div>

              </div>
              <div class="col-md-6" style="white-space: nowrap">
                <div class="form-group" >
                  <label class="control-label col-md-3" for="observation" style="margin-right: 13px; margin: top 10px;">Observations <font color="red">*</font></label>
                  <div class="col-md-8">
                    <textarea name="observation" placeholder="Enter Observation" id="observation" class="form-control input-md" rows="4"  required></textarea>
                  </div>
                </div>

                <div class="form-group" >
                  <label class="control-label col-md-3" for="action_taken" style="margin-right: 13px; margin: top 10px;">Action Taken</label>
                  <div class="col-md-8" style="padding-bottom: 8px;">
                    <textarea name="action_taken" placeholder="Enter Action Taken" id="action_taken" class="form-control input-md" rows="4"></textarea>
                  </div>
                </div>

                <div class="form-group" >
                  <label class="control-label col-md-3" for="file_input" style="margin-right: 13px; margin: top 10px;">File upload</label>
                  <div class="col-md-8" style="padding-bottom: 8px;">
                    <input name="file_input" id="file_input" class="form-control input-md" style="padding:3px" type="file">
                  </div>
                </div>  
                
        </form>
      </div>
    </div> 

        
      <div class="modal-footer">
            <button class="btn btn-primary" id="add_button" type="button" onclick="add_single_student_observation()">Submit</button>
      </div>

    </div>
  </div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
  
  
  let btn_status;
  let submit_btn;
  $(document).ready(function() {
    btn_status=document.getElementById("get_button");
    btn_status.disabled = true;
    btn_status.textContent="Please wait...";

    $("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD-MM-YYYY',
    separator: ' to ',
    startDate: moment().subtract(6, 'days'),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
});

$("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
$('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
$('#to_date').val(moment().format('DD-MM-YYYY'));

get_single_staff_observation_data();

});

$("#section_id").change(function(){
  var section_id = $("#section_id").val();
  $.post("<?php echo site_url('student_observation/observation/get_student');?>",{section_id:section_id},function(data){
    var resultData=$.parseJSON(data);
    var output1='';
    var stdName = resultData.stdname;

       for (var i=0,j=stdName.length; i < j; i++) {
         output1+='<option value="'+stdName[i].id+'">'+stdName[i].std_name+' </option>';
       }
        $("#student_id").html(output1);
        $('#student_id').selectpicker('refresh');
    });
});

function get_single_staff_observation_data(){
    btn_status.disabled = true;
    btn_status.textContent="Please wait...";
  var from_date = $("#from_date").val();
	var to_date = $("#to_date").val();
  var class_section_id = $("#class_section_id").val();
  // console.log(from_date);
  // console.log(to_date);
  // console.log(class_section_id);
  $("#observation_table").html(`<center><div class="spinner-border" role="status">
  <span class="sr-only">Loading...</span>
  </div></center>`);
  
  $.ajax({
    url: '<?php echo site_url('student_observation/observation/get_single_staff_observation_data'); ?>',
    type: 'post',
    data: {'from_date':from_date,'to_date':to_date,'class_section_id':class_section_id} ,
    success: function(data) {
      btn_status.disabled = false;
      btn_status.textContent = "Get";
      parsed_data = $.parseJSON(data);
      var observation_data = parsed_data.observation_data;
      html = construct_observation_table(observation_data);
      $("#observation_table").html(html);
      
      const reportName=`Student_Observation ${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

      $('#observation_type_table').DataTable( {
        ordering:false,
        paging : true,
        scrollY :'40vh',
        responsive: true,
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
						"pageLength": 10,
				dom: 'lBfrtip',
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'print',
					text: 'Print',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName,
					className: 'btn btn-info'
					}
				]
        	});

    },
    error: function (err) {
        console.log(err);
    }
  });
}

function construct_observation_table(observation_data){
  var html ='';

  if (observation_data.length == 0) {   
    html += '<h4>No Observations Added</h4>';
  }
  else{

    html += `
    <table id="observation_type_table" class="table table-bordered display responsive">
            <thead>
              <tr style="white-space: nowrap">
                <th>#</th>
                <th>Student Name</th>
                <th>Class</th>
                <th>Observation Date & time</th>
                <th>Category</th>
                <th>Observation</th>
                <th>Created On</th>
                <th>Action taken</th>
                <th>Status</th>
                <th>File Input</th>
              </tr>
            </thead>
    
    `;
    html += `<tbody>`;
    for(var i=0;i<observation_data.length;i++){
      var data = observation_data[i];
      // console.log(data['file_input']);
      html+=`
              <tr>
                <td>${i+1}</td>
                <td>${data['std_name']}</td>
                <td>${data['class_name_']}</td>
                <td>${data['observation_date']}</td>
                <td style="background:${data['color_code']}">${data['category']}</td>
                <td>${data['observation']}</td>
                <td>${data['created_on']}</td>
                <td>${data['action_taken'] || '-'}</td>
                <td>${data['status']}</td>`;
                if(data['file_input'] ){
                  html+=`<td><a target="_blank" href="${data['file_input']}">File</a></td>`;
                }else{
                  html+=`<td style="text-align:center"> - </td>`;
                }
                html+=`</tr>`;
    }
    html+=`</tbody>
        </table>`;
  }
  return html;
}
window.addEventListener('load', () => {
  var now = new Date();
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset());

  now.setMilliseconds(null)
  now.setSeconds(null)

  document.getElementById('observation_date').value = now.toISOString().slice(0, -1);
});
function add_single_student_observation(){
  var $form = $('#input_form');
        if (!$form.parsley().validate()){
          return false;
        }
        submit_btn=document.getElementById("add_button");
        submit_btn.disabled = true;
        submit_btn.textContent="Please wait...";
const form = $('#input_form')[0];
const formData = new FormData(form);
$.ajax({
    url: '<?php echo site_url('student_observation/observation/add_single_student_observation'); ?>',
    type: 'post',
    data:formData,
    processData: false,
    contentType: false,
    cache: false,
    success: function(data) {
      submit_btn.disabled = false;
      submit_btn.textContent="Submit";
        parsed_data = $.parseJSON(data);
        if(parsed_data){
          Swal.fire({
              // position: "top-end",
              icon: "success",
              title: "New Observation has been added",
              showConfirmButton: false,
              timer: 1500
            });
          setTimeout(()=>{
            location.reload();
          },1503);
        }
        else{
        console.log(err);
        }
        
    
    
    },
    error: function (err) {
        console.log(err);
    }
});
}

function fill_remarks(id){
  console.log(id);
  $.ajax({
    url: '<?php echo site_url('student_observation/observation/fill_remarks'); ?>',
    type: 'post',
    data:{id},
    success: function(data) {
        parsed_data = $.parseJSON(data);
        var output1=' <option value="" selected>Select observation</option>';
        parsed_data.forEach(element => {
          output1+='<option value="'+element.id+'">'+element.default_remarks+' </option>';
        });
        $("#predefined_observation").html(output1);
        $('#predefined_observation').selectpicker('refresh');
    },
    error: function (err) {
        console.log(err);
    }
});
}

function fill_observation_remarks(id){
  // Get the selected option's text
  var selectedText = $("#predefined_observation option:selected").text();
  $('#observation').val(selectedText.trim());
}
</script>
<style type="text/css">

    ::-webkit-scrollbar {
    width: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
    background: #f1f1f1;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
    background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
    background: #555;
    }
    
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }
  .widthadjust{
  width:600px;
  margin:auto;
  }

  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }
  .dt-buttons{
    margin-right: 3vh;

  }
  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
    
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5vh;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:2vh;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 5vh;
		}	
	}
</style>
