<div class="modal fade" id="add_contingency_plan" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;"">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Contengency Plan</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <input type="hidden" class="session_id" name="contingency_session_id" id="contingency_session_id">
                    <div class="form-group">
                        <label for="contingency_plans" class="control-label">Contengency Plan <font style="color: red;">*</font></label>
                        <textarea class="form-control" id="contingency_plans" name="contingency_plan" style="height: 11rem;" placeholder="Wanna Describe?"></textarea>
                        <span id="contingencyPlanError" style="display: none;"></span>
                    </div>

                    <div class="form-check form-switch pl-0">
                        <input class="form-check-input" type="checkbox" role="switch" id="visible_contingency_to_students">
                        <label class="form-check-label" style="margin-left: 2rem;" for="visible_contingency_to_students">Make visible to students</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mb-0" onClick="updateContingency()">Update</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // $("#add_contingency_plan").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $("#contingency_plans").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#contingencyPlanError").hide();
            if($("#contingency_plans").val() == ""){
                $("#contingencyPlanError").html("Contingency Plan Is Required").css("color", "red").show();
                return false;
            }
            updateContingencyPlan();
            loadContingencyPlan();
        }
    })

    function updateContingencyPlan() {
        const id = $("#contingency_session_id").val();
        const value = $("#contingency_plans").val();
        const visible_to_students = $("#visible_contingency_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/updateContengencyPlan"); ?>',
            type: "POST",
            data: { id, value, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_contingency_plan").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        getSessionData(id);
                        loadContingencyPlan();
                        showMainModal();
                    });
                } else {
                    $("#add_contingency_plan").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed to save activity",
                        text: "Please try again!",
                    }).then(() => {
                        $("#add_contingency_plan").modal('show');
                    });
                }
            },
            error: function (error) {
                console.log(error);
                $("#add_contingency_plan").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed to save activity",
                    text: "Please try again!",
                }).then(() => {
                    $("#add_contingency_plan").modal('show');
                });
            }
        })
    }

    function updateContingency() {
        $("#contingencyPlanError").hide();
        if($("#contingency_plans").val() == ""){
            $("#contingencyPlanError").html("Contingency Plan Is Required").css("color", "red").show();
            return false;
        }
        updateContingencyPlan();
        loadContingencyPlan();
    }
</script>