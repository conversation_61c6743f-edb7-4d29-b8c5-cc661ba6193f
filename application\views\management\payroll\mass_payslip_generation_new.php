<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/');?>">Payroll dashboard</a></li>
    <li>Mass Payslips Generation</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-4 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>Mass Payslips Generation</h3>
                </div>

                <div class="col-md-8 pr-0 d-flex justify-content-end align-items-center">
                    <div class="col-md-2">
                        <select class="form-control custom-select" name="staff_status" id="staff_status" onchange="change_staff_type_payroll()">
                                <option value=""><?php echo "Select Status" ?></option>
                                <?php foreach ($sStatus as $val => $name) { ?>
                                    <option class="staff_status_options" <?php if($adSelected == $val)  echo 'selected' ?> value="<?php echo $val; ?>"><?php echo $name; ?></option>
                                <?php } ?>
                        </select>   
                    </div>
                    <?php if (!empty($staff_type)) {  ?>
                    <div class="col-md-2">
                        <select onchange="change_staff_type_payroll()" class="form-control custom-select" id="stafftypeId" name="staff_type">
                            <option value="all">All</option>
                            <?php foreach ($staff_type as $key => $type) { ?>
                                <option class="staff_type_options" value="<?php echo $key ?>"><?php echo $type ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <?php  } ?>
                <!-- <ul class="panel-controls"> -->
                    <!-- <li> -->
                    <!-- <form class="form-horizontal"> -->
                    <!-- <div class="form-group"> -->
                        <div>
                            <label class="my-2 control-label" for="selected_schedule">Financial Year</label>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control custom-select"  onchange="get_financilayYearwise_data();" name="schedule_year" id="schedule_year">
                            <?php 
                                foreach ($financial_year as $key => $year) { ?>
                                    <option <?php if($year->id == $schedule_year) echo 'selected' ?> value="<?php echo $year->id ?>"><?php echo $year->f_year ?></option>';
                                <?php }
                            ?>
                            </select>
                        </div>
                        <div>
                            <label class="my-2 control-label" for="selected_schedule">Schedule</label>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control input-md custom-select" id="selected_schedule" onchange="get_schedule_data();">
                            </select>
                        </div>
                        
                        <div class="more">
                            <button id="more-btn" class="more-btn d-flex flex-column">
                                <span class="more-dot mb-1"></span>
                                <span class="more-dot mb-1"></span>
                                <span class="more-dot"></span>
                            </button>
                            <div class="more-menu" style="margin-right: -1%;display:none" id="display_menu">
                            <div class="more-menu-caret">
                                <div class="more-menu-caret-outer"></div>
                                <div class="more-menu-caret-inner"></div>
                            </div>
                            <div class="d-flex flex-column align-items-center">
                                <div class="more-menu-item action_btn" style="margin-bottom: 1rem;" data-button-type="export" role="presentation">
                                    <button onclick="download_csv_file()"  class="btn btn-primary">Download</button>
                                </div>
                                <div class="more-menu-item action_btn" data-button-type="export" role="presentation">
                                    <button onclick="$('#csvFile').click();" class="pull-right btn btn-info" id="upload_csv_button">Upload CSV</button>
                                    <input style="display: none;" type="file" id="csvFile" accept=".csv" onchange="readSingleFile(event)">
                                </div>
                            </div>
                            </div>
                        </div>
                    <!-- </div> -->
                    <!-- </form> -->

                   

                    <!-- </li> -->
                    <!-- <li> -->
                        
                    <!-- </li> -->
                    
                    
                        
                <!-- </ul> -->
                    
                </div>
            </div>
        </div>
        
        <!-- <div id="loader" class="loaderclass1"></div> -->
        <div class="panel-body">
            <?php if(!sizeof($schedules)){ ?>
            <div>
                Payroll Schedules are not yet added. Add a payroll schedule. <br>
                <a class="btn btn-success" href= "<?php echo site_url('management/payroll/new_payroll_schedule');?>">Add a Schedule</a>
            </div>
            <?php } else { ?>
                <div class="col-md-14 text-center" style="height: 10%; width: 100%;" id="payslip_data">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>
                <div class="table-responsive" id="payroll_monthly_staff_data">
                </div>
            <?php } ?>
        </div>

    </div>
</div>

<script>
    $(document).ready(function() {
        $('#import_monthly_payroll_close').on('click', function(){
            $("#upload_csv_button").html(`Upload CSV`);
            $("#table_from_csv").html('');
            $('#csvFile').val('');
        })
        function toggleMenu() {
            var menu = $('.more-menu');
            if (!menu.is(':visible')) {
                menu.show();
                $('.more').addClass('show-more-menu');
                $(document).on('click', hideMenuOutside);
            } else {
                menu.hide();
                $('.more').removeClass('show-more-menu');
                $(document).off('click', hideMenuOutside);
            }
        }

        function hideMenuOutside(event) {
            if (!$(event.target).closest('.more').length) {
                $('.more-menu').hide();
                $('.more').removeClass('show-more-menu');
                $(document).off('click', hideMenuOutside);
            }
        }

        $('#more-btn').on('click', function(event) {
            event.stopPropagation(); 
            toggleMenu();
        });

        $('.more-menu-item').on('click', function(event) {
            // handling menu item clicks
        });
    });
</script>
<style type="text/css">
  /* .loaderclass1 {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 5%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  } */
  .more-menu {
		width: 100px;
	}

	/* More Button / Dropdown Menu */

	.more-btn,
	.more-menu-btn {
		background: none !important;
		border: 0 none !important;
		line-height: normal !important;
		overflow: visible !important;
		-webkit-user-select: none !important;
		-moz-user-select: none !important;
		-ms-user-select: none !important;
		width: 100% !important;
		text-align: left !important;
		outline: none !important;
		cursor: pointer !important;
	}

	.more-dot {
		background-color: #aab8c2;
		margin: 0 auto;
		display: inline-block;
		width: 5px;
		height: 5px;
		margin-right: 1px;
		border-radius: 50%;
		transition: background-color 0.3s;
	}

	.more-menu {
		position: absolute;
		top: 100%;
		z-index: 900;
		float: left;
		padding: 10px 0;
		margin-top: 9px;
		background-color: #fff;
		border: 1px solid #ccd8e0;
		border-radius: 4px;
		box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
		opacity: 0;
		transform: translate(0, 15px) scale(.95);
		transition: transform 0.1s ease-out, opacity 0.1s ease-out;
		/* pointer-events: none; */
	}

	.more-menu-caret {
		position: absolute;
		top: -10px;
		right: 12px;
		width: 18px;
		height: 10px;
		float: right;
		overflow: hidden;
	}

	.more-menu-caret-outer,
	.more-menu-caret-inner {
		position: absolute;
		display: inline-block;
		margin-left: -1px;
		font-size: 0;
		line-height: 1;
	}

	.more-menu {
		right: 0% !important;
		width: 120px;
	}

	.more-menu-caret-outer {
		border-bottom: 10px solid #c1d0da;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		height: auto;
		left: 0;
		top: 0;
		width: auto;
	}

	.more-menu-caret-inner {
		top: 1px;
		left: 1px;
		border-left: 9px solid transparent;
		border-right: 9px solid transparent;
		border-bottom: 9px solid #fff;
	}

	.more-menu-items {
		margin: 0;
		list-style: none;
		padding: 0;
	}

	.more-menu-item {
		display: flex;
	}

	.more-menu-btn {
		min-width: 100% !important;
		color: #66757f !important;
		cursor: pointer !important;
		display: block !important;
		font-size: 13px !important;
		line-height: 18px !important;
		padding: 5px 20px !important;
		position: relative !important;
		white-space: nowrap !important;
	}

	
	.more-btn:hover .more-dot,
	.show-more-menu .more-dot {
		background-color: #516471;
	}

	.show-more-menu .more-menu {
		opacity: 1;
		transform: translate(0, 0) scale(1);
		pointer-events: auto;
	}

    .trloader {
        border: 4px solid #f3f3f3; /* Light grey */
        border-top: 4px solid #3498db; /* Blue */
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .dataTables_scrollBody {
        max-height: 300px !important;
    }

</style>

<script src="https://cdn.jsdelivr.net/npm/papaparse@5.3.2/papaparse.min.js"></script>
<script>
function initializeGlobalData() {
    globalInitialData = $('#edit_payroll').serializeArray();
    // console.log("Global data initialized:", globalInitialData);
}
var initialise = 0;
$(document).ready(function(){
    get_financilayYearwise_data();
    $('#edit_pay_slip').on('shown.bs.modal', function () {
        initializeGlobalData();
        // console.log(globalInitialData);
    });

    // When the modal is closed
    $('#edit_pay_slip').on('hidden.bs.modal', function () {
        $('#old_value').val(''); // Clear old_value
        $('#new_value').val(''); // Clear new_value
        globalInitialData = null;
        initialise = 0;
        // console.log("Modal Closed: Inputs cleared");
    });
    // var globalInitialData;
    var initial_val = {};
    var changedData = {};
    // Attach change event listener to form fields
    $('#edit_payroll').on('input change', ':input', function () {
        // console.log(globalInitialData);
        
        document.querySelector("#old_value").value="";
        document.querySelector("#new_value").value="";
        // Get the current state of the form fields
        var currentData = $('#edit_payroll').serializeArray();
        // console.log(currentData);
        
        // Identify the changed data
        
        $.each(currentData, function(index, item) {
            
            var fieldName = item.name;
            var initialValue = globalInitialData.find(data => data.name == fieldName)?.value || "";
            var currentValue = item.value;
            if (initialValue !== currentValue) {
                initial_val[fieldName] = initialValue;
                changedData[fieldName] = currentValue;
            }
        });
        // $('#old_value').val(JSON.stringify(initial_val));
        // $('#new_value').val(JSON.stringify(changedData));

        document.querySelector("#old_value").value = JSON.stringify(initial_val);
        document.querySelector("#new_value").value = JSON.stringify(changedData);
    });
});


function get_financilayYearwise_data() {
    var schedule_year = $('#schedule_year').val();
    $.ajax({
        url: '<?php echo site_url('management/payroll/get_financial_yearwise_data') ?>',
        type:'post',
        data:{'schedule_year': schedule_year},
        success:function(data){
            var schedule_data = $.parseJSON(data);
            if(schedule_data.length > 0)
                $('#selected_schedule').html(construct_table_schedule(schedule_data));
            else
                $('#selected_schedule').html('<option disabled selected>No schedule/s</option>');
            get_schedule_data();
        },
        error:function(error){
            
        }
    });
}

function construct_table_schedule(schedule_data) {
    var selected_schedule = '<?php echo $selected_schedule ?>';
    var html = '';
    var currentDate = new Date(); 
    for (var i = 0; i < schedule_data.length; i++) {
        var selectedSchedule = '';
        var disableOption = ''; 
        var startDate = new Date(schedule_data[i].start_date);
        if (startDate <= currentDate) {
            disableOption = '';
        } else {
            disableOption = 'disabled';
        }
        if (selected_schedule == schedule_data[i].id) {
            selectedSchedule = 'selected';
        }
        html += '<option ' + selectedSchedule + ' ' + disableOption + ' value="' + schedule_data[i].id + '">' + schedule_data[i].schedule_name + ' </option>';
    }
    return html;
}

function get_schedule_data(){
    change_staff_type_payroll();
}
// function get_schedule_data(){
//     change_staff_type_payroll();
// }

function change_staff_type_payroll() {
    $('#payroll_monthly_staff_data').html('');
    // $('.loaderclass1').css('opacity','5');
    $('#payslip_data').show()
    var stafftypeId = $('#stafftypeId').val();
    if (stafftypeId !== 'all') {
        window.localStorage.setItem("staffTpeId", stafftypeId);
    } else {
        // Set value to "all" for "All" option
        stafftypeId = 'all';
        window.localStorage.setItem("staffTpeId", stafftypeId);
    }
    
    var selected_schedule = $('#selected_schedule').val();
    if(!selected_schedule){
        $('#payroll_monthly_staff_data').html('<div class="no-data-display">Result not found</div>');
        $('#payslip_data').hide();
        return false;
    }

    var staff_status = $('#staff_status').val();
    var schedule_year = $('#schedule_year').val();
    var tableHeader = construct_monthl_table_header();
    $.ajax({
        url: '<?php echo site_url('management/payroll/get_payroll_structure_data_for_staff') ?>',
        type: 'post',
        data: {
            'stafftypeId': stafftypeId,
            'selected_schedule': selected_schedule,
            'staff_status': staff_status
        },
        success: function (response) {
            var staff_salaray = $.parseJSON(response);
            // $('.loaderclass1').css('opacity','5');
            if(staff_salaray === false || staff_salaray === null || Object.keys(staff_salaray).length === 0){
                $('#payroll_monthly_staff_data').html('<div class="no-data-display">Result not found</div>');
                $('#payslip_data').hide();
                return false;
            }
            $('#payroll_monthly_staff_data').html(tableHeader);
            $('#monthly_payslip_staff_data').html(staff_salaray);
            
            $('#reportTable').DataTable({
                "language": {
                    "search": "",
                    "searchPlaceholder": "Enter Search..."
                },
                paging: false,
                "scrollY": 300,
                "ordering": false, 
                "scrollCollapse": true // Ensure the table collapses to the correct width
            });
            $('#payslip_data').hide();
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function construct_monthl_table_header(){
    var html = '<table id="reportTable" class="table table-striped table-bordered nowrap">';
    html +='<thead><tr>';
    html +='<th>#</th>';
    html +='<th><button class="btn btn-warning" data-toggle="modal" data-target="#staff_approval">Send</button><br><div class="checkbox checkbox-warning"><input id="checkuncheck_all_staff" onclick="check_all_staff(this)" type="checkbox" class="check_staff styled"><label for="checkbox5">Approval</label></div></th>';

    //   html +='<th><button class="btn btn-warning" data-toggle="modal" data-target="#staff_approval">Send</button> <br> <input type="checkbox" name="selectAll_staff" onclick="check_all_staff(this)" style="width:20px;height: 18px;" id="checkuncheck_all_staff" class="check_staff"><label>All</label></th>';
    //   html +='<th><a id="massGenerateButton" onclick="mass_generation_pay_slip()" class="btn btn-primary">Generate </a> <input type="checkbox" name="selectAll" onclick="check_all(this)" class="check">  All </th>';

    html +='<th><button id="massGenerateButton" onclick="mass_payslip_generation_validation()" class="btn btn-primary" >Generate</button><br><div class="checkbox checkbox-warning"><input onclick="check_all(this)" type="checkbox" class="check styled"><label for="checkbox5">All</label></div></th>';


    html +='<th>Employee Code</th>';
    html +='<th>Staff Name</th>';
    html +='<th>Staff Status</th>';
    html +='<th>Payslip Status</th>';
    html +='<th>Selected Slab</th>';
    html +='<th>Staff Joining Date</th>';
    html +='<th>No. of Working Days</th>';
    html +='<th>Joining Date Based Working Days</th>';
    html +='<th>LOP</th>';
    html +='<th>No. of Payroll Days</th>';
    html +='<th>Monthly CTC</th>';
    html +='<th>Total Earnings</th>';
    html +='<th>Total Deductions</th>';
    html +='<th>Net Payble Amount</th>';
    html +='</tr>';
    html +='</thead>';
    html +='<tbody id="monthly_payslip_staff_data">';
    html +='</tbody>';
    html +='</thead>';
    html +='</table>';
    return html;
}

function check_all(check){
    if($(check).is(':checked')) {
        $(".staff_ids:not(:disabled)").prop('checked', true);
    }
    else {
        $(".staff_ids:not(:disabled)").prop('checked', false);
    }
}

function check_all_staff(check){
    if($(check).is(':checked')) {
        $(".staff_ids_approval:not(:disabled)").prop('checked', true);
    }
    else {
        $(".staff_ids_approval:not(:disabled)").prop('checked', false);
    }
}

function mass_payslip_generation_validation(){
    $('#massGenerateButton').attr('disabled','disabled').html('Please Wait...');
    var financial_year = $('#schedule_year').val();
    var approved_staffs = 0;
    var not_approved_staffs = 0;
    var staff_ids = [];
    $('.staff_ids').each(function() {
        if (!$(this).is(':disabled') && $(this).is(':checked')) {
            staff_ids.push($(this).val());
        }
    });
    if (staff_ids.length === 0) {
        $('#massGenerateButton').removeAttr('disabled','disabled').html('Generate');
        return false;
    }
    var confromMessage = `<h5>Payslip Generation for # of Staff ${staff_ids.length} </h5>`;
    $.ajax({
        url: '<?php echo site_url('management/payroll/get_approved_staffs_count') ?>',
        type: 'post',
        data: {
            'financial_year': financial_year, 'all_staff_ids': staff_ids
        },
        success: function (response) {
            var parsedData = JSON.parse(response);
            // console.log(parsedData);
            if(parsedData && parsedData.hasOwnProperty('approved') && parsedData.hasOwnProperty('not_approved')){
                approved_staffs = parseInt(parsedData.approved, 10) || 0;
                not_approved_staffs = parseInt(parsedData.not_approved, 10) || 0;
                confromMessage = `<h5>Payslip Generation for # of Staff: ${staff_ids.length} <br># of Staff TDS Approved: ${approved_staffs}<br><span style="color:red;"># of Staff TDS Not Approved: ${not_approved_staffs}</span><br> # of Staff Not Added To TDS: ${staff_ids.length - approved_staffs - not_approved_staffs} </h5>`;
            }
            
            bootbox.confirm({
                title: `Confirm - <b>Payslip Generation</b>`,
                message: `${confromMessage}`,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        mass_generation_pay_slip();
                    }else{
                        $('#massGenerateButton').removeAttr('disabled','disabled').html('Generate');
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        }
    })

    
}

async function mass_generation_pay_slip() {
    $('#massGenerateButton').attr('disabled','disabled').html('Please Wait...');
    var all_staff_ids = [];
    $('.staff_ids').each(function() {
        if (!$(this).is(':disabled') && $(this).is(':checked')) {
            all_staff_ids.push($(this).val());
        }
    });
    if (all_staff_ids.length === 0) {
        return false;
    }
    var selected_schedule = $('#selected_schedule').val();


    for(var i= 0; i<all_staff_ids.length; i++) {
        try {
            const result = await genearte_pay_slip_by_staff_wise(all_staff_ids[i], selected_schedule);
            if(i == all_staff_ids.length - 1) {
                // alert('Generation successful');
                Swal.fire({
                    title: 'Generation successful',
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    $('#massGenerateButton').removeAttr('disabled','disabled').html('Generate');
                });
                // window.location.reload();
            }
        } catch (error) {
            console.error(error);
        }
    }
}



function genearte_pay_slip_by_staff_wise(staffId, selected_schedule) {
    return new Promise((resolve, reject) => {
        
        setTimeout(() => {
            $.ajax({
                url: '<?php echo site_url('management/payroll/mass_payroll_generate_new'); ?>',
                type: 'post',
                data: {
                    'staff_id': staffId,
                    'selected_schedule': selected_schedule,
                },
                success: function(response) {
                    var resData = $.parseJSON(response);
                    $('#totalLop_'+staffId).html(resData.lop < 0 ? '0' : resData.lop);
                    $('#totalEarnings_'+staffId).html(resData.total_earnings);
                    $('#totalDeduct_'+staffId).html(resData.total_deductions);
                    $('#totalNetAmount_'+staffId).html(Math.round(resData.net_payble_amount * 100) / 100);
                    $('#noOfPayrollDays_'+staffId).html(resData.no_of_days_present);
                    $('#mass_check_box_staff_id_' + staffId).attr('disabled','disabled');
                    $('#payslipStatus_'+ staffId).html('Generated');
                    $('#approval_send_check_box_'+staffId).prop('disabled',false);
                    resolve();
                },
                error: function(err) {
                    console.log(err);
                    $('#mass_check_box_staff_id_' + staffId).attr('disabled','disabled');
                    $('#payslipStatus_'+ staffId).html('Failed');
                    reject(err);
                }
            });
        }, 250);
    });
}
</script>

<script>
function download_csv_file(){
    var currentDateTime = new Date();
    var day = String(currentDateTime.getDate()).padStart(2, '0');
    var month = currentDateTime.toLocaleString('default', { month: 'short' });
    var year = currentDateTime.getFullYear();
    var hours = String(currentDateTime.getHours()).padStart(2, '0');
    var minutes = String(currentDateTime.getMinutes()).padStart(2, '0');
    var seconds = String(currentDateTime.getSeconds()).padStart(2, '0');
    var formattedDateTime = `${day}-${month}-${year} ${hours}-${minutes}-${seconds}`;
    event.stopPropagation(); 
    event.preventDefault();
    var scheduleYear =  $('#schedule_year').val();
    var scheduleMonth = $('#selected_schedule').val();
    if (scheduleYear == '' || scheduleYear == null) {
        return false;
    }

    if (scheduleMonth == '' || scheduleMonth == null) {
        return false;
    }

    var selected_schedule_name = $('#selected_schedule option:selected').text();

    var downloadcsvstaffIds = [];
    $('.staff_ids').each(function() {
        downloadcsvstaffIds.push($(this).val());
    });
    if(downloadcsvstaffIds.length <= 0) {
        return false;
    }
    $.ajax({
        url: '<?php echo site_url('management/payroll/downloand_csv_for_monthly_payslip'); ?>',
        data: {
            'staff_ids': downloadcsvstaffIds,
            'schedule_year': scheduleYear,
            'schedule_month': scheduleMonth,
        },
        type: "post",
        success: function(data) {
            var blob = new Blob([data], { type: 'text/csv' });
            // Create a temporary anchor element
            var a = document.createElement('a');
            a.href = window.URL.createObjectURL(blob);
            a.download = `Payslip_data_${selected_schedule_name}_${formattedDateTime}.csv`;

            // Programmatically trigger the download
            document.body.appendChild(a);
            a.click();
            
            // Clean up
            document.body.removeChild(a);
        },
    });
}

function readSingleFile(e) {
    const file = e.target.files[0];
    if (!file) return;
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith(".csv")) {
        Swal.fire({
            icon: 'error',
            title: 'Invalid File',
            text: 'Only CSV files are allowed.',
        });
        return;
    }
    $("#upload_csv_button").html(`Please Wait...`);
    $("#table_from_csv").html('');
    const reader = new FileReader();
    reader.onload = function (e) {
        // let contents = e.target.result;
        // contents = contents.trim().replace(/"/g, ''); 
        // const arrayOfObjects = parseCSV(contents);
        // let headerData = arrayOfObjects[0];
        // let valuedata = arrayOfObjects[1];
        const contents = e.target.result;
        const parsed = Papa.parse(contents, {
            header: false,
            skipEmptyLines: true,
        });
        const arrayOfRows = parsed.data;
        const headerData = arrayOfRows[0];
        const valuedata = arrayOfRows.slice(1);
        $('#monthly_payroll_data').modal('show');
        var table= `<form enctype="multipart/form-data" method="post" id="payroll_import_data"  data-parsley-validate="" class="form-horizontal" style="width: 100%">`;
        var excludedColumns = ["staff_id", "schedule_id"];
        table +=`<table class="table table-bordered" >
            <thead>`;
        table += '<tr>';
        table += '<th></th>';
            for (let hd = 0; hd < headerData.length; hd++) {
                if (!excludedColumns.includes(headerData[hd])) {
                    table += '<th>'+headerData[hd]+'</th>';
                }
            }
            table += '</tr>';
        table += `</thead>
                <tbody>`;
        for(var i in valuedata) {
            if(valuedata[i].length > 0){
                table += '<tr id="tableTrRow_'+valuedata[i][1]+'">';
                table += '<td id="tableTdRow_'+valuedata[i][1]+'" ><div class="trloader" style="display: none;"></div></td>';
                for (let hd = 0; hd < valuedata[i].length; hd++) {
                    if (!excludedColumns.includes(headerData[hd])) {
                        let rawValue = valuedata[i][hd];
                        let cleanValue = rawValue.replace(/,/g, '');
                        table += '<td>'+rawValue+'</td>';
                        let sanitizedName = headerData[hd].trim().replace(/ /g, '_')
                        var inputName = 'staff_payroll[' + valuedata[i][1] + '][' + valuedata[i][2] + '][' + encodeURIComponent(sanitizedName) + ']';
                        table += '<input type="hidden" name="' + inputName + '" value="' + cleanValue + '">';
                    }
                }
                table += '</tr>';
                table += '</tr>';
            }
        }
        table += `</tbody>
            </table></form>`;
        $("#table_from_csv").html(table);
        $("#upload_csv_button").html(`Upload CSV`);
    };
    reader.readAsText(file);
}

function parseCSV(csv) {
    const rows = csv.split('\n');
    const headers_col = rows[0].split(',');
    const libr = [];
    
    for (let i = 1; i < rows.length; i++) {
        let row = rows[i].trim();
        if (row) {
            libr.push(row.split(','));
        }
    }
    return [headers_col, libr];
}


    // function parseCSV(csv) {
    //     const rows = csv.split('\n');
    //     let arrayOfObjects = [];
    //     const objectOfArrays= {};
	// 	let headers_col = [];
	// 	headers_col = rows[0].split(',');
	// 	var libr = [];
    //     for (let i = 1; i < rows.length - 1; i++) {
    //         libr.push(rows[i].split(','));
    //     }
	// 	return [headers_col, libr];
    // }

</script>


<div class="modal fade" id="monthly_payroll_data" tabindex="-1" role="dialog" style="width:90%;margin:auto;top:10%;bottom: 10%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Upload Payslip Additional Data</h4>
        </div>
        <div class="modal-body" style="max-height: 470px; overflow-y: auto;">
            <div id="table_from_csv">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="import_monthly_payroll_close" data-dismiss="modal">Close</button>
            <button type="button" onclick="update_monthly_payroll_data()" id="import_monthly_payroll" class="btn btn-success mt-0">Submit</button>
        </div>
    </div>
</div>

<script>
function update_monthly_payroll_data() {
    $('#import_monthly_payroll').prop('disabled', true).html('Please wait...');
    $('#import_monthly_payroll_close').prop('disabled', true);
    var $form = $('#payroll_import_data');
    var payrollData = [];    
    $form.find('table tr').each(function(index, row) {
        var rowData = {};        
        $(row).find('input').each(function() {
            var fieldName = $(this).attr('name');
            var fieldValue = $(this).val().trim();
            rowData[fieldName] = fieldValue;
        });
        if(Object.keys(rowData).length > 0){
            payrollData.push(rowData);
        }
    });
    
    var ajaxRequests = [];
    payrollData.forEach(function(data) {
        ajaxRequests.push(send_data_ajax_call(data));
    });

    $.when.apply($, ajaxRequests).done(function() {
        $('#import_monthly_payroll').prop('disabled', false).html('Submit');
        $('#import_monthly_payroll_close').prop('disabled', false);
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Payroll data has been successfully imported!'
        }).then(() => {
            // Hide the modal after the alert is closed
            $('#csvFile').val('');
            $('#monthly_payroll_data').modal('hide');
        });
    }).fail(function() {
        $('#import_monthly_payroll').prop('disabled', false).html('Submit');
        $('#import_monthly_payroll_close').prop('disabled', false);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'There was an error importing the payroll data.'
        }).then(() => {
            // Hide the modal after the alert is closed
            $('#csvFile').val('');
            $('#monthly_payroll_data').modal('hide');
        });
    });
}

function send_data_ajax_call(data) {
    var firstKey = Object.keys(data)[0];
    var staffIdMatch = firstKey.match(/\[([^\]]+)\]/);
    var staff_id = staffIdMatch ? staffIdMatch[1] : null;
    
    var rowId = '#tableTrRow_' + staff_id; 
    var $loader = $(rowId).find('.trloader');

    $loader.show();
    return $.ajax({
        url: '<?php echo site_url('management/payroll/import_monthly_payroll_additional_data'); ?>',
        type: 'post',
        data: data,
        success:function(res){
            //return false;
            $loader.hide();
            if(res.trim() != 0){
                $(rowId).css('background', '#6dbc6d');
                $('#tableTdRow_'+staff_id).html('Success');
            }else{
                $(rowId).css('background', '#e85757');
                $('#tableTdRow_'+staff_id).html('Approved / pending');
            }
        },
        error:function(){
            $loader.hide();
        }
    });
}

function change_present_days(){
    var no_of_present_days = $('#no_of_present_days').val();
    var staff_id = $('#payroll_staff_id').val();
    edit_per_payslip(staff_id, no_of_present_days);
}

function edit_per_payslip(staff_id, no_of_present_days = '') {
    // initialise = 0;
    var selected_schedule = $('#selected_schedule').val();
    if(selected_schedule !='' && selected_schedule == null){
        $('#edit_pay_slip').modal('hide');
        return false;
    }
    $.ajax({
        url: '<?php echo site_url('management/payroll/edit_mass_payslip_staff_wise') ?>',
        type:'post',
        data:{'staff_id': staff_id,'selected_schedule':selected_schedule,'no_of_present_days':no_of_present_days},
        success:function(data){
            var response_data = $.parseJSON(data);
            // console.log(response_data);
            $('#edit_payroll_btn').show();
            // if(response_data.UpdateButtonDisplayNone){
            //     $('#edit_payroll_btn').hide();
            // }
            let cal_payroll_data = response_data.cal_payroll_data;
            $('#monthly_gross').val(cal_payroll_data.monthly_gross);
            $('#monthly_basic_salary').val(cal_payroll_data.actual_basic_salary);
            $('#working_days').val(response_data.no_of_days);
            $('#no_of_present_days').val(response_data.no_of_present_days);
            $('#payroll_staff_id').val(staff_id);
            $('#earnings_data').html(construct_earning_data(response_data.earnings, cal_payroll_data));
            $('#deduction_data').html(construct_dedcution_data(response_data.deduction, cal_payroll_data));
            // Leave information // to do biometrict leave
            $('#leave_info_data').html(construct_leave_info_data(response_data.leave_info_new, response_data.leave_schedule_name));
            change_earnings_amount();
            change_deduction_amount();
            if(initialise == 0){
                initializeGlobalData();
                initialise++;
            }
        }
    });            
}

function construct_earning_data(earnings, cal_payroll_data){
    var html = '<h5 style="float: left;">Earnings [Rs.]</h5><br>';
    html += '<div class="form-group" style="margin-top: 10px;"></div>';
    for (let i = 0; i < earnings.length; i++) {
        let earningValue = cal_payroll_data[earnings[i].column_name];
        // console.log(earningValue);
        if(earningValue == null || earningValue == undefined){
            earningValue = 0;
        }
        html += '<div class="form-group">';
        html += '<label for="' + earnings[i].column_name + '" class="col-md-4 control-label">'+earnings[i].display_name+'</label>';
        html += '<div class="col-md-8">';
        html += '<input  type="text" value="'+earningValue+'" data-total_earings_include="'+earnings[i].total_earings_include+'" placeholder="Enter '+earnings[i].display_name+'" name="'+earnings[i].column_name+'" id="' + earnings[i].column_name + '" class="form-control earning_class" onkeyup="change_input_earnings_amount(this)">';
        html += '</div>';
        html += '</div>';
    }
    return html;
}

function construct_dedcution_data(deduction, cal_payroll_data){
    var html = '<h5 style="float: left;">Deductions [Rs.]</h5><br>';
    html += '<div class="form-group" style="margin-top: 10px;"></div>';
    for (let i = 0; i < deduction.length; i++) {
        let deductionValue = cal_payroll_data[deduction[i].column_name];
        // console.log(deductionValue);
        if(deductionValue == null || deductionValue == undefined){
            deductionValue = 0;
        }
        html += '<div class="form-group">';
        html += '<label class="col-md-4 control-label">'+deduction[i].display_name+'</label>';
        html += '<div class="col-md-8">';
        html += '<input  type="text" value="'+deductionValue+'" placeholder="Enter '+deduction[i].display_name+'" name="'+deduction[i].column_name+'" class="form-control deduction_class"  data-total_deduction_include="'+deduction[i].total_deduction_include+'" onkeyup="change_deduction_amount()">';
        html += '</div>';
        html += '</div>';
    }
    return html;
}

function change_earnings_amount() {
    var totalEarnings = 0;
    $('.earning_class').each(function() {
        var earningsInput = $(this);
        var totalEarningsInclude = earningsInput.data('total_earings_include');
        if (totalEarningsInclude === 1) {
            var inputValue = parseFloat(earningsInput.val()) || 0;
            totalEarnings += inputValue;
        }
    });
    $('#total_earnings').val(parseFloat(totalEarnings).toFixed(2));
    cal_net_amount();
}

function change_input_earnings_amount(inputField){
    var inputId = inputField.id;
    var cursorPos = inputField.selectionStart;
    var totalEarnings = 0;
    var inputArry = {};
    $('.earning_class').each(function() {
        var earningsInput = $(this);
        var totalEarningsInclude = earningsInput.data('total_earings_include');
        if (totalEarningsInclude === 1) {
            var inputName = earningsInput.attr('name');
            var inputValue = parseFloat(earningsInput.val()) || 0;
            totalEarnings += inputValue;
            inputArry[inputName] = inputValue;
        }
    });
    $('#total_earnings').val(parseFloat(totalEarnings).toFixed(2));
    cal_net_amount();
    recal_edit_earnings_amount(inputArry, inputId, cursorPos);
}


function recal_edit_earnings_amount(inputArry, inputId, cursorPos){
    var no_of_present_days = $('#no_of_present_days').val();
    var staff_id = $('#payroll_staff_id').val();
    var selected_schedule = $('#selected_schedule').val();
    var monthly_gross = $('#monthly_gross').val();
    $.ajax({
        url: '<?php echo site_url('management/payroll/edit_mass_earnings_changes_payslip_staff_wise') ?>',
        type:'post',
        data:{'staff_id': staff_id,'selected_schedule':selected_schedule,'no_of_present_days':no_of_present_days,'monthly_gross':monthly_gross,'inputArry':inputArry},
        success:function(data){
            var response_data = $.parseJSON(data);
            let cal_payroll_data = response_data.cal_payroll_data;
            $('#monthly_gross').val(cal_payroll_data.monthly_gross);
            $('#monthly_basic_salary').val(cal_payroll_data.monthly_basic_salary);
            $('#working_days').val(response_data.no_of_days);
            $('#no_of_present_days').val(response_data.no_of_present_days);
            $('#payroll_staff_id').val(staff_id);
            $('#earnings_data').html(construct_earning_data(response_data.earnings, cal_payroll_data));
            var inputField = $('#' + inputId);
            inputField.focus();
            inputField[0].setSelectionRange(cursorPos, cursorPos);


            change_earnings_amount();
        }
    });
}


function change_deduction_amount(){
    var totalDeduction = 0;
    $('.deduction_class').each(function() {
        var deductionInput = $(this);
        var totalDeductionInclude = deductionInput.data('total_deduction_include');
        if (totalDeductionInclude === 1) {
            var inputValue = parseFloat(deductionInput.val()) || 0;
            totalDeduction += inputValue;
        }
    });
    $('#total_deducation').val(parseFloat(totalDeduction).toFixed(2));
    cal_net_amount();
}

function cal_net_amount(){
    let totalEarningsCal = $('#total_earnings').val();
    let totalDeductionsCal = $('#total_deducation').val();
    var totalNetAmount = totalEarningsCal - totalDeductionsCal;
    var roundedNetAmount = totalNetAmount.toFixed(2);
    $('#monthly_net_amount').val(parseFloat(roundedNetAmount));
}

function construct_leave_info_data(leave_info_new, leave_schedule_name){
    var leaveHtml = '<div class="panel panel-default tabs">';
    leaveHtml += '<ul class="nav nav-tabs nav-justified">';
    leaveHtml += '<li class="active" style="width:max-content"><a href="#leaveData" data-toggle="tab">Leaves Data</a></li>';
    leaveHtml += '</ul>';
    leaveHtml += '<div class="panel-body tab-content">';
    leaveHtml += '<table id="example" class="table table-striped table-bordered" cellspacing="0" width="100%">';
    leaveHtml += '<thead>';
    leaveHtml += '<tr>';
    leaveHtml += '<th>Type</th>';
    leaveHtml += '<th>'+leave_schedule_name+'</th>';
    leaveHtml += '</tr>';
    leaveHtml += '</thead>';
    leaveHtml += '<tbody>';
    let totalLeave = 0;
    for (let i = 0; i < leave_info_new.length; i++) {
        totalLeave += parseFloat(leave_info_new[i].leave_count);
        leaveHtml += '<tr>';
        leaveHtml += '<td>'+leave_info_new[i].category_name+'</td>';
        leaveHtml += '<td>'+leave_info_new[i].leave_count+'</td>';
        leaveHtml += '</tr>';
    }
    leaveHtml += '</tbody>';
    leaveHtml += '<tfoot>';
    leaveHtml += '<tr>';
    leaveHtml += '<th>Total</th>';
    leaveHtml += '<th>'+totalLeave+'</th>';
    leaveHtml += '</tr>';
    leaveHtml += '</tfoot>';
    leaveHtml += '</table>';
    leaveHtml += '</div>';
    leaveHtml += '</div>';
    return leaveHtml;
}
</script>


<div class="modal fade" id="edit_pay_slip" tabindex="-1" role="dialog" style="width:90%;margin:auto;top:10%;bottom: 10%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">View Payslip</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body" style="max-height: 470px; overflow-y: auto;">
            <form class="form-horizontal" data-parsley-validate="" enctype="multipart/form-data" id="edit_payroll" method="post">
                <input  type="hidden" name="edit_payslip_staff_id" id="payroll_staff_id">
                <input type="hidden" name="old_value" id="old_value">
                <input type="hidden" name="new_value" id="new_value">

                <div class="row">
                    <div class="col-md-6 text-center">
                        <div class="form-group">
                            <div class="col-md-4">
                                <label class="control-label">No of Working Days <font color="red">*</font></label>
                            </div>
                            <div class="col-md-8">
                                <input name="working_days" readonly="" type="number"  placeholder="" class="form-control" id="working_days">
                            </div>
                        </div>
                    
                        <div class="form-group">
                            <div class="col-md-4">
                                <label class="control-label">Monthly CTC <font color="red">*</font></label>
                            </div>
                            <div class="col-md-8">
                                <input id="monthly_gross" readonly name="monthly_gross" type="number"  class="form-control" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-4">
                            <label class="control-label">Monthly Basic Salary <font color="red">*</font></label>
                        </div>
                            <div class="col-md-8">
                                <input id="monthly_basic_salary" readonly  type="number" class="form-control" data-parsley-error-message="Cannot be empty." required="">
                            </div>
                        </div><br>

                        <div class="form-group">
                            <div class="col-md-4">
                                <label class="control-label">No. of Payroll Days</label>
                            </div>
                            <div class="col-md-8">
                                <input type="text" placeholder="" id="no_of_present_days" onkeyup="change_present_days()" data-type="" data-parsley-error-message="Enter valid value" name="no_of_present_days" class="form-control input-md emailtype">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-center" id="leave_info_data"></div>
                </div>

                <!-- Total earnings and deductions -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-6">
                            <div class="well" id="earnings_data">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="well" id="deduction_data">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label col-sm-4" for="total_earnings">Total Earnings [Rs.]</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="total_earnings" id="total_earnings" readonly="" class="form-control" value="">
                                    </div>
                                    <span class="col-sm-12"></span>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="control-label col-sm-4" for="total_dedt">Total Deductions[Rs.]</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="total_deduct" readonly=""  id="total_deducation" class="form-control total_deduct" value="">
                                    </div>
                                    <span class="col-sm-12"></span>
                                </div>
                            </div>
                        </div>
                       
                        <div class="col-sm-12" style="margin-top:40px">
                            <div class="col-sm-3"></div>
                            <div class="form-group col-sm-6">
                                <label class="control-label col-sm-6" for="net_pay">Net Payble Amount [Rs.]</label>
                                <div class="col-sm-6">
                                    <input type="text" readonly="" id="monthly_net_amount" class="form-control" value="">
                                </div>
                                <span class="col-sm-12"></span>
                            </div>
                            <div class="col-sm-3"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div> 
        <div class="modal-footer">
            <button class="btn btn-primary" id="edit_payroll_btn"  type="button" onclick="insert_update_payroll_data()">Update</button>
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
function insert_update_payroll_data() {
    var selected_schedule = $('#selected_schedule').val();
    var $form = $('#edit_payroll');
    
    var formData = new FormData($form[0]);
    formData.append('selected_schedule', selected_schedule);
    $('#edit_payroll_btn').html('Please wait..').attr('disabled', 'disabled');
    $.ajax({
        url: '<?php echo site_url('management/payroll/individual_staff_payslip_generate'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            var resData = $.parseJSON(response);
            $('#totalLop_'+resData.staff_id).html(resData.lop < 0 ? '0' : resData.lop);
            $('#totalEarnings_'+resData.staff_id).html(resData.total_earnings);
            $('#totalDeduct_'+resData.staff_id).html(resData.total_deductions);
            $('#totalNetAmount_'+resData.staff_id).html(resData.net_payble_amount);
            $('#noOfPayrollDays_'+resData.staff_id).html(resData.no_of_days_present);
            $('#mass_check_box_staff_id_' + resData.staff_id).attr('disabled','disabled');
            $('#payslipEditButton_' + resData.staff_id).css('pointer-event','none');
            $('#payslipStatus_'+ resData.staff_id).html('Generated');
            $('#approval_send_check_box_'+resData.staff_id).prop('disabled',false);
            initialise = 0;
            $('#old_value').val(''); // Clear old_value
            $('#new_value').val(''); // Clear new_value
            globalInitialData = null;
            Swal.fire({
                icon: "success",
                title: "Payroll Data Edited Successfully",
                showConfirmButton: false,
                timer: 2500
            });
            setTimeout(function() {
                $('#edit_pay_slip').modal('hide');
            }, 2000);
            $('#edit_payroll_btn').html('Update').removeAttr('disabled');
        },
        error: function(xhr, status, error) {
            console.error(xhr.responseText);
            Swal.fire({
                icon: "error",
                title: "Server Error",
                showConfirmButton: false,
                timer: 2500
            });
            $('#edit_payroll_btn').html('Submit').removeAttr('disabled');
        }
    });
}


async function send_approval_staff_id() {
    $('#addsettings_subbtn').attr('disabled','disabled').html('Please Wait...');
    var selected_schedule = $('#selected_schedule').val();
    var payroll_approval_staff_id = [];
    $('.staff_ids_approval').each(function() {
        if (!$(this).is(':disabled') && $(this).is(':checked')) {
            payroll_approval_staff_id.push($(this).val());
        }
    });

    if (payroll_approval_staff_id.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Please select at least one staff ID',
            showConfirmButton: false,
            timer: 2500
        });
        return false;
    }
    
    var _staff_id = $('#payroll_approval_staff_id').val();
    if (!_staff_id) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Please select a staff ID',
            showConfirmButton: false,
            timer: 2500
        });
        return false;
    }

    for(var i= 0; i<payroll_approval_staff_id.length; i++) {
        try {
            const result = await sent_apprval_payslip_by_staff_wise(payroll_approval_staff_id[i], selected_schedule, _staff_id);
            if(i == payroll_approval_staff_id.length - 1) {
                // alert('Approval Sent Successfully');
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Approval Sent Successfully',
                    showConfirmButton: false,
                    timer: 2500
                }).then(() => {
                    window.location.reload();
                });
            }
        } catch (error) {
            console.error(error);
        }
    }
}



function sent_apprval_payslip_by_staff_wise(payroll_approval_staff_id, selected_schedule, _staff_id) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            $.ajax({
                url: '<?php echo site_url('management/payroll/send_approval_staff_id_upload'); ?>',
                type: 'post',
                data: {
                    'payroll_approval_staff_id': payroll_approval_staff_id,
                    '_staff_id': _staff_id,
                    'selected_schedule': selected_schedule
                },
                success: function(data) {
                    if(data) {
                        $(".staff_ids_approval").attr('disabled','disabled');
                        $('#send_approval_closeButton').hide();
                        $('#send_approval_fa_icon').hide();

                    } else {
                        $(".staff_ids_approval").attr('disabled','disabled');
                        $('#send_approval_closeButton').hide();
                        $('#send_approval_fa_icon').hide();
                    }
                    resolve();
                },
                error: function(err) {
                    console.log(err);
                    $(".staff_ids_approval").attr('disabled','disabled');
                    $('#send_approval_closeButton').hide();
                    $('#send_approval_fa_icon').hide();
                    reject(err);
                }
            });
        }, 250);
    });
}
</script>

<div class="modal fade" id="staff_approval" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Select Staff</h4>
        <button type="button" class="close" id="send_approval_fa_icon" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <form  method="post" enctype="multipart/form-data" data-parsley-validate="">
        <div class="modal-body" style="height:100px; overflow: scroll;">
          
          <div class="form-group">
            <label for="message-text" class="col-form-label">Select Staff <font color="red"> *</font></label>
            <div class="form-group">
                    <select required="" required name="payroll_approval_staff_id" id="payroll_approval_staff_id" class="form-control">
                        <option value="">Select Staff</option>
                        <?php foreach ($staff_list as $key => $val) { ?>
                          <option value="<?php echo $val->id ?>"><?php echo $val->name ?></option>
                        <?php } ?>
                      </select>
                  </div>
          </div>

        </div>
      </form>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="send_approval_closeButton" data-dismiss="modal">Close</button>
            <button type="button" onclick="send_approval_staff_id()" id="addsettings_subbtn" class="btn btn-success" style="margin-bottom: 3px;">Add</button>
        </div>
    </div>
  </div>
</div>

<style>
    .checkbox {
  padding-left: 20px;
}
.checkbox label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding-left: 5px;
  color:#56688a;
  font-weight: 600;
}
.styled{
    width: 22px;
    height: 18px;
}
.staff_ids_approval{
    width: 22px;
    height: 18px;
}
.staff_ids{
    width: 22px;
    height: 18px;
}

.dataTable{
    margin-bottom: 0px;
}

    .dataTables_wrapper .dataTables_scrollHead {
        overflow: hidden;
    }

    .dataTables_wrapper .dataTables_scrollBody {
        overflow: auto;
    }

    .dataTables_wrapper .dataTables_scrollHeadInner {
        box-sizing: content-box;
    }

    .dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar {
        width: 10px;
    }

    .dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-thumb {
        background-color: #888;
    }

    #reportTable_filter{
        border: none;
    }

    .confirmWidth{
        width: 50%;
        margin: auto;
    }
</style>