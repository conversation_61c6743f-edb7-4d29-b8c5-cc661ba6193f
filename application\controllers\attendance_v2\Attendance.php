<?php

class Attendance extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STUDENT_ATTENDANCE_V2')) {
            redirect('dashboard', 'refresh');
        }
        $this->load->model('class_section');
        $this->load->model('attendance_v2/attendance_model');
        $this->config->load('form_elements');
    }

    public function take_attendance($section_id = '')
    {
        $data['selected_section_id'] = $section_id;
        $data['attendance_type'] = 'subject_wise';
        $att_type = $this->settings->getSetting('student_attendance_type');
        $import_enabled = $this->settings->getSetting('student_attendance_import');
        $enable_notification = $this->settings->getSetting('student_attendancev2_enable_subject_absentees_notification');
        $data['notification_absentee_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_absentee_message');
        $data['notification_late_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_late_message');
        $data['notification_mode'] = $this->settings->getSetting('student_attendancev2_subject_attendance_enable_mode');
        $data['enable_notification'] = 0;
        if ($enable_notification) {
            $data['enable_notification'] = 1;
        }
        $data['import_enabled'] = 0;
        if ($import_enabled) {
            $data['import_enabled'] = 1;
        }
        $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
        if ($data['is_semester_scheme'] === '1') {
            $data['sections'] = $this->attendance_model->getAllClassSectionSemester();
        } else {
            $data['sections'] = $this->class_section->getAllClassSections();
        }

        if($this->settings->getSetting('enable_subject_attendance_teacher_permission') ) {
            foreach($data['sections'] as $key => $val) {
                $data['sections'][$key]->is_section_enabled= $this->attendance_model->checkSectionEnability($val->id);
            }
        } else {
            foreach($data['sections'] as $key => $val) {
                $data['sections'][$key]->is_section_enabled= 1;
            }
        }

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'attendance_v2/index_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'attendance_v2/index_mobile';
        } else {
            $data['main_content'] = 'attendance_v2/take_attendance';
        }

        $data["can_staff_take_previous_date_attendance"]=$this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.TAKE_PREVIOUS_DATE_ATTENDANCE');

        $this->load->view('inc/template_without_top_nav', $data);
    }

    public function take_attendance_with_periods($section_id = '')
    {
        // $data['period_list']=$this->settings->getSetting('student_attendance_v2_period_list');
        $data['selected_section_id'] = $section_id;
        $data['attendance_type'] = 'subject_wise';
        $att_type = $this->settings->getSetting('student_attendance_type');
        $import_enabled = $this->settings->getSetting('student_attendance_import');
        $enable_notification = $this->settings->getSetting('student_attendancev2_enable_subject_absentees_notification');
        $data['notification_absentee_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_absentee_message');
        $data['notification_late_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_late_message');
        $data['notification_mode'] = $this->settings->getSetting('student_attendancev2_subject_attendance_enable_mode');
        $data['enable_notification'] = 0;
        if ($enable_notification) {
            $data['enable_notification'] = 1;
        }
        $data['import_enabled'] = 0;
        if ($import_enabled) {
            $data['import_enabled'] = 1;
        }
        $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
        if ($data['is_semester_scheme'] === '1') {
            $data['sections'] = $this->attendance_model->getAllClassSectionSemester();
        } else {
            $data['sections'] = $this->class_section->getAllClassSections();
        }
        if($this->settings->getSetting('enable_subject_attendance_teacher_permission') ) {
            foreach($data['sections'] as $key => $val) {
                $data['sections'][$key]->is_section_enabled= $this->attendance_model->checkSectionEnability($val->id);
            }
        } else {
            foreach($data['sections'] as $key => $val) {
                $data['sections'][$key]->is_section_enabled= 1;
            }
        }

        $data["user"]=$this->authorization->isSuperAdmin();

        $data["can_edit_all_subjects"] = $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.EDIT_ATTENDANCE_ALLSUBJECTS');

        $data["can_staff_take_previous_date_attendance"] = $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.TAKE_PREVIOUS_DATE_ATTENDANCE');

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'attendance_v2/take_attendance_mobile_period_wise';
        } else if ($this->mobile_detect->isMobile()) {
            // $data['main_content'] = 'attendance_v2/index_mobile_v2';
            $data['main_content'] = 'attendance_v2/take_attendance_mobile_period_wise';
        } else {
            $data['main_content'] = 'attendance_v2/take_attendance_v2';
        }

        // we want sorting of student list
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $isSortingEnabled=$prefix_order_by==$prefix_student_name;
        
        $data["isSortingEnabled"] = $isSortingEnabled;

        $data["prefix_student_name"] = $prefix_student_name;

        $data["prefix_student_name_formatted"]=ucwords(str_replace("_", " ", $prefix_student_name));

        $this->load->view('inc/template_without_top_nav', $data);
    }

    public function take_attendance_mobile($section_id = '')
    {
        // echo "<pre>"; print_r($_POST); die();
        if (!isset($_POST['class_section_id'])) {
            redirect('attendance_v2/attendance/take_attendance');
        }
        $enable_notification = $this->settings->getSetting('student_attendancev2_enable_subject_absentees_notification');
        $data['notification_absentee_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_absentee_message');
        $data['notification_late_message'] = $this->settings->getSetting('student_attendancev2_subject_attendance_late_message');
        $data['notification_mode'] = $this->settings->getSetting('student_attendancev2_subject_attendance_enable_mode');
        $data['enable_notification'] = 0;
        if ($enable_notification) {
            $data['enable_notification'] = 1;
        }
        $data['section_id'] = $_POST['class_section_id'];
        $data['attendance_type'] = $_POST['attendance_type'];
        $data['attendance_type_id'] = ($_POST['attendance_type'] == 'subject_wise') ? $_POST['subject_id'] : $_POST['session_id'];
        $data['attendance_type_name'] = '';
        if ($_POST['attendance_type'] == 'subject_wise') {
            $data['attendance_type_name'] = $this->attendance_model->getSubjectNameById($_POST['subject_id']);
        }
        $data['date'] = $_POST['attendance_date'];
        $data['section'] = $this->class_section->getSectionDetail($data['section_id']);

        $data['ttp_id'] = isset($_POST['ttp_id']) ? $_POST['ttp_id'] : '0';
        $data['period_name'] = isset($_POST['period_name']) ? $_POST['period_name'] : '';

        if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'attendance_v2/take_attendance_tablet';
        } else if ($this->mobile_detect->isMobile()) {
            $data['main_content'] = 'attendance_v2/take_attendance_mobile';
        }

        $this->load->view('inc/template_without_top_nav', $data);
    }

    public function import_attendance()
    {
        $data['attendance_type'] = 'subject_wise';
        $att_type = $this->settings->getSetting('student_attendance_type');
        if ($att_type) {
            $data['attendance_type'] = $att_type;
        }
        $data['sections'] = $this->class_section->getAllClassSections();
        $data['main_content'] = 'attendance_v2/import_attendance';
        $this->load->view('inc/template_without_top_nav', $data);
    }

    public function checkAttendanceTaken()
    {
        $data['status'] = 0;
        $data['attendance_data'] = $this->attendance_model->checkAttendanceTaken();
        if (!empty($data['attendance_data'])) {
            $data['status'] = 1;
        }
        echo json_encode($data);
    }

    public function getSubjectSectionStudents()
    {
        // echo "<pre>"; print_r($_POST); die();

        $attendance_master_id = $_POST['attendance_master_id'];
        $data['subject'] = $this->attendance_model->getSubjectNameById($_POST['attendance_type_id']);
        if ($attendance_master_id == 0) {
            //take attendance
            $data['students'] = $this->attendance_model->getStudentsBySubjectSection($_POST);
        } else {
            //edit attendance
            $data['students'] = $this->attendance_model->getStudentsAttendanceById($attendance_master_id);
        }
        echo json_encode($data);
    }

    public function save_attndance_data()
    {
        // echo "<pre>"; print_r($_POST); die();

        $status = $this->attendance_model->save_attndance_data();
        echo $status;
    }

    public function save_attendance_data_period_wise_mobile()
    {
        // echo "<pre>"; print_r($_POST); die();

        $data['attendance_master_id'] = $this->attendance_model->save_attndance_data();
        $data['section_id'] = base_url("attendance_v2/Attendance/take_attendance_with_periods/" . $_POST['section_id']);

        echo json_encode($data);
        // echo base_url("attendance_v2/Attendance/take_attendance_with_periods/" . $status);
    }

    public function getStudentAttendanceAudit()
    {
        $student_attendance_id = $_POST['student_attendance_id'];
        $data['history'] = $this->attendance_model->getStudentAttendanceAudit($student_attendance_id);
        echo json_encode($data);
    }

    public function getSectionSubjectsORSessions()
    {
        $section_id = $_POST['section_id'];
        $data['attendance_type'] = $_POST['attendance_type'];
        $subject_attendance_mode = $this->settings->getSetting('student_attendancev2_subject_attendance_mode');
        if ($data['attendance_type'] == 'subject_wise') {
            $data['subjects'] = $this->attendance_model->getSectionSubjets($section_id);
            if($this->settings->getSetting('enable_subject_attendance_teacher_permission')) {
                foreach($data['subjects'] as $k => $v) {
                    $data['subjects'][$k]->is_subject_enabled= $this->attendance_model->checkSubjectEnability($section_id, $v->id);
                }
            } else {
                foreach($data['subjects'] as $k => $v) {
                    $data['subjects'][$k]->is_subject_enabled= 1;
                }
            }
            $data['actual_period_lists'] = [];
            if ($subject_attendance_mode == 'attendance_with_periods') {
                $data['actual_period_lists'] = $this->attendance_model->getActualPeriodLists($_POST);
                $data['taken_period_lists'] = $this->attendance_model->getTakenPeriodLists($section_id, $_POST["date"]);
            }
        } else if ($data['attendance_type'] == 'session_wise') {
            $data['sessions'] = [];
        }

        $display_list = array();
        foreach ($data['actual_period_lists'] as $apl_key => $apl) {
            if ($apl->period_seq_excl_break == "0") continue;
            $found = false;
            foreach ($data['taken_period_lists'] as $tpl_key => $tpl) {
                if ($apl->id == $tpl->ttp_id) {
                    $found = true;

                    $temp_row = $tpl;
                    $temp_row->is_taken = true;
                    $temp_row->start_time = $apl->start_time;
                    $temp_row->end_time = $apl->end_time;
                    $temp_row->short_name = $apl->short_name;
                    $display_list[] = $temp_row;
                }
            }

            if (!$found) {
                $temp_row = $apl;
                $temp_row->is_taken = false;
                $display_list[] = $temp_row;
            }
        }

        $data['display_list']=$display_list;
        echo json_encode($data);
    }

    public function isElective(){
        $result=$this->attendance_model->isElective($_POST);
        echo json_encode($result);
    }

    public function getElectiveSubjects(){
        if(isset($_POST["class_semester_id"])){
            $class_semester_id = $_POST["class_semester_id"];
        }else{
            $class_semester_id = 0;
        }
       
        $result=$this->attendance_model->getElectiveSubjects($_POST["elective_master_group_id"],$_POST["class_master_id"],$_POST["period_no"],$_POST["class_section_id"],$_POST["date"],$_POST["ttp_id"], $class_semester_id);
        echo json_encode($result);
    }

    public function get_absentees_latecomers()
    {
        $attendance_master_id = $_POST['attendance_master_id'];
        $data = $this->attendance_model->get_absentees_latecomers($attendance_master_id);
        echo json_encode($data);
    }

    public function get_day_attendance_absentees_latecomers()
    {
        $attendance_session_id = $_POST['attendance_session_id'];
        $selectedDate = $_POST['selectedDate'];
        $data = $this->attendance_model->get_day_attendance_absentees_latecomers($attendance_session_id,$selectedDate);
        echo json_encode($data);
    }

    public function get_attendence_students_names()
    {
        $attendance_master_id = $_POST['attendance_master_id'];
        $status = $_POST['status'];
        $data = $this->attendance_model->get_attendence_students_names($attendance_master_id,$status);
        echo json_encode($data);
    }

    public function send_messages()
    {
        $this->load->helper('texting_helper');
        $input = array();
        $input['source'] = 'Attendance';
        // $text_send_to = $this->settings->getSetting('text_send_to');
        
        if(!empty($_POST['text_send_to'])){
            $text_send_to = $_POST['text_send_to'];
        }else{
            $text_send_to="Both";
        }
        
        $input['mode'] = $_POST['communication_mode'];
        $input['student_id_messages'] = $_POST['student_messages'];
        $status = 0;
        $error = 0;
        $warning = 0;
        if ($input['mode'] == 'notification') {
            $input['send_to'] = 'Both';
            $res = sendUniqueText($input);
            if ($res['success'] != '') $status = 1;
            $error = $res['error'];
            $warning = $res['warning'];
        } else {
            if ($text_send_to == 'preferred') {
                $input['send_to'] = 'preferred';
                $res = sendUniqueText($input);
                if ($res['success'] != '') $status = 1;
                $error = $res['error'];
                $warning = $res['warning'];
            } else if ($text_send_to == 'preferred_parent') {
                $input['send_to'] = 'preferred';
                $res = sendUniqueText($input);
                if ($res['success'] != '') $status = 1;
                $error = $res['error'];
                $warning = $res['warning'];
            } else if ($text_send_to == '' || $text_send_to == 'Both') {
                $input['send_to'] = 'Both';
                $res = sendUniqueText($input);
                if ($res['success'] != '') $status = 1;
                $error = $res['error'];
                $warning = $res['warning'];
            } else if ($text_send_to == 'Father') {
                //sending to father
                $input['send_to'] = 'Father';
                $res = sendUniqueText($input);
                if ($res['success'] != '') $status = 1;
                $error = $res['error'];
                $warning = $res['warning'];
            } else if ($text_send_to == 'Mother') {
                //sending to mother
                $input['send_to'] = 'Mother';
                $res = sendUniqueText($input);
                if ($res['success'] != '') $status = 1;
                $error = $res['error'];
                $warning = $res['warning'];
            }
        }
        if ($status) $this->attendance_model->update_notified_at($_POST['attendance_master_id']);
        echo json_encode(['status' => $status, 'error' => $error, 'warning' => $warning]);
    }

    public function getAttendanceStatus()
    {
        $result = $this->attendance_model->getAttendanceStatus($_POST);
        echo json_encode($result);
    }

    public function checkPeriodExists()
    {
        $result = $this->attendance_model->checkPeriodExists($_POST);
        echo json_encode($result);
    }

    public function remove_attendance(){
        echo $this->attendance_model->remove_attendance($_POST);
    }

    public function get_subjects(){
        $data['viewSubjects'] = $this->attendance_model->callGetSubjects();
        if($this->settings->getSetting('enable_subject_attendance_teacher_permission')) {
            foreach($data['viewSubjects'] as $k => $v) {
                $data['viewSubjects'][$k]->is_subject_enabled= $this->attendance_model->checkSubjectEnability($_POST['class_master_id'], $v->subject_master_id);
            }
        } else {
            foreach($data['viewSubjects'] as $k => $v) {
                $data['viewSubjects'][$k]->is_subject_enabled= 1;
            }
        }
        echo json_encode($data);
    }

    public function get_class_semesters() {
        $class_section_id = $_POST['class_section_id'];
        $data['semesters'] = $this->attendance_model->get_class_semesters($class_section_id);
        echo json_encode($data);
      }

}
