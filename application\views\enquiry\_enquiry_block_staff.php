<hr>
  <div class="form-group">
      <label class=" col-md-12" for="acadYearId"><b>Academic Year <font color="red">*</font></b></label>
      <div class="col-md-12">
          <?php $acad_year = json_decode($this->settings->getSetting('Freez_academic_year_in_enquiry')) ;
        if(!empty($acad_year)) {?>
          <select class="form-control" onchange="onchange_acadyear()" id="acadYearId" name="academic_year" required>
              <?php
          foreach ($acad_years as $ay) {
            if (in_array($ay->acad_year_id,$acad_year)) {
              echo "<option value='$ay->acad_year_id'>$ay->acad_year_name</option>";
            }
          }
          ?>
          </select>
          <?php }else{?>
          <select class="form-control" onchange="onchange_acadyear()" id="acadYearId" name="academic_year">
              <?php
          foreach ($acad_years as $ay) {
            if (!empty($ay->acad_year_id)) {
              echo "<option value='$ay->acad_year_id' $ay->selected>$ay->acad_year_name</option>";
            }
          }
          ?>
          </select>
          <?php }?>

      </div>
  </div>

  <div class="form-group">
      <label class=" col-md-12" for="student_name"><b>Student's Name <font color="red">*</font></b> </label>
      <div class="col-md-12">
          <input type="text" required="" name="student_name" id="student_name" placeholder="Enter Student's Name"
              onkeydown="return /[a-z ]/i.test(event.key)" class="form-control">
          <span class="help-block"><?php 
      if(!$this->settings->getSetting('enquiry_help_block_student_name')){
        echo  'As per Official Records / SSLC Report Card';
      }else{
        echo  $this->settings->getSetting('enquiry_help_block_student_name');
      }
    ?></span>
      </div>
  </div>

  <div class="form-group">
      <?php if(!in_array('student_dob', $disabled_fields)) :  ?>
      <div class="form-group">
          <label class="col-md-12" for="student_dob"><b>Date of
                  Birth<?php if($required_fields['student_dob']['required']=='required') echo' <font color="red">*</font>' ?></b>
          </label>
          <div class="col-md-12">
              <div class="input-group">
                  <input type="text" <?php echo $required_fields['student_dob']['required'] ?> autocomplete="off"
                      data-date-end-date="0d" class="form-control date_pick" id="student_dob"
                      name="student_dob" onkeydown="return false" placeholder="Enter Student's Date Of Birth">
                  <span class="input-group-addon">
                      <span class="glyphicon glyphicon-calendar"></span>
                  </span>
              </div>
              <span class="help-block">
                  <?php 
            if(!$this->settings->getSetting('enquiry_help_block_student_dob')){
              echo  'As per Official Records / SSLC Report Card. And Format should be dd-mm-yyyy';
            }else{
              echo  $this->settings->getSetting('enquiry_help_block_student_dob');
            }
          ?>
              </span>
              <span class="help-block"><?= $dob_instruction ?></span>
              <?php if($this->settings->getSetting('enquiry_age_cal')){ ?>
              <label class="control-label" id="age_cal"></label>
              <?php } ?>
          </div>
      </div>
      <?php endif ?>

      <?php $label = $this->settings->getSetting('your_word_for_class') ?>
      <?php if(!in_array('class_name', $disabled_fields)) :  ?>
      <div class="form-group">
          <label class="col-md-12" for="class_name"><b>Looking for
                  <?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?>
                  <?php if($required_fields['class_name']['required']=='required') echo' <font color="red">*</font>' ?></b>
          </label>
          <div class="col-md-12">
              <select class="form-control" id="class_name" <?php echo $required_fields['class_name']['required'] ?>
                  name="class_name">
                  <option value="">Select Grade</option>
              </select>
              <span class="help-block">Grade for which admission is being sought</span>
          </div>
      </div>
      <?php endif ?>

      <?php if(!in_array('gender', $disabled_fields)) :  ?>
      <label
          class="col-md-12"><b>Gender<?php if($required_fields['gender']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label><br>
      <div class="col-md-9">
          <label class="radio-inline pt-0" for="gender-0">
              <input type="radio" data-parsley-group="block1" class="gender-radio" name="gender" id="gender-0" value="M"
                  checked>
              Male
          </label>
          <label class="radio-inline pt-0" for="gender-1">
              <input type="radio" data-parsley-group="block1" class="gender-radio" name="gender" id="gender-1"
                  value="F">
              Female
          </label>
          <label class="radio-inline pt-0" for="gender-2">
              <input type="radio" data-parsley-group="block1" class="gender-radio" name="gender" id="gender-2"
                  value="O">
              Others
          </label>
      </div>
      <?php endif ?>
  </div>

  <div class="form-group" id="age_dispaly" style="display: none;">
      <label class="col-md-12"><b>Age as on 31st May <span id="acadmicYear"></b> </label>
      <div class="col-md-12">
          <label class="" id="age"></label>
      </div>
  </div>

  <?php if(!in_array('parent_name', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="parent_name"><b>Parent's
              Name<?php if($required_fields['parent_name']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" id="parent_name" name="parent_name" placeholder="Enter Father/Mother/Guardian Name"
              onkeydown="return /[a-z ]/i.test(event.key)" <?php echo $required_fields['parent_name']['required'] ?>
              class="form-control">
      </div>

  </div>
  <?php endif ?>

  <?php if(!in_array('father_name', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="father_name"><b>Father's
              Name<?php if($required_fields['father_name']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="father_name" id="father_name" placeholder="Enter Father's Name"
              onkeydown="return /[a-z ]/i.test(event.key)" <?php echo $required_fields['father_name']['required'] ?>
              class="form-control">
          <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
          <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
          <?php } else{ ?>
          <span class="help-block">As per Aadhar Card</span>
          <?php }?>
      </div>

  </div>
  <?php endif ?>

  <?php if(!in_array('mother_name', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="mother_name"><b>Mother's
              Name<?php if($required_fields['mother_name']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="mother_name" id="mother_name" placeholder="Enter Mother's Name"
              onkeydown="return /[a-z ]/i.test(event.key)" <?php echo $required_fields['mother_name']['required'] ?>
              class="form-control">
          <?php if($this->settings->getSetting('enquiry_help_block_parent_name')) { ?>
          <span class="help-block"><?php echo $this->settings->getSetting('enquiry_help_block_parent_name')?></span>
          <?php } else{ ?>
          <span class="help-block">As per Aadhar Card</span>
          <?php }?>

      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('mobile_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="mobile_number"><b>Parent Mobile
              Number<?php if($required_fields['mobile_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="mobile_number" id="mobile_number" placeholder="Enter Mobile Number"
              <?php echo $required_fields['mobile_number']['required'] ?> class="form-control"
              data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
          <div id="result" style="display:inline;"></div>
          <p style="color:green;display:none ;font-weight:bold" id="verified">Verified&nbsp;<span id="spanid1"></span>
          </p>
      </div>
      <?php if($otp_verification) { ?>
      <input type="hidden" id="is_registered" name="is_registered" value="0">
      <input type="hidden" id="is_otp_verified" value="">
      <div class="col-md-12" id="otp_div" style="display:none;margin-top:10px">
          <input class="form-control" name="mobile_number_otp" id="mobile_number_otp" placeholder="Enter OTP Number"
              type="text" minlength="6" maxlength="6">
          <p style="color:red;display:none ;" id="wrong_otp">Enter the correct Otp</p>
      </div>
      <div class="col-md-12 my-3" id="btn_div">
          <button id="sendOTP" onclick="send_otp()" type="button" class="btn btn-info"
              style="background-color:green;color:white;border-radius:1rem">Send OTP</button>
          <button type="button"
              style="display:none;margin-right: 4px;background-color:green;color:white;border-radius:1rem"
              id="verifytype" class="btn btn-primary verify">Verify</button>
          <button id="reset_btn" onclick="reset_fields()" type="button" class="btn btn-warning"
              style="color:white;border-radius:1rem;display:none">Reset</button>
          <button onclick="resend_otp()" type="button" class="btn btn-info" id="show_hide_resend"
              style="background-color:orange;border-color:white;color:white;border-radius:1rem;display:none">Re-Send
              OTP</button>
          <b id="show_hide_timer" style="font-size: 13px;display:none">Resend OTP in <span id="timer"></span> </b>
      </div>
      <?php } ?>
  </div>
  <?php endif ?>

  <?php if(!in_array('student_phone_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="student_phone_number"><b>Student's Mobile
              Number<?php if($required_fields['student_phone_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="student_phone_number" id="student_phone_number"
              placeholder="Enter Student's Mobile Number"
              <?php echo $required_fields['student_phone_number']['required'] ?> class="form-control"
              data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('father_phone_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="father_phone_number"><b>Father's Mobile
              Number<?php if($required_fields['father_phone_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>

      <div class="col-md-12" style="display: flex;">

          <?php if(!in_array('f_country_code', $disabled_fields)) :  ?>

          <?php 
            $array = array();
            foreach ($this->config->item('country_codes') as $key => $code) {
                $array[$code] =  $code;
            }
            echo form_dropdown("f_country_code", $array,  "id='f_country_code' class='form-control'");
        ?>

          <?php endif ?>


          <input type="number" name="father_phone_number" id="father_phone_number"
              placeholder="Enter Father's Mobile Number"
              <?php echo $required_fields['father_phone_number']['required'] ?> class="form-control"
              data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('mother_phone_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="mother_phone_number"><b>Mother's Mobile
              Number<?php if($required_fields['mother_phone_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>

      <div class="col-md-12" style="display: flex;">

          <?php if(!in_array('m_country_code', $disabled_fields)) :  ?>

          <?php 
            $array = array();
            foreach ($this->config->item('country_codes') as $key => $code) {
                $array[$code] =  $code;
            }
            echo form_dropdown("m_country_code", $array,  "id='m_country_code' class='form-control'");
        ?>

          <?php endif ?>


          <input type="number" name="mother_phone_number" id="mother_phone_number"
              placeholder="Enter Mother's Mobile Number"
              <?php echo $required_fields['mother_phone_number']['required'] ?> class="form-control"
              data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('wtsapp_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="wtsapp_number"><b>Whatsapp
              Number<?php if($required_fields['wtsapp_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="wtsapp_number" id="wtsapp_number" placeholder="Enter Whatsapp number"
              <?php echo $required_fields['wtsapp_number']['required'] ?> class="form-control"
              data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('alternate_mobile_number', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="alternate_mobile_number"><b>Alternate Mobile
              Number<?php if($required_fields['alternate_mobile_number']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="alternate_mobile_number" id="alternate_mobile_number"
              <?php echo $required_fields['alternate_mobile_number']['required'] ?>
              placeholder="Enter Alternate mobile number" class="form-control" data-parsley-pattern="^[0-9 -()+]+$"
              data-parsley-length="[8, 15]">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('email', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="email"><b>Parent Email id
              <?php if($required_fields['email']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="email" name="email" id="email" placeholder="Enter Email-id"
              <?php echo $required_fields['email']['required'] ?> class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if ($student_caste_present_in_db == 1) { ?>
  <?php if(!in_array('category', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12"
          for=""><b>Category<?php if($required_fields['category']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select name="category" id="category" class="form-control select2"
              <?php echo $required_fields['category']['required'] ?> onchange="getCaste()">
              <option value="">Select Category</option>
              <?php foreach ($categoryOptions as $category) { ?>
              <option value="<?php echo $category->value ?>">
                  <?php echo $category->category ?>
              </option>
              <?php } ?>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('caste', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12"
          for=""><b>Caste<?php if($required_fields['caste']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control select2" name="caste" title='Select caste' id="caste"
              onchange="backFillCategory()">
              <?php echo '<option value="0">Select caste</option>' ?>
              <?php foreach ($casteOptions as $c) { ?>
              <option data-cate-id="<?php echo $c->category ?>" value="<?php echo $c->caste ?>" >
                  <?php echo $c->caste ?>
              </option>
              <?php } ?>
          </select>
      </div>
  </div>
  <?php endif ?>
  <?php }else { ?>
  <?php if(!in_array('category', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12"
          for=""><b>Category<?php if($required_fields['category']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
        <select name="category" id="category" class="form-control">
            <option value="">Select Category</option>
        <?php  foreach ($this->settings->getSetting('category') as $key => $category) { ?>
            <option value="<?= $key ?>"><?= $category?></option>
        <?php } ?>
        </select>
        
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('caste', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12"
          for=""><b>Caste<?php if($required_fields['caste']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="caste" id="caste" placeholder="Enter Caste"
              <?php echo $required_fields['caste']['required'] ?> class="form-control">
      </div>
  </div>
  <?php endif ?>
  <?php }?>



  <?php if(!in_array('student_email_id', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="student_email_id"><b>Student's Email
              id<?php if($required_fields['student_email_id']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="email" name="student_email_id" id="student_email_id" placeholder="Enter Student's Email-id"
              <?php echo $required_fields['student_email_id']['required'] ?> class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('father_email_id', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="father_email_id"><b>Father's Email
              id<?php if($required_fields['father_email_id']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="email" name="father_email_id" id="father_email_id" placeholder="Enter Father's Email-id"
              <?php echo $required_fields['father_email_id']['required'] ?> class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('mother_email_id', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="mother_email_id"><b>Mother's Email
              id<?php if($required_fields['mother_email_id']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="email" name="mother_email_id" id="mother_email_id" placeholder="Enter Mother's Email-id"
              <?php echo $required_fields['mother_email_id']['required'] ?> class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('parent_occupation', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="parent_occupation"><b>Parent's
              Occupation<?php if($required_fields['parent_occupation']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="parent_occupation" id="parent_occupation"
              <?php echo $required_fields['parent_occupation']['required'] ?> placeholder="Enter Parent's Occupation"
              class="form-control">
      </div>
  </div>

  <?php endif ?>


  <?php if(!in_array('father_occupation', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="father_occupation"><b>Father's
              Occupation<?php if($required_fields['father_occupation']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="father_occupation" id="father_occupation"
              <?php echo $required_fields['father_occupation']['required'] ?> placeholder="Enter Father's Occupation"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('mother_occupation', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="mother_occupation"><b>Mother's
              Occupation<?php if($required_fields['mother_occupation']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="mother_occupation" id="mother_occupation"
              <?php echo $required_fields['mother_occupation']['required'] ?> placeholder="Enter Mother's Occupation"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('residential_address', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="residential_address"><b>Residential
              Address<?php if($required_fields['residential_address']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <textarea rows="3" class="form-control" placeholder="Enter your Residential Address"
              name="residential_address" id="residential_address"
              <?php echo $required_fields['residential_address']['required'] ?>></textarea>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('current_city', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="current_city"><b>Student's Current
              City<?php if($required_fields['current_city']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="current_city" id="current_city"
              <?php echo $required_fields['current_city']['required'] ?> placeholder="Enter Student's Current City"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('current_country', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="current_country"><b>Student's Current
              Country<?php if($required_fields['current_country']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" <?php echo $required_fields['current_country']['required'] ?>
              name="current_country" id="current_country">
              <option value="">Select Country</option>
              <?php foreach ($this->config->item('country') as $key => $value) { ?>
              <option value="<?php echo $value ?>"><?php echo $value ?></option>
              <?php } ?>
          </select>

      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('board_opted', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="board_name"><b>Board/Curriculum Interested
              in<?php if($required_fields['board_opted']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="board_name" <?php echo $required_fields['board_opted']['required'] ?>
              name="board_opted">
              <?php 
          if (count($boards) == 1) { ?>
              <?php foreach ($boards as $key => $value) { ?>
              <option value="<?php echo $value; ?>"><?php echo $value; ?></option>
              <?php } ?>
              <?php }else{ ?>
              <option value="">Select Board</option>
              <?php foreach ($boards as $key => $value) { ?>
              <option value="<?php echo $value; ?>"><?php echo $value; ?></option>
              <?php }
          } ?>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('enquiry_combination', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12"
          for="enquiry_combination"><b>Courses<?php if($required_fields['enquiry_combination']['required']=='required') echo'<font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="enquiry_combination"
              <?php echo $required_fields['enquiry_combination']['required'] ?> name="enquiry_combination">
              <option value="">Select Combination/Program</option>
              <?php if($enquiry_combination) { ?> 
              <?php foreach ($enquiry_combination as $key => $val) { ?>
              <option value="<?php echo $val ?>"><?php echo str_replace('_', ' ', $val) ?></option>
              <?php } }?>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('student_current_school', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="student_current_school"><b>Previous/Current Institution
              Name<?php if($required_fields['student_current_school']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="student_current_school"
              <?php echo $required_fields['student_current_school']['required'] ?> placeholder="Enter Institution Name"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('board', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="board"><b>Previous/Current Institution
              Board<?php if($required_fields['board']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="board" name="board"
              <?php echo $required_fields['board']['required'] ?>>
              <option value="">Select Board</option>
              <option value="CBSE">CBSE</option>
              <option value="ICSE">ICSE</option>
              <option value="State">State</option>
              <option value="Home School">Home School</option>
              <option value="IGCSE">IGCSE</option>
              <option value="IBDP">IBDP</option>
              <option value="NIOS">NIOS</option>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('medium_of_instruction', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="medium_of_instruction"><b>Medium of
              Instruction<?php if($required_fields['medium_of_instruction']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="medium_of_instruction" name="medium_of_instruction"
              <?php echo $required_fields['medium_of_instruction']['required'] ?>>
              <option value="">Select Medium of Instruction</option>
              <option value="English">English</option>
              <option value="Kannada">Kannada</option>
              <option value="Hindi">Hindi</option>
              <option value="Sanskrit">Sanskrit</option>
              <option value="Malayalam">Malayalam</option>
              <option value="Others">Others</option>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('examination_passed', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="examination_passed"><b>Highest Examination
              Passed<?php if($required_fields['examination_passed']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" class="form-control" id="examination_passed" name="examination_passed"
              placeholder="Enter the Highest Examination Passed">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('currently_studying', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="currently_studying"><b>Currently Studying Grade<?php if($required_fields['currently_studying']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" class="form-control" id="currently_studying" name="currently_studying"
              placeholder="Enter Currently studying grade" <?php echo $required_fields['currently_studying']['required'] ?>>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('year_of_passing', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="year_of_passing"><b>Year of
              Passing<?php if($required_fields['year_of_passing']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" class="form-control" id="year_of_passing" name="year_of_passing"
              placeholder="Enter Year of Passing">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('total_marks', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="total_marks"><b>Total
              Marks<?php if($required_fields['total_marks']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" class="form-control" id="total_marks" name="total_marks"
              placeholder="Enter the total Marks">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('marks_in_percentage', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="marks_in_percentage "><b>Total Marks in
              Percentage<?php if($required_fields['marks_in_percentage']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" class="form-control" id="marks_in_percentage" name="marks_in_percentage"
              placeholder="Enter the Marks in percentage">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('boarding_type', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="boarding_type"><b>Preferred Boarding
              Type<?php if($required_fields['boarding_type']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control " id="boarding_type" name="boarding_type"
              <?php echo $required_fields['boarding_type']['required'] ?>>

              <?php 
           ?>
              <?php 
           $boarding =  $this->settings->getSetting('boarding');
           if (count($boarding) == 1) {
           foreach ($boarding as $key => $value) { ?>
              <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
              <?php } ?>
              <?php }else{ ?>
              <option value="">Select Preferred Boarding Type</option>
              <?php foreach ($boarding as $key => $value) { ?>
              <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
              <?php }
          } ?>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('enquiry_additional_coaching', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="enquiry_additional_coaching"><b>Additional
              Coaching<?php if($required_fields['enquiry_additional_coaching']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="enquiry_additional_coaching"
              <?php echo $required_fields['enquiry_additional_coaching']['required'] ?>
              name="enquiry_additional_coaching">
              <option value="">Select Additional Coaching</option>
              <?php foreach ($enquiry_additional_coaching as $key => $val) { ?>
              <option value="<?php echo $val ?>"><?php echo str_replace('_', ' ', $val) ?></option>
              <?php } ?>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('is_transfer_certificate_available', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="is_transfer_certificate_available"><b>Is Transfer Certificate
              Available?<?php if($required_fields['is_transfer_certificate_available']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="is_transfer_certificate_available"
              name="is_transfer_certificate_available"
              <?php echo $required_fields['is_transfer_certificate_available']['required']; ?>>
              <option value="">Select Transfer Certificate Available</option>
              <option value="Yes">Yes</option>
              <option value="No">No</option>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('previous_academic_report', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="previous_academic_report"><b>Previous Academic
              Reports<?php if($required_fields['previous_academic_report']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control " id="previous_academic_report" name="previous_academic_report"
              <?php echo $required_fields['previous_academic_report']['required']; ?>>
              <option value="">Select Academic Reports</option>
              <option value="avalibale">Available</option>
              <option value="Not Avalibale">Not Available</option>
          </select>
      </div>
  </div>
  <?php endif ?>



  <?php if(!in_array('additional_education_needs', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="additional_education_needs"><b>Does the Student have any Special education
              needs?<?php if($required_fields['additional_education_needs']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="additional_education_needs"
              <?php echo $required_fields['additional_education_needs']['required'] ?>
              name="additional_education_needs_name">
              <option value="">Select Value</option>
              <option value="No">No</option>
              <option value="Yes">Yes</option>
          </select>
      </div>
  </div>

  <div id="additional_education_needs_yes" style="display: none;" class="form-group">
      <label class="col-md-12" for="additional_education_needs_details"></label>
      <div class="col-md-12">
          <input type="text" class="form-control" placeholder="Enter details" name="additional_education_needs_details"
              id="additional_education_needs_details">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('has_sibling', $disabled_fields)) :  ?>

  <div class="form-group">
      <label class="col-md-12"><b>Does student have sibling?</b></label>
      <div class="col-md-12">
          <label class="" for="no_sibling"><input type="radio" checked="" class="inline-checkbox" name="has_sibling"
                  id="no_sibling" value="no">&nbsp;&nbsp;No</label>&nbsp;&nbsp;&nbsp;&nbsp;
          <label class="" for="has_sibling"><input type="radio" class="inline-checkbox" name="has_sibling"
                  id="has_sibling" value="yes">&nbsp;&nbsp;Yes</label>
      </div>
  </div>

  <div class="form-group" id="in_school" style="display: none;">
      <label class="col-md-12"><b>Sibling studying in?</b></label>
      <div class="col-md-12">
          <label class="" for="same_school"><input type="radio" checked="" class="inline-checkbox" name="sibling_in"
                  id="same_school"
                  value="same_school">&nbsp;&nbsp;<?php echo $this->settings->getSetting('school_name'); ?></label>&nbsp;&nbsp;&nbsp;&nbsp;
          <label class="" for="other"><input type="radio" class="inline-checkbox" name="sibling_in" id="other"
                  value="other">&nbsp;&nbsp;Other</label>
      </div>
  </div>

  <div class="form-group" id="sibling_input_fields" style="display: none;">
        <label class="col-md-12" for="sibling_name"><b>Sibling's
                Name<?php if($required_fields['sibling_name']['required']=='required') echo' <font color="red">*</font>' ?></b>
        </label>
        <div class="col-md-12">
            <input type="text" name="sibling_name" id="sibling_name" placeholder="Enter Sibling's Name"
                class="form-control">
    </div>

      <label class="col-md-12" for="sibling_class"><b>Sibling's
              Grade<?php if($required_fields['sibling_class']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12" id="sibling_grade_dropdown">
            <select name="sibling_class" id="sibling_class" class="form-control">
            <option value="">Select Grade</option>
            <?php if(!empty($master_table_class_names)) { ?>
                <?php foreach($master_table_class_names as $key =>$val) { ?> 
                    <option value="<?= $val->class_name ?>"><?= $val->class_name ?></option>
                <?php } } ?>
            </select>
      </div>

      

  </div>

  <div class="form-group" id="sibling_data" style="display: none;">
      <label class="col-md-12" for="sibling_studying"><b>Sibling's School?
              <?php if($required_fields['sibling_class']['required']=='required') echo' <font color="red">*</font>' ?></b></label>
      <div class="col-md-12">
          <input type="text" id="sibling_studying" name="sibling_studying"
              placeholder="Enter Sibling's  School" class="form-control">
      </div>

  </div>
  <?php endif ?>

  <?php if(!in_array('transportation_facility', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="transportation_facility"><b>Need Transportation
              Facility?<?php if($required_fields['transportation_facility']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="transportation_facility" name="transportation_facility"
              <?php echo $required_fields['transportation_facility']['required']; ?>>
              <option value="">Select Option</option>
              <option value="Yes">Yes</option>
              <option value="No">No</option>
          </select>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('interested_in', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="interested_in"><b>Curriculam interested
              in<?php if($required_fields['interested_in']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="interested_in" id="interested_in"
              <?php echo $required_fields['interested_in']['required'] ?> placeholder="Curricula interested in"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('referred_by', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="referred_by"><b>Reffered By
              <?php if($required_fields['referred_by']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
        <div class="col-md-3 p-0">
        <select name="class_id" id="class_id" class="form-control" onchange="get_reffered_student_list()">
            <option value="">Select Grade</option>
            <?php if(!empty($class_names)) { ?>
                <?php foreach($class_names as $key =>$val) { ?> 
                    <option value="<?= $val->id ?>"><?= $val->class_name ?></option>
                <?php } } ?>
        </select>
        </div>
        <div class="col-md-6" id="student_name_list">
            <select name="referred_by" id="referred_by" class="form-control " title="Select Student">
                <option value="">Select Student</option>
            </select>
        </div>
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('university', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="previous_board_university"><b>Previous
              Board/University<?php if($required_fields['university']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="previous_board_university" id="previous_board_university"
              <?php echo $required_fields['university']['required'] ?> placeholder="Enter Previous Board/University"
              class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('reason_for_leaving_school', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="reason_for_leaving_school"><b>Reason For Leaving
              School<?php if($required_fields['reason_for_leaving_school']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <input type="text" name="reason_for_leaving_school" id="reason_for_leaving_school"
              <?php echo $required_fields['reason_for_leaving_school']['required'] ?>
              placeholder="Enter Reason For Leaving School" class="form-control">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('got_to_know_by', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="get_others_name"><b>How did you get to know about
              us?<?php if($required_fields['got_to_know_by']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <select class="form-control" id="get_others_name" <?php echo $required_fields['got_to_know_by']['required'] ?>
              name="how_to_know">
              <option value="">Select Value</option>
              <?php foreach ($know_aboutUs as $key => $val) { ?>
              <option value="<?php echo $val ?>"><?php echo str_replace('_', ' ', $val) ?></option>
              <?php } ?>
          </select>
      </div>
  </div>

  <div id="known-div" style="display: none;" class="form-group">
      <label class="col-md-12" for="known_by"></label>
      <div class="col-md-12">
          <input type="text" class="form-control" placeholder="Write where did you get to know" name="known_by"
              id="known_by">
      </div>
  </div>
  <?php endif ?>

  <?php if(!in_array('message', $disabled_fields)) :  ?>
  <div class="form-group">
      <label class="col-md-12" for="message"><b>Anything you want to share with
              us?<?php if($required_fields['message']['required']=='required') echo' <font color="red">*</font>' ?></b>
      </label>
      <div class="col-md-12">
          <textarea rows="5" class="form-control" placeholder="Enter Your Message" name="message" id="message"
              <?php echo $required_fields['message']['required'] ?>></textarea>
      </div>
  </div>
  <?php endif ?>

  <?php if($captcha_code_verification) { ?>
  <!-- <input type="hidden" id="" value="">
    <div class="form-group my-4">
      <h5 class="col-md-12 "><b>Enter Captch </b> </h5>
      <div class="col-md-4" id="captcha-container">
        <p id="captcha-code"></p>
        <input type="text" class="form-control" placeholder="Enter Captcha" id="captcha-input" />
        <p style="color:red;display:none" id="captcha_text">Enter the correct captcha</p>       
      </div>
        <div class="col-md-4" style="margin-top:30px ;">
        <button type="button" id="submit-btn" class="btn btn-dark">Submit</button>
      </div>
    </div> -->
  <input type="hidden" id="is_captcha_verified" value="">
  <div class="form-group">
      <div class="col-md-4" id="">
          <div class="g-recaptcha" data-callback="recaptchaCallback" data-sitekey="<?= $captcha_site_key ?>"
              id="recaptcha_container"></div>
          <p id="click-event-result"></p>
      </div>
  </div>
  <?php }?>



  <style type="text/css">
@media only screen and (max-width: 768px) {
    input[class=gender-radio] {
        margin: 1px -14px -6px;
    }

    input[name=has_sibling] {
        margin: 0px 3px 0;
    }

    input[name=sibling_in] {
        margin: 1px 0px 0px;
    }
}

.col-md-12 {
    padding-left: 10px;
}

#captcha-container {
    text-align: center;
}

#captcha-code {
    font-size: 15px;
    font-weight: bold;
    letter-spacing: 15px;
    margin-bottom: 9px;
}

#captcha-input {
    font-size: 13px;
    /* text-transform: uppercase; */
}

#submit-btn {
    /* background-color: #007bff; */
    color: #ffffff;
    border: none;
    padding: 3px 14px;
    font-size: 16px;
    cursor: pointer;
}

#class_name option {
    width: 250px;
}
  </style>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
  <script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

  <script src='https://www.google.com/recaptcha/api.js'></script>
  <!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->

  <script type="text/javascript">
var casteOptions = <?php echo json_encode($casteOptions); ?>;
casteOptions = Object.entries(casteOptions)

function getCaste() {
    const casteCategory = $("#category option:selected").text();
    let castHtml = '<option value="0">Select caste</option>'
    casteOptions.forEach(c => {
        if (c[1].category.trim() == casteCategory.trim()) {
            castHtml += `<option data-cate-id="${c[1].category}" value="${c[1].caste}">${c[1].caste}</option>`;
        }
    })
    $("#caste").html(castHtml);
}

function backFillCategory() {
    const categoryName = $("#caste :selected")[0].dataset.cateId;
    const allCategories = document.querySelectorAll("#category")[0].querySelectorAll("option")

    allCategories.forEach(category => {
        if (category.value.trim() == categoryName.trim()) {
            category.selected = "Selected";
        }
    });

    $("#category").html(allCategories);
}



$(document).ready(function() {
    $(".select2").select2();
    var check_register = '<?php echo $check_registered_mobile_number ?>';
    if (check_register == 1) {
        $('#sendOTP').attr('disabled', 'disabled');
        var typingTimer;
        var doneTypingInterval = 500; // 1 second delay after typing stops

        $('#mobile_number').on('input', function() {
            var school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
            clearTimeout(typingTimer);
            var mobileNumber = $(this).val().replace(/\s/g, '');
            if (mobileNumber == '') {
                $('#sendOTP').attr('disabled', 'disabled');
            }
            var input_str = mobileNumber.replace(/^0+/, '');
            // alert(input_str);
            if (input_str) {
                typingTimer = setTimeout(function() {
                    // Make an AJAX request to the server to check registration status
                    $.ajax({
                        url: '<?php echo site_url('enquiry/users/check_registerd_number'); ?>',
                        method: 'POST',
                        data: {
                            'mobileNumber': input_str
                        },
                        success: function(response) {
                            var response = $.parseJSON(response);
                            if (response) {
                                $('#result').html(
                                    `<b style="color:green">Registered with ${school_name} </b>`
                                );
                                $('#is_registered').val(1);
                                $('#sendOTP').removeAttr('disabled');
                            } else {
                                $('#result').html(
                                    `<b style="color:red">Not Registered with ${school_name}  </b>`
                                );
                                $('#is_registered').val(0);
                                $('#sendOTP').attr('disabled', 'disabled');
                            }
                        },
                        error: function() {
                            $('#result').text(
                                'Error occurred while checking registration.');
                        }
                    });
                }, doneTypingInterval);
            } else {
                $('#result').empty();
            }
        });
    }
});

function recaptchaCallback() {
    var otp_verification = '<?php echo $otp_verification ?>';
    var isChecked = grecaptcha.getResponse().length > 0;
    if (otp_verification == 1) {
        var is_otp_verified = $('#is_otp_verified').val();
        if (is_otp_verified) {
            $('#subBtn').removeAttr("disabled");
        }
    } else {
        $('#subBtn').removeAttr("disabled");
    }
};




$(document).ready(function() {
    generateCaptcha();

    $('#submit-btn').click(function() {
        validateCaptcha();
    });

    var dob_maxdate = new Date();
    dob_maxdate.setDate(dob_maxdate.getDate());

    var dob_mindate = new Date();
    dob_mindate.setFullYear(dob_mindate.getFullYear() - 50);
    onchange_acadyear()
    $('.date_pick').datetimepicker({
        format: 'DD-MM-YYYY',
        viewMode: 'years',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    }).on('dp.change', function(e) {
        var d = new Date(e.date);
        var age = getAge(e.date);
        $('#age_cal').html(age);
        onchange_acadyear();
    });
});

function generateCaptcha() {
    var captchaCode = generateRandomAlphabeticCode(6); // Change the length as needed
    $('#captcha-code').text(captchaCode);
    $('#captcha-input').val('');
    $('#captcha-container').data('code', captchaCode);
}

function generateRandomAlphabeticCode(length) {
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    var code = '';

    for (var i = 0; i < length; i++) {
        var randomIndex = Math.floor(Math.random() * characters.length);
        code += characters.charAt(randomIndex);
    }

    return code;
}

function validateCaptcha() {
    var otp_verification = '<?php echo $otp_verification ?>';
    var userInput = $('#captcha-input').val();
    var correctAnswer = $('#captcha-code').text();
    if (userInput == correctAnswer) {
        // $('#tick_mark').text('✔');
        $('#captcha-input').attr('readonly', 'readonly');
        $('#captcha_text').hide();
        $('#submit-btn').html('Verified');
        if (otp_verification == 1) {
            if ($('#is_otp_verified').val()) {
                $('#subBtn').removeAttr("disabled");
            }
        } else {
            $('#subBtn').removeAttr("disabled");
        }
    } else {
        // $('#tick_mark').text('✘');
        $('#captcha_text').show();
        $('#captcha-input').val('');
        $('#tick_mark').css('color', 'red');
        generateCaptcha();

    }
}

function onchange_acadyear() {
    var dob_validation = '<?php echo $this->settings->getSetting('enquiry_dob_validation'); ?>';
    var acadYearId = $('#acadYearId').val();
    var dob = $('.date_pick').val();
    if (dob_validation == 1) {
        $.post('<?php echo site_url('enquiry/users/get_class_sections_validation');?>', {
            acadYearId: acadYearId,
            dob: dob
        }, function(data) {
            var cls_section = $.parseJSON(data);
            var output = '<option value="">Select Grade</option>';
            var len = cls_section.length;
            for (var i = 0, j = len; i < j; i++) {
                if (cls_section[i].status == 'Eligible') {
                    output += '<option value="' + cls_section[i].id + '">' + cls_section[i].class_name + ' ' +
                        '(' + cls_section[i].status + ')' + '</option>';
                } else if (cls_section[i].status == 'Not-Eligible' || cls_section[i].status == 'Seats Full') {
                    output += '<option disabled style="color: lightgray; font-style: italic;" value="' +
                        cls_section[i].id + '">' + cls_section[i].class_name + ' ' + '(' + cls_section[i]
                        .status + ')' + '</option>';
                } else {
                    output += '<option value="' + cls_section[i].id + '">' + cls_section[i].class_name +
                        '</option>';
                }
            };
            $('#class_name').html(output);
        });
    } else {
        $.post('<?php echo site_url('enquiry/users/get_class_sections');?>', {
            acadYearId: acadYearId
        }, function(data) {
            var cls_section = $.parseJSON(data);
            var output = '<option value="">Select Class </option>';
            var len = cls_section.length;
            for (var i = 0, j = len; i < j; i++) {
                output += '<option value="' + cls_section[i].id + '">' + cls_section[i].class_name +
                    '</option>';
            };
            $('#class_name').html(output);
        });
    }

}

$("#get_others_name").change(function() {
    var selected = $(this).val();
    if (selected == 'Others') {
        $("#known-div").show();
        $("#known_by").attr('required', true);
    } else {
        $("#known_by").attr('required', false);
        $("#known-div").hide();
    }
});

$("#additional_education_needs").change(function() {
    var selected = $(this).val();
    if (selected == 'Yes') {
        $("#additional_education_needs_yes").show();
        $("#additional_education_needs_details").attr('required', true);
    } else {
        $("#additional_education_needs_details").attr('required', false);
        $("#additional_education_needs_yes").hide();
    }
});

$('input[type=radio][name=has_sibling]').change(function() {
    if (this.value == 'yes') {
        $("#in_school").show();
        if ($('#same_school').is(':checked')) {
            $("#sibling_input_fields").show();
            $('#sibling_data').hide();
            $('#sibling_class').attr('required', 'required');
            $('#sibling_name').attr('required', 'required');
            $('#sibling_studying').removeAttr('required');
        } else {
            $('#sibling_data').show();
            $("#sibling_input_fields").hide();
            $('#sibling_class').removeAttr('required');
            $('#sibling_name').removeAttr('required');
            $('#sibling_studying').att('required', 'required');
        }
    } else {
        $("#in_school").hide();
        $("#sibling_input_fields").hide();
        $("#sibling_data").hide();
        $('#sibling_class').removeAttr('required');
        $('#sibling_name').removeAttr('required');
        $('#sibling_studying').removeAttr('required');
    }
});

function construct_grade_box(sibling_studying_in){
    var grade_list = '<?php echo json_encode($master_table_class_names) ?>';
    var grades = $.parseJSON(grade_list);
    if(sibling_studying_in == 'same_school'){
        var html = '<select name="sibling_class" id="sibling_class" class="form-control">';
        html = '<option value="">Select Grade</option>';
        for(var i=0;i<grades.length;i++){
            html += `<option value="${grades[i].class_name}">${grades[i].class_name}</option>`;
        }
        html += '</select>';
        $('#sibling_grade_dropdown').html(html);
    }else{
        var html = `<input type="text" name="sibling_class" id="sibling_class" placeholder="Enter Sibling's Grade"
              class="form-control">`;
        $('#sibling_grade_dropdown').html(html);
    }
}

$('input[type=radio][name=sibling_in]').change(function() {
    if (this.value == 'other') {
        $("#sibling_data").show();
        $("#sibling_studying").prop('required', true);
        $("#sibling_input_fields").hide();
        $('#sibling_class').removeAttr('required', 'required');
        $('#sibling_name').removeAttr('required', 'required');
    } else {
        $("#sibling_data").hide();
        $("#sibling_studying").prop('required', false);
        $("#sibling_input_fields").show();
    }
    construct_grade_box(this.value);

});

function getAge(dateVal) {
    var cal_date = '<?php echo $this->settings->getSetting('enquiry_date_to_calculate_age') ?>';
    var onSelectDate = new Date(dateVal);
    var monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October',
        'November', 'December'
    ];
    if (cal_date) {
        today = new Date(cal_date);
    } else {
        today = new Date();
    }
    var day = today.getDate();
    var month = monthNames[today.getMonth()];
    var year = today.getFullYear();
    var formattedDate = day + ' ' + month + ' ' + year;
    ageInMilliseconds = new Date(today - onSelectDate),
        years = ageInMilliseconds / (24 * 60 * 60 * 1000 * 365.25),
        months = 12 * (years % 1),
        days = Math.floor(30 * (months % 1));
    return Math.floor(years) + ' years ' + Math.floor(months) + ' months ' + days + ' days as on ' + formattedDate;
}

function reset_fields() {
    var mobileNumber = $("#mobile_number").val().replace(/\s/g, '');
    var input_str = mobileNumber.replace(/^0+/, '');
    var check_registered_mobile_number = '<?php echo $check_registered_mobile_number ?>';
    $("input[name=mobile_number]").removeAttr('readonly');
    $('input[name=mobile_number]').val('');
    $("input[name=mobile_number_otp]").val('');
    $("#otp_div").hide();
    $('#sendOTP').attr('disabled', 'disabled');
    $('#result').empty();
    $('#is_registered').val(0);
    $('#sendOTP').show();
    $('#verifytype').hide();
    timerOn = false;
    resend_timer(-1);
    $('#show_hide_resend').hide();
    $('#show_hide_timer').hide();
    if (mobileNumber) {
        $.ajax({
            url: '<?php echo site_url('enquiry/users/delete_otp'); ?>',
            type: 'post',
            data: {
                'mobileNumber': input_str
            },
            success: function(data) {
                var retData = $.parseJSON(data);
                $('#reset_btn').hide();
                if (check_registered_mobile_number == 0) {
                    $('#sendOTP').removeAttr('disabled');
                }
            }
        });
    }
}

function send_otp() {
    var mobileNumber = $("#mobile_number").val().replace(/\s/g, '');
    if(mobileNumber.trim() == ''){
        alert('Please Provide a Mobile Number for OTP Verification');
        return;
    }
    var input_str = mobileNumber.replace(/^0+/, '');
    var check_register = '<?php echo $check_registered_mobile_number ?>';
    if (check_register == 1) {
        check_registerd_number(input_str);
    } else {
        ajaxcallotp_number(mobileNumber);
    }
}

function check_registerd_number(mobileNumber) {
    $.ajax({
        url: '<?php echo site_url('enquiry/users/check_registerd_number'); ?>',
        type: 'post',
        data: {
            'mobileNumber': mobileNumber
        },
        success: function(data) {
            var retData = $.parseJSON(data);
            if (retData) {
                $('#registered').show();
                $('#spanid').text('✔');
                ajaxcallotp_number(mobileNumber);
            } else {
                $(function() {
                    new PNotify({
                        title: 'Error',
                        text: 'Mobile Number is Not Registered.',
                        type: 'error',
                    });

                });
            }
        }
    });
}

function resend_otp() {
    var mobileNumber = $("#mobile_number").val();
    if(mobileNumber.trim() == ''){
        alert('Please Provide a Mobile Number for OTP Verification');
        return;
    }
    $('#mobile_number_otp').val('');
    $("#mobile_number_otp").removeAttr('required');
    ajaxcallotp_number(mobileNumber);
}

function ajaxcallotp_number(mobileNumber) {
    $.ajax({
        url: '<?php echo site_url('enquiry/users/sendOTP'); ?>',
        type: 'post',
        data: {
            'mobileNumber': mobileNumber
        },
        success: function(data) {
            var retData = $.parseJSON(data);
            console.log(retData);
            if (retData.status == 'ok') {
                $("#otp_div").show();
                $('#mobile_number').attr('readonly', true);
                $("#sendOTP").hide();
                $('#resendOTP').show();
                $('#verifytype').show();
                $('#show_hide_resend').hide();
                $('#show_hide_timer').show();
                $('#reset_btn').show();

                resend_timer(59);
            } else {
                $(function() {
                    new PNotify({
                        title: 'Error',
                        text: retData.msg,
                        type: 'error',
                    });
                });
            }
        }
    });
}

var timerOn = true;

function resend_timer(remaining) {
    var m = Math.floor(remaining / 60);
    var s = remaining % 60;
    m = m < 10 ? '0' + m : m;
    s = s < 10 ? '0' + s : s;
    document.getElementById('timer').innerHTML = m + ':' + s;
    remaining -= 1;

    if (remaining >= 0 && timerOn) {
        setTimeout(function() {
            resend_timer(remaining);
        }, 1000);
        return;
    }
    if (!timerOn) {
        // Do validate stuff here
        return;
    }
    // $('.initial').addClass('active');

    $('#show_hide_resend').show();
    $('#show_hide_timer').hide();

}

$(".verify").click(function(e) {
    var captcha_code_verification = '<?php echo $captcha_code_verification ?>';
    var otp = $('#mobile_number_otp').val();
    if (otp == '') {
        alert('Enter the OTP');
        return false;
    }
    var mobileNumber = $("#mobile_number").val();
    $.ajax({
        url: '<?php echo site_url('enquiry/users/verify_otp');?>',
        type: 'post',
        data: {
            'otp': otp,
            'mobileNumber': mobileNumber
        },
        success: function(data) {
            if (data == 0) {
                $(function() {
                    new PNotify({
                        title: 'Error',
                        text: 'Enter OTP number invalid',
                        type: 'error',
                    });
                });
                $('#errorPopUp').show();
                $('#wrong_otp').show();
                $('#error_otp').html('Incorrect OTP. Please try again');
            } else {
                $('#show_hide_resend').hide();
                $('.verify').hide();
                $('#show_hide_timer').hide();
                $('#registered').hide();
                $('#verified').show();
                $('#spanid1').text('✔');
                $('#otp_div').hide();
                $('#btn_div').hide();
                $('#mobile_number').attr('readonly', 'readonly');
                $('#reset_btn').hide();
                $('#is_otp_verified').val(1);
                if (captcha_code_verification == 1) {
                    var isChecked = grecaptcha.getResponse().length > 0;
                    if (isChecked) {
                        $('#subBtn').removeAttr("disabled");
                    }
                } else {
                    $('#subBtn').removeAttr("disabled");
                }
                timerOn = false;
                resend_timer(-1);
            }
        }
    });
});

function get_reffered_student_list(){
    var class_id = $('#class_id').val();
    $.ajax({
        url: '<?php echo site_url('enquiry/enquiry_staff/get_reffered_student_list');?>',
        type: 'post',
        data: {
            'class_id':class_id
        },
        success: function(data) {
            var student_list = $.parseJSON(data);
            var output = '<select name="referred_by" id="referred_by" class="form-control" title="Select Students">'
            output += '<option value="">Select Student</option>'
            var len = student_list.length;
            for (var i = 0, j = len; i < j; i++) {
                console.log(student_list[i].admission_no)
                console.log(student_list[i].student_name)
                output += '<option value="' + student_list[i].admission_no + '">' + student_list[i].student_name+'('+ student_list[i].admission_no +')'+
                    '</option>';
            };
            output += '</select>';
            $('#student_name_list').html(output);
            $('#referred_by').selectpicker({
                liveSearch: true, // Enables search functionality
                liveSearchPlaceholder: 'Search Students...', // Set placeholder text
            });
        }
    });
}

$(document).ready(function() {
    $('#referred_by').selectpicker({
        liveSearch: true, // Enables search functionality
        liveSearchPlaceholder: 'Search Students...', // Set placeholder text
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
});
  </script>