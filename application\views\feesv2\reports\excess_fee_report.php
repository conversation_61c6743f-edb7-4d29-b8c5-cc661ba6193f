<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Excess Fee report</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
              Excess Fee report
            </h3>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">
      
        <div>
          <div class="col-md-3 form-group">
            <p  style="margin-bottom: 4px; margin-left:3px;">Class</p>
            <?php 
              $array = array();
              foreach ($classes as $key => $class) {
                $array[$class->classId] = $class->className; 
              }
              echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All Classes' class='form-control classId select '");
            ?>
          </div>

          <div class="col-md-3 form-group">
            <p  style="margin-bottom: 4px; margin-left:3px;">Payment Type</p>
            <select class="form-control select" id="excess_payment_type" multiple title='Select Payment' >
              <option value="9">Cash</option>
              <option value="4">Cheque</option>
              <option value="1">DD</option>
              <option value="8">Net Banking</option>
              <option value="888">Adjust Amount</option>
            </select>
          </div>

          <div class="col-md-2 form-group" style="margin-top: 22px;">
            <input type="button" value="Get Report" id="getReport" class="btn btn-primary" onclick="get_excess_amount_report()">
          </div>
          <div class="col-md-12 text-center">
            <div class="progress" id="progress" style="display: none;">
              <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%">
              </div>
            </div>
          </div>

        </div>

        <div class="col-md-12">
          <div id="fee_excess_controls" class="mb-2"></div>
      
          <div id="fee_excess_scroll" style="max-height: 500px; overflow-y: auto; overflow-x: auto; width: 100%;">
          
          </div>
        </div>
      </div>


  </div> 
</div>

<script type="text/javascript">
  function Printprofile() {
    const printWindow = window.open('', '_blank');
    const mainTable = document.getElementById('fee_excess_data')?.outerHTML || '';

    printWindow.document.write(`
      <html>
        <head>
          <title>Excess Fee Report</title>
          <style>
            body {
              font-family: 'Poppins', sans-serif;
              padding: 20px;
            }
            h2, h3, h4, h5 {
              margin: 5px 0;
              text-align: center;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              font-size: 12px;
            }
            h3 {
              margin: 15px 0;
            }
            a {
              color: inherit !important;
              text-decoration: none !important;
            }
            @media print {
              table { page-break-inside: auto; }
              tr    { page-break-inside: avoid; }
              td    { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <h3>Excess Fee Details</h3>
          ${mainTable}
          <script>
            window.onload = function() {
              window.print();
            };
            window.onafterprint = function() {
              window.close();
            };
          <\/script>
        </body>
      </html>
    `);

    printWindow.document.close();
  }

</script>
<style type="text/css">
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

    table {
        font-family: 'Poppins', sans-serif !important;
    }
  input[type  ='range'] {
    margin: 0 auto;
    width: 100px;
  }

  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }
  #fee_excess_data {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
}
#fee_excess_data thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
}
#fee_excess_data th,
#fee_excess_data td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
}
#fee_excess_data tbody tr:nth-child(even) {
    background-color: #f9fafb;
}
#fee_excess_data tbody tr:hover {
    background-color: #f1f5f9;
}
#fee_excess_data tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
}
  #fee_excess_scroll {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: auto;
    width: 100%;
    /* Custom thick scrollbar for all browsers */
    scrollbar-width: thick;
    scrollbar-color: #888 #f1f1f1;
  }
  #fee_excess_scroll::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }
  #fee_excess_scroll::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 8px;
  }
  #fee_excess_scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
  }
  #fee_excess_data {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    table-layout: auto;
    margin: 0;
  }

  #fee_excess_data thead th{
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        z-index: 2;
    }
    #table-toolbar {
        position: static !important;
        background: none !important;
        z-index: 1;
    }
    /* For Firefox */
    .fee_excess_data {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }
</style>

<script>
  function get_excess_amount_report() {
    // Inject table directly into the scrollable container
    $('#fee_excess_scroll').html('');
    $("#progress").show();
    $('#getReport').prop('disabled', true).val('Please wait...');
    var classId =  $('#classId').val();
    var excess_payment_type =  $('#excess_payment_type').val();
    
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_excess_amount_details'); ?>',
      data: {'classId':classId,'excess_payment_type':excess_payment_type},
      type: "post",
      success: function (data) {
        $("#progress").hide();
        $('#getReport').prop('disabled', false).val('Get Report');
        var data = JSON.parse(data);
        $('#fee_excess_scroll').html( _construct_excess_amount_report(data));

        var dt = $('#fee_excess_data').DataTable({
          ordering:false,
          paging : false,
          info : false,
          "language": {
            "search": "",
            "searchPlaceholder": "Enter Search..."
          },
          dom: 'Bfrtip',
          buttons: [
            {
              extend: 'excelHtml5',
              text: '<button class="btn btn-info"  style="margin-right: -10px;"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              filename: 'Fee Excess Report'
            },
            {
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
                action: function ( e, dt, node, config ) {
                  Printprofile();
                }
            }
          ],
          initComplete: function() {
            // Move the DataTables buttons and search to the custom controls container
            var controls = $('#fee_excess_controls');
            controls.empty();
            controls.append($('.dt-buttons'));
            controls.append($('.dataTables_filter'));
          }
        });
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
  
  function _construct_excess_amount_report(excess_data) {
    var h_output = `
      <table id="fee_excess_data" class="table table-bordered">
      <thead>
        <tr style="white-space: nowrap">
          <th>#</th>
          <th>Created On </th>
          <th>Receipt Number </th>
          <th>Payment Type </th>
          <th>Student Name</th>
          <th>Class Section</th>
          <th>Excess Amount </th>
          <th>Amount Consumed </th>
          <th>Refund Amount</th>
          <th>Balance Amount</th>
          <th>Remarks</th>
          <th>Created By </th>
        </tr>
      </thead>
      <tbody>`;

    var j = 1;
    for(var i in excess_data) {
       var balance_excess = parseFloat(excess_data[i].total_amount) - parseFloat(excess_data[i].total_used_amount) - parseFloat(excess_data[i].refund_amount);
       var bal = balance_excess.toFixed(2);
      h_output += `
        <tr>
          <td>${j ++}</td>
          <td>${excess_data[i].created_date}</td>
          <td>${excess_data[i].receipt_number}</td>`;
          h_output +='<td>'+payment_type_excess(excess_data[i].payment_type)+'</td>';
          h_output += `<td>${excess_data[i].student_name}</td>
          <td>${excess_data[i].cs_name}</td>
          <td>${excess_data[i].total_amount}</td>
          <td>${excess_data[i].total_used_amount}</td>
          <td>${excess_data[i].refund_amount}</td>
          <td>${bal}</td>
          <td>${excess_data[i].remarks}</td>
          <td>${excess_data[i].created_staff_name}</td>
        </tr>`;
    }
    h_output += '</tbody>';
    h_output += '</table>';
    
    return h_output;
  }

  function payment_type_excess(payment_type) {
    switch (payment_type) {
      case '1':
        $pValue = 'DD';
        break;  
      case '2':
        $pValue = 'Credit Card';
        break;
      case '3':
        $pValue = 'Debit Card';
        break;
      case '4':
        $pValue = 'Cheque '
        break;
      case '5':
        $pValue = 'Wallet Payment';
        break;
      case '6':
        $pValue = 'Challan '
        break;
      case '7':
        $pValue = 'Card (POS)';
        break;
      case '8':
        $pValue = 'Net Banking '
        break;
      case '9':
        $pValue = 'Cash';
        break;
      case '10':
        $pValue = 'Online Payment';
        break;
     case '11':
        $pValue = 'UPI';
        break;
      case '12':
        $pValue = 'Loan';
        break;
      case '13':
        $pValue = 'Loan';
        break;
      case '20':
        $pValue = 'St.Marys A/C';
        break;
      case '21':
        $pValue = 'Mary Madha A/C';
        break;
      case '999':
        $pValue = 'Excess Amount';
        break;
      case '30':
        $pValue = 'Transfer from Indus';
        break;
      case '31':
        $pValue = 'Bank Deposit';
        break;
      case '888':
        $pValue = 'Adjust Amount';
        break;
      default:
        $pValue = '';
        break;
    }
    return $pValue;
  }

  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>

<style type="text/css">
  .dt-button {
    border: none !important;
    background: none !important;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .dt-button .btn {
    line-height: 20px;
  }

  .dt-buttons {
    text-align: right;
    float: right;
    margin-bottom: 10px;
  }

  .dataTables_wrapper .dt-buttons {
    float: right;
  }

  .dataTables_filter input {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;  
    font-size: 14px;
    color: #495057;
    outline: none;
  }

  .dataTables_filter {
    border-bottom: 0px !important;
    margin-top: 3px;
  }

  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
  }

  #fee_excess_data {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
}
#fee_excess_data thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
}
#fee_excess_data th,
#fee_excess_data td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
}
#fee_excess_data tbody tr:nth-child(even) {
    background-color: #f9fafb;
}
#fee_excess_data tbody tr:hover {
    background-color: #f1f5f9;
}
#fee_excess_data tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
}
  #fee_excess_scroll {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: auto;
    width: 100%;
    /* Custom thick scrollbar for all browsers */
    scrollbar-width: thick;
    scrollbar-color: #888 #f1f1f1;
  }
  #fee_excess_scroll::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }
  #fee_excess_scroll::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 8px;
  }
  #fee_excess_scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
  }
  #fee_excess_data {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    table-layout: auto;
    margin: 0;
  }

  #fee_excess_data thead th{
        position: sticky !important;
        top: 0;
        background-color: #f1f5f9;
        z-index: 2;
    }
    #table-toolbar {
        position: static !important;
        background: none !important;
        z-index: 1;
    }
    /* For Firefox */
    .fee_excess_data {
        scrollbar-width: thick;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }
</style>