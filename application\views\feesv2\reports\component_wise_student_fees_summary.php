<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_dashboard'); ?>">Fee Dashboard</a></li>
    <li>Fee Detail Report (Component Wise)</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Fee Detail Report (Component Wise)
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form id="component_wise_report">
                <div class="col-md-12">
                    <div class="row" style="margin: 0px">


                        <div class="col-md-3 form-group" id="blueprintSelect">
                            <p>Select Fee Type <font color="red">*</font>
                            </p>
                            <select class="form-control changeFeeType" id="fee_type" name="fee_type">
                                <option value="">Select Blueprint</option>
                                <?php foreach ($fee_blueprints as $key => $val) { ?>
                                    <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                                <?php } ?>
                                <option value="application">Applications</option>
                            </select>
                        </div>
                        <div class="col-md-3 form-group" id="installmentType" style="display: none;">
                            <p>Select Installments Type <font color="red">*</font>
                            </p>
                            <select class="form-control" name="installment_type" id="installment_type">

                            </select>
                        </div>

                        <div class="col-md-3 form-group" id="installment" style="display: none">
                            <p>Select Installments</p>
                            <select class="form-control onchange_installments" name="installment" id="installmentId">

                            </select>
                        </div>

                        <div class="col-md-3 form-group" style="display: none" id="component">
                            <p>Select Component <font color="red">*</font>
                            </p>
                            <select id="component_name" name="component_name[]" multiple title="Select Component" class="form-control select">
                                <option value="">Please Select Blueprint First</option>
                            </select>
                        </div>

                        <div class="col-md-3 form-group">
                            <p>Class</p>
                            <?php
                            $array = array();
                            // $array[0] = 'Select Classes';
                            foreach ($classes as $key => $class) {
                                $array[$class->classId] = $class->className;
                            }
                            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
                            ?>
                        </div>

                        <div class="col-md-3 form-group">
                            <p>Class/Section</p>
                            <?php
                            $array = array();
                            $array[0] = 'Select Section';
                            foreach ($classSectionList as $key => $cl) {
                                $array[$cl->id] = $cl->class_name . $cl->section_name;
                            }
                            echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
                            ?>
                        </div>

                        <div class="col-md-3 form-group">
                            <p for="sectionId">Admission Status</p>
                            <select id="admission_status" name="admission_status" class="form-control input-md">
                                <option value=""><?php echo "All" ?></option>
                                <?php foreach ($admission_status as $value => $type) { ?>
                                    <option value="<?php echo $value ?>"><?php echo $type ?></option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-3 form-group">
                            <p>Admission Type</p>
                            <?php
                            $array = array();
                            $array[0] = 'Select Admission Type';
                            foreach ($admission_type as $key => $admission) {
                                $array[$key] = ucfirst($admission);
                            }
                            echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control'");
                            ?>
                        </div>

                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-12 text-center">
                        <input type="button" onclick="get_component_wise_report()" name="search" id="search" class="btn btn-primary" value="Get Report">
                    </div>
                </div>
            </form>

            <div class="text-center mt-2">
                <div style="display: none;" class="progress" id="progress">
                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div>
                </div>
            </div>



            <div class="col-12 text-center loading-icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;margin: 20px 0;"></i>
            </div>

            <div id="printArea">
                <div id="print_visible" style="display: none; text-align: center;">
                    <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
                        <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
                    </h3>
                    <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
                        Fees Summary Report
                    </h4>
                </div>
                <ul class="panel-controls" id="exportButtons" style="display: none;">
                  <div class="search-box">
                    <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
                  </div>
                  <button class="btn btn-info" style="margin-left:3px; margin-bottom: 10px; border-radius: 8px !important;" onclick="printProfile()">
                    <span class="fa fa-print" aria-hidden="true"></span> Print
                  </button>
                  <button class="btn btn-info" style="margin-left:3px; margin-bottom: 10px; border-radius: 8px !important;" onclick="exportToExcel_daily()">
                    <span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel
                  </button>
                </ul>

                <div class="summary_data_report mt-2">

                </div>

                <div class="componenent_wise_report" style="overflow-x: auto;">

                </div>
            </div>
        </div>

    </div>
</div>
</div>

<script type="text/javascript">
    $('.changeFeeType').on('change', function() {
        var bpType = $('.changeFeeType').val();
        $('#installment_type').val('');
        $('#component').hide();
        $('#installmentType').hide();
        if (bpType != 'all') {
            changeInstallment_type(bpType);
            $('#installmentType').show();
            $('#component').show();
        } else {
            $('#installmentId').val('');
            $('#installmentType').hide();
            $('#component').hide();
        }
    });

    let componentNames = {

    }

    function changeInstallment_type(bpType) {
        $.ajax({
            url: '<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
            type: 'post',
            data: {
                'bpType': bpType
            },
            success: function(data) {
                var data = JSON.parse(data);
                var output = '<option value="">Select Installments Type</option>';
                for (var i = 0; i < data.length; i++) {
                    output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                }
                $('#installment_type').html(output);
            }
        });
    }

    function changeInstallment() {
        var installment_type = $('#installment_type').val();
        $.ajax({
            url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
            type: 'post',
            data: {
                'installment_type': installment_type
            },
            success: function(data) {
                var data = JSON.parse(data);
                var output = '<option value="">Select Installments</option>';
                for (var i = 0; i < data.length; i++) {
                    output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                }
                $('#installmentId').html(output);
                $('#installment').show();
                $('#installmentId').selectpicker();
            }
        });
    }
    $('#installment_type').on('change', function() {
        changeInstallment();
    });

    let msg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;">
      No Data to show
    </div>
  `;

    $("#fee_type").change(() => {
        let fee_type = Number($('#fee_type').val());
        if (fee_type) {
            $.ajax({
                url: '<?php echo site_url('feesv2/reports_v2/get_blueprint_components'); ?>',
                type: "POST",
                data: {
                    "fb_id": fee_type
                },
                success: function(data) {
                    data = $.parseJSON(data)
                    if (data.length) {
                        let options = ``
                        data.forEach((data, i) => {
                            options += `<option value="${data.id}">${data.name}</option>`
                        })
                        $("#component_name").html(options);
                        $('#component_name').selectpicker('refresh');
                    } else {
                        $("#component_name").html(`<option value="">Component(s) Not Available</option>`);
                    }
                }
            })
        }
    })
    var cohortStudentIds = [];
    var completed = 0;
    var total_students = 0;
    // var totalFeeAssingedStudentCount = 0;
    // var aluminiCount = 0;
    // var summaryData = [];
    function get_component_wise_report() {
        $('#table-search').val('');
        $(".summary_data_report").hide();
        completed = 0;
        total_students = 0;

        // Disable the button and show loading state
        $('#search').prop('disabled', true).val('Please wait...');

        let fee_type = $('#fee_type').val();
        let installment_type = $('#installment_type').val();
        let component_id = $("#component_name").val();

        let validationErrors = [];

        if (!fee_type || fee_type === '') {
            validationErrors.push('Select Fee Type');
        }

        if ($('#installmentType').is(':visible') && (!installment_type || installment_type === '')) {
            validationErrors.push('Select Installments Type');
        }

        if ($('#component').is(':visible') && (!component_id || component_id.length === 0)) {
            validationErrors.push('Select Component');
        }

        if (validationErrors.length > 0) {
            let errorMessage = '';
            if (validationErrors.length === 1) {
                errorMessage = 'Please ' + validationErrors[0];
            } else {
                errorMessage = 'Please select the following fields:\n• ' + validationErrors.join('\n• ');
            }

            new PNotify({
                title: 'Required Fields Missing',
                text: errorMessage,
                type: 'error',
                styling: 'bootstrap3',
                delay: 5000,
                addclass: 'custom-validation-notification',
                width: '350px',
                icon: 'fa fa-exclamation-triangle',
                hide: true,
                closer: true,
                sticker: false,
                opacity: 0.95,
                shadow: true,
                cornerclass: 'ui-pnotify-sharp'
            });

            // Re-enable button on validation error
            $('#search').prop('disabled', false).val('Get Report');
            return false;
        }

        $(".loading-icon").show();
        $(".componenent_wise_report").hide()

        let clsId = $('#classId').val();
        let classSectionId = $('#classSectionId').val();
        let admission_type = $('#admission_type').val();
        let admission_status = $('#admission_status').val();

            $.ajax({
                url: '<?php echo site_url('feesv2/reports_v2/component_wise_getStudentsForSummary_v2'); ?>',
                type: "POST",
                data: {
                    "fee_type": fee_type,
                    "clsId": clsId,
                    "classSectionId": classSectionId,
                    "component_id": component_id,
                    "admission_type": admission_type,
                    "admission_status": admission_status,
                },
                success: function(data) {

                    var cohort_student_ids = JSON.parse(data);
                    if (cohort_student_ids.length == 0) {
                        $('.table-responsive').html('<h3>Data not found</h3>');
                        $(".loading-icon").hide();
                        // Re-enable button when no data found
                        $('#search').prop('disabled', false).val('Get Report');
                        $("#progress").hide();
                        $("#exportButtons").hide();
                        return;
                    }
                    $(".componenent_wise_report").show();
                    cohortStudentIds = cohort_student_ids;
                    total_students = parseInt(150 * (cohortStudentIds.length - 2)) + parseInt(cohortStudentIds[cohortStudentIds.length - 1]).length;
                    var progress = document.getElementById('progress-ind');
                    progress.style.width = (completed / total_students) * 100 + '%';
                    $("#progress").show();
                    callReportGetter(0);
                },
                error: function(err) {
                    // Re-enable button on AJAX error
                    $('#search').prop('disabled', false).val('Get Report');
                    $("#progress").hide();
                    $(".loading-icon").hide();
                    alert('Network may be slow. Please try again.');
                }
            })
    }

    function callReportGetter(index) {

        if (index < cohortStudentIds.length) {
            getReport(index);
        } else {
            $("#progress").hide();
            $(".loading-icon").hide();
            $("#exportButtons").show();

            // Re-enable button when all processing is complete
            $('#search').prop('disabled', false).val('Get Report');

            generateSummaryTable(); // Generate summary table only at the end
            initializeSearch();
        }
    }



    function getReport(index) {
        var cohortstudentids = cohortStudentIds[index];
        let component_id = $("#component_name").val();
        let fee_type = $('#fee_type').val();
        let installment_type = $('#installment_type').val();
        let installmentId = $('#installmentId').val();
        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/get_component_wise_getStudentsForSummary_v2'); ?>',
            type: "POST",
            data: {
                "cohortstudentids": cohortstudentids,
                "component_id": component_id,
                "fee_type": fee_type,
                "installment_type": installment_type,
                "installmentId": installmentId
            },
            success: function(data) {
                var resdata = $.parseJSON(data);
                var componentheader = resdata.component_header;
                var student_data = resdata.student_data;
                var fee_data = resdata.feeArry;
                if (index == 0) {
                    constructFeeHeader(componentheader);
                }

                completed += student_data.length;
                // completed += 2;
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed / total_students) * 100 + '%';
                construct_fee_component_data(componentheader, student_data, fee_data, index);
                $(".loading-icon").hide();
            }
        })
    }

    function construct_fee_component_data(componentheader, student_data, fee_data, index) {
        var m = 0;
        var srNo = Number(index) * 150;
        var output = '<tbody';



        for (var i in student_data) {
            output += '<tr>';
            output += `<td> ${m+1+srNo} </td>`
            output += '<td>' + student_data[i].first_name + '</td>'
            output += '<td>' + student_data[i].class + '</td>'
            output += '<td>' + student_data[i].admission_no + '</td>'
             output += '<td>' + student_data[i].enrollment_number + '</td>'
            output += '<td>' + student_data[i].fee_blueprint_name + '</td>';

            var studentFeeData = fee_data[student_data[i].student_id];

            // Create a lookup object for student's fee data by component ID
            var studentFeeByComponent = {};
            if (studentFeeData) {
                for (var fd = 0; fd < studentFeeData.length; fd++) {
                    if (studentFeeData[fd] && studentFeeData[fd].comp_id) {
                        studentFeeByComponent[studentFeeData[fd].comp_id] = studentFeeData[fd];
                    }
                }
            }

            // Loop through each component in the header to ensure consistent ordering
            for (var comp_id in componentheader) {
                var componentData = studentFeeByComponent[comp_id];

                let component_amount = componentData?.component_amount || 0;
                let component_amount_paid = componentData?.component_amount_paid || 0;
                let concession_amount = componentData?.concession_amount || 0;
                let component_balance = component_amount - component_amount_paid - concession_amount;

                // Update summary totals only if we have actual data
                if (componentData && comp_id) {
                    // Initialize component totals if they don't exist
                    if (!componentNames[`data_${comp_id}`][`component_amount_${comp_id}`]) {
                        componentNames[`data_${comp_id}`][`component_amount_${comp_id}`] = 0;
                    }
                    if (!componentNames[`data_${comp_id}`][`component_amount_paid_${comp_id}`]) {
                        componentNames[`data_${comp_id}`][`component_amount_paid_${comp_id}`] = 0;
                    }
                    if (!componentNames[`data_${comp_id}`][`concession_amount_${comp_id}`]) {
                        componentNames[`data_${comp_id}`][`concession_amount_${comp_id}`] = 0;
                    }

                    componentNames[`data_${comp_id}`][`component_amount_${comp_id}`] += Number(component_amount);
                    componentNames[`data_${comp_id}`][`component_amount_paid_${comp_id}`] += Number(component_amount_paid);
                    componentNames[`data_${comp_id}`][`concession_amount_${comp_id}`] += Number(concession_amount);

                    componentNames.total.total_component_amount += Number(component_amount);
                    componentNames.total.total_component_amount_paid += Number(component_amount_paid);
                    componentNames.total.total_concession_amount += Number(concession_amount);
                }

                // Add cells for this component
                output += `<td>${component_amount}</td>`;
                output += `<td>${component_amount_paid}</td>`;
                output += `<td>${concession_amount}</td>`;
                output += `<td>${component_balance}</td>`;
            }

            output += '</tr>';
            m++;
        }
        output += '</tbody>';
        $('#fee_component_data').append(output);
        index++;
        callReportGetter(index);
    }

    function generateSummaryTable() {
        // Check if componentNames is properly initialized
        if (!componentNames || !componentNames.total) {
            return;
        }

        let table = `<table id="print_summary" class="table table-striped table-bordered">
                    <tr>
                        <th>Component Name</th>
                        <th>Component Amount</th>
                        <th>Component Amount Paid</th>
                        <th>Concession Amount</th>
                    </tr>`

        // Generate rows for each component
        for (let val of Object.values(componentNames)) {
            if (val.name && val.comp_id) {
                const component_amount = in_currency(`${val[`component_amount_${val.comp_id}`] || 0}`)
                const component_amount_paid = in_currency(`${val[`component_amount_paid_${val.comp_id}`] || 0}`)
                const concession_amount = in_currency(`${val[`concession_amount_${val.comp_id}`] || 0}`)
                table += `
                <tr>
                <td>${val.name}</td>
                <td>${component_amount}</td>
                <td>${component_amount_paid}</td>
                <td>${concession_amount}</td>
                </tr>
                `
            }
        }

        // Add grand total row
        if (componentNames.total) {
            let {
                total: {
                    total_component_amount = 0,
                    total_component_amount_paid = 0,
                    total_concession_amount = 0
                }
            } = componentNames

            table += `
                    <tr style="font-weight: 600;">
                    <td>Grand Total</td>
                    <td>${in_currency(total_component_amount)}</td>
                    <td>${in_currency(total_component_amount_paid)}</td>
                    <td>${in_currency(total_concession_amount)}</td>
                    </tr>
                    `
        }

        table += `</table>`

        $(".summary_data_report").html(table)
    }

    function in_currency(amount) {
        var formatter = new Intl.NumberFormat('en-IN', {
            // style: 'currency',
            currency: 'INR',
        });
        return formatter.format(amount);
    }

    function constructFeeHeader(componentData) {
        $(".summary_data_report").show();

        // Clear any existing data
        $('.componenent_wise_report').empty();

        var h_output = '<table id="fee_component_data" class="table table-bordered">';
        h_output += '<thead>';
        h_output += '<tr>';
        h_output += '<th rowspan="2" >#</th>';
        h_output += '<th rowspan="2">Student Name</th>';
        h_output += '<th rowspan="2">Class</th>';
        h_output += '<th rowspan="2">Admission No</th>';
        h_output += '<th rowspan="2">Enrollment Number</th>';
        h_output += '<th rowspan="2">Fee Blueprint Name</th>';

        // Initialize component tracking object
        componentNames = {}
        componentNames.total = {};
        componentNames.total.total_component_amount = 0
        componentNames.total.total_component_amount_paid = 0
        componentNames.total.total_concession_amount = 0

        // Ensure we have component data before proceeding
        if (componentData && Object.keys(componentData).length > 0) {
            for (var k in componentData) {
                componentNames[`data_${k}`] = {};
                componentNames[`data_${k}`].name = componentData[k];
                componentNames[`data_${k}`].comp_id = k;

                h_output += '<th colspan="4">' + componentData[k] + '</th>';
            }
            h_output += '</tr>';
            h_output += '<tr>';
            for (var k in componentData) {
                h_output += '<th>Total Amount</th>';
                h_output += '<th>Amount Paid</th>';
                h_output += '<th>Concession</th>';
                h_output += '<th>Balance</th>';
            }
        } else {
            h_output += '<th colspan="4">No Components Selected</th>';
            h_output += '</tr>';
            h_output += '<tr>';
            h_output += '<th colspan="4">Please select components to view data</th>';
        }
        h_output += '</tr>';

        h_output += '</thead>';
        h_output += '</table>';
        $('.componenent_wise_report').html(h_output);
    }

    function printProfile() {
        const printWindow = window.open('', '_blank');
        let printHeader = document.getElementById('print_visible').outerHTML;
        printHeader = printHeader.replace('display: none;', ''); // Remove the inline style

        const summaryTable = document.getElementById('print_summary').outerHTML;
        const componentTable = document.getElementById('fee_component_data').outerHTML;


        printWindow.document.write(`
            <html>
            <head>
                <title>Fee Component Report</title>
                <style>
                    body {
                        font-family: 'Poppins', sans-serif;
                        padding: 20px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        font-size: 12px;
                    }
                    h3 { margin: 15px 0; }
                    @media print {
                        table { page-break-inside: auto }
                        tr { page-break-inside: avoid }
                    }
                </style>
            </head>
            <body>
                ${printHeader}
                <h3>Fee Summary</h3>
                ${summaryTable}
                <h3>Component Details</h3>
                ${componentTable}
                <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    function exportToExcel_daily() {
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };

        const summaryTable = document.getElementById('print_summary').outerHTML;
        const componentTable = document.getElementById('fee_component_data').outerHTML;

        htmls = '<h3>Fee Summary</h3>' + summaryTable + '<h3>Component Details</h3>' + componentTable;

        var ctx = {
            worksheet: 'Fee Report',
            table: htmls
        }

        var link = document.createElement("a");
        link.download = "fee_report_component_wise.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();
    }

    function initializeSearch() {
        // Reset any previous search
        $('#table-search').val('');

        // Attach the search event handler
        $('#table-search').off('keyup').on('keyup', function() {
            const searchText = $(this).val().toLowerCase();

            // Search in the component-wise table
            $('#fee_component_data tbody tr').each(function() {
                let rowVisible = false;

                // Search in all cells of the row
                $(this).find('td').each(function() {
                    if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
                        rowVisible = true;
                        return false; // Break the loop if found
                    }
                });

                // Show/hide the row based on search result
                $(this).toggle(rowVisible);
            });
        });
    }

    // Document ready function for search functionality
    $(document).ready(function() {
        // Initialize search functionality when document is ready
        $(document).on('keyup', '#table-search', function() {
            const searchText = $(this).val().toLowerCase();

            // Search in the component-wise table
            $('#fee_component_data tbody tr').each(function() {
                let rowVisible = false;

                // Search in all cells of the row
                $(this).find('td').each(function() {
                    if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
                        rowVisible = true;
                        return false; // Break the loop if found
                    }
                });

                // Show/hide the row based on search result
                $(this).toggle(rowVisible);
            });
        });
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
</script>

<style type="text/css">
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');
    table {
        font-family: 'Poppins', sans-serif !important;
    }

    .search-box {
      display: inline-block;
      position: relative;
      margin-right: 2px;
      vertical-align: middle;
    }

    .input-search {
      line-height: 1.5;
      padding: 5px 10px;
      display: inline;
      width: 177px;
      height: 27px;
      background-color: #f2f2f2 !important;
      border: 1px solid #ccc !important;
      border-radius: 4px !important;
      margin-right: 0 !important;
      font-size: 14px;
      color: #495057;
      outline: none;
      margin-bottom: 10px;
    }

    .input-search::placeholder {
      color: rgba(73, 80, 87, 0.5);
      font-size: 14px;
      font-weight: 300;
    }

    .panel-controls {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    #fee_component_data, #print_summary {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    }

    #fee_component_data thead th, #print_summary thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
    }

    #fee_component_data th,
    #fee_component_data td,
    #print_summary th,
    #print_summary td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
    }

    #fee_component_data tbody tr:nth-child(even) {
    background-color: #f9fafb;
    }

    #fee_component_data tbody tr:hover,
    #print_summary tbody tr:hover {
    background-color: #f1f5f9;
    }

    #fee_component_data tfoot tr,
    #print_summary tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
    }
    .componenent_wise_report {
        margin: 12px 0;
        /* overflow: auto; */
    }

    .parsley-error {
        color: #B94A48 !important;
        background-color: #F2DEDE !important;
        border: 1px solid #EED3D7 !important;
    }

    /* Custom validation notification styling */
    .custom-validation-notification {
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        z-index: 9999 !important;
        font-family: 'Poppins', sans-serif !important;
        border-radius: 8px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        border: none !important;
        animation: slideInRight 0.3s ease-out !important;
    }

    .custom-validation-notification .ui-pnotify-title {
        font-weight: 600 !important;
        font-size: 14px !important;
        color: #dc3545 !important;
        margin-bottom: 8px !important;
    }

    .custom-validation-notification .ui-pnotify-text {
        font-size: 13px !important;
        line-height: 1.5 !important;
        color: #721c24 !important;
        white-space: pre-line !important;
    }

    .custom-validation-notification.ui-pnotify-error {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
        border-left: 4px solid #dc3545 !important;
    }

    .custom-validation-notification .ui-pnotify-icon {
        color: #dc3545 !important;
        font-size: 16px !important;
        margin-right: 10px !important;
    }

    .custom-validation-notification .ui-pnotify-closer {
        color: #dc3545 !important;
        font-weight: bold !important;
        opacity: 0.7 !important;
        transition: opacity 0.2s ease !important;
    }

    .custom-validation-notification .ui-pnotify-closer:hover {
        opacity: 1 !important;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

</style>