


<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/sales_controller_v2');?>">Inventory Sales</a></li>
    <li class="active">Student Wise Sales Report</li>
</ul>

<div class="col-md-12">
    <div class="panel cd_border" style="border: 1px solid #e5e5e5;">
        <div class="card-header panel_heading_new_style_staff_border" style="margin: 5px 0 0 10px;">
            <div class="row" style="margin: 0px">
                <h3>
                    <a style="" class="back_anchor" href="<?php echo site_url('procurement/sales_controller_v2') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Student Wise Sales Report
                    <button style="margin-left: 5px;" onclick="print_graph_table()" class="btn btn-info pull-right"><span class="fa fa-print"></span> Table</button>
                        <button onclick="export_table()" class="btn btn-info pull-right"><span class="glyphicon glyphicon-export"></span> Table</button>
                </h3>
            </div>
        </div>
        <div class="panel-body">
<!-- Filter -->
            <div class="">
                <div class="col-md-3">
                    <div class="input-group">
                        <input class="form-control" placeholder="Tap RFID Card" value="" autofocus type="text" id="input_rfid" name="input_rfid">   
                        <span id="rfid_search_btn" class="input-group-addon" style="cursor: pointer;" onclick="get_details_from_rfid()">Get</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <input class="form-control" value=""  placeholder="Admission No" type="text" id="admission_no" name="admission_no">   
                        <span class="input-group-addon" style="cursor: pointer;" onclick="student_admission_search()">Get</span>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="input-group">
                        <input onkeypress="myFunction()" class="form-control" autocomplete="off" placeholder="Search by Name" type="text" id="stdName1" name="student_name">  
                        <input onkeypress="myFunction()" style="display: none;" class="form-control" autocomplete="off" placeholder="Search by Name" type="text" id="stdName12" name="student_name12">  
                        <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-pencil"></span></span> 
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="input-group">
                        <select id="class_section_id"  name="classes" class="form-control input-md">
                            <option value="">Select Class - Section</option>
                            <?php foreach ($classList as $key => $cls) { ?>
                                <option value="<?php echo $cls->class_section_id; ?>"><?php echo $cls->class_name. " - " .$cls->section_name; ?></option>
                            <?php } ?>
                        </select>
                        <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                <br><br><br>
                <div class="col-md-3">
                    <div class="input-group">
                        <select id="student_id"  name="student" class="form-control input-md">
                            <option value="">Select Student</option>
                        </select>
                        <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>

                

                <div class="col-md-3">
                    <div class="input-group">
                        <select id="selected_category"  name="selected_category" class="form-control input-md" onchange="onchange_category()">
                            <option value="">Select Category</option>
                            <?php if(!empty($category)) { foreach($category as $k => $v) { ?>
                                <option value="<?php echo $v->category_id; ?>"><?php echo ucwords($v->category_name); ?></option>
                            <?php }} ?>
                        </select>
                        <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>

                <div class="col-md-3">
                    <!-- <div class="input-group"> -->
                        <select multiple id="selected_subcategories"  name="selected_subcategories[]" class="form-control" title="All">
                            <option value="">Select Sub - categories</option>
                        </select>
                        <!-- <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-caret-down"></span></span> -->
                    <!-- </div> -->
                </div>


                <div class="col-md-3">
                    <div class="input-group">
                        <select id="sales_year_id"  name="sales_year_id" class="form-control selects" title="">
                            <option value="">All Sales Year</option>
                            <?php
                                if(!empty($salesYear)) {
                                    foreach($salesYear as $key => $val) {
                                        $selected= '';
                                        if($val->is_active == 1) {
                                            $selected= 'selected';
                                        }
                                        echo "<option $selected value='$val->id'>$val->year_name</option>";
                                    }
                                }
                            ?>
                        </select>
                        <span class="input-group-addon" style="cursor: pointer;"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>

                
                
      
            </div>

            <div class="col-md-12">
                <center class="">
                    <button style="display: none; width: 200px; margin-top: 10px;" id="get_report_button" class="btn btn-info" onclick="get_reports()">Get Report</button>
                </center>
            </div>

                
<!--  -->
<br>
            <div id="details_divs">
                <div class="col-md-12" id="student_details_div" style="display: flex; padding: 10px 0 0 12px; margin: 0 0 -10px 3px;">    </div>
                <div class="col-md-7" id="summary_div">    </div> <br>
                <div class="col-md-12" id="sales_table">    </div>
                <div class="col-md-12" id="sales_return_table">    </div>
            </div>

        </div>
    </div>
</div>
     

<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.min.js" integrity="sha512-Bkf3qaV86NxX+7MyZnLPWNt0ZI7/OloMlRo8z8KPIEUXssbVwB1E0bWVeCvYHjnSPwh4uuqDryUnRdcUw6FoTg==" crossorigin="anonymous"></script> -->

<script>

    function onchange_category() {
        var selected_category= $("#selected_category").val();
        if(selected_category) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/getProductNames'); ?>',
                type: "post",
                data: {'category_id':selected_category},
                success(data) {
                    var p_data = JSON.parse(data);
                    var html= '';
                    if(p_data.length) {
                        for(var v of p_data) {
                            html += `<option value="${v.id}">${v.product_name}</option>`;
                        }
                        $("#selected_subcategories").html(html);
                    }
                    
                }
            });
        }
    }
    // search by name start
    var aMerge = [];
    var cls_sec_ids_arr= [];
    $(document).ready(function(){
        var s_names = JSON.parse('<?php echo json_encode($studentNames); ?>');
        for(var i=0; i < s_names.length; i++){
        aMerge.push(s_names[i].s_name.trim() + ' ('+s_names[i].class_name + s_names[i].section_name+') '+' ('+s_names[i].id_number+')' +s_names[i].promotion_status);
        cls_sec_ids_arr.push(s_names[i].class_section_id);
        }
        
    });

    function myFunction() {
        autocomplete(document.getElementById("stdName12"), aMerge, document.getElementById("stdName1"));
    }

    autocomplete(document.getElementById("stdName1"), aMerge, document.getElementById("stdName12"));
    var stdName = '';

    function autocomplete(inp, arr, inp12) {
       
        $("#get_report_button").hide();
        $("#sales_table").html(``);
        $("#sales_return_table").html(``);
        var currentFocus;
        inp.addEventListener("input", function(e) {
        var a, b, i, val = this.value;
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        this.parentNode.appendChild(a);
        
        for (i = 0; i < arr.length; i++) {
            
          if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
            b = document.createElement("DIV");
            
            b.style.cursor = 'pointer';
            var stdNameSplit = arr[i].split('\(');
            var original_name = arr[i].split('(');

            var merge = stdNameSplit[0];
            var split1 = '('+stdNameSplit[1];
            var card_number = stdNameSplit[2];

            var cardNumberSplit = card_number.split(')');


            let promotion_status_temp= cardNumberSplit[1];
            if(promotion_status_temp == '4' || promotion_status_temp == '5') {
              b.style.color = 'red';
            } else {
              b.style.color = 'black';
            }


            var prom_sts= cardNumberSplit[1];
// console.log(cardNumberSplit)

            b.innerHTML = "<strong>" + merge.substr(0, val.length) + "</strong>";
            b.innerHTML += merge.substr(val.length) + ' ' + split1;
            b.innerHTML += "<input data-promotion_status='"+prom_sts+"' type='hidden' value='" +cls_sec_ids_arr[i] + " " +merge + " " + split1 + "_" +cardNumberSplit+"'> <span style='opacity: 0;'>"+original_name[0]+ ' ('+original_name[1]+"</span>";
            b.addEventListener("click", function(e) {
                inp.value = this.getElementsByTagName("input")[0].value;
                inp12.value = this.getElementsByTagName("span")[0].innerHTML;
                $("#stdName12").show();
                $("#stdName1").hide();
                // inp.value.style.display= 'none';
                // inp12.value.style.display= 'auto';
                sepstdName = this.getElementsByTagName("input")[0].value;
                var nameSplit = sepstdName.split('_');
                var c_sec = nameSplit[0];
                var cls_sec_d = c_sec.split(' ');
                var cNo = nameSplit[1];
                var stdId = cNo.split(',')
                var output='';
                
                

                // Get the input element
                let inputElement = this.getElementsByTagName("input")[0];
                // Fetch the value of data-promotion_status from the input element
                let prom_sts_new = inputElement.getAttribute("data-promotion_status");


                output+='<option selected data-promotion_status="'+prom_sts_new+'" value="'+stdId[0]+'">'+this.getElementsByTagName("span")[0].innerHTML+' </option>';
                $("#student_id").html(output);

                // console.log(prom_sts)

                $(`#class_section_id option[value="${cls_sec_d[0]}"]`).prop('selected', true);

                $("#get_report_button").show();
                $("#get_report_button").click();
                // Reset the values
                $("#stdName1, #stdName12").val('');

            });
            a.appendChild(b);
          }
        }
        });

        inp.addEventListener("keydown", function(e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) {
                
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { //up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) {
                e.preventDefault();
                if (currentFocus > -1) {
                if (x) x[currentFocus].click();
                }
            }
        });
        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }
        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
            x[i].classList.remove("autocomplete-active");
            }
        }
        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
            if (elmnt != x[i] && elmnt != inp) {
            x[i].parentNode.removeChild(x[i]);
            }
        }
        }
        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });

        
    }
// search by name end

// Onchange class-section fn
    $("#class_section_id").change(event, function() {
        $("#get_report_button").hide();
        $("#sales_table").html(``);
        $("#sales_return_table").html(``);
        // console.log(event);
        let class_section_id= $("#class_section_id").val();
        if(class_section_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/sales_controller_v2/get_all_the_students_class_section_wise'); ?>',
                type: "post",
                data: {class_section_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    var students= `<option value="">Select Student</option>`;
                    for(var v of p_data) {
                        if(v.promotion_status == '4' || v.promotion_status == '5') {
                        coloram= 'red';
                        } else {
                        coloram= 'black';
                        }
                        students += `
                                    <option style="color: ${coloram};" data-promotion_status="${v.promotion_status}" value="${v.student_admission_id}">${v.std_name}</option>
                                    `;
                    }
                    $("#student_id").html(students).prop('disabled', false).prop('readonly', false);
                    // $("#get_report_button").show();
                    // console.log(students);
                    
                }
            });
        }
    });

    // start rfid
    var timeout;
    $('#input_rfid').on('input', function() {
        $("#get_report_button").hide();
        $("#sales_table").html(``);
        $("#sales_return_table").html(``);
        var rfidValue = $('#input_rfid').val();
        clearTimeout(timeout);
        if (rfidValue !== '') {
            // $("#get_details_button").prop('disabled', false);
            timeout = setTimeout(function() {
                check_if_rfid_mapped(rfidValue);
            }, 500);
        }
        $('#input_rfid').on("keyup", function(event) {
            if (event.key === "Escape") {
                $('#input_rfid').val("");
            }
        });
    });

    async function check_if_rfid_mapped(rfidValue) {
        await $("#get_report_button").hide();
        await $("#sales_table").html(`<img src="<?php  echo base_url('assets/img/ajax-loader.gif');?>">`);
        await $("#sales_return_table").html(``);
        await $("#rfid_search_btn").css('pointer-events', 'none').html('Wait..');
        await $.ajax({
            url: '<?php echo site_url('procurement/sales_controller_v2/check_if_rfid_mapped'); ?>',
            type: "post",
            data: {rfidValue},
            async: true,
            success(data) {
                var p_data = JSON.parse(data);
                console.log('nov 28', p_data);
                if(p_data.status == '-1') {
                    $(function(){
                        new PNotify({
                            title:'Error',
                            text: 'Data not found.',
                            type:'error',
                            delay: 300
                        });
                    });
                    $("#sales_table").html(``);
                    
                    $("#student_id").html('');
                    $(`#class_section_id option[value=""]`).prop('selected', true);
                } else {
                    $(function(){
                        new PNotify({
                            title:'Success',
                            text: 'RFID found',
                            type:'success',
                            delay: 300
                        });
                    });

                    var output='';
                    output+='<option data-promotion_status="'+p_data.promotion_status+'" value="'+p_data.id+'">'+p_data.std_name+' </option>';
                    $("#student_id").html(output);
                    $(`#class_section_id option[value="${p_data.class_section_id}"]`).prop('selected', true);
                    $("#get_report_button").show();
                    $("#get_report_button").click();
                }
                
            }
        });
        await $("#rfid_search_btn").css('pointer-events', 'auto').html('Get');
    }

    async function get_details_from_rfid() {
        $("#get_report_button").hide();
        $("#sales_table").html(``);
        $("#sales_return_table").html(``);
        var rfidValue = $('#input_rfid').val();
        if (rfidValue !== '') {
            // await $("#sales_table").html(`<img src="<?php // echo base_url('assets/img/ajax-loader.gif');?>">`);
            await check_if_rfid_mapped(rfidValue);
        }
    }
    // end rfid

    // search by adm_no
    async function student_admission_search() {
        await $("#get_report_button").hide();
        // await $("#sales_table").html(``);
        await $("#sales_table").html(`<img src="<?php  echo base_url('assets/img/ajax-loader.gif');?>">`);
        await $("#sales_return_table").html(``);
        var admission_no = $('#admission_no').val();
        await $.ajax({
            url:'<?php echo site_url('procurement/sales_controller_v2/get_student_id_by_adm_no') ?>',
            type:'post',
            data : {'admission_no':admission_no},
            success : await function(data){     
                var std_data=$.parseJSON(data);
                console.log(std_data);
                if (std_data.status == '1') {
                var output='';
                output+='<option value="'+std_data.id+'">'+std_data.std_name+' </option>';
                $("#student_id").html(output);
                $(`#class_section_id option[value="${std_data.class_section_id}"]`).prop('selected', true);
                $("#get_report_button").show();

                $(function(){
                    new PNotify({
                    title: 'Error',
                    text:  'Data found',
                    type: 'success',
                    delay: 300
                    });
                });

                $("#get_report_button").click();

                }else{
                    $(`#class_section_id option[value=""]`).prop('selected', true);
                    $("#student_id").html('');
                    $(function(){
                        new PNotify({
                        title: 'Error',
                        text:  'Data not found',
                        type: 'error',
                        delay: 300
                        });
                    });
                    $("#sales_table").html(``);

                }
            }
        });
    }

    function construct_student_deails() {
        let promotion_status= $("#student_id").find(":selected").data("promotion_status");
        let studentN= $('#student_id').find(":selected").text();
        if(promotion_status == '4' || promotion_status == '5') {
            studentN= '<font color="red">' + studentN + ' - Alumini</font>';
        }
        var std= ``;
        std += `
            <h4 style="width: fit-content; margin: 0 20px 0 0;"><strong>Name:</strong> ${studentN}</h4> <h4 style="width: fit-content; margin: 0 0 0 20px;"><strong>Class:</strong> ${$('#class_section_id').find(":selected").text()}</h4>
            
        `;
        $("#student_details_div").html(std);
    }

    async function get_reports() {
        var student_id= $("#student_id").val();
        var selected_subcategories= $("#selected_subcategories").val();
        var selected_category= $("#selected_category").val();
        var sales_year_id= $("#sales_year_id").val();

        if(student_id) {
            await $("#sales_table").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
            await $("#sales_return_table").html(``);
            await $("#get_report_button").prop('disabled', true).html('Please Wait...');
            await $.ajax({
                url: '<?php echo site_url('procurement/sales_controller_v2/get_report_of_a_student'); ?>',
                type: "post",
                data: {student_id, selected_category, selected_subcategories, sales_year_id},
                success: await function(data) {
                    var p_data = JSON.parse(data);
                    var sab_thik_hai_kya= p_data.sab_thik_hai_kya;
                    var final_arr= p_data.final_arr;
                    // let status= p_data.status;
                    // let sales= p_data.sales;
                    // let sales_return= p_data.return;
                    if(sab_thik_hai_kya == '1') {
                        construct_student_deails();
                        construct_student_s_item_table(final_arr);
                    } else {
                        $("#sales_table").html(`<br><br><div class="no-data-display">Data Not Found</div>`);
                        $("#sales_return_table").html(``);
                    }
// Don't delete this code (Check in the controller this code is exist)
                    // if(status != 'ns_nr') {
                    //     construct_student_deails();
                    //     construct_sales_table(sales);
                    //     if(status == 's_r') {
                    //         construct_return_table(sales_return);
                    //         construct_summary(sales, sales_return);
                    //     }
                    // } else {
                    //     $("#sales_table").html(`<br><br><div class="no-data-display">Data Not Found</div>`);
                    //     $("#sales_return_table").html(``);
                    // }
                    
                }
            });
            await $("#get_report_button").prop('disabled', false).html('Get Report');
        } else {
            alert('Please select a student first.');
        }
    }

    function construct_student_s_item_table(final_arr) {

        var selected_student_name = $('#student_id').find(":selected").text();
        $("#sales_table").html('Loading...');
        var sales_table= `
                    <div><br>
                   
                        <table class="table table-bordered" id="table1">
                        <thead>
                            <tr>
                                <th colspan="3" style="font-weight: 1000;">Total Quantity = <span id="tot_qty"></span></th>
                                <th  colspan="4" style="font-weight: 1000;">Total Amount = <span class="fa fa-inr"></span> <span id="tot_amt"></span></th>
                            </tr>
                            <tr>
                                <th>#</th>
                                <th>Transaction Type</th>
                                <th>Transaction Details</th>
                                <th>Item Name</th>
                                <th>Quantity</th>
                                <th>Unit Selling Price (Cost Price)</th>
                                <th>Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
        let sn= 0;
        var qtys= 0;
        var amts= 0;
        for (var i= 0; i<final_arr.length; i++) {
            var item= `<font color="#d4d4d4">${final_arr[i].category_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#7b8b91">${final_arr[i].subcategory_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#000000">${final_arr[i].item_name}</font>`;
           if(final_arr[i].tx_type != 'Total') {

            if(final_arr[i].is_bought_from_prev_sales_year && final_arr[i].is_bought_from_prev_sales_year == '1') { // Subtract if only return not bought. That means these returned item was bought from previous sales year
                qtys -= Number(final_arr[i].quantity);
                amts -= Number(final_arr[i].amount);
            }
                sales_table += `<tr>`;
                sales_table += `<td>${final_arr[i].tx_type == 'Issue' ? ++sn : ''}</td>`;
                sales_table += `<td style="color: ${final_arr[i].tx_type == 'Issue' ? 'green' : 'red'};">${final_arr[i].tx_type}</td>`;
                sales_table += `<td style="color: ${final_arr[i].tx_type == 'Issue' ? 'green' : 'red'};">${final_arr[i].tx_type == 'Issue' ? final_arr[i].sold_on : final_arr[i].return_on}</td>`;
                sales_table += `<td>${item}</td>`;
                sales_table += `<td>${final_arr[i].symbol == '-' ? '(' : ''} ${final_arr[i].quantity} ${final_arr[i].symbol == '-' ? ')' : ''}</td>`;
                sales_table += `<td>${(+final_arr[i].selling_price).toFixed(2)} (${(+final_arr[i].cost_prodcut).toFixed(2)})</td>`;
                sales_table += `<td>${final_arr[i].symbol == '-' ? '(' : ''} ${(+final_arr[i].amount).toFixed(2)} ${final_arr[i].symbol == '-' ? ')' : ''}</td>`;
                sales_table += `</tr>`;
           } else {
            //    alert();
            //  else {
                qtys += Number(final_arr[i].quantity);
                amts += Number(final_arr[i].amount);
            // }
                sales_table += `<tr>`;
                sales_table += `<td></td>`;
                sales_table += `<td style="color: black; font-weight: bold;">${final_arr[i].tx_type}</td>`;
                sales_table += `<td>-</td>`;
                sales_table += `<td>-</td>`;
                sales_table += `<td style="color: black; font-weight: bold;">${final_arr[i].quantity}</td>`;
                sales_table += `<td>-</td>`;
                sales_table += `<td style="color: black; font-weight: bold;">${(+final_arr[i].amount).toFixed(2)}</td>`;
                sales_table += `</tr>`;

                sales_table += `<tr>`;
                sales_table += `<td colspan="6" style="height: 20px;"></td>`;
                sales_table += `</tr>`;
           }
        }
        sales_table += `
                        </tbody>
                    </table>
                </div>
                `;

        $("#sales_table").html(sales_table);
        $("span#tot_qty").html(qtys);
        $("span#tot_amt").html(amts.toFixed(2));
    }

    function construct_sales_table(sales, summary) {
        var selected_student_name = $('#student_id').find(":selected").text();
        $("#sales_table").html('Loading...');
        var sales_table= `
                    <div><br>
                    <div><strong style="margin: 0 0 -6px 0;">Items Sell (<b id="total_sell_price"></b>)</strong> 
                    </div>
                        <table class="table table-bordered" id="table1">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Item Name</th>
                                <th>Quantity</th>
                                <th>Unit Sell Price (Cost Price)</th>
                                <th>Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
        let sn= 1;
        var qtys= 0;
        var amts= 0;
        var tot_sell_price= 0;
        for (var i= 0; i<sales.length; i++) {
            var item= `<font color="#d4d4d4">${sales[i].category_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#7b8b91">${sales[i].subcategory_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#000000">${sales[i].item_name}</font>`;
            sales_table += `<tr>`;
            sales_table += `<td>${sn++}</td>`;
            sales_table += `<td>${item}</td>`;
            sales_table += `<td>${sales[i].quantity}</td>`;
            sales_table += `<td>${(+sales[i].selling_price).toFixed(2)} (${(+sales[i].cost_prodcut).toFixed(2)})</td>`;
            sales_table += `<td>${(+sales[i].amount).toFixed(2)}</td>`;
            // sales_table += `<td>${sales[i].sales_master_remarks}</td>`;
            sales_table += `</tr>`;

            qtys += Number(sales[i].quantity);
            amts += Number(sales[i].amount);
            

            if(i<sales.length - 1 && sales[i].subcategory_name != sales[i+1].subcategory_name || i == sales.length - 1) {
                sales_table += `<tr>`;
                // sales_table += `<th>-</th>`;
                sales_table += `<th colspan="2" style="text-align: right;">Total</th>`;
                sales_table += `<th>${qtys}</th>`;
                sales_table += `<th>-</th>`;
                sales_table += `<th>${amts.toFixed(2)}</th>`;
                sales_table += `</tr>`;

                tot_sell_price += amts;

                sales_table += `<tr style="height: 20px;">
                                    <td colspan="5"></td>
                                </tr>`;

                qtys= 0;
                amts= 0;
            }
        }

        sales_table += `
                        </tbody>
                    </table>
                </div>
                `;

        $("#sales_table").html(sales_table);
        setTimeout(() => {
            $("#total_sell_price").html(`Total: ${tot_sell_price}`);
        }, 1500);
    }

    async function construct_return_table(sales_return) {
        var selected_student_name = $('#student_id').find(":selected").text();
        await $("#sales_return_table").html('Loading...');
        var return_table= `
                    <div><br><div><strong>Sales Return (<b id="total_return_price"></b>)</strong></div>
                        <table class="table table-bordered" id="table2">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Item Name</th>
                                <th>Return Quantity</th>
                                <th>Unit Selling Prize</th>
                                <th>Unit Refund Prize</th>
                                <th>Total Refund Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
        let sn= 1;
        var qtys= 0;
        var amts= 0;
        var tot_ret_price= 0;
        for (var i= 0; i<sales_return.length; i++) {
            // console.log('Nov 27: ', v, sales_return);
            var item= `<font color="#d4d4d4">${sales_return[i].category_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#7b8b91">${sales_return[i].subcategory_name}</font> <span class="fa fa-angle-double-right"></span> <font color="#000000">${sales_return[i].item_name}</font>`;
            return_table += `<tr>`;
            return_table += `<td>${sn++}</td>`;
            return_table += `<td>${item}</td>`;
            return_table += `<td>${sales_return[i].return_quantity} ${sales_return[i].unit_type ? '(' +sales_return[i].unit_type+ ')' : ''}</td>`;
            return_table += `<td>${sales_return[i].selling_price || ''}</td>`;
            return_table += `<td>${+sales_return[i].refund_amount != 0 && +sales_return[i].return_quantity != 0 ? [+( (+(sales_return[i].refund_amount)) / (+(sales_return[i].return_quantity)) ).toFixed(2)] : ''}</td>`;
            return_table += `<td>${(+sales_return[i].refund_amount).toFixed(2) || ''}</td>`;
            return_table += `</tr>`;

            qtys += Number(sales_return[i].return_quantity);
            amts += Number(sales_return[i].refund_amount);
            

            // console.log(i == sales_return.length - 1, "i == sales_return.length - 1")
            if(i<sales_return.length - 1 && sales_return[i].subcategory_name != sales_return[i+1].subcategory_name || i == sales_return.length - 1) {
                return_table += `<tr>`;
                // return_table += `<th>-</th>`;
                return_table += `<th colspan="2" style="text-align: right;">Total</th>`;
                return_table += `<th>${qtys}</th>`;
                return_table += `<th>-</th>`;
                return_table += `<th>-</th>`;
                return_table += `<th>${amts.toFixed(2)}</th>`;
                return_table += `</tr>`;

                tot_ret_price += amts;
                
                return_table += `<tr style="height: 20px;">
                                    <td colspan="6"></td>
                                </tr>`;

                qtys= 0;
                amts= 0;
            }


        }

        return_table += `
                        </tbody>
                    </table>
                </div>
                `;

        $("#sales_return_table").html(return_table);
        setTimeout(() => {
            $("#total_return_price").html(`Total: ${tot_ret_price}`);
        }, 1500);
    }

    $("#student_id").change(event, async function() {
        var student_id= $("#student_id").val();
        if(student_id) {
            await $("#get_report_button").show();
            await $("#get_report_button").click();
        } else {
            await $("#get_report_button").hide();
        }
    });

    // async function printDiv(divName) {
    //     await $("#heading").show();
    //     var printContents = document.getElementById(divName).innerHTML;
    //     var originalContents = document.body.innerHTML;
    //     document.body.innerHTML = printContents;
    //     await window.print();
    //     document.body.innerHTML = originalContents;
    //     await $("#heading").hide();
    // }

    function print_graph_table() {
        var old_tablam_div= $("#details_divs").html();
        $('table').css('border-collapse', 'collapse');
        $("tr, th, td").css('border', '1px solid black');
        $("th, td").css('minWidth', '200px').css('maxWidth', '200px');

        var table1= document.getElementById('sales_table').innerHTML;
        var table2= document.getElementById('sales_return_table').innerHTML;
        var heading= document.getElementById('student_details_div').innerHTML;

        var a= window.open('', '', 'height=1200, width=1200');

        a.document.write('<html>');
        a.document.write('<body><center><h2><?php echo $this->settings->getSetting('school_name'); ?></h2>');
        a.document.write(heading);
        a.document.write(table1);
        a.document.write(table2);
        a.document.write('</center></body></html>');
        a.document.close();
        a.print();
        a.close(); // Add this line to close the window
        $("#details_divs").html(old_tablam_div);
    }

    function export_table(){
        var old_tablam_div= $("#details_divs").html();
        $('table').css('border-collapse', 'collapse');
        $("tr, th, td").css('border', '1px solid black');
        $("th, td").css('minWidth', '200px').css('maxWidth', '200px');
        var table1= document.getElementById('sales_table').innerHTML;
        var table2= document.getElementById('sales_return_table').innerHTML;

       
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };
        var head = '';
        var title = '';
        // var mainTable = $("#mainTable").html();

        var heading= document.getElementById('student_details_div').innerHTML;
        htmls = '<br><table><tr><td colspan="6">'+heading+'</td></tr></table><br>' + table1 + '<br><br>' + table2 + '<br><br>' ;

        var ctx = {
            worksheet : 'Spreadsheet',
            table : htmls
        }


        var link = document.createElement("a");
        link.download = "export.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();

        // $("#mainTable").html(flash_storer);
        $("#details_divs").html(old_tablam_div);

    }

    function construct_summary(sales, returns) {
        var summ= ` <div class="col-md-12" style="opacity: 0; width: 100%; height: 25px;">p</div>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th colspan="4" style="text-align: center;">Summary</th>
                            </tr>
                            <tr>
                                <th>Item</th>
                                <th>Issued</th>
                                <th>Returned</th>
                                <th>Balance <small>(Need to Return)</small></th>
                            </tr>
                        </thead>
                        <tbody>`;
        for (var i= 0; i<sales.length; i++) {
           summ += `<tr>
                        <th>${sales[i].item_name}</th>
                        <td>${sales[i].quantity}</td>`;
            var ret= 0;
            var bal= +sales[i].quantity;
            for (let j = 0; j < returns.length; j++) {
                if(sales[i].proc_im_items_id == returns[j].proc_im_items_id) {
                    ret= +returns[j].return_quantity;
                    bal= bal - ret;
                    break;
                }
            }
                summ += `<td>${ret}</td>
                        <th>${bal}</th>
                    </tr>`;
        }
        summ += `</tbody>
                </table>`;
        $("#summary_div").html(summ);
    }
    
</script>


        
            <td></td>
            <th></th>
        </tr>
    
<style type="text/css">
#tags{
    position:relative;
    padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  background-color: #e9e9e9; 
}
.autocomplete-active {
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
.del {
  max-width: 600px;
  margin: auto;
}
</style>