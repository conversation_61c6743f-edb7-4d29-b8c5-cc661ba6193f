<div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
  <?php
    /**
     * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
     * For new students, we will display only FEES and PROFILE. Other features are hidden.
     */
    $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
  ?>
  <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
    <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
    <h3 class="card-title">
      <strong> Student </strong>
      <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NAME')) : ?>
     <?php echo ucfirst($studentData->stdName) ;?>
     <?php endif ?>
    </h3>
    <?php if ( $studentData->profile_status == 'Unlock') { ?>
       <ul class="panel-controls ml-auto">
          <a  class="btn btn-primary btn-md " href="<?php echo site_url('parent_controller/edit_profile_parent/'.$callFrom); ?>"><i class="fa fa-pencil" style="font-size: 13px;"></i>Edit</a>
          <!-- <a href="https://www.nextelement.in/se_help/parent/index.htm#t=user_guide%2Fprofile%2Fedit_pro.htm" class="circleButton_noBackColor pull-right" add target="_blank" data-toggle="tooltip" data-placement="top" title="Help">
            <span class="fa fa-question fontColor_orange" style="font-size: 19px;"></span>
          </a> -->
       </ul>
    <?php } ?>
    </div>
  </div>
  <div class="card-body">
    <div class="row"  style="margin: 0px;">
      <div class="col-md-12">

        <div class="col-md-2">
          <?php
            $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            $gender = 'Female';
            if($studentData->gender == 'M'){
              $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
              $gender = 'Male';
            }
            ?>
          <?php if($this->settings->isProfile_profile_enabled('STUDENT_PHOTO') && !$isNewStudent){ ?>
          <img class="img-responsive" src="<?php echo (empty($studentData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->picture_url); ?>">
          <?php } ?>
        </div>

        <div class="col-lg-8 col-md-12">
          <form class="form-horizontal">
            <div class="row">
              <?php if($this->settings->isProfile_profile_enabled('ADMISSION_NO') && !$isNewStudent){ ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label" for="adm"><strong>Admission No </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo $studentData->admissionNo;?></h5>
                </div>
              </div>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('ENROLLMENT_NUMBER')){ ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label" for="adm"><strong>Enrollment Number </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo $studentData->enrollment_number;?></h5>
                </div>
              </div>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('ALPHA_ROLL_NUMBER')){ ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label" for=""><strong>Alpha Roll Number </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo $studentData->alpha_rollnum;?></h5>
                </div>
              </div>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('CLASS_SECTION') && !$isNewStudent){ ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label" for="cs"><strong>Class / Section </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo $studentData->className.'/'.$studentData->sectionName ;?></h5>
                </div>
              </div>
              <?php } ?>

              <?php if($this->settings->getSetting('is_semester_scheme')){ ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Semester </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo $studentData->semester;?></h5>
                  </div>
                </div>
              <?php } ?>
              
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_EMAIL')) : ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label"><strong>Email ID </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($studentData->student_email == '') ? 'Not available' : $studentData->student_email;?></h5>
                </div>
              </div>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('INITIAL_PASSWORD')) : ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label"><strong>Initial Password  </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($studentData->student_email_password == '') ? 'Not available' : $studentData->student_email_password;?></h5>
                </div>
              </div>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_DOB')) : ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label " for="dob"><strong>Date of Birth </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($studentData->dob)?date('d-M-Y', strtotime($studentData->dob)):'' ?></h5>
                </div>
              </div>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOTHER_TONGUE')) : ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label " for="dob"><strong>Mother Tongue</strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($studentData->mother_tongue == '')? 'Not available' : $studentData->mother_tongue ;?></h5>
                </div>
              </div>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_GENDER')) : ?>
              <div class="form-group col-md-6">
                <label class="col-md-5 control-label" for="g"><strong>Gender </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php if ($studentData->gender == 'M') {echo 'Male';} else if($studentData->gender == 'F'){echo 'Female';}else{echo 'Not available';} ?></h5>
                </div>
              </div>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_BLOOD_GROUP')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Blood Group  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->blood_group == '')? 'Not available' : $studentData->blood_group ;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_STOP')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Stop  </strong></label>
                  <div class="col-md-7">
                    <!-- <h5 class="form-control-static"><?php // echo ( $stops[$studentData->stop] || $stops[$studentData->stop] == '')? 'Not available' : $stops[$studentData->stop] ;?></h5> -->
                    <h5 class="form-control-static">
                          <?php 
                          if (isset($stops[$studentData->stop])) {
                              echo ($stops[$studentData->stop] == '') ? 'Not available' : $stops[$studentData->stop];
                          } else {
                              echo 'Not available';
                          }
                          ?>
                      </h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_PICKUP_MODE')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Pickup Mode </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->pickup_mode == '')? 'Not available' : $studentData->pickup_mode ;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NATIONALITY')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Nationality  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static" >
                          <?php 
                          if (isset($studentData->nationality)) {
                              echo ($studentData->nationality == '') ? 'Not available' : $studentData->nationality;
                          } else {
                              echo 'Not available';
                          }
                          ?>
                      </h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('CATEGORY')) : ?>

              <div class="form-group col-md-6">
                <label class="col-md-5 control-label"><strong>Category  </strong></label>
                <div class="col-md-7">
                  <h5 class="form-control-static"><?php echo ($studentData->category == '') ? 'Not available' : $studentData->category ;?></h5>
                </div>
              </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_CASTE')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Caste  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->caste == '' || $studentData->caste == '0') ? 'Not available' : $studentData->caste ;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_RELIGION')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Religion  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->religion == '' || $studentData->religion == '0') ? 'Not available' : $studentData->religion;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOBILE_NUMBER')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Mobile Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->student_mobile_no == '') ? 'Not available' : $studentData->student_mobile_no;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('PREFFERED_CONTACT_NUMBER')) : ?>

                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Preffered Contact Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->preferred_contact_no == '') ? 'Not available' : $studentData->preferred_contact_no;?></h5>
                  </div>
                </div>

                <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_AADHAR')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Aadhar Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->aadhar_no == '') ? 'Not available' : $studentData->aadhar_no;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('NAME_AS_PER_AADHAR')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Name As Per Aadhar Number  </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php echo ($studentData->name_as_per_aadhar == '') ? 'Not available' : $studentData->name_as_per_aadhar;?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_ADDRESS') && !$isNewStudent) : ?>

              <?php if (!empty($studentAddress)) {
                foreach ($studentAddress as $val => $address) { ?>
                  <div class="form-group col-md-6">
                    <label class="col-md-5 control-label"><strong><?php echo $val ?>  </strong></label>
                    <?php foreach ($address as $key => $s_ad) { ?>
                      <div class="col-md-7">
                        <h5 class="form-control-static">
                          <?php if($s_ad->Address_line1 == '' && $s_ad->Address_line2 == '' && $s_ad->area == '' && $s_ad->district == '' && $s_ad->state == '' && $s_ad->country == '' && $s_ad->pin_code == ''){ echo 'Not available';}else{ echo $s_ad->Address_line1 .' '.$s_ad->Address_line2.' '.$s_ad->area.' '.$s_ad->district.' '.$s_ad->state.' '.$s_ad->country.' '.$s_ad->pin_code; } ?>
                        </h5>
                      </div>
                    <?php } ?>
                  </div>
                <?php }
              } ?>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_HOUSE')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>House </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php 
                      if ($studentData->student_house == '0' || $studentData->student_house == null) {
                        echo "Not available";
                      }else{
                        echo  ucwords($studentData->student_house);
                      } 
                    ?></h5>
                  </div>
                </div>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('COMBINATION')) : ?>
                <div class="form-group col-md-6">
                  <label class="col-md-5 control-label"><strong>Course </strong></label>
                  <div class="col-md-7">
                    <h5 class="form-control-static"><?php 
                      if ($studentData->combination_name == '0' || $studentData->combination_name == null) {
                        echo "Not available";
                      }else{
                        echo  ucwords($studentData->combination_name);
                      } 
                    ?></h5>
                  </div>
                </div>

              <?php endif ?>

            </div>

          </form>
        </div>

      </div>
    </div>
  </div>
</div>
