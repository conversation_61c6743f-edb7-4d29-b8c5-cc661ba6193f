<?php

class Subject_master extends CI_Controller {
    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('SCHOOL') || !$this->authorization->isModuleEnabled('SUBJECT.MASTER')) {
          redirect('dashboard', 'refresh');
        }
        $this->load->model('subjects/Subjects_model', 'subject_model');
    }

    public function index() {
        $data['main_content'] = 'subject_master/index.php';
        $this->load->view('inc/template', $data);
    }

    public function add_or_update() {
        $result = $this->subject_model->add_or_update_subject($_POST);
        if (!$result) {
          $data['result'] = 0;
        } else {
          $data['result'] = 1;
          $data['subject'] = $this->subject_model->get_subject_by_id($result);
        }
        echo json_encode($data);
    }

    public function delete() {
        echo $this->subject_model->delete_subject($_POST['subject_id']);
    }

    public function subject_list() {
        $data['subjects'] = $this->subject_model->get_subject_list();
        echo json_encode($data);
    }
}