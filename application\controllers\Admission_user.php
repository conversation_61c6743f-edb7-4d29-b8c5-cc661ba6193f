<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Admission_user extends CI_Controller {

	public function __construct() {		
		parent::__construct();

        $this->load->model('Admission_model');
        $this->load->library('session');
        $this->load->helper('captcha');
        $this->load->library('filemanager');
	}

    public function verifyMobileForm() {
        if(isset($_SESSION['loginstatus'])) {
            $mobileNumber = $this->session->userdata('loginstatus');
            $data['isGuest'] = true;
            $data['mobileNumber'] = $mobileNumber;
            $data['au_id'] = $this->Admission_model->get_id_mobile_number($mobileNumber);
            $data['classess'] = $this->Admission_model->get_classess();
			$data['admissions'] = $this->Admission_model->admission_settings_get();
            // $data['main_content'] = 'admission/registerForm';			
			$data['admission_form_word'] = $this->settings->getSetting('admission_form_word');
			$data['disable_new_admissions_tab'] = $this->settings->getSetting('disable_new_admissions_tab');
			$data['no_application_display'] = $this->settings->getSetting('admissions_no_application_display_text');
			$data['my_application'] = $this->Admission_model->get_all_admission_process($data['au_id']);
				foreach ($data['my_application'] as $key => &$application) {
					$application->continue_status = '';
					$application->revert_info =  $this->Admission_model->get_revert_admission_info($application->id);
					$application->rejected_documents = $this->Admission_model->get_rejected_documents($application->id);
					foreach ($data['admissions'] as $key => $val) {
						$class_applied_for = json_decode($val['class_applied_for'], true);
						if ($val['open_for_admissions'] == TRUE) {
							if ($application->admission_setting_id == $val['id']) {
								$application->enable_partial_payment = $val['enable_partial_payment'];
								if (in_array($application->grade_applied_for, $class_applied_for)) {
									$application->status = 'enabled';
								}
							}
						}else{
							$application->enable_partial_payment = 0;
						}
						if ( $application->admission_setting_id == $val['id'] && $val['open_for_admissions'] == FALSE && ($application->curr_status == 'Draft' || $application->curr_status == 'Payment Pending')) {
							$application->continue_status = 'disabled';
						}
					}
				}
			$data['main_content'] = 'admission/form/selection_classes';			
			
            // $data['main_content'] = 'admission/form/dashboard';
            $this->load->view('admission/inc/short_application_template', $data);

        } else {

            $this->load->view('admission/verifyMobile_new');
        }
    }

    public function verifyMobile() {

    	$data['isGuest'] = true;
		$data['main_content'] = 'admission/verifyMobile_new';
        $this->load->view('admission/inc/template', $data);

    }

    public function sendOTP() {

		$input = $this->input->post();
    	$otp = rand(100000,999999);
    	$email = $this->settings->getSetting('admission_email_based_otp');
    	$smsint = $this->settings->getSetting('smsintergration');
    	$msg = $otp. ' is your One Time Password for verification as an applicant for registration-Nextelement';
    	$from_name = $this->settings->getSetting('school_name');
    	$emailMessage = '
		Dear Parent ,
		<br>
		Your OTP for '.$from_name.'admissions is '.$otp.'
		<br>
		Thanks and Regards, <br>
		-'.$from_name.'';

		if (filter_var($input['mobileNumber'], FILTER_VALIDATE_EMAIL ) && $email) {
        	$emailId = $input['mobileNumber'];
        	$data = array(
	            'send_unique_emails' => 1,
	            'message' => [$emailMessage],
	            'subject' => 'OTP for Admissions Process',
	            'mail_username' => ['NextElement'],
	            'email_ids' => [$emailId],
	            'template' => 'Admission OTP'
	        );
        	$res = $this->__email_one_time_verification_application($data);
        	//echo "<pre>"; print_r($res); die();
			if($res) {
				$this->Admission_model->insertOTP($input, $otp);
				echo json_encode(['status' => 'ok','msg' => 'Email Sent!']);
			} else {
				echo json_encode(['status' => 'error','msg' => 'Unable to send Email please try again!' ]);
			}
		 	
        }else{
    	 	$content =  urlencode(''.$msg.'');

			// $content = 'Thisisyourone-timepassword:'.$otp;
			$get_url = 'https://'.$smsint->url.'?apikey='.$smsint->api_key.'&senderid='.$smsint->sender.'&number='.$input['mobileNumber'].'&message='.$content;
			
			// 04-07-2025 Mohan 
			// $get_url modiefied new api key requirments. the following commented code is old api key requirements.
			 
			// $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$input['mobileNumber'].'&sender='.$smsint->sender.'&message='.$content;
			$check_returned = $this->curl->simple_get($get_url);

			if(!empty($check_returned)) {

				$check_returned = json_decode($check_returned);

				if($check_returned->status == 'OK') {
					$this->Admission_model->insertOTP($input, $otp);
					echo json_encode(['status' => 'ok','msg' => 'SMS Sent!']);
				} else {
					echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
				}
			} else {
				echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
			}
        }

    	//temporarily commnting old sms sending code

        
	}

	private function __email_one_time_verification_application($data){

	 	$from_name = $this->settings->getSetting('school_name');    

        $from_email = $this->settings->getSetting('admisison_one_time_password_send_email_id');   

        // $from_name = $set->email_subject;
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];

        $data['from_email'] = $from_email;
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;


        $data = http_build_query($data);
        $curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        // echo "<pre>"; print_r($response);
        // echo "<pre>"; print_r($err);
        // die();
        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
	}

	private function new_sms_sender($number, $message) {
		$api_key = 'adwm0e5HYvQE3TNI';
		$senerid = 'NXTSMS';
		//http://promotional.mysmsbasket.com/V2/http-api.php?apikey=XXXXXXXXXXXXXXXX&senderid=XXXXXX&number=XXXXXXXXXXX,XXXXXXXXXXX,XXXXXXXXXXX&message=hello there&format=json
		$url = 'http://promotional.mysmsbasket.com/V2/http-api.php';
	    $curl = curl_init();
	    curl_setopt_array($curl, array(
	      CURLOPT_URL => $url,
	      CURLOPT_RETURNTRANSFER => true,
	      CURLOPT_ENCODING => "",
	      CURLOPT_MAXREDIRS => 10,
	      CURLOPT_TIMEOUT => 30,
	      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	      CURLOPT_CUSTOMREQUEST => "POST",
	      CURLOPT_POSTFIELDS => "apikey=".$api_key."&senderid=".$senerid."&number=".$number."&message=".$message."&format=json",
      	  CURLOPT_HTTPHEADER => array(
	        "Accept: application/json",
	        "Cache-Control: no-cache",
	        "Content-Type: application/x-www-form-urlencoded"
	      ),
    	));

    	$response = curl_exec($curl);
    	$err = curl_error($curl);
    	curl_close($curl);

    	// trigger_error($response);
    	$result = json_decode($response);
    	if($result->status == 'OK') {
    		return 1;
    	}
    	return 0;
	}

	public function instruction_by_setting_id(){
		$admission_setting_id = $this->input->post('admission_setting_id');
        $admissions_data = $this->_get_admissions_settings_byId($admission_setting_id);
		if(!empty($admissions_data['address'])){
			$admissions_data['address'] = json_decode($admissions_data['address']);
		}
		// echo '<pre>';print_r($admissions_data['address']);die();
		echo json_encode($admissions_data);
	}

	public function saveAction() {
		$data['disable_new_admissions_tab'] = $this->settings->getSetting('disable_new_admissions_tab');
		$data['no_application_display'] = $this->settings->getSetting('admissions_no_application_display_text');
		$input = $this->input->post();
        if(isset($_SESSION['loginstatus'])) {
            $mobileNumber = $this->session->userdata('loginstatus');
			$mobileNumber = preg_replace('/^0/', '', $mobileNumber);
            $data['isGuest'] = true;
            $data['mobileNumber'] = $mobileNumber;

            $data['au_id'] = $this->Admission_model->get_id_mobile_number($mobileNumber);
            $this->session->set_userdata('admission_user_id', $data['au_id']);

            $data['classess'] = $this->Admission_model->get_classess();
            // $data['main_content'] = 'admission/registerForm';
				
				$data['admission_form_word'] = $this->settings->getSetting('admission_form_word');
				$data['short_name'] = $this->settings->getSetting('school_short_name');
				$data['admissions'] = $this->Admission_model->admission_settings_get();

				$data['au_id'] = $this->session->userdata('admission_user_id');
				$data['my_application'] = $this->Admission_model->get_all_admission_process($data['au_id']);
				foreach ($data['my_application'] as $key => &$application) {

					$application->continue_status = '';
					$application->revert_info =  $this->Admission_model->get_revert_admission_info($application->id);
					$application->rejected_documents = $this->Admission_model->get_rejected_documents($application->id);
					foreach ($data['admissions'] as $key => $val) {

						$class_applied_for = json_decode($val['class_applied_for'], true);

						if ($val['open_for_admissions'] == TRUE) {
							if ($application->admission_setting_id == $val['id']) {
								$application->enable_partial_payment = $val['enable_partial_payment'];
								if (in_array($application->grade_applied_for, $class_applied_for)) {
									$application->status = 'enabled';
								}
							}
						}else{
							$application->enable_partial_payment = 0;
						}
						if ($application->admission_setting_id == $val['id'] &&  $val['open_for_admissions'] == FALSE && ($application->curr_status == 'Draft' || $application->curr_status == 'Payment Pending')) {
							$application->continue_status = 'disabled';
						}
					}
				}
				$data['main_content'] = 'admission/form/selection_classes';
				$this->load->view('admission/inc/short_application_template', $data);
				// $data['main_content'] = 'admission/form/dashboard';
				// $this->load->view('admission/inc/template', $data);

        } else if($this->Admission_model->verifyOTP($input)) {

            $this->session->set_userdata('loginstatus', $input['mobileNumber']);

			$data['isGuest'] = true;
			$data['mobileNumber'] = $input['mobileNumber'];
			$config = array(
	            'img_url' => base_url() . 'assets/image_for_captcha/',
	            'img_path' => 'assets/image_for_captcha/',
	            'img_height' => 40,
	            'word_length' => 5,
	            'img_width' => 150,
	            'font_size' => 12
	        );

	        $captcha = create_captcha($config);
	        $this->session->unset_userdata('valuecaptchaCode');
	        $this->session->set_userdata('valuecaptchaCode', $captcha['word']);
	        $data['captchaImg'] = $captcha['image'];
	        $data['captchaWord'] = $captcha['word'];
	        $data['au_id'] = $this->Admission_model->get_id_mobile_number($data['mobileNumber']);
            $this->session->set_userdata('admission_user_id', $data['au_id']);
			$data['classess'] = $this->Admission_model->get_classess();
			// $data['main_content'] = 'admission/registerForm';
				$data['admission_form_word'] = $this->settings->getSetting('admission_form_word');
				$data['admissions'] = $this->Admission_model->admission_settings_get();
				$data['au_id'] = $this->session->userdata('admission_user_id');
				$data['my_application'] = $this->Admission_model->get_all_admission_process($data['au_id']);
				foreach ($data['my_application'] as $key => &$application) {
					$application->continue_status = '';
					$application->revert_info =  $this->Admission_model->get_revert_admission_info($application->id);
					$application->rejected_documents = $this->Admission_model->get_rejected_documents($application->id);
					foreach ($data['admissions'] as $key => $val) {
						$class_applied_for = json_decode($val['class_applied_for'], true);
						if ($val['open_for_admissions'] == TRUE) {
							if ($application->admission_setting_id == $val['id']) {
								$application->enable_partial_payment = $val['enable_partial_payment'];
								if (in_array($application->grade_applied_for, $class_applied_for)) {
									$application->status = 'enabled';
								}
							}
						}else{
							$application->enable_partial_payment = 0;
						}
						if ($application->admission_setting_id == $val['id'] &&  $val['open_for_admissions'] == FALSE && ($application->curr_status == 'Draft' || $application->curr_status == 'Payment Pending')) {
							$application->continue_status = 'disabled';
						}
					}
				}
				$data['main_content'] = 'admission/form/selection_classes';
				$this->load->view('admission/inc/short_application_template', $data);
				// $data['main_content'] = 'admission/form/dashboard';
				// $this->load->view('admission/inc/template', $data);
			

		} else {
			// redirect('admissions');
			echo 0;
		}
	}

	private function _get_admissions_settings_byId($id){
		return  $this->Admission_model->admission_settings_getbyId($id);
	}
}
