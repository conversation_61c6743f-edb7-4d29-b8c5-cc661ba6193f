<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 april 2023
 *
 * Description: Controller for Inentory Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Requisition_controller_v2 extends CI_Controller {
	function __construct() {
	    parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        //This should come up only for super admins
        if (!$this->authorization->isModuleEnabled('PROCUREMENT') || !$this->authorization->isAuthorized('PROCUREMENT.MODULE')) {
            redirect('dashboard', 'refresh');
          }

        $this->load->model('procurement/Requisition_model_v2');  
        $this->load->library('filemanager');
    }

    public function index(){
        $site_url = site_url();

        $data['sara_tiles'] = array(
// First Row
            [
                'title' => 'Inventory Management',
                'sub_title' => 'Add / Edit / View Items',
                'icon' => 'svg_icons/add.svg',
                'url' => $site_url.'procurement/inventory_controller_v2/item_master_widgets',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            ],
            [
                'title' => 'Vendor Management',
                'sub_title' => 'Add / Edit / View Vendors',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/vendor_controller_v2/vendor_master',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION')
            ],
            [
                'title' => 'Manage Service Contracts',
                'sub_title' => 'Add / Edit / View Service Contracts',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/service_contract_controller/service_contract_dashboard',
                'permission' =>  $this->authorization->isModuleEnabled('PROCUREMENT_SERVICE_CONTRACT') && $this->authorization->isAuthorized('PROCUREMENT_SERVICE_CONTRACT.MODULE')
            ],
// Second Row = Purchase Flows
            [
                'title' => 'Indent Management',
                'sub_title' => 'Add / Edit / View Items',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'procurement/inventory_controller_v2/indent_widgets_v2',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            ],
            [
                'title' => 'Purchase Order Management',
                'sub_title' => 'Add / Edit / View Items',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url.'procurement/inventory_controller_v2/indent_widgets',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            ],
            [
                'title' => 'Delivery Management',
                'sub_title' => 'Add/View Invoice',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/invoice_controller_v2/index',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            ],
            [
                'title' => 'Delivery Management V2',
                'sub_title' => 'Add/View Delivery',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/invoice_controller_v2/index_v2',
                'permission' => $this->authorization->isSuperAdmin()
            ],
            // [
            //     'title' => 'Service Delivery Challan Management',
            //     'sub_title' => 'Add / Edit / View Service Deliveries',
            //     'icon' => 'svg_icons/assessment.svg',
            //     'url' => $site_url . 'procurement/inventory_controller_v2/service_delivery_management',
            //     'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            // ],
           
            [
                'title' => 'Invoice Management',
                'sub_title' => 'Add / Edit / View Invoices',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/invoice_controller_v2/invoice_management_dashboard',
                'permission' => $this->authorization->isSuperAdmin()
            ],
// Administration
            [
                'title' => 'Budget Management',
                'sub_title' => 'Add / Edit / View Procurement Budget',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/budget_controller/budget_dashboard',
                'permission' =>  $this->authorization->isModuleEnabled('PROCUREMENT_BUDGET') && $this->authorization->isAuthorized('PROCUREMENT_BUDGET.MODULE')
            ],
            [
                'title' => 'Procurement Sales Year',
                'sub_title' => 'Sales Year',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/requisition_controller_v2/procurement_sales_year',
                'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
            ],
            
            [
                'title' => 'Payment Voucher Management',
                'sub_title' => 'Payment Voucher',
                'icon' => 'svg_icons/vendormaster.svg',
                'url' => $site_url.'procurement/Payment_voucher_controller/payment_voucher_dashboard',
                'permission' =>  $this->authorization->isSuperAdmin()
            ]
            );
            $data['sara_tiles'] = checkTilePermissions($data['sara_tiles']);



    
        $data['main_content']    = 'procurement/requisition_view_v2/console';
        $this->load->view('inc/template', $data);
    }

    public function master() {
        $data['main_content']    = 'inventory_new/master_view';
        $this->load->view('inc/template', $data);
    }

    public function requisition_management() {
        $data['avatar_id']= $this->authorization->getAvatarId();
        $data['main_content']    = 'procurement/requisition_view_v2/requisition_management';
        $this->load->view('inc/template', $data);
    }

    public function item_management() {
        $data['main_content']    = 'procurement/requisition_view_v2/item_management';
        $this->load->view('inc/template', $data);
    }

    public function indents(){
        $data["vendors"]=$this->Requisition_model_v2->getActiveVendors();
        $data["purchase_categories"]=$this->Requisition_model_v2->getProductCategories();
        $data["departments"]=$this->Requisition_model_v2->getStaffDepartments();
        $data['main_content'] = 'procurement/purchase_management/bill_of_materials/desktop_indents_dashboard';
        $this->load->view('inc/template', $data);
    }

    public function view_indent($indentId=0){
        $isValidIndent = $this->Requisition_model_v2->validateIndentId($indentId);
        if (!$isValidIndent["status"]) {
            $data['error_message'] = 'The requested indent does not exist.';
            $data['back_url'] = site_url('procurement/Requisition_controller_v2/indents');
            $data['main_content'] = 'procurement/requisition_view_v2/error_page';
            $this->load->view('inc/template', $data);
            return;
        }

        $data["vendors"] = $this->Requisition_model_v2->getActiveVendors();

        $data["canSelectQuotation"] = $this->Requisition_model_v2->canSelectQuotation($indentId);

        $data["indentId"] = $indentId;
        $data["indentName"] = $isValidIndent["name"];
        $data["indentStatus"] = $isValidIndent["status"];
        $data['main_content'] = 'procurement/purchase_management/bill_of_materials/desktop_view_indent';
        $this->load->view('inc/template', $data);
    }

    public function get_product_sub_categories(){
        $purchase_sub_categories = $this->Requisition_model_v2->get_product_sub_categories($this->input->post());
        echo json_encode($purchase_sub_categories);
        // echo "<pre>"; print_r($data["purchase_categories"]); die();
    }

    public function get_product_item(){
        $purchase_sub_categories = $this->Requisition_model_v2->get_product_item($this->input->post());
        echo json_encode($purchase_sub_categories);
        // echo "<pre>"; print_r($data["purchase_categories"]); die();
    }

    public function save_indent(){
        // echo "<pre>"; print_r($_POST); die();
        echo $this->Requisition_model_v2->save_indent($this->input->post());
    }
    public function get_indents(){
        $bill_of_materials = $this->Requisition_model_v2->get_indents($this->input->post());
        echo json_encode($bill_of_materials);
    }

    public function get_indent_items(){
        $result = $this->Requisition_model_v2->get_indent_items($this->input->post());
        echo json_encode($result);
    }

    public function remove_indent(){
        echo $this->Requisition_model_v2->remove_indent($this->input->post());
    }
    public function delete_individual_indent_items(){
        echo $this->Requisition_model_v2->delete_individual_indent_items($this->input->post());
    }

    public function send_for_dept_approval(){
        echo $this->Requisition_model_v2->send_for_dept_approval($this->input->post());
    }

    public function get_indent_history(){
        $bom_log=$this->Requisition_model_v2->get_indent_history($this->input->post());
        echo json_encode($bom_log);
    }

    public function add_items_into_indent(){
        echo $this->Requisition_model_v2->add_items_into_indent($this->input->post());
    }

    public function send_for_quotation_approval(){
        echo $this->Requisition_model_v2->send_for_quotation_approval($this->input->post());
    }

    public function make_quotation_as_selected(){
        echo $this->Requisition_model_v2->make_quotation_as_selected($this->input->post());
    }

    public function update_indent_item_qty(){
        echo $this->Requisition_model_v2->update_indent_item_qty($this->input->post());
    }
    
    public function dummy_report() {
        $data['main_content']    = 'procurement/requisition_view_v2/report_view';
        $this->load->view('inc/template', $data);
    }

    public function get_indent_approvers_list(){
        $result=$this->Requisition_model_v2->get_indent_approvers_list($this->input->post());
        echo json_encode($result);
    }

    public function get_indent_approvers(){
        $result=$this->Requisition_model_v2->get_indent_approvers($this->input->post());
        echo json_encode($result);
    }

    public function get_quotation_approvers(){
        $result=$this->Requisition_model_v2->get_quotation_approvers($this->input->post());
        echo json_encode($result);
    }

    public function get_quotation_files(){
        $result = $this->Requisition_model_v2->get_quotation_files($this->input->post());
        
        foreach($result as $key =>$val){
            $val->file_path = $this->filemanager->getFilePath($val->file_path);
        }

        echo json_encode($result);
    }

    public function approve_indent(){
        echo $this->Requisition_model_v2->approve_indent($this->input->post());
    }

    public function reject_indent(){
        echo $this->Requisition_model_v2->reject_indent($this->input->post());
    }

    public function getBudgetCategories(){
        $result=$this->Requisition_model_v2->getBudgetCategories($this->input->post());
        echo json_encode($result);
    }

    public function getBudgetDetailsForIndentInFirstLevelFinancialApproval(){
        $indentMasterId=$this->input->post("indentMasterId");
        $result=$this->Requisition_model_v2->getBudgetDetailsForIndentInFirstLevelFinancialApproval($indentMasterId);
        echo json_encode($result);
    }

    public function request_for_modification(){
        echo $this->Requisition_model_v2->request_for_modification($this->input->post());
    }

    public function get_current_indent_staus(){
        $status=$this->Requisition_model_v2->get_current_indent_staus($this->input->post());
        echo json_encode($status);
    }

    private function s3FileUpload($file,$folder='other_documents'){
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder);
    }
    public function upload_indent_qoutation(){
        $quotation_file_path = '';
        if (isset($_FILES['upload_file'])) {
            $quotation_file_path = $this->s3FileUpload($_FILES['upload_file']);
        }
        echo $this->Requisition_model_v2->upload_indent_qoutation($this->input->post(),$quotation_file_path);
    }

    public function bring_po_qoutation(){
        $file_url=$this->Requisition_model_v2->bring_po_qoutation($this->input->post());
        echo json_encode($file_url);
    }

    public function generate_indent_pdf_for_quotation(){
        $indentId = $this->input->post('indentId');
        $bom_template = $this->Requisition_model_v2->generate_indent_pdf_for_quotation($this->input->post());
        $update_html_template = $this->Requisition_model_v2->update_bom_html_template($bom_template, $indentId);

        $result = '';
        if ($update_html_template) {
            $result = $this->genearte_bom_pdf($bom_template, $indentId);
        }
        echo $result;
    }

    public function check_indent_pdf_generated_invoice(){
        $indentId = $_POST['indentId'];
        $invoice_type = $_POST['invoice_type'];
        echo $this->Requisition_model_v2->check_indent_pdf_generated_invoice($indentId, $invoice_type);
    }
    public function download_generated_bom($indentId){
        // $indentId=$this->input->post("indentId");
        $pdf_url = $this->db->select("pdf_path")->from("procurement_indents_master ")->where("id", $indentId)->get()->row()->pdf_path;
        $url = $this->filemanager->getFilePath($pdf_url);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('indent.pdf', $data, TRUE);
    }

    private function genearte_bom_pdf($html, $indentId){
        $school = CONFIG_ENV['main_folder'];
        $path = $school . '/bill_of_materials/' . uniqid() . '-' . time() . ".pdf";

        $bucket = $this->config->item('s3_bucket');

        $status = $this->Requisition_model_v2->update_bom_pdf_path($indentId, $path);
        $page = 'portrait';
        $page_size = 'a4';
        $curl = curl_init();
        $postData = urlencode($html);

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url() . 'Callback_Controller/updateBOMPdfLink';

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_USERPWD => $username . ":" . $password,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
                CURLOPT_HTTPHEADER => array(
                    "Accept: application/json",
                    "Cache-Control: no-cache",
                    "Content-Type: application/x-www-form-urlencoded",
                    "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
                ),
            )
        );

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $jsonDecode = json_decode($response, true);
        if ($jsonDecode['status'] == true) {
            echo 1;
        } else {
            echo 0;
        }
    }

    public function check_indent_approver_already_assigned(){
        echo $this->Requisition_model_v2->check_indent_approver_already_assigned($this->input->post());
    }

    public function check_indent_quotation_approver_already_assigned(){
        echo $this->Requisition_model_v2->check_indent_quotation_approver_already_assigned($this->input->post());
    }

    public function inventoty_transaction_report() {
        $data['categories'] = $this->Requisition_model_v2->getProductCategories();
        $data['years'] = $this->Requisition_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/requisition_view_v2/inventory_transaction_report';
        $this->load->view('inc/template', $data);
    }

    public function get_the_request_number() {
        $status= $this->Requisition_model_v2->get_the_request_number();
        if( sizeof($status) == 0 ) {
            echo json_encode(0);
        } else {
            echo json_encode($status[0]->id);
        }

    }

    public function get_staff_details() {
        $status= $this->Requisition_model_v2->get_staff_details();
        echo json_encode($status);
    }

    public function get_requisition_category() {
        $status= $this->Requisition_model_v2->get_requisition_category();
        echo json_encode($status);
    }

    public function get_requisition_sub_category() {
        $category_id= $_POST['category_id'];
        $status= $this->Requisition_model_v2->get_requisition_sub_category($category_id);
        echo json_encode($status);
    }

    public function get_requisition_item() {
        $sub_category_id= $_POST['sub_category_id'];
        $status= $this->Requisition_model_v2->get_requisition_item($sub_category_id);
        echo json_encode($status);
    }

    public function submit_requisition_request_form() {
        $status= $this->Requisition_model_v2->submit_requisition_request_form();
        echo json_encode($status);
    }

    // constrion for purchase order v2 in progress

    public function purchase_order_v2() {
        $data['avatar_id'] = $this->authorization->getAvatarId();
        $data['main_content'] = 'procurement/requisition_view_v2/purchase_order_v2/desktop_purchase_order';
        $this->load->view('inc/template', $data);
    }

    public function create_purchase_order_v2() {
        // $this->load->model('Internalticketing_model');
        $data['vendors'] = $this->Requisition_model_v2->getVendorsForPurchaseOrderV2();

        $data['poTypes'] = array_merge(
            $this->Requisition_model_v2->poRequestTypes(),
            [
                (object) ["category_type" => "Service Milestones"]
            ]
        );
        $data['department_list'] = $this->Requisition_model_v2->get_departments();
        $data['loggedinUserName'] = $this->authorization->getUsername();
        $data['requestNo'] =  $this->Requisition_model_v2->generateReceiptNumberForPurchaseOrderV2();
        $data['staffDetails'] = $this->Requisition_model_v2->get_staff_details();
        $data['procurementCategory'] = $this->Requisition_model_v2->getRequisitionCategoryForPurchaseOrderV2();
        $data['main_content'] = 'procurement/requisition_view_v2/purchase_order_v2/create_purchase/desktop_create_purchase_order';
        $this->load->view('inc/template', $data);
    }

    public function getRequestTypeAndVendorNameForPurchaseOrderV2() {
        $response = $this->Requisition_model_v2->getRequestTypeAndVendorNameForPurchaseOrderV2($_POST);
        echo json_encode($response);
    }

    public function getPurchaseOrders() {
        $response = $this->Requisition_model_v2->getPurchaseOrders($_POST);
        echo json_encode($response);
    }

    public function createNewPurchaseOrderv2() {
        $status= $this->Requisition_model_v2->createNewPurchaseOrderv2($_POST);
        echo json_encode($status);
    }

    public function addSingleMilestonesInPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->addSingleMilestonesInPurchaseOrderV2($_POST);
        echo json_encode($result);
    }

    public function updateSingleMilestonesInPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->updateSingleMilestonesInPurchaseOrderV2($_POST);
        echo json_encode($result);
    }

    public function removeMilestoneFromPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->removeMilestoneFromPurchaseOrderV2($_POST);
        echo json_encode($result);
    }

    private function handleFileUploads($folder) {
        $uploadedFiles = [];
        if (isset($_FILES) && !empty($_FILES)) {
            foreach ($_FILES as $file) {
                foreach ($file['name'] as $key => $value) {
                    $uploadFile = ['name' => $file['name'][$key], 'tmp_name' => $file['tmp_name'][$key]];
                    $uploadResult = $this->s3FileUpload($uploadFile, $folder);

                    if (!empty($uploadResult['file_name'])) {
                        $uploadedFiles[] = [
                            'url' => $uploadResult['file_name'],
                            'name' => $file['name'][$key],
                            'size' => $file['size'][$key]
                        ];
                    }
                }
            }
        }
        return $uploadedFiles;
    }

    public function submitAdditionalDetailsInPurchaseOrderV2(){
        $uploadedFiles = $this->handleFileUploads('purchase_order_documents');
        $postData = !empty($uploadedFiles) ? array_merge($_POST, ['uploaded_files' => $uploadedFiles]) : $_POST;
        $uploadedDocs = $this->Requisition_model_v2->submitAdditionalDetailsInPurchaseOrderV2($postData);
        echo json_encode($uploadedDocs);
    }

     public function removeUploadedDocsPOV2(){
        $uploadedDocs = $this->Requisition_model_v2->removeUploadedDocsPOV2($_POST);
        echo json_encode($uploadedDocs);
     }

    public function getPurchaseOrderApprovers(){
        $approvers = $this->Requisition_model_v2->getPurchaseOrderApprovers($_POST);
        echo json_encode($approvers);
    }

    public function getAddedMilestoneAndTotalPaymentDetails(){
        $milestones = $this->Requisition_model_v2->getAddedMilestoneAndTotalPaymentDetails($_POST);
        echo json_encode($milestones);
    }

    public function submitPaymentDetails(){
        $milestones = $this->Requisition_model_v2->submitPaymentDetails($_POST);
        echo json_encode($milestones);
    }

    public function saveFinalSubmitStatus(){
        $status = $this->Requisition_model_v2->saveFinalSubmitStatus($_POST);
        echo json_encode($status);
    }

    public function view_purchase_order($poMasterId = 0, $sourceType = 'direct', $sourceTypeId = 0) {
        $data['poMasterId'] = $poMasterId;

        // validate po number
        $purchaseOrderValidate=$this->Requisition_model_v2->validatePurchaseOrderNumber($poMasterId);

        if (!$purchaseOrderValidate["status"]) {
            $data['error_message'] = 'The requested purchase order does not exist.';
            $data['back_url'] = site_url('procurement/Requisition_controller_v2/purchase_order_v2');
            $data['main_content'] = 'procurement/requisition_view_v2/error_page';
            $this->load->view('inc/template', $data);
            return;
        }

        $data['details'] = $this->Requisition_model_v2->getPurchaseOrderDetails($poMasterId);
        $data['history'] = $this->Requisition_model_v2->getPurchaseOrderHistory($poMasterId);
        $data['approvers'] = $this->Requisition_model_v2->getPurchaseOrderApprovers(['po_master_id' => $poMasterId, 'sourceType' => $sourceType, 'sourceTypeId' => $sourceTypeId]);
        $data['productList'] = $this->Requisition_model_v2->getPurchaseOrderProducts($poMasterId);
        $data['milestones'] = $this->Requisition_model_v2->getPurchaseOrderMilestones($poMasterId);
        $data['additionalDetails'] = $this->Requisition_model_v2->getPurchaseOrderAdditionalDetails($poMasterId);
        $data['payment'] = $this->Requisition_model_v2->getPurchaseOrderAdditionalPaymentDetails($poMasterId);
        $data['loggedInUser'] = $this->authorization->getAvatarStakeHolderId();
        $data['procurementCategory'] = $this->Requisition_model_v2->get_requisition_category();
        $data['purchaseCategoryId'] = $this->Requisition_model_v2->getPurchaseCategoryId($poMasterId);

        $data['main_content'] = 'procurement/requisition_view_v2/purchase_order_v2/view_purchase/desktop_view_purchase_order';
        $this->load->view('inc/template', $data);
    }

    public function submitPurchaseApprovalv2(){
        $status = $this->Requisition_model_v2->submitPurchaseApprovalv2($_POST);
        echo json_encode($status);
    }

    function handleMilestoneAction(){
        $status = $this->Requisition_model_v2->handleMilestoneAction($_POST);
        echo json_encode($status);
    }

    function getMilestoneDetails(){
        $details = $this->Requisition_model_v2->getMilestoneDetails($_POST);
        // echo "<pre>"; print_r($details); die();
        echo json_encode($details);
    }

    public function addEditMilestone(){
        $status = $this->Requisition_model_v2->addEditMilestone($_POST);
        echo json_encode($status);
    }

    public function deletePurchaseItem(){
        $status = $this->Requisition_model_v2->deletePurchaseItem($_POST);
        echo json_encode($status);
    }

    public function getPurchaseItemDetails(){
        $details = $this->Requisition_model_v2->getPurchaseItemDetails($_POST);
        echo json_encode($details);
    }

    public function addEditItem(){
        $status = $this->Requisition_model_v2->addEditItem($_POST);
        echo json_encode($status);
    }

    public function getCurrentPODetailsForStep1(){
        $result = $this->Requisition_model_v2->getCurrentPODetailsForStep1($_POST);
        echo json_encode($result);
    }

    public function addSingleItemToPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->addSingleItemToPurchaseOrderV2($_POST);
        echo json_encode($result);
    }
    // constrion for purchase order v2 ends here

    public function updateSingleItemToPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->updateSingleItemToPurchaseOrderV2($_POST);
        echo json_encode($result);
    }
    // constrion for purchase order v2 ends here
    public function removeSingleItemFromPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->removeSingleItemFromPurchaseOrderV2($_POST);
        echo json_encode($result);
    }
    public function cancelPurchaseOrderV2(){
        $result = $this->Requisition_model_v2->cancelPurchaseOrderV2($_POST);
        echo json_encode($result);
    }
    public function updatePurchaseOrderStatusInV2(){
        $status = $this->Requisition_model_v2->updatePurchaseOrderStatusInV2($_POST);
        echo json_encode($status);
    }

    function updateTermsAndConditions(){
        $poMasterId = $this->input->post('poMasterId');
        $terms = $this->input->post('terms_and_conditions');

        if ($poMasterId && $terms !== null) {
            $response = $this->Requisition_model_v2->updateTermsAndConditions($poMasterId, $terms);
        }else{
            $response = ['status' => false, 'message' => 'Failed to update Terms and Conditions.'];
        }
        echo json_encode($response);
    }

    public function add_new_requisition_request_form() {
        $data['vendors']= $this->Requisition_model_v2->get_vendors();
        // $this->load->model('Internalticketing_model');
        $data['department_list'] = $this->Requisition_model_v2->get_departments();
        $data['main_content'] = 'procurement/requisition_view_v2/requisition_request_form';
        $this->load->view('inc/template', $data);
    }

    public function generate_purchase_order(){
        $purchase_order_id = $this->input->post('currentRequisitionId');
        // echo '<pre>HIHIHHI '; print_r($this->settings->getSetting('school_short_name')); die(); exit();
        if($this->settings->getSetting('school_short_name') == 'iisb') {
            $purchase_order_template =  $this->Requisition_model_v2->generate_purchase_order($this->input->post());
        } else {
            $purchase_order_template =  $this->Requisition_model_v2->generate_purchase_order_diya_school($this->input->post());
        }
        
        $update = $this->Requisition_model_v2->update_html_purchase_order($purchase_order_template, $purchase_order_id);
        $result = '';
        if ($update) {
          $result =  $this->genearte_po_pdf($purchase_order_template, $purchase_order_id);
        //   $this->download_purchase_order($purchase_order_id);
        }
        echo $result;
    }

    public function download_purchase_order($requisition_id){
        $pdf_url=$this->db->select("pdf_path")->from("procurement_requisition")->where("id",$requisition_id)->get()->row();
        if(!empty($pdf_url)) {
            $pdf_url= $pdf_url->pdf_path;
        } else {
            $pdf_url= '';
        }
        $url = $this->filemanager->getFilePath($pdf_url);
        // echo "<pre>"; print_r($url); die();
		$data = file_get_contents($url);
		$this->load->helper('download');
		force_download('purchase_order.pdf', $data, TRUE);
    }

    public function get_all_requisition_requests() {
        $status= $this->Requisition_model_v2->get_all_requisition_requests($_POST['short_long'], $_POST['procurement_requisition_id']);
        echo json_encode($status);
    }

    public function get_po_edit_data() {
        $data['vendors'] = $this->Requisition_model_v2->get_vendors();
        // $this->load->model('Internalticketing_model');
        $data['department_list'] = $this->Requisition_model_v2->get_departments();
        
        $data['edit_data']= $this->Requisition_model_v2->get_po_edit_data($_POST);
        echo json_encode($data);
    }

    public function save_po_edit_changes(){
        echo $this->Requisition_model_v2->save_po_edit_changes($_POST);
    }

    public function add_item_to_po(){
        echo $this->Requisition_model_v2->add_item_to_po($_POST);
    }

    public function get_po_item_data(){
        $status = $this->Requisition_model_v2->get_po_item_data($_POST);
        echo json_encode($status);
    }

    public function save_po_item_edit_changes(){
        echo $this->Requisition_model_v2->save_po_item_edit_changes($_POST);
    }

    public function get_po_history(){
        $result = $this->Requisition_model_v2->get_po_history($_POST);        
        echo json_encode($result);
    }

    public function get_previous_po_status(){
        $result= $this->Requisition_model_v2->get_previous_po_status($_POST);
        echo json_encode($result);
    }

    public function change_po_status(){
        echo $this->Requisition_model_v2->change_po_status($_POST);
    }

    public function check_purchase_order_pdf_generated_invoice(){
        $requisition_id = $_POST['requisition_id'];
        $invoice_type = $_POST['invoice_type'];
        echo $this->Requisition_model_v2->check_purchase_order_pdf_generated_invoice($requisition_id, $invoice_type);
    }

    function force_download_document($procurement_requisition_documents_id) {
        
        $this->load->library('filemanager');
        
        $link = $this->Requisition_model_v2->get_document_url($procurement_requisition_documents_id);
        $document_name= $link->document_type;
        $url = $this->filemanager->getFilePath($link->document_url);
        // getting extension
        $extension= explode('/', $url);
        $extension= explode('.', end($extension))[1];

        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download("$document_name.$extension", $data, TRUE); 
    }
    public function get_all_items() {
        $status= $this->Requisition_model_v2->get_all_items();
        echo json_encode($status);
    }

    private function genearte_po_pdf($html, $purchase_order_id)
    {
        $school = CONFIG_ENV['main_folder'];
        $path = $school . '/purchase_orders/' . uniqid() . '-' . time() . ".pdf";

        $bucket = $this->config->item('s3_bucket');

        $status = $this->Requisition_model_v2->update_pdf_path_purchase_order($purchase_order_id, $path); 
        $page = 'portrait';
        $page_size = 'a4';
        $curl = curl_init();
        $postData = urlencode($html);

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url() . 'Callback_Controller/updatePurchasePdfLink';

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=" . $path . "&bucket=" . $bucket . "&page=" . $page . "&page_size=" . $page_size . "&data=" . $postData . "&return_url=" . $return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        )
        );

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        $jsonDecode = json_decode($response, true);
        if (is_array($jsonDecode) && isset($jsonDecode['status']) && $jsonDecode['status'] === true) {
            echo 1;
        } else {
            echo 0;
        }
    }

    public function deactivate_po(){
        echo $this->Requisition_model_v2->deactivate_po($_POST);
    }

    public function remove_po_item(){
        echo $this->Requisition_model_v2->remove_po_item($_POST);
    }

    
    public function procurement_sales_year() {
        $data['salesYear']= $this->Requisition_model_v2->get_all_salesYear();
        $data['main_content'] = 'procurement/requisition_view_v2/procurement_sales_year';
        $this->load->view('inc/template', $data);
    }

    public function add_sales_year(){
        echo $this->Requisition_model_v2->add_sales_year($_POST);
    }

    public function get_all_invoice_sales_year_wise(){
        echo json_encode($this->Requisition_model_v2->get_all_invoice_sales_year_wise($_POST['sales_year_id']));
    }

    public function close_item_level_invoice() {
        echo json_encode($this->Requisition_model_v2->close_item_level_invoice());
    }

    public function activate_sales_year() {
        echo json_encode($this->Requisition_model_v2->activate_sales_year());
    }

    public function onclick_update_closing_balance_to_the_closed_year() {
        echo json_encode($this->Requisition_model_v2->onclick_update_closing_balance_to_the_closed_year());
    }

    public function getAvailableIndentsPoV2() {
        $indents = $this->Requisition_model_v2->getAvailableIndentsPoV2(); // Fetch indents
        echo json_encode($indents); // Return as JSON
    }

    public function getAvailableServiceContractsPoV2() {
        $serviceContracts = $this->Requisition_model_v2->getAvailableServiceContractsPoV2(); // Fetch indents
        echo json_encode($serviceContracts); // Return as JSON
    }

    public function fillDetailsToPurchaseOrderV2(){
        $status = $this->Requisition_model_v2->fillDetailsToPurchaseOrderV2($this->input->post());
        echo json_encode($status); // Return as JSON
    }

    public function getBudgetDetailsForPurchaseOrder(){
        $budgetDetails = $this->Requisition_model_v2->getBudgetDetailsForPurchaseOrder($this->input->post());
        echo json_encode($budgetDetails); // Return as JSON
    }

    // Service Delivery Challan (Start): Under Construction 👷👷👷🏗️🏗️🏗️🚧🚧🚧 🛠️🛠️🛠️
    public function service_delivery_challans(){
        $data['main_content'] = 'procurement/service_delivery_challan/desktop/dashboard/challans_dashboard';
        $this->load->view('inc/template', $data);
    }
    public function getAllServiceDeliveryChallans(){
        $status = $this->Requisition_model_v2->getAllServiceDeliveryChallans($this->input->post());
        echo json_encode($status);
    }

    public function create_service_delivery_challan(){
        $currentSDCNo = $this->Requisition_model_v2->getCurrentServiceDeliveryChallanNumber();
        $allApprovedPOs = $this->Requisition_model_v2->getAllApprovedPOs();
        
        $data["currentSDCNo"]=$currentSDCNo;
        $data["allApprovedPOs"] = json_encode($allApprovedPOs);

        $data['main_content'] = 'procurement/service_delivery_challan/desktop/create/create_challan';
        $this->load->view('inc/template', $data);
    }

    public function getItemsForSDCFromPO(){
        $itemsList = $this->Requisition_model_v2->getItemsForSDCFromPO($this->input->post());
        echo json_encode($itemsList);
    } 

    public function getMilestonesForSDCFromPO(){
        $itemsList = $this->Requisition_model_v2->getMilestonesForSDCFromPO($this->input->post());
        echo json_encode($itemsList);
    } 

    public function view_service_delivery_challan($challanId=0){
        $isValidChallan = $this->Requisition_model_v2->validateServiceDeliveryChallan($challanId);
        if (!$isValidChallan["status"]) {
            $data['error_message'] = 'The requested Service Delivery Challan does not exist.';
            $data['back_url'] = site_url('procurement/Requisition_controller_v2/service_delivery_challans');
            $data['main_content'] = 'procurement/requisition_view_v2/error_page';
            $this->load->view('inc/template', $data);
            return;
        }

        $data["challanId"] = $challanId;
        $data["challanName"] = $isValidChallan["sdc_number"];
        
        $data['main_content'] = 'procurement/service_delivery_challan/desktop/view/view_challan';
        $this->load->view('inc/template', $data);
    }

    public function saveServiceDeliveryChallan(){
        echo $this->Requisition_model_v2->saveServiceDeliveryChallan($this->input->post());
    }

    public function fetchServiceDeliveryChallanDetails(){
        $challanDetails=$this->Requisition_model_v2->fetchServiceDeliveryChallanDetails($this->input->post());
        echo json_encode($challanDetails);
    }
    
    // Service Delivery Challan (End): Under Construction 👷👷👷🏗️🏗️🏗️🚧🚧🚧 🛠️🛠️🛠️

}
?>