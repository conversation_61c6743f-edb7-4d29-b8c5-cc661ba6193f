<div class="payment-failure-container">
    <div class="failure-illustration">
        
    <?php $this->load->view('svg_icons/application_payment_failed.svg') ?>
    </div>

    <h1 class="failure-title">Your Payment Failed!</h1>
    <p class="failure-message">Your transaction has failed due to some technical error. Please try again</p>

    <div class="action-buttons">
        <a class="btn home-btn" href="<?php echo site_url('admissions/home') ?>" onclick="goto_my_application()">Go to Home Page</a>
        <button class="btn retry-btn">Try Again</button>
    </div>
</div>

<style>
.payment-failure-container {
    max-width: 400px;
    margin: 40px auto;
    text-align: center;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.failure-illustration {
    position: relative;
    height: 300px;
    margin: 35px 0;
}

.phone-mockup {
    position: absolute;
    right: 0;
    width: 200px;
    height: 400px;
    border: 3px solid #000;
    border-radius: 30px;
    background: white;
    padding: 20px;
}

.alert-icon {
    width: 40px;
    height: 40px;
    background: #6c63ff;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    line-height: 40px;
    margin: 20px auto;
}

.content-lines .line {
    height: 10px;
    background: #f0f0f0;
    margin: 10px 0;
    border-radius: 5px;
}

.character {
    position: absolute;
    left: 0;
    bottom: 50px;
    width: 150px;
}

.failure-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 15px;
}

.failure-message {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.home-btn {
    background-color: #ffa500;
    color: white;
    border-radius: 10px;
}

.home-btn:hover {
    background-color: #ff9000;
}

.retry-btn {
    background-color: #28a745;
    color: white;
    border-radius: 10px;
}

.retry-btn:hover {
    background-color: #218838;
}

@media (max-width: 480px) {
    .payment-failure-container {
        margin: 20px;
    }

    .failure-illustration {
        height: 200px;
    }

    .phone-mockup {
        width: 150px;
        height: 300px;
    }

    .character {
        width: 100px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}
</style>