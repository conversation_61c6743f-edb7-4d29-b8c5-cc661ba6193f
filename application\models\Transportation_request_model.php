<?php
	defined('BASEPATH') OR exit('No direct script access allowed');
            
	class Transportation_request_model extends CI_Model {
	        
    private $yearId;
    private $current_branch;
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function getclass() {
        $this->db_readonly->select('c.id,c.class_name');
        $this->db_readonly->from('class c');
        $this->db_readonly->order_by('c.display_order, c.id');
        $this->db_readonly->where('acad_year_id',$this->yearId);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->where('c.is_placeholder!=',1);
        return $this->db_readonly->get()->result();
    }

    public function get_fee_Stop_list(){
        return $this->db->order_by('name','asc')->get('feev2_stops')->result();
    }

    public function get_fee_km_list(){
		return $this->db->get('feev2_km')->result();
	}

    public function get_transportation_request_data($input){
        $this->db_readonly->select('sa.id')
        ->from('student_admission sa')
        ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
        ->join('class c', "sy.class_id=c.id");
        if($input['filter_route'] != ''){
            $this->db_readonly->join('feev2_stops fs','sy.stop=fs.id');
            $this->db_readonly->where('fs.route',$input['filter_route']);
        }
        $this->db_readonly->where('sa.admission_status',2)
        ->where("sy.promotion_status!=",4)
        ->order_by('sa.first_name')
        ->order_by('sy.class_id');  
        if ($input['classId']) {
          $this->db_readonly->where_in('sy.class_id',$input['classId']);
        }
        if ($input['stops']) {
          $this->db_readonly->where('sy.stop',$input['stops']);
        }
        if ($input['submitted_status'] == 'submitted') {
            $this->db_readonly->group_start()
                ->where("sy.transport_details_created_on IS NOT NULL")
                ->or_where("sy.transport_details_admin_side_edited_on IS NOT NULL")
            ->group_end();
        } else if ($input['submitted_status'] == 'not_submitted') {
            $this->db_readonly->where("sy.transport_details_created_on IS NULL");
            $this->db_readonly->where("sy.transport_details_admin_side_edited_on IS NULL");
        }
        if ($input['approved_status']) {
            $this->db_readonly->where("sy.transport_request_status",$input['approved_status']);
        }
        $stdResult = $this->db_readonly->get()->result();
        $student_ids = [];
        foreach ($stdResult as $key => $val) {
          array_push($student_ids, $val->id);
        }
        return $student_ids;
    
    }

    public function get_transportation_stu_details($stud_ids){
        $address_type = $this->settings->getSetting('transportation_request_home_address_type');
        if(empty($address_type)){
            $address_type = 0;
        }
        $address = "";

        $address = ", CONCAT(
            IFNULL(ad.Address_line1, ''), ' ',
            IFNULL(ad.Address_line2, ''), ' ',
            IFNULL(ad.area, ''), ' ',
            IFNULL(ad.district, ''), ' ',
            IFNULL(ad.state, ''), ' ',
            IFNULL(ad.country, ''), ' ',
            IFNULL(ad.pin_code, '')
        ) AS student_home_address";

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }

        $this->db_readonly->select("sa.enrollment_number,
            CONCAT(IFNULL(c.class_name, ''), ' ', IFNULL(cs.section_name, '')) AS classSection,
            $std_name,
            IFNULL(sy.has_transport_km, '') AS has_transport_km,
            sy.stop,
            IFNULL(sy.pickup_mode, '') AS pickup_mode,
            CONCAT(IFNULL(p.first_name, ''), ' ', IFNULL(p.last_name, '')) AS fName,
            CONCAT(IFNULL(p1.first_name, ''), ' ', IFNULL(p1.last_name, '')) AS mName,
            IFNULL(p.mobile_no, '') AS father_mobile_no,
            sa.id AS stdId,
            IFNULL(p1.mobile_no, '') AS mother_mobile_no,
            IFNULL(p1.email, '') AS mother_email_id,
            IFNULL(p.email, '') AS father_email_id,
            IFNULL(DATE_FORMAT(sy.transport_details_created_on, '%d-%M-%Y'), '') AS transport_details_created_on,
            IFNULL(DATE_FORMAT(sy.transport_details_admin_side_edited_on, '%d-%M-%Y'), '') AS transport_details_admin_side_edited_on,
            IFNULL(sy.nearest_land_mark, '') AS nearest_land_mark,
            IFNULL(sy.transport_mode, '') AS transport_mode,
            IFNULL(sy.transportation_additional_details, '') AS transportation_additional_details,
            CASE 
                WHEN a.avatar_type = 2 THEN CONCAT(IFNULL(pr.first_name, ''), ' ', IFNULL(pr.last_name, '')) 
                WHEN a.avatar_type = 4 THEN CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) 
                ELSE '' 
            END AS created_by,
            CONCAT(IFNULL(sm1.first_name, ''), ' ', IFNULL(sm1.last_name, '')) as edited_by,
            IFNULL(sy.transport_request_status, '') AS transport_request_status,ifnull(google_maps_link,'-') as google_maps_link".
            $address
        )->from('student_admission sa')
        ->join('student_year sy', "sa.id = sy.student_admission_id")
        ->join('avatar a', 'sy.transport_details_created_by = a.id', 'left')
        ->join('parent pr', 'a.stakeholder_id = pr.id AND a.avatar_type = 2', 'left')
        ->join('staff_master sm', 'a.stakeholder_id = sm.id AND a.avatar_type = 4', 'left')
        ->join('avatar a1', 'sy.transport_details_admin_side_edited_by = a1.id', 'left')
        ->join('staff_master sm1', 'a1.stakeholder_id = sm1.id AND a1.avatar_type = 4', 'left')
        ->join('class c', "sy.class_id = c.id")
        ->join('class_section cs', 'sy.class_section_id = cs.id', 'left')
        ->join("student_relation sr", "sr.std_id = sa.id AND sr.relation_type = 'Father'")
        ->join("student_relation sr1", "sr1.std_id = sa.id AND sr1.relation_type = 'Mother'")
        ->join("parent p", "p.id = sr.relation_id")
        ->join("parent p1", "p1.id = sr1.relation_id")
        ->where('sy.acad_year_id', $this->yearId)
        ->where("sy.promotion_status !=", 'JOINED')
        ->where_in('sa.id', $stud_ids)
        ->order_by('c.id')
        ->order_by('cs.id')
        ->order_by('sa.first_name');
        $this->db_readonly->join('address_info ad', "sa.id = ad.stakeholder_id AND ad.avatar_type = 1 AND ad.address_type = $address_type", 'left');
        $stdDetails = $this->db_readonly->get()->result();
        return $stdDetails; 
    }

    public function get_assigned_transport($studentId){

            $this->db->select("ifnull(stop,'') as stop, ifnull(drop_stop,'') as drop_stop, ifnull(pickup_mode,'') pickup_mode, ifnull(has_transport_km,'') as has_transport_km,ifnull(sy.transport_mode,'') as transport_mode,ifnull(sy.transportation_additional_details,'') as transportation_additional_details,ifnull(sy.transport_required,'') as transport_required,ifnull(date_format(sy.transport_details_created_on,'%d-%M-%Y'),'') as transport_details_created_on,ifnull(sy.nearest_land_mark,'') as nearest_land_mark,ifnull(sy.transport_request_status,'-') as transport_request_status,date_format(sy.transport_request_approved_on,'%d-%M-%Y') as transport_request_approved_on,CASE WHEN a.avatar_type = 2 THEN concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) when a.avatar_type=4 THEN concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) ELSE '' END AS created_by, concat(ifnull(sm1.first_name,''),' ',ifnull(sm1.last_name,'')) as transport_request_approved_by,ifnull(sy.transportation_distance,'') as transportation_distance,ifnull(google_maps_link,'') as google_maps_link,ifnull(date_format(sy.transport_details_admin_side_edited_on,'%d-%M-%Y'),'') as edited_on,concat(ifnull(sm2.first_name,''),' ',ifnull(sm2.last_name,'')) as edited_by");
            $this->db->from('student_year sy');
            $this->db->join('student_admission sa','sy.student_admission_id=sa.id');
            $this->db->join('avatar a','sy.transport_details_created_by=a.id','left');
            $this->db->join('parent p','a.stakeholder_id=p.id and a.avatar_type=2','left');
        $this->db->join('staff_master sm','a.stakeholder_id=sm.id and a.avatar_type=4','left');
        $this->db->join('staff_master sm1','sy.transport_request_approved_by=sm1.id','left');
        $this->db->join('avatar a1','sy.transport_details_admin_side_edited_by=a1.id','left');
        $this->db->join('staff_master sm2','a1.stakeholder_id=sm2.id and a1.avatar_type=4','left');
            $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
            $this->db->where('student_admission_id', $studentId);
            $result =  $this->db->get()->row();
           
            $result->pickup = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
            ->from('feev2_stops fs')
            ->where('fs.id',$result->stop)
            ->join('feev2_km fk','fs.kilometer=fk.id')
            ->get()->row();
            
            $result->drop = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
            ->from('feev2_stops fs')
            ->where('fs.id',$result->drop_stop)
            ->join('feev2_km fk','fs.kilometer=fk.id')
            ->get()->row();
            return $result;
        }

        public function update_transport_Details(){
            $transportStageKm = '';
                if(isset( $_POST['kilometer'])){
                    if (!empty($this->input->post('fee_applied_for'))) {
                        $transportStageKm = $this->input->post('fee_applied_for');
                    }else{
                        $transportStageKm = $this->input->post('kilometer');
                    }
                }
                
                $transportation_mode = isset($_POST['transportation_mode']) ? $_POST['transportation_mode'] : '';
                $transport_addition_details = isset($_POST['transport_addition_details']) ? $_POST['transport_addition_details'] : '';
                if($this->input->post('transport') == 1){
                    $transportation_mode = 'School Bus';
                    $transport_addition_details = ''; 
                }
            $transport = $this->input->post('transport');
                $tData = array(
                    'stop'=> ($transport == 1) ? $this->input->post('stops') : '',
                    'has_transport_km'=>($transport == 1) ? $transportStageKm : '',
                    'pickup_mode'=>(isset($_POST['pickup_mode']) && ($transport == 1) ) ? $_POST['pickup_mode'] : '',
                    'drop_stop'=>(isset($_POST['drop_stop']) && ($transport == 1)) ? $_POST['drop_stop'] : '',
                    'transport_mode'=>$transportation_mode,
                    'transportation_additional_details'=>($transport == 0) ? $transport_addition_details : '',
                    'transport_details_admin_side_edited_on'=>$this->Kolkata_datetime(),
                    'transport_details_admin_side_edited_by'=>$this->authorization->getAvatarId(),
                    'transport_required'=>$this->input->post('transport'),
                    'nearest_land_mark' => (isset($_POST['nearest_land_mark']) && ($transport == 1)) ? $_POST['nearest_land_mark'] : '',
                    'google_maps_link' => (isset($_POST['google_maps_link'])) ? $_POST['google_maps_link'] : '',
                    'transport_request_status' => 'Pending'
                );
                    // echo '<pre>';print_r($tData);die();
                $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
                $this->db->where('student_admission_id', $_POST['staff_student_id']);
                return $this->db->update('student_year', $tData); 
          }

          public function approval_confirmSubmit($input){
            $previous_status = $this->db->select('transport_request_status')->from('student_year')->where('student_admission_id',$input['staff_stud_id_approve'])->where('acad_year_id',$this->yearId)->get()->row()->transport_request_status;
            $edit_history = array(
                'student_id' =>$input['staff_stud_id_approve'],
                'old_data' => 'Transportation Request Status : '.$previous_status,
                'new_data' => 'Transportation Request Status : '.$input['approve_reject'],
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' =>$this->Kolkata_datetime(),
                'source' => 'staff'
            );

            $this->db->insert('student_edit_history',$edit_history);

            $data = array(
              'transport_request_status' => $input['approve_reject'],
              'transport_request_approved_by' =>$this->authorization->getAvatarStakeHolderId(),
              'transport_request_approved_on' => $this->Kolkata_datetime(),
            );
            $this->db->where('student_admission_id',$input['staff_stud_id_approve']);
            $this->db->where('acad_year_id',$this->yearId);
            return $this->db->update('student_year',$data);
        }
        
        public function get_staff_transportation_request_data($input) {
            $this->db_readonly->select("
                sm.id as staff_id,
                CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name, 
                sm.employee_code, fs.id, fs.name, fs.route,
                CONCAT(IFNULL(sm2.first_name, ''), ' ', IFNULL(sm2.last_name, '')) AS created_by,
                DATE_FORMAT(smtr.created_on, '%d-%M-%Y') as created_on,
                smtr.status as requested_status, smtr.transportation_mode,
                smtr.transportation_additional_details, smtr.nearest_land_mark,
                CONCAT(IFNULL(sm3.first_name, ''), ' ', IFNULL(sm3.last_name, '')) AS approve_by,
                concat(ifnull(sm4.first_name,''),' ',ifnull(sm4.last_name,'')) as edited_by,DATE_FORMAT(smtr.transport_details_admin_side_edited_on, '%d-%M-%Y') as edited_on
            ")
            ->from('staff_master sm')
            ->join('staff_master_transport_requests smtr', 'sm.id = smtr.staff_id AND smtr.acad_year_id = ' . $this->acad_year->getAcadYearId(), 'left')
            ->join('staff_master sm2', 'smtr.created_by = sm2.id', 'left')
            ->join('staff_master sm3', 'smtr.approve_by = sm3.id', 'left')
            ->join('staff_master sm4','smtr.transport_details_admin_side_edited_by=sm4.id','left')
            ->join('feev2_stops fs', 'smtr.pickup_stop_id = fs.id', 'left')
            ->where('sm.status', 2); // Active staff
        
            // Apply filters dynamically
            $filters = [
                'sm.staff_type' => $input['staff_type'] ?? null,
                'fs.route' => $input['filter_route'] ?? null,
                'fs.name' => $input['stops'] ?? null,
                'smtr.status' => $input['approved_status'] ?? null
            ];
            foreach ($filters as $key => $value) {
                if (!empty($value) && $value != -1) {
                    $this->db_readonly->where($key, $value);
                }
            }
            
            if ($input['submitted_status'] == 'submitted') {
                $this->db_readonly->group_start()
                    ->where("smtr.created_on IS NOT NULL")
                    ->or_where("smtr.transport_details_admin_side_edited_on IS NOT NULL")
                ->group_end();
            } else if ($input['submitted_status'] == 'not_submitted') {
                $this->db_readonly->where("smtr.created_on IS NULL");
                $this->db_readonly->where("smtr.transport_details_admin_side_edited_on IS NULL");
            }
        
            return $this->db_readonly->get()->result_array();
        }

        public function get_staff_transport_request_data($staff_id){
            $result = $this->db_readonly->select('str.is_transport_required,str.nearest_land_mark,str.status,str.transportation_mode,transportation_additional_details, fs.route, fs.name, DATE_FORMAT(str.created_on, "%d-%M-%Y") as created_on,concat(ifnull(sm.first_name,"")," ",ifnull(sm.last_name,"")) as created_by,str.is_transport_required,""  as pickup_mode,pickup_stop_id,concat(ifnull(sm1.first_name,"")," ",ifnull(sm1.last_name,"")) as staff_name,sm1.employee_code,DATE_FORMAT(str.approved_on, "%d-%M-%Y") as approved_on,concat(ifnull(sm2.first_name,"")," ",ifnull(sm2.last_name,"")) as approve_by,sm1.contact_number,concat(ifnull(ad.Address_line1,"")," ",ifnull(ad.Address_line2,"")," ",ifnull(ad.area,"")," ",ifnull(ad.district,"")," ",ifnull(ad.state,"")," ",ifnull(ad.country,"")," ",ifnull(ad.pin_code,"")) as address,u.email,concat(ifnull(sm3.first_name,"")," ",ifnull(sm3.last_name,"")) as edited_by,DATE_FORMAT(str.transport_details_admin_side_edited_on, "%d-%M-%Y") as edited_on')
                ->from('staff_master_transport_requests str')
                ->join('feev2_stops fs', 'pickup_stop_id = fs.id','left')
                ->join('staff_master sm','str.created_by=sm.id','left')
                ->join('staff_master sm1','str.staff_id=sm1.id')
                ->join('staff_master sm2','str.approve_by=sm2.id','left')
                ->join('staff_master sm3','str.transport_details_admin_side_edited_by=sm3.id','left')
                ->join('address_info ad','sm1.id=ad.stakeholder_id and ad.avatar_type=4','left')
                ->join('avatar a','sm1.id=a.stakeholder_id and a.avatar_type=4','left')
                ->join('users u','a.user_id=u.id','left')
                ->where('staff_id', $staff_id)
                ->where('acad_year_id', $this->acad_year->getAcadYearId())
                ->get()
                ->row();
            return $result;
        }

        public function update_staff_transport_Details($input){
            $data_exist = $this->db->select('*')->from('staff_master_transport_requests')->where('staff_id',$input['staff_student_id'])->where('acad_year_id',$this->acad_year->getAcadYearId())->get()->row();
            if($input['transport'] == 'Yes'){
                $transportation_mode = 'School Bus';
                $transport_addition_details = '';
                $stop = $input['stops'];
                $nearest_land_mark = $input['nearest_land_mark'];
            }else{
                $transportation_mode = $input['transportation_mode'];
                $transport_addition_details = $input['transport_addition_details'];
                $stop = '';
                $nearest_land_mark = '';
            }
            if(empty($data_exist)){
                $data = array(
                    'staff_id' =>$input['staff_student_id'],
                    'acad_year_id' => $this->acad_year->getAcadYearId(),
                    'is_transport_required'=>$input['transport'],
                    'transportation_mode'=>$transportation_mode,
                    'transportation_additional_details'=>$transport_addition_details,
                    'pickup_stop_id'=>$stop,
                    'nearest_land_mark' => $nearest_land_mark,
                    'transport_details_admin_side_edited_by'=>$this->authorization->getAvatarStakeHolderId(),
                    'transport_details_admin_side_edited_on'=>$this->Kolkata_datetime(),
                    'status'=>'Pending'
                );
                return $this->db->insert('staff_master_transport_requests',$data);
            }else{
                $data = array(
                    'is_transport_required'=>$input['transport'],
                    'transportation_mode'=>$transportation_mode,
                    'transportation_additional_details'=>$transport_addition_details,
                    'pickup_stop_id'=>$stop,
                    'nearest_land_mark' => $nearest_land_mark,
                    'transport_details_admin_side_edited_by'=>$this->authorization->getAvatarStakeHolderId(),
                    'transport_details_admin_side_edited_on'=>$this->Kolkata_datetime(),
                    'status'=>'Pending'
                );
                $this->db->where('staff_id',$input['staff_student_id']);
                $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
                return $this->db->update('staff_master_transport_requests',$data);
            }
            
        }

        public function staff_approval_confirmSubmit($input){
            $previous_status = $this->db->select('status')->from('staff_master_transport_requests')->where('staff_id',$input['staff_stud_id_approve'])->where('acad_year_id',$this->acad_year->getAcadYearId())->get()->row()->status;
            $edit_history = array(
                'staff_id' =>$input['staff_stud_id_approve'],
                'old_data' => 'Transportation Request Status : '.$previous_status,
                'new_data' => 'Transportation Request Status : '.$input['approve_reject'],
                'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                'edited_on' =>$this->Kolkata_datetime()
            );

            $this->db->insert('staff_edit_history',$edit_history);
            $this->db->where('staff_id',$input['staff_stud_id_approve']);
            $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
            $data = array(
                'status'=>$input['approve_reject'],
                'approved_on' => $this->Kolkata_datetime(),
                'approve_by' => $this->authorization->getAvatarStakeHolderId()
            );
            return $this->db->update('staff_master_transport_requests',$data);
        }

        public function get_staff_transport_form_pdf_path($staff_id){
            $path = $this->db->select('template_path')->from('staff_master_transport_requests')->where('staff_id',$staff_id)->where('acad_year_id',$this->acad_year->getAcadYearId())->get()->row();
            if(!empty($path->template_path)){
                return $path->template_path;
            }else{
                return '';
            }
        }

        public function update_staff_transport_form_path($staff_id,$path){
            $this->db->where('staff_id',$staff_id);
            $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
            return $this->db->update('staff_master_transport_requests',array('template_path'=>$path));
        }

        public function transport_request_approved_email_content($input){
            $email_content = $this->db->select('*')->from('email_template')->where('name','student_transport_request_approval_email')->get()->row();
            if(empty($email_content)){
                return array();
            }

            $stud_data = $this->db->select('concat(ifnull(sa.first_name,"")," ",ifnull(sa.last_name,"")) as student_name,concat(ifnull(c.class_name,"")," ",ifnull(cs.section_name,"")) as class_name,sa.admission_no, sa.enrollment_number')
            ->from('student_admission sa')
            ->join('student_year sy','sa.id=sy.student_admission_id')
            ->join('class c','sy.class_id=c.id')
            ->join('class_section cs','sy.class_section_id=cs.id','left')
            ->where('sa.id',$input['staff_stud_id_approve'])
            ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
            ->get()->row();

            $parent_emails = $this->db->select('p.id,email')
            ->from('parent p')
            ->join('student_relation sr','p.id=sr.relation_id')
            ->where('student_id',$input['staff_stud_id_approve'])
            ->get()->result();

            $email_array = [];

            foreach ($parent_emails as $parent) {
                if (!empty($parent->email)) {
                    $email_array[] = $parent->email;
                }
            }

            $email_content->student_name = $stud_data->student_name;
            $email_content->class_name = $stud_data->class_name;
            $email_content->admission_no = $stud_data->admission_no;
            $email_content->enrollment_number = $stud_data->enrollment_number;
            $email_content->email_ids = $email_array;

            return (array)$email_content;
        }

        public function save_sending_email_data($emails_data){
            return $this->db->insert_batch('email_sent_to', $emails_data);
        }

        public function check_non_refund_transport_fees_status($student_id) {
            $result = new stdClass();
            $result->fee_enable = 0;
            $result->status = 'Not Paid';
            $result->terms_conditions = '';
            $blueprints = $this->db_readonly->select('fb.id,terms_conditions')
            ->from('feev2_blueprint fb')
            ->where('acad_year_id',$this->acad_year->getAcadYearId())
            ->where('is_transport_request',1)
            ->get()->row();
            if(!empty($blueprints)){
                $result->fee_enable = 1;
                $result->terms_conditions = $blueprints->terms_conditions;
                $result->trans_id = 0;
                $result->trns_status = 0;
                $result->trns_status = 0;
                $result->pdf_status = 0;
                $fee_status = $this->db_readonly->select("(case when fcs.fee_collect_status ='STARTED' then 'Paid' else 'Not Paid' end) as fee_collect_status, ft.id as trans_id, ft.status as trns_status, ft.pdf_status,fcs.id as cohort_student_id,fss.id as fee_schecdule_id")
                ->from('feev2_cohort_student fcs')
                ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
                ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id','left')
                ->where('fcs.blueprint_id',$blueprints->id)
                ->where('fcs.student_id',$student_id)
                ->get()->row();
                if(!empty($fee_status)){
                    $result->status = $fee_status->fee_collect_status;
                    $result->trans_id = !empty($fee_status->trans_id) ? $fee_status->trans_id : 0;
                    $result->trns_status = !empty($fee_status->trns_status) ? $fee_status->trns_status : 0;
                    $result->pdf_status = !empty($fee_status->pdf_status) ? $fee_status->pdf_status : 0;
                    $result->cohort_student_id = !empty($fee_status->cohort_student_id) ? $fee_status->cohort_student_id : 0;
                    $result->fee_schecdule_id = !empty($fee_status->fee_schecdule_id) ? $fee_status->fee_schecdule_id : 0;
                    $result->student_id = $student_id;
                }
            }
            return $result;
        }

        public function store_transportation_edit_history(){
            $transportation_mode = isset($_POST['transportation_mode']) ? $_POST['transportation_mode'] : '';
                $transport_addition_details = isset($_POST['transport_addition_details']) ? $_POST['transport_addition_details'] : '';
                if($this->input->post('transport') == 1){
                    $transportation_mode = 'School Bus';
                    $transport_addition_details = ''; 
                }

            $transportation_old_data = $this->db->select('transport_mode,stop,pickup_mode,drop_stop,transport_mode,transportation_additional_details,transport_required,nearest_land_mark,google_maps_link,transport_request_status')->from('student_year')->where('student_admission_id',$_POST['staff_student_id'])->where('acad_year_id',$this->acad_year->getAcadYearId())->get()->row();
                $old_val = '';
                $new_val = '';
                if ($transportation_old_data->transport_required != $this->input->post('transport')) {
                    $old_val = 'Transportation Required : ' . (($transportation_old_data->transport_required == 1) ? 'Yes ,' : (($transportation_old_data->transport_required == 0) ? 'No ,' : ''));

                    $new_val = 'Transportation Required : ' . (($this->input->post('transport') == 1) ? 'Yes ,' : (($this->input->post('transport') == 0) ? 'No ,' : ''));
                }

                if(isset($_POST['stops']) && $transportation_old_data->stop != $this->input->post('stops')){
                    $old_stop_name = '';
                    $new_stop_name = '';
                    if(!empty($transportation_old_data->stop)){
                        $old_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$transportation_old_data->stop)->get()->row()->name;
                    }
                    if(!empty($this->input->post('stops'))){
                        $new_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$this->input->post('stops'))->get()->row()->name;
                     }
                    $old_val .= ' Stop:'.$old_stop_name.' , ';
                    $new_val .= ' Stop:'.$new_stop_name. ' , ';
                }else{
                    $old_stop_name = '';
                    if(!empty($transportation_old_data->stop)){
                        $old_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$transportation_old_data->stop)->get()->row()->name;
                    }
                    $old_val .= ' Stop:'.$old_stop_name.' , ';
                    $new_val .= ' Stop: ';
                }

                if(isset($_POST['pickup_mode']) &&  $transportation_old_data->pickup_mode != $this->input->post('pickup_mode')){

                    $pickup_modes = $this->settings->getSetting('transport_mode');
                    $old_pickup_mode = '';
                    $new_pickup_mode = '';

                    if(!empty($pickup_modes) && !empty($transportation_old_data->pickup_mode)){
                        foreach($pickup_modes as $key => $v){
                            if($transportation_old_data->pickup_mode == $v->value){
                                $old_pickup_mode = $v->name;
                            }
                        }
                    }

                    if(!empty($pickup_modes) && !empty($this->input->post('pickup_mode'))){
                        foreach($pickup_modes as $key => $v){
                            if($this->input->post('pickup_mode') == $v->value){
                                $new_pickup_mode = $v->name;
                            }
                        }
                    }

                    $old_val .= ' Pickup Mode:'.$old_pickup_mode.' , ';
                    $new_val .= ' Pickup Mode:'.$new_pickup_mode.' , ';
                }else{
                    $old_pickup_mode = '';
                    if(!empty($pickup_modes) && !empty($transportation_old_data->pickup_mode)){
                        foreach($pickup_modes as $key => $v){
                            if($transportation_old_data->pickup_mode == $v->value){
                                $old_pickup_mode = $v->name;
                            }
                        }
                    }
                    $old_val .= ' Pickup Mode:'.$old_pickup_mode.' , ';
                    $new_val .= ' Pickup Mode:  ';
                }
                

                if(isset($_POST['nearest_land_mark']) && $transportation_old_data->nearest_land_mark != $this->input->post('nearest_land_mark')){
                    $old_val .= ' Nearest Location:'.$transportation_old_data->nearest_land_mark;
                    $new_val .= ' Nearest Location:'.$this->input->post('nearest_land_mark');
                }else{
                    $old_val .= ' Nearest Location:'.$transportation_old_data->nearest_land_mark;
                    $new_val .= ' Nearest Location:  ';
                   
                }

                if(isset($_POST['transportation_mode']) && $transportation_old_data->transport_mode != $transportation_mode){
                    $old_val .= ' Transport Mode : '.$transportation_old_data->transport_mode.' ,';
                    $new_val .= ' Transport Mode : '.$transportation_mode.' ,';
                }else{
                    $old_val .= ' Transport Mode : '.$transportation_old_data->transport_mode.' ,';
                    $new_val .= ' Transport Mode :  ';
                }

                if(isset($_POST['transport_addition_details']) && $transportation_old_data->transportation_additional_details != $transport_addition_details){
                    $old_val .= ' Transport Additional details : '.$transportation_old_data->transport_mode;
                    $new_val .= ' Transport Additional details : '.$transport_addition_details;
                }else{
                    $old_val .= ' Transport Additional details : '.$transportation_old_data->transport_mode;
                    $new_val .= ' Transport Additional details : ';
                }

                $edit_history = array(
                    'student_id' => $_POST['staff_student_id'],
                    'old_data' => $old_val,
                    'new_data' => $new_val,
                    'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                    'edited_on' =>$this->Kolkata_datetime(),
                    'source' => 'staff'
                );

                $this->db->insert('student_edit_history',$edit_history);
        }

        public function store_staff_transportation_edit_history(){
            $transportation_mode = isset($_POST['transportation_mode']) ? $_POST['transportation_mode'] : '';
                $transport_addition_details = isset($_POST['transport_addition_details']) ? $_POST['transport_addition_details'] : '';
                if($this->input->post('transport') == 1){
                    $transportation_mode = 'School Bus';
                    $transport_addition_details = ''; 
                }

            $transportation_old_data = $this->db->select('is_transport_required,transportation_mode,transportation_additional_details,pickup_stop_id,nearest_land_mark,status')->from('staff_master_transport_requests')->where('staff_id',$_POST['staff_student_id'])->get()->row();
            if(empty($transportation_old_data)) return;
            $old_val = '';
            $new_val = '';
                if ($transportation_old_data->is_transport_required != $this->input->post('transport')) {
                    $old_val = 'Transportation Required : ' .$transportation_old_data->is_transport_required;

                    $new_val = 'Transportation Required : ' . $this->input->post('transport');
                }

                if(isset($_POST['stops']) && $transportation_old_data->pickup_stop_id != $this->input->post('stops')){
                    $old_stop_name = '';
                    $new_stop_name = '';
                    if(!empty($transportation_old_data->stop)){
                        $old_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$transportation_old_data->pickup_stop_id)->get()->row()->name;
                    }
                    if(!empty($this->input->post('stops'))){
                        $new_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$this->input->post('stops'))->get()->row()->name;
                     }
                    $old_val .= ' Stop:'.$old_stop_name.' , ';
                    $new_val .= ' Stop:'.$new_stop_name. ' , ';
                }else{
                    $old_stop_name = '';
                    if(!empty($transportation_old_data->pickup_stop_id)){
                        $old_stop_name = $this->db->select('name')->from('feev2_stops')->where('id',$transportation_old_data->pickup_stop_id)->get()->row()->name;
                    }
                    $old_val .= ' Stop:'.$old_stop_name.' , ';
                    $new_val .= ' Stop: ';
                }

                if(isset($_POST['nearest_land_mark']) && $transportation_old_data->nearest_land_mark != $this->input->post('nearest_land_mark')){
                    $old_val .= ' Nearest Location:'.$transportation_old_data->nearest_land_mark;
                    $new_val .= ' Nearest Location:'.$this->input->post('nearest_land_mark');
                }else{
                    $old_val .= ' Nearest Location:'.$transportation_old_data->nearest_land_mark;
                    $new_val .= ' Nearest Location: ';
                }

                if(isset($_POST['transportation_mode']) && $transportation_old_data->transportation_mode != $transportation_mode){
                    $old_val .= ' Transport Mode : '.$transportation_old_data->transportation_mode.' ,';
                    $new_val .= ' Transport Mode : '.$transportation_mode.' ,';
                }else{
                    $old_val .= ' Transport Mode : '.$transportation_old_data->transportation_mode.' ,';
                    $new_val .= ' Transport Mode :  ';
                }

                if(isset($_POST['transport_addition_details']) && $transportation_old_data->transportation_additional_details != $transport_addition_details){
                    $old_val .= ' Transport Additional details : '.$transportation_old_data->transportation_additional_details;
                    $new_val .= ' Transport Additional details : '.$transport_addition_details;
                }else{
                    $old_val .= ' Transport Additional details : '.$transportation_old_data->transportation_additional_details;
                    $new_val .= ' Transport Additional details ';
                }

                $edit_history = array(
                    'staff_id' => $_POST['staff_student_id'],
                    'old_data' => $old_val,
                    'new_data' => $new_val,
                    'edited_by' => $this->authorization->getAvatarStakeHolderId(),
                    'edited_on' =>$this->Kolkata_datetime()
                );

                $this->db->insert('staff_edit_history',$edit_history);
        }
}