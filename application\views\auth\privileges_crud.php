<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">User Management</a></li>    
  <li><a href="<?php echo site_url('roles/index') ?>">Roles</a></li>
  <li>Assign Staff and Privileges to Role</li>
</ul>

<form enctype="multipart/form-data" method="post" id="role-form" action="<?php echo site_url('roles/submit_staff_and_privileges_to_roles/'.$role_id);?>" data-parsley-validate="" class="form-horizontal" >
  <div class="col-md-12">
    <div class="card cd_border" style="padding-bottom:2px;">
      <div class="card-header panel_heading_new_style_staff_border_v2">
        <h3 class="card-title panel_title_new_style_staff mb-2 mr-3 mt-2">
          <a class="back_anchor" href="<?php echo site_url('roles/index');?>">
            <span class="fa fa-arrow-left"></span>
          </a> 
          Assign Staff to <strong><?php echo $role_name; ?></strong>
        </h3>
        <br>
        <div>
          <div class="col-md-6">
            <div class="form-group">
              <div class="row">
                <label class="control-label col-md-6">Pre-defined permission sets</label>
                <div class="col-md-6">
                  <select class="form-control" id="pre-defined">
                    <option value="">Select Role</option>
                    <?php foreach ($predefined as $name) {
                      echo '<option value="'.$name.'">'.$name.'</option>';
                    } ?>
                  </select>
                </div>  
              </div>
            </div>
          </div>
          <div class="col-md-6">
            &nbsp;
            <button type="button" onclick="getPrivileges('reset')" class="btn btn-primary">Reset to default</button>&nbsp;&nbsp; OR &nbsp;&nbsp;
            <button type="button" onclick="getPrivileges('add')" class="btn btn-primary">Add missing</button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="card shadow-sm mb-4">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Assign Staff</h5>
            <button class="btn btn-sm btn-light" type="button" id="toggleAssignStaffBtn">
              <i class="fa fa-chevron-up" id="toggleAssignStaffIcon"></i>
            </button>
          </div>

          <div id="assignStaffBody" class="card-body">
            <div class="row mb-3 align-items-center">
              <label for="staff_type" class="col-auto col-form-label fw-bold">Filter by Staff Type:</label>
              <div class="col-auto">
                <select class="form-control form-select form-select-sm" name="staff_type" onchange="change_status_staff_details()" id="staff_type" style="width: 200px;">
                  <option value="-1">All</option>
                  <?php foreach ($this->settings->getSetting('staff_type') as $key => $val) { ?>
                    <option value="<?php echo $key; ?>"><?php echo $val; ?></option>
                  <?php } ?>
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col-md-5">
                <label for="multi_d" class="form-label fw-bold">Available Staff</label>
                <select name="users_list" id="multi_d" class="form-control select2" size="10" multiple>
                  <?php 
                    $allStaff = array(); 
                    foreach ($staff as $staf) { 
                      $allStaff[$staf->id] = $staf->staffName; ?>
                      <option data-staff_type="<?= $staf->staff_type ?>" value="<?= $staf->id ?>"><?= $staf->staffName ?></option>
                  <?php } ?>
                </select>
              </div>

              <div class="col-md-2 d-flex flex-column justify-content-center align-items-center">
                <button type="button" id="multi_d_rightAll" class="btn btn-outline-secondary mb-2" title="Select All">
                  <i class="fa fa-angle-double-right"></i>
                </button>
                <button type="button" id="multi_d_rightSelected" class="btn btn-outline-secondary mb-2" title="Select">
                  <i class="fa fa-angle-right"></i>
                </button>
                <button type="button" id="multi_d_leftSelected" class="btn btn-outline-secondary mb-2" title="Deselect">
                  <i class="fa fa-angle-left"></i>
                </button>
                <button type="button" id="multi_d_leftAll" class="btn btn-outline-secondary mb-2" title="Remove All">
                  <i class="fa fa-angle-double-left"></i>
                </button>
              </div>

              <div class="col-md-5">
                <label for="multi_users_to_2" class="form-label fw-bold">Selected Staff</label>
                <select name="staffIdArr[]" id="multi_users_to_2" class="form-control" size="10" multiple required>
                  <?php 
                    if (!empty($staff_assinged)) {
                      foreach ($staff_assinged as $assigned) { 
                        $allStaff[$assigned->id] = $assigned->first_name; ?>
                        <option selected data-staff_type="<?= $staf->staff_type ?>" value="<?= $assigned->id ?>"><?= $assigned->first_name ?></option>
                  <?php } } ?>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="card shadow-sm mb-4">
          <div class="card-header">
            <h5 class="mb-0">Assign Privileges to Role</h5>
          </div>
          <div class="card-body" style="padding-top: 5px">
            <div class="table-responsive">
              <table class="table table-bordered align-middle mb-0" id="privilege_table">
                <thead class="thead-light">
                  <tr>
                    <th style="width: 25%;">Privilege</th>
                    <th style="width: 75%;">Assigned / Unassigned</th>
                  </tr>
                </thead>
                <tbody>
                  <?php
                  $unassigned = [];
                  $assigned = [];
                  $allPrivileges = [];
                  $is_super_admin = $this->authorization->isSuperAdmin();
                  foreach ($privData as $pId => $priv): 
                    $allocation = $priv['enable_allocation'] == 0 
                      ? '<br><span class="text-danger small">(Cannot Assign)</span>' 
                      : '';
                    $disabled = $priv['enable_allocation'] == 0 ? 'disabled' : '';
                  ?>
                    <tr>
                      <td class="fw-bold text-dark">
                        <?= $priv['p_name'] ?> <?= $allocation ?>
                      </td>
                      <td>
                        <span id="unassigned_<?php echo $pId ?>">
                          <?php foreach ($priv['unassigned'] as $psId => $psName) {
                            echo '<span ';
                            if($is_super_admin || $priv['enable_allocation'] == 1) {
                              echo 'onclick="assignPrivilege('.$pId.','.$psId.',\''.$psName.'\',\''.$priv['p_name'].'\')"';
                            }
                            echo ' class="label label-default label-form enabled">'.$psName.'&nbsp;&nbsp;<i class="fa fa-plus"></i></span>';
                            $unassigned[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                            $allPrivileges[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                          }  ?>
                        </span>
                        <span id="assigned_<?php echo $pId ?>">
                          <?php foreach ($priv['assigned'] as $psId => $psName) {
                            echo '<input type="hidden" name="privileges[]" value="'.$psId.'"><span ';
                            if($is_super_admin || $priv['enable_allocation'] == 1) {
                              echo 'onclick="unassignPrivilege('.$pId.','.$psId.',\''.$psName.'\',\''.$priv['p_name'].'\')" ';
                            }
                            echo 'class="label label-success label-form enabled">'.$psName.'&nbsp;&nbsp;<i class="fa fa-times"></i></span>';
                            $assigned[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                            $allPrivileges[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                          }  ?>
                        </span>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>

      </div>
      <div class="card-footer">
        <center>
          <button type="button" onclick="confirmSubmit()" class="btn btn-primary">Submit</button>
          <a class="btn btn-danger" href="<?php echo site_url('roles/index') ?>">Cancel</a>
        </center>
      </div>
    </div>
  </div>
</form>

<!-- <div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <form enctype="multipart/form-data" method="post" id="role-form" action="<?php echo site_url('roles/submit_staff_and_privileges_to_roles/'.$role_id);?>" data-parsley-validate="" class="form-horizontal" >
    <div class="panel-heading new-panel-heading">
      <h3 class="panel-title"></h3>
    </div>
    <div class="panel-body">
      <div class="col-md-12">
        <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <div class="row">
                  <label class="control-label col-md-6">Pre-defined permission sets</label>
                  <div class="col-md-6">
                    <select class="form-control" id="pre-defined">
                      <option value="">Select Role</option>
                      <?php foreach ($predefined as $name) {
                        echo '<option value="'.$name.'">'.$name.'</option>';
                      } ?>
                    </select>
                  </div>  
                </div>
              </div>
          </div>
          <div class="col-md-6">
            &nbsp;
            <button type="button" onclick="getPrivileges('reset')" class="btn btn-primary">Reset to default</button>&nbsp;&nbsp; OR &nbsp;&nbsp;
            <button type="button" onclick="getPrivileges('add')" class="btn btn-primary">Add missing</button>
          </div>
        </div>
      <hr>
      </div>

      <div class="panel panel-default new-panel-style_3">
        <div class="panel-heading new-panel-heading">
          <h3 class="panel-title"><strong>Staff</strong></h3>
        </div>
        <div class="panel-body">
          <div class="form-group">
            <div class="col-sm-12">                     
               <div class="row">
                <div class="col-5">
                  <label class="control-label">Staff</label>
                  <select name="users_list" id="multi_d" class="form-control users_list" size="10" multiple="multiple">
                    <?php $allStaff = array(); foreach ($staff as $key => $staf) { 
                      $allStaff[$staf->id] = $staf->staffName;
                      ?>
                        <option value="<?= $staf->id ?>"><?= $staf->staffName ?></option>
                   <?php } ?>
                  </select>
                  </div>
                <div class="col-1">
                  <div style="margin:0 0 48px;"> </div>
                    <button data-placement='top' data-toggle='tooltip' data-original-title='Select All' type="button" id="multi_d_rightAll" class="btn btn-secondary " style="margin-bottom: 10px;"><i style="font-size: 22px;" class="fa fa-angle-double-right"></i>
                    </button>
                    <button data-placement='top' data-toggle='tooltip' data-original-title='Select' type="button" id="multi_d_rightSelected" class="btn btn-secondary " style="margin-bottom: 10px;" ><i style="font-size: 22px;" class="fa fa-angle-right"></i>
                    </button>
                    <br>
                    <button data-placement='top' data-toggle='tooltip' data-original-title='Deselect' type="button" id="multi_d_leftSelected" style="margin-bottom: 10px;" class="btn btn-secondary "><i style="font-size: 22px;" class="fa fa-angle-left"></i>
                    </button> 
                    <br>
                  <button data-placement='top' data-toggle='tooltip' data-original-title='Remove All' type="button" id="multi_d_leftAll" class="btn btn-secondary " style="margin-bottom: 10px;" ><i style="font-size: 22px;" class="fa fa-angle-double-left"></i>
                  </button>
                </div>
                <div class="col-5">
                  <label class="control-label">Selected Staff</label>
                    <select required="" name="staffIdArr[]" id="multi_users_to_2"  class="form-control staffMulti" size="10" multiple="multiple">
                      <?php if (!empty($staff_assinged)) { 
                          foreach ($staff_assinged as $assigned) { 
                            $allStaff[$assigned->id] = $assigned->first_name;
                            ?>
                             <option selected="" value="<?= $assigned->id ?>"><?= $assigned->first_name ?></option>
                         <?php }
                      } ?>
                    </select>
                </div>
              </div>
          </div>
        </div>
        </div>
      </div>

      <div class="panel panel-default new-panel-style_3">
        <div class="panel-heading new-panel-heading">
          <h3 class="panel-title"><strong>Assign Privileges to Role</strong></h3>
        </div>
        <div class="panel-body" style="padding:0px;">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th style="width:15%;">Privilege</th>
                <th style="width:85%;">Assigned/Unassigned</th>
              </tr>
            </thead>
            <tbody>
              <?php
              $unassigned = array();
              $assigned = array();
              $allPrivileges = array();
              $is_super_admin = $this->authorization->isSuperAdmin();
              foreach ($privData as $pId => $priv) { 
                $allocation = '';
                $disabled = '';
                if($priv['enable_allocation'] == 0) {
                  $allocation = '<br><span class="text-danger">(Cannot Assign)</span>';
                  $disabled = 'disabled';
                }
                ?>
                <tr>
                  <td><?php echo $priv['p_name'] ?> <?php echo $allocation; ?></td>
                  <td>
                    <span id="unassigned_<?php echo $pId ?>">
                    <?php foreach ($priv['unassigned'] as $psId => $psName) {
                      echo '<span ';
                      if($is_super_admin || $priv['enable_allocation'] == 1) {
                        echo 'onclick="assignPrivilege('.$pId.','.$psId.',\''.$psName.'\',\''.$priv['p_name'].'\')"';
                      }
                      echo ' class="label label-default label-form enabled">'.$psName.'&nbsp;&nbsp;<i class="fa fa-plus"></i></span>';
                      $unassigned[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                      $allPrivileges[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                    }  ?>
                  </span>
                  <span id="assigned_<?php echo $pId ?>">
                    <?php foreach ($priv['assigned'] as $psId => $psName) {
                      echo '<input type="hidden" name="privileges[]" value="'.$psId.'"><span ';
                      if($is_super_admin || $priv['enable_allocation'] == 1) {
                        echo 'onclick="unassignPrivilege('.$pId.','.$psId.',\''.$psName.'\',\''.$priv['p_name'].'\')" ';
                      }
                      echo 'class="label label-success label-form enabled">'.$psName.'&nbsp;&nbsp;<i class="fa fa-times"></i></span>';
                      $assigned[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                      $allPrivileges[$pId][] = array('id' => $psId, 'name' => $psName, 'pName' => $priv['p_name']);
                    }  ?>
                  </span>
                  </td>
                </tr>
              <?php } ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="panel-footer"> 
        <center>
          <button type="button" onclick="confirmSubmit()" class="btn btn-primary">Submit</button>
          <a class="btn btn-danger" href="<?php echo site_url('roles/index') ?>">Cancel</a>
        </center>
    </div>
  </form>
</div> -->
<style type="text/css">
  .enabled {
    cursor: pointer;
    margin: 3px; 
  }
  .enabled:hover {
    transform: scale(1.02,1.1);
  }
  
</style>
<script type="text/javascript">
var assigned = [];
var unassigned = [];
var allPrivileges = [];
var initialUnassigned = [];
var initialAssigned = [];
var initialStaff = [];
var allStaff = [];
var newStaff = [];
$(document).ready(function(){
  assigned = JSON.parse('<?php echo json_encode($assigned); ?>');
  unassigned = JSON.parse('<?php echo json_encode($unassigned); ?>');
  allStaff = JSON.parse('<?php echo json_encode($allStaff); ?>');
  initialStaff = $("#multi_users_to_2").val();
  newStaff = initialStaff;
  for(var pId in assigned) {
    for(var i in assigned[pId]) {      
      initialAssigned.push(assigned[pId][i].id);
    }
  }
  for(var pId in unassigned) {
    for(var i in unassigned[pId]) {      
      initialUnassigned.push(unassigned[pId][i].id);
    }
  }
  allPrivileges = JSON.parse('<?php echo json_encode($allPrivileges); ?>');
  $('#privilege_table').DataTable({
    paging: true,
    lengthChange: true,
    ordering: false,
    searching: true,
    destroy: true,
    drawCallback: function() {
      // Ensure all enabled privileges are properly tracked after search/filter/page change
      $('input[name="privileges[]"]').remove();
      for(var pId in assigned) {
        for(var i in assigned[pId]) {
          $('<input>').attr({
            type: 'hidden',
            name: 'privileges[]',
            value: assigned[pId][i].id
          }).appendTo('#role-form');
        }
      }
    },
    initComplete: function() {
      // Initial setup of hidden inputs
      $('input[name="privileges[]"]').remove();
      for(var pId in assigned) {
        for(var i in assigned[pId]) {
          $('<input>').attr({
            type: 'hidden',
            name: 'privileges[]',
            value: assigned[pId][i].id
          }).appendTo('#role-form');
        }
      }
    }
  });
});

function change_status_staff_details() {
  const selectedType = document.getElementById("staff_type").value;
  const availableSelect = document.getElementById("multi_d");
  const selectedSelect = document.getElementById("multi_users_to_2");

  Array.from(availableSelect.options).forEach(option => {
    const staffType = option.getAttribute("data-staff_type");
    option.style.display = (selectedType === "-1" || staffType === selectedType) ? "block" : "none";
  });
  Array.from(selectedSelect.options).forEach(option => {
    const staffType = option.getAttribute("data-staff_type");
    option.style.display = (selectedType === "-1" || staffType === selectedType) ? "block" : "none";
  });
}

document.getElementById('toggleAssignStaffBtn').addEventListener('click', function () {
  const body = document.getElementById('assignStaffBody');
  const icon = document.getElementById('toggleAssignStaffIcon');

  if (body.style.display === 'none') {
    body.style.display = 'block';
    icon.classList.remove('fa-chevron-down');
    icon.classList.add('fa-chevron-up');
  } else {
    body.style.display = 'none';
    icon.classList.remove('fa-chevron-up');
    icon.classList.add('fa-chevron-down');
  }
});

function getDifference(current, initial, type) {
  var privs = '';
  var cs = 'label-success';
  if(type == 'remove')
    cs = 'label-default';
  for(var pId in current) {
    for(var i in current[pId]) {
      if(initial == null || initial.indexOf(current[pId][i].id) == -1) {
        privs += '<span class="label '+cs+' label-form">'+current[pId][i].pName+'-'+current[pId][i].name+'</span> ';
      }
    }
  }
  return privs;
}

function getStaffAdded() {
  var staffs = '';
  for(var i in newStaff) {
    if(initialStaff == null || initialStaff.indexOf(newStaff[i]) == -1) {
      staffs += '<span class="label label-success label-form">'+allStaff[newStaff[i]]+'</span> ';
    }
  }
  return staffs;
}

function getRemovedStaff() {
  var staffs = '';
  for(var i in initialStaff) {
    if(newStaff.indexOf(initialStaff[i]) == -1) {
      staffs += '<span class="label label-default label-form">'+allStaff[initialStaff[i]]+'</span> ';
    }
  }
  return staffs;
}

function confirmSubmit() {
  // Update hidden inputs for all enabled privileges
  $('input[name="privileges[]"]').remove();
  for(var pId in assigned) {
    for(var i in assigned[pId]) {
      $('<input>').attr({
        type: 'hidden',
        name: 'privileges[]',
        value: assigned[pId][i].id
      }).appendTo('#role-form');
    }
  }

  var assignedHtml = '<div><strong>Assigned Privileges: </strong>';
  assignedHtml += getDifference(assigned, initialAssigned, 'add')+'</div>';
  var unassignedHtml = '<div><strong>Unasigned Privileges: </strong>';
  unassignedHtml += getDifference(unassigned, initialUnassigned, 'remove')+'</div>';
  var addedStaff = '<div><strong>Added Staff: </strong>';
  addedStaff += getStaffAdded()+'</div>';
  var removedStaff = '<div><strong>Removed Staff: </strong>';
  removedStaff += getRemovedStaff()+'</div>';
  
  bootbox.confirm({
    title : "Confirm the following changes before submitting.",  
    message: assignedHtml+'<br>'+unassignedHtml+'<br>'+addedStaff+'<br>'+removedStaff,
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result) {
        $("#role-form").submit();
      }
    }
  });
}

function assignPrivilege(pId, psId, psName, pName) {
  var ps = {};
  ps.id = psId;
  ps.name = psName;
  ps.pName = pName;
  for(var i in unassigned[pId]) {
    if(unassigned[pId][i].id == psId) {
      unassigned[pId].splice(i,1);
    } 
  }
  if(assigned[pId] == undefined) {
    assigned[pId] = [];
  }
  (assigned[pId]).push(ps);
  changeInputs(pId, pName);
}

function unassignPrivilege(pId, psId, psName, pName) {
  var ps = {};
  ps.id = psId;
  ps.name = psName;
  ps.pName = pName;
  for(var i in assigned[pId]) {
    if(assigned[pId][i].id == psId) {
      assigned[pId].splice(i,1);
    } 
  }
  if(unassigned[pId] == undefined) {
    unassigned[pId] = [];
  }
  (unassigned[pId]).push(ps);
  changeInputs(pId, pName);
}

function changeInputs(pId, pName) {
  $("#unassigned_"+pId).html('');
  $("#assigned_"+pId).html('');
  var unHtml = '';
  var asHtml = '';

  for(var i in unassigned[pId]) {
    unHtml += '<span onclick="assignPrivilege('+pId+','+unassigned[pId][i].id+',\''+unassigned[pId][i].name+'\',\''+pName+'\')" class="label label-default label-form enabled">'+unassigned[pId][i].name+'&nbsp;&nbsp;<i class="fa fa-plus"></i></span>';
  }
  for(var i in assigned[pId]) {
    asHtml += '<input type="hidden" name="privileges[]" value="'+assigned[pId][i].id+'"><span onclick="unassignPrivilege('+pId+','+assigned[pId][i].id+',\''+assigned[pId][i].name+'\',\''+pName+'\')" class="label label-success label-form enabled">'+assigned[pId][i].name+'&nbsp;&nbsp;<i class="fa fa-times"></i></span>';
  }
  $("#unassigned_"+pId).html(unHtml);
  $("#assigned_"+pId).html(asHtml);
}

function ajaxCall(type, roleName) {
  $.ajax({
    url: "<?php echo site_url('roles/getDeafultPrivileges');?>",
    data: {'roleName':roleName},
    type: 'post',
    success: function(data) {
      var data = JSON.parse(data);
      var found = data.found;
      if(found == 0) {
        $(function(){
          new PNotify({
            title: 'Error',
            text: 'No pre-defined permissions to assign',
            type: 'error',
          });
        });
      } else {
        var subs = data.subs;
        if(type == 'reset')
          resetToDefault(subs);
        else 
         addMissing(subs);
      }
    },
    error: function(err) {
      console.log(err);
    }
  });
}

// function missingPrivs(subs) {
//   var unass = unassigned;
//   var missing = [];
//   for(var index in subs) {
//     if(unass[subs[index].pId] != undefined) {
//       var un = unass[subs[index].pId];
//       for(var i in un) {
//         if(un[i].id == subs[index].psId) {
//           missing.push(subs[index]);
//           break;
//         }
//       }
//     }
//   }
//   console.log(missing);
// }

function getPrivileges(type) {
  var roleName = $("#pre-defined").val();
  if(roleName == '') {
    return false;
  }
  if(type == 'add') {
    ajaxCall(type, roleName);
  } else {
    bootbox.confirm({
      title : "Confirm",  
      message: "This will reset permissions to "+roleName+" pre-defined permissions. Are you sure?",
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      callback: function (result) {
        if(result) {
          ajaxCall(type, roleName);
        }
      }
    });
  }
  /*bootbox.confirm({
    title : "Confirm",  
    message: "This will reset permissions to "+roleName+" pre-defined permissions. Are you sure?",
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result) { 
        // var options = $("#multi_permission_to_2 option");
        // $.map(options ,function(option) {
        //   option.selected = true;
        // });
        // $("#multi_perm_leftAll").click();
        // var options = $("#multi_permission option");
        // $.map(options ,function(option) {
        //   option.selected = false;
        // });
        $.ajax({
          url: "<?php //echo site_url('roles/getDeafultPrivileges');?>",
          data: {'roleName':roleName},
          type: 'post',
          success: function(data) {
            var data = JSON.parse(data);
            var found = data.found;
            if(found == 0) {
              $(function(){
                new PNotify({
                  title: 'Error',
                  text: 'No pre-defined permissions to assign',
                  type: 'error',
                });
              });
            } else {
              var subs = data.subs;
              if(type == 'reset')
                resetToDefault(subs);
              else 
                addMissing(subs);
              // var options = $("#multi_permission option");
              // $.map(options ,function(option) {
              //   if(subs.indexOf(option.value) != -1) {
              //     option.selected = true;
              //   }
              // });
              // $("#multi_pem_rightSelected").click();
            }
          },
          error: function(err) {
            console.log(err);
          }
        });
      }
    }
  });*/
}

function addMissing(subs) {
  var unass = unassigned;
  for(var index in subs) {
    if(unass[subs[index].pId] != undefined) {
      var un = unass[subs[index].pId];
      for(var i in un) {
        if(un[i].id == subs[index].psId) {
          assignPrivilege(subs[index].pId, un[i].id, un[i].name, subs[index].pName);
          break;
        }
      }
    }
  }
}

function unassignAll() {
  var pAssigned = assigned;
  assigned = [];
  for(var pId in pAssigned) {
    $('#assigned_'+pId).html('');
    for(var i in pAssigned[pId]) {
      unassignPrivilege(pId, pAssigned[pId][i].id, pAssigned[pId][i].name, pAssigned[pId][i].pName);
    }
  }
}

function resetToDefault(subs) {
  unassignAll();
  for(var i in subs) {
    assignPrivilege(subs[i].pId, subs[i].psId, subs[i].psName, subs[i].pName);
  }
}

$(function() {   
    $('#multi_d').multiselect({
        right: '#multi_d_to, #multi_users_to_2',
        rightSelected: '#multi_d_rightSelected',
        leftSelected: '#multi_d_leftSelected',
        rightAll: '#multi_d_rightAll',
        leftAll: '#multi_d_leftAll',
        moveToRight: function(Multiselect, options, event, silent, skipStack) {
            var button = $(event.currentTarget).attr('id');
            if (button == 'multi_d_rightSelected') {
                var left_options = Multiselect.left.find('option:selected');
                Multiselect.right.eq(0).append(left_options);
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                }
            } else if (button == 'multi_d_rightAll') {
                var left_options = Multiselect.left.find('option');
                Multiselect.right.eq(0).append(left_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                  Multiselect.right.eq(0).find('option').attr('selected', true);
                    Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                }
            }
            $("#multi_users_to_2 option").prop('selected',true);
            newStaff = $("#multi_users_to_2").val();
        },
        
        moveToLeft: function(Multiselect, options, event, silent, skipStack) {
            var button = $(event.currentTarget).attr('id');
            
            if (button == 'multi_d_leftSelected') {
                var right_options = Multiselect.right.eq(0).find('option:selected');
                Multiselect.left.append(right_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                }
            } else if (button == 'multi_d_leftAll') {
                var right_options = Multiselect.right.eq(0).find('option');
                Multiselect.left.append(right_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                }
            }
            $("#multi_users_to_2 option").prop('selected',true);
            newStaff = $("#multi_users_to_2").val();
        }
    });
});
</script>

<script type="text/javascript">
/*$(function() {   
    $('#multi_permission').multiselect({
        right: '#multi_d_to, #multi_permission_to_2',
        rightSelected: '#multi_pem_rightSelected',
        leftSelected: '#multi_perm_leftSelected',
        rightAll: '#multi_perm_rightAll',
        leftAll: '#multi_perm_leftAll',
        moveToRight: function(Multiselect, options, event, silent, skipStack) {
            var button = $(event.currentTarget).attr('id');
            if (button == 'multi_pem_rightSelected') {
                var left_options = Multiselect.left.find('option:selected');
                Multiselect.right.eq(0).append(left_options);
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                }
            } else if (button == 'multi_perm_rightAll') {
                var left_options = Multiselect.left.find('option');
                Multiselect.right.eq(0).append(left_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                }
            } 
        },
        
        moveToLeft: function(Multiselect, options, event, silent, skipStack) {
            var button = $(event.currentTarget).attr('id');
            
            if (button == 'multi_perm_leftSelected') {
                var right_options = Multiselect.right.eq(0).find('option:selected');
                Multiselect.left.append(right_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                }
            } else if (button == 'multi_perm_leftAll') {
                var right_options = Multiselect.right.eq(0).find('option');
                Multiselect.left.append(right_options);
                
                if ( typeof Multiselect.callbacks.sort == 'function' && !silent ) {
                    Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                }
            } 
        }
    });
});*/
</script>

<style type="text/css">
  .btn.btn-xs, .btn-group-xs > .btn{
    padding: 0px 8px;
  }
</style>