<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Daily Challan Details</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin:0">
        <div class="col-md-6 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Daily Challan Details
          </h3>
        </div>
      </div>
    </div>

    <div class="card-body">
      <form enctype="multipart/form-data" id="demo-form" action="<?php echo site_url('feesv2/reports/daily_transcation_summary');?>" class="form-horizontal" data-parsley-validate method="post">
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <label for="date" class="control-label" style = "margin-left:2px; margin-bottom:4px;">Date</label>
              <select name="date" class="form-control">
                <option value="">Select Date</option>
                <?php foreach ($allDate as $key => $date) { ?>
                  <option <?php if($date->paid_date == $cDate) echo 'selected' ?> value="<?= $date->paid_date;?>"><?= $date->paid_date;?></option>
                <?php } ?>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group" style="margin-top: 28px;">
              <input type="submit" name="search" id="search" class="btn btn-primary" value="Get Details">
            </div>
          </div>
        </div>
      </form>

      <div class="pull-right mb-3" style="margin-bottom: 10px;">
        <button class="btn btn-info" style="margin-left: 3px; border-radius: 8px !important;" onclick="printProfile()">
          <span class="fa fa-print" aria-hidden="true"></span> Print
        </button>
      </div>


      <div id="printbill">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Date</th>
              <th>Challan Amount</th>
              <th>Bank Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><?= $cDate;?></td>
              <td><?= $total_challanAmount->challanAmount ?></td>
              <td><?= $total_bankAmount->bankAmount ?></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>


<script type="text/javascript">
  $(document).ready(function(){
    $('#from_date,#to_date').datepicker({
      format: 'dd-mm-yyyy',
      todayHighlight: true,
      "autoclose": true
    });

   
  });
</script>
 
<script type="text/javascript">
function printProfile() {
  var printContent = document.getElementById('printbill').innerHTML;
  var win = window.open('', '_blank');

  win.document.open();
  win.document.write(`
    <html>
      <head>
        <title>Print Preview</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            padding: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
          }
          h3 {
            margin-bottom: 20px;
          }
        </style>
      </head>
      <body onload="window.print(); window.close();">
      <h3 style="text-align: center;">Total receipt amount and Bank Amount</h3>
        ${printContent}
      </body>
    </html>
  `);
  win.document.close();
}

</script>

<script type="text/javascript">
  function sms() {
    var date = '<?= $cDate ?>';   
    var cAmount = '<?= $total_challanAmount->challanAmount ?>';
    var bAmount = '<?= $total_bankAmount->bankAmount ?>';
     // $.ajax({
     //    url: '<?php //echo site_url('sms/dailyGenerationReport'); ?>',
     //    data: {'date': date, 'cAmount': cAmount,'bAmount':bAmount},
     //    type: "post",
     //    success: function (data) {
     //      location.reload();
     //    },
     //    error: function (err) {
     //      console.log(err);
     //    }
     //  });
  }
</script>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table {
    font-family: 'Poppins', sans-serif !important;
  }

  .card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 20px;
  }

  #printbill {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  #printbill thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }
</style>
