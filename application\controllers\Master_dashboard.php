<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Master_dashboard extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      		redirect('auth/login', 'refresh');
    	}
  	}

  	public function index() {
  		$data['currentAcadYear'] = $this->acad_year->getAcadYear();
      $site_url = site_url();
      $data['tiles'] = array(
        [
          'title' => 'School Management',
          'sub_title' => 'Manage school information',
          'icon' => 'svg_icons/school_management.svg',
          'url' => $site_url.'Master_dashboard/school_management',
          'permission' => 1
        ],
        [
          'title' => 'Academic Year',
          'sub_title' => 'Change academic year<br><strong>Current '.$data['currentAcadYear'].'</strong>',
          'icon' => 'svg_icons/calendar.svg',
          'url' => $site_url.'config/acad_year_config',
          'permission' => 1
        ],
        [
          'title' => 'Config Management',
          'sub_title' => 'Add/Edit school config',
          'icon' => 'svg_icons/config_management.svg',
          'url' => $site_url.'config',
          'permission' => 1
        ],
        [
          'title' => 'Config History',
          'sub_title' => 'School Config History',
          'icon' => 'svg_icons/config_management.svg',
          'url' => $site_url.'config/history',
          'permission' => 1
        ],
        [
          'title' => 'Log Viewer',
          'sub_title' => 'View logs',
          'icon' => 'svg_icons/logs.svg',
          'url' => $site_url.'dblogs',
          'permission' => 1
        ],
        [
          'title' => 'Report Templates',
          'sub_title' => 'Report templates for management',
          'icon' => 'svg_icons/report_templates.svg',
          'url' => $site_url.'email_controller/management_templates',
          'permission' => 1
        ],
        [
          'title' => 'Inactive Report Cards',
          'sub_title' => 'unused report cards saved on s3',
          'icon' => 'svg_icons/inactive_report.svg',
          'url' => $site_url.'Master_dashboard/unusedReportCards',
          'permission' => 1
        ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);
      $data['msm_tiles'] = array(
        [
          'title' => 'Configure Schools',
          'sub_title' => 'Configure schools',
          'icon' => 'svg_icons/school.svg',
          'url' => $site_url.'Master_dashboard/msm_schools',
          'permission' => 1
        ],
        [
          'title' => 'Configure Users',
          'sub_title' => 'Configure schools',
          'icon' => 'svg_icons/configmanagement.svg',
          'url' => $site_url.'Master_dashboard/msm_users',
          'permission' => 1
        ],
        [
          'title' => 'Import Redis Sessions',
          'sub_title' => 'Import Redis Sessions',
          'icon' => 'svg_icons/configmanagement.svg',
          'url' => $site_url.'Master_dashboard/import_redis_sessions',
          'permission' => 1
        ]
      );
      $data['msm_tiles'] = checkTilePermissions($data['msm_tiles']);
      // echo "<pre>"; print_r($data); die();

 	 	  $data['main_content'] = 'master/index';
      $this->load->view('inc/template', $data);
  	}

  	public function school_management() {
      $site_url = site_url();
      $data['tiles'] = array(
        // [
        //   'title' => 'Class Master',
        //   'sub_title' => 'Add/Edit Classes & Sections',
        //   'icon' => $this->config->item('base_url').'/assets/img/blue48px/class-master.png',
        //   'url' => $site_url.'class_master',
        //   'permission' => 1
        // ],
        [
          'title' => 'Student CSV',
          'sub_title' => 'Import student data using CSV file',
          'icon' => 'svg_icons/studentcsv.svg',
          'url' => $site_url.'csv/student',
          'permission' => 1
        ],
        [
          'title' => 'Staff CSV',
          'sub_title' => 'Import staff data using CSV file',
          'icon' => 'svg_icons/staffcsv.svg',
          'url' => $site_url.'csv/staff',
          'permission' => 1
        ],
        [
          'title' => 'Library books CSV',
          'sub_title' => 'Import library books data using CSV file',
          'icon' => 'svg_icons/librarybookcsv.svg',
          'url' => $site_url.'csv/lbr_books',
          'permission' => 1
        ],
        [
          'title' => 'Staff QR Code',
          'sub_title' => 'Generate staff QR codes',
          'icon' => 'svg_icons/staffqrcode.svg',
          'url' => $site_url.'library_controller/generate_staff_qr_codes',
          'permission' => 1
        ],
        [
          'title' => 'Student QR Code',
          'sub_title' => 'Generate student QR codes',
          'icon' => 'svg_icons/studentqrcode.svg',
          'url' => $site_url.'library_controller/generate_student_qr_codes',
          'permission' => 1
        ],
        [
          'title' => 'Student URL QR Code',
          'sub_title' => 'Generate student URL QR codes',
          'icon' => 'svg_icons/studentqrcode.svg',
          'url' => $site_url.'library_controller/generate_url_qr_codes',
          'permission' => 1
        ],
        [
          'title' => 'Parent QR Code',
          'sub_title' => 'Generate Parent QR codes',
          'icon' => 'svg_icons/parentqrcode.svg',
          'url' => $site_url.'library_controller/generate_parent_qr_codes',
          'permission' => 1
        ],
        [
          'title' => 'Student Photo',
          'sub_title' => 'Generate student Photos',
          'icon' => 'svg_icons/studentphoto.svg',
          'url' => $site_url.'library_controller/generate_student_photos',
          'permission' => 1
        ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);
  		$data['main_content'] = 'master/school_management';
    	$this->load->view('inc/template', $data);
  	}

    public function unusedReportCards() {
      // $data['unused_report_cards'] = $this->db->query("select count(id) as unused from assessments_marks_card_history where id not in (select active_marks_card_id from assessments_marks_cards)")->row()->unused;
      $data['unused_report_cards'] = $this->db->query("select id,pdf_link from assessments_marks_card_history where id not in (select active_marks_card_id from assessments_marks_cards)")->result();
      $data['main_content'] = 'master/unused_report_cards';
      $this->load->view('inc/template', $data);
    }

    public function removeFile() {
      $this->load->library('aws_library');
      $url = $_POST['url'];
      $id = $_POST['id'];
      $status = $this->aws_library->deleteS3File($url);
      if($status) {
        $this->db->where('id', $id)->delete('assessments_marks_card_history');
        echo 1;
      } else {
        echo 0;
      }
    }

    public function msm_schools () {
		  $this->load->model('msm/msm_model','msm_model');

      $data['school_list'] = $this->msm_model->get_school_list();
      $data['main_content'] = 'msm/desktop/msm_schools';
      $this->load->view('inc/template', $data);
    }

    public function msm_users() {
		  $this->load->model('msm/msm_model','msm_model');
      $data['host_school'] = $this->settings->getSetting('school_short_name');
      $data['root_users'] = $this->msm_model->get_msm_users();
      $data['main_content'] = 'msm/desktop/admin_console';
      $this->load->view('inc/template', $data);
    }

    public function import_redis_sessions () {
      $redis = new Redis();
      $redis->connect('schoolelement.japvaf.clustercfg.aps1.cache.amazonaws.com', 6379);
      $sessionKeys = $redis->keys('ci_session:*');
      $i = 1;
      foreach ($sessionKeys as $key) {
        print_r($i ++ . ') ');print_r($key);
        if ($i <= 126282) continue;
        $sessionData = $redis->get($key);

        $sessionId = str_replace('ci_session:', '', $key);
        $this->storeSessionInDatabase($sessionId, $sessionData);
      }
    }

    function storeSessionInDatabase($sessionId, $sessionData)
    {
        // Prepare session data for insertion
        $sessionDataArray = [
            'id' => $sessionId,
            'ip_address' => '************', // Placeholder for IP address, adjust as needed
            'timestamp' => time(),
            'data' => $sessionData,
        ];

        // Insert or update the session record
        $this->db->replace('ci_sessions', $sessionDataArray);
    }

    public function add_single_school(){
		  $this->load->model('msm/msm_model','msm_model');

      $school_name = $_POST['school_name'];
      $description = $_POST['description'];
      $school_address = $_POST['school_address'];
      $school_code = $_POST['school_code'];
      $website_address = $_POST['website_address'];
      $erp_url = $_POST['erp_url'];
      $domain = $_POST['domain'];
  
      $success = $this->msm_model->add_single_school($school_name,$description,$school_address,$school_code,$website_address,$erp_url,$domain);
  
      if ($success) {
          $this->session->set_flashdata('flashSuccess', 'School added Successfully.');
      } else {
          $this->session->set_flashdata('flashError', 'Something Wrong..');
      }
      echo json_encode($success);
  
    }

    public function get_school_list(){
		  $this->load->model('msm/msm_model','msm_model');
      $data['school_data'] = $this->msm_model->get_school_list();
      echo json_encode($data);
    }

    public function get_school_list_for_particular_user(){
		  $this->load->model('msm/msm_model','msm_model');
      
      $user_id = $this->input->post('user_id');
      $res = $this->msm_model->get_school_list_for_particular_user($user_id);
      echo json_encode($res);
    }
  
    // public function check() {
    //   $this->load->library('aws_library');
    //   $this->aws_library->deleteS3File('demoschool/marks_cards/60154c6cd9522-1612008556.pdf');
    // }

    // public function parent_usernames() {
    //   $data['main_content'] = 'master/parent_usernames';
    //   $this->load->view('inc/template', $data);
    // }

    // public function convert_usernames() {
    //   $sql = "select u.id as user_id, p.id as parent_id, u.username, p.mobile_no 
    //           from parent p 
    //           join avatar a on a.stakeholder_id=p.id 
    //           join users u on u.id=a.user_id  
    //           where a.avatar_type=2";
    //   $user_data = $this->db->query($sql)->result();
    //   $update_data = array();
    //   $usernames = array();
    //   $password_data = array();
    //   // echo '<pre>'; print_r($user_data);
    //   foreach ($user_data as $key => $user) {
    //     array_push($usernames, $user->username);
    //     // $password_data[] = array('password' => 'rhps', 'id' => $user->user_id);
    //   }
    //   foreach ($user_data as $key => $user) {
    //     if(!in_array($user->mobile_no, $usernames) && $user->mobile_no != '') {
    //       array_push($usernames, $user->mobile_no);
    //       $update_data[] = array('username' => $user->mobile_no, 'id' => $user->user_id);
    //     }
    //   }
    //   // echo '<pre>'; print_r($password_data);die();
    //   if(!empty($update_data)) {
    //     $this->db->update_batch('users', $update_data, 'id');
    //   }
    //   /*foreach ($password_data as $key => $val) {
    //     $this->ion_auth->update($val['id'], $val);
    //   }*/
    //   $this->session->set_flashdata('flashSuccess', 'Updated Usernames');
    //   redirect('Master_dashboard/parent_usernames');
    // }
    public function add_permit_to_host_school(){
		  $this->load->model('msm/msm_model','msm_model');

      $host_school = $this->input->post('host_school_code');
      $user_id = $this->input->post('user_id');
      $user_name = $this->input->post('user_name');
      $res = $this->msm_model->add_host_user_mapping($host_school, $user_id, $user_name);
      echo $res;
    }
  
    public function add_permit_to_other_schools($res, $input){
		  $this->load->model('msm/msm_model','msm_model');

      $res = json_decode($res);
      $user_id_in_school = $res->user_id;
      $avatar_id = $res->avatar_id;
      $staff_id = $res->staff_id;
      $user_name = $_POST['user_name'];
      $user_id = $_POST['user_id'];
      $school_code = $_POST['school_code'];
      $res = $this->msm_model->add_guest_user_mapping($user_id,$user_name,$school_code,$user_id_in_school, $avatar_id, $staff_id);
    }

    public function remove_school_permission_for_users(){
		  $this->load->model('msm/msm_model','msm_model');

      $school_code = $this->input->post('school_code');
      $user_id = $this->input->post('user_id');
      $res = $this->msm_model->remove_school_permission_for_users($school_code, $user_id);
    }

    private function __call_api($data, $end_point) {
      // print_r($end_point);die();
      $curl_request = [
          CURLOPT_URL => $end_point,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POST => 1,
          CURLOPT_POSTFIELDS => json_encode($data),
          CURLOPT_SSL_VERIFYHOST => 0,
          CURLOPT_SSL_VERIFYPEER => 0,
          CURLOPT_HTTPHEADER => [
              "content-type: application/json"
          ]
      ];
  
      $curl = curl_init();
      curl_setopt_array($curl, $curl_request);
      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);
      return $response;
    }

    public function bridge_msm_user_mapping() {
      $data = $_POST;
      // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
      $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];
      
      $data['response'] = $this->__call_api($data, $end_point);
      $data['status'] = '1';
      $res = $this->add_permit_to_other_schools($data['response'], $_POST);
      if(empty($input)) {
        echo json_encode($data);
      }
    }
}