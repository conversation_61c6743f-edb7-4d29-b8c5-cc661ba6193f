<div class="modal" tabindex="-1" role="dialog" id="resources_modal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="border-radius:1rem; width: 80%; margin-top: 1% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h4 class="modal-title" id="modal-title">Add New Activity or Learning Resource</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="container-fluid">
                    <!-- Search Section -->
                    <div class="row mb-2">
                        <div class="col-12">
                            <div class="search-wrapper">
                                <input type="text" class="form-control search-input" id="search_resources" placeholder="Search Activities or Resources...">
                                <i class="fa fa-search search-icon"></i>
                            </div>
                        </div>
                    </div>

                    <!-- No Results Message -->
                    <div id="message" style="display: none;">
                        <div class="no-data-display">
                            <i class="fa fa-info-circle mr-2"></i>
                            No Activities Found!
                        </div>
                    </div>

                    <!-- Activities Grid -->
                    <div class="row mx-0" id="resource_content">
                        <!-- Meta Data Section -->
                        <div class="col-12 mb-3">
                            <h5 class="section-title">Meta Data</h5>
                        </div>

                        <!-- Learning Context -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="LEARNING_CONTEXT" id="learningContext" style="display:none;" onclick="hideMainModal('add_learning_context', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FF9B9B;">
                                    <i class="fa fa-book"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Learning Context</h5>
                                    <p>Learning context refers to the set of conditions where learners build knowledge.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Learning Objective -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="LEARNING_OBJECTIVE" id="learningObjectiveType" style="display:none;" onclick="hideMainModal('learning_objective', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FFD6A5;">
                                    <i class="fa fa-bullseye"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Learning Objective</h5>
                                    <p>Learning objectives or targets are statements that define what students are expected to learn.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Learning Intention -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="LEARNING_INTENTION" id="learning_intention" style="display:none;" onclick="hideMainModal('add_learning_intention', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FFEA20;">
                                    <i class="fa fa-comment"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Learning Intention</h5>
                                    <p>Describes what the teacher wants students to know, understand, and be able to do.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Skills -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="SKILLS" id="skillType" style="display:none;" onclick="hideMainModal('add_skills', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #CBFFA9;">
                                    <i class="fa fa-tasks"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Skills</h5>
                                    <p>A special ability or technique acquired by special training in either an intellectual or physical area.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Success Criteria -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="SUCCESS_CRITERIA" id="successCriteria" style="display:none;" onclick="hideMainModal('add_success_criteria', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #98EECC;">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Success Criteria</h5>
                                    <p>Success criteria are the measures used to determine whether learners have met the learning intentions.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Execution Plan Section -->
                        <div class="col-12 mb-3 mt-4" id="executionPlanDiv">
                            <h5 class="section-title">Execution Plan</h5>
                        </div>

                        <!-- Beginning Plan -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="BEGINNING_PLAN" data-plan="Beginning" id="beginning_plan" style="display:none;" onclick="hideMainModal('add_plan', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #D0F5BE;">
                                    <i class="fa fa-play"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Beginning Plan</h5>
                                    <p>A documented planning process that outlines strategies to be accomplished.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Middle Plan -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="MIDDLE_PLAN" data-plan="Middle" id="middle_plan" style="display:none;" onclick="hideMainModal('add_plan', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FFD4B2;">
                                    <i class="fa fa-pause"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Middle Plan</h5>
                                    <p>A documented planning process that outlines strategies to be accomplished.</p>
                                </div>
                            </div>
                        </div>

                        <!-- End Plan -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="END_PLAN" data-plan="End" id="end_plan" style="display:none;" onclick="hideMainModal('add_plan', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #D0E8F2;">
                                    <i class="fa fa-stop"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>End Plan</h5>
                                    <p>A documented planning process that outlines strategies to be accomplished.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Extended Learning -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="EXTENDED_LEARNING" id="extended_learning" style="display:none;" onclick="hideMainModal('add_extended_learning', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FFB4B4;">
                                    <i class="fa fa-graduation-cap"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Extended Learning</h5>
                                    <p>Additional learning activities and resources to extend student understanding beyond the core lesson.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Contingency Plan -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="CONTINGENCY_PLAN" id="contingency_plan" style="display:none;" onclick="hideMainModal('add_contingency_plan', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #B4E4FF;">
                                    <i class="fa fa-shield"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Contingency Plan</h5>
                                    <p>Backup strategies and alternative approaches to ensure learning objectives are met.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Resources Section -->
                        <div class="col-12 mb-3 mt-4" id="resourcesDiv">
                            <h5 class="section-title">Resources</h5>
                        </div>

                        <!-- Add Resources -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="ADD_RESOURCES" id="resourceType" style="display:none;" onclick="hideMainModal('add_resources', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #F3BCC8;">
                                    <i class="fa fa-file"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Add Resources</h5>
                                    <p>Videos, PowerPoints, Worksheets, Folders and files to support teaching.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Add Book Reference -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="ADD_BOOK_REFERENCE" id="book_resourceType" style="display:none;" onclick="hideMainModal('add_book_resources', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #FF8C8C;">
                                    <i class="fa fa-book"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Add Book Reference</h5>
                                    <p>Books and dictionaries to support teaching on tricky subjects.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="ADDITIONAL_INFORMATION" id="additional_information" style="display:none;" onclick="hideMainModal('add_additional_information', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #9AC5F4;">
                                    <i class="fa fa-info-circle"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Additional Information</h5>
                                    <p>Excess data or excess information.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Assessment -->
                        <div class="col-md-4 mb-3 activity" data-activity-name="ADD_ASSESSMENT" id="assessmentType" style="display:none;" onclick="hideMainModal('add_assessment', 'resources_modal')">
                            <div class="activity-card">
                                <div class="activity-icon" style="background: #A6D0DD;">
                                    <i class="fa fa-clipboard"></i>
                                </div>
                                <div class="activity-content">
                                    <h5>Add Assessment</h5>
                                    <p>Methods or tools that educators use to evaluate educational needs of students.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php 
$this->load->view('academics/lesson_plan/report/design_session/add_learning_context_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_learning_objective_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_learning_intention_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_skills_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_success_criteria_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_plan_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_extended_learning_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_contingency_plan_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_resource_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_book_references_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_additional_information_modal');
$this->load->view('academics/lesson_plan/report/design_session/add_assessment_modal');
?>

<script>
    var totalActivities = [
        "learningContext",
        "learningObjectiveType",
        "learning_intention",
        "skillType",
        "successCriteria",
        "beginning_plan",
        "middle_plan",
        "end_plan",
        "extended_learning",
        "contingency_plan",
        "resourceType",
        "book_resourceType",
        "additional_information",
        "assessmentType",
    ];

    let executionPlanActivities = [
        "beginning_plan",
        "middle_plan",
        "end_plan",
        "extended_learning",
        "contingency_plan"
    ];

    let resourcesActivities = [
        "resourceType",
        "book_resourceType",
        "additional_information",
        "assessmentType",
    ];

    var enabled_activities_for_lms = <?php echo json_encode($this->settings->getSetting("enable_lms_activities")) ?>;

    if(enabled_activities_for_lms==false){
        enabled_activities_for_lms=totalActivities;
    }
    
    var enabledActiviesObj = {};
    if (enabled_activities_for_lms?.length) {
        $("#message").hide();
        $("#resource_content").show();
        enabled_activities_for_lms.forEach(enabledActivity => {
            if(enabledActivity == 'learning_context'){
                enabledActiviesObj['learningContext'] = 'learningContext';
            } else if(enabledActivity == 'success_criteria') {
                enabledActiviesObj['successCriteria'] = 'successCriteria';
            } else {
                enabledActiviesObj[enabledActivity] = enabledActivity;
            }
        })
    } else {
        $("#message").show();
        $("#resource_content").hide();
    }
    
    totalActivities.forEach(activity => {
        if (activity in enabledActiviesObj) {
            $(`#${activity}`).show();
        } else {
            $(`#${activity}`).hide();
        }
    });

    let hasExecutionPlan = executionPlanActivities.some(act => act in enabledActiviesObj);
    if (hasExecutionPlan) {
        $("#executionPlanDiv").show();
    } else {
        $("#executionPlanDiv").hide();
    }

    let hasResourcePlan = resourcesActivities.some(act => act in enabledActiviesObj);
    if (hasResourcePlan) {
        $("#resourcesDiv").show();
    } else {
        $("#resourcesDiv").hide();
    }

    // Search functionality
    $("#search_resources").on("keyup", function() {
        var searchText = $(this).val().toLowerCase();
        var hasResults = false;

        // Search through all activity cards
        $(".activity").each(function() {
            var $activity = $(this);
            var title = $activity.find("h5").text().toLowerCase();
            var description = $activity.find("p").text().toLowerCase();
            
            if (title.includes(searchText) || description.includes(searchText)) {
                $activity.show();
                hasResults = true;
            } else {
                $activity.hide();
            }
        });

        // Show/hide section titles based on visible activities
        $(".section-title").each(function() {
            var $section = $(this);
            var $nextActivities = $section.nextUntil(".section-title");
            var hasVisibleActivities = false;

            $nextActivities.each(function() {
                if ($(this).is(":visible")) {
                    hasVisibleActivities = true;
                    return false; // break the loop
                }
            });

            if (hasVisibleActivities) {
                $section.show();
            } else {
                $section.hide();
            }
        });

        // Show/hide no results message
        if (!hasResults) {
            $("#message").show();
            $("#resource_content").hide();
        } else {
            $("#message").hide();
            $("#resource_content").show();
        }
    });

    // Clear search when modal is closed
    $("#resources_modal").on("hidden.bs.modal", function() {
        $("#search_resources").val("");
        $("#message").hide();
        $("#resource_content").show();
        
        // Reset visibility of all activities
        totalActivities.forEach(activity => {
            if (activity in enabledActiviesObj) {
                $(`#${activity}`).show();
            } else {
                $(`#${activity}`).hide();
            }
        });

        // Show all section titles
        $(".section-title").show();
    });

    $(".addOn").click(function (e) {
        $('#resources_modal').modal('hide');
        getSessionData(id);
    })

    function showResourcesModal() {
        $("#resources_modal").modal("show");
    }

    function successConfirmBox() {
        swal.fire({
            title: "Successful",
            text: "Activity updated",
            icon: "success"
        })

        setTimeout(_ => {
            $(".swal2-confirm").trigger("click");
        }, 700)
    }

    function errorConfirmBox() {
        Swal.fire({
            title: "Failed",
            text: "Something went wrong",
            icon: "error"
        })
    }

    function hideMainModal(subModal, mainModal){
        parentModalToReopen = mainModal;
        $(`#${mainModal}`).modal('hide');
        if(subModal == "add_plan"){
            const plan = event.currentTarget.getAttribute("data-plan");
            planType = plan;
            $('#plan-name-here').html(`Add ${plan} Plan`);
        }
        $(`#${subModal}`).modal('show');
    }

    function showMainModal(){
        $(`#${parentModalToReopen}`).modal('show');
    }
</script>

<style>
    /* .modal-dialog {
        margin: auto;
        width: 80%;
        max-width: 1200px;
        top: 5%;
    } */

    .search-wrapper {
        position: relative;
        margin-bottom: 20px;
    }

    .search-input {
        padding: 12px 20px;
        padding-left: 45px;
        border-radius: 25px;
        border: 1px solid #e0e0e0;
        font-size: 16px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }

    .section-title {
        color: #333;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .activity-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        height: 100%;
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .activity-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .activity-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .activity-icon i {
        color: white;
        font-size: 24px;
    }

    .activity-content {
        flex-grow: 1;
    }

    .activity-content h5 {
        margin: 0 0 8px 0;
        color: #333;
        font-weight: 600;
    }

    .activity-content p {
        margin: 0;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
    }

    .alert {
        border-radius: 10px;
        padding: 15px 20px;
    }

    .alert i {
        font-size: 18px;
    }
</style>