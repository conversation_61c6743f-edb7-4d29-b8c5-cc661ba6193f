<?php $this->load->view('msm_v3/styles/_landing_css'); ?>
<div class = "card landing_card">
    <div class="container-fluid py-2 pt-3">
        <div class="row mx-0"><?php $this->load->view('msm_v3/desktop/_top_bar'); ?></div>
    </div>
    <div class="container-fluid py-2 pt-3 custom-scrollbar" style="overflow: auto; overflow-x: hidden; padding-left: 1.5rem">

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <!-- <div class="graph_top">
                    <div class="row graph_top_card_row">
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Leads
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_leads">100</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Applications
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_applications">100</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Converted
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_converted">100</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-3" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Staff Attendance</h4>
                            </div>
                            <!-- <div class="col-md-4" id="staff_attendance_legend_div"></div> -->
                            <div class="col-md-4" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="pe-3" id="attendance_date_range">
                                    <input type="date" id="attendance_date">
                                </div>
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_staff_view('column_chart', 'staff_attendance_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_staff_view('table', 'staff_attendance_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_attendance_pie_card" style="display: block; margin: 1rem;">
                            <div id="staff_attendance_graph">
                                <center>Loading Staff Attendance Graph</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_attendance_table_card" style="display: none; margin: 1rem;">
                            <table class="table table-bordered align-items-center mb-0" width="90%" style="margin: 1.5rem; table-layout: fixed">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">#</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">Institution</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">Total Staff</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">Checked-in</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">Not Checked-in</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="color: #0F256E">On-Leave</th>
                                    </tr>
                                </thead>
                                <tbody id="staff_attendance_table">
                                    <tr>
                                        <td colspan="6" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <button class="btn vertical-text mx-1" style="border: 1px solid #00CC83">Summary <</button>
            </div> -->
        </div>

        <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Staff Leave Information</h5>
                    </div>
                    <div class="modal-body">
                        <p id="modal-content"></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick = "close_info_modal()">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <!-- <div class="graph_top">
                    <div class="row graph_top_card_row">
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Leads
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Admissions
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Converted
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; margin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-5" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Staff Attendance Trend (7-day)</h4>
                            </div>
                            <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                                <select class="form-select" id="dropdownSchool_staff" style="width: 50%; margin-right: 5%" aria-label="Select Schools">
                                </select>
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_staff_view('column_chart', 'staff_attendance_trend_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_staff_view('table', 'staff_attendance_trend_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_attendance_trend_pie_card" style="display: block; margin: 1rem;">
                        <div id="staff_attendance_trend_graph">
                            <center>Loading Staff Attendance Trend (7-day) Graph</center>
                        </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_attendance_trend_table_card" style="display: none; margin: 1rem;">
                            <div class="table-responsive" id="staff_attendance_trend_table" width="90%" style="margin: 1.5rem;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center btn-group dropstart">
                <button type="button" class="btn vertical-text mx-1" style="border: 1px solid #00CC83; border-radius: 5px;" data-bs-toggle="dropdown" aria-expanded="false">
                Summary <span class="dropdown-toggle"></span>
                </button>
                <div class="dropdown-menu">
                    Admissions - IISB, IELCW has achieved more than 80% of the target.
                    Admissions - ITARI has achieved less than 50% of the target.
                    Admissions - IISB, IELCW has achieved more than 80% of the target.
                    Admissions - ITARI has achieved less than 50% of the target.
                    Admissions - IISB, IELCW has achieved more than 80% of the target.
                    Admissions - ITARI has achieved less than 50% of the target.
                    Admissions - IISB, IELCW has achieved more than 80% of the target.
                    Admissions - ITARI has achieved less than 50% of the target.
                </div>
            </div> -->
        </div>

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-3" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Staff Gender</h4>
                            </div>
                            <!-- <div class="col-md-5" id="staff_gender_legend_div"></div> -->
                            <div class="col-md-3" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_staff_view('column_chart', 'staff_gender_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_staff_view('table', 'staff_gender_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_gender_pie_card" style="display: block; margin: 1rem;">
                            <div id="staff_gender_graph">
                                <center>Loading Staff Gender</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="staff_gender_table_card" style="display: none; margin: 1rem;">
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class=" font-weight-bolder  opacity-7">#</th>
                                        <th class=" font-weight-bolder  opacity-7" >Institution</th>
                                        <th class=" font-weight-bolder  opacity-7" ># Male</th>
                                        <th class=" font-weight-bolder  opacity-7" ># Female</th>
                                        <th class=" font-weight-bolder  opacity-7" >Total</th>
                                    </tr>
                                </thead>
                                <tbody id="staff_gender_table">
                                    <tr>
                                        <td colspan="6" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <button class="btn vertical-text mx-1" style="border: 1px solid #00CC83">Summary <</button>
            </div> -->
        </div>
    
        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-3" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Staff Qualification</h4>
                        </div>
                        <!-- <div class="col-md-5" id="staff_qualification_legend_div"></div> -->
                        <!-- <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_staff_qual" style="width: 50%; margin-right: 5%" aria-label="Select Schools">
                            </select>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_staff_view('column_chart', 'staff_qualification_card', this)">
                                    <img src="<?php //echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_staff_view('table', 'staff_qualification_card', this)">
                                    <img src="<?php //echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div> -->
                    </div>
                    <!-- <div class="animate__animated animate__pulse" id="staff_qualification_pie_card" style="display: block; margin: 1rem;">
                        <div id="staff_qualification_graph">
                            <center>Loading Staff Qualification Graph</center>
                        </div>
                    </div> -->
                    <div class="card-body animate__animated animate__pulse" id="staff_qualification_table_card" style="display: block;overflow-y: auto;">
                        <!-- <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                                <tr>
                                    <th class=" font-weight-bolder  opacity-7">#</th>
                                    <th class=" font-weight-bolder  opacity-7" >Qualification Type</th>
                                    <th class=" font-weight-bolder  opacity-7" >Count</th>
                                </tr>
                            </thead>
                            <tbody id="staff_qualification_table">
                                <tr>
                                    <td colspan="4" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                </tr>
                            </tbody>
                        </table> -->
                        <div id="staff_qualification_table" class="table-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-3" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Staff Attrition Trend</h4>
                        </div>
                        <!-- <div class="col-md-5" id="staff_attrition_legend_div"></div> -->
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_staff_attr" style="width: 50%; margin-right: 5%" aria-label="Select Schools">
                            </select>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_staff_view('column_chart', 'staff_attrition_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_staff_view('table', 'staff_attrition_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="staff_attrition_pie_card" style="display: block; margin: 1rem;">
                        <div id="staff_attrition_trend_graph">
                            <center>Loading Staff Attrition Trend Graph</center>
                        </div>
                    </div>
                    <div class="card-body animate__animated animate__pulse" id="staff_attrition_table_card" style="display: none; margin: 1rem; min-height: 350px; overflow-y: auto;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                                <tr>
                                    <th class=" font-weight-bolder  opacity-7" >Month</th>
                                    <th class=" font-weight-bolder  opacity-7" >Count</th>
                                </tr>
                            </thead>
                            <tbody id="staff_attrition_table">
                                <tr>
                                    <td colspan="4" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-3" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Staff Birthday</h4>
                        </div>
                        <div class="col-md-4" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_staff_birthday" style="width: 50%; margin-right: 5%" aria-label="Select Schools">
                            </select>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse custom-scrollbar" id="staff_birthday_pie_card" style="display: block; margin: 1rem; max-height: 350px; overflow-y: auto; padding: 1rem;">
                    </div>
                    <div class="card-body animate__animated animate__pulse" id="staff_birthday_table_card" style="display: none; margin: 1rem; min-height: 350px; overflow-y: auto;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                                <tr>
                                    <th class=" font-weight-bolder  opacity-7" >Month</th>
                                    <th class=" font-weight-bolder  opacity-7" >Count</th>
                                </tr>
                            </thead>
                            <tbody id="staff_birthday_table">
                                <tr>
                                    <td colspan="4" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('msm_v3/styles/_staff_css'); ?>
<?php $this->load->view('msm_v3/scripts/_staff_script'); ?>