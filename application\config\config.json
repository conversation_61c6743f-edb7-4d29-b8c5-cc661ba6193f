[{"name": "school_name", "type": "string", "group": "school", "description": "Mention the Full School Name.", "default": "School Name"}, {"name": "school_name_line1", "type": "string", "group": "school", "description": "Mention the first line of the School Address", "default": "School Address 1"}, {"name": "school_name_line2", "description": "Mention the second line of the School Address", "group": "school", "type": "string", "default": ""}, {"name": "school_short_name", "description": "Mention the short name of the school. Short name is used inline in ID cards (Student, Staff, Library), for Calendar, etc. Caution: After entering this for the first time, it is NOT advisable to change this at any cost.", "group": "school", "type": "string", "default": "", "isEditable": false}, {"name": "parent_login_no_kid_message", "description": "Mention the error to be displayed when parent login but there no kid in the current year associated with him.", "group": "school", "type": "string", "default": "No wards associated with this login found for the current academic year. If this is incorrect, please contact school administrator. You can view previous academic year information by clicking on the button below."}, {"name": "school_logo", "description": "School logo. Used in login page, landing page and in admissions module extensively.", "group": "school", "type": "string", "storage": "local", "default": "assets/img/nextelement_logo.jpg"}, {"name": "school_admision_logo", "description": "School admission logo. Used in login page, landing page and in admissions module extensively.", "group": "school", "type": "string", "storage": "local", "default": "/assets/img/nextelement_logo.jpg"}, {"name": "school_seal", "description": "School Seal. Used for Student Certificates.", "group": "school", "type": "string", "storage": "local", "default": "assets/img/nextelement_logo.jpg"}, {"name": "school_signature", "description": "School Signature. Used for Student Certificates.", "group": "school", "type": "string", "storage": "local", "default": "assets/img/nextelement_logo.jpg"}, {"name": "DISE", "description": "DISE code", "group": "school", "type": "string", "default": ""}, {"name": "company_logo", "type": "string", "description": "Company logo used in login page, dashboard screen and profile screen in mobile", "group": "school", "storage": "local", "default": "/assets/img/nextelement_logo.jpg"}, {"name": "app_brand_logo_long", "type": "string", "description": "Company logo used in login page, dashboard screen and profile screen in mobile", "group": "school", "storage": "local", "default": "/assets/img/se_logo_long.png"}, {"name": "app_brand_logo", "type": "string", "description": "Company logo used in login page, dashboard screen and profile screen in mobile", "group": "school", "storage": "local", "default": "/assets/img/selogo.png"}, {"name": "company_name", "type": "string", "description": "Company name used across the application", "group": "school", "default": "NextElement", "isEditable": false}, {"name": "login_background", "type": "image", "description": "Image used for login background", "group": "login", "storage": "s3", "default": "/assets/img/temp_school_bg.jpg"}, {"name": "enquiry_login_background", "type": "string", "description": "Enquiry login background to used in login page", "group": "enquiry", "storage": "local", "default": "/assets/img/enquiry_background_template.jpg"}, {"name": "school_header", "type": "string", "group": "examination", "storage": "local", "description": "Header image used for report cards and for school reports", "default": ""}, {"name": "examination_string_to_use_for_tbd_in_report_card", "type": "string", "group": "examination", "storage": "local", "description": "Provide the string to use for TBD values in report card", "default": "NA"}, {"name": "favicon", "type": "string", "description": "Favorite icon", "group": "general", "storage": "local", "default": ""}, {"name": "school_abbreviation", "type": "string", "description": "School abbreviation used mainly in parent module/onboarding flow", "group": "school", "default": "", "isEditable": false}, {"name": "academic_year_id", "type": "string", "description": "The year Id of the current academic year", "group": "school", "default": "18"}, {"name": "promotion_academic_year_id", "type": "string", "description": "The year Id of the promotion academic year", "group": "school", "default": "19"}, {"name": "double_promotion_academic_year_id", "type": "string", "description": "The year Id of the double promotion academic year", "group": "school", "default": "20"}, {"name": "mobile_app_name", "type": "string", "description": "App name used mainly in parent module/onboarding flow", "group": "general", "default": ""}, {"name": "online_room_booking_template_id", "type": "string", "description": "Timetable template used for booking rooms. Create the template and thne add the template id here", "group": "timetable", "default": ""}, {"name": "student_attendance_absentee_sms_message", "type": "string", "group": "student attendance", "default": "Your ward %std_name% of %cs_name% was absent on %date%. Regards- Principal - %school_name%-NXTSMS", "description": "Defines the SMS message content sent to parents when a student is marked absent in reports of student day attendance. The SMS template must be approved by the SMS provider."}, {"name": "student_attendance_import_from_tracking", "type": "boolean", "group": "attendance", "default": "Turn this on to enable import from student tracking"}, {"name": "examination_markscard_remarks_enable_text_formatting", "type": "boolean", "group": "examination", "default": "Turn this on to enable student remarks points wise display"}, {"name": "student_attendance_latecomer_sms_message", "type": "string", "group": "student attendance", "description": "Defines the SMS template content sent to parents when a student is marked as a latecomer in reports of student day attendance. The SMS template must be approved by the SMS provider.", "default": "Your ward %std_name% of %cs_name% was late on %date%. Regards- Principal - %school_name%-NXTSMS"}, {"name": "competition_attendance", "type": "boolean", "description": "Enable/Disable Competition Attendance", "group": "attendance", "default": 0}, {"name": "show_all_students_in_latecomer", "type": "boolean", "description": "If set to true, all students would be shown in the latecomer attendance screen; else only absent students of the section would be displayed.", "group": "student attendance", "default": 1}, {"name": "emergency_exit_visitor", "type": "boolean", "description": "If yes, Student Emergency Exit would mandatorily require the pick-up person to be registered as visitor", "group": "student attendance", "default": 0}, {"name": "forgot_password_mobile", "type": "boolean", "description": "If true, forgot password using mobile will be enabled for parents and staff", "group": "login", "default": 1}, {"name": "contact_email", "type": "boolean", "description": "If true, contact support team using mail will be enabled for parents and staff", "group": "login", "default": 1}, {"name": "support_mail", "type": "string", "description": "support mail id for parents and staff to contact", "group": "login", "default": "<EMAIL>"}, {"name": "forgot_password_email", "type": "boolean", "description": "If true, forgot password using email will be enabled for parents and staff", "group": "login", "default": 0}, {"name": "forgot_password_send_email_id", "type": "string", "group": "school", "description": "Mention the forgt password from email verification .", "default": "<EMAIL>"}, {"name": "examination_allow_additional_remarks", "type": "boolean", "description": "Allow school to enter an additional remark (second remark) in the report card", "group": "examination", "default": 0}, {"name": "examination_remarks_length", "type": "string", "description": "Length of the remark that goes into the report card", "group": "examination", "default": 250}, {"name": "examination_marks_rounding_digits", "type": "string", "description": "Number of digits after the decimal point in report cards", "group": "examination", "default": 1}, {"name": "examination_show_acknowledgement", "type": "boolean", "description": "Show Report Card acknowledgement button for parents", "group": "examination", "default": 0}, {"name": "examination_fail_percentage", "type": "string", "description": "Display Fail if below this percentage", "group": "examination", "default": 35}, {"name": "timetable_attendance_link", "type": "string", "description": "Provide the timetable attendance link (short-cut) to display in timetable - day, subject, none", "group": "timetable", "default": "none"}, {"name": "timetable_header_class_section_id", "type": "string", "description": "Provide the timetable template id that needs to be used for the timetable header for room and staff timetables.", "group": "timetable", "default": 0}, {"name": "timetable_half_day_period", "type": "string", "description": "The period # which should be considered for substitution, when a staff applies a half day leave.", "group": "timetable", "default": 5}, {"name": "timetable_last_period", "type": "string", "description": "The period # which should be considered as the last period for the school", "group": "timetable", "default": 9}, {"name": "timetable_consider_4_alloc_p6_for_cs_lab", "type": "boolean", "description": "This config is required *ONLY* for NPS. This is a hack to support a 4-section allocation for NPS RNR for Computer Sr Lab.", "group": "timetable", "default": 0}, {"name": "timetable_template_for_staff", "type": "string", "description": "This config is to provide the timetable template id that should be used to render staff timetable", "group": "timetable", "default": 0}, {"name": "timetable_template_for_room", "type": "string", "description": "This config is to provide the timetable template id that should be used to render room timetable", "group": "timetable", "default": 0}, {"name": "substitution_algo_daily_workload_cap", "type": "string", "description": "This config is used to configure the substitution algorithm parameters", "group": "timetable", "default": "4"}, {"name": "substitution_algo_daily_substitution_cap", "type": "string", "description": "This config is used to configure the substitution algorithm parameters", "group": "timetable", "default": "1"}, {"name": "substitution_algo_weekly_substitution_cap", "type": "string", "description": "This config is used to configure the substitution algorithm parameters", "group": "timetable", "default": "3"}, {"name": "board", "type": "array", "description": "Specifies the boards provided by the school. Conventions used 1: State, 2: CBSE, 3: ICSE, 4:IGCSE, 5: Andhra_State", "group": "school", "default": "[{\"name\":\"State\", \"value\":\"1\"}, {\"name\":\"CBSE\", \"value\":\"2\"}, {\"name\":\"ICSE\", \"value\":\"3\"}, {\"name\":\"IGCSE\", \"value\":\"4\"}, , {\"name\":\"Andhra_State\", \"value\":\"5\"}]", "isEditable": false}, {"name": "admission_board", "type": "array", "description": "Specifies the boards provided by the school. Conventions used 1: State, 2: CBSE, 3: ICSE, 4:IGCSE, 5: Andhra_State", "group": "admissions", "default": "[{\"name\":\"State\", \"value\":\"1\"}, {\"name\":\"CBSE\", \"value\":\"2\"}, {\"name\":\"ICSE\", \"value\":\"3\"}, {\"name\":\"IGCSE\", \"value\":\"4\"},  {\"name\":\"Andhra_State\", \"value\":\"5\"}]"}, {"name": "medium", "type": "array", "description": "Specifies the medium of instructions provided by the school. Conventions used 1: English, 2: Kannada", "group": "school", "default": "[{\"name\":\"English\", \"value\":\"1\"}, {\"name\":\"Kannada\", \"value\":\"2\"}]"}, {"name": "classType", "type": "json", "description": "Provides a grouping of class types for the school", "group": "school", "default": "[{\"name\":\"Pre-Nursery (PREP-1 TO  PREP-3)\", \"value\":\"1\"}, {\"name\":\"High School (1 TO 10)\", \"value\":\"2\"}]"}, {"name": "admission_type", "type": "array", "description": "Type of admission (new admission or re-admission) of the student. Conventions used 1: Re-admission, 2: New Admission", "group": "student", "default": "[{\"name\":\"Re-admission\", \"value\":\"1\"}, {\"name\":\"New Admission\", \"value\":\"2\"}]", "isEditable": false}, {"name": "admission_status", "type": "array", "description": "Admission status of the student. Conventions used: 1: Pending, 2: Approved, 3: Rejected, 4: Alumni, 5: Partial-term", "group": "student", "default": "[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Alumni\", \"value\":\"4\"}, {\"name\":\"Partial-term\", \"value\":\"5\"}]", "isEditable": false}, {"name": "student_record_selected_lang", "type": "boolean", "description": "If 1, shows the tile to add selected language during primary", "group": "student", "default": "0"}, {"name": "student_record_height_weight", "type": "boolean", "description": "If 1, enables the sub-module to record height and weight of students at a regular interval. This is not completely implemented(?)", "group": "student", "default": "0"}, {"name": "admission_number", "type": "json", "description": "These parameters are used to generate the admission number of the student", "group": "student", "default": "{\"manual\":\"TRUE\", \"admission_generation_algo\":\"NEXTELEMENT\", \"infix\":\"NE\", \"digit_count\":\"5\", \"index_offset\":\"0\"}"}, {"name": "generate_class_wise_admission_number", "type": "boolean", "description": "These parameters are used to generate the admission number class wise", "group": "student", "default": "1"}, {"name": "admission_number_generation_while_student_move_to_erp", "type": "boolean", "description": "These parameter are used to generate the admission number when student is moving to the erp", "group": "admissions", "default": "1"}, {"name": "enrollment_number", "type": "json", "description": "These parameters are used to generate the enrollment number of the student", "group": "student", "default": "{\"manual\":\"TRUE\", \"enrollment_generation_algo\":\"NEXTELEMENT\", \"infix\":\"NE\", \"digit_count\":\"5\", \"index_offset\":\"0\"}"}, {"name": "enable_registration_no_input", "type": "boolean", "description": "If 1, enables the Registration number of the student", "group": "student", "default": "0"}, {"name": "staff_status", "type": "array", "description": "Specifies the work status of the Staff. 1:Pending, 2:Approved, 3:Rejected, 4:Resigned, 5:Retired", "group": "staff", "default": "[{\"name\":\"Pending\", \"value\":\"1\"}, {\"name\":\"Approved\", \"value\":\"2\"}, {\"name\":\"Rejected\", \"value\":\"3\"}, {\"name\":\"Resigned\", \"value\":\"4\"}, {\"name\":\"Retired\", \"value\":\"5\"}]", "isEditable": false}, {"name": "rte", "type": "array", "description": "Specifies the RTE status of Student. 1:RTE, 2:Non-RTE", "group": "student", "default": "[{\"name\":\"RTE\", \"value\":\"1\"}, {\"name\":\"Non-RTE\", \"value\":\"2\"}]", "isEditable": false}, {"name": "category", "type": "array", "description": "Specifies the category student belongs to. 1: General, 2: SC/ST, 3: Category IIA, 4: Category IIB, 5: Category IIIA, 6: OBC", "group": "student", "default": "[{\"name\":\"General\", \"value\":\"1\"}, {\"name\":\"SC/ST\", \"value\":\"2\"}, {\"name\":\"CATEGORY IIA\", \"value\":\"3\"}, {\"name\":\"CATEGORY IIB\", \"value\":\"4\"}, {\"name\":\"CATEGORY IIIA\", \"value\":\"5\"}, {\"name\":\"OBC\", \"value\":\"6\"}]"}, {"name": "boarding", "type": "array", "description": "Specifies the types of boarding the student can choose from. 1: Day School", "group": "student", "default": "[{\"name\":\"Day School\", \"value\":\"1\"}]"}, {"name": "staff_profile_enableStaffProfileEdit", "type": "boolean", "description": "Enables editing of Staff Profile by Staff", "group": "staff", "default": "0"}, {"name": "staff_profile_mandatory_fields", "type": "multiple", "description": "Select the columns that required to profile", "group": "staff login", "options": ["STAFF_NAME", "FATHER_NAME", "MOTHER_NAME", "MARITAL_STATUS", "SPOUSE_NAME", "STAFF_PHOTO", "DOB", "GENDER", "AADHAR_NUMBER", "QUALIFICATION", "SUBJECT_SPECIALIZATION", "TOTAL_EXPERIENCE", "TOTAL_EDUCATION_EXPERIENCE", "CONTACT_NUMBER", "EMERGENCY_INFO", "SPOUSE_CONTACT_NO", "EMAIL", "PRESENT_ADDRESS", "PERMANENT_ADDRESS", "BLOOD_GROUP", "ALTERNATIVE_NUMBER"]}, {"name": "staff_profile_enableQualificationEdit", "type": "boolean", "description": "Enables editing of Staff Qualification data by Staff", "group": "staff", "default": "0"}, {"name": "circular_sms_msg1", "type": "string", "description": "Specifies the first part of the Default SMS message sent when a circular is sent", "group": "communication", "default": "You have a new circular with title"}, {"name": "circular_sms_msg2", "type": "string", "description": "Specifies the second part of the Default SMS message sent when a circular is sent", "group": "communication", "default": ", Please check your School App."}, {"name": "circular_categories", "type": "string", "description": "Specifies the Circular categories", "group": "communication", "default": "[\"Student\",\"<PERSON>e\",\"Portions\",\"PIP\",\"Events\",\"Holidays\",\"Celebrations\",\"Circulars\"]"}, {"name": "circular_from_email", "type": "string", "description": "Specifies the from email id for the circular sent through mail", "group": "communication", "default": "NULL"}, {"name": "circularv2_categories", "type": "string", "description": "Specifies the list of categories available for classifying circular notices (e.g., General, Examination, Event, Holiday).", "group": "communication", "default": "[\"Student\",\"<PERSON>e\",\"Portions\",\"PIP\",\"Events\",\"Holidays\",\"Celebrations\",\"Circulars\"]"}, {"name": "circularv2_from_email", "type": "string", "description": "Specifies the from email id for the circularV2 sent through mail", "group": "communication", "default": "NULL"}, {"name": "circular_enable_email", "type": "string", "description": "Specifies the 'From' email address used when sending circular emails.", "group": "communication", "default": "NULL"}, {"name": "circular_enable_sms", "type": "string", "description": "Specifies whether SMS should be enabled when sending circulars", "group": "communication", "default": "1"}, {"name": "circularv2_sendto_mode", "type": "string", "description": "Specifies the recipients of circulars. Options include: show-selection, parents-only, students-only, or both.", "group": "communication", "default": "both"}, {"name": "parent_profile_display_student", "type": "json", "description": "Specifies the Student fields that needs to be displayed in parent portal", "group": "parent login", "default": "{\"display\":\"1\", \"fields\":[\"admission_no\",\"dob\",\"gender\",\"roll_no\"]}", "isEditable": false}, {"name": "parent_profile_display_father", "type": "json", "description": "Specifies the Father fields that needs to be displayed in parent portal", "group": "parent login", "default": "{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"], \"address\":\"1\"}", "isEditable": false}, {"name": "parent_profile_display_mother", "type": "json", "description": "Specifies the Mother fields that needs to be displayed in parent portal", "group": "parent login", "default": "{\"display\":\"1\", \"fields\":[\"name_photo\",\"email\",\"contact_no\",\"occupation\"],\"address\":\"1\"}", "isEditable": false}, {"name": "indus_single_window_approval_teams", "type": "array", "description": "Specify the type of teams to approve single window", "group": "student", "default": "[{\"name\":\"ICT Team\",\"value\":\"1\"},{\"name\":\"Admission\", \"value\":\"2\"},{\"name\":\"Accounts\",\"value\":\"3\"},{\"name\":\"Transportation\",\"value\":\"4\"},{\"name\":\"Uniforms\",\"value\":\"5\"},{\"name\":\"Books\",\"value\":\"6\"}]"}, {"name": "student_address_types", "type": "array", "description": "Specify the type of addresses supported for Student", "group": "student", "default": "[{\"name\":\"Home Address\", \"value\":\"0\"},{\"name\":\"Office Address\", \"value\":\"1\"}]"}, {"name": "father_address_types", "type": "array", "description": "Specify the type of addresses supported for Father", "group": "student", "default": "[{\"name\":\"Home Address\", \"value\":\"0\"},{\"name\":\"Office Address\", \"value\":\"1\"}]"}, {"name": "mother_address_types", "type": "array", "description": "Specify the type of addresses supported for Mother", "group": "student", "default": "[{\"name\":\"Home Address\", \"value\":\"0\"},{\"name\":\"Office Address\", \"value\":\"1\"}]"}, {"name": "modules", "type": "multiple", "description": "Select the modules that needs to be enabled for this school", "group": "general", "options": ["ACTIVITY", "ADDITIONAL_INCOME", "ADMISSION", "ACADEMICS", "AFL", "ASSETS", "BUILDING_MASTER", "BUS_TRACKING", "COMMUNICATION", "COMPETITION", "CIRCULAR", "CIRCULARS_V2", "CIRCULAR_WIDGET", "DAIRY", "DONATION", "ELIBRARY", "ENGLISHROOTS", "EMAILS", "EXAMINATION", "EXPENSE", "FEESV2", "FLASH_NEWS", "GALLERY", "HELIUM", "HOMEWORK", "INTERNAL_TICKETING", "ITARI", "INDUS_SUY", "INFIRMARY", "STAFF_TASKS_BASKET", "STUDENT_TASKS", "HOMEWORK_SUBMISSION", "MSM", "INVENTORY", "LESSON_PLAN", "LIBRARY", "MANAGEMENT", "ONLINE_CLASS", "ONLINE_CLASS_V2", "OTHERLINKS", "PARENT_INITIATIVE", "PARENTS_LOGIN", "PARENT_TICKETING", "PAYROLL", "PERMISSIONS", "PROCUREMENT", "PROCUREMENT_ASSETS", "PROCUREMENT_INVENTORY", "PROCUREMENT_SALES", "PROCUREMENT_BUDGET", "PROCUREMENT_SERVICE_CONTRACT", "PTM", "PURCHASE", "QUESTION_BANK", "REGISTER", "ROOM_BOOKING", "REPORTS", "SALES", "SCHOOL", "SCHOOL_CALENDAR", "SCHOOL_INFO_WIDGET", "SIMULATE_504_TIMEOUT", "SMS", "STAFF_INITIATIVE", "STUDENT_ATTENDANCE", "STUDENT_DAY_ATTENDANCE_V2", "STUDENT_ATTENDANCE_V2", "STUDENT_CATEGORY_WIDGET", "STUDENT_360", "LEAVE", "LEAVE_V2", "STAFF_ATTENDANCE", "STAFF_CATEGORY_WIDGET", "STAFF_LOGIN", "STAFF_MASTER", "STAFF_OBSERVATION", "STUDENT_CERTIFICATES", "STUDENT_LEAVE", "STUDENT_MASTER", "STUDENT_OBSERVATION", "STUDENT_NONCOMPLIANCE", "SUBSTITUTION", "TASKSTODO", "TEXTING", "TEXTING_WIDGET", "TIMETABLE", "TRANSPORT", "USER_MANAGEMENT", "VENDOR", "VISITOR", "ENQUIRY", "EVENT", "WALLET", "VIRTUAL_CLASSROOM", "STAFF_360_DEGREE", "STUDENT_TRACKING", "STAFF_RECRUITMENT", "STUDENET_COUNSELLING", "CLASSROOM_CHRONICLES", "STAFF_TRANSPORT", "STUDENT_EXIT_FLOW_STAFF", "BOARDING", "CALENDAR_EVENTS_V2", "STAFF_CONSENT", "DAILY_PLANNER", "TRANSPORTATION_REQUEST", "STUDENT_CERTIFICATE", "ID_CARDS"]}, {"name": "parent_modules", "type": "multiple", "description": "Select the modules that needs to be enabled for the parents of this school", "group": "general", "options": ["AFL", "ASSESSMENTS", "ASSESSMENT_POSRTIONS_V1", "ASSESSMENT_POSRTIONS_V2", "ASSESSMENT_MARKS", "ATTENDANCE", "ATTENDANCE_V2", "ATTENDANCE_SUMMARY", "CERTIFICATES", "CIRCULARS", "CONSENT_FORM", "CIRCULARS_V2", "FEESV2", "FEESV3", "FEES_JODO", "FEES_25_26", "FEES_DASHBOARD_WIDGET", "FLASH_NEWS", "GALLERY", "ESCORT_AUTH", "LMS", "HELIUM", "EVENT_V2", "HOMEWORK", "STUDENT_TASKS", "STUDENT_REWARD", "HOMEWORK_SUBMISSION", "MARKS_ANALYSIS", "MARKS_CARD", "NON_COMPLIANCE", "ONLINE_CLASS", "ONLINE_CLASS_V2", "OTHERLINKS", "PARENT_INITIATIVE", "PARENT_TICKETING", "SCHOOL_CALENDAR", "SMS", "STUDENT_HEALTH", "CLASSROOM_CHRONICLES", "STUDENT_LEAVE", "TEXTING", "TIMETABLE", "TRANSPORT", "LIBRARY_PARENT", "VIDEOCHAT", "WALLET", "VACCINATION_STATUS", "DIGITAL_DAIRY", "UPLOAD_AADHAR", "STUDENT_EXIT_FLOW", "STUDENT_INVENTORY", "FEES_MULTIPLE_BLUEPRINT", "STUDENT_INVENTORY_RESERVE_ITEMS", "STUDENT_INVENTORY_RESERVED_ITEMS", "TRANSPORT_REQUEST", "ID_CARDS", "STUDENT_DAY_ATTENDANCE_V2"]}, {"name": "parent_ticketing_types", "type": "string", "description": "Specifies the ticketing types", "group": "parent ticketing", "default": "[\"Compliment\",\"<PERSON><PERSON><PERSON>\",\"Concern\"]"}, {"name": "parent_ticketing_default_assignee", "type": "string", "description": "Specifies the default assignee if none specified", "group": "parent ticketing", "default": "47"}, {"name": "parent_ticketing_management_notif", "type": "string", "description": "Specify the people those who need to be notified on new ticket", "group": "parent ticketing", "default": "[\"47\",\"48\",\"49\",\"50\",\"51\"]"}, {"name": "student_action", "type": "json", "group": "general", "default": ""}, {"name": "smsintergration", "type": "json", "description": "Mode is TEST or LIVE", "group": "communication", "default": "{\"url\":\"promotional.mysmsbasket.com/V2/http-api.php\", \"api_key\":\"CMxAn4EKyxCsLhAe\", \"sender\":\"NXTSMS\",\"mode\":\"LIVE\"}"}, {"name": "sms_credit_length", "type": "json", "group": "communication", "default": "{\"unicode_single\":\"70\", \"unicode_multi\":\"60\", \"non_unicode_single\":\"160\", \"non_unicode_multi\":\"150\"}"}, {"name": "admissions_forms", "type": "json", "group": "admissions", "default": ""}, {"name": "email_settings", "type": "json", "group": "communication", "default": "{\"from_email\":\"<EMAIL>\",\"from_name\":\"National Public School Agara\"}"}, {"name": "email", "type": "json", "group": "communication", "default": ""}, {"name": "lbr_payment_modes", "type": "json", "group": "library", "default": "[{\"name\":\"Cash\", \"value\":\"9\"},{\"name\":\"DD\", \"value\":\"1\"},{\"name\":\"Cheque\", \"value\":\"4\"},{\"name\":\"Net Banking\", \"value\":\"8\"}]"}, {"name": "books_type", "type": "json", "group": "library", "default": "[{\"name\":\"Issue\", \"value\":\"1\"},{\"name\":\"Reference\", \"value\":\"2\"},{\"name\":\"Specimen Copy\", \"value\":\"3\"},{\"name\":\"CD\", \"value\":\"4\"},{\"name\":\"Non-Issue\", \"value\":\"5\"}]"}, {"name": "library_manual_accession_no", "type": "boolean", "description": "If 1, shows model popup for manual access number entry (Only APS)", "group": "library", "default": "0"}, {"name": "return_date", "type": "boolean", "description": "If 1, shows the Fee Date change", "group": "library", "default": "0"}, {"name": "new_arrivals_date", "type": "string", "description": "From date Enter YYYY-MM-DD format", "group": "library", "default": "2023-03-21"}, {"name": "enquiry_date_to_calculate_age", "type": "string", "description": "From date Enter YYYY-MM-DD format", "group": "enquiry", "default": ""}, {"name": "enquiry_captcha_site_key", "type": "string", "description": "Site key for captha verification in enquiry", "group": "enquiry", "default": ""}, {"name": "salt", "type": "string", "group": "", "default": ""}, {"name": "api_key", "type": "string", "group": "", "default": ""}, {"name": "user_provisioning_challenge_fields", "type": "json", "description": "To provide validation fields for parent provisioning", "group": "parent login", "default": "{\"fields\":[{\"name\": \"dob\",\"value\": \"As per school records (in DD-MM-YYYY).\"}]}"}, {"name": "user_provisioning_school_code", "type": "string", "description": "School code. (value is school short name)", "group": "parent login", "default": ""}, {"name": "user_provisioning_process", "type": "string", "description": "Provisioning process to be followed", "group": "parent login", "default": "validate_and_activate"}, {"name": "user_provisioning_link_message_body", "type": "string", "description": "Provisioning sms message body", "group": "parent login", "default": ""}, {"name": "user_provisioning_link_message_footer", "type": "string", "description": "Provisioning sms message footer", "group": "parent login", "default": ""}, {"name": "user_provisioning_cridential_message", "type": "string", "description": "Provisioning credentials message", "group": "parent login", "default": ""}, {"name": "user_provisioning_manual_cridential_part_1", "type": "string", "description": "Provisioning manual credentials message part1  (Used for Activate and Send)", "group": "parent login", "default": "Welcome to {#var#}. We request you to download our School App - SchoolElement at the earliest. Through this, we will stay connected on a regular basis. Steps to download the SchoolElement App:\\n\\nStep-1: Click on the link that is applicable: \\nAndroid App: https://cutt.ly/tcpIMv4\\niPhone App: https://cutt.ly/3cpOJrL \\n\\nStep-2 Enter School Code: {##var##}\\n\\nStep-3 Enter username and password provided Username: %%username%% Password: %%password%%\\n\\nYou can also connect to the application using Web Browser at https://demoschool.schoolelement.in \\n\\nThis is a personalized credential. DO NOT SHARE THIS with anybody."}, {"name": "enquiry_form_text", "type": "string", "description": "This text is visible on top of Enquiry form", "group": "enquiry", "default": "Welcome to Enquiry Form."}, {"name": "text_to_displays_at_consent_form_submissions", "type": "string", "description": "This text is visible when parent submitting consent form", "group": "admissions", "default": "I hereby confirm that I have read and agreed to the terms and conditions specified in the agreement."}, {"name": "admission_login_text", "type": "string", "description": "Text shown on the admissions login screen.", "group": "admissions", "default": "Welcome to the admissions portal of Millennium World School.Enter an Indian mobile number / Email to continue"}, {"name": "adhar_declaration_path", "type": "string", "description": "", "group": "admissions", "default": ""}, {"name": "pan_card_declaration_path", "type": "string", "description": "", "group": "admissions", "default": ""}, {"name": "enquiry_help_block_parent_name", "type": "string", "description": "Comment Below Father name and Mother name in enquiry form", "group": "enquiry", "default": "As per Aadhar Card"}, {"name": "admission_help_block_for_dob", "type": "string", "description": "Help block Below DOB in admission form", "group": "admissions", "default": "As per official records, format should be dd-mm-yyyy"}, {"name": "your_word_for_class", "type": "string", "description": "Change Grade into customized name in enquiry", "group": "enquiry", "default": "Grade"}, {"name": "your_word_for_institute", "type": "string", "description": "Change School into customized name in Admissions", "group": "enquiry", "default": "Scool"}, {"name": "institute_group_name", "type": "string", "description": "Institute group name", "group": "admissions", "default": ""}, {"name": "institute_type", "type": "string", "description": "school or pu or degree", "group": "admissions", "default": "school"}, {"name": "enable_terms_and_condition_strict_mode", "type": "boolean", "description": "After reading complete terms and condition parent able to submit the application.", "group": "admissions", "default": "1"}, {"name": "Freez_academic_year_in_enquiry", "type": "string", "description": "Freez Academic year in enquiry_form", "group": "enquiry", "default": "[24]"}, {"name": "admission_form_word", "type": "string", "description": "Provide the word to be displayed on top of Admission form. Used in admission forms.", "group": "admissions", "default": "Admission Forms"}, {"name": "curriculum_interested", "type": "string", "description": "", "group": "admissions", "default": "[\"IB\",\"CBSE\",\"ICSE\",\"State\",\"Home School\",\"IGCSE\",\"IBDP\",\"NIOS\",\"Montessori\",\"NA\"]"}, {"name": "religion", "type": "string", "description": "", "group": "admissions", "default": "[\"Hindu\",\"Muslim\",\"Jain\",\"Chertain\"]"}, {"name": "admission_applications_closed_display_text", "type": "string", "description": "Provide the text to be displayed when admissions are closed.", "group": "admissions", "default": "Admission form issue closed for maintenance. We will be back soon."}, {"name": "admissions_no_application_display_text", "type": "string", "description": "This text will display when there is no application for the logged in mobile number or email", "group": "admissions", "default": "No Application Found."}, {"name": "user_provisioning_manual_cridential_part_2", "type": "string", "description": "Provisioning manual credentials message part2  (Used for Activate and Send)", "group": "parent login", "default": "- Demoschool"}, {"name": "add_class_admitted_to", "type": "boolean", "description": "To allow student class admitted to in add student.", "group": "student", "default": 0}, {"name": "is_semester_scheme", "type": "boolean", "description": "To allow semester column.", "group": "school", "default": 0}, {"name": "user_support_email", "type": "string", "description": "This is the support email address.", "group": "school", "default": "<EMAIL>"}, {"name": "receipt_gen", "type": "json", "description": "These parameters are used to generate the fee receipt number", "group": "fees", "default": "{\"fee_generation_algo\":\"NEXTELEMENT\", \"infix\":\"NULL\", \"digit_count\":\"5\"}"}, {"name": "fee_date", "type": "boolean", "description": "If 1, shows the Fee Date change", "group": "fees", "default": "0"}, {"name": "enable_indus_single_window_approval_process", "type": "boolean", "description": "Used to enable approval process in fees.", "group": "fees", "default": "1"}, {"name": "sales_manual_receipt_number", "type": "boolean", "description": "If 1, Generate the sales manual receipt number", "group": "procurement", "default": "0"}, {"name": "fee_installment_selection", "type": "boolean", "description": "If 1, selection installment custom", "group": "fees", "default": "0"}, {"name": "fee_due_date_display", "type": "boolean", "description": "If 1, shows the Fee due date", "group": "fees", "default": "0"}, {"name": "fee_report_donor_filter", "type": "boolean", "description": "If 1, shows the Donors Filter in Fee Details Report ", "group": "fees", "default": "0"}, {"name": "fee_report_category_filter", "type": "boolean", "description": "If 1, shows the Category Filter in Fee Details Report ", "group": "fees", "default": "0"}, {"name": "fee_payment_sms", "type": "json", "description": "These parameters are used to generate the fee payment after send sms", "group": "fees", "default": "{\"sms_enabled\":\"FALSE\",\"source\":\"Fee\",\"sh_type\":\"Student\", \"message\":\"Fee payment of Rs. %%amount%% has be initiated. Txn Id: %%trans_id%%. Please keep this Txn ID for future reference\"}"}, {"name": "fee_payment_card_charge_amount", "type": "json", "description": "These parameters are used to card charge amount.  (Creadit Card : 2, Debit Card : 3, Card POS : 7)", "group": "fees", "default": "[{\"value\":\"2\", \"amount\":\"0.05\"}, {\"value\":\"3\", \"amount\":\"0.02\"}, {\"value\":\"7\", \"amount\":\"0.02\"}]"}, {"name": "loan_provider_charges", "type": "boolean", "description": "If 1, shows the Loan Column in Fee Report ", "group": "fees", "default": "0"}, {"name": "fee_adjustment_amount", "type": "boolean", "description": "If 1, shows the Adjustment button Fee", "group": "fees", "default": "0"}, {"name": "fee_refund_amount_display", "type": "boolean", "description": "If 1, shows the Refund coloumn", "group": "fees", "default": "0"}, {"name": "fee_fine_amount", "type": "boolean", "description": "If 1, shows the Fine button Fee", "group": "fees", "default": "0"}, {"name": "fee_daily_tx_installment_column", "type": "boolean", "description": "If 1, shows the Fine button Fee", "group": "fees", "default": "0"}, {"name": "fee_daily_tx_created_by_column", "type": "boolean", "description": "If 1, shows the Fine button Fee", "group": "fees", "default": "0"}, {"name": "fee_balance_sms", "type": "string", "description": "These parameters are used to balance payment after send sms", "group": "fees", "default": "Balance report sms"}, {"name": "fast_fee_collection_receipt_name", "type": "string", "description": "These parameters are used to fase fee collection file name", "group": "fees", "default": "fast_fee_receipt"}, {"name": "fee_balance_due_sms", "type": "string", "description": "These parameters are used to generate the fee balance amount after send sms", "group": "fees", "default": "Balance fee message"}, {"name": "addition_concession_amount", "type": "boolean", "description": "These parameters are used to enabled field to addition concession for next year", "group": "fees", "default": 0}, {"name": "app_links", "type": "json", "description": "These parameters are used to provide app links.", "group": "school", "default": "{\"android_app\":\"\",\"ios_app\":\"\"}"}, {"name": "sms_acad_year", "type": "boolean", "description": "This is used to show/hide academic year", "group": "communication", "default": 0}, {"name": "refresh_journey_url", "type": "string", "description": "URL used to send bus journey details to the Dhundhoo server for live tracking and updates.", "group": "transportation", "default": "https://v1.dhundhoo.com/vendor/journeys/refresh?apiKey="}, {"name": "transport_api_key", "type": "string", "description": "<PERSON>rname used to access the admin console of the Dhundhoo transport system.", "group": "transportation", "default": ""}, {"name": "transport_admin_username", "type": "string", "description": "This is the username for the admin console of dhun<PERSON><PERSON>.", "group": "transportation", "default": ""}, {"name": "transport_admin_password", "type": "string", "description": "This is the password for the admin console of d<PERSON><PERSON><PERSON>.", "group": "transportation", "default": ""}, {"name": "transport_mode", "type": "json", "description": "Transport Pickup Mode fee related", "group": "transportation request", "default": "[{\"name\":\"Pickup & Drop\", \"value\":\"1\"}, {\"name\":\"Pickup\", \"value\":\"2\"}, {\"name\":\"Drop\", \"value\":\"3\"}]"}, {"name": "attendance_time", "type": "json", "description": "Attendance time range to check status", "group": "staff attendance", "default": "{\"from\":\"9:00\", \"to\":\"10:00\"}"}, {"name": "show_staff_handling_in_parent_profile", "type": "boolean", "description": "If set to true, Subjects and class teacher show in student profile page", "group": "parent login", "default": 0}, {"name": "student_leave_auto_approve", "type": "boolean", "description": "If set to true, student leave auto approved", "group": "student leave", "default": 0}, {"name": "transport_enable_notification", "type": "boolean", "description": "Specifies whether transport-related notifications should be sent to users (parent / staff).", "group": "transportation", "default": 0}, {"name": "transport_picup_and_end_point_enabled", "type": "boolean", "description": "This is to Enabled for staff entry of pickup and end points", "group": "transportation request", "default": 0}, {"name": "transport_enable_insecure_link_tracking", "type": "boolean", "description": "Enables LIVE tracking of buses using an insecure (non-HTTPS) tracking link.", "group": "transportation", "default": 0}, {"name": "user_activation_message_new", "type": "string", "description": "Parent Activation Message new", "group": "parent login", "default": "Dear %%name%%Welcome to SchoolElement, the all-new refreshed school management application by NPS Agara. Link for Android App: https://bit.ly/2O2ZW0W. Link for iPhone App: https://apple.co/2T2Klzq. Broswer link: https://npsaga.schoolelement.in/. Your account is activated. Scool code : npsaga, %%credentials%%- NPS Agara"}, {"name": "user_reactivation_message_new", "type": "string", "description": "Parent Re-Activation Message new", "group": "parent login", "default": "Dear %%name%%Welcome to SchoolElement, the all-new refreshed school management application by NPS Agara. Link for Android App: https://bit.ly/2O2ZW0W. Link for iPhone App: https://apple.co/2T2Klzq. Broswer link: https://npsaga.schoolelement.in/. Your account is activated. Scool code : npsaga, %%credentials%%- NPS Agara"}, {"name": "payment_mode_default", "type": "boolean", "description": "This is to specify default 'Select Payment mode'", "group": "fees", "default": 0}, {"name": "show_placeholder_sections", "type": "boolean", "description": "This will indicate showing or not showing placeholder sections.", "group": "school", "default": 0}, {"name": "display_receipts_in_parent", "type": "boolean", "description": "This is to fee receipt show and hide for parents", "group": "fees", "default": 0}, {"name": "homework_module_name", "type": "string", "description": "Name to be displayed in homework module for parent.", "group": "student homework", "default": "Homework"}, {"name": "library_module_name", "type": "string", "description": "Name to be displayed in library module for parent.", "group": "library", "default": "Library"}, {"name": "otherlinks_module_name", "type": "string", "description": "Name to be displayed in Other Links module for parent.", "group": "other links", "default": "Other Links"}, {"name": "academic_start_month", "type": "string", "description": "Academic year start month (add numbers 01, 02, ..., 12)", "group": "calendar", "default": "5"}, {"name": "circular_tile_name", "type": "string", "description": "Specifies the display name or label to be shown for the Circular module in the application interface.", "group": "communication", "default": "Circular"}, {"name": "commity_members_staff_ids", "type": "string", "description": "Comma seperated staff ids of commity members", "group": "enquiry", "default": ""}, {"name": "staff_type", "type": "array", "description": "Type of staff", "group": "staff", "default": "[{\"name\":\"Teaching\", \"value\":\"0\"},{\"name\":\"Non-Teaching\", \"value\":\"1\"},{\"name\":\"Board Member\", \"value\":\"2\"}]", "isEditable": false}, {"name": "homework_default_template", "type": "string", "description": "Specify the default template for homework - it should be one of 3-Column Table template, Simple Template, Blank Template, Template 1, Template 2 Template 3, Template 4", "group": "student homework", "default": "Blank Template"}, {"name": "dont_show_bus_tracking", "type": "boolean", "description": "Enables or disables the display of bus tracking on both parent and staff sides.", "group": "transportation", "default": 0}, {"name": "examination_report_footer", "type": "string", "description": "Provide the titles for report footer.", "group": "examination", "default": "[\"Class Teacher(Name & Signature)\", \"Date\", \"Checked by (Name & Signature)\", \"Coordinator Signature\"]"}, {"name": "display_transportation_numbers", "type": "string", "description": "Specifies which contact details (none, driver, attender, or both) are shown in the transportation section visible to parents.", "group": "transportation", "default": "none"}, {"name": "registered_emails", "type": "string", "description": "Specifies the list of verified email addresses registered for sending emails.", "group": "communication", "default": "[\"<EMAIL>\",\"<EMAIL>\"]"}, {"name": "admissions_sms", "type": "string", "description": "Please provide the SMS content to be sent to the parent upon submission of the admission form. Ensure that the message is compliant with SMS provider guidelines and approved accordingly.", "group": "admissions", "default": "Dear Parent, your online application (Reg. No: %%admission_no%%) has been submitted successfully. Please print, sign, and submit the form at the school front office within 5 working days to complete the registration process. - %%school_name%%"}, {"name": "show_email_option_in_admission", "type": "boolean", "description": "These parameters are used to enabled email button to send users", "group": "admissions", "default": 0}, {"name": "show_payment_link_tab_in_admissions", "type": "boolean", "description": "This is used to enable payment link tab in admissions", "group": "admissions", "default": 1}, {"name": "show_move_to_erp_doj", "type": "boolean", "description": "These parameters are used to enabled date select in Admission - move to erp module", "group": "admissions", "default": 0}, {"name": "show_admission_receipt_button", "type": "boolean", "description": "These parameters are used to enabled admission receipt button", "group": "admissions", "default": 0}, {"name": "application_receipt_is_custom", "type": "boolean", "description": "These parameters are used to enabled admission receipt custom", "group": "admissions", "default": 0}, {"name": "application_new_document_tab", "type": "boolean", "description": "These parameters are used to enabled new previous school/documen tab", "group": "admissions", "default": 0}, {"name": "application_fees_donot_collect", "type": "boolean", "description": "If true, the application fees will not be collected for offline applications. The application will be directly moved to submit state. If false, the application will be moved to submit state after payment is done.", "group": "admissions", "default": 1}, {"name": "admission_fees_auto_assign", "type": "boolean", "description": "", "group": "admissions", "default": 1}, {"name": "admission_filter_version", "type": "string", "description": "v1 or v2", "group": "admissions", "default": "v1"}, {"name": "admission_combination", "type": "string", "description": "Provide the combination options for admission. Used in admission budget.", "group": "admissions", "default": "[\"PCMB\",\"PCMCS\",\"PCME\",\"Others\"]"}, {"name": "admission_budget_filter", "type": "string", "description": "Provide the filter options for admission budget. Used in admission budget.", "group": "admissions", "default": "[\"Combination\",\"Category\"]"}, {"name": "parent_ticketing_enable_direct_staff_assign", "type": "boolean", "description": "Parent Ticketing Category Or Staff Filter", "group": "parent ticketing", "default": 0}, {"name": "admissions_target_from_management", "type": "json", "description": "Admission Target from Management for the academic year. Used in Multi-school Mangement Module.", "group": "admissions", "default": "[{\"acad_year_id\":\"22\", \"target\":\"150\"},{\"acad_year_id\":\"23\", \"target\":\"250\"}]"}, {"name": "email_templates", "type": "string", "description": "email Template", "group": "enquiry", "default": "[\"enquiry confirmation email to parent\",\"admission confirmation email to parent\",\"user_provision\"]"}, {"name": "sms_templates", "type": "string", "description": "SMS Template", "group": "enquiry", "default": "[{\"category\":\"Enquiry\",\"Items\":[\"enquiry confirmation sms to parent\"]},{\"category\":\"Admission\",\"Items\":[\"Admission confirmation sms to parent\"]},{\"category\":\"Library\",\"Items\":[\"Student Books Issued\",\"Staff Books Issued\",\"Student Books return\",\"Staff Books return\"]}]"}, {"name": "enquiry_dob_instruction", "type": "string", "description": "Enquiry DOB instruction per the school requirement. This instruction comes under the DOB box", "group": "enquiry", "default": "Note that your ward age should be atleast 2 years 10 months as on May 31st of that academic year for Prep-1."}, {"name": "enquiry_how_did_you_get_to_know_about_us", "type": "string", "description": "Enquiry How did you get to know about us combo box", "group": "enquiry", "default": "[\"Friends\",\"News_Paper\",\"School_Banner\",\"Web_Site\",\"Others\"]"}, {"name": "enquiry_additional_coaching", "type": "string", "description": "Enquiry Additional Coaching ", "group": "enquiry", "default": "[\"KCET\",\"NEET\",\"Others\"]"}, {"name": "enquiry_combination", "type": "string", "description": "Enquiry Combination", "group": "enquiry", "default": "[\"PCMB\",\"PCMCS\",\"PCME\",\"Others\"]"}, {"name": "enquiry_header_instruction", "type": "string", "description": "Enquiry Header Instruction", "group": "enquiry", "default": "<h5>Welcome to Academic Year 2020 -2021 Registrations</h5><p>PNCCS is a now a Scintillating Grades K-12 Cambridge Pathway School with Reckoning Finnish Model integrated into Cambridge Academic Learning Styles of Our Teaching Methodology thus enriching our Unique Pedagogy for Our Student Cognitians</p><p>PNCCS is now Open for Registrations of Students for enrolment into Day care Section, Grade Kindergarten to Grade 11 AS level Admissions of Cambridge International Examinations Curriculum for the Academic Year 2020-2021. </p><p><strong>Note:</strong> Filling in of this form alone will not confirm your ward’s admissions into PNCCS School Roll, request you to proceed further to meet up with our Admission Counselor on School Campus.</p>"}, {"name": "enquiry_title", "type": "string", "description": "enquiry Title", "group": "enquiry", "default": "Enquiry form for admission"}, {"name": "enquiry_users_left_side_customize_header", "type": "string", "description": "enquiry Left side header customize img format only", "group": "enquiry", "default": "assets/img/nextelement_logo.jpg"}, {"name": "enquiry_school_website_url", "type": "string", "description": "enquiry after submit redirect url ex: www.website.com", "group": "enquiry", "default": "Enquiry form for admission"}, {"name": "enquiry_school_thankyou_url", "type": "string", "description": "Thank you page that the enquiry should be redirected to after processing. Ensure to put the complete URL eg: http://www.rediff.com", "group": "enquiry", "default": "none"}, {"name": "enquiry_custom_message", "type": "string", "description": "Custom message to display after the enquiry form submitted", "group": "enquiry", "default": "Enquiry form for admission"}, {"name": "enquiry_sms", "type": "string", "description": "Enquiry SMS", "group": "enquiry", "default": "Thank you for your enquiry for admission. We will contact you shortly."}, {"name": "revert_application_sms", "type": "string", "description": "Revert submission SMS", "group": "admissions", "default": "Your application is reverted back resubmit the aplication."}, {"name": "enquiry_remarks_place_holder", "type": "string", "description": "Enquiry default remarks", "group": "enquiry", "default": "TOTAL FEE: CONCESSION: FEES TO BE PAID:"}, {"name": "management_staff_ids", "type": "json", "description": "Specifies the staff IDs of management personnel who should receive notifications.", "group": "communication", "default": "[{\"id\":\"10\"},{\"id\":\"11\"}]"}, {"name": "marks_entry_components_count", "type": "string", "description": "Allowed number of components for marks entry at once", "group": "examination", "default": "5"}, {"name": "marks_card_page_size", "type": "string", "description": "Size of the marks card pdf page (a4|a5)", "group": "examination", "default": "a4"}, {"name": "markscard_pictures", "type": "array", "description": "Provide the list of pictures that can be printend on marks card", "group": "examination", "default": "[{\"name\":\"Pen Picture\", \"value\":\"pen_picture\"}]"}, {"name": "forgot_email_enabled", "type": "boolean", "description": "If true, forgot using email will be enabled forgot password", "group": "login", "default": 1}, {"name": "admission_email_based_otp", "type": "boolean", "description": "If true, admission using email will be enabled otp password", "group": "admissions", "default": 1}, {"name": "admission_new_view_details", "type": "boolean", "description": "If true, the v2 version of admission flow will enabled.", "group": "admissions", "default": 1}, {"name": "show_biometric_data_in_payroll", "type": "boolean", "description": "If true, shows biometric data in payroll", "group": "general", "default": 0}, {"name": "attendance_grace_time", "type": "string", "description": "Provide the attendance grace time for staff in minutes", "group": "staff attendance", "default": 0}, {"name": "application_form_status", "type": "string", "description": "Specifies the Admission Follow-up status", "group": "admissions", "default": "[\"Selected for exam\",\"Selected for interactions\",\"Completed interactions\"]"}, {"name": "application_form_dob_criteria", "type": "string", "description": "Specifies age criteria", "group": "admissions", "default": "2021-09-01"}, {"name": "enquiry_follow_up_status", "type": "string", "description": "Specifies the Enquiry status", "group": "enquiry", "default": "[\"Created\",\"Closed-not interested\",\"Follow-up required\",\"Processed for admission\"]"}, {"name": "library_enable_notification", "type": "boolean", "description": "This is to specify send library notifications / not", "group": "library", "default": 0}, {"name": "text_send_to", "type": "string", "description": "Specifies the recipient of the SMS/text message. Options include: Father, Mother, Both, Preferred Parent (preferred_parent or preferred).", "group": "communication", "default": "Both"}, {"name": "text_mode_to_use", "type": "string", "description": "Specify the text mode (sms, notification, notification_sms)", "group": "communication", "default": "sms"}, {"name": "fees_balance_report_show_rte_filter", "type": "boolean", "description": "If 1, shows the Fee Balance report rte filter", "group": "fees", "default": "0"}, {"name": "fees_balance_show_staff_kids_filter", "type": "boolean", "description": "If 1, shows the Fee Balance report Staff Kids filter", "group": "fees", "default": "0"}, {"name": "fees_balance_report_show_partially_filter", "type": "boolean", "description": "If 1, shows the Fee Balance report partially filter", "group": "fees", "default": "0"}, {"name": "fees_stuent_summary_report_show_combination_filter", "type": "boolean", "description": "If 1, shows the Fee Student Summary Combination filter", "group": "fees", "default": "0"}, {"name": "followup_closure_reason", "type": "boolean", "description": "If 1, asks for followup closure", "group": "enquiry", "default": "0"}, {"name": "show_enquiry_advance_amount_paid", "type": "boolean", "description": "If 1, show enquiry advance amount paid field", "group": "enquiry", "default": "0"}, {"name": "enquiry_disable_enquiry_link", "type": "boolean", "description": "If 1, Enquiry form will be closed for parent", "group": "enquiry", "default": "1"}, {"name": "allow_sales_for_new_students", "type": "boolean", "description": "If 1, allow sales for new students", "group": "fees", "default": "0"}, {"name": "inventory_attributes", "type": "string", "description": "Specifies the Inventory attributes", "group": "procurement", "default": "[\"brand\"]"}, {"name": "admissions_payment_mode", "type": "json", "group": "admissions", "description": "Provide the payment modes to be used at the staff counter during admission fee payment.", "default": "[{\"name\":\"Cash\", \"value\":\"9\"},{\"name\":\"DD\", \"value\":\"1\"},{\"name\":\"Debit card\", \"value\":\"3\"},{\"name\":\"Credit Card\", \"value\":\"2\"}]"}, {"name": "sales_payment_modes", "type": "json", "group": "sales", "default": "[{\"name\":\"Cash\", \"value\":\"9_0\"},{\"name\":\"Cheque\", \"value\":\"4_0\"},{\"name\":\"DD\", \"value\":\"1_0\"},{\"name\":\"Card\", \"value\":\"7_0\"},{\"name\":\"Net Banking\", \"value\":\"8_0\"},{\"name\":\"Online Link\", \"value\":\"10_0\"},{\"name\":\"UPI\", \"value\":\"11_0\"}]"}, {"name": "fee_receipt_concession_column", "type": "boolean", "description": "If 1, Fee receipt concession column show component wise", "group": "fees", "default": "0"}, {"name": "create_enquiry_date_enabled", "type": "boolean", "description": "If 1, created date enabled for enquiry creation form", "group": "enquiry", "default": "0"}, {"name": "enquiry_import_from_email", "type": "boolean", "description": "If 1, enabled for enquiry import data from email", "group": "enquiry", "default": "0"}, {"name": "birthday_message", "type": "string", "description": "The message template used to send birthday wishes to students.", "group": "communication", "default": "Dear %%student_name%%, Wish you a many more happy returns of the day."}, {"name": "birthday_sms_send", "type": "boolean", "description": "If 1,the sms for birthday wish is enabled", "group": "communication", "default": "0"}, {"name": "birthday_send_mode", "type": "string", "description": "Specifies the mode for sending birthday wishes — options include Notification, SMS or Notification_SMS.", "group": "communication", "default": "[\"sms\",\"notification_sms\",\"notification\"]"}, {"name": "birthday_mail_send", "type": "boolean", "description": "If 1,the sms for birthday wish is enabled", "group": "communication", "default": "0"}, {"name": "school_support_email_phone", "type": "json", "description": "Provide the list of pictures that can be printend on marks card", "group": "school", "default": "[{\"email\":\"<EMAIL>\", \"phone\":\"0000-000-000\"}]"}, {"name": "library_return_reminder_sms", "type": "boolean", "description": "If 1, library sms reminder sms", "group": "library", "default": "0"}, {"name": "staff_for_sending_birthday", "type": "string", "description": "sending the list of students for whom the birthday wishes are sent", "group": "communication", "default": "Dont use this, use management_staff_ids instead"}, {"name": "library_reminder_notification", "type": "string", "description": "The message that is sending for return book notifications", "group": "library", "default": "Reminder to return Library book %%book_title%% issued on %%issue_date%%, tomorrow. Kindly return to avoid fine."}, {"name": "library_late_reminder_notification", "type": "string", "description": "The message that is sending for return book notifications", "group": "library", "default": "Library book %%book_title%% issued on %%issue_date%% is not returned. Fine accumulated: %%fine_amount%%. Kindly return ASAP."}, {"name": "puc_combination", "type": "boolean", "description": "If 1, student combination entery enabled", "group": "general", "default": "0"}, {"name": "birthday_message_staff", "type": "string", "description": "The message template used to send birthday wishes to staff members.", "group": "communication", "default": "Dear %%staff_name%%, Wish you a many more happy returns of the day."}, {"name": "idcard_attributes", "type": "string", "description": "specifies the attributes of the idcard", "group": "id cards", "default": "[\"Student Name\",\"DOB\"]"}, {"name": "idcard_staff_attributes", "type": "string", "description": "specifies the attributes for the staff idcard", "group": "id cards", "default": "[\"Staff Name\",\"DOB\"]"}, {"name": "idcard_parent_attributes", "type": "string", "description": "specifies the attributes for the parents idcards", "group": "id cards", "default": "[\"parent Name\",\"DOB\"]"}, {"name": "parent_profile_instructions", "type": "string", "description": "Instructions to be shown in parent profile edit", "group": "parent login", "default": "[\"Instruction 1\", \"Instruction 2\"]"}, {"name": "require_approved_sms_template", "type": "boolean", "description": "Enable/Disable Sms template approval", "group": "school", "default": 1}, {"name": "fee_modes", "type": "json", "group": "fees", "default": "[{\"name\":\"No\", \"value\":\"0\"},{\"name\":\"Yes\", \"value\":\"1\"}]"}, {"name": "fees_sales_account_number", "type": "string", "group": "fees", "description": "Enter Sales Bank Account Number.", "default": "001235"}, {"name": "fee_mode_required", "type": "boolean", "description": "Enable/Disable Life time fees", "group": "fees", "default": 1}, {"name": "online_partial_payment", "type": "boolean", "description": "Enable/Disable online_partial_payment", "group": "fees", "default": 1}, {"name": "homework_submission_instructions", "type": "string", "description": "Instructions to be shown in student homework submission", "group": "student homework", "default": "[\"You can submit upto 5 attachments, one by one.\"]"}, {"name": "online_class_server", "type": "string", "description": "Media server to be used for Online Classes; Eg: ************ or gcp.nextelement.in", "group": "online classes", "default": "gcp.nextelement.in"}, {"name": "transport_select_for_parents", "type": "boolean", "description": "Enable/Disable for transportation fee parents can you choose stop and pay the fees.", "group": "fees", "default": 1}, {"name": "online_recording", "type": "boolean", "description": "Enable/Disable Recording feature", "group": "online classes", "default": 0}, {"name": "quota", "type": "array", "description": "Specifies the Univeristy Quota for Student. 1:Management, 2:Univeristy", "group": "student", "default": "[{\"name\":\"Management\", \"value\":\"1\"}, {\"name\":\"University\", \"value\":\"2\"}]"}, {"name": "attempt", "type": "array", "description": "Specifies the Univeristy Quota for Student. 1:Management, 2:Univeristy", "group": "student", "default": "[{\"name\":\"Not applicable\", \"value\":\"1\"}, {\"name\":\"1st attempt\", \"value\":\"2\"}]"}, {"name": "online_attendance_percentage", "type": "string", "description": "max percentage that can be selected while sending sms for the absentees ", "group": "online classes", "default": 30}, {"name": "online_absent_message", "type": "string", "description": "Message for sending sms to the absent students", "group": "online classes", "default": "Dear Parent, %%student_name%% (%%class_name%% %%section_name%%) is absent for the Online Class- %%schedule_name%%"}, {"name": "online_attendance_SMS", "type": "boolean", "description": "Enable/Disable online attedance sms feature", "group": "online classes", "default": 0}, {"name": "gallery_api_key", "type": "string", "description": "Create an api key for allowing access to get gallery", "group": "external", "default": "********-6ca8-407d-b6f4-63bea4d3ed83b4cba353-1199-4990-b2cf-596aa0b13892"}, {"name": "allow_provisioning_for_activated", "type": "boolean", "description": "Used to allow sending activation link to already activated but not loggen in parent accounts", "group": "parent login", "default": 0}, {"name": "school_timezone", "type": "string", "description": "Provide the school timezone", "group": "school", "default": "Asia/Kolkata"}, {"name": "show_user_guide", "type": "boolean", "description": "Show OR Hide user guide", "group": "parent login", "default": 1}, {"name": "enable_english_roots", "type": "boolean", "description": "Enable or Disable English Roots", "group": "external", "default": 1}, {"name": "lesson_plan", "type": "json", "description": "Specifies the Number of files upload and file size in MB", "group": "lms", "default": "{\"lp_task_max_submit_file\":\"1\", \"size\":\"2MB\"}"}, {"name": "resources", "type": "json", "description": "Specifies the file size in MB", "group": "lms", "default": "{\"resource_size\":\"5MB\"}"}, {"name": "image_size_in_admissions", "type": "json", "description": "Specifies the file size in MB", "group": "admissions", "default": "2"}, {"name": "documents_size_in_admissions", "type": "json", "description": "Specifies the file size in MB", "group": "admissions", "default": "2"}, {"name": "fee_concession", "type": "boolean", "description": "Specifies the concession Auto calulcate enabled", "group": "fees", "default": 1}, {"name": "from_emails", "type": "json", "description": "Specifies the list of email addresses that can be used as the 'From' address when sending emails.", "group": "communication", "default": "[{\"email\":\"<EMAIL>\"}]"}, {"name": "staff_attendance_timings", "type": "json", "description": "Specifi staff attendance timings for FD(Full day), HD(Half Day)", "group": "staff attendance", "default": "[{\"name\":\"FD\", \"from\":\"8:00 am\", \"to\":\"4:00 pm\"}, {\"name\":\"HD\", \"from\":\"8:00 am\", \"to\":\"12:00 pm\"}]"}, {"name": "staff_attendance_type", "type": "string", "description": "Provide which way the staff attendance is taken (biometric|manual)", "group": "staff attendance", "default": "manual"}, {"name": "staff_attendance_disallow_outside_checkin", "type": "boolean", "description": "Dis-allow checkin from outside of the geo-fence", "group": "staff attendance", "default": "0"}, {"name": "staff_attendance_allow_permission_only_checkin", "type": "boolean", "description": "Allow check-in from mobile attendance only if location permission is ON", "group": "staff attendance", "default": "0"}, {"name": "staff_attendance_mandate_late_remarks", "type": "boolean", "description": "Makes the Remarks mandatory if the staff is late. 0 for no AND 1 for yes", "group": "staff attendance", "default": "0"}, {"name": "enabled_staff_payroll_data", "type": "boolean", "description": "If set to true, staff payroll enabled.", "group": "payroll", "default": 1}, {"name": "online_class_quiz", "type": "boolean", "description": "Enable this to conduct quiz in online class", "group": "online classes", "default": 1}, {"name": "payroll_working_days", "type": "boolean", "description": "Enable this feature working days change to present days  ", "group": "payroll", "default": 1}, {"name": "deactivation_modules", "type": "string", "description": "Specifies the modules to be deactivated partially", "group": "general", "default": "[\"AFL\",\"Circulars\",\"Report Cards\",\"Online Class\",\"Student Task\", \"Time Table\",\"Wallet\",\"Texts\",\"Calendar\",\"Attendance\",\"Apply_TC\",\"Links\",\"Student_Consent_Forms\"]"}, {"name": "display_names_caps", "type": "boolean", "description": "Enable this to make the names to capital letters", "group": "general", "default": 0}, {"name": "fee_balance_from_email", "type": "boolean", "description": "Enable this to make the fees balance report email options", "group": "fees", "default": 0}, {"name": "half_day_hours", "type": "string", "description": "Specify staff attendance half-day in hours", "group": "staff attendance", "default": "4"}, {"name": "expense_vocher_number_algorithm", "type": "string", "description": "Specifies the vocher number is based on payment_type or simple", "group": "procurement", "default": "simple"}, {"name": "online_class_duration", "type": "string", "description": "Provide the default class duration (in Minutes) for online class V2", "group": "online classes", "default": "60"}, {"name": "student_attendance_type", "type": "string", "description": "Provide the student attendance type (subject_wise|session_wise)", "group": "student subject attendance", "default": "subject_wise"}, {"name": "online_class_show_copy_link", "type": "boolean", "description": "Enable/Disable showing copy link - default enabled", "group": "online classes", "default": 1}, {"name": "freez_primary_fields", "type": "string", "description": "To disable the primary fields in admission form", "group": "admissions", "default": "student_name,grade,dob,f_name,m_name"}, {"name": "prefix_student_name", "type": "string", "description": "The student name will be prepended with roll number or enrollment number or admission number. Values - roll_number, enrollment_number, admission_number", "group": "student", "default": "roll_number, enrollment_number, admission_no,alpha_rollnum, registration_no"}, {"name": "prefix_order_by", "type": "string", "description": "The student name sorting with roll number or enrollment number or admission number or first name. Values - roll_number, enrollment_number, admission_number,first_name", "group": "student", "default": "roll_number, enrollment_number, admission_number,alpha_rollnum,first_name"}, {"name": "display_roll_no_with_student_name", "type": "boolean", "description": "If Enabled, the student name will be prepended with roll number", "group": "online classes", "default": 0}, {"name": "staff_attendance_school_latitude", "type": "string", "description": "Enter the latitude of the school central location", "group": "staff attendance", "default": 0}, {"name": "staff_attendance_school_longitude", "type": "string", "description": "Enter the longitude of the school central location", "group": "staff attendance", "default": 0}, {"name": "staff_attendance_school_radius", "type": "string", "description": "Enter the radius of the school in meters central location. Beyond this, it will consider the location to be outside school", "group": "staff attendance", "default": 0}, {"name": "student_attendance_import", "type": "boolean", "description": "Enable/Disable student attendance import", "group": "student subject attendance", "default": 0}, {"name": "timetable_times", "type": "json", "description": "Specify day start time, middle time and end time", "group": "timetable", "default": "{\"start\":\"8:00 am\", \"middle\":\"12:00 pm\", \"end\":\"4:00 pm\"}"}, {"name": "empty_strand_sub_strand", "type": "string", "description": "Is strand and sub strand to be added empty by default, (0-not empty, 1-strand empty, 2-(strand and sub strand empty))", "group": "afl", "default": 0}, {"name": "late_payment_refresh_button", "type": "boolean", "description": "Enable <PERSON>", "group": "fees", "default": 0}, {"name": "helium_accessor", "type": "string", "description": "Provide who accesses helium vendor, school, nextelement", "group": "helium", "default": "school"}, {"name": "helium_tile_type", "type": "string", "description": "Provide the UI to use to show the Helium tile type in mobile. Can be - normal, banner, textad", "group": "helium", "default": "normal"}, {"name": "active_semester", "type": "string", "description": "Provide currently active semester (odd/even)", "group": "school", "default": "odd"}, {"name": "marks_entry_version", "type": "string", "description": "Select V1/V2 version of marks entry", "group": "examination", "default": "v1"}, {"name": "msm_schools", "type": "string", "description": "Add the schools under the same management", "group": "msm", "default": "[{\"school_code\":\"vspuc\",\"school_name\":\"Vidya Sanskaar PU College\",\"school_domain\":\"schoolelement\"},{\"school_code\":\"vsiscm\",\"school_name\":\"Vidya Sanskaar Degree College\",\"school_domain\":\"schoolelement\"},{\"school_code\":\"vsips\",\"school_name\":\"Vidya Sanskaar International Public School\",\"school_domain\":\"schoolelement\"}]"}, {"name": "enable_native_location", "type": "boolean", "description": "Enable native location call from android", "group": "staff attendance", "default": 1}, {"name": "late_after", "type": "string", "description": "Specify how many minutes after the shift start time the staff is late, (default 5 minutes)", "group": "staff attendance", "default": "5"}, {"name": "student_attendancev2_subject_attendance_mode", "type": "string", "description": "Determine whether periods need to be mentioned for subject attendance. Values: no_periods, attendance_with_periods", "group": "student subject attendance", "default": 0}, {"name": "student_attendancev2_enable_subject_absentees_notification", "type": "boolean", "description": "Enable/Disable option for sending notifications for absentees&latecomers in subjet attendance", "group": "student subject attendance", "default": 0}, {"name": "student_attendancev2_enable_day_absentees_notification", "type": "boolean", "description": "Enable/Disable option for sending notifications for absentees in day attendance", "group": "student subject attendance", "default": 0}, {"name": "student_attendancev2_subject_attendance_absentee_message", "type": "string", "description": "Provide a message that needs to be used for Notification or SMS for absentees", "group": "student subject attendance", "default": "Your ward %%student_name%% of %%class_section%% is absent on %%date%% for the subject %%subject%%. Kindly ignore the sms if you have already informed the school through email- ${school_name} - NXTSMS"}, {"name": "student_attendancev2_day_attendance_absentee_message", "type": "string", "description": "Provide a message that needs to be used for Notification or SMS for absentees", "group": "student attendance", "default": "Your ward %%student_name%% of %%class_section%% is absent today %%date%%. Regards- Principal - %%school_name%%-NXTSMS"}, {"name": "student_attendancev2_subject_attendance_late_message", "type": "string", "description": "Provide a message that needs to be used for Notification or SMS for late comers", "group": "student subject attendance", "default": "Your ward %%student_name%% of %%class_section%% is late on %%date%% for the subject %%subject%%. Kindly ignore the sms if you have already informed the school through email- ${school_name} - NXTSMS"}, {"name": "student_attendancev2_subject_attendance_enable_mode", "type": "string", "description": "Provide whether SMS or Notification or both needs to be enalbed. Values - sms-only, notif-only, both", "group": "student subject attendance", "default": "both"}, {"name": "student_attendancev2_day_attendance_enable_mode", "type": "string", "description": "Provide whether SMS or Notification or both needs to be enalbed. Values - sms-only, notif-only, both", "group": "student attendance", "default": "both"}, {"name": "default_enquiry_year_id", "type": "string", "description": "Set the default enquiry year id", "group": "enquiry", "default": 22}, {"name": "expense_vocher_set_fee_format_id", "type": "string", "description": "Set fee format id for expense vocher", "group": "procurement", "default": "{\"payment_type_cash\":\"0\", \"payment_type_others\":\"0\", \"payment_type_cheque\":\"0\"}"}, {"name": "payment_gateway_convenience_charge_message", "type": "string", "description": "Add this message online payment popup message", "group": "fees", "default": "Kindly note that a convenience fee will be applied on Fees paid online. The following are the convenience charges of most commonly used payment methods - <br>Net-banking: <br>UPI: <br>Credit Card: <br>Debit Card: <br>Wallet: <br>The convenience charges will reflect as soon as select the bank OR in case of card, as soon as you enter the card details. Check the convenience charges before clicking on Pay button."}, {"name": "examination_text_message", "type": "string", "description": "SMS message to be sent for examination to parents", "group": "examination", "default": "Dear <PERSON><PERSON>, We are hereby sharing marks obtained by your ward{#STUDENT_NAME#} in {#ASS_NAME#} Subject {#SUBJECT1#}: {#MARKS1#} Subject {#SUBJECT2#}: {#MARKS2#} Subject {#SUBJECT3#}: {#MARKS3#} Subject {#SUBJECT4#}: {#MARKS4#} Subject {#SUBJECT5#}: {#MARKS5#} Subject {#SUBJECT6#}: {#MARKS6#} Regards, Principal J S P U College-NXTSMS"}, {"name": "examination_enable_sms", "type": "boolean", "description": "Enable Examination SMS Functionality", "group": "examination", "default": 0}, {"name": "calender_events_notification_message", "type": "string", "description": "Provide a message that needs to be used for Notification for parent", "group": "calendar", "default": "Dear parents, upcoming events on %%fromdate_to_todate%%  %%event_type%%, %%event_name%%, %%school_name%%"}, {"name": "payment_gateway", "type": "string", "description": "Payment gateway of choice - options: traknpay, payu", "group": "payment", "default": "traknpay"}, {"name": "donation_receipt_id", "type": "string", "description": "receipt number", "group": "donation", "default": ""}, {"name": "staff_documents_name", "type": "string", "description": "Specifies the Staff Documents Name", "group": "staff", "default": "[\"Birth Certificate\",\"Aadhar Card\"]"}, {"name": "parent_annual_income_option", "type": "string", "description": "Parent Annual income option to show in student tab and parent console", "group": "student", "default": "[\"00-50,000\",\"50,000-1,00,000\"]"}, {"name": "student_documents_name", "type": "string", "description": "Specifies the Staff Documents Name", "group": "student", "default": "[\"Birth Certificate\",\"Aadhar Card\"]"}, {"name": "staff_leave_rules", "type": "string", "description": "Staff Leave Instruction", "group": "staff leave", "default": ""}, {"name": "leave_approver_mode", "type": "string", "description": "Leave Approval Levels - level_1, level_2, level_3", "group": "staff leave", "default": "[\"level_1\"]"}, {"name": "enable_multi_level_leave_approver_mode", "type": "boolean", "description": "Enable multi-level leave approval", "group": "staff leave", "default": 0}, {"name": "multi_level_leave_approval_strategy", "type": "string", "description": "Enable multi-level leave approval - reject_if_any_one_rejects or level_wise_Approve - /Rejects if higher approver approves/rejects", "group": "staff leave", "default": "reject_if_any_one_rejects"}, {"name": "admission_age_guidelines_message", "type": "string", "description": "Provide the message for age guidelines display in admission process.", "group": "admissions", "default": "Age as on 1-06-2023 Allowed place application 1.Nur-3yrs, 2.Jr KG- 4 yrs 3.Sr KG -4.5 yrs 4.1- 5.5yrs"}, {"name": "admisison_one_time_password_send_email_id", "type": "string", "group": "admissions", "description": "From email id for sending one time password for email verification during admission process.", "default": "<EMAIL>"}, {"name": "student_admission_custom_fields", "type": "array", "description": "Specifies the custom fields", "group": "student", "default": "[{\"name\":\"custom1\", \"value\":\"custom field name\"}, {\"name\":\"custom2\", \"value\":\"custom field name\"}, {\"name\":\"custom3\", \"value\":\"custom field name\"},{\"name\":\"custom4\", \"value\":\"custom field name\"},{\"name\":\"custom5\", \"value\":\"custom field name\"}]"}, {"name": "staff_custom_fields", "type": "array", "description": "Specifies the custom fields", "group": "staff", "default": "[{\"name\":\"custom1\", \"value\":\"custom field name\"}, {\"name\":\"custom2\", \"value\":\"custom field name\"}, {\"name\":\"custom3\", \"value\":\"custom field name\"},{\"name\":\"custom4\", \"value\":\"custom field name\"},{\"name\":\"custom5\", \"value\":\"custom field name\"}]"}, {"name": "fee_collection_v1", "type": "boolean", "description": "Enable fee collection new version Functionality", "group": "fees", "default": 0}, {"name": "disable_document_tab_in_admissions", "type": "boolean", "description": "To disable document tab in Admissions", "group": "admissions", "default": 0}, {"name": "enable_enquiry_student_referral_url", "type": "boolean", "description": "Enable to generate enquiry referral link", "group": "enquiry", "default": 1}, {"name": "disable_rte", "type": "boolean", "description": "Not to show RTE data in reports", "group": "admissions", "default": 1}, {"name": "enabled_medical_form_tab_in_admissions", "type": "boolean", "description": "To enabled medical form tab in Admissions", "group": "admissions", "default": 1}, {"name": "enabled_vaccination_details_in_admissions", "type": "boolean", "description": "To enabled vaccination details in Admissions", "group": "admissions", "default": 1}, {"name": "enabled_hospitalization_details_in_admissions", "type": "boolean", "description": "To enabled Hospitalization details in Admissions", "group": "admissions", "default": 1}, {"name": "staff_profile_view_v1", "type": "boolean", "description": "Enablestaff_profile_view new version Functionality", "group": "staff login", "default": 0}, {"name": "student_profile_view_v1", "type": "boolean", "description": "Enablestudent_profile_view new version Functionality", "group": "student login", "default": 0}, {"name": "enquiry_mobile_number_otp_verification", "type": "boolean", "description": "Enable Mobile Vefication in enquiry.", "group": "enquiry", "default": 0}, {"name": "enquiry_donot_show_classes_inEnquiry", "type": "boolean", "description": "Not to show classes in enquiry.", "group": "enquiry", "default": 0}, {"name": "enquiry_check_registered_mobile_number", "type": "boolean", "description": "To check Registered Mobile Number", "group": "enquiry", "default": 0}, {"name": "enquiry_dob_validation", "type": "boolean", "description": "To check eligibality of class", "group": "enquiry", "default": 0}, {"name": "enquiry_captcha_code_verification", "type": "boolean", "description": "To Show Captcha Verification", "group": "enquiry", "default": 0}, {"name": "enquiry_create_admission_record", "type": "boolean", "description": "To create admission form if reporting status is convert for the selected follow up status in enquiry.", "group": "enquiry", "default": 0}, {"name": "disable_new_admissions_tab", "type": "boolean", "description": "To disable New Admissions tab in admissions", "group": "student login", "default": 0}, {"name": "document_verification_in_admissions", "type": "boolean", "description": "Only after documents is verified offer release is allowed", "group": "admissions", "default": 1}, {"name": "enquiry_page_type", "type": "string", "description": "Allowed values - enquiry_horizontal_type (used for Indus), enquiry_pncc, enquiry_mlps, enquiry_jspuc, enquiry_yashasvi", "group": "enquiry", "default": "enquiry_horizontal_type"}, {"name": "enquiry_pick_status_from_table", "type": "boolean", "description": "Whether to pick up the enquiry status from table", "group": "enquiry", "default": 0}, {"name": "admission_pick_status_from_table", "type": "boolean", "description": "Whether to pick up the Admission status from table", "group": "admissions", "default": 0}, {"name": "directly_linkTo_feesAssign_siblingConnected", "type": "boolean", "description": "This is to enable direct connect to assign fees and sibling connected in admissions", "group": "admissions", "default": 1}, {"name": "enable_gallery_image_download", "type": "boolean", "description": "Whether to download image or not", "group": "school", "default": "1"}, {"name": "show_all_electives", "type": "boolean", "description": "Whether to show all electives or not", "group": "school", "default": "1"}, {"name": "enable_multiple_geofence", "type": "boolean", "description": "Whether to show multiple geofences for staff or not", "group": "school", "default": "0"}, {"name": "enable_staff_regularize_attendance", "type": "boolean", "description": "Whether to enable staff regularize Attendance or not", "group": "school", "default": "0"}, {"name": "admission_joining_period", "type": "string", "description": "Proivde the options to be displayed in joining period dropdown in admission forms.", "group": "admissions", "default": "[\"Term 1- Aug to Dec\",\"Term 2 - Jan - June\"]"}, {"name": "student_parent_guardian_photo_size", "type": "string", "description": "Specifies the Number of files upload and file size in KB", "group": "student", "default": "10000000"}, {"name": "enable_staff_tablet_view_top_bar_icon", "type": "boolean", "description": "Whether to enable tablet view top bar notification and approval widget", "group": "staff", "default": "0"}, {"name": "fee_excess_amount_receipt_number_set", "type": "string", "description": "Set receipt number format id for excess amount", "group": "fees", "default": "0"}, {"name": "library_accession_no_receiptbook_id", "type": "string", "description": "Set receipt number format id for library accession number", "group": "library", "default": ""}, {"name": "enable_day_attendance_classteacher_permission", "type": "boolean", "description": "If disabled, teacher can take attendance of all class and section otherwise allocated class only", "group": "student attendance", "default": "0"}, {"name": "enable_subject_attendance_teacher_permission", "type": "boolean", "description": "If disabled, teacher can take attendance of all class and section otherwise allocated class only", "group": "student subject attendance", "default": "0"}, {"name": "open_for_admissions", "type": "boolean", "description": "If disabled, Admissions will not be shown", "group": "admissions", "default": "1"}, {"name": "fees_enforce_consent_form_acceptance", "type": "string", "description": "Parent will see a note if consent forms are not submitted. On click of continue, they are redirected to consent form module.", "group": "fees", "default": "Submit the consent forms"}, {"name": "fees_display_note", "type": "string", "group": "fees", "description": "Pa<PERSON> will see a note on click of fees. On confirmation, they will be able to continue to pay fees.", "default": "Fees paid pop up note display"}, {"name": "allow_fee_payment_on_consent_agreed", "type": "boolean", "group": "fees", "description": "Parents must compulsorily agree to the consent form.", "default": "1"}, {"name": "student_consent_fees_check_for_readmission", "type": "boolean", "group": "fees", "description": "Consent forms must be submitted by all students before any fee payment can be made.", "default": "1"}, {"name": "fees_note_display", "type": "string", "group": "fees", "description": "Mention the fees pop up notes display.", "default": "Fees paid pop up note display"}, {"name": "staff_zoho_ticket_url", "type": "string", "group": "staff", "description": "Enter the url to submit zoho tickets from staff.", "default": ""}, {"name": "student_photo_note_display", "type": "string", "group": "admissions", "description": "Student photo note display.", "default": "Add white background under the photo upload"}, {"name": "enquiry_age_cal", "type": "boolean", "description": "Enquiry calulcate days", "group": "enquiry", "default": "1"}, {"name": "student_view_birthdays", "type": "boolean", "description": "Enables the display of student birthdays widget in the Dashboard.", "group": "student", "default": "1"}, {"name": "display_slab_in_transport", "type": "boolean", "description": "Shows Slab field in transportation details.", "group": "transportation request", "default": "1"}, {"name": "staff_view_birthdays", "type": "boolean", "description": "Enables the display of staff birthdays widget in the Dashboard.", "group": "staff", "default": "1"}, {"name": "staff_attendance_algorithm", "type": "string", "description": "Choose attendance type for staff between default and consider_check_in_only", "group": "staff attendance", "default": "default"}, {"name": "student_attendancev2_attendance_message", "type": "string", "description": "Provide a message that needs to be used for Notification or SMS for absentees", "group": "student attendance", "default": "Your ward %%student_name%% of %%class_section%% is %%remarks%% for %%classes%% periods %%date%%. Kindly ignore the sms if you have already informed the school through email- ${school_name} - NXTSMS"}, {"name": "enquiry_help_block_student_name", "type": "string", "description": "Enquiry student name help block text", "group": "enquiry", "storage": "enquiry", "default": "As per Official Records / SSLC Report Card"}, {"name": "enquiry_help_block_student_dob", "type": "string", "description": "Enquiry student dob help block text", "group": "enquiry", "storage": "enquiry", "default": "As per Official Records / SSLC Report Card. Format should be dd-mm-yyyy"}, {"name": "escort_enable_notification", "type": "boolean", "description": "Enable/Disable to send notification to the parents", "group": "escort", "default": 0}, {"name": "online_challan_payment_mode", "type": "boolean", "description": "Enable/Disable show online challan payment mode for fast fee collection", "group": "fees", "default": 0}, {"name": "visitors_module_switch_school_details", "type": "string", "description": "Visitors <PERSON><PERSON><PERSON> enter school details its help for easy to switch school", "group": "visitor", "default": "[{\"school_name\":\"iisb\", \"school_logo\":\"https://sof.schoolelement.in//assets/img/sof_logo.jpeg\", \"url\":\"https://sof.schoolelement.in/Visitors_v2_controller/visitor_dashboard\"}, {\"school_name\":\"suy\", \"school_logo\":\"https://suy.schoolelement.in//assets/img/suy_logo.png\", \"url\":\"https://suy.schoolelement.in/Visitors_v2_controller/visitor_dashboard\"},{\"school_name\":\"sof\", \"school_logo\":\"https://sof.schoolelement.in//assets/img/sof_logo.jpeg\", \"url\":\"https://sof.schoolelement.in/Visitors_v2_controller/visitor_dashboard\"}]"}, {"name": "enquiry_generate_pdf", "type": "boolean", "description": "Enquiry Generate and Download PDF", "group": "enquiry", "default": 0}, {"name": "custom_class_from_enquiry_class_table", "type": "boolean", "description": "Enquiry Custom Class", "group": "enquiry", "default": 0}, {"name": "student_admission_number_receipt_book_id", "type": "string", "description": "Set Receipt Book format id for Student Admission number", "group": "student", "default": "0"}, {"name": "enrollment_number_receipt_book_id", "type": "string", "description": "Set Receipt Book format id for Student Enrollment number", "group": "student", "default": "0"}, {"name": "academic_year_id_for_transportation_details", "type": "string", "description": "Set the Academic Year ID for the year transportation details need to be collected.", "group": "transportation request", "default": "25"}, {"name": "staff_employee_code_receipt_book_id", "type": "string", "description": "Set Receipt Book format id for Staff Employee Code", "group": "staff", "default": "0"}, {"name": "staff_visitor_approval_notiffication", "type": "boolean", "description": "Enable/Disable to send notification to the staff/seccurity guard", "group": "visitor", "default": 0}, {"name": "admissions_submit_short_application_after_payment", "type": "boolean", "description": "If enabled, the application will be submitted after payment is done. If not enabled, parent will continue to fill the long application form. Used in Short Application Form flow.", "group": "admissions", "default": 0}, {"name": "transport_request_hide_route_and_stop", "type": "boolean", "description": "Enable to hide the route and stop in transport request.", "group": "transportation request", "default": 1}, {"name": "disable_parent_transport_request_access", "type": "boolean", "description": "Parents cannot submit the transport request form when this config is enabled", "group": "transportation request", "default": 1}, {"name": "enquiry_dedupe_enquiry_remarks_required", "type": "boolean", "description": "Enable to enter the remarks while dedupe the enquiry", "group": "enquiry", "default": 1}, {"name": "transportation_request_home_address_type", "type": "string", "description": "Enter the address type from student address to show the address in transport request", "group": "transportation request", "default": 1}, {"name": "msm_graph_theme", "type": "string", "description": "Theme for MSM Dashboard. Supported values = (blue_pink, orange, green_yellow)", "group": "msm", "default": "orange"}, {"name": "itari_admission_academic_year", "type": "string", "description": "Enter the Academic year that needs to be applied for.", "group": "itari", "default": "24"}, {"name": "library_borrow_return_rfid_number", "type": "boolean", "description": "Library borrow return RFID Card Number", "group": "library", "default": 0}, {"name": "enquiry_sources", "type": "string", "description": "Create the source for display enquiry form source dropdown", "group": "enquiry", "default": "[\"walk-in\",\"Website\",\"reference\"]"}, {"name": "enable_close_option_in_internal_ticketing", "type": "boolean", "description": "Enable close option in internal ticketing", "group": "internal ticketing", "default": 0}, {"name": "enable_calender_notifications", "type": "boolean", "description": "Enable notifications for upcoming calender events", "group": "calendar", "default": 0}, {"name": "enable_staff_biometric_notifications", "type": "boolean", "description": "Enable staff biometric notifications for each puch", "group": "staff attendance", "default": 0}, {"name": "internal_ticketing_from_email", "type": "string", "description": "From email id for Internal Ticketing", "group": "internal ticketing", "default": "manju<PERSON><EMAIL>"}, {"name": "enable_force_change_password_for_parents", "type": "boolean", "description": "Enable force change password for the parents when they login first time", "group": "login", "default": 0}, {"name": "circular_receipt_number_set", "type": "string", "description": "Specifies the receipt number format ID to be used for circular acknowledgments.", "group": "communication", "default": "0"}, {"name": "payroll_age_cal_pt_above_60", "type": "string", "description": "Payroll age calculation for PF above 60 years", "group": "payroll", "default": "0"}, {"name": "payroll_net_pay_with_pf_caluclation", "type": "string", "description": "Payroll pf without net payment reduce pf amount", "group": "payroll", "default": "0"}, {"name": "calculate_no_days_in_payslip", "type": "string", "description": "Calculate number of days in leave", "group": "payroll", "default": "0"}, {"name": "student_wallet_enable_debits_only", "type": "string", "description": "Student wallet Transcation without load money", "group": "student wallet", "default": "0"}, {"name": "payroll_disbursement_notification", "type": "boolean", "description": "If its true payroll disbursement notification for staffs", "group": "payroll", "default": "0"}, {"name": "leave_application_from_email", "type": "string", "description": "From email id for leave application", "group": "staff leave", "default": "manju<PERSON><EMAIL>"}, {"name": "examination_show_alumni_students_in_marks_entry", "type": "boolean", "description": "If enabled, alumni or terminated students will appear in marks entry", "group": "examination", "default": "0"}, {"name": "fees_concession_approval_flow", "type": "boolean", "description": "If enabled, fees concession approval flow", "group": "fees", "default": "0"}, {"name": "fast_fee_collection_payment_modes", "type": "json", "group": "fees", "default": "[{\"name\":\"CASH\", \"value\":\"9_0\"},{\"name\":\"DD\", \"value\":\"1_0\"},{\"name\":\"CHEQUE\", \"value\":\"4_0\"},{\"name\":\"NET BANKING\", \"value\":\"8_0\"},{\"name\":\"CREDIT CARD\", \"value\":\"2_0\"},{\"name\":\"DEBIT CARD\", \"value\":\"3_0\"},{\"name\":\"JODO\", \"value\":\"12_0\"},{\"name\":\"UPI\", \"value\":\"11_0\"},{\"name\":\"ADJUSTED\", \"value\":\"22_0\"},{\"name\":\"CARD\", \"value\":\"7_0\"}]"}, {"name": "staff_recruitment_redirect_website", "type": "string", "description": "After submitting of Staff recruitment form redirect to url ex: www.website.com", "group": "staff recruitment", "default": ""}, {"name": "emergency_exit_student_notification", "type": "boolean", "description": "After exit of the studen notification is sent to parents.", "group": "attendance", "default": "0"}, {"name": "enable_staff_teaching_plan_in_parent_lms_console", "type": "boolean", "description": "This is to show the staff related session details to visible on parent side console or not 1 -> yes, 0-> for no in LMS(Lesson plan)", "group": "lms", "default": "0"}, {"name": "access_control_for_internal_ticketing", "type": "boolean", "description": "Internal ticketing access control", "group": "internal ticketing", "default": "0"}, {"name": "enable_confidential_option_in_internal_ticketing", "type": "boolean", "description": "Internal ticketing confidentiality", "group": "internal ticketing", "default": "0"}, {"name": "staff_recruitment_captcha_site_key", "type": "string", "description": "Site key for captha verification in staff recruitment", "group": "staff recruitment", "default": ""}, {"name": "staff_recruitment_captcha_verification", "type": "boolean", "description": "To Show Captcha Verification", "group": "staff recruitment", "default": 0}, {"name": "parent_fee_payment_notifcation", "type": "string", "description": "Parent Notification Fee Payment", "group": "fees", "default": "This is to confirm that your fee payment for the %%studentName%% %%%classSection%% towards %%%blueprintName%% has been successfully processed. Transaction ID: %%trans_id%% and Amount Paid: %%amount%%"}, {"name": "advanced_hr_custom_yesno_fields", "type": "json", "description": "configurable yes and no fields. maximum up to 5 questions", "group": "staff recruitment", "default": "[{\"field_id\": \"1\",\"text\": \"Sample yes/no Question?\"}, {\"field_id\": \"2\",\"text\": \"Sample yes/no Question?\"},{\"field_id\": \"3\",\"text\": \"Sample yes/no Question?\"},{\"field_id\": \"4\",\"text\": \"Sample yes/no Question?\"},{\"field_id\": \"5\",\"text\": \"Sample yes/no Question?\"}]"}, {"name": "advanced_hr_custom_text_fields", "type": "json", "description": "configurable custom text fields. maximum up to 5 questions", "group": "staff recruitment", "default": "[{\"field_id\": \"1\",\"text\": \"Are you fine to have a Police verification?\"}, {\"field_id\": \"2\",\"text\": \"Are you fine to have a Police verification?\"},{\"field_id\": \"3\",\"text\": \"Are you fine to have a Police verification?\"},{\"field_id\": \"4\",\"text\": \"Are you fine to have a Police verification?\"},{\"field_id\": \"4\",\"text\": \"Are you fine to have a Police verification?\"}]"}, {"name": "jodo_enabled_flex_cred_payment", "type": "boolean", "description": "configurable testing purpose dont use this", "group": "fees", "default": "0"}, {"name": "jodo_header_authorization_key", "type": "string", "description": "<PERSON><PERSON>", "group": "fees", "default": "Provided for key from Jodo team"}, {"name": "jodo_payment_gateway_convenience_charge_message", "type": "string", "description": "", "group": "fees", "default": "Add message to shown parent side fees page"}, {"name": "birthday_flashnews_message", "type": "string", "description": "Message to be displayed in the flash news section for staff birthdays.", "group": "communication", "default": " Happy Birthday to an incredible member of our school community!  %%staff_name%%, your dedication and hard work inspire us all. May your day be filled with joy and well-deserved celebrations. Best Wishes"}, {"name": "payroll_download_pdf", "type": "boolean", "description": "configurable download if payroll pdf template added", "group": "payroll", "default": "1"}, {"name": "payroll_tax_declaration_enable_perquisite_income_calculation", "type": "boolean", "description": "If set to true, staff can declare perquisite income such as availing company-provided accommodation during tax declaration.", "group": "payroll", "default": 1}, {"name": "enable_auto_approve_leaves_applied_by_admin", "type": "boolean", "description": "", "group": "staff leave", "default": "0"}, {"name": "disable_closing_balance_display_in_fees_collection", "type": "boolean", "description": "Clossing balance text in fees collection dashboard not visible", "group": "fees", "default": "0"}, {"name": "admission_duplicate_count_removed", "type": "boolean", "description": "Exclude the duplicate count in admission total count statistics if enabled.", "group": "admissions", "default": "1"}, {"name": "enable_staff_class_subject_access_control_for_lms", "type": "boolean", "description": "Controls class and subject visibility for staff in LMS. If enabled, staff can access only their assigned classes and subjects; if disabled, all classes and subjects will be visible.", "group": "lms", "default": "0"}, {"name": "enable_lms_activities", "type": "multiple", "description": "Select the LMS activities to be enabled for this school.", "group": "lms", "options": ["learning_context", "learningObjectiveType", "learning_intention", "skillType", "success_criteria", "beginning_plan", "middle_plan", "end_plan", "extended_learning", "contingency_plan", "resourceType", "book_resourceType", "additional_information", "assessmentType"]}, {"name": "refund_trigger_notification_parent", "type": "boolean", "description": "Refund notification to parent", "group": "fees", "default": "1"}, {"name": "student_task_module_name", "type": "string", "description": "Name to be displayed in Student Task module for parent & Staff", "group": "student task", "default": "Student Task"}, {"name": "push_notification_activity_name", "type": "string", "description": "Specifies the activity name or identifier used when sending push notifications through the notification service.", "group": "communication", "default": "com.nextelement.npsagara.NPSWebView"}, {"name": "push_notification_key", "type": "json", "description": "This is for pushnotification.", "group": "communication", "default": "AAAAUDz1VNc:APA91bElEEr8l1ZjP-SFou4Eop-qJODkK-4ptJ5oBCWWVydV0TO3qqbU9TgNk1PJyljipj-u2ttbm_ffsqdZ8JRtbt-RycVuhpFbzux07owjZcWcgzvgYIOEuQWrmLJnCrZeeYyOPcjH"}, {"name": "disable_staff_attendance_auto_checkout", "type": "boolean", "description": "0 to disable, 1 to enable the staff auto checkout", "group": "staff attendance", "default": "0"}, {"name": "staff_attendance_default_auto_checkout_time", "type": "json", "description": "This is for staff attendance auto checkout time.", "group": "staff attendance", "default": "{\"startHours\":\"8\",\"startMinutes\":\"30\",\"endHours\":\"16\",\"endMinutes\":\"30\"}"}, {"name": "student_task_publish_time_default", "type": "string", "description": "Set what is the default selected publish type for school. Choose from- Immediate, Later", "group": "student task", "default": "Immediate"}, {"name": "Student_attendance_backfill_enable", "type": "boolean", "description": "0 to disable, 1 to enable the student attendance Backfill", "group": "student attendance", "default": "0"}, {"name": "enable_fee_paying_students_count_in_widget", "type": "boolean", "description": "Enable to show the fee paying students in student statistics widget in dashboard", "group": "student", "default": 0}, {"name": "staff_profile_family_details_instruction", "type": "string", "description": "Displays an instructional note in the Family Details section of the staff profile to guide data entry.", "group": "staff", "default": "Family Details Instruction"}, {"name": "enable_fee_dashboard_inventory_expense_view", "type": "boolean", "description": "in Fee dashboard inventory expenses view", "group": "fees", "default": 0}, {"name": "staff_enable_primary_instance_filter_in_view", "type": "boolean", "description": "Adds a filter for 'Is Primary Instance' in the View Staff section.", "group": "staff", "default": 0}, {"name": "enable_procurement_send_notification_email_to_parent", "type": "boolean", "description": "Send Emails and Notification To Parents About The Purchased / Returned Items", "group": "sales", "default": 0}, {"name": "payroll_require_password_for_staff_payslip_download", "type": "boolean", "description": "Requires staff to enter their password during the process of downloading their payslip for added security.", "group": "payroll", "default": 0}, {"name": "enable_staff_agreement_to_tds_payroll", "type": "boolean", "description": "Enables staff to agree or re-open their consent for TDS agreement in the payroll module.", "group": "payroll", "default": 0}, {"name": "is_semester_scheme_tally_report", "type": "boolean", "description": "To allow semester column.", "group": "fees", "default": 0}, {"name": "disable_fee_concession_remarks_parent_side", "type": "boolean", "description": "To disable fees concession remarks.", "group": "fees", "default": 0}, {"name": "subject_attendance_notify_late_absent_notification_template", "type": "string", "description": "Provide a message that needs to be used for Notification or SMS for absentees in subject attendance", "group": "student subject attendance", "default": "Your ward was %%student_name%% of %%class_section%% is %%action_type%% on %%date%% for the subject(s) %%subjects%%. Kindly ignore if you have already informed the school through email-%%school_name%% - NXTSMS"}, {"name": "subject_attendance_automatic_text_mode_to_use", "type": "string", "description": "Specify the text mode for subject attendance (sms, notification)", "group": "student subject attendance", "default": "notification"}, {"name": "State Wise PT", "type": "string", "description": "Please specify the state for accurate Professional Tax (PT) calculation. Currently, PT calculation is applicable only for the following states: Karnataka, Andhra Pradesh, Telangana, and Maharashtra. Please use the state names exactly as listed.", "group": "payroll", "default": "{\"state\":\"Karnataka\"}"}, {"name": "inventory_cart_receipt_number_set", "type": "string", "description": "Set receipt number format id for inventory cart", "group": "inventory", "default": "0"}, {"name": "staff_attendance_enable_face_check_in_blink", "type": "boolean", "description": "Enable staff face check-in Blink for attendance.", "group": "staff attendance", "default": 0}, {"name": "staff_attendance_enable_face_check_in_oval", "type": "boolean", "description": "Enable staff face check-in Oval for attendance.", "group": "staff attendance", "default": 0}, {"name": "staff_attendance_face_check_in_attempt_timeout", "description": "Enable staff face check-in Timeout for attendance.", "group": "staff attendance", "type": "string", "default": "10"}, {"name": "staff_attendance_face_register_image_quality", "description": "Enable staff face ID Quality for registration. Ensure that the quality range is between 10 to 35 only.", "group": "staff attendance", "type": "string", "default": "25"}, {"name": "staff_attendance_face_register_image_brightness", "description": "Enable staff face ID Brightness for registration. Ensure that the quality range is between 70 to 150 only.", "group": "staff attendance", "type": "string", "default": "115"}, {"name": "inventory_shoppingcart_donot_show_price", "type": "boolean", "description": "if enabled price is not visable in shopping cart module.", "group": "inventory", "default": 0}, {"name": "disable_current_fees_if_previous_balance_is_present", "type": "boolean", "description": "if enabled current fees collection disabled.", "group": "fees", "default": 0}, {"name": "staff_leaves_limit_previous_leave_apply_dates", "type": "string", "description": "At least 1 day has to be set, if set 0 it will become disable", "group": "staff leave", "default": "30"}, {"name": "staff_leaves_limit_next_leave_apply_dates", "type": "string", "description": "At least 1 day has to be set, if set 0 it will become disable", "group": "staff leave", "default": "30"}, {"name": "inventory_shopping_cart_guidelines", "type": "string", "description": "This guideline is visible in a popup in Place new order.", "group": "inventory", "default": "Guidelines:<br>1. Explore the product catalog to find school uniforms, books, or other essentials. Use the \"Add to Cart\" button to quickly add items while adjusting quantities as needed.<br>2. View your selected items in the cart with updated prices and quantities. Any changes you make are reflected instantly, ensuring you always see the correct total.<br>3. Proceed to checkout confidently with secure payment options. Choose between home delivery or school pickup, based on your convenience.<br>4. After purchase, track your orders and review past transactions through your account. This helps in reordering or managing receipts for school-related purchases."}, {"name": "inventory_shoppingcart_reserve_end_date", "type": "string", "description": "From date Enter YYYY-MM-DD format", "group": "inventory", "default": "2023-03-21"}, {"name": "enable_budget_send_email_to_staff", "type": "boolean", "description": "Send Emails and Notification To Staffs About Approval of The Budgets.", "group": "sales", "default": 0}, {"name": "staff_attendance_allow_cancel_till_days", "type": "string", "description": "At least 1 day has to be set, if set 0 it will become disable", "group": "staff attendance", "default": "30"}, {"name": "disable_acad_year_student_wallet_parent", "type": "boolean", "description": "Acad year filter will not be available for the parent.", "group": "student wallet", "default": 0}, {"name": "disable_acad_year_student_wallet_transcend", "type": "boolean", "description": "Acad year filter will not be available for the transcend.", "group": "student wallet", "default": 0}, {"name": "include_paisa_value_in_payroll_reports", "type": "boolean", "description": "Rounds off the amount values in payroll reports by excluding paisa values.", "group": "payroll", "default": 0}, {"name": "staff_attendance_mode", "type": "string", "description": "Staff attendance checkin modes - location_checkin, face_checkin and location_checkin_and_face_checkin", "group": "staff attendance", "default": "location_checkin"}, {"name": "staff_attendance_supported_platforms", "type": "string", "description": "Staff attendance checkin supported platforms - desktop, mobile_browser, mobile_app", "group": "staff attendance", "default": "[\"desktop\",\"mobile_browser\",\"mobile_app\"]"}, {"name": "examination_report_card_word_for_absent", "type": "string", "description": "Add Absent words for report card like AB,Ab,Abesnt,ab ", "group": "examination", "default": "AB"}, {"name": "enable_collect_fee_if_student_all_the_status", "type": "boolean", "description": "Enable button for collect fees if student alumni, rejected, ectc.", "group": "fees", "default": 0}, {"name": "include_guardian_in_student_communications", "type": "boolean", "description": "Enable this option to include guardians as recipients when sending circulars or emails to students.", "group": "communication", "default": 0}, {"name": "staff_profile_confirm_popup_description", "type": "string", "description": "This message will appear in staff login popup.", "group": "staff", "default": "Your staff profile needs to be updated and confirmed. Click on the go to profile button to start the process."}, {"name": "enable_tax_declaration_leave_travel_allowance_for_staff", "type": "boolean", "description": "Enables Leave Travel Allowance (LTA) option for staff during the tax declaration process.", "group": "payroll", "default": 0}, {"name": "accounts_team_email", "type": "string", "description": "This email is used to send email to HODs while performing any action in budget module.", "group": "procurement", "default": "manju<PERSON><EMAIL>"}, {"name": "cfo_email", "type": "string", "description": "This email is used to send email to Accounts team while performing any action in budget module.", "group": "procurement", "default": "<EMAIL>"}, {"name": "tc_request_notification_from_email", "type": "string", "description": "Specifies the from email id for the TC sent through mail", "group": "student", "default": ""}, {"name": "payroll_collect_perk_tax_mode", "type": "string", "description": "Specifies whether the perquisite tax is to be collected from the employee or borne by the employer. Use: employee or employer.", "group": "payroll", "default": "employee"}, {"name": "discount_calucate_based_balance", "type": "boolean", "description": "parent side fees collection discount calucate based balance amount if enabled", "group": "fees", "default": "1"}, {"name": "staff_payslip_generation_order_employee_code", "type": "boolean", "group": "payroll", "description": "Enables listing of staff by employee code during payroll payslip generation and approval.", "default": "0"}, {"name": "student_year_custom_fields", "type": "array", "description": "Specifies the custom fields", "group": "student", "default": "[{\"name\":\"sy_custom1\", \"value\":\"custom field 1\"}, {\"name\":\"sy_custom2\", \"value\":\"custom field 2\"}, {\"name\":\"sy_custom3\", \"value\":\"custom field 3\"},{\"name\":\"sy_custom4\", \"value\":\"custom field 4\"},{\"name\":\"sy_custom5\", \"value\":\"custom field 5\"}]"}, {"name": "parent_side_ekart_selection_category_options", "type": "string", "description": "Configure select options from all_mode and choose_mode", "group": "e<PERSON>t", "default": "all_mode"}, {"name": "staff_leaves_enable_number_of_leaves_edit", "type": "boolean", "group": "staff leave", "description": "To enable or disable staff leaves no. of days count to edit by staff", "default": "0"}, {"name": "admissions_ui_theme_color", "type": "array", "description": "Specify the color theme for the admissions UI", "group": "admissions", "default": "[{\"name\":\"primary_background_color\", \"value\":\"#04327f\"}, {\"name\":\"secondary_background_color\", \"value\":\"#ff5f00\"}, {\"name\":\"primary_font_color\", \"value\":\"white\"}, {\"name\":\"secondary_font_color\", \"value\":\"white\"}]"}, {"name": "enable_parent_side_pending_admission_flow", "type": "boolean", "group": "admissions", "description": "Enable Admission Flow V2 Fee, Student Profile, Documents, Consent Form.", "default": "0"}, {"name": "examination_use_alternate_job_server_ip", "type": "string", "group": "examination", "description": "If an IP is provided, it uses this IP as job server IP for examination PDF generation. If not, it uses the default IP.", "default": "0"}, {"name": "procurement_sales_enable_discount_feature", "type": "boolean", "group": "procurement", "description": "The procurement_sales_enable_discount_feature is a system configuration parameter that controls the availability of discount-related functionalities in procurement and sales operations. When enabled, it permits users to apply, manage, and process discounts on transactions, while disabling it restricts any discount-related actions within the system.", "default": "0"}, {"name": "idcard_order_number", "type": "string", "description": "Set order number format id for idcard", "group": "id cards", "default": "0"}, {"name": "staff_termination_enable_future_date_deactivation", "type": "boolean", "group": "staff", "description": "Upon enabling this the staff get terminated and deactivated on the last date of work if it is in the future.", "default": "0"}, {"name": "procurement_enable_multiple_category_in_sales", "type": "boolean", "group": "procurement", "description": "The procurement_enable_multiple_category_in_sales is a system configuration parameter that allows multiple-categories-items to be issued in a single trasaction. When enabled, it permits users to allow selecting multiple categories item in the sales window.", "default": "0"}, {"name": "staff_attendance_reminder_mode", "description": "Please mention how the reminder to staff regarding attendance check-in and check-out should be sent. Choose from the following options: Notification, Email, or Both. If the mode is set to Email or Both, please ensure an email template with the name `staffAttendanceReminderEmail` is added.", "group": "staff attendance", "type": "string", "default": "Notification"}, {"name": "fees_enable_online_payment_delete", "description": "If the payment mode is online, the delete option be enabled", "group": "fees", "type": "boolean", "default": "0"}, {"name": "procurement_vouchers_approvers", "type": "json", "description": "Defines the approval hierarchy for payment vouchers/payment advice, specifying required approvers at each level", "group": "procurement", "default": "{\"accounting\":\"1\", \"cfo\":\"2\", \"ceo\":\"3\"}"}, {"name": "classroom_chronicles_module_name", "type": "string", "group": "Classroom Chronicles", "description": "Changing this will update the name of the Classroom Chronicles module everywhere it appears across the system.", "default": "Classroom Chronicles"}]