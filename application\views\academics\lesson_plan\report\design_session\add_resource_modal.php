<div class="modal fade" id="add_resources" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 80%; margin-top: 1% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius: 1rem;border-top-left-radius: 1rem;">
                <h4 class="modal-title">List of Resources <b>Grade (<span id="grade"></span>)</b></h4>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body pt-0">
                <div class="card-body" id="resources_filter">
                    <div class="row">
                        <div class="col-md-3 pl-1">
                            <label>Select Resource Type <font style="color: red;">*</font></label>
                            <select class="form-control" name="resource_type_modal" id="resource_type_modal" onchange="getResources()">
                                <?php if(!empty($resource_types)) {?>
                                    <option value="all">All Types</option>
                                    <?php
                                    foreach ($resource_types as $key => $value) {
                                        echo '<option value="' . $value->resource_type . '">' . ucwords($value->resource_type) . '</option>';
                                    }
                                    ?>
                                <?php } else { ?>
                                    <option disabled selected>Please Add Resource Types In Config</option>
                                <?php } ?>
                            </select>
                            <div style="position: absolute; right: 22px; top: 69%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                        <div class="col-md-3 pl-1">
                            <label>Subject Name <font style="color: red;">*</font></label>
                            <select class="form-control" name="resource_subject_id" id="resource_subject_id" onchange="getSubjectResources()">
                            </select>
                            <div style="position: absolute; right: 25px; top: 69%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 1.5rem" id="addedResources">
                    <label class="mb-1 pl-1"><b>Added Resource(s)</b> <font style="color: red;">*</font></label>
                    <div class="card-body" id="temp_selected_resources" style="border: 1px solid #ccc;border-radius: .8rem;min-height: 5rem;">
                        Start By Selecting Resources From Below
                    </div>
                    <span id="resourcesError" style="display: none;"></span>
                </div>
                
                <div id="resources">
                    <label class="mb-1 pl-1"><b>Resource(s)</b></label>
                    <div class="card-body p-2" id="resources_body" style="height:23rem; overflow-y: scroll;border: 1px solid #ccc;border-radius: .8rem;margin-bottom: 1.5rem;">
                    </div>
                </div>

                <div class="form-check form-switch pl-1" id="makeVisibleToStudents">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_resources_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_resources_to_students">Make visible to students</label>
                </div>
                <div id="noresources">
                    <div class="no-data-display mt-3">No Resources Found. Please Add Resources Before Proceeding</div>
                </div>
            </div>
            <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: 1rem;border-bottom-right-radius: 1rem;">
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $("#add_resources").on("shown.bs.modal", e => {
        get_subject_name_by_lesson_id();
        getResources();
    })

    function get_subject_name_by_lesson_id() {
        const lesson_id = $("#select_lesson").val();
        if(lesson_id == '') {
            $("#resource_subject_id").html('<option disabled selected>No Subject Found</option>');
        };
        $.ajax({
            url: '<?php echo site_url('academics/Lesson_plan/get_subject_name_by_lesson_id'); ?>',
            data: { lesson_id },
            type: "post",
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData && Object.keys(parsedData).length < 0) {
                    $("#resource_subject_id").html('<option disabled selected>No Subject Found</option>');
                    return;
                }
                $("#grade").text(parsedData.class_name);
                let option = `<option value="${parsedData.id}">${parsedData.subject_name}</option>`;
                $("#resource_subject_id").html(option);
            }
        })
    }

    var selected_resources_count = 0;

    function getResources() {
        $("#resources").show();
        $('#resources_body').html('<div class="no-data-display">Loading...</div>').css('height', 'auto');
        var subject_id = $("#select_subject").val();
        var resource_type = $("#resource_type_modal").val();
        var class_name = '<?php echo $class_name ?>';

        $.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResources'); ?>',
            data: { 'resource_type': resource_type, 'class_name': class_name, 'subject_id': subject_id },
            type: "post",
            success: function (data) {
                let parsedData = JSON.parse(data);
                if (parsedData.resources && parsedData.resources.length < 0) {
                    $("#addedResources").hide();
                    $("#resources_filter").hide();
                    $("#makeVisibleToStudents").hide();
                    $("#resources").hide();
                    $('#resources_body').html('');
                    $("#noresources").show();
                    $("#btns_modal").html('<a class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</a>');
                    return;
                }
                $("#noresources").hide();
                $("#resources").show();
                $("#makeVisibleToStudents").show();
                $("#resources_filter").show();
                $("#addedResources").show();
                var resources = parsedData.resources;
                var resourcesList = '';
                for (var i = 0; i < resources.length; i++) {
                    var date = moment(resources[i].created_on).format('DD-MM-YYYY');
                    resourcesList += `<div class="col-md-4" style="padding:5px;">
                        <div class="names">
                            <div style="width: 85%;padding: 5px 10px;">
                            <b>Name : </b>${resources[i].name}<br>
                            <b>Type : </b>${resources[i].resource_type}<br>
                            <b>Date : </b>${date}<br>
                            <b>Subject : </b>${resources[i].subject_name}
                            </div>
                            <div style="width: 10%;">
                                <a class="new_circleShape_buttons" onclick="oneStepResources('${resources[i].id}','${resources[i].name}')" style="cursor:pointer;background-color:#fe970a;color:white;padding: .35rem 1rem;position:absolute;">
                                    <span class="fa fa-plus" style="position: absolute;line-height: 3rem;top: 50%;left: 50%;transform: translate(-50%, -50%);"></span>
                                </a>
                            </div>
                        </div>
                    </div>`;
                }
                var btns_list = `<a class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</a>
                                <button class="btn btn-primary mt-0" onclick="confirmResources()">Update</button>`;
                $("#resources_body").html(resourcesList);
                $("#btns_modal").html(btns_list);
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function oneStepResources(id, name) {
        if (selected_resources_count == 0) {
            $("#temp_selected_resources").html('');
        }
        var html = '';
        var html_main = '';
        if ($("#temp_add_btn_" + id).length == 0) {
            if (selected_resources_count >= 5) {
                bootbox.dialog({
                    title: "Warning....",
                    message: "<h4><center>You can select maximum of FIVE resources only.</center></h4>",
                    className: "dialogWide",
                    buttons: {
                        ok: {
                            label: "Ok",
                            className: 'btn btn-primary'
                        }
                    }
                });
            } else {
                selected_resources_count = selected_resources_count + 1;
                html += `<div id="temp_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_class"><input type="hidden" class="resourcesIds" name="resource_ids[]" value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
                html_main += `<div id="main_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_main_class"><input type="hidden" name="main_resource_ids[]"  value="${id}">${name}&nbsp;&nbsp;<span class="fa fa-times remove"></span></div>`;
                $("#temp_selected_resources").append(html);
            }
        }
    }

    function removeOneStepResource(div_id) {
        $("#temp_" + div_id).remove();
        $("#main_" + div_id).remove();
        selected_resources_count = selected_resources_count - 1;
        if (selected_resources_count == 0) {
            $("#temp_selected_resources").html('Start By Selecting Resources From Below');
        }
    }

    function confirmResources() {
        var resources_ids = [];
        $(".resourcesIds").each(function () {
            resources_ids.push($(this).val());
        });
        $("#resourcesError").text("").hide();
        if (resources_ids.length == 0) {
            $("#resourcesError").text("Please Select From The Available Resources Below.").css("color", "red").show();
            return;
        }

        const visible_to_students = $("#visible_resources_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/save_resources_details') ?>',
            type: 'post',
            data: { session_id, resources_ids, visible_to_students },
            success: function (data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $('#add_resources').modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(function () {
                        getSessionData(session_id);
                        $("#temp_selected_resources").html("Start By Selecting Resources From Below");
                        selected_resources_count = 0;
                        showMainModal();
                    });
                } else {
                    $('#add_resources').modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed",
                        text: "Failed to save activity!",
                    }).then(function () {
                        $('#add_resources').modal('show');
                    });
                }
            },
            error: function (err) {
                console.log(err);
                $('#add_resources').modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed",
                    text: "Failed to save activity!",
                }).then(function () {
                    $('#add_resources').modal('show');
                });
            }
        });
    }
</script>

<style type="text/css">
    #video-player {
        object-fit: cover;
        width: 100%;
        height: 500px;
    }

    .resources_class {
        padding: .4rem 1.4rem;
        border-radius: 20rem;
        margin: 3px;
        display: inline-block;
        cursor: pointer;
        background: #e0f1ff;
        color: #000000;
    }

    .resources_main_class {
        padding: .4rem 1.4rem;
        border-radius: 20rem;
        margin: 3px;
        display: inline-block;
        cursor: pointer;
        background: #e0f1ff;
        color: #000000;
    }

    .names {
        border: 1px solid #ccc;
        margin-bottom: .5rem;
        border-radius: 10px;
        display: flex;
        height: 8rem;
        overflow: auto;
        padding: .5rem 0.2rem;
    }

    .dialogWide>.modal-dialog {
        width: 50% !important;
        margin-left: 25%;
    }

    .list-group-item {
        margin-bottom: 1px;
    }

    .label-default,
    .label-success,
    .label-danger {
        cursor: pointer;
    }

    .list-group-item.active {
        background-color: #ebf3f9;
        border-color: #ebf3f9;
        color: #737373;
    }

    .list-group-item.active,
    .list-group-item.active:hover,
    .list-group-item.active:focus {
        background: #ebf3f9;
        color: #737373;
    }

    .list-group-item {
        border: none;
    }

    .loaderclass {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 30%;
        margin-left: 40%;
        position: absolute;
        z-index: 99999;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .active {
        background: #6893ca;
    }

    .discard {
        background: #C82333;
    }

    .new_circleShape_buttons {
        padding: .35rem .55rem;
        border-radius: 50%;
        font-size: 16px;
        height: 3rem;
        width: 3rem;
        text-align: center;
        vertical-align: middle;
        box-shadow: 0px 2px 8px #ccc;
        cursor: pointer;
    }

    .borderless thead tr th,
    .borderless tbody tr td {
        border: none;
    }
</style>