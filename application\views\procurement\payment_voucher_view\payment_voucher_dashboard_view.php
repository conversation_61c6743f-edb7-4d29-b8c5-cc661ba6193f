<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li>Payment Voucher Dashboard </li>
</ul>

<div>
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/requisition_controller_v2') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Payment Voucher Dashboard
                    </h3>
                    <button onclick="show_voucher_documentation()" class="btn btn-secondary pull-right">Documentation</button>
                </div>
            </div>
        </div>

        <div class="panel-body">
            <div class="col-md-12">
                <div class="col-md-3">
                    <a href="<?php echo site_url('procurement/Payment_voucher_controller/manage_payment_vouchers') ?>">
                    <div class="widget widget-default widget-item-icon new_height">
                        <div class="widget-item-left" style="width:52px;">
                        <span class="animate__animated animate__fadeIn">
                            <?php $this->load->view('svg_icons/add.svg') ?>
                        </span>
                        </div>
                        <div class="widget-data" style="margin: 18px auto;">
                        <div class="h3">Payment Vouchers</div>
                        </div>
                    </div>
                    </a>
                </div>
            </div>

             <div class="col-md-12">
                <div class="m-0 d-flex">
                    <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Reports</p></div>
                    <div class="mt-1 flex-fill"><hr></div>
                </div>
            </div>
            <div class="col-md-12"> 
                <div class="col-md-3">
                    <a href="<?php echo site_url('') ?>">
                    <div class="widget widget-default widget-item-icon new_height">
                        <div class="widget-item-left" style="width:52px;">
                        <span class="animate__animated animate__fadeIn">
                            <?php $this->load->view('svg_icons/assessment.svg') ?>
                        </span>
                        </div>
                        <div class="widget-data" style="margin: 18px auto;">
                        <div class="h3">Test Report</div>
                        </div>
                    </div>
                    </a>
                </div>
            </div>

        </div>

    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    function show_voucher_documentation() {
        Swal.fire({
            html: `
            
            
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --secondary:#f8f8ff;
            --accent: #4895ef;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }
        
        body {
            background-color: #f5f7ff;
            color: var(--dark);
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        li {
            list-style-type: arrow;
            margin-left: 20px;
            text-align: left;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 0 0 40px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        h2 {
            font-size: 2rem;
            color: var(--primary);
            margin: 2rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--light-gray);
        }
        
        h3 {
            font-size: 1.5rem;
            color: var(--secondary);
            margin: 1.5rem 0 1rem;
        }
        
        p {
            margin-bottom: 1rem;
        }
        
        .intro {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 2rem 0;
        }
        
        .feature-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .workflow-img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 2rem auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }
        
        th {
            background-color: var(--primary);
            color: white;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .steps {
            margin: 2rem 0;
            padding-left: 1.5rem;
        }
        
        .steps li {
            margin-bottom: 1rem;
        }
        
        .mermaid {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .integration-diagram {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .faq {
            margin: 2rem 0;
        }
        
        .faq-item {
            margin-bottom: 1.5rem;
        }
        
        .faq-question {
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .cta-buttons {
            display: flex;
            gap: 15px;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
            border: 2px solid var(--primary);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary);
            color: white;
        }
        
        footer {
            background-color: var(--dark);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            h2 {
                font-size: 1.75rem;
            }
            
            .cta-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
    <header>
        <div class="container">
            <h1>Payment Advice & Release Voucher Module</h1>
            <p class="intro">ProcurePay - Integrated Payment Processing System</p>
        </div>
    </header>
    
    <div class="container">
        <section id="overview">
            <h2>1. Module Overview</h2>
            <p>The Payment Voucher & Release Module automates the payment process after invoice approval, handling:</p>
            <ul>
                <li>Payment voucher creation</li>
                <li>TDS and tax calculations</li>
                <li>Multi-mode payment processing</li>
                <li>Approval workflows</li>
                <li>Accounting integration</li>
            </ul>
            
            <h3>Integration with Invoice Management</h3>
            <div class="integration-diagram">
                <div class="mermaid">
                    graph LR
                        A[Invoice Created] --> B[Invoice Approved]
                        B --> C{Payment Type?}
                        C -->|Regular| D[Create Payment Voucher]
                        C -->|Advance| E[Create Advance Voucher]
                        C -->|Milestone| F[Create PO Payment Voucher]
                        D --> G[Payment Processing]
                        E --> G
                        F --> G
                        G --> H[Update Invoice Status]
                </div>
            </div>
        </section>
        
        <section id="database-schema">
            <h2>2. Database Structure</h2>
            
            <h3>Core Tables</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table</th>
                        <th>Purpose</th>
                        <th>Key Fields</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>procurement_payment_vouchers</code></td>
                        <td>Master voucher records</td>
                        <td>voucher_number, invoice_id, payment_status, net_payable</td>
                    </tr>
                    <tr>
                        <td><code>procurement_payment_voucher_items</code></td>
                        <td>Line item details</td>
                        <td>voucher_id, amount, tds_amount, account_code</td>
                    </tr>
                    <tr>
                        <td><code>procurement_payment_transactions</code></td>
                        <td>Payment execution records</td>
                        <td>voucher_id, payment_reference, transaction_status</td>
                    </tr>
                    <tr>
                        <td><code>procurement_tds_deductions</code></td>
                        <td>TDS tracking</td>
                        <td>section_code, amount, quarter, filing_status</td>
                    </tr>
                </tbody>
            </table>
        </section>
        
        <section id="workflows">
            <h2>3. Key Workflows</h2>
            
            <h3>Voucher Creation Flow</h3>
            <div class="mermaid">
                graph TD
                    A[Trigger: Invoice Approved] --> B{Determine Voucher Type}
                    B -->|Regular Invoice| C[Calculate TDS/Taxes]
                    B -->|PO Milestone| D[Verify Milestone Completion]
                    B -->|Petty Cash| E[Verify Receipts]
                    C --> F[Generate Voucher Items]
                    D --> F
                    E --> F
                    F --> G[Submit for Approval]
            </div>
            
            <h3>Payment Release Flow</h3>
            <div class="mermaid">
                graph TD
                    A[Approved Voucher] --> B{Payment Mode}
                    B -->|Bank Transfer| C[Generate Payment File]
                    B -->|Cheque| D[Print Cheque]
                    B -->|Petty Cash| E[Dispense Cash]
                    C --> F[Submit to Bank]
                    D --> G[Handover to Vendor]
                    E --> H[Obtain Receipt]
                    F --> I[Update Transaction Status]
                    G --> I
                    H --> I
            </div>
        </section>
        
        <section id="features">
            <h2>4. Key Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>TDS Management</h4>
                    <ul>
                        <li>Automatic TDS calculation</li>
                        <li>Section code mapping</li>
                        <li>Quarterly reporting</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Tax Handling</h4>
                    <ul>
                        <li>GST compliance</li>
                        <li>TCS support</li>
                        <li>Tax reconciliation</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Payment Modes</h4>
                    <ul>
                        <li>Bank transfers</li>
                        <li>Cheque processing</li>
                        <li>Petty cash management</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Approval Workflows</h4>
                    <ul>
                        <li>Amount-based routing</li>
                        <li>Delegation support</li>
                        <li>Mobile approvals</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <section id="integration">
            <h2>5. Integration with Invoice Management</h2>
            
            <h3>Data Flow</h3>
            <ol class="steps">
                <li><strong>Invoice Approval</strong> triggers voucher creation</li>
                <li><strong>Voucher</strong> pulls invoice details (vendor, amounts, taxes)</li>
                <li><strong>Payment Status</strong> updates back to invoice</li>
                <li><strong>TDS Certificates</strong> generated and linked to both systems</li>
            </ol>
            
            <h3>Status Synchronization</h3>
            <table>
                <thead>
                    <tr>
                        <th>Voucher Status</th>
                        <th>Invoice Status Update</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Voucher Created</td>
                        <td>Payment Processing</td>
                    </tr>
                    <tr>
                        <td>Payment Approved</td>
                        <td>Payment Approved</td>
                    </tr>
                    <tr>
                        <td>Payment Completed</td>
                        <td>Paid</td>
                    </tr>
                    <tr>
                        <td>Payment Failed</td>
                        <td>Payment Failed</td>
                    </tr>
                </tbody>
            </table>
        </section>
        
      
        
        <section id="faq" class="faq">
            <h2>6. Frequently Asked Questions</h2>
            
            <div class="faq-item">
                <p class="faq-question">Q: How are partial payments handled?</p>
                <p>A: The system allows multiple vouchers against a single invoice, each with their own payment status and accounting entries.</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: Can we change TDS rates after voucher creation?</p>
                <p>A: Yes, but it requires re-approval if the voucher was already approved. An audit trail is maintained.</p>
            </div>
            
            <div class="faq-item">
                <p class="faq-question">Q: How does petty cash reconciliation work?</p>
                <p>A: Petty cash vouchers require receipt uploads and are reconciled against monthly budgets with alerts for overspending.</p>
            </div>
        </section>
    </div>
    
    <footer>
        <div class="container">
            <p><strong>Module Version:</strong> 1.0.0<br>
            <strong>Last Updated:</strong> May 01, 2025</p>
        </div>
    </footer>
   
    `,
            confirmButtonText: 'Got it!',
            customClass: 'swal-width'
        });
    }

    mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
</script>


<style>
    .swal-width {
        width: 97% !important;
        text-align: left !important;
    }
</style>