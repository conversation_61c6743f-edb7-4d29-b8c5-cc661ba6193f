<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/sales_controller_v2');?>">Inventory Sales</a></li>
    <li class="active">Category Wise Student's Sales Report</li>
</ul>

<div class="col-md-12">
    <div class="panel cd_border" style="border: 1px solid #e5e5e5;">
        <div class="card-header panel_heading_new_style_staff_border" style="margin: 5px 10px 10px 10px; padding-bottom: 15px; border-bottom: 1px solid lightgrey;">
            <div class="" style="margin: 0px">
                <h3>
                    <a style="" class="back_anchor" href="<?php echo site_url('procurement/sales_controller_v2') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Category Wise Student's Sales Report
                    <button style="margin-left: 5px; display: none;" id="plus_f" onclick="show_filter()" class="btn btn-secondary pull-right"><span style="" class="fa fa-plus"></span> Filter</button>
                    <button id="minus_f" style="" onclick="hide_filter()" class="btn btn-secondary pull-right"><span style="" class="fa fa-minus"></span> Filter</button>
                    
                </h3>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-12" id="filters_div">
                <div class="form-group col-md-2">
                    <label for="report_format">Format of Report<font style="color: red; font-weight: bolder; font-size: larger;">*</font></label>
                    <select name="report_format" id="report_format" class="form-control select2" required onchange="onchange_format()">
                        <option value="category-wise">Category/Item Wise</option>
                        <option value="student-wise">Student Wise</option>
                    </select>
                </div>


                <div class="form-group col-md-1">
                    <label for="sales_year_id">Sales Year <font style="color: red; font-weight: bolder; font-size: larger;">*</font></label>
                    <select name="sales_year_id" id="sales_year_id" class="form-control select2" required>
                        <option value="">Select...</option>
                        <?php
                            if(!empty($salesYear)) {
                                foreach($salesYear as $key => $val) {
                                    $selected= '';
                                    if($val->is_active == 1) $selected= 'selected';
                                    echo "<option $selected value='$val->id'>$val->year_name</option>";
                                }
                            }
                        ?>
                    </select>
                </div>


                <span id="category-span">
                    <div class="form-group col-md-3">
                        <label for="category">Select Category <font style="color: red; font-weight: bolder; font-size: larger;">*</font></label>
                        <select name="category" id="category" class="form-control select2" onchange="onchange_category()" required>
                            <option value="">Select...</option>
                            <?php
                                if(!empty($category)) {
                                    foreach($category as $key => $val) {
                                        echo "<option value='$val->category_id'>$val->category_name</option>";
                                    }
                                }
                            ?>
                        </select>
                    </div>
                </span>
                <div class="col-md-3 form-group">
                    <label for="subcategories">Select Sub Categories</label>
                    <select name="subcategories" id="subcategories" class="form-control select2" multiple onchange="get_items()">

                    </select>
                </div>
                <div class="col-md-3 form-group">
                    <label for="items">Select Items</label>
                    <select name="items" id="items" class="form-control select2" multiple>

                    </select>
                </div>
                <div style="width: 100%;">
                    <center>
                        <button id="getbtn" class="btn btn-info" style="min-width: 150px;" onclick="get_reports()">Get Report</button>
                        <button id="getbtn_for_UI" class="btn btn-info" style="min-width: 150px; display: none;">Get Report</button>
                    </center>
                </div>
            </div>
            <div class="col-md-12" id="pleasewait_div" style="display: none;">
                
            </div>

            <div class="col-md-12" style="height: 20px;;"></div>
            <div class="col-md-12" style="margin-top: 10px; display: none;" id="reports_div">
                
            </div>

        </div>
    </div>
</div>


        
    

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    $(document).ready(() => {
        $(".select2").select2();
    });

    function onchange_format() {
        var report_format= $("#report_format").val();
        $("#items").html('');
            $("#subcategories").html('');
        if(report_format == 'student-wise') {
            $("#items").siblings("span.select2").css('pointer-events', 'none').prop('readonly', true);
            $("#subcategories").siblings("span.select2").css('pointer-events', 'none').prop('readonly', true);
            
            $("#category-span").html(`
                    <div class="form-group col-md-3">
                        <label for="category">Select Category <font style="color: red; font-weight: bolder; font-size: larger;">*</font></label>
                        <select name="category" id="category" class="form-control select2" onchange="onchange_category()" required multiple>
                            <?php
                                if(!empty($category)) {
                                    foreach($category as $key => $val) {
                                        echo "<option value='$val->category_id'>$val->category_name</option>";
                                    }
                                }
                            ?>
                        </select>
                    </div>
                    `);
        } else {
            $("#items").siblings("span.select2").css('pointer-events', 'auto').prop('readonly', false);
            $("#subcategories").siblings("span.select2").css('pointer-events', 'auto').prop('readonly', false);
            $("#category-span").html(`
                    <div class="form-group col-md-3">
                        <label for="category">Select Category <font style="color: red; font-weight: bolder; font-size: larger;">*</font></label>
                        <select name="category" id="category" class="form-control select2" onchange="onchange_category()" required>
                            <option value="">Select...</option>
                            <?php
                                if(!empty($category)) {
                                    foreach($category as $key => $val) {
                                        echo "<option value='$val->category_id'>$val->category_name</option>";
                                    }
                                }
                            ?>
                        </select>
                    </div>
                    `);
        }
    }

    function get_reports() {
        let category= $("#category").val() || 0;
        let report_format= $("#report_format").val() || 'category-wise';
        // console.log(category); return;
        let subcategories= $("#subcategories").val() || 0;
        let items= $("#items").val() || 0;
        if(!items[0]) {
            items= 0;
        }
        var sales_year_id= $("#sales_year_id").val();
        if(!sales_year_id) {
           return alert('Sales year is not defined');
        }
        if(category && category != 0) {
            $("#getbtn").hide().prop('disabled', true).html('Searching inputs... Please Wait!');
            $("#reports_div").hide();
            $("#getbtn_for_UI").show().prop('disabled', true).html('Please Wait! ... ');
            $("#pleasewait_div").show().html(`<div class="spinner-container">
                <div class="spinner"></div>
            </div>`);
            $.ajax({
                url: '<?php echo site_url('procurement/sales_controller_v2/get_items_and_students_list'); ?>',
                type: "post",
                data: {category, subcategories, items, sales_year_id, report_format},
                success(data) {
                    var p_data = JSON.parse(data);
                    get_items_links_to_student(p_data, category, subcategories, items, report_format);
                }
            });
        }
    }

    function chunkArray(arr, chunkSize) {
        // Initialize empty array to hold chunks
        const chunks = [];

        // Loop through the array, incrementing by chunk size each iteration
        for (let i = 0; i < arr.length; i += chunkSize) {
            // Slice the array to get a chunk of size chunkSize from position i
            const chunk = arr.slice(i, i + chunkSize);
            // Push the chunk to the chunks array
            chunks.push(chunk);
        }

        // Return the array of chunks
        return chunks;
    }

    function asyncOperation_data(student_arr, category1, subcategories1, items1, j, chunc_std_len, report_format) {

        
        return new Promise((resolve, reject) => {
            // Simulating an asynchronous operation (e.g., fetching data from a server)
            setTimeout(() => {
                let category= $("#category").val() || 0;
                let subcategories= $("#subcategories").val() || 0;
                let items= $("#items").val() || 0;
                if(!items[0]) {
                    items= 0;
                }
                // Resolve the promise after the operation is done
                var sales_year_id= $("#sales_year_id").val();
                $.ajax({
                    url: '<?php  echo site_url('procurement/sales_controller_v2/get_item_wise_sales_report'); ?>',
                    type: 'post',
                    data: {student_arr, category, subcategories, items, sales_year_id, report_format},
                    // async: true,
                    success: function(data) {
                        // console.log(data);
                        if(report_format == 'student-wise') {
                            construct_student_wise_item_list_student_wise(JSON.parse(data), j, chunc_std_len);
                        } else {
                            construct_student_wise_item_list(JSON.parse(data), j, chunc_std_len);
                        }
                        resolve();
                    },
                    error: function(err) {
                        console.log(err);
                        reject();
                    }
                });
            }, 300); // Simulating varying processing times
        });
    }

    function asyncOperation(item_obj, student_arr, i) {
        
        var item_id= item_obj.item_id;
        if(i == 0) {
           var times = 10;
        } else {
           var times= 500;
        }
        return new Promise((resolve, reject) => {
            // Simulating an asynchronous operation (e.g., fetching data from a server)
            setTimeout(() => {
                // Resolve the promise after the operation is done
                var $form = $(`#one_form_${i}`);
                $.ajax({
                    url: '<?php  echo site_url('procurement/sales_controller_v2/get_item_wise_sales_report'); ?>',
                    type: 'post',
                    data: {item_id, student_arr},
                    // async: true,
                    success: function(data) {
                        // console.log(data);
                        display_std_wise_table(JSON.parse(data), item_obj);

                        resolve();
                    },
                    error: function(err) {
                        console.log(err);
                        reject();
                    }
                });
            }, times); // Simulating varying processing times
        });
    }

    let sn= 1;
    let t_amt= 0;
    let t_qty= 0;
    function construct_student_wise_item_list(data, j, chunc_std_len){
        if(j == 0) {
            sn = 1;
            t_amt= 0;
            t_qty= 0;
        } 
        var tables= ``;
        
        for(var i in data) {

            let promotion_status= data[i].promotion_status;
            let studentN= data[i].name;
            if(promotion_status == '4' || promotion_status == '5') {
                studentN= '<font color="red">' + studentN + ' - Alumini</font>';
            }

            t_amt += +data[i].final_amount;
            t_qty += +data[i].final_quantity;
               tables += `<tr>
                            <td>${sn++}</td>
                            <td>${data[i].admission_no}</td>
                            <td>${data[i].enrollment_number || '-'}</td>
                            <td>${studentN}</td>
                            <td>${data[i].class_name} - ${data[i].section_name}</td>
                            <td>${data[i].category_name}</td>
                            <td>${data[i].subcategory_name}</td>
                            <td>${data[i].item_name}</td>
                            <td>${data[i].final_quantity}</td>
                            <td>${data[i].final_amount}</td>
                        </tr>`;
        }
        if(j == 0) {
            var header= `<div class="col-md-6" id="sammari">     </div>
                        <table class="table table-bordered table-responsive" id="table_id1">
                            <thead>
                                <tr>
                                    <th style="min-width: 40px;">#</th>
                                    <th style="min-width: 120px;">Admission No</th>
                                    <th style="min-width: 135px;">Enrollment No</th>
                                    <th style="min-width: 130px;">Student Name</th>
                                    <th style="min-width: 130px;">Class-Section</th>
                                    <th style="min-width: 100px;">Category</th>
                                    <th style="min-width: 120px;">Sub Category</th>
                                    <th style="min-width: 200px;">Item</th>
                                    <th style="min-width: 120px;">Total Quantity</th>
                                    <th style="min-width: 120px;">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody id="display_table_data">
                            </tbody>
                        </table>`;

            // $("#display_table_data").html('');
            $("#reports_div").html(header).show();
            setTimeout(() => {
                $("#display_table_data").html(tables);
            }, 100);

            $("#getbtn").prop('disabled', false).html('Get Report');
            $("#getbtn_for_UI").html('Still Fetching Some More Rows.... Please Wait !');
            $("#pleasewait_div").hide();

        } else {
            $("#display_table_data").append(tables);
        }
        if(j == chunc_std_len - 1) {
           setTimeout(() => {
                var summry= `
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th>Total Quantity</th>
                                            <td>${t_qty}</td>
                                        </tr>
                                        <tr>
                                            <th>Total Amount</th>
                                            <td>${t_amt.toFixed(2)}</td>
                                        </tr>
                                    </tbody>
                                </table>
                        `;
                $("#sammari").html(summry);

            $('#table_id1').DataTable( {
                "language": {
                        "search": "",
                        "searchPlaceholder": "Enter Search..."
                    },
                    "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                    "pageLength": 10,
                    dom: 'lBfrtip',
                    buttons: [
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            filename: 'category_wise_student_sales_report',
                            className: 'btn btn-info'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            filename: 'category_wise_student_sales_report',
                            className: 'btn btn-info'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            filename: 'category_wise_student_sales_report',
                            className: 'btn btn-info'
                        }
                    ]
                } );

                $("#getbtn").show().prop('disabled', false).html('Get Report');
                $("#getbtn_for_UI, #pleasewait_div").hide();
                $("#pleasewait_div").hide();
                $("#reports_div").show();
           }, 1000);
    }
    }

    function display_std_wise_table(data, item) {
        var tables= `<table class="table table-bordered" id="table_id">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Admission No</th>
                                <th>Enrollment No</th>
                                <th>Student Name</th>
                                <th>Class - Section</th>
                                <th>Category</th>
                                <th>Sub Category</th>
                                <th>Item</th>
                                <th>Total Quantity</th>
                                <th>Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>`;
        var sn= 1;
        for(var i in data) {
            // for(var v of data[i].students) {
               tables += `<tr>
                            <td>${sn++}</td>
                            <td>${data[i].admission_no}</td>
                            <td>${data[i].enrollment_number || '-'}</td>
                            <td>${data[i].name}</td>
                            <td>${data[i].class_name} - ${data[i].section_name}</td>
                            <td>${item.category_name}</td>
                            <td>${item.subcategory_name}</td>
                            <td>${item.item_name}</td>
                            <td>${data[i].final_quantity}</td>
                            <td>${data[i].final_amount}</td>
                        </tr>`;
            // }
        }
        tables += `</tbody>
                </table>`;
        $("#reports_div").append(tables).show();
        $('#table_id').DataTable( {
        "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
            "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: 'category_wise_student_sales_report',
                    className: 'btn btn-info'
                },
                {
                    extend: 'print',
                    text: 'Print',
                    filename: 'category_wise_student_sales_report',
                    className: 'btn btn-info'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: 'category_wise_student_sales_report',
                    className: 'btn btn-info'
                }
            ]
        } );
    }

    
    async function get_items_links_to_student(students, category, subcategories, items, report_format) {
        let chunkedStudents = chunkArray(students, 100);
        for(var j= 0; j<chunkedStudents.length; j++) {
            try {
                const result = await asyncOperation_data(chunkedStudents[j], category, subcategories, items, j, chunkedStudents.length, report_format);
            } catch (error) {
                console.error(error);
            }
        }

    }

    async function get_item_links_to_student(items, students) {
        let chunkedStudents = chunkArray(students, 100);
        for(var i= 0; i<items.length; i++) {
            var student_ids= [];
            for(var j= 0; j<chunkedStudents.length; j++) {
                try {
                    const result = await asyncOperation(items[i], chunkedStudents[j], i);
// return;
                } catch (error) {
                    console.error(error);
                }
            }
        }

    }

    // function get_reports() {
    //     let category= $("#category").val() || 0;
    //     let subcategories= $("#subcategories").val() || 0;
    //     let items= $("#items").val() || 0;
    //     if(!items[0]) {
    //         items= 0;
    //     }
    //     if(category && category != 0) {
    //         $("#getbtn").hide().prop('disabled', true).html('Searching inputs... Please Wait!');
    //         $("#reports_div").hide();
    //         $("#getbtn_for_UI").show().prop('disabled', true).html('Please Wait! ... ');
    //         $("#pleasewait_div").show().html(`<div class="spinner-container">
    //             <div class="spinner"></div>
    //         </div>`);
    //         $.ajax({
    //             url: '<?php // echo site_url('procurement/sales_controller_v2/get_category_wise_student_sales_report'); ?>',
    //             type: "post",
    //             data: {category, subcategories, items},
    //             success(data) {
    //                 $("#getbtn").show().prop('disabled', false).html('Get Report');
    //                 $("#getbtn_for_UI, #pleasewait_div").hide();
    //                 $("#pleasewait_div").hide();
    //                 $("#reports_div").show();
    //                 var p_data = JSON.parse(data);
    //                 if(Object.keys(p_data)?.length !== 0) {
    //                     __construct_table_reports(p_data);
    //                 } else {
    //                     $("#reports_div").html(`<div class="no-data-display">No Data</div>`);
    //                 }
    //             }
    //         });
    //     }
    // }

    function onchange_category() {
        var report_format= $("#report_format").val();
        $("#items").html('');
            $("#subcategories").html('');
        // alert(report_format)
        if(report_format == 'student-wise') {
             $("#items").siblings("span.select2").css('pointer-events', 'none').prop('readonly', true);
            $("#subcategories").siblings("span.select2").css('pointer-events', 'none').prop('readonly', true);
            return 0;
        } else {
            var report_format= $("#report_format").val();
            $("#items").siblings("span.select2").css('pointer-events', 'auto').prop('readonly', false);
            $("#subcategories").siblings("span.select2").css('pointer-events', 'auto').prop('readonly', false);
        }

        var selected_category= $("#category").val();
        if(selected_category) {
            $.ajax({
                url: '<?php echo site_url('procurement/inventory_controller_v2/getProductNames'); ?>',
                type: "post",
                data: {'category_id':selected_category},
                success(data) {
                    var p_data = JSON.parse(data);
                    var html= '';
                    if(p_data.length) {
                        for(var v of p_data) {
                            html += `<option value="${v.id}">${v.product_name}</option>`;
                        }
                        $("#subcategories").html(html);
                    }
                    
                }
            });
        }
    }

    function get_items(){
        var product_id = $("#subcategories").val();
        if(product_id && product_id.length === 1 && product_id[0]) {
            product_id= product_id[0];
            $("#items").siblings("span.select2").css('pointer-events', 'auto').prop('readonly', false);
        } else {
            $("#items").html('<option value="" selected>All Items</option>');
            $("#items").siblings("span.select2").css('pointer-events', 'none').prop('readonly', true);
            return;
        }
        var options = '<option value="">Select Sub-Product</option>';
        if(product_id == '') {
            $("#variant_id").html(options);
            return false;
        }
        $.ajax({
            url:'<?php echo site_url('procurement/inventory_controller_v2/getVariantNames3') ?>',
            type:'post',
            data: {product_id:product_id},
            success : function(data){
                var variant = JSON.parse(data);
                if(variant.length > 0) {
                    for (var i = 0; i < variant.length; i++) {
                        options += '<option value="'+variant[i].id+'">'+variant[i].name+'</option>';
                    }
                }
                $("#items").html(options);
            }
        });
    };

    function show_filter() {
        $("#filters_div").show();
        $("#plus_f").hide();
        $("#minus_f").show();
    }

    function hide_filter() {
        $("#filters_div").hide();
        $("#plus_f").show();
        $("#minus_f").hide();
    }



    function construct_student_wise_item_list_student_wise(data, j, chunc_std_len){
        if(j == 0) {
            sn = 1;
            t_amt= 0;
            t_qty= 0;
        } 
        var tables= ``;
        
        for(var i in data) {

            let promotion_status= data[i].promotion_status;
            let studentN= data[i].name;
            if(promotion_status == '4' || promotion_status == '5') {
                studentN= '<font color="red">' + studentN + ' - Alumini</font>';
            }

           

            let totalCategory= (data[i].categoryInformations)?.length;
               tables += `<tr>
                            <td rowspan="${totalCategory}">${sn++}</td>
                            <td rowspan="${totalCategory}">${data[i].admission_no}</td>
                            <td rowspan="${totalCategory}">${data[i].enrollment_number || '-'}</td>
                            <td rowspan="${totalCategory}">${studentN}</td>
                            <td rowspan="${totalCategory}">${data[i].class_name} - ${data[i].section_name}</td>
                            `;
            var IndexValue= 0;
            var totalQty= 0;
            var totalAmt= 0;

            for(var v of data[i].categoryInformations) {
                totalQty += Number(v.final_quantity);
                totalAmt += Number(v.final_amount);
            }
            for(var v of data[i].categoryInformations) {
                t_amt += +v.final_amount;
                t_qty += +v.final_quantity;

                // totalQty += Number(v.final_quantity);
                // totalAmt += Number(v.final_amount);
                if(IndexValue == 0) {
                    tables += `
                            <td>${v.category_name}</td>
                            <td>${v.final_quantity}</td>
                            <td>${v.final_amount}</td>
                            <td rowspan="${totalCategory}">
                                <b>Quantity: </b>${totalQty}<br>
                                <b>Amount: </b>${totalAmt}
                            </td>
                        </tr>
                        `;
                } else {
                    tables += `
                        <tr style="border-bottom: 2px solid black;">
                            <td>${v.category_name}</td>
                            <td>${v.final_quantity}</td>
                            <td>${v.final_amount}</td>
                        </tr>
                        `;
                }

                IndexValue ++;
            }
        }
        if(j == 0) {
            var header= `<div class="col-md-6" id="sammari">     </div> <div class="col-md-6" id="exportBtn">     </div>
                        <table class="table table-bordered" id="table_id1" style="border: 2px solid black;">
                            <thead>
                                <tr  style="border-bottom: 2px solid black;">
                                    <th style="min-width: 7%;">#</th>
                                    <th style="min-width: 10%;">Admission No</th>
                                    <th style="min-width: 10%;">Enrollment No</th>
                                    <th style="min-width: 12%;">Student Name</th>
                                    <th style="min-width: 7%;">Class-Section</th>
                                    <th style="min-width: 10%;">Category</th>
                                    <th style="min-width: 10%;">Total Quantity</th>
                                    <th style="min-width: 10%;">Total Amount</th>
                                    <th style="min-width: 24%;">Sub Total</th>
                                </tr>
                            </thead>
                            <tbody id="display_table_data">
                            </tbody>
                        </table>`;

            // $("#display_table_data").html('');
            $("#reports_div").html(header).show();
            setTimeout(() => {
                $("#display_table_data").html(tables);
            }, 300);

            $("#getbtn").prop('disabled', false).html('Get Report');
            $("#getbtn_for_UI").html('Still Fetching Some More Rows.... Please Wait !');
            $("#pleasewait_div").hide();

        } else {
            $("#display_table_data").append(tables);
        }
        if(j == chunc_std_len - 1) {
           setTimeout(() => {
                var summry= `
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th>Total Quantity</th>
                                            <td>${t_qty}</td>
                                        </tr>
                                        <tr>
                                            <th>Total Amount</th>
                                            <td>${t_amt.toFixed(2)}</td>
                                        </tr>
                                    </tbody>
                                </table>

                        `;
                $("#sammari").html(summry);

                    $("#exportBtn").html(`<br><br><button class="btn btn-info pull-right" onclick="__exportTable()">Export</button>`);

                $("#getbtn").show().prop('disabled', false).html('Get Report');
                $("#getbtn_for_UI, #pleasewait_div").hide();
                $("#pleasewait_div").hide();
                $("#reports_div").show();
           }, 1000);
    }
    }

    function __exportTable() {
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };
        var head = '';
        var title = '';
        var mainTable = $("#table_id1").html();


        htmls = '<br>' + mainTable + '<br>';

        var ctx = {
            worksheet : 'Spreadsheet',
            table : htmls
        }


        var link = document.createElement("a");
        link.download = "export.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();
    }



</script>

<style>
  .spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    background-color: white;
  }

  .spinner {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    border: 6px solid rgba(0, 0, 0, 0.1);
    border-top-color: #4285f4;
    animation: spin 1s infinite cubic-bezier(0.4, 0.15, 0.6, 0.85);
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>