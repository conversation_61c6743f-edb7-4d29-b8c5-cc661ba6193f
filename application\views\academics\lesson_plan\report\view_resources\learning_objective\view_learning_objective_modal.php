<div class="modal" tabindex="-1" role="dialog" id="view_learningObjectiveType">
    <div class="modal-dialog" role="document" id="view_learningObjectiveType_modal">
        <div class="modal-content" style="border-radius:1rem;width: 70%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Learning Objective</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_learningObjectiveType_data">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('learning_objective', 'view_learningObjectiveType')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Add More</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    function loadObjective() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ learningObjectiveType } = data);
                // console.log(learningObjectiveType);

                let html = `<table class="table table-bordered">
                        <tr class="bg-light">
                            <th>#</th>
                            <th>Learning Objective Type</th>
                            <th>Description</th>
                            <th style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Delete</th>
                        </tr>`

                learningObjectiveType.forEach((r, i) => {
                    if (r.lp_session_id != session_id) return;
                    html += `
                    <tr>
                        <td>${++i}</td>
                        <td id="learning-objective_${r.lp_objective_id}">${r.objective_name}</td>
                        <td>${r.manage_objective_description}</td>
                        <td style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;"><a onClick="delete_session_learning_objective('${r.lp_objective_id}')" class="btn btn-danger "><i class="fa fa-trash-o mr-0"></i></a></td>
                    </tr>
                    `
                })

                html += `</table>`;
                $("#view_learningObjectiveType_data").html(html);
            }
        })
    }

    $("#view_learningObjectiveType").on("shown.bs.modal", e => {
        loadObjective();
    })

    function delete_session_learning_objective(learning_objective_id) {
        var learning_objective_type = $(`#learning-objective_${learning_objective_id}`).text();
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('academics/lesson_plan/delete_session_learning_objective') ?>',
                    type: 'POST',
                    data: { learning_objective_id },
                    success: function (data) {
                        let parsedData = JSON.parse(data);
                        if (parsedData) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: 'Learning Objective deleted successfully.',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                loadObjective();
                                getSessionData(session_id);
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500
                            })
                        }
                    },
                    error: function (data) {
                        Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500
                            })
                    }
                });
            }
        })
    }
</script>