<?php $isMobile = $this->mobile_detect->isMobile();?>
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <?php if($type == 'viewOnlyFromMID') { ?>
        <li><a href="<?php echo site_url('management/payroll'); ?>">Payroll Dashboard</a></li>
        <li><a href="<?php echo site_url('management/payroll/manage_income'); ?>">Manage Investment Declaration</a></li>
        <li>Income Tax Comparison</li>
    <?php } else {?>
        <li><a href="<?php echo site_url('staff/Payroll_controller/'); ?>">Payroll Dashboard</a></li>
        <li><a href="<?php echo site_url('staff/Payroll_controller/income_declaration'); ?>">Investment Declaration</a></li>
        <?php if($type == 'viewOnly'){ ?>
            <li>Income Tax Comparison</li>
        <?php } else {?>
            <li>Income Tax Calculation</li>
        <?php } ?>
    <?php }?>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff" style="margin-top: 3px;">
                        <?php if($type == 'viewOnlyFromMID') { ?>
                            <a class="back_anchor" href="<?php echo site_url('management/payroll/manage_income'); ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a>
                            <?php } else {?>
                                <a class="back_anchor" href="<?php echo site_url('staff/Payroll_controller/income_declaration'); ?>">
                                    <span class="fa fa-arrow-left"></span>
                                </a>
                            <?php }?>
                        <?php 
                            echo $type == 'viewOnlyFromMID' 
                                ? "{$staffName}'s Income Tax Comparison" 
                                : ($type == 'viewOnly' ? 'Income Tax Comparison' : 'Income Tax Calculation'); 
                        ?>
                    </h3>
                </div>
                <?php if($type != '') { ?>
                    <div class="col-md-3 pl-0">
                        <div class="col-md-6 <?php echo ($isMobile ? '' : 'pull-right'); ?>">
                            <select class="form-control custom-select <?php echo ($isMobile ? 'pull-right' : ''); ?>"  onchange="income_schedule_year_change();" name="schedule_year" id="schedule_year" <?php echo ($isMobile ? 'style="width: 50% !important"' : ''); ?>>
                                <?php foreach ($financial_year as $year) {
                                        $selected_str = ($year->id == $selected_financial_year_id) ? 'selected' : '';
                                        echo "<option value='$year->id' $selected_str>$year->f_year</option>";
                                    } ?>
                            </select>
                    </div>
                <?php } ?>
            </div>
        </div>
        <input type="hidden" name="old_value" id="old_value" value="<?php echo $previous_selected_regime?>">
        <div class="panel-body" id="selectRegimeDiv">
            <div class="col-md-12 d-flex justify-content-center">
                <div class="col-md-4 btn btn-outline-info" id="new_regime_div" style="border:2px solid black; border-radius: 5px;">
                    <a href="#" id="new_regim_cal_per_month" onclick="changeActiveStatus('new_regime_div', 'old_regime_div')" style="text-decoration: none;">
                        <!-- <input type="checkbox"> -->
                        <h3 style="margin-bottom: 0px; padding: 5px;">
                            New Regime
                            <?php echo $type != '' && $previous_selected_regime == 1 ? '<span>(Selected)</span>' : '' ?>
                        </h3>
                    </a>
                </div>

                <div class="col-md-4 btn btn-outline-info" id="old_regime_div" style="border:2px solid black; margin-left: 5px; border-radius: 5px;">
                    <a href="#" id="old_regim_cal_per_month" onclick="changeActiveStatus('old_regime_div', 'new_regime_div')" style="text-decoration: none;">
                        <h3 style="margin-bottom: 0px; padding: 5px;">
                            Old Regime
                            <?php echo $type != '' && $previous_selected_regime == 2 ? '<span>(Selected)</span>' : '' ?>
                        </h3>
                    </a>
                </div>
            </div>
        </div>

        <?php $this->load->view('management/payroll/income_declaration/page2/_new_regime_calculation') ?>
        <?php $this->load->view('management/payroll/income_declaration/page2/_old_regime_calculation') ?>

        <div class="panel-body" id="no_data_body">
            <div class="no-data-display">Select Regime</div>
        </div>

    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
    var selected_financial_year_id = '<?php echo $selected_financial_year_id; ?>';
    var staff_id = '<?php echo $staffid ?>';
    var type = '<?php echo $type ?>';

    function income_schedule_year_change() {
        var selectedYear = $('#schedule_year').val();
        localStorage.setItem('selected_financial_year', selectedYear);
        var baseUrl = '<?php echo site_url('staff/Payroll_controller/income_tax_calculation'); ?>';
        var fullUrl = baseUrl + '/' + selectedYear + '/' + staff_id + '/' + type;
        window.location.href = fullUrl;
    }

    $(document).ready(function() {
        <?php if($previous_selected_regime != 0) { ?>
            <?php if($previous_selected_regime == 1) { ?>
                changeActiveStatus('new_regime_div', 'old_regime_div');
                new_regim_cal_per_month(new Event('click'));
            <?php } else if($previous_selected_regime == 2) { ?>
                changeActiveStatus('old_regime_div', 'new_regime_div');
                old_regim_cal_per_month(new Event('click'));
            <?php } ?>
        <?php } ?>
        income_declaration_cal();
        var selectedFinancialYear = localStorage.getItem('selected_financial_year');
        if (selectedFinancialYear !== null) {
            var baseUrl = "<?php echo site_url('staff/Payroll_controller/income_declaration'); ?>";
            var fullUrl = baseUrl + "/" + selectedFinancialYear;
            $('.back_anchor').attr('href', fullUrl);
        }
    });

    function changeActiveStatus(selectedDivId, otherDivId) {
        var selectedDiv = document.getElementById(selectedDivId);
        var otherDiv = document.getElementById(otherDivId);

        selectedDiv.classList.add('active');
        otherDiv.classList.remove('active');

        selectedDiv.querySelector('h3').style.color = "white";
        otherDiv.querySelector('h3').style.color = "black";

        // selectedDiv.innerHTML += '<i class="fa fa-check" style="color:black;"></i>';
        // otherDiv.querySelector('.fa-check').style.display = 'none';
    }

    function income_declaration_cal() {
        $("#sliderDiv").hide();
        $('.counselling_tabel').html('');

        $.ajax({
            url: '<?php echo site_url('staff/Payroll_controller/income_decalaration_cal'); ?>',
            type: 'post',
            data: {'staffid': staff_id, 'financial_year_id': selected_financial_year_id},
            success: function(data) {
                var echr_data = JSON.parse(data);
                if(echr_data.length == 0){
                    $('#selectRegimeDiv').hide();
                    $('#no_data_body').html('<div class="no-data-display">No Data Found</div>');
                    return;
                }
                //Fill the Old Regime Table
                let final_deductions = Number(echr_data.total_80_deductions) + Number(echr_data.or_lta);
                $("#or_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#or_sd").val(echr_data.or_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra_exemption").val(echr_data.hra_exemption.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_sd_hra").val(`(${(parseFloat(echr_data.hra_exemption) + parseFloat(echr_data.or_sd)).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);

                $("#or_income_from_salary").val(echr_data.income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#or_pt").val(echr_data.pt_paid.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_income_from_salary_pt").val(echr_data.taxable_income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php if($collectPerkTaxMode == 'employee') { ?>
                    $("#or_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php } ?>
                $("#or_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_sec24").val(`(${parseFloat(echr_data.sec_24).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);
                $("#or_gross_salary_income").val(echr_data.gross_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                
                $("#or_80c").val(echr_data.c_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ccd").val((echr_data.ccd_80 < 50000 ? '₹'+echr_data.ccd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}): echr_data.ccd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})));
                $("#or_80d").val(echr_data.d_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80dd").val(echr_data.dd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ddb").val(echr_data.ddb_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80g").val(echr_data.g_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80u").val(echr_data.u_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ttab").val(Number(echr_data.ttab_80).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80e").val('₹' + echr_data.e_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#total_80_deductions").val(final_deductions.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                
                $("#or_lta").val(Number(echr_data.or_lta).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                // $("#total_or_lta_dedections").val(Number(echr_data.or_lta).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_taxable_salary").val(echr_data.or_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_basic_tax").val(echr_data.or_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_tax_rebate").val(`(${echr_data.or_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);
                $("#or_surcharge").val(echr_data.or_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_net_income_tax_surcharge").val(echr_data.or_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_net_income_tax").val(echr_data.or_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_cess").val(parseFloat(echr_data.or_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_yearly_tds").val(echr_data.or_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_final_tds").val(echr_data.or_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                //Fill the New Regime Table
                $("#nr_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_sd").val(echr_data.nr_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#nr_income_from_salary_pt").val(echr_data.taxable_income_from_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php if($collectPerkTaxMode == 'employee') { ?>
                    $("#nr_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php } ?>
                $("#nr_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_gross_salary_income").val(echr_data.gross_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                
                $("#nr_basic_tax").val(echr_data.nr_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_tax_rebate").val(`(${echr_data.nr_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);
                $("#nr_net_income_tax").val(echr_data.nr_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_surcharge").val(echr_data.nr_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_net_income_tax_surcharge").val(echr_data.nr_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_cess").val(parseFloat(echr_data.nr_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_yearly_tds").val(echr_data.nr_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_final_tds").val(echr_data.nr_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_taxable_salary").val(echr_data.nr_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                //Save the fields in the form - Old Regime
                $("#or_perquisite_income_field").val(echr_data.perquisite_income);
                $("#or_total_income_field").val(echr_data.total_income);
                $("#or_taxable_salary_field").val(echr_data.or_taxable_salary);
                $("#or_tax_amt_field").val(echr_data.or_tax_amt);
                $("#or_80c_field").val(echr_data.c_80);
                $("#or_80ccd_field").val(echr_data.ccd_80);

                $("#or_80d_field").val(echr_data.d_80);
                $("#or_basic_tax_field").val(echr_data.or_basic_tax);
                $("#or_cess_field").val(echr_data.or_cess);

                //Save the fields in the form - New Regime
                $("#nr_perquisite_income_field").val(echr_data.perquisite_income);
                $("#nr_total_income_field").val(echr_data.total_income);
                $("#nr_taxable_salary_field").val(echr_data.nr_taxable_salary);
                $("#nr_tax_amt_field").val(echr_data.nr_tax_amt);
                $("#nr_80c_field").val(echr_data.c_80);
                $("#nr_80d_field").val(echr_data.d_80);
                $("#nr_80ccd_field").val(echr_data.ccd_80);
                $("#nr_basic_tax_field").val(echr_data.nr_basic_tax);
                $("#nr_cess_field").val(echr_data.nr_cess);
            }
        });
    }

    $("#new_regim_cal_per_month").on("click",e=>{
        new_regim_cal_per_month(e);
    })

    function new_regim_cal_per_month(e) {
        e.preventDefault();
        $('#new_regim_schedu_number').show();
        $('#new_body_show').show();
        $('#old_regim_schedu_number').hide();
        $('#old_body_show').hide();
        $('#no_data_body').hide();
    }

    $("#old_regim_cal_per_month").on("click",e=>{
        old_regim_cal_per_month(e);
    })

    function old_regim_cal_per_month(e) {
        e.preventDefault();
        $('#old_regim_schedu_number').show();
        $('#old_body_show').show();
        $('#new_regim_schedu_number').hide();
        $('#new_body_show').hide();
        $('#no_data_body').hide();
    }

    function new_regim_save() {
        Swal.fire({
            title: "Confirm",
            text: `You are submitting your tax declaration in the New Regime. Continue?`,
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes",
            cancelButtonText: "No"
        }).then((result) => {
            if (result.isConfirmed) {
                var nr_tax_amt_field = $("#nr_tax_amt_field").val();
                var staffid = '<?php echo $staffid ?>';
                var nr_taxable_salary_field = $("#nr_taxable_salary_field").val();
                var nr_perquisite_income_field = $("#nr_perquisite_income_field").val();
                var nr_80d_field = $("#nr_80d_field").val();
                var nr_80c_field = $("#nr_80c_field").val();
                var nr_80ccd_field = $("#nr_80ccd_field").val();
                var nr_total_income_field = $("#nr_total_income_field").val();
                var nr_basic_tax_field = $("#nr_basic_tax_field").val();
                var nr_cess_field = $("#nr_cess_field").val();
                var old_value = $("#old_value").val();
                var jsonOldValue = JSON.stringify({"selected_regime": old_value});
                var new_value = JSON.stringify({"selected_regime": "1"});
                $.ajax({
                    url: '<?php echo site_url('staff/Payroll_controller/new_regim_save'); ?>',
                    type: "post",
                    data: {
                        'nr_tax_amt_field': nr_tax_amt_field,
                        'staffid': staffid,
                        'nr_taxable_salary_field': nr_taxable_salary_field,
                        'nr_perquisite_income_field': nr_perquisite_income_field,
                        'nr_80d_field': nr_80d_field,
                        'nr_80c_field': nr_80c_field,
                        'nr_80ccd_field': nr_80ccd_field,
                        'nr_total_income_field': nr_total_income_field,
                        'nr_basic_tax_field': nr_basic_tax_field,
                        'nr_cess_field': nr_cess_field,
                        'selected_financial_year_id' : selected_financial_year_id,
                        'old_value': jsonOldValue,
                        'new_value': new_value,
                        'source': 'Select Regime',
                        'call_from': '<?php echo $call_from;?>',
                    },
                    success: function(data) {
                        if (data) {
                            Swal.fire({
                                icon: "success",
                                title: "New Regime Tax-Calculation Successfully Added",
                                showConfirmButton: false,
                                timer: 2500
                            }).then(function() {
                                window.location.href = '<?php echo site_url('staff/Payroll_controller/income_declaration/'); ?>' + selected_financial_year_id;
                            });
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Something went wrong",
                                showConfirmButton: false,
                                timer: 2500
                            });
                        }
                    }
                });
            }
        });
    }

    function old_regim_save() {
        Swal.fire({
            title: "Confirm",
            text: `You are submitting your tax declaration in the Old Regime. Continue?`,
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes",
            cancelButtonText: "No"
        }).then((result) => {
            if (result.isConfirmed) {
                var or_tax_amt_field = $("#or_tax_amt_field").val();
                var or_taxable_salary_field = $("#or_taxable_salary_field").val();
                var or_perquisite_income_field = $("#or_perquisite_income_field").val();
                var staffid = '<?php echo $staffid ?>';
                var or_total_income_field = $("#or_total_income_field").val();
                var or_80c_field = $("#or_80c_field").val();
                var or_80ccd_field = $("#or_80ccd_field").val();
                var or_80d_field = $("#or_80d_field").val();
                var or_basic_tax_field = $("#or_basic_tax_field").val();
                var or_cess_field = $("#or_cess_field").val();
                var old_value = $("#old_value").val();
                var jsonOldValue = JSON.stringify({"selected_regime": old_value});
                var new_value = JSON.stringify({"selected_regime": "2"});
                $.ajax({
                    url: '<?php echo site_url('staff/Payroll_controller/old_regim_save'); ?>',
                    type: "post",
                    data: {
                        'or_tax_amt_field': or_tax_amt_field,
                        'staffid': staffid,
                        'or_taxable_salary_field': or_taxable_salary_field,
                        'or_total_income_field': or_total_income_field,
                        'or_perquisite_income_field': or_perquisite_income_field,
                        'or_80c_field': or_80c_field,
                        'or_80ccd_field': or_80ccd_field,
                        'or_80d_field': or_80d_field,
                        'or_cess_field': or_cess_field,
                        'selected_financial_year_id' : selected_financial_year_id,
                        'or_basic_tax_field': or_basic_tax_field,
                        'old_value': jsonOldValue,
                        'new_value': new_value,
                        'source': 'Select Regime',
                        'call_from': '<?php echo $call_from;?>',
                    },
                    success: function(data) {
                        if (data) {
                            Swal.fire({
                                icon: "success",
                                title: "Old Regime Tax-Calculation Successfully Added",
                                showConfirmButton: false,
                                timer: 2500
                            }).then(function() {
                                window.location.href = '<?php echo site_url('staff/Payroll_controller/income_declaration/'); ?>' + selected_financial_year_id;
                            });
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Something went wrong",
                                showConfirmButton: false,
                                timer: 2500
                            });
                        }
                    }
                });
            }
        });
    }

    function view_additional_allowance_break_down(type){
        $(`#${type}_additional_allowance_info_btn`).html('Please wait...').prop('disabled', true);
        var staff_id = '<?php echo $staffid ?>';
        var financial_year = '<?php echo $selected_financial_year_id; ?>';
        $.ajax({
            url: "<?php echo site_url('staff/Payroll_controller/view_additional_allowance_break_down')?>",
            type: "POST",
            data: {
                'staff_id': staff_id,
                'financial_year': financial_year,
            },
            success: function (response) {
                let parsed_allowance_data = JSON.parse(response);
                if(Object.keys(parsed_allowance_data).length > 0 > 0){
                    showAllowanceBreakdown(parsed_allowance_data, type);
                } else {
                    Swal.fire({
                        title: "No Data Found",
                        icon: "warning",
                        timer: 1500,
                        showConfirmButton: false,
                    }).then(() => {
                        $(`#${type}_additional_allowance_info_btn`).html('More Info').prop('disabled', false);
                    });
                }
            }
        });
    }

    function showAllowanceBreakdown(data, type) {
        let tableHtml = `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 1px solid #ddd;">
                        <th style="padding: 8px; text-align: left;">Allowance</th>
                        <th style="padding: 8px; text-align: right;">Amount</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Variables to calculate total
        let totalAmount = 0;

        // Loop through data to build rows
        Object.values(data).forEach((item) => {
            tableHtml += `
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;text-align: left;">${item.display_name}</td>
                    <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd;">${item.amount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
                </tr>
            `;
            totalAmount += item.amount;
        });

        // Add total row
        tableHtml += `
            </tbody>
            <tfoot>
                <tr>
                    <td style="padding: 8px; font-weight: bold;text-align: left;">Total</td>
                    <td style="padding: 8px; text-align: right; font-weight: bold;">${totalAmount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
                </tr>
            </tfoot>
        </table>
        `;
        
        Swal.fire({
            title: "Allowance Breakdown",
            html: tableHtml,
            confirmButtonText: "Close",
        }).then(() => {
            $(`#${type}_additional_allowance_info_btn`).html('More Info').prop('disabled', false);
        });
    }
</script>
