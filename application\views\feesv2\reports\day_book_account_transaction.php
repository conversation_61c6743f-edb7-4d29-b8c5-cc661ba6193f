<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Account-level Transactions</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
          Account-level Transactions
          </h3>
        </div>
      </div>
    </div>
    <style type="text/css">
      p{
        margin-bottom: .5rem;
      }
      input[type=checkbox]{
        margin: 0px 4px;
      }
    </style>
    <div class="card-body">
      <div class="row" style="margin: 0px">
        <div class="col-lg-2">
          <p>Date Range</p>
          <div id="reportrange" class="dtrange">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2" id="classSection">
          <p>Class</p>
          <?php 
            $array = array();
            foreach ($classes as $key => $class) {
              $array[$class->classId] = $class->className; 
            }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All' class='form-control classId select '");
          ?>
        </div>

        <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.5rem;">
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
        </div>
      </div>
    </div>

    <div class="card-body">
      <div id="printArea">
        <div class="text-center mt-2">
          <div id="progress" class="progress" style="display: none; height: 8px; background-color: #e0e0e0;">
            <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%; height: 100%; background-color: #007bff;">
            </div>
          </div>
          <div class="row" style="margin-top: 20px; margin-bottom: 10px;">
            <div class="col-md-12 text-right" id="exportButtons" style="display: none;">
              <div class="search-box">
                <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
              </div>
              <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="printProfile()">
                <span class="fa fa-print" aria-hidden="true"></span> Print
              </button>
              <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="exportToExcel_daily()">
                <span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel
              </button>
            </div>
          </div>
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Account-level Transaction details </h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>


        <div class="panel-body day_book_account_tx  table-responsive hidden-xs">
           <h3>Select Date range to get report</h3>
        </div>

        <div class="panel-body day_book_account_sales  table-responsive hidden-xs">
        </div>
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">


  function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });

    $(document).on('input', '#table-search', function() {
    var value = $(this).val().toLowerCase();
    // Search in both tables
    $('.day_book_account_tx table tbody tr, .day_book_account_sales table tbody tr').filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
    });
});

    $('#salesInclude').change(function(){
      if(this.checked) {
        $('#classSection').hide();
       }else{
        $('#classSection').show();
      }
    });
   
    $('#search').on('click',function(){
       $(".day_book_account_tx").html('');    
       $(".day_book_account_sales").html('');    
      $(".loading-icon").show();
      $("#progress").show();
      $('.prarthana_daily_reports').show();
      var from_date = $('#from_date').val();
      var to_date = $('#to_date').val();
      var classId = $('#classId').val();

      $('#fromDate').html(from_date);
      $('#toDate').html(to_date);
      $(this).prop('disabled', true).val('Please wait...'); 
      $.ajax({
        url: '<?php echo site_url('feesv2/reports/generate_report_for_day_book_account_transaction'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date,'classId':classId},
        success: function(data) {
          var rData =JSON.parse(data);
          console.log(rData);
          $(".loading-icon").hide();
          $("#progress").hide();
          $("#exportButtons").show();
          $(".day_book_account_tx").html(rData.fees);    
          $(".day_book_account_sales").html(rData.sales);    
          $('#search').prop('disabled', false).val('Get Report');     

          // $(".day_book_account_tx").html(data);    
        }
      });
    });
  });

</script>

<style>
 @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table {
    font-family: 'Poppins', sans-serif !important;
  }
  
  .search-box {
      display: inline-block;
      position: relative;
      margin-right: 2px;
      vertical-align: middle;
    }

    .input-search {
      line-height: 1.5;
      padding: 5px 10px;
      display: inline;
      width: 177px;
      height: 27px;
      background-color: #f2f2f2 !important;
      border: 1px solid #ccc !important;
      border-radius: 4px !important;
      margin-right: 0 !important;
      font-size: 14px;
      color: #495057;
      outline: none;
    }

    .input-search::placeholder {
      color: rgba(73, 80, 87, 0.5);
      font-size: 14px;
      font-weight: 300;
    }

  .card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 20px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }

  table th,
  table td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
  }

  table tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  table tbody tr:hover {
    background-color: #f1f5f9;
  }

  table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .btn-primary:hover,
  .btn-primary:focus {
    background-color: #0069d9;
    border-color: #0062cc;
  }
</style>

 <script>
  function printProfile() {
    const printWindow = window.open('', '_blank');
    // Get the summary header and both tables
    let printHeader = document.getElementById('print_visible').outerHTML;
    printHeader = printHeader.replace('display: none;', ''); // Show header

    // Get the main tables (fees and sales)
    const feesTable = document.querySelector('.day_book_account_tx').outerHTML;
    const salesTable = document.querySelector('.day_book_account_sales').outerHTML;

    printWindow.document.write(`
        <html>
        <head>
            <title>Account-level Transactions Report</title>
            <style>
                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    background: #fff;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-size: 12px;
                }
                h3, h4, h5 {
                    margin: 5px 0;
                    text-align: center;
                }
                h3, h4, h5 { margin: 10px 0; }
                @media print {
                    table { page-break-inside: auto }
                    tr { page-break-inside: avoid }
                }
            </style>
        </head>
        <body>
            ${printHeader}
            ${feesTable}
            ${salesTable}
            <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

  function exportToExcel_daily(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var summaryTable = $("#print_visible").html();
    var mainTable = $(".day_book_account_tx").html();
    var salesTable = $(".day_book_account_sales").html();

    htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable +'<br><br>' + salesTable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD.MM.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>