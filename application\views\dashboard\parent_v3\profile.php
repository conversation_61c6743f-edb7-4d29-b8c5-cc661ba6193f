<!-- Profile Page - Mobile UI -->
<style>
    body {
        background: #f7f8fa;
        font-family: 'Inter', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    }
    .profile-header {
        background: #fff;
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        display: flex;
        align-items: center;
        position: relative;
    }
    .profile-header .back-btn {
        font-size: 1.5rem;
        color: #7b5cff;
        background: none;
        border: none;
        margin-right: 0.5rem;
        padding: 0;
        cursor: pointer;
    }
    .profile-header-title {
        flex: 1;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
    }
    .profile-alert {
        background: #fffbe6;
        color: #b26a00;
        border-radius: 10px;
        margin: 1rem 1rem 0.5rem 1rem;
        padding: 0.7rem 1rem;
        font-size: 0.98rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #ffe58f;
    }
    .profile-alert .fa {
        margin-right: 0.5rem;
    }
    .profile-alert .arrow {
        margin-left: auto;
        color: #b26a00;
    }
    .profile-avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 1.2rem;
        margin-bottom: 1.2rem;
    }
    .profile-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e0e0e0;
        margin-bottom: 0.7rem;
    }
    .profile-name {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.2rem;
    }
    .profile-class {
        color: #888;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    .switch-profile-btn {
        background: #7b5cff;
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 0.7rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1.2rem;
        margin-top: 0.5rem;
        width: 90%;
        max-width: 320px;
        box-shadow: 0 2px 8px rgba(123,92,255,0.08);
        transition: background 0.2s;
    }
    .switch-profile-btn:active {
        background: #4e8cff;
    }
    .profile-list {
        background: #f6f7fb;
        border-radius: 16px;
        margin: 0 1rem 1.2rem 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.01);
        padding: 0.5rem 0;
    }
    .profile-list-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.2rem;
        border-bottom: 1px solid #ececec;
        font-size: 1.05rem;
        color: #333;
        cursor: pointer;
        background: #fff;
        border-radius: 10px;
        margin: 0.5rem 0;
        transition: background 0.15s;
    }
    .profile-list-item:last-child {
        border-bottom: none;
    }
    .profile-list-item .fa, .profile-list-item .fa-solid {
        font-size: 1.2rem;
        margin-right: 1.1rem;
        color: #7b5cff;
        min-width: 22px;
        text-align: center;
    }
    .profile-list-item .fa-lock, .profile-list-item .fa-headset {
        color: #4e8cff;
    }
    .profile-list-item .fa-sign-out-alt {
        color: #fd5e53;
    }
    .profile-list-item .fa-trash {
        color: #ff3b30;
    }
    .profile-list-item:active {
        background: #f0f0ff;
    }
    .profile-list-item .right-arrow {
        margin-left: auto;
        color: #bbb;
        font-size: 1.1rem;
    }
    .profile-list-item.delete {
        color: #ff3b30;
        font-weight: 600;
        justify-content: center;
        background: #fff0f0;
        border: 1px solid #ffe0e0;
    }
    .profile-list-item.delete .fa-trash {
        margin-right: 0.7rem;
    }
    @media (min-width: 600px) {
        .profile-header, .profile-alert, .profile-list, .profile-avatar-section {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }
    }
</style>

<!-- FontAwesome CDN (for icons) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />

<div class="profile-header">
    <button class="back-btn" onclick="window.history.back()">
        <i class="fa fa-arrow-left"></i>
    </button>
    <div class="profile-header-title">Profile</div>
    <div style="width:32px;"></div>
</div>

<div class="profile-alert">
    <i class="fa fa-exclamation-triangle"></i>
    <span>Profile confirmation is pending</span>
    <i class="fa fa-chevron-right arrow"></i>
</div>

<div class="profile-avatar-section">
    <img src="https://randomuser.me/api/portraits/men/32.jpg" class="profile-avatar" alt="User">
    <div class="profile-name">Aadi Kumar Sharma</div>
    <div class="profile-class">Grade 2-A</div>
    <button class="switch-profile-btn" onclick="window.location.href='<?php echo site_url('parent/switch_profile'); ?>'">Switch Profile</button>
</div>

<div class="profile-list">
    <div class="profile-list-item" onclick="window.location.href='<?php echo site_url('parent/profile_detail'); ?>'">
        <i class="fa fa-user"></i>
        Profile Detail
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='<?php echo site_url('parent/change_password'); ?>'">
        <i class="fa fa-lock"></i>
        Change Password
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='<?php echo site_url('parent/help_support'); ?>'">
        <i class="fa fa-headset"></i>
        Help & Support
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item" onclick="window.location.href='<?php echo site_url('auth/logout'); ?>'">
        <i class="fa fa-sign-out-alt"></i>
        Logout
        <i class="fa fa-chevron-right right-arrow"></i>
    </div>
    <div class="profile-list-item delete" onclick="if(confirm('Are you sure you want to delete your account?')) window.location.href='<?php echo site_url('parent/delete_account'); ?>'">
        <i class="fa fa-trash"></i>
        Delete Account
    </div>
</div> 