<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Objective_model extends CI_Model {
	private $yearId;
	public function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function getObjectives(){
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
    $created_by = $this->db->select("concat(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as name")
    ->from('staff_master s')
    ->where('s.id', $stakeholder_id)
    ->get()->row();
    $result = $this->db->select('o.*, a.friendly_name')
    ->from('lp_objectives o')
    ->join('avatar a', 'a.id = o.created_by', 'left')
    ->order_by("a.friendly_name, o.objective_name")
    ->get()->result();
    return $result;
  }

  public function submitObjective(){
    $objectiveName = trim($_POST['objective_name']);
    $objectiveNameValidation = $this->db->select('objective_name')
    ->from('lp_objectives')
    ->where('objective_name', $objectiveName)
    ->get()->row();
    if($objectiveNameValidation){
      return -1;
    }
    $data = array(
      'objective_name' => trim($_POST['objective_name']),
      'objective_description' => trim($_POST['objective_description']) != '' ? trim($_POST['objective_description']) : NULL,
      'created_by' => $this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime()
    );
    return $this->db->insert('lp_objectives',$data);
  }
}