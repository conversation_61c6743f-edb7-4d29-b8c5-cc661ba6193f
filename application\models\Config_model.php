<?php
    defined('BASEPATH') OR exit('No direct script access allowed');
    class Config_model extends CI_Model{
        public function __construct(){
            parent::__construct();
            date_default_timezone_set('Asia/Kolkata');
        }

        public function Kolkata_datetime(){
            $timezone = new DateTimeZone("Asia/Kolkata" );
            $date = new DateTime();
            $date->setTimezone($timezone );
            $dtobj = $date->format('Y-m-d H:i:s');
            return $dtobj;
        }

        public function getDBData(){
            $this->db->select('id, name, value');
            //TO Do
            //$this->db->where('name','login_background');
            return  $this->db->get('config')->result();
        }

        public function updateConfig($name, $newValue){
            //Config table is getting updated. Clear the cache
            $cache_key = CONFIG_ENV['school_sub_domain'] . "_config_table_data";
            $this->cache->delete($cache_key);
            log_message('error', 'Deleting cache ' . $cache_key);

            $today = date('Y-m-d');
            //echo $today; die();
            $this->db->select('name');
            $current_types = $this->db->get('config');
            //echo '<pre>'; print_r($current_props->result_array()); die();

            if(in_array($name, $current_types)){

                $data = array(
                            'name' => $name,
                            'value' => $newValue,
                            'date_added' => $today
                );
                // $this->db->set('date_added', $today);               
                // $this->db->set('value',$new_module_string);
                // $this->db->set('name', $type);
                //$this->db->where('name', $type);
                return  $this->db->insert('config', $data);
            } 
            else
            {
                $this->db->set('date_added', $today);
                $this->db->set('value', $newValue);
                //$this->db->set('name', $type);
                $this->db->where('name', $name);
                return  $this->db->update('config');
            } 
        }

        public function getEnabledValues($type){
            $this->db->select('value');
            $this->db->where('name', $type);
            return $this->db->get('config')->row_array()['value'];
        }

        public function upsertToConfig($name, $value, $type){
            //Config table is getting updated. Clear the cache
            $cache_key = CONFIG_ENV['school_sub_domain'] . "_config_table_data";
            $this->cache->delete($cache_key);
            log_message('error', 'Deleting cache ' . $cache_key);

            $rowCount = $this->db->select('count(*) as cnt')
                ->where('name', $name)
                ->get('config')->row();

            if ($rowCount->cnt > 0) {
                //Config already in db. Update!
                $this->db->set('value', $value);
                $this->db->where('name', $name);
                return  $this->db->update('config');
            } else {
                //Config not in DB. Insert!
                $data = array(
                    'name' => $name,
                    'value' => $value,
                    'type' => $type
                );
                return  $this->db->insert('config', $data);
            } 
        }

        public function getTemplateConfigData () {
            return json_decode(file_get_contents('application/config/config.json'));
        }

        public function deleteConfig($name){
            //Config table is getting updated. Clear the cache
            $cache_key = CONFIG_ENV['school_sub_domain'] . "_config_table_data";
            $this->cache->delete($cache_key);
            log_message('error', 'Deleting cache ' . $cache_key);
            
            $this->db->where('name', $name);
            $result = $this->db->delete('config');

            return $result;
        }

        public function get_config_settings() {
            log_message('error', 'In get config settings');

            $this->load->driver('cache', array('adapter' => 'file'));

            // Define a unique cache key for your query
            $school_sub_domain = CONFIG_ENV['school_sub_domain'];
            if (!empty($school_sub_domain)) {
                $cache_key = $school_sub_domain . "_config_table_data";

                // Check if the result is already cached
                if (!$result = $this->cache->get($cache_key)) {
                    // Cache miss, so query the database
                    log_message('error', 'Creating cache ' . $cache_key);
                    $result =  $this->db_readonly->select("name, value, type")->get('config')->result_array();
    
                    // Save the result to cache
                    $this->cache->save($cache_key, $result, 300); // 300 seconds (5 minutes) cache time
                } else {
                    // Cache hit, use the cached result
                    log_message('error', 'cache hit! ' . $cache_key);
                    $result = $this->cache->get($cache_key);
                }
            } else {
                //Do not work with cache is $school_sub_domain is not defined
                $result =  $this->db_readonly->select("name, value, type")->get('config')->result_array();
            }

            return $result;
        }

        public function insertimageConfig($name, $value, $type){
            //Config table is getting updated. Clear the cache
            $cache_key = CONFIG_ENV['school_sub_domain'] . "_config_table_data";
            $this->cache->delete($cache_key);
            log_message('error', 'Deleting cache ' . $cache_key);
            
            $rowCount = $this->db->select('count(*) as cnt')
                ->where('name', $name)
                ->get('config')->row();

            if ($rowCount->cnt > 0) {
                //Config already in db. Update!
                $this->db->set('value', $value);
                $this->db->set('type', $type);
                $this->db->where('name', $name);
                return  $this->db->update('config');
            } else {
                //Config not in DB. Insert!
                $data = array(
                    'name' => $name,
                    'value' => $value,
                    'type' => $type
                );
                return  $this->db->insert('config', $data);
            } 
        }

        public function before_update($name){
            $result = $this->db->select('value')
                ->where('name', $name)
                ->get('config')->row();
            return $result->value;
        }
        
        public function insertOTP($input, $otp) {
            $this->db->where('email_id', $input['email']);
            $this->db->where('name', $input['name']);
            $q = $this->db->get('config_user');
            $this->db->reset_query();

            if ($q->num_rows() > 0) {
                $configData = array(
                    'otp' => $otp
                );
                $this->db->where('email_id', $input['email']);
                $this->db->where('name', $input['name']);
                $this->db->update('config_user', $configData);
            } else {
                $configData = array(
                    'email_id' => $input['email'],
                    'name' => $input['name'],
                    'otp' => $otp
                );
                $this->db->insert('config_user', $configData);
            }
        }

        public function verifyOTP($input) {
            if (!isset($input['email']) && !isset($input['name']))
                return 0;
            $this->db_readonly->where('email_id', $input['email']);
            $this->db_readonly->where('otp', $input['otpCode']);
            $q = $this->db_readonly->get('config_user');
            if ( $q->num_rows() > 0 ) {
                return 1;
            } else {
                return 0;
            }
        }

        public function insert_into_history($name, $mobile_num, $person_name, $status){
            $configData = array(
                'config_name' => $name,
                'modified_date_time' => $this->Kolkata_datetime(),
                'modified_by_name' => $person_name,
                'modified_by_email_id' => $mobile_num,
                'modified_status' => $status
            );
            $this->db->insert('config_history', $configData);
        }

        public function get_config(){
            $sql = "select id, name from config";
            return $this->db_readonly->query($sql)->result();
        }

        public function get_config_history($config_id, $from_date, $to_date) {
            $from_date = date('Y-m-d', strtotime($from_date));
            $to_date = date('Y-m-d', strtotime($to_date));

            $sql = "select * from config_history 
            join config c on c.name = config_name
            where DATE(modified_date_time) BETWEEN '$from_date' AND '$to_date'";

            if($config_id != "all"){
                $sql = $sql . " AND c.id = $config_id";
            }
            return $this->db_readonly->query($sql)->result();
        }
    }
?>    