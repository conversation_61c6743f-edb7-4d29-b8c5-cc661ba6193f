<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('student_nc/menu'); ?>">Student Noncompliance Menu</a></li>
  <li>Resolve Penalty</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student_nc/menu') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a>
            Resolve Penalty
          </h3>
        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="col-md-2 form-group pt-3">
        <select name="penalty_status" id="penalty_status" class="form-control" style="margin-top: 12px;">
          <option value="all">All</option>
          <option value="penalty" selected>Penalty</option>
          <option value="grace">Grace</option>
          <option value="served">Served</option>
          <option value="no_penalty">No Penalty</option>
        </select>
      </div>
      <div class="col-md-2 form-group pt-3">
        <select name="acad_year" id="acad_year" class="form-control" style="margin-top: 12px;">
        <option value="">All</option>
          <?php if(!empty($acad_years)){ foreach($acad_years as $key => $val) {
            $selected = ''; if($val->acad_year_id == $this->acad_year->getAcadYearId()) { $selected = 'selected'; } ?>
            <option value="<?= $val->acad_year_id ?>" <?= $selected ?>><?= $val->acad_year ?></option>
          <?php }} ?>
        </select>
      </div>
      <div class="col-md-2 form-group pt-3">
        <button class="btn btn-primary mt-3" onclick="get_resolve_penalty_types()">Get</button>
      </div>
      <div id="nc_table" class="panel-body table-responsive hidden-xs">
      </div>
    </div>

    <div class="card-body pt-1">
      <div id="penalty_table" class="panel-body table-responsive hidden-xs">
      </div>
    </div>

  </div>
</div>

<div class="modal" id="view_std_history" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document" style="width:60%;margin:auto;" id="view_std_history_dialog_div">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalHeader" style="font-size:20px;">Non-Compliance history of <span id="modal_std_name"></span></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true" style="font-size: 30px;color: red;">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="" style="padding:20px;">
        <span id="std_nc_history_table">
        </span>
      </div>
    </div>
  </div>
</div>

<script language="javascript">
  $(document).ready(function() {
    //Set the status if cookie is available
    var resolve_penalty_types_penalty_status = _get_cookie('resolve_penalty_types_penalty_status');
    if (resolve_penalty_types_penalty_status != null) {
      $("#penalty_status").val(resolve_penalty_types_penalty_status);
    }

    get_resolve_penalty_types();
  });

  function get_resolve_penalty_types() {
    var penalty_status = $("#penalty_status").val();
    var acad_year_id = $("#acad_year").val();

    _set_cookie('resolve_penalty_types_penalty_status', penalty_status);

    $.ajax({
      url: '<?php echo site_url('student_nc/noncompliance/get_resolve_penalty_types'); ?>',
      type: 'post',
      data: {
        'penalty_status': penalty_status,'acad_year_id':acad_year_id
      },
      success: function(data) {
        parsed_data = $.parseJSON(data);
        var nc_data = parsed_data;
        html = construct_resolve_penalty_table(nc_data);
        $("#nc_table").html(html);
      },
      error: function(err) {
        console.log(err);
      }
    });
  }

  function construct_student_history_table(nc_data) {
    var html = '';

    if (nc_data.length == 0) {
      html += '<h4>No Non-compliance History found for the student</h4>';
    } else {
      html += `
    <table id="nc_type_table" class="table table-bordered">
            <thead>
              <tr>
                <th style="min-width: 25px;">#</th>
                <th style="min-width: 150px;">Date</th>
                <th style="min-width: 150px;">Non-Compliance</th>
                <th style="min-width: 200px;">Remarks</th>
                <th style="min-width: 75px;">Penalty</th>
                <th style="min-width: 75px;">Penalty Status</th>
              </tr>
            </thead>
    
    `;
      html += `<tbody>`;
      for (var i = 0; i < nc_data.length; i++) {
        var data = nc_data[i];

        var served_button = '';

        switch (data['penalty_status']) {
          case 'penalty':
            font_color = '#e1a8a8';
            break;
          case 'grace':
            font_color = '#a4cda4';
            break;
          case 'served':
            font_color = '#9bd99b';
            break;
          default:
            font_color = '#e1a8a8';
        }
        html += `
              <tr>
                <td>${i+1}</td>
                <td>${data['snc_date']}</td>
                <td style="background:${data['color_code']}">${data['category']}</td>
                <td>${data['remarks']}</td>
                <td>${data['penalty_name']}</td>
                <td style="background:${font_color}">${data['penalty_status'].toUpperCase()}</td>
              </tr>`;
      }
      html += `</tbody>
        </table>`;
    }
    return html;
  }

  function construct_resolve_penalty_table(nc_data) {
    var html = '';

    if (nc_data.length == 0) {
      html += '<h4>No Non-Compliance records for the selected criteria</h4>';
    } else {

      html += `
    <table id="nc_type_table" class="table table-bordered">
            <thead>
              <tr>
                <th style="min-width: 25px;">#</th>
                <th style="min-width: 125px;">Student Name</th>
                <th style="min-width: 75px;">Class</th>
                <th style="min-width: 75px;">Date</th>
                <th style="min-width: 150px;">Non-Compliance</th>
                <th style="min-width: 200px;">Remarks</th>
                <th style="min-width: 100px;">Created On</th>
                <th style="min-width: 125px;">Created By</th>
                <th style="min-width: 75px;">Penalty</th>
                <th style="min-width: 75px;">Penalty Status</th>
                <th style="min-width: 75px;">Penalty handled by</th>
                <th style="min-width: 250px;">Actions</th>
              </tr>
            </thead>
    
    `;
      html += `<tbody>`;
      for (var i = 0; i < nc_data.length; i++) {
        var data = nc_data[i];

        var served_button = '';
        var status_display = '';
        var is_disable ='';

        switch (data['penalty_status']) {
          case 'penalty':
            font_color = '#e1a8a8';
            is_disable = '';
            break;
          case 'grace':
            font_color = '#a4cda4';
            is_disable = 'disabled';
            break;
          case 'served':
            font_color = '#9bd99b';
            is_disable = 'disabled';
            break;
          default:
            font_color = '#e1a8a8';
        }
        html += `
              <tr>
                <td>${i+1}</td>
                <td>${data['std_name']}</td>
                <td>${data['class_name']}</td>
                <td>${data['snc_date']}</td>
                <td style="background:${data['color_code']}">${data['category']}</td>
                <td>${data['remarks'] || "-"}</td>
                <td>${data['created_on']}</td>
                <td>${data['created_staff_name']}</td>
                <td>${data['penalty_name']}</td>
                <td style="background:${font_color}">${data['penalty_status'].toUpperCase()}<br><small>(resolved on ${data['penalty_served_date']})</small></td>
                <td>${data['penalty_staff_name'].length>1 && data['penalty_staff_name'] || "NA"}</td>
                <td>
                  <input type="button" class="btn btn-primary" onclick="show_history_dialog('${data['std_name']}', '${data['std_admission_id']}')" value="History">
                  <input type="button" style="display:${data['penalty_status']==="no_penalty" && 'none'}" class="btn btn-primary" ${is_disable} onclick="mark_as_served('${data['id']}', '${data['std_name']}', 'served')" value="Served">
                  <input type="button" style="display:${data['penalty_status']==="no_penalty" && 'none'}" class="btn btn-primary" ${is_disable} onclick="mark_as_served('${data['id']}', '${data['std_name']}', 'grace')" value="Grace">
                </td>
              </tr>`;
      }
      html += `</tbody>
        </table>`;
    }
    return html;
  }

  function show_history_dialog(std_name, sa_id) {
    $.ajax({
      url: '<?php echo site_url('student_nc/noncompliance/get_student_nc_history'); ?>',
      type: 'post',
      data: {
        'sa_id': sa_id
      },
      success: function(data) {
        parsed_data = $.parseJSON(data);
        var nc_data = parsed_data;
        html = construct_student_history_table(nc_data);
        $('#modal_std_name').html(std_name);
        $('#std_nc_history_table').html(html);
        $('#view_std_history').modal('show');
      },
      error: function(err) {
        console.log(err);
      }
    });
  }

  function mark_as_served(snc_id, std_name, new_status) {
    bootbox.confirm({
      title: "Confirm Resolving Non-Compliance",
      message: "<h4 style='margin-left:40px;'><center>"+ std_name +" Non-compliance</center></h4>" + `<div style="margin-top:20px;"><label class="control-label col-md-3" for="add_grace_served_remarks">Remarks</label><textarea class="col-md-9" id ="add_grace_served_remarks" name="add_grace_served_remarks" rows="4" cols="50" ></textarea></div>`,
      className: "medium",

      buttons: {
        confirm: {
          label: 'Submit',
          className: 'btn-success'
        },
        cancel: {
          label: 'Close',
          className: 'btn-danger'
        }
      },
      callback: function(result) {
        let add_grace_served_remarks = $('#add_grace_served_remarks').val();
        if (result) {
          $.ajax({
            url: '<?php echo site_url('student_nc/noncompliance/resolve_nc'); ?>',
            type: 'post',
            data: {
              'snc_id': snc_id,
              'new_status': new_status,
              'add_grace_served_remarks':add_grace_served_remarks
            },
            success: function(data) {
              if (data) {
                $(function() {
                  new PNotify({
                    title: 'Success',
                    text: 'Assessment Finalized successfully',
                    type: 'success',
                  });
                });
              } else {
                $(function() {
                  new PNotify({
                    title: 'Warning',
                    text: 'Something Went Wrong',
                    type: 'warning',
                  });
                });
              }
              get_resolve_penalty_types();
            }
          });
        }
      }
    });
  }
</script>

<style type="text/css">
  .medium {
    width: 450px;
    margin: auto;
  }
</style>