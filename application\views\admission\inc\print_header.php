<!DOCTYPE html>
<html lang="en">
<?php 
$admission_ui_colors = [];
$ui_colors_array = json_decode($this->settings->getSetting('admissions_ui_theme_color'), true);

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
} ?>

<head>
    <!-- META SECTION -->
    <title><?php echo $this->settings->getSetting('school_name'); ?></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <link rel="icon" href="<?php echo base_url() . $this->settings->getSetting('favicon'); ?>" type="image/x-icon" />
    <!-- <PERSON>ND META SECTION -->
    <!-- CSS INCLUDE -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css"
        integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/theme-default.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/custom.css?lat1212" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/parsley.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/bootstrap-datetimepicker.css" />
    <!-- <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/chung-timepicker.css"/> -->
    <!-- Notify -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.brighttheme.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/pnotify.buttons.css" />
    <!-- <link rel="stylesheet" type="text/css" href="<?php //echo site_url('assets/css/monthly.css'); ?>" /> -->

    <style type="text/css" href="https://cdn.datatables.net/buttons/1.5.2/css/buttons.bootstrap.min.css"></style>
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/bootstrap-changes-style.css" />
    <link
        href="https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <!-- EOF CSS INCLUDE -->
    <!-- 
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins.js"></script> -->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
    </script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
        integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous">
    </script>
</head>

<body style="background-color: #F9F7FE !important;">
    <div class="panel-group" style="margin:0;padding:0;">
        <div style="background-color: #fff; padding: 10px 20px; box-shadow: 0 0 4px rgba(0,0,0,0.05);">
            <div style="display: flex;justify-content: space-between; align-items: center;flex-wrap: wrap;row-gap: 10px;">
                <div style="display: flex; align-items: center; gap: 10px; min-width: 0;">
                    <img class="img-rounded" width="38px"
                        src="<?php echo base_url() . $this->settings->getSetting('school_logo'); ?>" alt="School Logo"
                        style="border-radius: 4px; flex-shrink: 0;" />

                    <span class="school_name_in_header" style="font-weight: 600;font-size: 18px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;color: #0A0D14;">
                        <?php echo $this->settings->getSetting('school_name'); ?>
                    </span>
                </div>

                <!-- Logout -->
                <div style="display: flex;">
                    <a href="#" id="logoutBtn" style="text-decoration: none; display: inline-flex; align-items: center; gap: 6px;
               padding: 6px 14px; border: 1.5px solid #dc3545; color: #dc3545;
               border-radius: 10px; font-size: 14px; font-weight: 500;
               transition: all 0.2s ease;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24"
                            stroke="#dc3545" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H6a2 2 0 01-2-2V7a2 2 0 012-2h5a2 2 0 012 2v1" />
                        </svg>
                        Logout
                    </a>
                </div>

            </div>
        </div>
    <script>
    document.getElementById("logoutBtn").addEventListener("click", function(e) {
        e.preventDefault(); // Prevent default link action
        $("body").addClass("modal1");
        $(".modal1").css("display", 'contents');
        Swal.fire({
            title: 'Are you sure you want to logout?',
            html: '<p style="text-align:center; font-size: 16px;"></p>',
            customClass: {
                popup: 'rounded-xl shadow-lg',
                title: 'text-lg font-semibold text-gray-800',
                htmlContainer: 'text-sm text-gray-500'
            },
            showCancelButton: false,
            showConfirmButton: false,
            reverseButtons: true,
            didOpen: () => {

                const titleEl = document.querySelector(".swal2-title");
                if (titleEl) {
                    titleEl.style.backgroundColor = "transparent"; // Or remove styling
                }

                const container = Swal.getHtmlContainer();
                const footer = document.createElement('div');
                footer.className = 'custom-button-container';
                footer.innerHTML = `
                <button class="swal2-cancel btn-outline" id="cancelBtn">Cancel</button>
                <button class="swal2-confirm btn-primary" id="createBtn">Logout</button>
            `;
                container.parentNode.appendChild(footer);

                document.getElementById('cancelBtn').addEventListener('click', () =>
                    Swal.close());

                document.getElementById('createBtn').addEventListener('click', () => {
                    window.location.href =
                        "<?php echo site_url('admission_controller/logout'); ?>";
                });

            }
        })
    });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
    .swal2-confirm.btn-primary {
        background-color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>;
        border: none;
        padding: 10px 18px;
        font-size: 14px;
        border-radius: 8px;
        font-weight: 600;
        min-width: 120px;
    }

    .swal2-cancel.btn-outline {
        background: transparent;
        color:<?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        border: 2px solid <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        padding: 10px 18px;
        font-size: 14px;
        border-radius: 8px;
        font-weight: 600;
        min-width: 120px;
    }

    .rounded-xl {
        border-radius: 12px;
        /* You can adjust this value */
    }

    .custom-button-container {
        display: flex;
        justify-content: center;
        /* Center the buttons horizontally */
        gap: 12px;
        /* Minimal horizontal space between buttons */
        margin-top: 10px;
        /* Slight vertical gap from message */
        display: flex;
        justify-content: center;
        /* Center the buttons horizontally */
        gap: 12px;
        /* Minimal horizontal space between buttons */
        margin-top: 10px;
        /* Slight vertical gap from message */
    }

    @media (max-width: 576px) {
        .panel-heading {
            flex-direction: column !important;
            align-items: flex-start !important;
        }

        .school_name_in_header {
            font-size: 16px !important;
        }

        #logoutBtn {
            width: 100%;
            justify-content: center;
            margin-top: 8px;
        }
    }
    </style>