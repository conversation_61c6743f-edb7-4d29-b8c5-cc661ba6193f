<div class="modal fade" id="add_learning_intention" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Learning Intention</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="intention_session_id" id="intention_session_id">
                <div class="form-group">
                    <label for="learning-intention" class="control-label">Learning Intention <font style="color: red;">*</font></label>
                    <textarea class="form-control" id="learning-intention" name="learning-intention" style="height: 11rem;" placeholder="Wanna Describe ?"></textarea>
                    <span id="learningIntentionError" style="display: none;"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_intention_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_intention_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="updateIntention()">Update</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // $("#add_learning_intention").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $("#learning-intention").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#learningIntentionError").hide();
            if($("#learning-intention").val() == ""){
                $("#learningIntentionError").html("Please enter learning intention").css("color", "red").show();
                return false;
            }
            updateLearningIntention();
            loadLearningIntention();
        }
    })

    function updateLearningIntention() {
        const id = $("#intention_session_id").val();
        const value = $("#learning-intention").val();
        const visible_to_students = $("#visible_intention_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/updateLearningIntention"); ?>',
            type: "POST",
            data: { id, value, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_learning_intention").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        getSessionData(id);
                        loadLearningIntention();
                        showMainModal();
                    });
                } else {
                    $("#add_learning_intention").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed to save activity",
                        text: "Please try again!",
                    }).then(() => {
                        $("#add_learning_intention").modal('show');
                    });
                }
            },
            error(err){
                console.log(err);
                $("#add_learning_intention").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed to save activity",
                    text: "Please try again!",
                }).then(() => {
                    $("#add_learning_intention").modal('show');
                });
            }
        })
    }

    function updateIntention() {
        $("#learningIntentionError").hide();
        if($("#learning-intention").val() == ""){
            $("#learningIntentionError").html("Please enter learning intention").css("color", "red").show();
            return false;
        }
        updateLearningIntention();
        loadLearningIntention();
    }
</script>