<div class="modal" tabindex="-1" role="dialog" id="view_book_resourceType">
    <div class="modal-dialog" role="document" id="view_book_resourceType_modal">
        <div class="modal-content" style="border-radius:1rem;width: 80%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Book Resources</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_book_resourceType_data">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('add_book_resources', 'view_book_resourceType')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Add More</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function loadBooks() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ book_resourceType } = data);
                if (!book_resourceType.length) {
                    $("#view_book_resourceType").trigger("click");
                }
                let html = `<table class="table table-bordered">
                        <tr class="bg-light">
                            <th>#</th>
                            <th>Book Name</th>
                            <th>Resource Detail</th>
                            <th style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Delete</th>
                        </tr>`

                book_resourceType.forEach((r, i) => {
                    html += `
                        <tr>
                            <td>${++i}</td>
                            <td id="book_${r.id}">${r.book_name}</td>
                            <td>${r.reference_detail}</td>
                            <td style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;"><a onClick="delete_book_resources('${r.id}')" class="remove btn btn-danger "><i class="fa fa-trash-o mr-0"></i></a></td>
                        </tr>`;
                })
                html + `</table>`
                $("#view_book_resourceType_data").html(html);
            }
        })
    }


    $("#view_book_resourceType").on("shown.bs.modal", e => {
        loadBooks();
    })

    function delete_book_resources(resource_id) {
        var book_name = $(`#book_${resource_id}`).text();
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('academics/lesson_plan/delete_resources_book_type_by_id') ?>',
                    type: 'post',
                    data: { 'resource_id': resource_id },
                    success: function (data) {
                        let parsedData = JSON.parse(data);
                        if (parsedData) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted',
                                text: 'Book Resource Deleted Successfully',
                                showConfirmButton: false,
                                timer: 1500,
                            }).then(() => {
                                loadBooks();
                                getSessionData(session_id);
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500,
                            })
                        }
                    },
                    error: function (data) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Something went wrong',
                            showConfirmButton: false,
                            timer: 1500,
                        })
                    }
                });
            }
        });
    }
</script>