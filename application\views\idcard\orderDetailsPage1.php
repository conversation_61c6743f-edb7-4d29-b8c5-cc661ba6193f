<?php
$is_digital = strtolower($order_details->id_card_type) === 'digital card';
?>
<div class="container my-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card p-3" style="border-radius: 10px; border: 1px solid #ddd;">
                    <h5 class="mb-3" style="padding: 8px;">Order Details</h5>
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="mb-1" style="font-weight: 500;">
                                <?= $order_details->id_card_for ?> 
                                <?php if (!$is_digital): ?>
                                    (cost per unit ₹<?= $order_details->unit_price ?>)
                                <?php endif; ?>
                            </p>
                            <small class="text-muted">Qty: <?= $order_details->quantity ?></small>
                            <?php if ($is_digital): ?>
                                <div class="digital-badge mt-2">
                                    <i class="fa fa-check-circle"></i> Digital Card
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (!$is_digital): ?>
                            <div class="text-end">
                                <p class="mb-0" style="font-weight: 500;"><?= $order_details->quantity ?> X ₹<?= $order_details->unit_price ?></p>
                                <p class="mb-0" style="font-weight: 500;"><?= format_inr($order_details->sub_total) ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php if (!$is_digital): ?>
                    <div class="card p-3 mt-3">
                        <h5 style="padding: 8px;">Order Summary</h5>
                        <div class="d-flex justify-content-between">
                            <span>Sub Total</span> <span><?= format_inr($order_details->sub_total) ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>cGST</span> <span><?= format_inr($order_details->c_gst) ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>sGST</span> <span><?= format_inr($order_details->s_gst) ?></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Grand Total</span> <span><?= format_inr($order_details->grand_total) ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <!-- <div class="col-md-4">
                <div class="card p-3">
                    <h5>Timeline</h5>
                    <ul class="timeline">
                        <li>ID card sent for printing <span class="text-muted" style="float: right;">Just now</span></li>
                        <li>All students data approved <span class="text-muted" style="float: right;">1 minute ago</span></li>
                        <li>Students data sent for approval <span class="text-muted" style="float: right;">5 minutes ago</span></li>
                        <li>Order created for student ID Card <span class="text-muted" style="float: right;">20 minutes ago</span></li>
                        <li>All students data approved <span class="text-muted" style="float: right;">1 minute ago</span></li>
                        <li>Students data sent for approval <span class="text-muted" style="float: right;">5 minutes ago</span></li>
                        <li>Order created for student ID Card <span class="text-muted" style="float: right;">20 minutes ago</span></li>
                    </ul>
                </div>
            </div> -->
        </div>
    </div>

    <style>
        
    .timeline {
        list-style: none;
        padding-left: 0;
        position: relative;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 0;
        width: 2px;
        height: 100%;
        background: #ddd;
    }
    .timeline li {
        position: relative;
        padding-left: 30px;
        margin-bottom: 10px;
    }
    .timeline li::before {
        content: '';
        position: absolute;
        left: 0;
        top: 4px;
        width: 12px;
        height: 12px;
        background: #fff;
        border: 2px solid #ddd;
        border-radius: 50%;
    }

    .form-step{
        display: none;
    }

    .form-step-active{
        display: block;
    }

    .digital-badge {
        display: inline-block;
        background-color: #e8f5e9;
        color: #2e7d32;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 14px;
    }

    .digital-badge i {
        margin-right: 5px;
    }

    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: box-shadow 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .card h5 {
        color: #2c3e50;
        font-weight: 600;
    }

    .text-muted {
        color: #6c757d !important;
    }
    </style>