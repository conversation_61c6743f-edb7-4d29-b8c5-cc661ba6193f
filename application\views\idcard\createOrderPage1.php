<div class="container col-md-12">
    <div class="row">
        <!-- Left half - Form fields -->
        <div class="col-md-12" id="form_container">
            <form id="orderForm1" data-parsley-validate>
                <div class="row">
                    <div class="form-group col-md-6">
                        <label for="orderName">Order Name <font color="red">*</font></label>
                        <input type="text" id="orderName" value="<?= isset($order_details->order_name) ? $order_details->order_name : '' ?>" name="orderName" data-parsley-error-message="Cannot be empty, Name is requried" required data-parsley-trigger="keyup" data-parsley-errors-container="#status_error" placeholder="Enter Name">
                        <span style="color: #858383;">Enter a friendly order name for your reference</span>
                        <div id="status_error" style="color: red;"></div>
                    </div>

                    <?php
                    // Define all possible verifier options
                    $allVerifierOptions = [
                        'by_school_admin' => 'By School Admin',
                        'by_class_teacher' => 'By Class Teacher',
                        'by_respective_staff' => 'By Respective Staff',
                        'by_parent' => 'By Parent'
                    ];

                    // Default verifier options (will be filtered by JavaScript)
                    $verifierOptions = $allVerifierOptions;

                    // Check if object exists and contains the verifier field
                    $selectedVerifiers = [];
                    if (isset($order_details) && isset($order_details->id_card_verifier)) {
                        $decoded = json_decode($order_details->id_card_verifier, true);
                        if (is_array($decoded)) {
                            $selectedVerifiers = $decoded;
                        }
                    }
                    ?>

                    <div class="form-group col-md-6">
                        <label for="idCardFor">ID Card For <font color="red">*</font></label>
                        <select id="idCardFor" required name="idCardFor" data-parsley-error-message="Cannot be empty, Idcard for is required" data-parsley-errors-container="#status_error_cardTypeFor" onchange="updateSelectionOptions(); updateVerifierOptions();">
                            
                            <option disabled <?= !isset($order_details->id_card_for) ? 'selected' : '' ?>>Select ID Card For</option>
                            
                            <option value="Staff"
                                <?= isset($order_details->id_card_type) && $order_details->id_card_for == "Staff" ? 'selected' : '' ?>>
                                Staff
                            </option>
                            
                            <option value="Student"
                                <?= !isset($order_details->id_card_for) || $order_details->id_card_for != "Staff" ? 'selected' : '' ?>>
                                Student
                            </option>

                            <option value="Parent"
                                <?= isset($order_details->id_card_for) && $order_details->id_card_for == "Parent" ? 'selected' : '' ?>>
                                Parent
                            </option>
                            
                        </select>
                        <div id="status_error_cardTypeFor" style="color: red;"></div>
                    </div>

                    <!-- Parent Type Selection -->
                    <div class="form-group col-md-6" id="parentTypeGroup" style="display: none;">
                        <label for="parentType">Parent Type <font color="red">*</font></label>
                        <select id="parentType" name="parentType" data-parsley-error-message="Cannot be empty, Parent type is required" data-parsley-errors-container="#status_error_parentType" required>
                            <option disabled selected>Select Parent Type</option>
                            <option value="Father">Father</option>
                            <option value="Mother">Mother</option>
                            <option value="Guardian">Guardian 1</option>
                            <option value="Guardian_2">Guardian 2</option>
                        </select>
                        <div id="status_error_parentType" style="color: red;"></div>
                    </div>

                    <div class="form-group col-md-6" id="verifier_div">
                        <div class="d-flex align-items-center">
                            <label for="verifier">Verifier <font color="red">*</font></label>
                            <button type="button" class="btn btn-sm help-btn ml-2" id="verifier-help-btn">
                                <i class="fa fa-question-circle text-info"></i>
                            </button>
                        </div>
                        <select id="verifier" class="select2" name="verifier[]" data-parsley-error-message="Cannot be empty, Verifier is requried" data-parsley-errors-container="#status_error_verifier" required multiple="multiple" data-placeholder="Select who will verify the ID cards">
                            <?php foreach ($verifierOptions as $value => $label): ?>
                                <option value="<?= $value ?>" <?= in_array($value, $selectedVerifiers) ? 'selected' : '' ?>>
                                    <?= $label ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span style="color: #858383;">Select who will verify the ID cards before printing</span>
                        <div id="status_error_verifier" style="color: red;"></div>
                    </div>

                    <div class="form-group col-md-6">
                        <label for="idCardType">ID Card Type <font color="red">*</font></label>
                        <select id="idCardType" name="idCardType" data-parsley-error-message="Cannot be empty, Idcard type is required" data-parsley-errors-container="#status_error_cardType" required>
                            <option disabled <?= empty($order_details->id_card_type) ? 'selected' : '' ?>>Select ID Card Type</option>
                            <option value="Normal Card" <?= isset($order_details->id_card_type) && $order_details->id_card_type == "Normal Card" ? 'selected' : '' ?>>Normal card</option>
                            <option value="RFID Card" <?= isset($order_details->id_card_type) && $order_details->id_card_type == "RFID Card" ? 'selected' : '' ?>>RFID card</option>
                            <option value="Digital Card" <?= isset($order_details->id_card_type) && $order_details->id_card_type == "Digital Card" ? 'selected' : '' ?>>Digital Card</option>
                        </select>
                        <div id="status_error_cardType" style="color: red;"></div>
                    </div>

                    <div class="form-group col-md-6" id="selectionGroup" style="display: none;">
                        <label id="selectionLabel" for="selectOptions"></label>
                        <select id="selectOptions" data-parsley-validate-if-empty data-parsley-error-message="Cannot be empty, group for is requried" data-parsley-errors-container="#status_error_selectionGroup" name="selectOptions" required>
                            <option>Select an Option</option>
                        </select>
                        <div id="status_error_selectionGroup" style="color: red;"></div>
                    </div>

                    <div class="form-group col-md-6" id="descriptionGroup">
                        <label id="descriptionLabel" for="description">Description</label>
                        <textarea id="description" name="description" class="form-control" placeholder="Enter a description for this order..." rows="3"><?= isset($order_details->description) ? $order_details->description : '' ?></textarea>
                        <span style="color: #858383;">Add details about this order for your reference</span>
                    </div>
                </div>
            </form>
        </div>

        <!-- Right half - Detailed profile type container -->
        <div class="col-md-6" id="detailed_profile_type_container" style="display: none;">
            <img scr="<?= base_url("assets/img/no_student_found.svg") ?>">
        <!-- dynamic data using ajax -->
        <!-- copy this html code and paste in the js for dynamic data -->
        <!-- using demo data for the demo -->
        <div class="container-page-1 " >
            <div class="header">Select Students</div>

            <div class="search-container">
                <input type="text" class="search-input col-md-8" placeholder="Search Students">
                <select class="form-select col-md-2">
                    <option>6th</option>
                </select>
                <select class="form-select col-md-2">
                    <option>Section</option>
                </select>
            </div>

            <div class="student-count">
                220/500 Students Selected
                <button id="showSelectedBtn" onclick="show_selected_students()" class="btn btn-link">Show Selected</button>
            </div>

            <div class="table-container scroller">
                <table>

                </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Form layout styles */
    .form-group {
        margin-bottom: 20px;
    }

    /* Fix for verifier field width */
    #verifier_div {
        overflow: hidden; /* Prevent content from spilling out */
    }

    /* Help button styling */
    .help-btn {
        padding: 0;
        background: transparent;
        border: none;
        line-height: 1;
        margin-left: 5px;
        cursor: pointer;
    }

    .help-btn i {
        font-size: 16px;
    }

    .help-btn:focus, .help-btn:active {
        outline: none;
        box-shadow: none;
    }

    #verifier_div .select2-container {
        width: 100% !important;
        max-width: 100%;
    }

    /* Ensure form fields don't overlap when right section is visible */
    @media (min-width: 768px) {
        .col-md-4 #verifier_div .select2-container,
        .col-md-4 #descriptionGroup textarea {
            max-width: 100%;
            width: 100% !important;
        }

        /* Adjust textarea height when in narrow layout */
        .col-md-4 #description {
            min-height: 100px;
        }
    }

    /* Make sure the right container takes full height */
    #detailed_profile_type_container {
        height: 100%;
        padding: 0;
    }

    /* Loading spinner styles */
    .spinner-border {
        display: inline-block;
        width: 3rem;
        height: 3rem;
        vertical-align: text-bottom;
        border: 0.25em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border .75s linear infinite;
    }

    @keyframes spinner-border {
        to { transform: rotate(360deg); }
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    /* Alert styles */
    .alert {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.25rem;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    /* Button styles */
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .btn-primary:hover {
        color: #fff;
        background-color: #0069d9;
        border-color: #0062cc;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .text-center {
        text-align: center !important;
    }

    /* Style for the header to make it look non-clickable */
    #detailed_profile_type_container .header {
        cursor: default;
        user-select: none;
        font-weight: bold;
        padding: 10px 0;
        color: #333;
    }

    /* Styles for the empty state */
    .empty-state {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 30px;
        margin: 20px 0;
    }

    .empty-state h4 {
        color: #495057;
        margin-bottom: 15px;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 20px;
    }

    /* Styles for the selection popup */
    .selection-popup-container {
        padding: 10px;
    }

    .selection-popup-container .table {
        margin-bottom: 0;
    }

    .selection-popup-container .table th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
    }

    /* Highlight selected row */
    .selection-popup-container .table tr:has(input:checked) {
        background-color: #e2f0ff;
    }

    /* Filter styles - scoped to selection popup only with specific class names */
    .selection-popup-container .popup-filter-select {
        min-width: 150px;
        font-size: 14px;
    }

    .selection-popup-container .popup-form-group {
        display: flex;
        align-items: center;
        margin-bottom: 0;
    }

    .selection-popup-container .popup-filter-label {
        margin-bottom: 0;
        white-space: nowrap;
        font-weight: 500;
    }

    /* Make the search container responsive */
    @media (max-width: 768px) {
        .selection-popup-container .row > div {
            margin-bottom: 10px;
        }

        .selection-popup-container .d-flex {
            flex-wrap: wrap;
        }

        .selection-popup-container .popup-form-group {
            margin-right: 0 !important;
            margin-bottom: 10px;
            width: 100%;
        }
    }

    /* Style for the selected data table */
    #selected-data-container {
        margin-top: 20px;
    }

    #selected-data-container .student-count {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
    }

    /* Style for the action buttons */
    .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
    }

    /* Style for the buttons - Bootstrap 4 compatible */
    #refresh-staff-btn, #refresh-student-btn {
        background-color: white;
        border: 1px solid #6c757d;
        color: #6c757d;
        border-radius: 4px;
        transition: all 0.2s ease;
        padding: 5px 13px;
        white-space: nowrap;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 100px;
    }

    #refresh-staff-btn:hover, #refresh-student-btn:hover {
        background-color: #f8f9fa;
        color: #5a6268;
        border-color: #6c757d;
    }

    #refresh-staff-btn:focus, #refresh-student-btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        outline: none;
    }

    #add-more-staff-btn, #add-more-student-btn {
        background-color: #007bff;
        border: none;
        color: white;
        border-radius: 4px;
        transition: all 0.2s ease;
        padding: 5px 13px;
        white-space: nowrap;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 120px;
    }

    #add-more-staff-btn:hover, #add-more-student-btn:hover {
        background-color: #0069d9;
        color: white;
    }

    #add-more-staff-btn:focus, #add-more-student-btn:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    /* Style for the count display */
    .student-count {
        padding: 10px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .student-count strong {
        font-size: 1.1em;
        color: #007bff;
    }

    .remove-item-btn{
        border-radius: 4px;
    }

    /* Table styling - Bootstrap 4 compatible */
    #profile-data-table {
        font-size: 0.95rem;
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        background-color: #fff;
    }

    #profile-data-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
        text-transform: uppercase;
        font-size: 13px;
    }

    #profile-data-table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    /* Highlight row on hover */
    #profile-data-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.075);
    }

    /* Table hover effect */
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.075);
    }

    /* Status badge styling - Bootstrap 4 compatible */
    .badge-light {
        display: inline-block;
        padding: 0.25em 0.6em;
        border-radius: 0.25rem;
        font-size: 85%;
        font-weight: 500;
        white-space: nowrap;
        text-align: center;
        min-width: 80px;
        background-color: #f8f9fa;
        color: #212529;
    }

    /* Badge primary styling */
    .badge-primary {
        background-color: #007bff;
        color: #fff;
        border-radius: 0.25rem;
    }

    /* Empty state styling */
    .empty-state {
        padding: 30px;
        background-color: #f9f9f9;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .empty-state img {
        max-width: 150px;
        margin-bottom: 20px;
        opacity: 0.8;
    }

    .empty-state h4 {
        color: #495057;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .container-page-1 {
        background: #f8f9fa;
        border-radius: 8px;
        overflow: auto;
        height: 100%;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        border: 1px solid #dee2e6;
    }
    .container-page-1 .header {
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 15px;
        color: #333;
        padding-bottom: 10px;
        border-bottom: 2px solid #6200EE;
        display: inline-block;
    }
    .search-container {
        margin: 0px;
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }
    .container-page-1 input, select {
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 5px;
        font-size: 14px;
        background-color: white;
    }
    .container-page-1 .search-input {
        flex: 1;
    }
    .student-count {
        font-size: 14px;
        margin-bottom: 10px;
    }
    #showSelectedBtn {
        float: right;
    }
    .table-container {
        max-height: 400px;
        overflow-y: auto;
        background: white;
        border-radius: 0.25rem;
        border: 1px solid #dee2e6;
        margin-bottom: 1.5rem;
    }
    thead {
        position: sticky;
        top: 0;
        z-index: 10; /* Ensures it stays above the rows */
        background: white; /* Ensures it remains visible */
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1); /* Adds a shadow for better visibility */
    }
    table {
        width: 100%;
        border-collapse: collapse;
    }
    th, td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
        font-size: 14px;
    }
    th {
        background-color: #f5f5f5;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    .checkbox {
        width: 18px;
        height: 18px;
        cursor: pointer;
        border: 1px solid #6200EE;
        border-radius: 3px;
    }

    .checkbox:checked {
        background-color: #6200EE;
        border-color: #6200EE;
    }
    .scroller {
        scrollbar-width: thin;
    }

    .custom-swal-popup .swal2-actions {
        justify-content: flex-end !important; /* Align buttons to the right */
        width: 95%; /* Ensure it takes full width */
    }

    .custom-swal-popup {
        width: 800px !important;
        max-width: 90%;
    }
    .student-table {
        max-height: 300px;
        overflow-y: auto;
    }
    table {
        width: 100%;
        border-collapse: collapse;
    }
    th, td {
        border-bottom: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    .select2-container--default .select2-selection--multiple .select2-selection__rendered{
        margin: 5px !important;
    }
    .select2-container--default .select2-selection--multiple .select2-selection__choice{
        background-color: #ffffff;
    }
    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove{
        margin-right: 5px;
        color: #000000;
    }

    /* Style for Select2 placeholder */
    .select2-container--default .select2-selection--multiple .select2-search__field::placeholder {
        color: #999;
        opacity: 0.8;
    }

    /* Style for Select2 container */
    .select2-container--default .select2-selection--multiple {
        min-height: 38px;
        padding: 2px;
    }

    /* Style for the helper text */
    .form-group span {
        display: block;
        margin-top: 5px;
        font-size: 12px;
    }

    /* Textarea styling */
    #description {
        width: 100%;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        resize: vertical;
        min-height: 80px;
    }

    #description:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    #description::placeholder {
        color: #999;
        opacity: 0.8;
    }
</style>

<script>
    // Function to handle layout changes based on detailed profile container visibility
    function updateLayoutBasedOnDetailedContainer() {
        const formContainer = document.getElementById("form_container");
        const detailedContainer = document.getElementById("detailed_profile_type_container");
        const formFields = document.querySelectorAll("#orderForm1 .form-group");

        if (detailedContainer.style.display === "none") {
            // When detailed container is hidden, form takes full width
            formContainer.className = "col-md-12";

            // Make form fields 2 per row (col-md-6)
            formFields.forEach(field => {
                field.className = field.className.replace("col-md-12", "col-md-6");
            });
        } else {
            // When detailed container is shown, form takes 1/3 width
            formContainer.className = "col-md-4";

            // Make form fields 1 per row (col-md-12)
            formFields.forEach(field => {
                field.className = field.className.replace("col-md-6", "col-md-12");
            });
        }
    }

    // Call this function on page load to set initial layout
    document.addEventListener("DOMContentLoaded", function() {
        updateLayoutBasedOnDetailedContainer();

        // Initialize Select2 with placeholder
        if ($.fn.select2) {
            $('#verifier').select2({
                placeholder: "Select who will verify the ID cards",
                allowClear: true,
                width: 'resolve', // This will make Select2 respect the container width
                dropdownAutoWidth: true
            });

            // Force the Select2 container to respect its parent width
            setTimeout(() => {
                const verifierDiv = document.getElementById('verifier_div');
                if (verifierDiv) {
                    const select2Container = verifierDiv.querySelector('.select2-container');
                    if (select2Container) {
                        select2Container.style.width = '100%';
                        select2Container.style.maxWidth = '100%';
                    }
                }
            }, 0);
        }

        // Initialize verifier options based on current selection
        updateVerifierOptions();
    });

    // Function to update verifier options based on ID Card For selection
    function updateVerifierOptions() {
        const idCardFor = document.getElementById('idCardFor').value;
        const verifierSelect = document.getElementById('verifier');
        const parentTypeGroup = document.getElementById('parentTypeGroup');

        // Handle parent type select visibility
        if (idCardFor === 'Parent') {
            parentTypeGroup.style.display = 'block';
            document.getElementById('parentType').setAttribute('required', 'required');
        } else {
            parentTypeGroup.style.display = 'none';
            document.getElementById('parentType').removeAttribute('required');
        }

        // Store currently selected values
        const selectedOptions = Array.from(verifierSelect.selectedOptions).map(option => option.value);

        // Define available options based on ID Card For
        let availableOptions = [];

        if (idCardFor === 'Staff') {
            // For Staff: School Admin or Respective Staff
            availableOptions = [
                { value: 'by_school_admin', text: 'By School Admin' },
                { value: 'by_respective_staff', text: 'By Respective Staff' }
            ];
        } else if (idCardFor === 'Student') {
            // For Student: School Admin or Class Teacher
            availableOptions = [
                { value: 'by_school_admin', text: 'By School Admin' },
                { value: 'by_class_teacher', text: 'By Class Teacher' },
                { value: 'by_parent', text: 'By Parent' }
            ];
        } else if (idCardFor === 'Parent') {
            // For Parent: School Admin or Class Teacher
            availableOptions = [
                { value: 'by_school_admin', text: 'By School Admin' },
                { value: 'by_class_teacher', text: 'By Class Teacher' }
            ];
        } else {
            // Default options if nothing is selected or for other types
            availableOptions = [
                { value: 'by_school_admin', text: 'By School Admin' },
                { value: 'by_class_teacher', text: 'By Class Teacher' },
                { value: 'by_respective_staff', text: 'By Respective Staff' },
                { value: 'by_parent', text: 'By Parent' }
            ];
        }

        // Clear existing options
        verifierSelect.innerHTML = '';

        // Add new options
        availableOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;

            // Select the option if it was previously selected
            if (selectedOptions.includes(option.value)) {
                optionElement.selected = true;
            }

            verifierSelect.appendChild(optionElement);
        });

        // Refresh Select2 if it's initialized
        if ($.fn.select2) {
            $(verifierSelect).select2('destroy').select2({
                placeholder: "Select who will verify the ID cards",
                allowClear: true,
                width: 'resolve', // This will make Select2 respect the container width
                dropdownAutoWidth: true
            });

            // Force the Select2 container to respect its parent width
            setTimeout(() => {
                const verifierDiv = document.getElementById('verifier_div');
                if (verifierDiv) {
                    const select2Container = verifierDiv.querySelector('.select2-container');
                    if (select2Container) {
                        select2Container.style.width = '100%';
                        select2Container.style.maxWidth = '100%';
                    }
                }
            }, 0);
        }
    }

    function updateSelectionOptions() {
        let idCardFor = document.getElementById("idCardFor").value;
        let selectionGroup = document.getElementById("selectionGroup");
        let selectionLabel = document.getElementById("selectionLabel");
        let selectOptions = document.getElementById("selectOptions");
        let detailedContainer = document.getElementById("detailed_profile_type_container");

        // Reset the global variables when ID card type changes
        window.currentSelectedItems = [];
        window.currentProfileType = null;
        window.popupData = {};

        // Reset the detailed container to its initial state
        if (detailedContainer) {
            detailedContainer.style.display = "none";
        }

        // Reset form container width to full
        const formContainer = document.getElementById("form_container");
        if (formContainer) {
            formContainer.className = "col-md-12";
            // Update form field widths to two per row
            const formFields = document.querySelectorAll("#orderForm1 .form-group");
            formFields.forEach(field => {
                field.className = field.className.replace("col-md-12", "col-md-6");
            });
        }

        // Show the selection dropdown
        selectionGroup.style.display = "block";

        // Update label and options based on selection
        if (idCardFor === "Student") {
            selectionLabel.innerText = "Select Student";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_students">All Students</option>
                <option value="student_subset">Student Subset</option>
            `;
        } else if (idCardFor === "Staff") {
            selectionLabel.innerText = "Select Staff";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_staff">All Staff</option>
                <option value="staff_subset">Staff Subset</option>
            `;
        } else if (idCardFor === "Parent") {
            selectionLabel.innerText = "Select Parent";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_parents">All Parents</option>
                <option value="parent_subset">Parent Subset</option>
            `;
        } else {
            selectionGroup.style.display = "none";
            selectOptions.value = ""; // Reset the value
        }

        // **Revalidate Parsley when field is displayed**
        window.Parsley && $(selectOptions).parsley().reset();
    }

    document.addEventListener("DOMContentLoaded", function () {
        updateSelectionOptions(); // populate options based on initial selection

        // After options are populated, select the appropriate value
        <?php if (isset($order_details->id_card_for_type)) : ?>
            setTimeout(() => {
                document.getElementById("selectOptions").value = "<?= $order_details->id_card_for_type ?>";
                // Call the function to show/hide detailed_profile_type_container based on initial selection
                get_gata_selected_profile_type();
            }, 100); // timeout to make sure options are injected first
        <?php endif; ?>

        // Add event listener to selectOptions dropdown
        document.getElementById("selectOptions").addEventListener("change", function() {
            get_gata_selected_profile_type();
        });

        // Add event listener to idCardFor dropdown to handle changes
        const idCardForElement = document.getElementById("idCardFor");
        if (idCardForElement) {
            idCardForElement.addEventListener("change", function() {
                // Reset the detailed container
                const detailedContainer = document.getElementById("detailed_profile_type_container");
                const formContainer = document.getElementById("form_container");
                
                if (detailedContainer) {
                    // Reset display
                    detailedContainer.style.display = "none";
                    detailedContainer.innerHTML = ''; // Clear the content
                }
                
                if (formContainer) {
                    // Reset form container to full width
                    formContainer.className = "col-md-12";
                    // Reset form fields to two per row
                    const formFields = document.querySelectorAll("#orderForm1 .form-group");
                    formFields.forEach(field => {
                        field.className = field.className.replace("col-md-12", "col-md-6");
                    });
                }

                // Reset global variables
                window.currentSelectedItems = [];
                window.currentProfileType = null;
                window.popupData = {};

                // Reset the selectOptions dropdown
                const selectOptions = document.getElementById("selectOptions");
                if (selectOptions) {
                    selectOptions.selectedIndex = 0;
                }

                // Update selection options for the new ID card type
                updateSelectionOptions();

                // If we're on step 1, reload templates
                if (typeof currentStep !== 'undefined' && currentStep === 1) {
                    templates = [];
                    setTimeout(() => {
                        loadTemplatesViaAjax(true);
                    }, 100);
                }
            });
        }
    });
</script>