<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  23 May 2018
 *
 * Description: Model class for Class Master table
 *
 * Requirements: PHP5 or above
 *
 */

class Student_attendance extends CI_Controller {
	function __construct() {
		parent::__construct();
		
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		
		$this->load->model('report/Student_attendance_model');
		$this->load->model('report/fee_report_model');
		date_default_timezone_set('Asia/Kolkata');
		$this->load->model('student/Student_Model','student');
		$this->load->model('attendance/Attendance_model','attendanceModel');
		$this->load->helper('attendance_helper');
      }

	public function index() {
		$data['main_content'] = 'reports/attendance/report/index';
		$this->load->view('inc/template', $data);
	}  

	public function reports($type) {
		$data['getclassinfo'] = $this->student->getclass();
		$data['type'] = $type;
		$show_all_sections = $this->authorization->isAuthorized('STUDENT_ATTENDANCE.SHOW_ALL_SECTIONS');
		$data['class_section'] = $this->attendanceModel->getAllClassandSection($show_all_sections);
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'reports/attendance/report/selectParams_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'reports/attendance/report/selectParams_mobile';
        }else{
          $data['main_content'] = 'reports/attendance/report/selectParams';  	
        }
		$this->load->view('inc/template', $data);
	}
	

	private function _prepareAttendanceData($details) {

		$preresult = [];
		$day = [];

		foreach ($details as $k => $v) {


			$day[$v['day']] = $v['day'];

			$data = ['id' => $v['id'],
                    'attendance_session_id' => $v['attendance_session_id'],
                    'attendance_master_id' => $v['attendance_master_id'],
                    'attendance_master_group_id' => $v['attendance_master_group_id'],
                    'reference_type' => $v['reference_type'],
                    'reference_id' => $v['reference_id'],
                    'reference_status' => $v['reference_status'],
                    'status' => $v['status'],
                    'day' => $v['day']
                    ];

			if(isset($preresult[$v['student_id']])) {
				$preresult[$v['student_id']][$data['day']][] = $data;
			} else {
				$preresult[$v['student_id']]['student'] = 
					['student_id' => $v['student_id'],
                    'roll_no' => $v['roll_no'],
                    'admission_no' => $v['admission_no'],
                    'std_name' => $v['std_name'],
                    'csName' => $v['csName'],
                    'id' => $v['id'],
                    'enrollment_number' => $v['enrollment_number']
                    ];
                $preresult[$v['student_id']][$data['day']][] = $data;
			}
		}

		foreach ($preresult as $key => $value) {

			$temp_arr = $day; 
			$only_keys = array_keys($value);

			foreach ($temp_arr as $key1 => $value1) {
				if(in_array($key1, $only_keys)) {
					unset($temp_arr[$key1]);
				}
			}

			if(!empty($temp_arr)) {
				foreach ($temp_arr as $key3 => $value3) {
					$preresult[$key][$key3] = [];
				}

				foreach ($day as $key8 => $value) {
					$new_arr[$key8] = $preresult[$key][$key8];
					unset($preresult[$key][$key8]);
				}

				$preresult[$key] = array_merge($new_arr, $preresult[$key]);
			}		

		}

		// based on timestamp
		function compareByTimeStamp($time1, $time2)
		{
		    if (strtotime($time1) > strtotime($time2))
		        return 1;
		    else if (strtotime($time1) < strtotime($time2)) 
		        return -1;
		    else
		        return 0;
		}
	 
	
		// sort array with given user-defined function
		usort($day, "compareByTimeStamp");

		// echo '<pre>'; print_r($day); die();

		return ['result' => $preresult, 'days' => $day];

	}

	public function _prepareAttendanceMonthwiseData($details) {

		// echo "<pre>"; print_r($details); die();
		$preresult = [];
		$month_year_arr = [];

		foreach ($details as $k => $v) {
			$v['M'] = date('M',strtotime($v['day']));
			$v['Y'] = date('Y',strtotime($v['day']));
			$month_year = $v['M'].'_'.$v['Y'];
			// $data = $v;
			$month_year_arr[$month_year] = date('M-Y', strtotime('01-'.$v['M'].'-'.$v['Y']));
			
			$data = ['Y' => $v['Y'],
                    'M' => $v['M'],
                    'attendance_days' => $v['attendance_days'],
                    'attendance' => $v['attendance'],
                    'refattendance' => $v['refattendance']
                    ];

			if(isset($preresult[$v['student_id']])) {
				$preresult[$v['student_id']][$month_year][] = $data;
			} else {
				$preresult[$v['student_id']]['student'] = 
					['student_id' => $v['student_id'],
                    'roll_no' => $v['roll_no'],
                    'admission_no' => $v['admission_no'],
                    'sts_number' => $v['sts_number'],
                    'pen_number' => $v['pen_number'],
                    'std_name' => $v['std_name'],
                    'csName' => $v['csName']
                    ];
                $preresult[$v['student_id']][$month_year][] = $data;
			}
		}
		// echo "<pre>"; print_r($preresult);die();

		foreach ($preresult as $key => $value) {

			$temp_arr = $month_year_arr; 
			$only_keys = array_keys($value);

			foreach ($temp_arr as $key1 => $value1) {
				if(in_array($key1, $only_keys)) {
					unset($temp_arr[$key1]);
				}
			}

			if(!empty($temp_arr)) {
				foreach ($temp_arr as $key3 => $value3) {
					$preresult[$key][$key3] = [];
					ksort($preresult[$key]);
				}
				
			}
		}

		return ['result' => $preresult, 'month_year' => $month_year_arr];

	}


	public function _prepareAttendanceMonthwiseData_new($details, $ma_periods) {
		// trigger_error('prepare report block start');
		$attendance = [];
		foreach ($details as $key => $val) {
			$attendance[$val['student_id']][$val['day']][] = [
				'status' => $val['status'],
				'day' => $val['day'],
				'reference_status' => $val['reference_status'],
				'reference_type' => $val['reference_type'],
			];
		}

		$att_status_total = [];
		foreach ($attendance as $student_id => $dates) {
			foreach ($dates as $date => $val) {
				if (!empty($val)) {
					$state = prepare_attendance_count($val, $ma_periods);
					$daywise = $state['day']['status'] + $state['competation']['status'];
					if (!isset($att_status_total[$student_id][$date])) {
						$att_status_total[$student_id][$date]['present'] = 0;
						$att_status_total[$student_id][$date]['absent'] = 0;
					}
					if ($daywise > 0) {
						if($daywise =='0.5'){
							$att_status_total[$student_id][$date]['present'] += $daywise;
							$att_status_total[$student_id][$date]['absent'] += $daywise;
						}else{
							$att_status_total[$student_id][$date]['present'] += $daywise;
						}
					} else {
						$att_status_total[$student_id][$date]['absent'] += 1;
					}
				} else {
					$att_status_total[$student_id][$date]['present'] = 0;
					$att_status_total[$student_id][$date]['absent'] = 0;
				}
			}
		}

		$monthly_attendance = [];
		foreach ($att_status_total as $student_id => $dates) {
			foreach ($dates as $date => $data) {
				$month_year = date('m-Y', strtotime($date));
				if (!isset($monthly_attendance[$student_id][$month_year])) {
					$monthly_attendance[$student_id][$month_year] = ['present' => 0, 'absent' => 0];
				}
				$monthly_attendance[$student_id][$month_year]['present'] += $data['present'];
				$monthly_attendance[$student_id][$month_year]['absent'] += $data['absent'];
			}
		}
		

		$attendance = [];
		$month_year_arr = [];

		
		foreach ($details as $val) {
			$val['M'] = date('M', strtotime($val['day']));
			$val['Y'] = date('Y', strtotime($val['day']));
			$month_year =  date('m', strtotime($val['day'])) . '-' . date('Y', strtotime($val['day']));
			$month_year_arr[$month_year] = date('M-Y', strtotime('01-' . $val['M'] . '-' . $val['Y']));
			if (!isset($attendance[$val['student_id']])) {
				$attendance[$val['student_id']] = [
					'student_id' => $val['student_id'],
					'roll_no' => $val['roll_no'],
					'admission_no' => $val['admission_no'],
					'std_name' => $val['std_name'],
					'csName' => $val['csName'],
					'pen_number' => $val['pen_number'],
					'sts_number' => $val['sts_number'],
					'id' => $val['id'],
					'attendance' => [],
				];
			}
			$attendance[$val['student_id']]['attendance'] = $monthly_attendance[$val['student_id']];
		}
		// trigger_error('prepare report block end');
		return array('result'=>$attendance, 'month'=>$month_year_arr);
	}

	public function processReport() {
		// trigger_error('processReport() called');
		$input = $this->input->post();
		if(isset($input['report_month_to'])){
			if(empty($input['report_month_to'])){
				$input['report_month_to']=$input['report_month_from'];
			}
			
		}
		// trigger_error('in processReport() after isset check');
		
		$allDetails = $this->student->getclassection_NEW($input['classsection']);
		$data['class_section'] = $allDetails[0];
		$input['classid'] = $allDetails[0]->class_id;
		$grade_section=$allDetails[0]->class_name.' '.$allDetails[0]->section_name;
		$data['grade_section']=$grade_section;
		$data['input'] = $input;
		$data['ma_periods'] = $this->attendanceModel->get_timetable_details_for_day_attendance($input['classid'], $input['classsection']);
		$data['session_data'] = false;
		$viewPage = '';
		if($input['report_type'] == 1) {
			$details = $this->Student_attendance_model->get_attendance_details_for_monthly_report($input);
			$data['data'] = $this->_prepareAttendanceData($details);
			$viewPage = 'daywise';
		} elseif ($input['report_type'] == 3) {
            // trigger_error('In processReport() function month wise intiated');
            $from_date = DateTime::createFromFormat('d-m-Y', $input['report_month_from']);
            $to_date = DateTime::createFromFormat('d-m-Y', $input['report_month_to']);
            if (!$from_date || !$to_date) {
                $data['error'] = 'Invalid date format for report_month_from or report_month_to.';
                $data['data'] = [];
                $data['main_content'] = 'reports/attendance/report/type/monthwise';
                $this->load->view('inc/template', $data);
                return;
            }
            $input['report_date_from'] = $from_date->format('d-m-Y');
            $input['report_date_to'] = $to_date->format('d-m-Y');
		
			$details = $this->Student_attendance_model->get_attendance_details_for_monthly_report($input);
			
			$monthdata = $this->_prepareAttendanceMonthwiseData_new($details, $data['ma_periods']);
			$data['data'] = $monthdata;
			$viewPage = 'monthwise';
		}
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'reports/attendance/report/type/'.$viewPage.'_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'reports/attendance/report/type/'.$viewPage.'_mobile';
        }else{
          $data['main_content'] = 'reports/attendance/report/type/'.$viewPage;     	
        }
		$this->load->view('inc/template', $data);
	}

    public function special_case_report() {
		//echo '<pre>';print_r($this->input->post());die();
		$selectedDate = $this->input->post('selectedDate');
		$selectedRemarks = $this->input->post('report_remarks');
		$classId = $this->input->post('className');
		$sessionName = $this->input->post('sessionName');
		if ($classId == null)
			$classId = array ();
		$data['selectedDate']    = $selectedDate;
		$data['selectedRemarks'] = $selectedRemarks;
		$data['selectedClasses'] = $classId;
		$data['selectedSessionName'] = $sessionName;
		//echo $selectedDate;die();
		// $data['classList'] =  $this->fee_report_model->get_Allclasses();
		$show_all_sections = $this->authorization->isAuthorized('STUDENT_ATTENDANCE.SHOW_ALL_SECTIONS');
		$data['classList'] =  $this->fee_report_model->get_permitted_classes($show_all_sections);
		$data['sessions'] = $this->Student_attendance_model->getSessions();
		
		if (!empty($selectedDate)) {
			$data['result'] = $this->Student_attendance_model->getStdAttendanceReports($selectedDate, $selectedRemarks,$classId, $sessionName);
		}
		// echo '<pre>';print_r($data['result']);die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'reports/attendance/special_cases_tablet';
		}else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'reports/attendance/special_cases_mobile';
		}else{
			$data['main_content'] = 'reports/attendance/special_cases';    	
		}
    	$this->load->view('inc/template', $data);
  	}

	public function get_session_class_wise(){
		$result =$this->Student_attendance_model->get_session_class_wise();
		echo json_encode($result);
	}

  	public function smsAttendace_preview(){
	  	$remarks = $this->input->post('remarks');
	  	$date = $this->input->post('date');
		$stdId = $this->input->post('resId');
		$sent_to =$this->input->post('sent_to');
		$session_name = $this->input->post('session_name');
		$communication_mode = $this->input->post('communication_mode');
		$result = $this->Student_attendance_model->getStdParentphoneNumbers($stdId,$sent_to);
		if (empty($result)) {
			return false;
		}
		$html_str = '<thead><tr><th>#</th><th>Number</th><th>Relation</th><th>Message</th></tr></thead><tbody>';
		$i = 1;
		$message_for_credit_calculation = '';
		$school_name = $this->settings->getSetting('school_name');
		if($remarks == 'absentees') {
			$att_absent_sms_message = $this->settings->getSetting('student_attendance_absentee_sms_message');
			foreach ($result as $key => $res){
				$sms_sontent = $att_absent_sms_message;
				$sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
				$sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
				$sms_sontent = str_replace('%date%', $date, $sms_sontent);
				$sms_sontent = str_replace('%week_day%',date("l", strtotime($date)), $sms_sontent);
				$sms_sontent = str_replace('%session%', implode(', ', $session_name), $sms_sontent);
				$sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
				$html_str .= '<tr>';
				$html_str .= '<td>'.$i.'</td>';
				$html_str .= '<td>'.(($res->mobile_no=='')?'No number':$res->mobile_no).'</td>';
				$html_str .= '<td>'.$res->relation.'</td>';
				$html_str .= '<td>'.$sms_sontent.'</td>';
				$html_str .= '</tr>';
				$i++;
				if(strlen($message_for_credit_calculation) < strlen($sms_sontent)) {
	                $message_for_credit_calculation = $sms_sontent;
	                //get the largest message for credits calculation
	            }
			}
		}
		if($remarks == 'latecomer') {
			$att_latecomer_sms_message = $this->settings->getSetting('student_attendance_latecomer_sms_message');
			foreach ($result as $key => $res){
				$sms_sontent = $att_latecomer_sms_message;
				$sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
				$sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
				$sms_sontent = str_replace('%date%', $date, $sms_sontent);
				$sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
				$html_str .= '<tr>';
				$html_str .= '<td>'.$i.'</td>';
				$html_str .= '<td>'.(($res->mobile_no=='')?'No number':$res->mobile_no).'</td>';
				$html_str .= '<td>'.$res->relation.'</td>';
				$html_str .= '<td>'.$sms_sontent.'</td>';
				$html_str .= '</tr>';
				$i++;
				if(strlen($message_for_credit_calculation) < strlen($sms_sontent)) {
	                $message_for_credit_calculation = $sms_sontent;
	                //get the largest message for credits calculation
	            }
			}
		}
		$html_str .= "</tbody>";
		$is_credits_available = 0;
		if($communication_mode == '' || $communication_mode == 'sms' || $communication_mode == 'notification_sms') {
			$this->load->helper('texting_helper');
			if($sent_to == '') {
				$sent_to = 'both';
			}
	        $is_credits_available = checkCredits($message_for_credit_calculation, 1, 'parent', $sent_to);
		} 

		echo json_encode(array('html' => $html_str, 'credits_available' => $is_credits_available));
  	}

  	public function sms_attendance_send(){
		$text_send_to = $this->input->post('sent_to'); 
	  	$remarks = $this->input->post('remarks');
	  	$date = $this->input->post('date');
	  	$stdId = $this->input->post('resId'); 
		$session_name = $this->input->post('session_name');
	  	$sent_by = $this->authorization->getAvatarId();	
		$result = $this->Student_attendance_model->getStdInfo($stdId);
		$shIds_msg = array();
		if (empty($result)) {
			echo 0;
			return;
		}
		$school_name = $this->settings->getSetting('school_name');
	  	if ($remarks == 'absentees') {
	  		foreach ($result as $key => $res) {
	  			$sms_sontent = $this->settings->getSetting('student_attendance_absentee_sms_message');
	  			$sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
				$sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
				$sms_sontent = str_replace('%date%', $date, $sms_sontent);
				$sms_sontent = str_replace('%session%', implode(', ', $session_name), $sms_sontent);
				$sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
				//$sms_content =  'Your ward '.$res->std_name.' of '.$res->csName.' is absent today '.$date.'. Kindly ignore the sms if you have already informed the school through email. Principal - NPSRNR';
				$shIds_msg[$res->id] = $sms_sontent;
	  		}
	  		//$msg = "Your ward <std_name> of <class_section> is absent today ".$date.". Kindly ignore the sms if you have already informed the school through email. Principal - NPSRNR";
		}elseif ($remarks == 'latecomer') {
			foreach ($result as $key => $res) {
				$sms_sontent = $this->settings->getSetting('student_attendance_latecomer_sms_message');
				$sms_sontent = str_replace('%std_name%', $res->std_name, $sms_sontent);
				$sms_sontent = str_replace('%cs_name%', $res->csName, $sms_sontent);
				$sms_sontent = str_replace('%date%', $date, $sms_sontent);
				$sms_sontent = str_replace('%school_name%', $school_name, $sms_sontent);
				//$sms_content =  'Your ward '.$res->std_name.' of '.$res->csName.' has come to school late today '.$date.'. Principal - NPSRNR';
				$shIds_msg[$res->id] = $sms_sontent;
  			}
  			//$msg = "Your ward <std_name> of <class_section> has come to school late today ".$date.". Principal - NPSRNR";	
 		}
 		
		$input = array();
		$this->load->helper('texting_helper');
		$input['student_id_messages'] = $shIds_msg;
		$input['source'] = 'Attendance';
		$input['mode'] = $this->input->post('communication_mode');
		$insId1 = 0;
		$insId2 = 0;
		if($input['mode'] == 'notification') {
			$input['send_to'] = 'Both';
			$res = sendUniqueText($input);
			if($res['success'] != ''){
				$insId1 = 1;
			} else {
				$insId1 = 0;
			}
		} else {
			if($text_send_to == 'preferred') {
				$input['send_to'] = 'preferred';
				$preferred = sendUniqueText($input);
				if($preferred['success'] != ''){
					$insId1 = 1;
				} else {
					$insId1 = 0;
				}
			} else if($text_send_to == 'preferred_parent') {
				$input['send_to'] = 'preferred';
				$preferred_parent = sendUniqueText($input);
				if($preferred_parent['success'] != ''){
					$insId1 = 1;
				} else {
					$insId1 = 0;
				}
			} else {
				if($text_send_to == '' || $text_send_to == 'Father' || $text_send_to == 'Both') {
					//sending to father
					$input['send_to'] = 'Father';
					$father = sendUniqueText($input);
					if($father['success'] != ''){
						$insId1 = 1;
					} else {
						$insId1 = 0;
					}
				}
				if($text_send_to == '' || $text_send_to == 'Mother' || $text_send_to == 'Both') {
					//sending to mother
					$input['send_to'] = 'Mother';
					$mother = sendUniqueText($input);
					if($mother['success'] != '') {
						$insId2 = 1;
					} else {
						$insId2 = 0;
					}
				}
			}
		}
 		if($insId1 == 1 || $insId2 == 1)
 			echo 1;
 		else 
 			echo 0;
  	}

	public function ClassWiseSummary(){
		$data['class_section'] = $this->attendanceModel->getAllClassandSection();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'reports/attendance/report/ClassWiseSummary';
		}else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'reports/attendance/report/ClassWiseSummary';
		}else{
			$data['main_content'] = 'reports/attendance/report/ClassWiseSummary';   	
		}
		$this->load->view('inc/template', $data);
	}

	public function attenance_not_taken(){
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'reports/attendance/report/attendance_not_taken/tablet_index';
		} else if ($this->mobile_detect->isMobile()) {
			$data['main_content'] = 'reports/attendance/report/attendance_not_taken/mobile_index';
		} else {
			$data['main_content'] = 'reports/attendance/report/attendance_not_taken/desktop_index';
		}
		$this->load->view('inc/template', $data);
	}

	public function get_attendance_not_taken(){
		$result = $this->attendanceModel->get_attendance_not_taken($_POST);
		echo json_encode($result);
	}
	
    public function get_attendence_data_by_class_section(){
		$result = $this->attendanceModel->get_attendence_data_by_class_section($_POST);
		echo json_encode($result);
	}
  	public function dayAttendance() {

		$data['getclassinfo'] = $this->student->getclass();
		$data['class_section'] = $this->attendanceModel->getAllClassandSection();
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'reports/attendance/report/dayAttendance/index_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'reports/attendance/report/dayAttendance/index_mobile';
        }else{
          $data['main_content'] = 'reports/attendance/report/dayAttendance/index';   	
        }
		$this->load->view('inc/template', $data);
  	}

	public function class_level_day_attendance(){
		$data['getclassinfo'] = $this->student->getclass();
		$data['class_section'] = $this->attendanceModel->getAllClassandSection();
		
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'reports/attendance/report/class_level_day_attendance/index_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'reports/attendance/report/class_level_day_attendance/index_mobile';
        }else{
          $data['main_content'] = 'reports/attendance/report/class_level_day_attendance/index';   	
        }
		$this->load->view('inc/template', $data);
	}

  	public function requestDayAttendance($input_data = '') {

  		$competitionAttendance = $this->settings->getSetting('competition_attendance');

		$input = $this->input->post();
  		if(empty($input)){
  			list($input['report_date'],$input['classid'],$input['classsection']) = explode('_', $input_data);
  		}
		// echo '<pre>'; print_r($input); die();
  		$input['classid'] = $this->student->getclassection_NEW($input['classsection'])[0]->class_id;

  		$data['report_date'] = $preInput['report_date_from'] = $input['report_date'];
  		$preInput['report_date_to'] = $input['report_date'];
  		$data['classid'] = $preInput['classid'] = $input['classid'];
  		$data['classsection'] = $preInput['classsection'] = $input['classsection'];

  		$data['ma_periods'] = $this->attendanceModel->getTimetablePeriodsByClass($input['classid'], $input['classsection']);
  		// echo '<pre>'; print_r($data); die();

  		$data['session_data'] = $data['ma_periods']['display'];
  		$data['competitionAttendance'] = $competitionAttendance;

  		$details = $this->Student_attendance_model->getAttendanceDetails($preInput);
  		$data['data'] = $this->_prepareAttendanceData($details);
		//$data['classsectionid'] = $input['classsection'];
        if ($this->mobile_detect->isTablet()) {
          $data['main_content'] = 'reports/attendance/report/dayAttendance/show_tablet';
        }else if($this->mobile_detect->isMobile()){
          $data['main_content'] = 'reports/attendance/report/dayAttendance/show_mobile';
        }else{
          $data['main_content'] = 'reports/attendance/report/dayAttendance/show';    	
        }

  		
  		if( (empty($data['data']['result'])) && $competitionAttendance) {
  			$details = $this->Student_attendance_model->getCompetationAttendanceDetails($preInput);
  			if(!empty($details)) {
  				$data['data']['result'] = $details;
				if ($this->mobile_detect->isTablet()) {
					$data['main_content'] = 'reports/attendance/report/dayAttendance/showOnlyCompetation_tablet';
				}else if($this->mobile_detect->isMobile()){
					$data['main_content'] = 'reports/attendance/report/dayAttendance/showOnlyCompetation_mobile';
				}else{
					$data['main_content'] = 'reports/attendance/report/dayAttendance/showOnlyCompetation';     	
				}	
  			}

  		}
		$this->load->view('inc/template', $data);
  	}

	public function get_class_level_day_attendance(){
	  $result=$this->Student_attendance_model->get_class_level_day_attendance();
	  echo json_encode($result);
    }

  	public function dayAttendanceDetail() {
		$input = $this->input->post();
  		$student_id =$input['id'];
		$day =$input['report_date'];
		$class_id = $input['classid'];
		$clas_section_id = $input['classsection'];
  		$details = $this->Student_attendance_model->getAttendanceDetailsByStudent($student_id, $day);
  		$data_details = $this->_prepareAttendanceData($details);
  		$data['student'] = $data_details['result'][$student_id]['student'];
  		$data['day_data'] = $data_details['result'][$student_id][$day];
  		$data['classid'] = $class_id;
  		$data['classsection'] = $clas_section_id;
  		$data['report_date'] = $day;
		$data['student_id'] = $student_id;
		$data['classsectionid'] = $clas_section_id;
		echo json_encode($data);
		
  	}


}