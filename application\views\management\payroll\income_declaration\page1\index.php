<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <?php if ($call_from == 'staff'): ?>
        <li><a href="<?php echo site_url('staff/Payroll_controller/'); ?>">Payroll dashboard</a></li>
        <li>Investment Declaration</li>
    <?php elseif ($call_from == 'admin'): ?>
        <li><a href="<?php echo site_url('management/payroll/'); ?>">Payroll dashboard</a></li>
        <li><a href="<?php echo site_url('management/payroll/manage_income'); ?>">Manage Investment Declaration</a></li>
        <li>Staff Investment Declaration</li>
    <?php endif; ?>
</ul>

<?php
$formatted_date = "N/A";
if(!empty($staff->doj)){
    $staff_doj = $staff->doj;
    $date = DateTime::createFromFormat('Y-m-d', $staff_doj);
    $formatted_date = $date->format('d-m-Y');
}

function formatIndianCurrency($number) {
    if (floatval($number) == 0) {
        return '₹0.00';
    }
    $decimal = "";
    if (strpos($number, ".") !== false) {
        list($number, $decimal) = explode(".", $number);
        $decimal = "." . substr($decimal, 0, 2);
    }
    
    $lastThree = substr($number, -3);
    $rest = substr($number, 0, -3);
    if (strlen($rest) > 0) {
        $rest = preg_replace("/\B(?=(\d{2})+(?!\d))/", ",", $rest);
    }
    
    return '₹' . $rest . "," . $lastThree . $decimal;
}

$isMobile = $this->mobile_detect->isMobile();
?>

<!-- <div id="pdfModal">
    <div id="pdfModalContent">
        <span id="pdfModalClose" onclick="closeModal()">&times;</span>
        <iframe id="pdfViewer" style="width:100%; height: 80vh;" frameborder="0"></iframe>
    </div>
</div> -->
<div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="margin-top: 5%;">
        <div class="modal-content" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.5)">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProofModalLabel"><b><span id="columnName"></span> <span id="fileName"></span> Proof</b></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" id="pdfModalClose" onclick="closeModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <iframe id="pdfViewer" src="" style="width: 100%; height: 500px; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelDeclarationProof" onclick="closeModal()">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff" style="margin-top: 3px;">
                        <?php if($call_from == 'staff' && !$isMobile){?>
                            <a class="back_anchor" href="<?php echo site_url('staff/Payroll_controller/'); ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a>
                        <?php } else if($call_from == 'staff' && $isMobile){?>
                            <!--  -->
                        <?php } else {?>
                            <a class="back_anchor" href="<?php echo site_url('management/payroll/manage_income'); ?>">
                                <span class="fa fa-arrow-left"></span>
                            </a>
                        <?php }?>
                        <?php if($call_from == 'staff' && !$isMobile){?>
                            <span id="header_text">Investment declaration for FY <?= $selected_year_string ?></span>
                        <?php } else if($call_from == 'staff' && $isMobile){?>
                            <span id="header_text" style="font-size: 18px !important">Investment declaration for FY <?= $selected_year_string ?></span>
                        <?php } else {?>
                            <span id="header_text">Investment declaration for FY <?= $selected_year_string ?></span>
                        <?php }?>
                    </h3>
                </div>
                <div class="col-md-3 pl-0">
                    <div class="col-md-6 <?php echo ($isMobile ? '' : 'pull-right'); ?>">
                        <select class="form-control custom-select <?php echo ($isMobile ? 'pull-right' : ''); ?>"  onchange="income_schedule_year_change();" name="schedule_year" id="schedule_year" <?php echo ($isMobile ? 'style="width: 50% !important"' : ''); ?>>
                            <?php 
                                foreach ($financial_year as $year) {
                                    $selected_str = ($year->id == $selected_year_id) ? 'selected' : '';
                                    echo "<option value='$year->id' $selected_str>$year->f_year</option>";
                                }
                            ?>
                        </select>
                </div>
            </div>

        </div>
        <?php
        ?>

        <?php if (($check_sal_record != -1 && !isset($tax_calc_error)) || ($reopenForProofSubmission == 'Reopen' && $check_sal_record != -1)) { ?>
            <div class="panel-body" id="income_tax_open" style="display:none;">
                <div class="col-md-12">
                <form id="formId" method="post" enctype="multipart/form-data" data-parsley-validate="" action="<?php echo base_url('staff/Payroll_controller/save_incometax_decalaration'); ?>">

                <input type="hidden" value="" name="old_value" id="old_value">
                <input type="hidden" value="" name="new_value" id="new_value">
                <input type="hidden" name="source" id="source" value="Staff Tax Declaration">
                <input type="hidden" name="financial_year_id" id="financial_year_id">
                <input type="hidden" name="staff_id" id="staff_id" value="<?= $staffid?>">
                <input type="hidden" name="call_from" id="call_from" value="<?= $call_from?>">
                <input type="hidden" name="reopenForProofStatus" id="reopenForProofStatus" value="<?= $reopenForProofSubmission?>">
                <input type="hidden" name="previous_ctc_with_employee_pf" id="previous_ctc_with_employee_pf" value="<?= $previous_ctc_with_employee_pf ?>">
                <input type="hidden" name="previous_basic_salary_with_da" id="previous_basic_salary_with_da" value="<?= $previous_basic_salary_with_da ?>">
                <input type="hidden" name="previous_hra" id="previous_hra" value="<?= $previous_hra ?>">
                <input type="hidden" name="previous_professional_tax" id="previous_professional_tax" value="<?= $previous_professional_tax ?>">
                <input type="hidden" name="previous_outside_ctc_allowance" id="previous_outside_ctc_allowance" value="<?= $previous_outside_ctc_allowance ?>">
                <input type="hidden" name="previous_vpf" id="previous_vpf" value="<?= $previous_vpf ?>">
                <input type="hidden" name="previous_employee_pf_contribution" id="previous_employee_pf_contribution" value="<?= $previous_employee_pf_contribution ?>">
                <input type="hidden" name="staff_months_in_year" id="staff_months_in_year" value="<?= $staff_months_in_year ?>">
                    <div class="col-md-12">
                        <div class="row" style="margin-left:10px">
                            <p class="sub_header_note">
                                <strong>Note:</strong> Your <i>approximate</i> TDS in Old Regime is <span style="font-weight:700;font-size:16px;color:blue"><?php echo number_format($or_approx_tds); ?> (assuming no investments) and <?php echo number_format($or_approx_tds_max); ?> (assuming max investments)</span>; in New Regime is <span style="font-weight:700;font-size:16px;color:blue"><?php echo number_format($nr_approx_tds); ?></span>.
                                In case you are choosing New Regime, you can skip adding the declarations for HRA, 80C, 80D investments.
                            </p>
                        </div>

                        <div class="row" style="margin-left:4px">
                            <div class="col-md-3">
                                <label class="form-group">Employee Code</label>
                                <input type="text" readonly id="staff_code" name="staff_code" value="<?= $staff->employee_code?>" class="form-control" placeholder="Enter Employee ID Number" name="">
                            </div>

                            <div class="col-md-3">
                                <label class="form-group">Department </label>
                                <input type="text" readonly id="staff_department" name="staff_department" value="<?= $staff->department?>" class="form-control" placeholder="Department Not Assigned" name="">
                            </div>

                            <div class="col-md-3">
                                <label class="form-group">Designation </label>
                                <input type="text" readonly id="staff_designation" name="staff_designation" value="<?= $staff->designation_name?>" class="form-control" placeholder="Designation Not Assigned">
                                <input type="hidden" id="staff_designation_id" name="staff_designation_id" value="<?= $staff->designation_id ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-group">Joining Date </label>
                                <input type="text" readonly id="staff_joining_date" name="staff_joining_date" value="<?= $formatted_date?>" class="form-control" placeholder="Staff Joining Date">
                                <input type="hidden" id="staff_joining_date" name="staff_joining_date" value="<?= $staff->doj ?>">
                            </div>
                        </div>
                        <?php if($this->settings->getSetting('payroll_tax_declaration_enable_perquisite_income_calculation') == 1){?>
                            <div class="row col-md-12 mt-5" style="margin-left:4px">
                                <div class="form-group">
                                    <label>Are you availing company accommodation?</label>
                                </div>
                                <div class="col-md-4" style="font-size: 10px;">
                                    <input type="radio" id="availing_company_accommodation" name="availing_company_accommodation" value="1" <?php echo ($availing_company_accommodation == 1) ? 'checked' : ''; ?>>
                                    <label for="" style="font-size: 12px;">Yes</label>
                                    <input type="radio" id="availing_company_accommodation" name="availing_company_accommodation" value="0" <?php echo ($availing_company_accommodation == 1) ? '' : 'checked'; ?> style="margin-left: 6px;">
                                    <label for="" style="font-size: 12px;">No</label>
                                </div>
                            </div>
                        <?php }?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_rent'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_80c_investments'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_80d_investments'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_other_investments_exemptions'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_other_employer_income'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/_under_section_twenty_four'); ?>
                        <?php $this->load->view('management/payroll/income_declaration/page1/leave_travel_allowance'); ?>
                    </div>
                </form>
            </div><br>
                <center>
                    <a class="btn btn-danger col-2" href="<?php echo site_url('staff/Payroll_controller/'); ?>" style="font-size: 15px;margin-top: 30px;">Cancel</a>
                    <button id="submitBtn" onclick="add_income_decalaration()" name="Submit" type="submit" class="btn btn-primary col-2" style="font-size: 15px;margin-top: 30px;">Next</button>
                </center>
            </div>
        <?php } else { ?>
            <div class="panel-body">
                <?php if($check_sal_record == -1) : ?>
                    <div class="no-data-display">Salary structure not created. Kindly contact finance team.</div>
                <?php endif ?>
                <?php if(isset($tax_calc_error) && $tax_calc_error == -4) : ?>
                    <div class="no-data-display">Your joining date is not entered in the ERP. Please contact your Administrator.</div>
                <?php endif ?>
                <?php if(isset($tax_calc_error) && $tax_calc_error == -5) : ?>
                    <div class="no-data-display">Financial year is not configured. Please contact your administrator.</div>
                <?php endif ?>
            </div>
        <?php } ?>
</div>

<?php if($check_sal_record != -1 && !isset($tax_calc_error) && isset($staff_details)){?>
    <?php $this->load->view('management/payroll/income_declaration/page1/_table') ?>
<?php }?>


</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
function validateFile(inputElement) {
    const file = inputElement.files[0];

    // If no file is selected, hide the file name and remove button
    if (!file) {
        $(`#${inputElement.id}_file_name`).hide();
        $(`#${inputElement.id}_remove`).hide();
        return; // Exit if no file is selected
    }

    const allowedType = 'application/pdf'; // Only PDF files allowed
    const maxSize = 5 * 1024 * 1024; // 5MB size limit

    // Check file type (only PDF allowed)
    if (file.type !== allowedType) {
        Swal.fire('Invalid File', 'Only PDF files are allowed.', 'error');
        inputElement.value = ''; // Reset file input
        const baseId = inputElement.id.replace('_proof', ''); // Remove '_proof' from the id
        $(`#${baseId}_file_name`).hide(); // Hide file name
        $(`#${baseId}_remove`).hide(); // Hide remove button
        return;
    }

    // Check file size (should be under 5MB)
    if (file.size > maxSize) {
        Swal.fire('File Size Error', 'File size should not exceed 5MB.', 'error');
        inputElement.value = ''; // Reset file input
        const baseId = inputElement.id.replace('_proof', ''); // Remove '_proof' from the id
        $(`#${baseId}_file_name`).hide(); // Hide file name
        $(`#${baseId}_remove`).hide(); // Hide remove button
        return;
    }

    // If the file is valid, display the file name and show the "Remove" button
    const fileName = file.name;
    const baseId = inputElement.id.replace('_proof', '');
    const fileURL = URL.createObjectURL(file);
    $(`#${baseId}_file_name`).show(); // Display the selected file name
    $(`#${baseId}_file_link`).text(fileName).attr('href', fileURL).show();
    $(`#${baseId}_remove`).show(); // Show the "Remove" button
    $(`#${baseId}_proof_input`).hide(); // Hide the "Attach File" icon

    Swal.fire('File Selected', 'The file is valid.', 'success');
}

// Remove the selected file
function removeFile(field) {
    // Reset the file input and hide the file name and remove button
    $(`#${field}_proof`).val(''); // Reset file input
    $(`#${field}_file_name`).hide(); // Hide the file name
    $(`#${field}_remove`).hide(); // Hide the "Remove" button
    $(`#${field}_proof_input`).show(); // Show the "Attach File" icon
}

function openFileInNewTab(field) {
    const fileInput = $(`#${field}_proof`)[0];
    const file = fileInput.files[0];

    if (file) {
        const fileURL = URL.createObjectURL(file); // Create a URL for the file
        window.open(fileURL, '_blank'); // Open the file in a new tab
    } else {
        Swal.fire('No File Selected', 'Please select a file to view.', 'warning');
    }
}

function validateForm() {
    let allValid = true;

    $('input[type="file"]').each(function() {
        const inputId = $(this).attr('id');
        const inputValue = $(`#${inputId}`).val();
        const relatedInput = $(`#${inputId}_proof_input`);

        if (relatedInput.is(':visible') && !inputValue) {
            Swal.fire('Error', 'All proofs are mandatory.', 'error');
            allValid = false;
            return false;
        }
    });

    if (allValid) {
        return true;
    }
    return false;
}

function validateNumber(input) {
    input.value = input.value.replace(/[^\d.]/g, '');

    if (input.value.startsWith('.')) {
        input.value = '0' + input.value; 
    }
    input.value = input.value.replace(/(\..*)\./g, '$1');
}

function handleInput(inputElement) {
    limitInput(inputElement, 1000000);
    validateNumber(inputElement);

    const inputId = inputElement.id;
    const inputValue = parseFloat(inputElement.value) || 0;

    const attachmentButton = $(`#${inputId}_proof_input`);
    const fileInput = $(`#${inputId}_proof`);
    const fileNameSpan = $(`#${inputId}_file_name`);
    const removeButton = $(`#${inputId}_remove`);

    // If the input value is greater than 0, show the file attachment button and make it required
    if (inputValue > 0) {
        attachmentButton.show(); // Show the "Attachment" button
        fileInput.prop('required', true); // Make file input required
    } else {
        attachmentButton.hide(); // Hide the "Attachment" button
        fileInput.prop('required', false); // Make file input not required
        fileNameSpan.hide(); // Hide the file name
        removeButton.hide(); // Hide the remove button
    }
}

function income_schedule_year_change() {
    var selectedYear = $('#schedule_year').val();
    localStorage.setItem('selected_financial_year', selectedYear);
    window.location.href = '<?php echo site_url('staff/Payroll_controller/income_declaration/'); ?>' +selectedYear;
}

$(document).ready(function() {
    var storedYear = localStorage.getItem('selected_financial_year');
    if (storedYear) {
        $('#schedule_year').val(storedYear);
    }
    <?php if($reopenForProofSubmission == 'Reopen' && $check_sal_record != -1 && isset($anyRejectedProof) && $anyRejectedProof == 1){?>
        Swal.fire({
            icon: "warning",
            title: "Some of your document proofs have been Rejected!",
            html: "Please scroll down and click '<span style='font-weight:700;font-size:16px;color:blue'>Next</span>' to verify",
            confirmButtonText: "OK"
        });
        // .then((result) => {
        //     if (result.isConfirmed) {
        //         window.location.href = "<?php //echo site_url('staff/payroll_controller/add_incometax_declaration_proofs'); ?>/" + 
        //                                 "<?php //echo $selected_year_id; ?>" + "/" + 
        //                                 "<?php //echo $staffid; ?>";
        //     }
        // });
    <?php }?>
    income_permission_check();
    if (localStorage.getItem('selected_financial_year') !== null) {
        // $('#schedule_year').val(localStorage.getItem('selected_financial_year'));
        $('#schedule_year option').each(function() {
            // $('#schedule_year option').removeAttr('selected');
            if ($(this).val() === localStorage.getItem('selected_financial_year')) {
                $(this).attr('selected', 'selected');
            }
        });
    }

    <?php if ($check_sal_record != -1 && !isset($tax_calc_error)) : ?>
        var hasRentDetails = <?php echo !empty($staff_details_house) ? 'true' : 'false'; ?>;
        var c80_open = <?php echo !empty($c80_open) ? 'true' : 'false'; ?>;
        var c80_others_open = <?php echo !empty($c80_others_open) ? 'true' : 'false'; ?>;
        var d80_open = <?php echo !empty($d80_open) ? 'true' : 'false'; ?>;
        var other_investment_exemptions = <?php echo !empty($other_investment_exemptions) ? 'true' : 'false'; ?>;
        var _under_section_twenty_four = <?php echo !empty($_under_section_twenty_four) ? 'true' : 'false'; ?>;
        var leave_travel_allowance = <?php echo !empty($leave_travel_allowance) ? 'true' : 'false'; ?>;
        var others_open = <?php echo !empty($others_open) ? 'true' : 'false'; ?>;

        document.getElementById("staying_rented_house_checkbox").checked = hasRentDetails;
        document.getElementById("investments_80c_chk").checked = c80_open;
        document.getElementById("other_80c_investments_chk").checked = c80_others_open;
        document.getElementById("80d_Exemptions").checked = d80_open;
        document.getElementById("other_investments_exemptions_chk").checked = other_investment_exemptions;
        document.getElementById("other_employer_income_chk").checked = others_open;
        document.getElementById("_under_section_twenty_four_chk").checked = _under_section_twenty_four;
        document.getElementById("_leave_travel_allowance_chk").checked = leave_travel_allowance;

        toggleRentDetails();
        investments_80c();
        other_80c_investments_func();
        exemptions_80d();
        other_investments_exemptions();
        other_employer_income_func();
        under_section_twenty_four();
        leave_travel_allowance_checkbox();
    <?php endif ?>

    var self_age = $('#self_age option:selected').val();
    // console.log(self_age);
    if (self_age == 'below_60'){
        $('#80d_self_med').show();
        $('#80d_self_med_above_60').hide();
        $('#80d_preventive_health_self').show();
        $('#80d_medical_bills_self').hide();
        $('#parents_age option[value="below_60"]').prop('disabled', false);
    } else {
        $('#80d_self_med').hide();
        $('#80d_self_med_above_60').show();
        $('#80d_preventive_health_self').show();
        $('#80d_medical_bills_self').show();
        $('#parents_age option[value="below_60"]').prop('disabled', true);
    }

    var parent_age = $('#parents_age option:selected').val();
    // console.log(parent_age);
    if (parent_age == 'below_60'){
        $('#80d_parent_med').show();
        $('#80d_parent_med_above_60').hide();
        $('#80d_preventive_health_parent').show();
        $('#80d_medical_bills_parent').hide();
    } else {
        $('#80d_parent_med').hide();
        $('#80d_parent_med_above_60').show();
        $('#80d_preventive_health_parent').show();
        $('#80d_medical_bills_parent').show();
    }

    var initialData = $('#formId').serializeArray();
    var initial_val = {};
    var changedData = {};
    // Attach change event listener to form fields
    $('#formId :input').change(function() {
        document.querySelector("#old_value").value="";
        document.querySelector("#new_value").value="";
        // Get the current state of the form fields
        var currentData = $('#formId').serializeArray();
        // Identify the changed data
        
        $.each(currentData, function(index, item) {
            
            var fieldName = item.name;
            var initialValue = initialData[index].value;
            var currentValue = item.value;
            if (initialValue !== currentValue) {
                initial_val[fieldName] = initialValue;
                changedData[fieldName] = currentValue;
            }
        });
        // $('#old_value').val(JSON.stringify(initial_val));
        // $('#new_value').val(JSON.stringify(changedData));

        document.querySelector("#old_value").value=JSON.stringify(initial_val);
        document.querySelector("#new_value").value=JSON.stringify(changedData);
    });

});

function limitInput(element, limit) {
    var value = parseFloat(element.value);
    
    if (value > limit) {
        element.value = limit;
        alert("Input value cannot exceed " + limit);
    }
}

function income_permission_check() {
    var staffid = '<?php echo $staffid ?>';
    var schedule_year = $('#schedule_year').val();
    $('#financial_year_id').val(schedule_year);
    $(".loading-icon").show();
    var call_from = '<?php echo $call_from?>'
    $.ajax({
        url: '<?php echo site_url('staff/Payroll_controller/income_permission_check'); ?>',
        type: 'post',
        data: {'staffid': staffid, 'schedule_year':schedule_year, 'call_from': call_from},
        success: function(data) {
            var parsed_data = $.parseJSON(data);
            
            if(parsed_data == null){
                $('#income_tax_open').hide();
                $('#income_tax_open_footer').hide();
                $('#income_tax_submited').hide();
                $('#income_tax_close').show();
            } else if (parsed_data && (parsed_data.staff_submit_status == 'Submitted' || parsed_data.staff_submit_status == 'Approved' || parsed_data.staff_submit_status == 'Close') && (parsed_data.proof_submission_status != 'Reopened')) {
                $('#income_tax_open').hide();
                $('#income_tax_open_footer').hide();
                $('#income_tax_submited').show();
                $('#income_tax_close').hide();
            } else if (parsed_data && (parsed_data.staff_submit_status == 'Open' || parsed_data.staff_submit_status == 'Reopen') || (parsed_data.proof_submission_status == 'Reopened')) {
                $('#income_tax_open').show();
                $('#income_tax_open_footer').show();
                $('#income_tax_close').hide();
                $('#income_tax_submited').hide();
            }  
            $(".loading-icon").hide();

        },
        error: function(err) {
            console.log(err);
        }
    });
}

function toggleRentDetails() {
    var checkBox = document.getElementById("staying_rented_house_checkbox");
    var rentDetails = document.getElementById("staying_rented_house");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function investments_80c() {
    var checkBox = document.getElementById("investments_80c_chk");
    var rentDetails = document.getElementById("investments_80c_div");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function other_employer_income_func() {
    var checkBox = document.getElementById("other_employer_income_chk");
    var rentDetails = document.getElementById("other_employer_income_div");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function other_80c_investments_func() {
    var checkBox = document.getElementById("other_80c_investments_chk");
    var rentDetails = document.getElementById("other_80c_investments_div");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function other_investments_exemptions() {
    var checkBox = document.getElementById("other_investments_exemptions_chk");
    var divElement = document.getElementById("other_investments_exemptions_div");
    if (checkBox.checked == true){
        divElement.style.display = "block";
    } else {
        divElement.style.display = "none";
    }
}

function exemptions_80d() {
    var checkBox = document.getElementById("80d_Exemptions");
    var rentDetails = document.getElementById("80dExemptions");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function under_section_twenty_four(){
    var checkBox = document.getElementById("_under_section_twenty_four_chk");
    var rentDetails = document.getElementById("under_section_twenty_four_div");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}

function leave_travel_allowance_checkbox(){
    var checkBox = document.getElementById("_leave_travel_allowance_chk");
    var lta = document.getElementById("leave_travel_allowance_div");
    if (checkBox.checked == true){
        lta.style.display = "block";
    } else {
        lta.style.display = "none";
    }
}

function let_out_property() {
    var checkBox = document.getElementById("let_out_property_with_or_without");
    var rentDetails = document.getElementById("let_out_property_withorwithout");
    if (checkBox.checked == true){
        rentDetails.style.display = "block";
    } else {
        rentDetails.style.display = "none";
    }
}


$(document).ready(function() {
    var optionElement = document.querySelector(`option[value="${$('#schedule_year').val()}"]`);
    var innerHTML = optionElement.innerHTML;
    var year = innerHTML.match(/\d{4}/)[0];
    var [startYear, endYear] = year.split('-').map(year => parseInt(year));

    var staff_joining_date = "<?php echo isset($staff) ? $staff->doj : ''; ?>";
    var dateParts = staff_joining_date.split('-');
    var joiningYear = parseInt(dateParts[0]);
    var joiningMonth = parseInt(dateParts[1]) - 1;
    var startDate;

    if(joiningYear < startYear){
        startDate = new Date(startYear, 3);
    }
    else{
        if (joiningMonth <= 3) {
            startDate = new Date(startYear, 3);
        } else {
            startDate = new Date(startYear, joiningMonth);
        }
    }

    $(`#addbox_start_date`).datepicker({
        format: 'mm-yyyy',
        startView: 'months',
        minViewMode: 'months',
        autoclose: true,
        placeholder: 'From Month',
        startDate: startDate,
        endDate: new Date(startYear + 1, 2)
    }).on('changeDate', function(e) {
        var selectedDate = e.date;
        var minEndDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
        $('#addbox_end_date').datepicker("setStartDate", minEndDate);
        calculate_total_amount_of_month('addmodal');
    });
    $(`#addbox_end_date`).datepicker({
        format: 'mm-yyyy',
        startView: 'months',
        minViewMode: 'months',
        autoclose: true,
        placeholder: 'To Month',
        startDate: new Date(startYear, 3),
        endDate: new Date(startYear + 1, 2)
    }).on('changeDate', function(e) {
        var selectedDate = e.date;
        calculate_total_amount_of_month('addmodal');
    });
});

function monthsDiff(date1, date2) {
    var date1Parts = date1.split('-');
    var date2Parts = date2.split('-');

    var date1Obj = new Date(parseInt(date1Parts[1]), parseInt(date1Parts[0]) - 1, 1);
    var date2Obj = new Date(parseInt(date2Parts[1]), parseInt(date2Parts[0]) - 1, 1);
    var months;
    months = (date2Obj.getFullYear() - date1Obj.getFullYear()) * 12;
    months -= date1Obj.getMonth();
    months += date2Obj.getMonth();
    return months <= 0 ? 0 : months;
}

function calculate_total_amount_of_month(modalType){
    if(modalType == 'addmodal'){
        var from_date = $('#addbox_start_date').val();
        var to_date = $('#addbox_end_date').val();
        var monthly_amount = parseFloat($('#addbox_monthly_amount').val());

        if (from_date && to_date && !isNaN(monthly_amount)) {
            var total_months = monthsDiff(from_date, to_date);
            var total_rent = monthly_amount * (total_months + 1); 
            
            $('#addbox_total_rent').text('Total Rent: ' + total_rent.toFixed(2));
            $('#addbox_total_rent_input').val(total_rent.toFixed(2));
        } else {
            $('#addbox_total_rent').text('Total Rent: 0');
            $('#addbox_total_rent_input').val(0);
        }
    } else {
        var from_date = $('#editStartDate').val();
        var to_date = $('#editEndDate').val();
        var monthly_amount = parseFloat($('#editMonthlyAmount').val());

        if (from_date && to_date && !isNaN(monthly_amount)) {
            var total_months = monthsDiff(from_date, to_date);
            var total_rent = monthly_amount * (total_months + 1); 
            
            $('#editTotalRent').text('Total Rent: ' + total_rent.toFixed(2));
            $('#total_rent_input_edit').val(total_rent.toFixed(2));
        } else {
            $('#editTotalRent').text('Total Rent: 0');
            $('#total_rent_input_edit').val(0);
        }
    }
}

function getAllTableData() {
    var tableData = [];
    var table = document.getElementById('dataTable');
    var rows = table.getElementsByTagName('tr');
    for (var i = 1; i < rows.length; i++) { // Start from index 1 to skip the header row
        var cells = rows[i].getElementsByTagName('td');
        var rowData = {
            'rowId': cells[0].textContent.trim(),
            'rentedFrom': cells[1].textContent.trim(),
            'rentedTo': cells[2].textContent.trim(),
            'amountPerMonth': cells[3].textContent.trim(),
            'totalRent': cells[4].textContent.trim(),
            'address': cells[5].textContent.trim(),
            'landlordName': cells[6].textContent.trim(),
            'landlordPancard': cells[7].textContent.trim()
        };
        tableData.push(rowData);
    }
    return tableData;
}

function deleterowRentHouse(button) {
    var row = button.closest('tr');
    var recordInput = row.querySelector('input[name="record_id[]"]');
    
    // Check if recordInput exists and has a value
    if (recordInput && recordInput.value.trim() !== '') {
        var recordId = recordInput.value;
        $.ajax({
            url: '<?php echo site_url('staff/Payroll_controller/delete_renthouse_row'); ?>',
            method: 'POST',
            data: { recordId: recordId },
            success: function(response) {
                row.remove();
                console.log('Record deleted successfully.');
            },
            error: function(xhr, status, error) {
                console.error('Error deleting record:', error);
            }
        });
    } else {
        // If recordId is not available, simply remove the row
        row.remove();
        console.log('Row removed.');
    }
}



function add_income_decalaration() {
    localStorage.setItem('selected_financial_year', $('#schedule_year').val());
    var isValid = $('#formId').parsley().validate();
    if (isValid) {
    $("#submitBtn").attr('disabled','disabled').html('Please wait...');
        Swal.fire({
            icon: "success",
            title: "Income Declaration added",
            showConfirmButton: false,
            timer: 1500
        }).then(function() {
            $("#formId").submit();
        });
    } else {
        $("#submitBtn").removeAttr('disabled').html('Next');
    }
}
function self_age_click() {
    var self_age = $('#self_age option:selected').val();

    // console.log(self_age);
    if (self_age == 'below_60'){
        $('#80d_self_med').show();
        $('#80d_self_med_above_60').hide();
        $('#80d_preventive_health_self').show();
        $('#80d_medical_bills_self').hide();
        $('#parents_age option[value="below_60"]').prop('disabled', false);
        $('#parents_age').selectpicker('val', 'below_60');
        parent_age_click();
    } else {
        $('#80d_self_med').hide();
        $('#80d_self_med_above_60').show();
        $('#80d_preventive_health_self').show();
        $('#80d_medical_bills_self').show();
        $('#parents_age option[value="below_60"]').prop('disabled', true);
        $('#parents_age').selectpicker('val', 'above_60');
        parent_age_click();
    }

}
    
function parent_age_click() {
    var parent_age = $('#parents_age option:selected').val();
    // console.log(parent_age);

    if (parent_age == 'below_60'){
        $('#80d_parent_med').show();
        $('#80d_parent_med_above_60').hide();
        $('#80d_preventive_health_parent').show();
        $('#80d_medical_bills_parent').hide();
    } else {
        $('#80d_parent_med').hide();
        $('#80d_parent_med_above_60').show();
        $('#80d_preventive_health_parent').show();
        $('#80d_medical_bills_parent').show();
    }

}

let staffTaxRegime = '<?php echo isset($staff_details) ? $staff_details->tax_regime_name : '' ?>'

function get_mytds_slip(){
    income_declaration_cal();
    if(staffTaxRegime == 'New Regime'){
        $('#taxCalculationNewRegime').show();
    }else if(staffTaxRegime == 'Old Regime'){
        $('#taxCalculationOldRegime').show();
    }
    $('#staffTaxDetails').show();
}

function close_staff_regime_cal(){
    $('#staffTaxDetails').hide();
}

function income_declaration_cal() {
    var staffid = '<?php echo $staffid ?>';
    var selected_financial_year_id = $('#schedule_year').val();
    $.ajax({
        url: '<?php echo site_url('staff/Payroll_controller/income_decalaration_cal'); ?>',
        type: 'post',
        data: {'staffid': staffid, 'financial_year_id': selected_financial_year_id},
        success: function(data) {
            var echr_data = JSON.parse(data);

            //Fill the Old Regime Table
            let final_deductions = Number(echr_data.total_80_deductions) + Number(echr_data.or_lta);
            $("#or_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

            $("#or_sd").val(echr_data.or_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_hra_exemption").val(echr_data.hra_exemption.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_sd_hra").val(`(${(parseFloat(echr_data.hra_exemption) + parseFloat(echr_data.or_sd)).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);

            $("#or_income_from_salary").val(echr_data.income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

            $("#or_pt").val(echr_data.pt_paid.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_income_from_salary_pt").val(echr_data.taxable_income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

            $("#or_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_gross_salary_income").val(echr_data.gross_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80c").val(echr_data.c_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80ccd").val('₹' + echr_data.ccd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80d").val(echr_data.d_80 != null ? echr_data.d_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}) : '₹0.00');
            $("#or_80dd").val(echr_data.dd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80ddb").val(echr_data.ddb_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80g").val(echr_data.g_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80u").val(echr_data.u_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80e").val('₹' + echr_data.e_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            // $("#total_80_deductions").val(echr_data.total_80_deductions.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#total_80_deductions").val(final_deductions.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_tax_rebate").val('('+echr_data.or_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})+')');
            $("#or_net_income_tax").val(echr_data.or_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_taxable_salary").val(echr_data.or_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_basic_tax").val(echr_data.or_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_surcharge").val(echr_data.or_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_net_income_tax_surcharge").val(echr_data.or_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_cess").val(parseFloat(echr_data.or_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_yearly_tds").val(echr_data.or_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_final_tds").val(echr_data.or_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_80ttab").val(Number(echr_data.ttab_80).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#or_sec24").val(`(${parseFloat(echr_data.sec_24).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);
            $("#or_lta").val(Number(echr_data.or_lta).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

            //Fill the New Regime Table
            $("#nr_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_sd").val(echr_data.nr_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

            $("#nr_income_from_salary_pt").val(echr_data.taxable_income_from_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_gross_salary_income").val(echr_data.gross_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_tax_rebate").val('('+echr_data.nr_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})+')');
            $("#nr_net_income_tax").val(echr_data.nr_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_basic_tax").val(echr_data.nr_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_surcharge").val(echr_data.nr_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_net_income_tax_surcharge").val(echr_data.nr_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_cess").val(parseFloat(echr_data.nr_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_yearly_tds").val(echr_data.nr_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_final_tds").val(echr_data.nr_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_taxable_salary").val(echr_data.nr_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            $("#nr_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
        }
    });
}

function get_monthly_payslip_and_view(total_tds, tax_regime){
    var staff_id = '<?php echo $staffid ?>';
    var financial_year = $('#schedule_year').val();
    $.ajax({
        url: '<?php echo site_url('staff/Payroll_controller/get_staff_wise_payroll_data_for_agreement'); ?>',
        type: 'post',
        data: {'staff_id': staff_id, 'financial_year': financial_year},
        success: function(data) {
            // console.log(data);
            parsed_data = $.parseJSON(data);
            // console.log(parsed_data);
            merged_data = parsed_data.merged_data;
            schedules = parsed_data.schedules;
            staff_details = parsed_data.staff_details;
            let nr_tax_amt = parsed_data.nr_tax_amt ? parsed_data.nr_tax_amt : 0;
            let or_tax_amt = parsed_data.or_tax_amt ? parsed_data.or_tax_amt : 0;
            if (merged_data.length != 0) {
                // console.log(merged_data);
                // console.log(schedules);
                // console.log(staff_details);
                let currentDate = new Date();
                let total_collected = 0;
                let total_projected = 0;
                merged_data.forEach(item => {
                    let matchingSchedule = schedules.find(schedule => schedule.id == item.schedule_id);
                    
                    if (matchingSchedule) {
                        let endDate = new Date(matchingSchedule.end_date);
                        let tdsValue = parseFloat(item.tds);
                        if (endDate <= currentDate) {
                            item.color = 'green';
                        } else {
                            item.color = 'grey';
                        }

                        if (!isNaN(tdsValue)) {
                            if (endDate <= currentDate) {
                                total_collected += tdsValue;
                            } else {
                                total_projected += tdsValue;
                            }
                        }
                    }
                });
                let content = '<div>';
                // content += '<h3>Your Details</h3>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="4">Your Details</th></tr></thead>';
                content += '<tr class="text-start">';
                content += '<th>ID</th>';
                content += '<td>' + staff_details.employee_code + '</td>';
                content += '<th>Name</th>';
                content += '<td>' + staff_details.staff_name + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<th>DOB</th>';
                content += '<td>' + staff_details.dob + '</td>';
                content += '<th>PAN</th>';
                content += '<td>' + staff_details.pan_number + '</td>';
                content += '</tr>';
                content += '</table>';
                content += '<br>';
                // content += '<h4>Monthly TDS Amount</h4>';
                content += '<h5 style="text-align: end;"><span><i style="color: green" class="fa fa-square"></i> Collected</span> <span><i style="color: grey" class="fa fa-square"></i> Projected</span></h5>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="4">Monthly TDS Amount</th></tr></thead>';
                content += '<tr><th>Month</th><th>TDS</th><th>Month</th><th>TDS</th></tr>';
                
                for (let i = 0; i < 6; i++) {
                    content += '<tr>';
                    
                    if (i < merged_data.length) {
                        content += '<td style="color:' + merged_data[i].color + ';">' + merged_data[i].schedule_name + '</td>';
                        content += '<td style="color:' + merged_data[i].color + ';">' + merged_data[i].tds + '</td>';
                    } else {
                        content += '<td></td><td></td>';
                    }
                    
                    if (i + 6 < merged_data.length) {
                        content += '<td style="color:' + merged_data[i + 6].color + ';">' + merged_data[i + 6].schedule_name + '</td>';
                        content += '<td style="color:' + merged_data[i + 6].color + ';">' + merged_data[i + 6].tds + '</td>';
                    } else {
                        content += '<td></td><td></td>';
                    }

                    content += '</tr>';
                }

                content += '</table>';
                content += '<br>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="3">TDS Details</th></tr></thead>';
                content += '<thead>';
                content += '<tr>';
                content += '<th>Total Collected</th>';
                content += '<th>Total Projected</th>';
                content += '<th>Total TDS</th>';
                content += '</tr>';
                content += '</thead>';
                content += '</tbody>';
                content += '<tr>';
                content += '<td style="color:green">' + parseFloat(total_collected).toFixed(2) + '</td>';
                content += '<td>' + parseFloat(total_projected).toFixed(2) + '</td>';
                content += '<td style="color:blue"><b>' + total_tds + '</b></td>';
                content += '</tr>';
                content += '</tbody>';
                content += '</table><br>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="2">Regime Wise TDS</th></tr></thead>';
                content += '<thead>';
                content += '<tr>';
                content += '<th>Regime</th>';
                content += '<th>Total TDS</th>';
                content += '</tr>';
                content += '</thead>';
                content += '</tbody>';
                content += `<tr style="${tax_regime == 'New Regime' ? 'background-color: #dff0d8;' : ''}">`;
                content += `<td>New Regime ${tax_regime == 'New Regime' ? '<span class="regime">(Selected By You)</span>' : '' }</td>`;
                content += '<td>' + nr_tax_amt + '</td>';
                content += '</tr>';
                content += `<tr style="${tax_regime == 'Old Regime' ? 'background-color: #dff0d8;' : ''}">`;
                content += `<td>Old Regime ${tax_regime == 'Old Regime' ? '<span class="regime">(Selected By You)</span>' : '' }</td>`;
                content += '<td>' + or_tax_amt + '</td>';
                content += '</tr>';
                content += '</tbody>';
                content += '</table>';
                content += '</div>';
                content += '<div class="mt-2" style="color: green;"><b>You have agreed to the TDS Calculation.</b></div>';

                Swal.fire({
                    title: 'TDS Statement',
                    html: content,
                    showCancelButton: true,
                    showConfirmButton: false,
                    cancelButtonText: "<i class='fa fa-times'></i> Close",
                    cancelButtonColor: '#dc3545',
                    width: '600px',
                }).then((result) => {
                    if (result.dismiss === Swal.DismissReason.cancel) {
                        // console.log("Closed");
                    }
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "No Data Found",
                    showConfirmButton: false,
                    timer: 1500
                });
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function get_monthly_payslip_and_agreement(total_tds, tax_regime){
    var staff_id = '<?php echo $staffid ?>';
    var financial_year = $('#schedule_year').val();
    $.ajax({
        url: '<?php echo site_url('staff/Payroll_controller/get_staff_wise_payroll_data_for_agreement'); ?>',
        type: 'post',
        data: {'staff_id': staff_id, 'financial_year': financial_year},
        success: function(data) {
            // console.log(data);
            parsed_data = $.parseJSON(data);
            // console.log(parsed_data);
            merged_data = parsed_data.merged_data;
            schedules = parsed_data.schedules;
            staff_details = parsed_data.staff_details;
            let nr_tax_amt = parsed_data.nr_tax_amt ? parsed_data.nr_tax_amt : 0;
            let or_tax_amt = parsed_data.or_tax_amt ? parsed_data.or_tax_amt : 0;
            if (merged_data.length != 0) {
                // console.log(merged_data);
                // console.log(schedules);
                // console.log(staff_details);
                let currentDate = new Date();
                let total_collected = 0;
                let total_projected = 0;
                merged_data.forEach(item => {
                    let matchingSchedule = schedules.find(schedule => schedule.id == item.schedule_id);
                    
                    if (matchingSchedule) {
                        let endDate = new Date(matchingSchedule.end_date);
                        let tdsValue = parseFloat(item.tds);
                        if (endDate <= currentDate) {
                            item.color = 'green';
                        } else {
                            item.color = 'grey';
                        }

                        if (!isNaN(tdsValue)) {
                            if (endDate <= currentDate) {
                                total_collected += tdsValue;
                            } else {
                                total_projected += tdsValue;
                            }
                        }
                    }
                });
                let content = '<div>';
                // content += '<h3>Your Details</h3>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="4">Your Details</th></tr></thead>';
                content += '<tr class="text-start">';
                content += '<th>ID</th>';
                content += '<td>' + staff_details.employee_code + '</td>';
                content += '<th>Name</th>';
                content += '<td>' + staff_details.staff_name + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<th>DOB</th>';
                content += '<td>' + staff_details.dob + '</td>';
                content += '<th>PAN</th>';
                content += '<td>' + staff_details.pan_number + '</td>';
                content += '</tr>';
                content += '</table>';
                content += '<br>';
                // content += '<h4>Monthly TDS Amount</h4>';
                content += '<h5 style="text-align: end;"><span><i style="color: green" class="fa fa-square"></i> Collected</span> <span><i style="color: grey" class="fa fa-square"></i> Projected</span></h5>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="4">Monthly TDS Amount</th></tr></thead>';
                content += '<tr><th>Month</th><th>TDS</th><th>Month</th><th>TDS</th></tr>';
                
                for (let i = 0; i < 6; i++) {
                    content += '<tr>';
                    
                    if (i < merged_data.length) {
                        content += '<td style="color:' + merged_data[i].color + ';">' + merged_data[i].schedule_name + '</td>';
                        content += '<td style="color:' + merged_data[i].color + ';">' + merged_data[i].tds + '</td>';
                    } else {
                        content += '<td></td><td></td>';
                    }
                    
                    if (i + 6 < merged_data.length) {
                        content += '<td style="color:' + merged_data[i + 6].color + ';">' + merged_data[i + 6].schedule_name + '</td>';
                        content += '<td style="color:' + merged_data[i + 6].color + ';">' + merged_data[i + 6].tds + '</td>';
                    } else {
                        content += '<td></td><td></td>';
                    }

                    content += '</tr>';
                }

                content += '</table>';
                content += '<br>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="3">TDS Details</th></tr></thead>';
                content += '<thead>';
                content += '<tr>';
                content += '<th>Total Collected</th>';
                content += '<th>Total Projected</th>';
                content += '<th>Total TDS</th>';
                content += '</tr>';
                content += '</thead>';
                content += '</tbody>';
                content += '<tr>';
                content += '<td style="color:green">' + parseFloat(total_collected).toFixed(2) + '</td>';
                content += '<td>' + parseFloat(total_projected).toFixed(2) + '</td>';
                content += '<td style="color:blue"><b>' + total_tds + '</b></td>';
                content += '</tr>';
                content += '</tbody>';
                content += '</table><br>';
                content += '<table class="table-bordered" style="width:100%; border-collapse: collapse;">';
                content += '<thead><tr><th colspan="2">Regime Wise TDS</th></tr></thead>';
                content += '<thead>';
                content += '<tr>';
                content += '<th>Regime</th>';
                content += '<th>TDS</th>';
                content += '</tr>';
                content += '</thead>';
                content += '</tbody>';
                content += `<tr style="${tax_regime == 'New Regime' ? 'background-color: #dff0d8;' : ''}">`;
                content += `<td>New Regime ${tax_regime == 'New Regime' ? '<span class="regime">(Selected By You)</span>' : '' }</td>`;
                content += '<td>' + nr_tax_amt + '</td>';
                content += '</tr>';
                content += `<tr style="${tax_regime == 'Old Regime' ? 'background-color: #dff0d8;' : ''}">`;
                content += `<td>Old Regime ${tax_regime == 'Old Regime' ? '<span class="regime">(Selected By You)</span>' : '' }</td>`;
                content += '<td>' + or_tax_amt + '</td>';
                content += '</tr>';
                content += '</tbody>';
                content += '</table>';
                content += '</div>';
                content += `<div style="border-left: 4px solid #007BFF; background-color: #F0F8FF; padding: 10px; margin-top: 15px;text-align: left;">
                        <strong>Note:</strong><br>
                        <ol style="margin-left: 20px;">
                            <li>If you agree with the above calculations, click on <span style="color: #28a745;">Agree</span>.</li>
                            <li>If you wish to make changes to your declaration, click on <span style="color: #007BFF;">Re-open</span> to request a re-open of your TDS Declaration Form.</li>
                        </ol>
                    </div>`;

                Swal.fire({
                    title: 'TDS Statement',
                    html: content,
                    showCloseButton: true,
                    // showCancelButton: true,
                    showDenyButton: true,
                    // cancelButtonText: "<i class='fa fa-times'></i> Close",
                    denyButtonText: "<i class='fa fa-refresh'></i> Re-open",
                    confirmButtonText: "<i class='fa fa-check'></i> Agree",
                    denyButtonColor: '#007bff',
                    confirmButtonColor: '#28a745',
                    // cancelButtonColor: '#dc3545',
                    width: '600px',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    allowEnterKey: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        tds_agreed_reopen_by_staff(staff_id, financial_year, 1, total_tds);
                    } else if (result.isDenied) {
                        tds_agreed_reopen_by_staff(staff_id, financial_year, 2, total_tds);
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                        // console.log("Cancelled");
                    }
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "No Data Found",
                    showConfirmButton: false,
                    timer: 1500
                });
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function tds_agreed_reopen_by_staff(staff_id, financial_year, status, total_tds){
    var msg1 = status == 1 ? 'I agree with the TDS calculation as provided.' : 'I would like to have my TDS re-opened for reconsideration.' ;
    var msg2 = status == 1 ? 'You have confirmed your agreement with the TDS calculation.' : 'Your request to re-open the TDS has been successfully submitted.' ;
    var selected_financial_year_id = $('#schedule_year').val();
    Swal.fire({
        title: 'Are you sure?',
        text:  `${msg1}`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'No',
        confirmButtonText: 'Yes!',
        reverseButtons: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Processing...',
                text: 'Please wait while we process your request.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            $.ajax({
                url: '<?php echo site_url('staff/Payroll_controller/tds_agreed_reopen_by_staff'); ?>',
                type: 'POST',
                data: {
                    'staff_id': staff_id,
                    'financial_year': financial_year,
                    'status': status,
                },
                success: function(response) {
                    if (response) {
                        Swal.close();
                        Swal.fire({
                            title: 'Success!',
                            text: `${msg2}`,
                            icon: 'success',
                            confirmButtonText: 'OK',
                        }).then(() => {
                            if (result.isConfirmed) {
                                window.location.href = '<?php echo site_url('staff/Payroll_controller/income_declaration/'); ?>' + selected_financial_year_id;
                            }
                        });
                    } else {
                        showErrorSwal();
                    }
                },
                error: function(err) {
                    console.log(err);
                }
            });
        } else {
            get_monthly_payslip_and_agreement(total_tds);
        }
    });
}

function showErrorSwal() {
    Swal.fire({
        title: 'Something went wrong!',
        text: 'Please try again later.',
        icon: 'error',
        timer: 1500,
        showConfirmButton: false
    }).then(() => {
        get_monthly_payslip_and_agreement(); 
    });
}

function view_additional_allowance_break_down(){
    $('#additional_allowance_info_btn').html('Please wait...').prop('disabled', true);
    var staff_id = '<?php echo $staffid ?>';
    var financial_year = $('#schedule_year').val();
    $.ajax({
        url: "<?php echo site_url('staff/Payroll_controller/view_additional_allowance_break_down')?>",
        type: "POST",
        data: {
            'staff_id': staff_id,
            'financial_year': financial_year,
        },
        success: function (response) {
            $('#staffTaxDetails').hide();
            let parsed_allowance_data = JSON.parse(response);
            if(Object.keys(parsed_allowance_data).length > 0 > 0){
                showAllowanceBreakdown(parsed_allowance_data);
            } else {
                Swal.fire({
                    title: "No Data Found",
                    icon: "warning",
                    timer: 1500,
                    showConfirmButton: false,
                }).then(() => {
                    $('#additional_allowance_info_btn').html('More Info').prop('disabled', false);
                    if(staffTaxRegime == 'New Regime'){
                        $('#taxCalculationNewRegime').show();
                    }else if(staffTaxRegime == 'Old Regime'){
                        $('#taxCalculationOldRegime').show();
                    }
                    $('#staffTaxDetails').show();
                });
            }
        }
    });
}

function showAllowanceBreakdown(data) {
    let tableHtml = `
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="border-bottom: 1px solid #ddd;">
                    <th style="padding: 8px; text-align: left;">Allowance</th>
                    <th style="padding: 8px; text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
    `;

    // Variables to calculate total
    let totalAmount = 0;

    // Loop through data to build rows
    Object.values(data).forEach((item) => {
        tableHtml += `
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;text-align: left;">${item.display_name}</td>
                <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd;">${item.amount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
            </tr>
        `;
        totalAmount += item.amount;
    });

    // Add total row
    tableHtml += `
        </tbody>
        <tfoot>
            <tr>
                <td style="padding: 8px; font-weight: bold;text-align: left;">Total</td>
                <td style="padding: 8px; text-align: right; font-weight: bold;">${totalAmount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
            </tr>
        </tfoot>
    </table>
    `;
    
    Swal.fire({
        title: "Allowance Breakdown",
        html: tableHtml,
        confirmButtonText: "Close",
    }).then(() => {
        $('#additional_allowance_info_btn').html('More Info').prop('disabled', false);
        if(staffTaxRegime == 'New Regime'){
            $('#taxCalculationNewRegime').show();
        }else if(staffTaxRegime == 'Old Regime'){
            $('#taxCalculationOldRegime').show();
        }
        $('#staffTaxDetails').show();
    });
}

function closeModal() {
    const modal = document.getElementById('pdfModal');
    modal.style.display = 'none';
    const iframe = document.getElementById('pdfViewer');
    iframe.src = ''; 
}

function viewAttachment(fileUrl, key) {
    let columnName = getColumnName(key);
    const pdfModal = document.getElementById('pdfModal');
    const pdfViewer = document.getElementById('pdfViewer');
    $('#pdfModal #fileName').html(columnName);
    pdfViewer.src = fileUrl;

    pdfModal.style.display = 'block';
}

function getColumnName(columnName) {
    const categories = {
        'availing_company_accommodation': 'Availing Company Accommodation',
        'grand_total_rent': 'Rent',
        'landlord_pan_card': 'PAN of the landlord',
        'public_provident_fund': 'Public Provident Fund (PPF)',
        'nsc_investment': 'National Savings Certificate (Investment + Accrued Interest)',
        'tax_saving_fixed_deposit': 'Tax Saving Fixed Deposit (5 Years and above)',
        'elss_mutual_fund': 'ELSS Tax Saving Mutual Fund',
        'life_insurance': 'Life Insurance Premium',
        'other_80c_investments': 'Other 80C Investments',
        'new_pension_scheme': 'New Pension Scheme (NPS) (U/S 80CCC)',
        'pension_plan_for_insurance': 'Pension Plan from Insurance Co./Mutual Funds (U/S 80CCC)',
        'principal_repayment_house_loan': 'Principal Repayment on House Building Loan',
        'sukanya_samriddhi_yojana': 'Sukanya Samriddhi Yojana / Deposit Scheme',
        'stamp_duty_registration_fees': 'Stamp Duty & Registration Fees on House Buying',
        'tution_fees_for_children': 'Tuition Fees for Children (max 2 Children)',
        'additional_deducation_for_nps': 'Additional Deduction for National Pension Scheme U/S 80CCD(1B)',
        'eightyd_medical_insurance_premium_self': '80D Medical Insurance Premium (for Self, Spouse & Children)',
        'medical_insurance_premium_self_80d_senior': '80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen',
        'preventive_health_checkup_80d': '80D - Preventive Health Checkup',
        'medical_bills_for_self_senior': '80D - Medical Bills for Self, Spouse, Children - Senior Citizen',
        'eightyd_medical_insurance_premium_parent': '80D Medical Insurance Premium (for Parents)',
        'medical_insurance_premium_parent_80d_senior': '80D Medical Insurance Premium (for Parents) - Senior Citizen',
        'preventive_health_checkup_parents_80d': '80D - Preventive Health Checkup for Parents',
        'medical_bills_for_parents_senior': '80D - Medical Bills for Parents - Senior Citizen',
        'eightydd_medical_treatment_dependent_handicapped': '80DD Medical Treatment for Dependent Handicapped',
        'medical_treatment_dependent_handicapped_servere_80dd': '80DD Medical Treatment for Dependent Handicapped - Severe',
        'eightyddb_expenditure_medical_tretment_self_dependent': '80DDB Expenditure on Medical Treatment for Self/Dependent',
        'expenditure_medical_tretment_self_dependent_80ddb_senior': '80DDB Expenditure on Medical Treatment for Self/Dependent - Senior',
        'eightye_interest_paid_education': '80E Interest Paid on Education Loan',
        'eightytta_b_senior_citizens': '80TTA/B Interest from Savings Account',
        'eightyggc_donation_approved_funds': '80G - Donations to Approved Funds (100% Exemption)',
        'donation_approved_funds_80ggc_fifty': '80G - Donations to Approved Funds (50% Exemption)',
        'eightygg_rent_paid_no_hra_recived': '80GG Rent Paid in Case of No HRA Received',
        'eightyu_physically_disabled_person': '80U Physically Disabled Person',
        'physically_disabled_person_80u_severe': '80U Physically Disabled Person - Severe',
        'other_employer_income': 'Other Employer Income',
        'other_employer_tds': 'Other Employer TDS',
        'interest_paid_on_home_loan': 'Interest Paid on Home Loan',
        'leave_travel_allowance': 'Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)'
    };

    // Return the description if the column name exists, otherwise return the column name itself
    return categories[columnName] || columnName;
}

function getTaxCalculationsComparison(){
    var staffid = '<?php echo $staffid ?>';
    var selected_financial_year_id = $('#schedule_year').val();
    var baseUrl = '<?php echo site_url('staff/Payroll_controller/income_tax_calculation'); ?>';
    var fullUrl = baseUrl + '/' + selected_financial_year_id + '/' + staffid + '/viewOnly';
    window.open(fullUrl, '_blank');
}
</script>

<style>
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.form-group:last-child{
    margin-bottom: 6px;
}
ul.panel-controls>li>a {
    border-radius: 50%;
}

.disabled{
    color: lightgray !important;
}

.sub_header_note {
    color: #8f7f7f !important;
    font-size: 14px;
}

.regime{
    color: #398439;
    font-weight: bold;
}

/* #pdfModal {
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
}

#pdfModalContent {
    background-color: white;
    margin: 3% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 900px;
}

#pdfModalClose {
    color: white;
    font-size: 20px;
    font-weight: bold;
    position: absolute;
    top: 25px;
    right: 190px;
    cursor: pointer;
}

#pdfModalClose:hover,
#pdfModalClose:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
} */
</style>