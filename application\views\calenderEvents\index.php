<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/Management_controller'); ?>">Management</a></li>
    <li><a href="<?php echo site_url('calender_events/calenderevents'); ?>">Events/Holidays </a></li>
</ul>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('management/management_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Events/Holidays
          </h3>
          <input type="button"  name="text" onclick="upcomingevents()" id="upcomingevents" class="btn btn-primary" value="Upcoming Events" style="float:right" data-toggle="modal" data-target="#upcomingevents_modal">

<div id="upcomingevents_modal" class="modal fade width-modal" role="dialog">
    <div class="modal-dialog" >
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title" style="color:#1e428a;"><strong>Upcoming Events</strong></h4>
        </div>
              <div class="modal-body" style="height:450px; overflow:scroll;">
                <div id="upcomingevents_data">
                </div>
              </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
       </div>
    </div>
  </div>   
        </div>
      </div>
    </div>

<div class="page-content-wrap">
    <div class="row" style="margin: 0px;display: block;">
        <div class="col-md-6">
            <div class="panel panel-default new-panel-style_3 ">
                <?php $site_url = site_url('calender_events/calenderevents/addEvent/');
                if ($id != null) {
                    $site_url .= $editData[0]->id;
                }
                ?>

                <form id="demo-form" action="<?php echo $site_url; ?>" class="form-horizontal" data-parsley-validate
                    method="POST" enctype="multipart/form-data">
                    <div class="panel-heading new-panel-heading ">
                        <h3 class="panel-title"><strong>Add Holidays/Events </strong></h3>
                    </div>
                    <div class="panel-body">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-md-4 control-label">Event/Holiday Name <font color="red">*</font>
                                    </label>
                                <div class="col-md-6">
                                    <input placeholder="Enter Event/Holiday Name" name="event_name" id="event_name" type="text"
                                        value="<?php echo ($id == null) ? '' : htmlspecialchars($editData[0]->event_name, ENT_QUOTES, 'UTF-8'); ?>"
                                        class="form-control input-md"  data-parsley-error-message="Cannot be empty or include Special Caracter" 
                                        data-parsley-minlength="1" required="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-4 control-label">Event/Holiday Type </label>
                                <div class="col-md-6">
                                    <select id="event_type" name="event_type" class="form-control classId select">
                                        <option value="1" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 1) ? 'selected' : ''); ?>>Event</option>
                                        <option value="2" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 2) ? 'selected' : ''); ?>>Holiday</option>
                                        <option value="3" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 3) ? 'selected' : ''); ?>>Holiday Range</option>
                                        <option value="4" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 4) ? 'selected' : ''); ?>>Event Range</option>
                                        <option value="5" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 5) ? 'selected' : ''); ?>>Info</option>
                                        <option value="6" <?php echo ($id == null) ? '' : (($editData[0]->event_type == 6) ? 'selected' : ''); ?>>Info Range</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-4 control-label" for="from_date"> Date <font color="red">*</font>
                                    </label>
                                    
                                <div class="col-md-6" >
                                    <div class="input-group date" id="fromdatePicker" >
                                        <input autocomplete="off" type="text" class="form-control input-md" id="from_date"
                                            name="from_date"
                                            value="<?php echo ($id == null) ? '' : date("d-m-Y", strtotime($editData[0]->from_date)); ?>"
                                            placeholder="Enter Date of event"
                                            data-parsley-error-message="Cannot be empty"
                                            data-parsley-pattern="\d{2}\-\d{2}\-\d{4}" data-parsley-minlength="1"
                                            required="" data-parsley-errors-container="#checkbox-errors">
                                        <span class="input-group-addon" >
                                            <span class="glyphicon glyphicon-calendar" ></span>
                                        </span>
                                       
                                    </div>
                                    <div id="checkbox-errors"></div>
                                </div>
                            </div>

                            <div class="form-group" id="todate" <?php echo ($id == null) ? 'style="display:none;"' : (($editData[0]->event_type == 3 || $editData[0]->event_type == 4) ? '' : 'style="display:none;"'); ?>>
                                <label class="col-md-4 control-label" for="to_date">End Date<font color="red">*</font>
                                    </label>
                                <div class="col-md-6">
                                    <div class="input-group date" id="todatePicker">
                                        <?php $value = "";
                                        if ($id != null) {
                                            if ($editData[0]->to_date != null) {
                                                $value = date("d-m-Y", strtotime($editData[0]->to_date));
                                                
                                            }
                                        } ?>
                                        <input autocomplete="off" type="text" class="form-control input-md" id="to_date" name="to_date" value="<?php echo ($id == null) ? '' : date("d-m-Y", strtotime($editData[0]->to_date)); ?>"
                                        placeholder="Enter Date of event" data-parsley-pattern="\d{2}\-\d{2}\-\d{4}"
                                        data-parsley-error-message="Cannot be empty" data-parsley-minlength="1"
                                        data-parsley-errors-container="#checkbox-errors1">
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                    <div id="checkbox-errors1"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-4 control-label">Applicable To </label>
                                <div class="col-md-6">
                                    <select id="applicable_to" name="applicable_to" class="form-control classId select"
                                        onchange="showClasses()">
                                        <option value="1" <?php echo ($id == null) ? '' : (($editData[0]->applicable_to == 1) ? 'selected' : ''); ?>>All Staff</option>
                                        <option value="2" <?php echo ($id == null) ? '' : (($editData[0]->applicable_to == 2) ? 'selected' : ''); ?>>All Parents</option>
                                        <option value="3" <?php echo ($id == null) ? '' : (($editData[0]->applicable_to == 3) ? 'selected' : ''); ?>>All Staff and Parents</option>
                                        <option value="4" <?php echo ($id == null) ? '' : (($editData[0]->applicable_to == 4) ? 'selected' : ''); ?>>Parents Of Selected Class</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" id="select_class" style="display: none;">
                                <label class="col-md-4 control-label">Select Class</label>
                                <div class="col-md-6">
                                    <select id="class_section" placeholder="Select class" class="form-control select2" name="class_section[]"
                                        multiple="multiple">
                                        <?php 
                                            $selected_class_sections = [];
                                            if (!empty($editData) && isset($editData[0]->class_section)) {
                                                $selected_class_sections = (array)json_decode($editData[0]->class_section);
                                            }
                                            foreach ($class_section as $key => $cls_section) {
                                                $selected = in_array($cls_section->id, $selected_class_sections) ? 'selected' : ''; ?>
                                                <option value="<?= $cls_section->id; ?>"  <?= $selected; ?>>
                                                    <?= $cls_section->class_name . '' . $cls_section->section_name; ?>
                                                </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-4 control-label">Enable Flash News</label>
                                <div class="col-md-6 col-xs-12">
                                    <?php 
                                         $checked = '';
                                         if (!empty($editData) && $editData[0]->enable_flash_news == 1) {
                                             $checked = 'checked';
                                         }
                                    ?>
                                    <label class="check9" for="enable_flash_news">
                                        <div class="icheckbox_minimal-grey" style="position: relative;">
                                            <input type="checkbox" <?php echo $checked ?> class="icheckbox"
                                                style="position: absolute; opacity: 0;" id="enable_flash_news"
                                                name="enable_flash_news">
                                            <ins class="iCheck-helper"
                                                style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;">
                                            </ins>
                                        </div> Enable Flash News
                                    </label>
                                    <!-- <span class="help-block">Checkbox sample, easy to use</span> -->
                                </div>
                            </div>

                            <?php $boards = $this->settings->getSetting('board'); ?>
                            <div class="form-group">
                                <label class="col-md-4 control-label">Board </label>
                                <div class="col-md-6">
                                    <select id="board" name="board" class="form-control input-md classId select">
                                        <option value='100'>All</option>
                                        <!---'100' means 'All -->
                                        <?php foreach ($boards as $id => $name) {
                                            $selected = '';
                                            if ($id != null && !empty($editData) && $editData[0]->board == $id) {
                                                $selected = 'selected';
                                            }
                                            echo '<option value="' . $id . '" ' . $selected . '>' . $name . '</option>';
                                        } ?>
                                    </select>
                                </div>
                            </div>

                        </div>
                    </div>
                    <center>
                        <div class="panel-footer new-footer">
                            <?php if (!empty($editData)) { ?>
                                <input type="submit" id="submit_event" value="Update" class="btn btn-primary">
                                <a class="btn btn-warning"
                                    href="<?php echo site_url('calender_events/calenderevents/') ?>">Cancel</a>
                            <?php } else { ?>
                                <input type="submit" value="Submit" id="submit_event" class="btn btn-primary">
                                <a class="btn btn-warning"
                                    href="<?php echo site_url('calender_events/calenderevents/') ?>">Cancel</a>
                            <?php } ?>

                        </div>
                    </center>
                </form>
            </div>
        </div>

        <div class="col-md-6">
       
           
            <div class="panel panel-default new-panel-style_3">
                <div class="panel-heading new-panel-heading">
                    <h3 class="panel-title" style="display: block;"><strong>Holidays/Events </strong></h3>
                    <div class="pull-right">
                        <div id="loader" class="loaderclass" style="display:none"></div>
                        <p style="display: inline;"><span class="fa fa-square staff"></span> Staff Events</p>
                        <p style="display: inline;"><span class="fa fa-square parent"></span> Parent Events</p>
                        <p style="display: inline;"><span class="fa fa-square both"></span> Both</p>
                        <p style="display: inline;"><span class="fa fa-square Selected_class"></span>Selected Class</p>
                    </div>
                    <?php $cm = date("m"); ?>
                    <select id="month" name="month" class="form-control input-md">
                        <?php
                        foreach ($months as $key => $month) {
                            echo '<option ' . $month['is_current'] . ' value="' . $month['index'] . '">' . $month['name'] . '</option>';
                        }
                        ?>
                    </select>
                </div>
                <div class="panel-body" id="monthEvents" style="max-height:50rem;min-height:50rem;overflow-x:auto">
                    <ul class="list-group">
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>

    $(document).ready(function () {
        function toggleToDate() {
            var eventType = parseInt($("#event_type").val(), 10); // Get updated event type value
            var toDateDiv = $("#todate");
            var toDateInput = $("#to_date");

            if (eventType === 3 || eventType === 4) {
                toDateDiv.show();
            } else {
                toDateDiv.hide();
                toDateInput.val(""); // Clear the input field when hidden
            }
        }

        toggleToDate(); // Call the function on page load

        // If event type changes dynamically, call toggleToDate() again
        $("#event_type").on("change", function () {
            toggleToDate();
        });
    });


    document.getElementById("submit_event").addEventListener("click", function (e) {
        e.preventDefault(); // Prevent form submission
        let actionText = (this.value === "Update") ? "Do you want to update this event?" : "Do you want to add an event?";
        Swal.fire({
            title: "Event Confirmation",
            text: actionText,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, submit it!"
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById("submit_event").closest("form").submit();
            }
        });
    });
   

    $("form").submit(function(){
        const eventName=$("#event_name").val();
        const formDate=$("#from_date").val();

        if(eventName && formDate){
            $("#submit_event").text("Please wait...").prop("disabled",true);
            swal("Event Added Successfully",{
                            button:true
                        });
                    }
            
    });

    $(document).ready(function() {
        $('#class_section').select2({
                width: '100%',
                placeholder: 'Select Class'
        });

        $(document).on('click', function(e) {
            if (!$(e.target).closest('#select_class').length) {
                $('#class_section').select2('close');
            }
        });
    });

    function deleteEvent(id) {
        $.ajax({
            url: '<?php echo site_url("calender_events/calenderevents/deleteEvent"); ?>',
            dataType: 'json',
            method: 'post',
            data: { 'id': id },
            success: function (data1) {
                swal("Event Deleted Successfully",{
                            button:true
                        });
                window.location = '<?php echo site_url("calender_events/calenderevents/"); ?>';
            }
        });
    }

   
    $(document).ready(function () {
        var selected_month = _get_cookie('selected_month');
        if(selected_month && selected_month != null && selected_month != '') {
            $("#month option[value='"+selected_month+"']").prop('selected', true);
        }
        showClasses();
        getallEvents();
        getMonthEvents();
        var maxdate = new Date();
        maxdate.setFullYear(maxdate.getFullYear() + 2);
        var event_type = $("#event_type").val();
        if (event_type == 3 || event_type == 4 || event_type == 6) {
            $("#todate").show();
            $("#to_date").attr('required','required');

            $('#fromdatePicker,#from_date').datetimepicker({
                useCurrent: false,
                viewMode: 'days',
                format: 'DD-MM-YYYY',
                maxDate: maxdate
            }).on('dp.change', function (selected) {
                var minDate = moment(new Date(selected.date));
                minDate.add(1, 'days');
                $('#todatePicker').data('DateTimePicker').minDate(minDate);
                $('#to_date').data('DateTimePicker').minDate(minDate);
                $(this).data("DateTimePicker").hide();
            });

            $('#todatePicker,#from_date').datetimepicker({
                useCurrent: false,
                viewMode: 'days',
                format: 'DD-MM-YYYY',
                maxDate: maxdate
            }).on('dp.change', function (e) {
                var decrementDay = moment(new Date(e.date));
                decrementDay.subtract(1, 'days');
                $('#fromdatePicker').data('DateTimePicker').maxDate(decrementDay);
                $('#from_date').data('DateTimePicker').maxDate(decrementDay);
                $(this).data("DateTimePicker").hide();
            });
        } else {
            $("#todate").hide();
            $("#to_date").removeAttr('required');
            $('#fromdatePicker,#from_date').datetimepicker({
                useCurrent: false,
                viewMode: 'days',
                format: 'DD-MM-YYYY',
                maxDate: maxdate
            });
        }


        $("#event_type").change(function () {
            var value = $(this).val();
            if (value == 3 || value == 4 || value == 6) {
                $("#todate").show();
                $("#to_date").attr('required','required');
                $('#fromdatePicker,#from_date').datetimepicker({
                    useCurrent: false,
                    viewMode: 'days',
                    format: 'DD-MM-YYYY',
                    maxDate: maxdate
                }).on('dp.change', function (selected) {
                    var minDate = moment(new Date(selected.date));
                    minDate.add(1, 'days');
                    $('#todatePicker').data('DateTimePicker').minDate(minDate);
                    $('#to_date').data('DateTimePicker').minDate(minDate);
                    $(this).data("DateTimePicker").hide();
                });

                $('#todatePicker,#to_date').datetimepicker({
                    useCurrent: false,
                    viewMode: 'days',
                    format: 'DD-MM-YYYY',
                    maxDate: maxdate
                }).on('dp.change', function (e) {
                    var decrementDay = moment(new Date(e.date));
                    decrementDay.subtract(1, 'days');
                    $('#fromdatePicker').data('DateTimePicker').maxDate(decrementDay);
                    $('#from_date').data('DateTimePicker').maxDate(decrementDay);
                    $(this).data("DateTimePicker").hide();
                });
            } else {
                $("#todate").hide();
                $("#to_date").removeAttr('required');
            }
        });

        function formatDate(date) {
            var day = new Date(date);
            var dd = day.getDate();
            var mm = day.getMonth() + 1;
            var yyyy = day.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            var formateddate = dd + '-' + mm + '-' + yyyy;
            return formateddate;
        }

        function getMonthEvents() {
           
            var month = $("#month").val();
            _set_cookie('selected_month', month);
            var siteUrl = '<?php echo site_url('calender_events/calenderevents/'); ?>';
            $('#loader').show();
            $.ajax({
                url: '<?php echo site_url('calender_events/calenderevents/get_monthEvents'); ?>',
                data: { 'month': month },
                type: "post",
                success: function (data) {
                    EventData = $.parseJSON(data);
                    upcomingevents()
                     //console.log(EventData);
                    var output = '';

                    const EventTypeColor={
                        1:"#ffa906",
                        2:"#00701a",
                        3:"#060bf3",
                        4:"#795548",
                        // 5:"#711DB0",
                        // 6:"#711DB0",
                    }

                    output += '<ul class="list-group">';
                    var i = 0;
                    for (; i < EventData.length; i++) {
                        var eventDate = EventData[i].fdate;
                        if (EventData[i].fdate!=EventData[i].tdate && EventData[i].tdate != null && EventData[i].full_tdate_year!=1970) {
                            eventDate += ' to ' + EventData[i].tdate;
                        }
                        var color = 'All Staff';
                        if (EventData[i].applicable_to == 2) {
                            color = 'All Parents';
                        } else if (EventData[i].applicable_to == 3) {
                            color = 'All Staff and Parents';
                        } else if (EventData[i].applicable_to == 4) {
                            color = 'Class - '+EventData[i].classs_name;
                        }
                        output += `
                    <li class="list-group-item" style="color:${EventTypeColor[EventData[i].applicable_to]}">
                    <h4>
                            ${eventDate}
                            <a href="${siteUrl}${escape(EventData[i].id)}" class="badge btn btn-primary pull-right" style="margin-left:2px;">
                                <i class="fa fa-edit"></i>
                            </a> </h4>
                            <button onclick="confirmDelete('${EventData[i].id}')" class="badge btn btn-danger pull-right"><i class="fa fa-trash-o"></i></button>
                           
                        Applicable for ${color}<br>
                        <b >${EventData[i].event_name}</b>
                    </li>
                `;
                    };
                    if (i == 0) {
                        output += '<li class="list-group-item">No Events/Holidays </li>';
                    }
                    output += '</ul>';
                    $('#monthEvents').html(output);
                    $('#loader').hide();
                },
                error: function (err) {
                    console.log(err);
                }
            });
        }

        $("#month").change(function () {
            getMonthEvents();
        });

    });
    
    function formatDate(dateString) {
    const options = { day: 'numeric', month: 'short', year: 'numeric' };
    const formattedDate = new Date(dateString).toLocaleDateString('en-US', options);
    return formattedDate;
}

    function getallEvents(){
        $.ajax({
    url: '<?php echo site_url('calender_events/calenderevents/getallEvents'); ?>',
    type: 'post',
    success: function(data) {
        parced_data = $.parseJSON(data);
        html = upcomingevents(parced_data);
      $("#upcomingevents_data").html(html);
      
    },
    error: function (err) {
        console.log(err);
    }
  });
}
    function upcomingevents(parced_data){
        //console.log(parced_data);
        if (parced_data && parced_data.length) {
        eventdate = '';
        upcomingEvents = [];
        html ='';
        html += '<div class="table-responsive" >';
        html+='<table class="table table-bordered">';
        html += '<thead><tr><th>#</th><th>Event From Date</th><th>Event Upto </th><th>Event Name</th></tr></thead>';
        
        for(var i=0; i < parced_data.length;i++){
            var data = parced_data[i];
            var format_from_Date = formatDate(data.from_date);
            var format_to_Date = formatDate(data.to_date);
            if (format_to_Date == 'Jan 1, 1970'){
                format_to_Date ='-';
            }
            html+='<tbody>';
            html+='<tr>';
            html+='<td>' + (i+1) + '</td>';
            html+='<td>' + format_from_Date + '</td>';
            html+='<td>' + format_to_Date + '</td>';
            html+='<td>' + data.event_name + '</td>';
            html+='</tr>';
            html+='</tbody>';
        }
        i++;
        
        html+='</table>';
            return html;
        } else {
        
        return '<h2>No events available.</h2>';
    }

        
    }
    

    function showClasses() {
        let selectedOption = $("#applicable_to").val();
        if (selectedOption == 4)
            $("#select_class").show();
        else
            $("#select_class").hide();
    }



function confirmDelete(eventId) {
        var confirmDelete = confirm("Are you sure you want to delete this event?");
        if (confirmDelete) {
            deleteEvent(eventId);
        }
    }

</script>



<style type="text/css">
    .modal {
    overflow-y:auto;
    
  }

  .modal-dialog{
    margin:8% 18%;
    width: 60%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 4px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 12%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}
    .loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 30%;
  margin-left: 70%;
  position: absolute;
  z-index: 99999;
}
    .list-group-item {
        border-color: #351f1f;
    }

    .staff {
        color: #ffa906;
    }

    .parent {
        color: #00701a;
    }

    .both {
        color: #060bf3;
    }
    .Selected_class{
        color: #795548;

    }
  


input.parsley-error-message,
select.parsley-error,
textarea.parsley-error {
  color: #B94A48;
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
}

.parsley-errors-list {
  margin: 2px 0 3px;
  padding: 0;
  list-style-type: none;
  font-size: 0.9em;
  line-height: 0.9em;
  opacity: 0;

  transition: all .3s ease-in;
  -o-transition: all .3s ease-in;
  -moz-transition: all .3s ease-in;
  -webkit-transition: all .3s ease-in;
}

.parsley-errors-list.filled {
  opacity: 1;
}

</style>