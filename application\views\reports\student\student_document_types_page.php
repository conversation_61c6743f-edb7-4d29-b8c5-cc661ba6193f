<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li>Student Document Types</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="card-title panel_title_new_style_staff m-0 col-md-12">
                    <a class="back_anchor" href="<?php echo site_url('student/student_menu'); ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Manage Student Document Types
                    <span class="" style="margin: 0 0 0 20px; float: right">
                        <!-- <button class="btn btn-primary" id="btn_config" onclick="add_docs_from_config()">Add Docs from Config</button> -->
                        <a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target="#student_documents_modal" >
                            <span class="fa fa-plus " style="font-size: 19px;"></span>
                        </a>
                    </span>
                </h3>
            </div>
            <div class="col-md-12" style="margin-top:15px">
                <div class="col-md-14 text-center" style="height: 10%; width: 100%;" id="documents_data">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>
                <div id="table_div">
                </div>
            </div>
        </div>
    </div>
</div>

<div>
    <form id="thisform">
    <?php if(!empty(json_decode($this->settings->getSetting('student_documents_name')))) { ?>
        <?php foreach (json_decode($this->settings->getSetting('student_documents_name')) as $key => $value) { ?>
            <input class="config" id="config<?php echo $key; ?>" type="hidden" value="<?php echo $value; ?>">
        <?php } }?>
    </form>
</div>


<div class="modal fade" id="student_documents_modal" tabindex="-1" role="dialog" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="width: 50%;margin: auto; margin-top: 1% !important;border-radius: 8px;">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Add Document Type</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <form id="doctype_add_form">
            <div class="modal-body">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Document Name<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <span class="fa fa-pencil"></span>
                                </span>
                                <input type="text" class="form-control" id="document_name" placeholder="Enter Document Name" name="document_name" maxlength="100" minlength="1" required>
                            </div>
                            <div class="help-block">Add a new document Type Here</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Document Type<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <select name="document_type" id="document_type" class="form-control">
                                <option value="">Select Document Type</option>
                                <option value="Aadhar">Aadhar</option>
                                <option value="PAN">PAN</option>
                                <option value="DL">DL</option>
                                <option value="Other">Other</option>
                            </select>
                            <div class="help-block">Add a new document Type Here</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Relation Type<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="relation_type" id="relation_type" class="form-control" required="">
                                    <option selected value="Student">Student</option>
                                    <option value="Father">Father</option>
                                    <option value="Mother">Mother</option>
                                    <option value="Guardian">Guardian</option>
                                </select>
                            </div>
                            <div class="help-block">Select visibility</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Based on nationality</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group mb-3">
                                <select name="based_on_nationality" id="based_on_nationality" class="form-control">
                                    <option value="All">All</option>
                                    <option value="if_nationality_indian">Only For Indians</option>
                                    <option value="if_nationality_other">Non-Indians</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Visible to Parent?<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group mb-3">
                                <select name="visibility" id="visibility_add" class="form-control" required="">
                                    <option selected value="1">Yes</option>
                                    <option value="0">No</option>
                                </select>
                            </div>
                            <div class="help-block">Select visibility</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Need Approval?<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group  mb-3">
                                <select name="need_approval" id="need_approval" class="form-control" required="">
                                    <option selected value="Yes">Yes</option>
                                    <option value="No">No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Is Mandatory?<font color="red">*</font></label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group  mb-3">
                                <select name="is_mandatory" id="is_mandatory" class="form-control" required="">
                                    <option value="1">Yes</option>
                                    <option selected value="0">No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Document Size in MB</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group  mb-3">
                                <input type="number" name="document_size" id="document_size" class="form-control" placeholder="Enter the document size in Mb">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div> <input type="reset" class="btn btn-danger" value="Clear Form" ></div>
                <button type="button" class="btn btn-primary" onclick="add_document_type()">Submit</button>
            </div>
        </form>
    </div>
</div>

<div class="modal fade" id="student_documents_modal_edit" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader1">Update Visibility</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <form>
            <input type="hidden" id="doc_id" value="">
            <div class="modal-body">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="name" class="col-md-4 col-xs-12 control-label">Document Type</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="edit_doc_type" id="edit_doc_type" class="form-control">
                                    <option value="">Edit Document type</option>
                                    <option id="aadhar_id" value="Aadhar" >Aadhar</option>
                                    <option id="pan_id" value="PAN" >PAN</option>
                                    <option id="dl_id" value="DL">DL</option>
                                    <option id="none_id" value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12" style="margin-top:10px;">
                <div class="form-group">
                        <label for="" class="col-md-4 col-xs-12 control-label">Based on nationality</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="edit_based_on_nationality" id="edit_based_on_nationality" class="form-control">
                                    <option value="All">All</option>
                                    <option value="if_nationality_indian">Only For Indians</option>
                                    <option value="if_nationality_other">Non-Indians</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12" style="margin-top:10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4 col-xs-12 control-label">Visible to Student?</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="visibility" id="visibility_edit" class="form-control">
                                    <option id="yes_id" value="1">Yes</option>
                                    <option id="no_id" value="0">No</option>
                                </select>
                            </div>
                            <div class="help-block">Change visibility</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12" style="margin-top:10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4 col-xs-12 control-label">Need Approval..?</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="edit_approval" id="edit_approval" class="form-control">
                                    <option id="approve_yes" value="Yes">Yes</option>
                                    <option id="approve_no" value="No">No</option>
                                </select>
                            </div>
                            <div class="help-block">Need Approval</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12" style="margin-top:10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4 col-xs-12 control-label">Is Mandatory?</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <select name="edit_is_mandatory" id="edit_is_mandatory" class="form-control">
                                    <option id="mandatory_yes" value="1">Yes</option>
                                    <option id="mandatory_no" value="0">No</option>
                                </select>
                            </div>
                            <div class="help-block">Is It Mandatory?</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-3" style="margin-top:10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4 col-xs-12 control-label">Document size in MB</label>
                        <div class="col-md-8 col-xs-12">
                            <div class="input-group">
                                <input type="number" name="edit_document_size" id="edit_document_size" class="form-control" placeholder="Enter the document size in MB">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div> <input type="reset" class="btn btn-outline-secondary" value="Clear Form" ></div>
                <button type="button" class="btn btn-outline-primary" onclick="edit_document()">Update</button>
            </div>
        </form>
    </div>
</div>

<script>
    $(document).ready(function() {
        get_documents_data();
    });

    function add_document_type() {
        var form = $('#doctype_add_form');
        if (!form.parsley().validate()) {
            return 0;
        }
        var formElement = form[0];
        var formData = new FormData(formElement);

        var doc_name= $("#document_name").val();
        var relation_type= $("#relation_type").val();
        var visibility= $("#visibility_add").val();
        if(doc_name)
            $.ajax({
                url: '<?php echo site_url('reports/student/Student_docs_controller/add_student_document_types'); ?>',
                type: "post",
                data: formData,
                processData: false,
                contentType: false,
                async: false,
                success: function(data) {
                    if(data == 1){
                        $(function(){
                            new PNotify({
                                title:'Success',
                                text: 'Successfully Added',
                                type:'success',
                            });
                        });
                    } else {
                        $(function(){
                            new PNotify({
                                title:'Error',
                                text: 'Document Type Already Exists',
                                type:'error',
                            });
                        });
                    }
                    formElement.reset();
                    form.parsley().reset();
                    $('#student_documents_modal').modal('hide');
                    get_documents_data();
                },
                error: function(err){
                    console.log(err);
                    formElement.reset();
                    form.parsley().reset();
                    $('#student_documents_modal').modal('hide');
                    get_documents_data();
                }
            });
    }

    // function refresh_modal(){
    //     $('#document_name').val('');
    // }

    function get_documents_data(){
        $('#documents_data').show();
        $('#table_div').html('');
        $.ajax({
            url: '<?php echo site_url('reports/student/Student_docs_controller/get_student_document_types'); ?>',
            type: "post",
            success: function(data) {
                var p_data = JSON.parse(data);
                if(p_data == ''){
                    $('#table_div').html('<div class="no-data-display">No Data Found</>')
                }else{
                    $('#table_div').html(_construct_document_table(p_data));
                }
                $('#documents_data').hide();
            },
            error: function(error){
                console.log(error);
                $('#documents_data').hide();
                $('#table_div').html('<div class="no-data-display">No Data Found</>')
            }
        });
    }

    function _construct_document_table(p_data){
        var html = '';
        html +=`<table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Document Name</th>
                            <th>Document Type</th>
                            <th>Relation Type</th>
                            <th>Visibility to Parent</th>
                            <th>Need Approval</th>
                            <th>Is Mandatory</th>
                            <th>Document SIze in MB</th>
                            <th>Based On Nationality</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="body_id">`
                    if(p_data) {
                        for (var index in p_data) {
                            var status = `
                                <label class="switch m-0">
                                    <input type="checkbox" onchange="updateStatus(${p_data[index].id}, this.checked)">
                                    <span></span>
                                </label>`;
                                
                            if (p_data[index].status == 1) {
                                status = `
                                <label class="switch m-0">
                                    <input type="checkbox" checked onchange="updateStatus(${p_data[index].id}, this.checked)">
                                    <span></span>
                                </label>`;
                            }

                            html += `
                                <tr>
                                    <td>${Number(index) + 1}</td>
                                    <td>${p_data[index].document_name}</td>
                                    <td>${p_data[index].document_type}</td>
                                    <td>${p_data[index].for_relation}</td>
                                    <td>${p_data[index].visibile_for_parents}</td>
                                    <td>${p_data[index].need_approval}</td>
                                    <td>${p_data[index].is_mandotary_display}</td>
                                    <td>${p_data[index].document_size_in_mb}</td>
                                   <td>${p_data[index].based_on_nationality.replace(/_/g, ' ')}</td>
                                    <td>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <button class="btn btn-info btn-sm" style="background-color: #fe970a; border: none;height: 2.5rem;" onclick="edit_visibility('${p_data[index].id}', '${p_data[index].visibile_for_parents}','${p_data[index].document_name}','${p_data[index].document_type}','${p_data[index].need_approval}',${p_data[index].is_mandatory},${p_data[index].document_size_in_mb},'${p_data[index].based_on_nationality}')">
                                                <span class="fa fa-edit"></span>
                                            </button>
                                            ${status}
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }
                    } 
                    `</tbody>`;
        return html;
    }

    function updateStatus(id, checked) {
    const status = checked ? 1 : 0;
    $.ajax({
        url: '<?php echo site_url('reports/student/Student_docs_controller/update_documents_status') ?>',
        type: 'post',
        data: {
            id: id,
            status: status
        },
        success: function(response) {
            if(response == 1) {
                Swal.fire({
                    icon: "success",
                    title: "Status updated successfully",
                    showConfirmButton: false,
                    timer: 1500
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Failed to update status",
                    showConfirmButton: false,
                    timer: 1500
                });
            }
            get_documents_data();
        }
    });
    }

    function edit_visibility(id,visibility,document_name,document_type,need_approval, is_mandatory,document_size_in_mb,based_on_nationality){
        $('#doc_id').val(id);
        if(visibility == 'No') {
            $('#visibility_edit #yes_id').removeAttr('selected');
            $('#visibility_edit #no_id').attr('selected', 'selected');
        }
        else {
            $('#visibility_edit #no_id').removeAttr('selected');
            $('#visibility_edit #yes_id').attr('selected', 'selected');
        }
        if(document_type == 'Aadhar') {
            $('#edit_doc_type #aadhar_id').attr('selected','selected');
            $('#edit_doc_type #pan_id').removeAttr('selected');
            $('#edit_doc_type #dl_id').removeAttr('selected');
            $('#edit_doc_type #none_id').removeAttr('selected');
        }
        else if(document_type == 'PAN'){
            $('#edit_doc_type #aadhar_id').removeAttr('selected');
            $('#edit_doc_type #pan_id').attr('selected', 'selected');
            $('#edit_doc_type #dl_id').removeAttr('selected');
            $('#edit_doc_type #none_id').removeAttr('selected');

        }else if(document_type == 'DL'){
            $('#edit_doc_type #aadhar_id').removeAttr('selected');
            $('#edit_doc_type #pan_id').removeAttr('selected');
            $('#edit_doc_type #dl_id').attr('selected','selected');
            $('#edit_doc_type #none_id').removeAttr('selected');

        }else{
            $('#edit_doc_type #aadhar_id').removeAttr('selected');
            $('#edit_doc_type #pan_id').removeAttr('selected');
            $('#edit_doc_type #dl_id').removeAttr('selected');
            $('#edit_doc_type #none_id').attr('selected','selected');
        }
        if(need_approval == 'No') {
            $('#edit_approval #approve_yes').removeAttr('selected');
            $('#edit_approval #approve_no').attr('selected', 'selected');
        }
        else {
            $('#edit_approval #approve_no').removeAttr('selected');
            $('#edit_approval #approve_yes').attr('selected', 'selected');
        }
        if(is_mandatory == 0) {
            $('#edit_is_mandatory #mandatory_yes').removeAttr('selected');
            $('#edit_is_mandatory #mandatory_no').attr('selected', 'selected');
        }
        else {
            $('#edit_is_mandatory #mandatory_no').removeAttr('selected');
            $('#edit_is_mandatory #mandatory_no').attr('selected', 'selected');
        }
        $('#edit_document_size').val(document_size_in_mb);
        $('#edit_based_on_nationality').val(based_on_nationality)
        $('#student_documents_modal_edit').modal('show');
    }

    function edit_document(){
        var doc_id = $('#doc_id').val();
        var visibility= $("#visibility_edit").val();
        var edit_doc_type= $("#edit_doc_type").val();
        var edit_approval= $("#edit_approval").val();
        var edit_is_mandatory= $("#edit_is_mandatory").val();
        var edit_document_size= $("#edit_document_size").val();
        var edit_based_on_nationality = $("#edit_based_on_nationality").val();
        $.ajax({
            url: '<?php echo site_url('reports/student/Student_docs_controller/edit_student_document_types'); ?>',
            type: "post",
            data: {
                'visibility': visibility,
                'doc_id': doc_id,
                'edit_doc_type':edit_doc_type,
                'edit_approval':edit_approval,
                'edit_is_mandatory':edit_is_mandatory,
                'edit_document_size':edit_document_size,
                "edit_based_on_nationality":edit_based_on_nationality
            },
            success: function(data) {
                // var p_data = JSON.parse(data);
                var p_data = data.trim();
                if(p_data){
                    $(function(){
                        new PNotify({
                            title:'Success',
                            text: 'Successfully Updated',
                            type:'success',
                        });
                    });
                }else{
                    $(function(){
                        new PNotify({
                            title:'Error',
                            text: 'Something went wrong',
                            type:'error',
                        });
                    });
                }
                get_documents_data();
                $("#student_documents_modal_edit").modal('hide');
                $('#student_documents_modal_edit form').trigger("reset");
            }
        });
    }

    function delete_document_type(primary_id, document_type) {
        bootbox.confirm({
            title: 'Confirm',
            message: `Are you sure to delete document type <strong>${document_type}</strong>?`,
            buttons: {
                cancel: {
                    label: '<i class="fa fa-times"></i> No',
                    className: 'btn-danger'
                },
                confirm: {
                    label: '<i class="fa fa-check"></i> Yes, I Confirm'
                }
            },
            callback: function (result) {
                if(result)
                $.ajax({
                    url: '<?php echo site_url('reports/student/Student_docs_controller/delete_document_type'); ?>',
                    type: "post",
                    data: {
                        'primary_id': primary_id
                    },
                    success: function(data) {
                        if(data == 1){
                            $(function(){
                                new PNotify({
                                    title:'Success',
                                    text: 'Successfully Deleted',
                                    type:'success',
                                });
                            });
                        }
                        get_documents_data();
                    }
                });
            }
        }).find("div.modal-dialog").addClass("medium");
    }

    function add_docs_from_config() {
        var config_id_arr = $(".config").map(function() {
            return $(this).val();
        }).get();
        
        if (config_id_arr.length == 0) {
            return false;
        }
        
        $('#documents_data').show();
        $('#table_div').html('');

        var ajaxCalls = config_id_arr.map(function(docName) {
            return $.ajax({
                url: '<?php echo site_url('reports/student/Student_docs_controller/add_student_document_types'); ?>',
                type: "POST",
                data: {
                    'document_name': docName,
                    'relation_type': 'Student',
                    'visibility': 'yes'
                }
            });
        });

        $.when.apply($, ajaxCalls).done(function() {
            get_documents_data();
        });
    }

</script>

<style>
    .new_circleShape_res {
        padding: 8px 13px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .medium {
        width: 40%;
        margin: 0 auto;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>