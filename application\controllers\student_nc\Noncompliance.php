<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: Controller for Timetable Menu.
 *
 * Requirements: PHP5 or above
 *
 */

class Noncompliance extends CI_Controller
{
  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_NONCOMPLIANCE')) {
     
      redirect('dashboard', 'refresh');
    }
    $this->load->model('noncompliance_model', 'noncompliance_model');
    $this->load->library('filemanager');
  }

  public function manage_categories()
  {
    $data['penalty_details'] = $this->noncompliance_model->get_penalty_types();
    $data['staff_details'] = $this->noncompliance_model->get_all_staff();
    $data['main_content'] = 'student_nc/manage_categories';
    $this->load->view('inc/template', $data);
  }

  public function resolve()
  {
    $data['acad_years'] = $this->noncompliance_model->get_acad_years();
    $data['main_content'] = 'student_nc/resolve';
    $this->load->view('inc/template', $data);
  }

  public function resolve_nc()
  { 
    $snc_id = $_POST['snc_id'];
    $new_status = $_POST['new_status'];
    $add_grace_served_remarks =$_POST['add_grace_served_remarks'];

    echo json_encode($this->noncompliance_model->resolve_nc($snc_id, $new_status,$add_grace_served_remarks));
  }

  public function get_student_nc_history()
  {
    $sa_id = $_POST['sa_id'];

    echo json_encode($this->noncompliance_model->get_student_nc_history($sa_id));
  }

  public function get_resolve_penalty_types()
  {
    $penalty_status = $_POST['penalty_status'];
    $acad_year_id = $_POST['acad_year_id'];

    $show_all = 0;
    if ($this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.ADMIN')) {
      $show_all = 1;
    }

    echo json_encode($this->noncompliance_model->get_resolve_penalty_types($show_all, $penalty_status,$acad_year_id));
  }

  public function add_category()
  {
    $category_name = $_POST['category_name'];
    $category_color = $_POST['category_color'];
    $penalty_name = $_POST['penalty_name'];
    $penalty_staff_id = $_POST['penalty_staff_id'];
    $success = $this->noncompliance_model->add_category($category_name, $category_color, $penalty_name, $penalty_staff_id);
    echo json_encode($success);
  }

  public function get_category_types()
  {
    $data['category_types'] = $this->noncompliance_model->get_category_types_new();
    echo json_encode($data);
  }

  public function delete_category()
  {
    $category_id = $_POST['category_id'];
    $success = $this->noncompliance_model->delete_category($category_id);
    echo json_encode($success);
  }

  public function manage_penalty()
  {
    $data['main_content'] = 'student_nc/manage_penalty';
    $this->load->view('inc/template', $data);
  }

  public function add_penalty()
  {
    $penalty_name = $_POST['penalty_name'];
    $success = $this->noncompliance_model->add_penalty($penalty_name);
    echo json_encode($success);
  }

  public function edit_penalty()
  {
    $penalty_name = $_POST['penalty_name'];
    $penalty_id = $_POST['penalty_id'];
    $success = $this->noncompliance_model->edit_penalty($penalty_name, $penalty_id);
    echo json_encode($success);
  }

  public function get_penalty_types()
  {
    $data['penalty_types'] = $this->noncompliance_model->get_penalty_types();
    echo json_encode($data);
  }

  public function delete_penalty()
  {
    $penalty_id = $_POST['penalty_id'];
    $success = $this->noncompliance_model->delete_penalty($penalty_id);
    echo json_encode($success);
  }

  public function manage_nc()
  {
    $data['sectionList'] = $this->noncompliance_model->getClassNames();
    $data['category_types'] = $this->noncompliance_model->get_category_types();
    $data['capacity_types'] = $this->noncompliance_model->get_capacity_types();
    $data['staff_list'] = $this->noncompliance_model->getStaffList();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student_nc/manage_nc/tablet/index';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student_nc/manage_nc/mobile/index';
    } else {
      $data['main_content'] = 'student_nc/manage_nc/desktop/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_penaly_by_snc_category(){
    $penalty = $this->noncompliance_model->get_penaly_by_snc_category($_POST);
    echo json_encode($penalty);
  }
  public function for_a_particular_student($student_name,$class_name) {
    $decoded_student_name = urldecode($student_name);
    $decoded_class_name = urldecode($class_name);
    $data['result'] = $this->noncompliance_model->for_a_particular_student($decoded_student_name,$decoded_class_name);
    $data['main_content'] = 'student_nc/manage_nc/for_a_particular_student';
    $this->load->view('inc/template',$data);
  }

  public function get_single_staff_nc_data()
  {
    $class_section_id = $_POST['class_section_id'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $result= $this->noncompliance_model->get_single_staff_nc_data($class_section_id, $from_date, $to_date);
    echo json_encode($result);
  }

  public function get_student()
  {
    $section_id = $this->input->post('section_id');
    $stdName = $this->noncompliance_model->get_studentclassSectionwise($section_id);
    echo json_encode(array('stdname' => $stdName));
  }
  private function s3FileUpload($file, $folder_name = 'Student_non_compliance')
  { 
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }
  public function add_multiple_student_nc()
  { if ($_FILES != null) {
        $file = $this->s3FileUpload($_FILES['file']);
         
    } else {
      $file= null;
    }
    $class_section_id = $_POST['class_section_id'];
    $student_id_arr = explode(',',$_POST['student_id']);
    
    $snc_category_id = $_POST['snc_category_id'];
    $staff_capacity_id = $_POST['staff_capacity_id'];
    $remarks = $_POST['remarks'];
    $snc_date = date('Y-m-d', strtotime($_POST['date']));

    $is_apply_penalty_checked = $_POST['is_apply_penalty_checked'];
    $penalty = $_POST['penalty'];
    $penalty_observer = $_POST['penalty_observer'];

    $success = $this->noncompliance_model->add_multiple_student_nc($class_section_id, $student_id_arr, $snc_category_id, $staff_capacity_id, $remarks, $snc_date,$file, $is_apply_penalty_checked, $penalty, $penalty_observer);

    if ($success) {
      $this->session->set_flashdata('flashSuccess', 'Non-Compliance added Successfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
    }
    echo json_encode($success);
  }

  public function section_nc_summary()
  {
    $data['sectionList'] = $this->noncompliance_model->getClassNames();


    $data['main_content'] = 'student_nc/sectionWise_nc_report';
    $this->load->view('inc/template', $data);
  }

  public function student_nc_report()
  {
    $data['sectionList'] = $this->noncompliance_model->getClassNames();

    // echo '<pre>'; print_r($data['sectionList']); die();

    $data['main_content'] = 'student_nc/student_nc_report';
    $this->load->view('inc/template', $data);
  }

  public function get_nc_analytics()
  {
    $result = $this->noncompliance_model->get_nc_analytics($_POST);
    echo json_encode($result);
  }

  public function get_particular_nc_analytics()
  {
    $result = $this->noncompliance_model->get_particular_nc_analytics($_POST);
    echo json_encode($result);
  }

  public function get_penalty_data_for_widget()
  {
    $result = $this->noncompliance_model->get_penalties_for_widget();
    echo json_encode($result);
  }

  public function get_observation_data_for_widget()
  {
    $from_date = $_POST['fromDate'];
    $to_date = $_POST['toDate'];
    $result = $this->noncompliance_model->get_observation_for_widget($from_date, $to_date);
    echo json_encode($result);
  }

  public function get_counselling_data_for_widget()
  {
    $from_date = $_POST['fromDate'];
    $to_date = $_POST['toDate'];
    $result = $this->noncompliance_model->get_counselling_for_widget($from_date, $to_date);
    echo json_encode($result);
  }

  public function update_combination_status(){
    echo $this->noncompliance_model->update_combination_status();
  }
}
