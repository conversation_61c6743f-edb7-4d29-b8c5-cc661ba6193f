<ul class="breadcrumb" id="parent_breadcums">
    <?php if(isset($callFrom) && $callFrom == ''){?>
        <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <?php } else {?>
        <li><a href="<?php echo site_url('admissionflowv2/index') ?>">Onboarding Page</a></li>
    <?php }?>
    <li><a href="<?php echo site_url('parent_controller/profile/'.$callFrom) ?>">Profile</a></li>
    <li>Edit Profile</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border" style="margin: 5px 0 0 10px; ">
            <div class="" style="margin: 0px; padding: 0 0 10px 0; border-bottom: 1px solid lightgrey;">
                <h3 class="panel-title card-title panel_title_new_style_staff">
                    <?php if(isset($callFrom) && $callFrom == ''){?>
                        <a class="back_anchor" href="<?php echo site_url('parent_controller/profile/'.$callFrom); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                    <?php }?>
                    Edit Profile
                </h3>
            </div>
        </div>

        <div class="col-md-12 form-horizontal" style="padding-top: 1.6rem;">
            <!-- Show instructions if provided -->
            <?php if (!empty($instructions)) { ?>
            <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
                <div class="row" style="margin-left: 10px;">
                    <div class="col-md-12">
                        <h4><strong> Instructions: </strong></h4>
                        <?php $i = 1;
            foreach ($instructions as $instruction) { ?>
                        <p><strong><?php echo $i++ ?>.</strong> <?php echo $instruction; ?></p>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php } ?>

            <!-- student -->
            <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">
                <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                    <div class="row d-flex" style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                        <h3 class="card-title">
                            <strong> Student </strong>
                            <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NAME')) : ?>
                            <?php echo ucfirst($studentData->stdName); ?>
                            <?php endif ?>
                        </h3>

                    </div>
                </div>
                <div class="card-body">
                    <div class="row" style="margin: 0px;">
                        <div class="col-md-12">

                            <div class="col-md-2">
                                <?php
             $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            $gender = 'Female';
            if ($studentData->gender == 'M') {
              $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
              $gender = 'Male';
            }
            ?>
                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_PHOTO')) : ?>

                                <?php 
                $showStudentError = 1;
                if (empty($student_id)) {
                  $showStudentError = 0;
                } ?>

                                <div class="text-center">
                                    <img onclick="$('#fileupload').click();"
                                        class="img-responsive img-circle <?php echo ($showStudentError == '0')? 'student_upload_disabled':'' ?>"
                                        id="student_photo" style="width:100px;height:100px"
                                        src="<?php echo (empty($studentData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->picture_url); ?>">
                                    <i onclick="$('#fileupload').click();" id="student_edit_pencil"
                                        class="fa fa-pencil <?php echo ($showStudentError == '0')? 'student_upload_disabled':'' ?>"></i>
                                    <input hidden="hidden" type="file" id="fileupload" class="file"
                                        data-preview-file-type="jpeg" name="student_photo" accept="image/*">
                                    <span class="help-block">Allowed file types: JPEG, JPG, and PNG; Allowed size: up to <?php if($this->settings->getSetting('student_parent_guardian_photo_size')){ echo $this->settings->getSetting('student_parent_guardian_photo_size')/1000; echo "kb"; }else{
                    echo "10mb"  ;
                }?> </span>
                                    <span style="color: red;" id="s_message"></span>
                                    <span id="fileuploadError"
                                        style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                                    <span id="student_error"
                                        style="display: <?php echo ($showStudentError == '0')?'block':'none' ?>;padding-top:5px;padding-bottom:5px;font-weight: 600; color: red;">
                                        Save name before saving photo
                                    </span>
                                    <br>
                                    <span id="percentage_completed_student"
                                        style="font-size: 20px; display: none; position: absolute;top: 65px;left: 0;right: 0;">0
                                        %</span>

                                    <!--   <div class="text-center" style="width: 100%;">
                  <div class="progress" style="height: 20px;">
                    <div class="progress-bar" id="single-file-percentage" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  
                </div> -->

                                    <button id="photo_student" type="button"
                                        onclick="save_profile_photos('student', <?php echo $student_id; ?>)"
                                        style="display: none;margin: auto;width:100px;"
                                        class="btn photo_btn">Upload</button>
                                </div>
                                <?php endif ?>
                                <!-- <div class="form-group" id="profile_id">
                <div id="dvPreview">
                <img class="img-responsive" id="student_photo"  width="100px;" height="100px;" src="<?php //echo (empty($studentData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->picture_url);
                                                                                                    ?>">
                </div>
                  <span id="fileuploadError" style="color:red;"></span>
                <div class="input-group" style="margin-top: 10px">
                  <div class="btn btn-secondary btn-file">
                    <i class="glyphicon glyphicon-upload"></i> Upload<input type="file" id="fileupload" class="file" data-preview-file-type="jpeg" name="student_photo" accept="image/jpeg,image/png">
                  </div>
                </div>
              </div> -->

                            </div>
                            <div class="col-md-10">
                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_NAME')) { ?>
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="student_dob"><strong>Name
                                        </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="s_first_name"
                                                id="s_first_name" placeholder="First name"
                                                value="<?php echo $studentData->first_name; ?>" required="">
                                            <input type="hidden" id="old_s_first_name" name="old_s_first_name"
                                                value="<?php echo $studentData->first_name; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_first_name"
                                                    data-name="first_name" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="s_last_name" id="s_last_name"
                                                placeholder="Last name" value="<?php echo $studentData->last_name; ?>">
                                            <input type="hidden" id="old_s_last_name" name="old_s_last_name"
                                                value="<?php echo $studentData->last_name; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_last_name"
                                                    data-name="last_name" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php } ?>

                                <!--  <div class="form-group" id="profile_id">
              <label class="col-md-2 control-label" for="adm"><strong>Admission No </strong></label>
              <div class="col-md-5">
                <h5 class="form-control-static"><?php //echo $studentData->admissionNo;
                                                ?></h5>
              </div>
            </div>

            <div class="form-group" id="profile_id">
              <label class="col-md-2 control-label" for="cs"><strong>Class / Section </strong></label>
              <div class="col-md-5">
                <h5 class="form-control-static"><?php //echo $studentData->className ;
                                                ?><?php //echo $studentData->sectionName ;
                                                                                        ?></h5>
              </div>
            </div> -->

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_DOB')) { ?>
                                <div class="form-group">
                                    <label class="col-md-2 control-label"> <strong>Date of Birth </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group date" id="dob_dtpicker">
                                            <span class="input-group-text">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                            <input type="text" class="form-control" id="student_dob" <?php
                                                                              $dob = $studentData->dob;
                                                                              if ($dob != null)
                                                                                echo 'value="' . date('d-m-Y', strtotime($dob)) . '"';
                                                                              ?> name="student_dob"
                                                placeholder="Enter Date of Brith">
                                            <input type="hidden" id="old_student_dob" name="old_student_dob"
                                                value="<?php echo $studentData->dob; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="student_dob"
                                                    data-name="dob" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php } ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_EMAIL')) : ?>
                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Email </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="email" name="s_email" class="form-control" id="s_email"
                                                value="<?php echo $studentData->student_email  ?>">
                                            <input type="hidden" id="old_s_email" name="old_s_email"
                                                value="<?php echo $studentData->student_email ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_email"
                                                    data-name="email" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>
                                <?php //if ($this->settings->isProfile_edit_enabled('STUDENT_MOTHER_TONGUE')) { ?>
                                <!-- <div class="form-group">
                <label class="col-md-2 control-label" for="student_dob"><strong>Mother Tongue </strong></label>
                <div class="col-md-5">
                  <div class="input-group">
                    <input type="text" class="form-control" name="s_mother_tongue" id="s_mother_tongue" placeholder="Mother Tongue" value="<?php //echo $studentData->mother_tongue; ?>" required="">
                    <div class="input-group-append">
                      <button class="input-group-text save_btn" data-id="s_mother_tongue" data-name="mother_tongue" data-type="student">Save</button>
                    </div>
                  </div>
                </div>
              </div> -->
                                <?php //} ?>


                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_GENDER')) { ?>
                                <div class="form-group" id="profile_id">
                                    <label class="col-md-2 control-label" for="g"><strong>Gender </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <select class="form-control" name="gender" id="student_gender">
                                                <option value="">Select Gender</option>
                                                <option value="M"
                                                    <?php if ($studentData->gender == 'M') echo 'selected' ?>>Male
                                                </option>
                                                <option value="F"
                                                    <?php if ($studentData->gender == 'F') echo 'selected' ?>>Female
                                                </option>
                                            </select>
                                            <input type="hidden" id="old_student_gender" name="old_student_gender"
                                                value="<?php if ($studentData->gender == 'M') {echo 'Male';} else{echo 'Female';} ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="student_gender"
                                                    data-name="gender" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php } ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_MOTHER_TONGUE')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Mother Tongue </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="text" name="s_mother_tongue" placeholder="Enter Mother Tongue"
                                                class="form-control" id="s_mother_tongue"
                                                value="<?php echo $studentData->mother_tongue  ?>">
                                            <input type="hidden" id="old_s_mother_tongue" name="old_s_mother_tongue"
                                                value="<?php echo $studentData->mother_tongue ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_mother_tongue"
                                                    data-name="mother_tongue" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_BLOOD_GROUP')) { ?>
                                <div class="form-group" id="profile_id">
                                    <label class="col-md-2 control-label"><strong>Blood Group </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <select class="form-control" name="blood_group" id="blood_group">
                                                <option value="">Select Blood Group</option>
                                                <option value="A +ve"
                                                    <?php echo ($studentData->blood_group == 'A +ve') ? 'selected' : '' ?>>
                                                    A +ve</option>
                                                <option value="B +ve"
                                                    <?php echo ($studentData->blood_group == 'B +ve') ? 'selected' : '' ?>>
                                                    B +ve</option>
                                                <option value="AB +ve"
                                                    <?php echo ($studentData->blood_group == 'AB +ve') ? 'selected' : '' ?>>
                                                    AB +ve</option>
                                                <option value="O +ve"
                                                    <?php echo ($studentData->blood_group == 'O +ve') ? 'selected' : '' ?>>
                                                    O +ve</option>
                                                <option value="A -ve"
                                                    <?php echo ($studentData->blood_group == 'A -ve') ? 'selected' : '' ?>>
                                                    A -ve</option>
                                                <option value="B -ve"
                                                    <?php echo ($studentData->blood_group == 'B -ve') ? 'selected' : '' ?>>
                                                    B -ve</option>
                                                <option value="AB -ve"
                                                    <?php echo ($studentData->blood_group == 'AB -ve') ? 'selected' : '' ?>>
                                                    AB -ve</option>
                                                <option value="O -ve"
                                                    <?php echo ($studentData->blood_group == 'O -ve') ? 'selected' : '' ?>>
                                                    O -ve</option>
                                            </select>
                                            <input type="hidden" id="old_blood_group" name="old_blood_group"
                                                value="<?php echo $studentData->blood_group ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="blood_group"
                                                    data-name="blood_group" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php } ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_STOP')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Stop </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <?php
                    $array = array();
                    $array[0] =  'Select Stop';
                    foreach ($stops as $key => $stop) {
                      $array[$stop->id] = $stop->name;
                    }
                    echo form_dropdown("stop", $array, set_value("stop", $studentData->stop), "id='stop' class='form-control'");
                    ?>

                                            <input type="hidden" id="old_stop" name="old_stop"
                                                value="<?php echo $studentData->stop ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="stop"
                                                    data-name="stop" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_PICKUP_MODE')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Pickup Mode </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <?php
                    $array = array();
                    $array[''] =  'Select Mode';
                    $array['PICKING'] = 'PICKING';
                    $array['DROPPING'] = 'DROPPING';
                    $array['BOTH'] = 'BOTH';
                    echo form_dropdown("pickup_mode", $array, set_value("pickup_mode", $studentData->pickup_mode), "id='pickup_mode' class='form-control'");
                    ?>
                                            <input type="hidden" id="old_pickup_mode" name="old_pickup_mode"
                                                value="<?php echo $studentData->pickup_mode ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="pickup_mode"
                                                    data-name="pickup_mode" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_NATIONALITY')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Nationality </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <?php
                    $array = array();
                    $array[0] =  'Select Nationality';
                    foreach ($this->config->item('nationality') as $key => $nationality) {
                      $array[$nationality] = $nationality;
                    }
                    echo form_dropdown("nationality", $array, set_value("nationality", $studentData->nationality), "id='nationality' class='form-control'");
                    ?>
                                            <input type="hidden" id="old_nationality" name="old_nationality"
                                                value="<?php echo $studentData->nationality ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="nationality"
                                                    data-name="nationality" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('CATEGORY')) : ?>
                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Category </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                           
                                            <?php 
                                            $array = array();
                                            $array['-'] =  'Select Category';
                                            foreach ($this->settings->getSetting('category') as $key => $category) {
                                            $array[$key] = $category;
                                            }
                                            echo form_dropdown("category", $array, set_value("category",$studentData->category), " id='s_category' name='s_category' class='form-control'");
                                            ?>
                                            <input type="hidden" id="old_s_category" name="old_s_category"
                                                value="<?php echo $studentData->category ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_category"
                                                    data-name="category" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_CASTE')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Caste </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="text" name="s_caste" class="form-control" id="s_caste"
                                                value="<?php echo $studentData->caste  ?>">
                                            <input type="hidden" id="old_s_caste" name="old_s_caste"
                                                value="<?php echo $studentData->caste ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_caste"
                                                    data-name="caste" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_RELIGION')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Religion </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <!-- <input type="text" name="s_religion" class="form-control" id="s_religion" value="<?php echo $studentData->religion  ?>"> -->
                                            <?php 
                        $array = array();
                        $array['-'] =  'Select Religion';
                        foreach ($this->config->item('religions') as $key => $religion) {
                            $array[$religion] = $religion;
                        }
                        echo form_dropdown("religion", $array, set_value("religion",$studentData->religion), " id='s_religion' name='s_religion' class='form-control'");
                        ?>
                                            <input type="hidden" id="old_s_religion" name="old_s_religion"
                                                value="<?php echo $studentData->religion ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_religion"
                                                    data-name="religion" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_MOBILE_NUMBER')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Student Mobile Number </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input name="s_student_mobile_no" placeholder="Enter Student Mobile Number"
                                                class="form-control" id="s_student_mobile_no"
                                                value="<?php echo $studentData->student_mobile_no  ?>">
                                            <input type="hidden" id="old_s_student_mobile_no" name="old_s_student_mobile_no"
                                                value="<?php echo $studentData->student_mobile_no ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_student_mobile_no"
                                                    data-name="student_mobile_no" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('PREFFERED_CONTACT_NUMBER')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Preffered Contact Number </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input name="preferred_contact_no" placeholder="Enter Preffered Contct Number"
                                                class="form-control" id="preferred_contact_no"
                                                value="<?php echo $studentData->preferred_contact_no  ?>">
                                            <input type="hidden" id="old_preferred_contact_no" name="old_preferred_contact_no"
                                                value="<?php echo $studentData->preferred_contact_no ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="preferred_contact_no"
                                                    data-name="preferred_contact_no" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_AADHAR')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Aadhar No. </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                            <input type="number" name="s_aadhar_no" placeholder="Enter Aadhar Number"
                                                class="form-control" id="s_aadhar_no"
                                                value="<?php echo $studentData->aadhar_no  ?>" maxlength="12"
                                                pattern="\d{12}">
                                            <input type="hidden" id="old_s_aadhar_no" name="old_s_aadhar_no"
                                                value="<?php echo $studentData->aadhar_no ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_aadhar_no"
                                                    data-name="aadhar_no" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('NAME_AS_PER_AADHAR')) : ?>

                                <div class="form-group">
                                    <label class="col-md-2 control-label"><strong>Name As Per Aadhar Number </strong></label>
                                    <div class="col-md-5">
                                        <div class="input-group">
                                        <input type="text" name="s_name_as_per_aadhar" placeholder="Enter Name As per Aadhar"
                                        class="form-control" id="s_name_as_per_aadhar"
                                        value="<?php echo $studentData->name_as_per_aadhar  ?>" 
                                        pattern="[A-Za-z\s]+" 
                                        title="Only letters and spaces are allowed">
                                            <input type="hidden" id="old_s_name_as_per_aadhar" name="old_s_name_as_per_aadhar"
                                                value="<?php echo $studentData->name_as_per_aadhar ; ?>">
                                            <div class="input-group-append">
                                                <button class="input-group-text save_btn" data-id="s_name_as_per_aadhar"
                                                    data-name="name_as_per_aadhar" data-type="student">Save</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php endif ?>

                                <?php if ($this->settings->isProfile_edit_enabled('STUDENT_ADDRESS')) : ?>
                                <div class="form-group">
                                    <?php $add_type = 0;
                foreach ($studentAddress as $add => $s_ad) {

                  echo '<div><label class="control-label col-md-2"><strong>' . $add . '</strong></label></div>';
                  echo '<div class="col-md-5" style="padding:0px;">';
                  if (!empty($s_ad)) { ?>
                                    <div class="form-group row">
                                        <div class="col-md-4">
                                            <input type="text" placeholder="No"
                                                value="<?php echo $s_ad->Address_line1  ?>"
                                                class="form-control input-md" name="s_line1[<?php echo $s_ad->addId ?>]"
                                                id="line1_<?php echo $s_ad->addId ?>">
                                                <input type="hidden" id="old_line1_<?php echo $s_ad->addId ?>" name="old_s_line1[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->Address_line1  ?>">
                                        </div>
                                        <div class="col-md-8">
                                            <input type="text" placeholder="Street"
                                                value="<?php echo $s_ad->Address_line2  ?>"
                                                class="form-control input-md" name="s_line2[<?php echo $s_ad->addId ?>]"
                                                id="line2_<?php echo $s_ad->addId ?>">
                                                <input type="hidden" id="old_line2_<?php echo $s_ad->addId ?>" name="old_s_line2[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->Address_line2  ?>">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-6">
                                            <input type="text" placeholder="Area" value="<?php echo $s_ad->area  ?>"
                                                class="form-control input-md" name="s_area[<?php echo $s_ad->addId ?>]"
                                                id="area_<?php echo $s_ad->addId ?>">
                                                <input type="hidden" id="old_area_<?php echo $s_ad->addId ?>" name="old_s_area[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->area  ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" placeholder="District"
                                                value="<?php echo $s_ad->district  ?>" class="form-control input-md"
                                                name="s_district[<?php echo $s_ad->addId ?>]"
                                                id="district_<?php echo $s_ad->addId ?>">
                                                <input type="hidden" id="old_district_<?php echo $s_ad->addId ?>" name="old_s_district[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->district  ?>">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-4">
                                            <input type="text" placeholder="State" value="<?php echo $s_ad->state  ?>"
                                                class="form-control input-md" name="s_state[<?php echo $s_ad->addId ?>]"
                                                id="state_<?php echo $s_ad->addId ?>">
                                                <input type="hidden" id="old_state_<?php echo $s_ad->addId ?>" name="old_s_state[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->state  ?>">
                                        </div>
                                        <div class="col-md-4">
                                            <select id="country_<?php echo $s_ad->addId ?>"
                                                name="s_country[<?php echo $s_ad->addId ?>]"
                                                class="form-control input-md">
                                                <option value="">Select Country</option>;
                                                <?php foreach ($this->config->item('country') as $nation) {
                            if ($s_ad->country == $nation)
                              echo '<option selected>' . $nation . '</option>';
                            else
                              echo '<option>' . $nation . '</option>';
                          } ?>
                                            </select>
                                            <input type="hidden" id="old_country_<?php echo $s_ad->addId ?>" name="old_s_country[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->country  ?>">
                                        </div>
                                        <div class="col-md-4">
                                            <input id="pin_code_<?php echo $s_ad->addId ?>" placeholder="Pin Code"
                                                value="<?php echo $s_ad->pin_code  ?>"
                                                name="s_pincode[<?php echo $s_ad->addId ?>]" type="text"
                                                class="form-control input-md" data-parsley-type="digits"
                                                data-parsley-length="[5, 8]"
                                                data-parsley-error-message="Enter a valid pin-code, only digits">
                                                <input type="hidden" id="old_pin_code_<?php echo $s_ad->addId ?>" name="old_s_pincode[<?php echo $s_ad->addId ?>]" value="<?php echo $s_ad->pin_code  ?>">
                                        </div>
                                    </div>
                                    <button type="button" id="add_student_<?php echo $s_ad->addId ?>"
                                        class="btn address-btn" data-update="1" data-id="<?php echo $s_ad->addId ?>"
                                        data-type="student" data-add_type="<?= $add ?>">Save</button>
                                    <?php
                  } else { ?>
                                    <div class="form-group row">
                                        <div class="col-md-4">
                                            <input type="text" placeholder="No" class="form-control input-md" value=""
                                                name="s_line1[]" id="line1_<?php echo $add_type ?>">
                                                <input type="hidden" id="old_s_line1_<?php echo $add_type ?>" name="old_line1[]" value="">
                                        </div>
                                        <div class="col-md-8">
                                            <input type="text" placeholder="Street" class="form-control input-md"
                                                value="" name="s_line2[]" id="line2_<?php echo $add_type ?>">
                                                <input type="hidden" id="old_s_line2_<?php echo $add_type ?>" name="old_line2[]" value="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-6">
                                            <input type="text" placeholder="Area" class="form-control input-md" value=""
                                                name="s_area[]" id="area_<?php echo $add_type ?>">
                                                <input type="hidden" id="old_area_<?php echo $add_type ?>" name="old_area[]" value="">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" placeholder="District" class="form-control input-md"
                                                value="" name="s_district[]" id="district_<?php echo $add_type ?>">
                                                <input type="hidden" id="old_district_<?php echo $add_type ?>" name="old_district[]" value="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-4">
                                            <input type="text" placeholder="State" class="form-control input-md"
                                                value="" name="s_state[]" id="state_<?php echo $add_type ?>">
                                                <input type="hidden" id="old_state_<?php echo $add_type ?>" name="old_state[]" value="">
                                        </div>
                                        <div class="col-md-4">
                                            <select id="country_<?php echo $add_type ?>" name="s_country[]"
                                                class="form-control input-md">
                                                <option value="">Select Country</option>;
                                                <?php foreach ($this->config->item('country') as $nation) {
                            echo '<option>' . $nation . '</option>';
                          } ?>
                                            </select>
                                            <input type="hidden" id="old_country_<?php echo $add_type ?>" name="old_country[]" value="">
                                        </div>
                                        <div class="col-md-4">
                                            <input id="pin_code_<?php echo $add_type ?>" placeholder="Pin Code"
                                                name="s_pincode[]" type="text" value="" class="form-control input-md"
                                                data-parsley-type="digits" data-parsley-length="[5, 8]"
                                                data-parsley-error-message="Enter a valid pin-code, only digits">
                                                <input type="hidden" id="old_pin_code_<?php echo $add_type ?>" name="old_pincode[]" value="">
                                        </div>
                                    </div>
                                    <button style="margin-left:10px;width:100px;" type="button"
                                        id="add_student_<?php echo $add_type ?>" class="btn address-btn" data-update="0"
                                        data-id="<?php echo $add_type ?>" data-type="student" data-add_type="<?= $add ?>">Save</button>

                                    <?php  }
                  echo '</div>';
                  $add_type++;
                } ?>
                                </div>;
                                <?php endif ?>


                            </div>

                        </div>
                    </div>
                </div>
                <!-- student -->

                <!-- father -->
                <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

                    <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                        <div class="row d-flex"
                            style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                            <h3 class="card-title">
                                <strong> Father </strong>
                                <?php if ($this->settings->isProfile_profile_enabled('FATHER_NAME')) : ?>
                                <?php echo ucfirst($fatherData->name); ?>
                                <?php endif ?>
                            </h3>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="row" style="margin: 0px;">
                            <div class="col-md-12">
                                <div class="col-md-2">
                                    <?php
              $picUrl = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/father.png';
              $gender = 'Female';
              if ($studentData->gender == 'M') {
                $picUrl = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/father.png';
                $gender = 'Male';
              }
              ?>

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_PHOTO')) : ?>
                                    <?php 
                $showFatherError = 1;
                if (empty($fatherData->id)) {
                  $showFatherError = 0;
                } ?>

                                    <div class="text-center">
                                        <img onclick="$('#fUpload').click();"
                                            class="img-responsive img-circle <?php echo ($showFatherError == '0')? 'father_upload_disabled':'' ?>"
                                            id="father_photo" style="width:100px;height:100px"
                                            src="<?php echo (empty($fatherData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($fatherData->picture_url); ?>">
                                        <i onclick="$('#fUpload').click();" id="father_edit_pencil"
                                            class="fa fa-pencil <?php echo ($showFatherError == '0')? 'father_upload_disabled':'' ?>"></i>
                                        <input hidden="hidden" type="file" id="fUpload" class="file"
                                            data-preview-file-type="jpeg" name="father_photo" accept="image/*">
                                        <span class="help-block">Allowed file types: JPEG, JPG, and PNG; Allowed size:
                                            up to <?php if($this->settings->getSetting('student_parent_guardian_photo_size')){ echo $this->settings->getSetting('student_parent_guardian_photo_size')/1000; echo "kb"; }else{
                    echo "10mb"  ;
                }?> </span>
                                        <span style="color: red;" id="f-message"></span>
                                        <span id="fileuploadError1"
                                            style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                                        <span id="father_error"
                                            style="display: <?php echo ($showFatherError == '0')?'block':'none' ?>;padding-top:5px;padding-bottom:5px;font-weight: 600; color: red;">
                                            Save name before saving photo
                                        </span>
                                        <span id="percentage_completed_father"
                                            style="font-size: 20px; display: none; position: absolute;top: 34px;left: 0;right: 0;">0
                                            %</span>
                                        <button id="photo_father" type="button"
                                            onclick="save_profile_photos('father', <?php echo $fatherData->id; ?>)"
                                            style="display: none;margin: auto;width:100px;"
                                            class="btn photo_btn">Upload</button>
                                    </div>

                                    <?php endif ?>
                                </div>

                                <div class="col-md-10">
                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_NAME')) { ?>
                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="f_first_name"> Name </label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="text" name="f_first_name" placeholder="First name"
                                                    required="" class="form-control" id="f_first_name"
                                                    value="<?php echo $fatherData->first_name  ?>">
                                                    <input type="hidden" id="old_f_first_name" name="old_f_first_name" value="<?php echo $fatherData->first_name  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_first_name"
                                                        data-name="first_name" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="text" name="f_last_name" placeholder="Last name"
                                                    class="form-control" id="f_last_name"
                                                    value="<?php echo $fatherData->last_name  ?>">
                                                    <input type="hidden" id="old_f_last_name" name="old_f_last_name" value="<?php echo $fatherData->last_name  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_last_name"
                                                        data-name="last_name" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <div class="form-group">

                                        <!-- <div class="col-md-5"> -->

                                        <!-- <div class="form-group row"> -->
                                        <?php if ($this->settings->isProfile_edit_enabled('FATHER_CONTACT_NO')) : ?>
                                        <label class="col-md-2 control-label"><strong>Mobile</strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="text" data-parsley-pattern="^[0-9 -()+]+$"
                                                    data-parsley-length="[8, 20]"
                                                    data-parsley-error-message="Enter Valid Contact Number"
                                                    pattern="\d{10}" name="f_mobile_no" placeholder="Mobile No."
                                                    class="form-control" id="f_mobile_no"
                                                    value="<?php echo $fatherData->mobile_no  ?>">
                                                    <input type="hidden" id="old_f_mobile_no" name="old_f_mobile_no" value="<?php echo $fatherData->mobile_no  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_mobile_no"
                                                        data-name="mobile_no" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>
                                    </div>
                                    <div class="form-group">
                                        <?php if ($this->settings->isProfile_edit_enabled('FATHER_EMAIL')) : ?>
                                        <label class="col-md-2 control-label"><strong>Email</strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="email" name="f_email" placeholder="Email Id"
                                                    class="form-control" id="f_email"
                                                    value="<?php echo $fatherData->fatherEmail  ?>">
                                                    <input type="hidden" id="old_f_email" name="old_f_email" value="<?php echo $fatherData->fatherEmail  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_email"
                                                        data-name="email" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>
                                    </div>
                                    <!-- </div> -->

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_QUALIFICATION')) : ?>

                                    <div class="form-group">
                                        <label class="col-md-2 control-label"
                                            for="m_qualification"><strong>Qualification</strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input id="f_qualification" placeholder="Enter Qualification"
                                                    value="<?php echo $fatherData->qualification ?>"
                                                    name="f_qualification" type="text" class="form-control input-md">
                                                    <input type="hidden" id="old_f_qualification" name="old_f_qualification" value="<?php echo $fatherData->qualification ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_qualification"
                                                        data-name="qualification" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif ?>

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_OCCUPATION')) : ?>

                                    <div class="form-group">
                                        <label class="col-md-2 control-label"><strong>Occupation </strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="text" name="f_occupation" class="form-control"
                                                    id="f_occupation" value="<?php echo $fatherData->occupation  ?>">
                                                    <input type="hidden" id="old_f_occupation" name="old_f_occupation" value="<?php echo $fatherData->occupation  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_occupation"
                                                        data-name="occupation" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif ?>

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_ANNUAL_INCOME')) : ?>

                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="f_annual_income"><strong>Annual
                                                Income</strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <?php if(!empty($this->settings->getSetting('parent_annual_income_option'))){ ?>
                                                <select name="f_annual_income" class="form-control" id="f_annual_income">
                                                    <option value="">Select Annual Income</option>
                                                    <?php $income_options = json_decode($this->settings->getSetting('parent_annual_income_option')) ;
                              foreach($income_options as $key => $val){ 
                                  $selected = ''; 
                                  if($fatherData->annual_income == $val) {
                                      $selected = 'selected';
                                  } ?>
                                                    <option value="<?= $val ?>" <?= $selected; ?>><?= $val ?></option>
                                                    <?php }
                          ?>
                                                </select>
                                                <?php } else{ ?>
                                                <input id="f_annual_income"
                                                    data-parsley-error-message="Enter valid currency value"
                                                    data-parsley-pattern="^[0-9]\d*(\.\d+)?$" placeholder="Enter Income"
                                                    value="<?php echo $fatherData->annual_income ?>"
                                                    name="f_annual_income" type="number" class="form-control input-md">
                                                <?php } ?>
                                                <input type="hidden" id="old_f_annual_income" name="old_f_annual_income" value="<?php echo $fatherData->annual_income  ?>">

                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_annual_income"
                                                        data-name="annual_income" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif ?>

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_COMPANY')) :
              ?>

                                    <div class="form-group">
                                        <label class="col-md-2 control-label"><strong>Company</strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="text" name="f_company" class="form-control" id="f_company"
                                                    value="<?php echo $fatherData->company  ?>">
                                                    <input type="hidden" id="old_f_company" name="old_f_company" value="<?php echo $fatherData->company  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_company"
                                                        data-name="company" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php endif ?>
                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_AADHAR')) : ?>

                                    <div class="form-group">
                                        <label class="col-md-2 control-label"><strong>Aadhar No. </strong></label>
                                        <div class="col-md-5">
                                            <div class="input-group mb-3">
                                                <input type="number" name="f_aadhar_no"
                                                    placeholder="Enter Aadhar Number" class="form-control"
                                                    id="f_aadhar_no" value="<?php echo $fatherData->aadhar_no  ?>"
                                                    maxlength="12" pattern="\d{12}">
                                                    <input type="hidden" id="old_f_aadhar_no" name="old_f_aadhar_no" value="<?php echo $fatherData->aadhar_no  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn" data-id="f_aadhar_no"
                                                        data-name="aadhar_no" data-type="father">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php endif ?>

                                    <?php if ($this->settings->isProfile_edit_enabled('FATHER_ADDRESS')) : ?>
                                    <div class="form-group">
                                        <label class="col-md-2 control-label"></label>
                                        <?php $add_type = 0;
                  foreach ($fatherAddress as $add => $f_ad) {
                    echo '<div class="col-md-5" style="padding: 0px;">';
                    echo '<div style="margin-left:10px;"><label class="control-label"><strong> ' . $add . '</strong></label></div>';
                    if (!empty($f_ad)) { ?>
                                        <div class="form-group row">
                                            <div class="col-md-4">
                                                <input type="text" placeholder="No"
                                                    value="<?php echo $f_ad->Address_line1  ?>"
                                                    class="form-control input-md"
                                                    name="f_line1[<?php echo $f_ad->addId ?>]"
                                                    id="line1_<?php echo $f_ad->addId ?>">
                                                    <input type="hidden" id="old_line1_<?php echo $f_ad->addId ?>" name="old_f_line1[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->Address_line1  ?>">
                                            </div>
                                            <div class="col-md-8">
                                                <input type="text" placeholder="Street"
                                                    value="<?php echo $f_ad->Address_line2  ?>"
                                                    class="form-control input-md"
                                                    name="f_line2[<?php echo $f_ad->addId ?>]"
                                                    id="line2_<?php echo $f_ad->addId ?>">
                                                    <input type="hidden" id="old_f_line2_<?php echo $f_ad->addId ?>" name="old_line2[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->Address_line2  ?>">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-6">
                                                <input type="text" placeholder="Area" value="<?php echo $f_ad->area  ?>"
                                                    class="form-control input-md"
                                                    name="f_area[<?php echo $f_ad->addId ?>]"
                                                    id="area_<?php echo $f_ad->addId ?>">
                                                    <input type="hidden" id="old_area_<?php echo $f_ad->addId ?>" name="old_f_area[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->area  ?>">
                                            </div>
                                            <div class="col-md-6">
                                                <input type="text" placeholder="District"
                                                    value="<?php echo $f_ad->district  ?>" class="form-control input-md"
                                                    name="f_district[<?php echo $f_ad->addId ?>]"
                                                    id="district_<?php echo $f_ad->addId ?>">
                                                    <input type="hidden" id="old_district_<?php echo $f_ad->addId ?>" name="old_f_district[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->district  ?>">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-4">
                                                <input type="text" placeholder="State"
                                                    value="<?php echo $f_ad->state  ?>" class="form-control input-md"
                                                    name="f_state[<?php echo $f_ad->addId ?>]"
                                                    id="state_<?php echo $f_ad->addId ?>">
                                                    <input type="hidden" id="old_state_<?php echo $f_ad->addId ?>" name="old_f_state[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->state  ?>">
                                            </div>
                                            <div class="col-md-4">
                                                <select id="country_<?php echo $f_ad->addId ?>"
                                                    name="f_country[<?php echo $f_ad->addId ?>]"
                                                    class="form-control input-md">
                                                    <option value="">Select Country</option>;
                                                    <?php foreach ($this->config->item('country') as $nation) {
                              if ($f_ad->country == $nation)
                                echo '<option selected>' . $nation . '</option>';
                              else
                                echo '<option>' . $nation . '</option>';
                            } ?>
                                                </select>
                                                <input type="hidden" id="old_country_<?php echo $f_ad->addId ?>" name="old_f_country[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->country  ?>">
                                            </div>
                                            <div class="col-md-4">
                                                <input id="pin_code_<?php echo $f_ad->addId ?>" placeholder="Pin Code"
                                                    value="<?php echo $f_ad->pin_code  ?>"
                                                    name="f_pincode[<?php echo $f_ad->addId ?>]" type="text"
                                                    class="form-control input-md" data-parsley-type="digits"
                                                    data-parsley-length="[5, 8]"
                                                    data-parsley-error-message="Enter a valid pin-code, only digits">
                                                    <input type="hidden" id="old_pin_code_<?php echo $f_ad->addId ?>" name="old_f_pincode[<?php echo $f_ad->addId ?>]" value="<?php echo $f_ad->pin_code  ?>">
                                            </div>
                                        </div>
                                        <button style="margin-left: 10px;width: 100px;" type="button"
                                            id="add_father_<?php echo $f_ad->addId ?>" class="btn address-btn"
                                            data-update="1" data-id="<?php echo $f_ad->addId ?>"
                                            data-type="father" data-add_type="<?= $add ?>">Save</button>
                                        <?php
                    } else { ?>
                                        <div class="form-group row">
                                            <div class="col-md-4">
                                                <input type="text" placeholder="No" class="form-control input-md"
                                                    value="" name="f_line1[<?php echo $add_type ?>]"
                                                    id="line1_<?php echo $add_type ?>">
                                                    <input type="hidden" id="old_line1_<?php echo $add_type ?>" name="old_f_line1[<?php echo $add_type ?>]" value="">
                                            </div>
                                            <div class="col-md-8">
                                                <input type="text" placeholder="Street" class="form-control input-md"
                                                    value="" name="f_line2[<?php echo $add_type ?>]"
                                                    id="line2_<?php echo $add_type ?>">
                                                    <input type="hidden" id="old_line2_<?php echo $add_type ?>" name="old_f_line2[<?php echo $add_type ?>]" value="">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-6">
                                                <input type="text" placeholder="Area" class="form-control input-md"
                                                    value="" name="f_area[<?php echo $add_type ?>]"
                                                    id="area_<?php echo $add_type ?>">
                                                    <input type="hidden" id="old_area_<?php echo $add_type ?>" name="old_area[<?php echo $add_type ?>]" value="">
                                            </div>
                                            <div class="col-md-6">
                                                <input type="text" placeholder="District" class="form-control input-md"
                                                    value="" name="f_district[<?php echo $add_type ?>]"
                                                    id="district_<?php echo $add_type ?>">
                                                    <input type="hidden" id="old_district_<?php echo $add_type ?>" name="old_f_district[<?php echo $add_type ?>]" value="">
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-md-4">
                                                <input type="text" placeholder="State" class="form-control input-md"
                                                    value="" name="f_state[<?php echo $add_type ?>]"
                                                    id="state_<?php echo $add_type ?>">
                                                    <input type="hidden" id="old_state_<?php echo $add_type ?>" name="old_f_state[<?php echo $add_type ?>]" value="">
                                            </div>
                                            <div class="col-md-4">
                                                <select id="country_<?php echo $add_type ?>"
                                                    name="f_country[<?php echo $add_type ?>]"
                                                    class="form-control input-md">
                                                    <option value="">Select Country</option>;
                                                    <?php foreach ($this->config->item('country') as $nation) {
                              echo '<option>' . $nation . '</option>';
                            } ?>
                                                </select>
                                                <input type="hidden" id="old_f_country_<?php echo $add_type ?>" name="old_country[<?php echo $add_type ?>]" value="">
                                            </div>
                                            <div class="col-md-4">
                                                <input id="pin_code_<?php echo $add_type ?>" placeholder="Pin Code"
                                                    name="f_pincode[<?php echo $add_type ?>]" type="text" value=""
                                                    class="form-control input-md" data-parsley-type="digits"
                                                    data-parsley-length="[5, 8]"
                                                    data-parsley-error-message="Enter a valid pin-code, only digits">
                                                    <input type="hidden" id="old_pin_code_<?php echo $add_type ?>" name="old_pincode[<?php echo $add_type ?>]" value="">
                                            </div>
                                        </div>
                                        <button style="margin-left: 10px;width: 100px;" type="button"
                                            id="add_father_<?php echo $add_type ?>" class="btn address-btn"
                                            data-update="0" data-id="<?php echo $add_type ?>"
                                            data-type="father" data-add_type="<?= $add ?>">Save</button>
                                        <?php  }
                    echo "</div>";
                    $add_type++;
                  } ?>
                                    </div>

                                    <?php endif ?>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- father -->

                <!-- mother -->
                <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

                    <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                        <div class="row d-flex"
                            style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                            <h3 class="card-title">
                                <strong> Mother </strong>
                                <?php if ($this->settings->isProfile_profile_enabled('MOTHER_NAME')) : ?>
                                <?php echo ucfirst($motherData->name); ?>
                                <?php endif ?>
                            </h3>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="row" style="margin: 0px;">
                            <div class="col-md-12">
                                <div class="col-md-2">
                                    <?php
              $picUrl = $this->config->item('s3_base_url').'/nextelement-common/Staff and Admin icons 64px/mother.png';
              ?>
                                    <?php if ($this->settings->isProfile_edit_enabled('MOTHER_PHOTO')) : ?>
                                    <?php 
                $showMotherError = 1;
                if (empty($motherData->id)) {
                  $showMotherError = 0;
                } ?>
                                    <div class="text-center">
                                        <img onclick="$('#mUpload').click();"
                                            class="img-responsive img-circle <?php echo ($showMotherError == '0')? 'mother_upload_disabled':'' ?>"
                                            id="mother_photo" style="width:100px;height:100px"
                                            src="<?php echo (empty($motherData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($motherData->picture_url); ?>">
                                        <i onclick="$('#mUpload').click();" id="mother_edit_pencil"
                                            class="fa fa-pencil <?php echo ($showMotherError == '0')? 'mother_upload_disabled':'' ?>"></i>
                                        <input hidden="hidden" type="file" id="mUpload" class="file"
                                            data-preview-file-type="jpeg" name="mother_photo" accept="image/*">
                                        <span class="help-block">Allowed file types: JPEG, JPG, and PNG; Allowed size:
                                            up to <?php if($this->settings->getSetting('student_parent_guardian_photo_size')){ echo $this->settings->getSetting('student_parent_guardian_photo_size')/1000; echo "kb"; }else{
                    echo "10mb"  ;
                }?> </span>
                                        <span style="color: red;" id="m_message"></span>
                                        <span id="fileuploadError2"
                                            style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                                        <span id="mother_error"
                                            style="display: <?php echo ($showMotherError == '0')?'block':'none' ?>;padding-top:5px;padding-bottom:5px;font-weight: 600; color: red;">
                                            Save name before saving photo
                                        </span>
                                        <span id="percentage_completed_mother"
                                            style="font-size: 20px; display: none; position: absolute;top: 34px;left: 0;right: 0;">0
                                            %</span>
                                        <button id="photo_mother" type="button"
                                            onclick="save_profile_photos('mother', <?php echo $motherData->id; ?>)"
                                            style="display: none;margin: auto;width:100px;"
                                            class="btn photo_btn">Upload</button>
                                    </div>
                                    <?php endif ?>
                                </div>

                                <div class="col-md-10">
                                    <form class="form-horizontal">
                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_NAME')) { ?>
                                        <div class="form-group">
                                            <label class="col-md-2 control-label" for="student_dob"><strong> Name
                                                </strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="text" name="m_first_name" placeholder="First name"
                                                        required="" class="form-control" id="m_first_name"
                                                        value="<?php echo $motherData->first_name  ?>">
                                                        <input type="hidden" id="old_m_first_name" name="old_m_first_name" value="<?php echo $motherData->first_name  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_first_name"
                                                            data-name="first_name" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="text" name="m_last_name" placeholder="Last name"
                                                        class="form-control" id="m_last_name"
                                                        value="<?php echo $motherData->last_name  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_last_name"
                                                            data-name="last_name" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php } ?>

                                        <div class="form-group">

                                            <?php if ($this->settings->isProfile_edit_enabled('MOTHER_CONTACT_NO')) : ?>
                                            <label class="col-md-2 control-label"><strong>Mobile</strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="text" data-parsley-pattern="^[0-9 -()+]+$"
                                                        data-parsley-length="[8, 20]" pattern="\d{10}"
                                                        data-parsley-error-message="Enter Valid Contact Number"
                                                        name="m_mobile_no" placeholder="Mobile No." class="form-control"
                                                        id="m_mobile_no" value="<?php echo $motherData->mobile_no  ?>">
                                                        <input type="hidden" id="old_m_mobile_no" name="old_m_mobile_no" value="<?php echo $motherData->mobile_no  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_mobile_no"
                                                            data-name="mobile_no" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif ?>
                                        </div>
                                        <div class="form-group">

                                            <?php if ($this->settings->isProfile_edit_enabled('MOTHER_EMAIL')) : ?>
                                            <label class="col-md-2 control-label"><strong>Email</strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="email" name="m_email" placeholder="Email Id"
                                                        class="form-control" id="m_email"
                                                        value="<?php echo $motherData->fatherEmail  ?>">
                                                        <input type="hidden" id="old_m_email" name="old_m_email" value="<?php echo $motherData->fatherEmail  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_email"
                                                            data-name="email" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif ?>
                                        </div>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_QUALIFICATION')) : ?>

                                        <div class="form-group">
                                            <label class="col-md-2 control-label"
                                                for="m_qualification"><strong>Qualification</strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input id="m_qualification" placeholder="Enter Qualification"
                                                        value="<?= $motherData->qualification ?>" name="m_qualification"
                                                        type="text" class="form-control input-md">
                                                        <input type="hidden" id="old_m_qualification" name="old_m_qualification" value="<?php echo $motherData->qualification ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn"
                                                            data-id="m_qualification" data-name="qualification"
                                                            data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_OCCUPATION')) : ?>

                                        <div class="form-group">
                                            <label class="col-md-2 control-label"><strong>Occupation </strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="text" name="m_occupation" class="form-control"
                                                        id="m_occupation"
                                                        value="<?php echo $motherData->occupation  ?>">
                                                        <input type="hidden" id="old_m_occupation" name="old_m_occupation" value="<?php echo $motherData->occupation  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_occupation"
                                                            data-name="occupation" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_ANNUAL_INCOME')) : ?>

                                        <div class="form-group">
                                            <label class="col-md-2 control-label" for="m_annual_income"><strong>Annual
                                                    Income</strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <?php if(!empty($this->settings->getSetting('parent_annual_income_option'))){ ?>
                                                    <select name="m_annual_income" class="form-control"
                                                        id="m_annual_income">
                                                        <option value="">Select Annual Income</option>
                                                        <?php $income_options = json_decode($this->settings->getSetting('parent_annual_income_option')) ;
                                                                foreach($income_options as $key => $val){ 
                                                                    $selected = ''; 
                                                                    if($motherData->annual_income == $val) {
                                                                        $selected = 'selected';
                                                                    } ?>
                                                                                        <option value="<?= $val ?>" <?= $selected; ?>><?= $val ?>
                                                                                        </option>
                                                                                        <?php }
                                                            ?>
                                                    </select>
                                                    <?php } else{ ?>
                                                    <input id="m_annual_income"
                                                        data-parsley-error-message="Enter valid currency value"
                                                        data-parsley-pattern="^[0-9]\d*(\.\d+)?$"
                                                        placeholder="Enter Income"
                                                        value="<?= $motherData->annual_income ?>" name="m_annual_income"
                                                        type="number" class="form-control input-md">
                                                    <?php }?>
                                                    <input type="hidden" id="old_m_annual_income" name="old_m_annual_income" value="<?php echo $motherData->annual_income  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn"
                                                            data-id="m_annual_income" data-name="annual_income"
                                                            data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endif ?>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_COMPANY')) :
                ?>

                                        <div class="form-group">
                                            <label class="col-md-2 control-label"><strong>Company </strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="text" name="m_company" class="form-control"
                                                        id="m_company" value="<?php echo $motherData->company  ?>">
                                                        <input type="hidden" id="old_m_company" name="old_m_company" value="<?php echo $motherData->company  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_company"
                                                            data-name="company" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endif ?>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_AADHAR')) : ?>

                                        <div class="form-group">
                                            <label class="col-md-2 control-label"><strong>Aadhar No. </strong></label>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <input type="number" name="m_aadhar_no"
                                                        placeholder="Enter Aadhar Number" class="form-control"
                                                        id="m_aadhar_no" value="<?php echo $motherData->aadhar_no  ?>"
                                                        pattern="\d{12}" maxlength="12">
                                                        <input type="hidden" id="old_m_aadhar_no" name="old_m_aadhar_no" value="<?php echo $motherData->aadhar_no  ?>">
                                                    <div class="input-group-append">
                                                        <button class="input-group-text save_btn" data-id="m_aadhar_no"
                                                            data-name="aadhar_no" data-type="mother">Save</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endif ?>

                                        <?php if ($this->settings->isProfile_edit_enabled('MOTHER_ADDRESS')) : ?>
                                        <div class="form-group">
                                            <label class="col-md-2 control-label"></label>
                                            <?php $add_type = 0;
                    foreach ($motherAddress as $add => $m_ad) {
                      echo '<div class="col-md-5" style="padding: 0px;">';
                      echo '<div style="margin-left:10px;"><label class="control-label"><strong> ' . $add . '</strong></label></div>';
                      if (!empty($m_ad)) { ?>
                                            <div class="form-group row">
                                                <div class="col-md-4">
                                                    <input type="text" placeholder="No"
                                                        value="<?php echo $m_ad->Address_line1  ?>"
                                                        class="form-control input-md"
                                                        name="m_line1[<?php echo $m_ad->addId ?>]"
                                                        id="line1_<?php echo $m_ad->addId ?>">
                                                        <input type="hidden" id="old_line1_<?php echo $m_ad->addId ?>" name="old_m_line1[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->Address_line1  ?>">
                                                </div>
                                                <div class="col-md-8">
                                                    <input type="text" placeholder="Street"
                                                        value="<?php echo $m_ad->Address_line2  ?>"
                                                        class="form-control input-md"
                                                        name="m_line2[<?php echo $m_ad->addId ?>]"
                                                        id="line2_<?php echo $m_ad->addId ?>">
                                                        <input type="hidden" id="old_line2_<?php echo $m_ad->addId ?>" name="old_m_line2[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->Address_line2  ?>">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-6">
                                                    <input type="text" placeholder="Area"
                                                        value="<?php echo $m_ad->area  ?>" class="form-control input-md"
                                                        name="m_area[<?php echo $m_ad->addId ?>]"
                                                        id="area_<?php echo $m_ad->addId ?>">
                                                        <input type="hidden" id="old_area_<?php echo $m_ad->addId ?>" name="old_m_area[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->area  ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="text" placeholder="District"
                                                        value="<?php echo $m_ad->district  ?>"
                                                        class="form-control input-md"
                                                        name="m_district[<?php echo $m_ad->addId ?>]"
                                                        id="district_<?php echo $m_ad->addId ?>">
                                                        <input type="hidden" id="old_district_<?php echo $m_ad->addId ?>" name="old_m_district[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->district  ?>">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-4">
                                                    <input type="text" placeholder="State"
                                                        value="<?php echo $m_ad->state  ?>"
                                                        class="form-control input-md"
                                                        name="m_state[<?php echo $m_ad->addId ?>]"
                                                        id="state_<?php echo $m_ad->addId ?>">
                                                        <input  type="hidden" id="old_state_<?php echo $m_ad->addId ?>" name="old_state[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->state  ?>">
                                                </div>
                                                <div class="col-md-4">
                                                    <select id="country_<?php echo $m_ad->addId ?>"
                                                        name="m_country[<?php echo $m_ad->addId ?>]"
                                                        class="form-control input-md">
                                                        <option value="">Select Country</option>;
                                                        <?php foreach ($this->config->item('country') as $nation) {
                                if ($m_ad->country == $nation)
                                  echo '<option selected>' . $nation . '</option>';
                                else
                                  echo '<option>' . $nation . '</option>';
                              } ?>
                                                    </select>
                                                    <input type="hidden" id="old_country_<?php echo $m_ad->addId ?>" name="old_m_country[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->country  ?>">
                                                </div>
                                                <div class="col-md-4">
                                                    <input id="pin_code_<?php echo $m_ad->addId ?>"
                                                        placeholder="Pin Code" value="<?php echo $m_ad->pin_code  ?>"
                                                        name="m_pincode[<?php echo $m_ad->addId ?>]" type="text"
                                                        class="form-control input-md" data-parsley-type="digits"
                                                        data-parsley-length="[5, 8]"
                                                        data-parsley-error-message="Enter a valid pin-code, only digits">
                                                        <input type="hidden" id="old_pin_code_<?php echo $m_ad->addId ?>" name="old_m_pincode[<?php echo $m_ad->addId ?>]" value="<?php echo $m_ad->pin_code  ?>">
                                                </div>
                                            </div>
                                            <button style="margin-left: 10px;width:100px;" type="button"
                                                id="add_mother_<?php echo $m_ad->addId ?>" class="btn address-btn"
                                                data-update="1" data-id="<?php echo $m_ad->addId ?>"
                                                data-type="mother" data-add_type="<?= $add ?>">Save</button>
                                            <?php
                      } else { ?>
                                            <div class="form-group row">
                                                <div class="col-md-4">
                                                    <input type="text" placeholder="No" class="form-control input-md"
                                                        value="" name="m_line1[<?php echo $add_type ?>]"
                                                        id="line1_<?php echo $add_type ?>">
                                                        <input type="hidden" id="old_line1_<?php echo $add_type ?>" name="old_m_line1[<?php echo $add_type ?>]" value="">
                                                </div>
                                                <div class="col-md-8">
                                                    <input type="text" placeholder="Street"
                                                        class="form-control input-md" value=""
                                                        name="m_line2[<?php echo $add_type ?>]"
                                                        id="line2_<?php echo $add_type ?>">
                                                        <input type="hidden" id="old_line2_<?php echo $add_type ?>" name="old_m_line2[<?php echo $add_type ?>]" value="">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-6">
                                                    <input type="text" placeholder="Area" class="form-control input-md"
                                                        value="" name="m_area[<?php echo $add_type ?>]"
                                                        id="area_<?php echo $add_type ?>">
                                                        <input type="hidden" id="old_area_<?php echo $add_type ?>" name="old_m_area[<?php echo $add_type ?>]" value="">
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="text" placeholder="District"
                                                        class="form-control input-md" value=""
                                                        name="m_district[<?php echo $add_type ?>]"
                                                        id="district_<?php echo $add_type ?>">
                                                        <input type="hidden" id="old_district_<?php echo $add_type ?>" name="old_m_district[<?php echo $add_type ?>]" value="">
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-md-4">
                                                    <input type="text" placeholder="State" class="form-control input-md"
                                                        value="" name="m_state[<?php echo $add_type ?>]"
                                                        id="state_<?php echo $add_type ?>">
                                                        <input type="hidden" id="old_state_<?php echo $add_type ?>" name="old_m_state[<?php echo $add_type ?>]" value="">
                                                </div>
                                                <div class="col-md-4">
                                                    <select id="country_<?php echo $add_type ?>"
                                                        name="m_country[<?php echo $add_type ?>]"
                                                        class="form-control input-md">
                                                        <option value="">Select Country</option>;
                                                        <?php foreach ($this->config->item('country') as $nation) {
                                echo '<option>' . $nation . '</option>';
                              } ?>
                                                    </select>
                                                    <input type="hidden" id="old_country_<?php echo $add_type ?>" name="old_m_country[<?php echo $add_type ?>]" value="">
                                                </div>
                                                <div class="col-md-4">
                                                    <input id="pin_code_<?php echo $add_type ?>" placeholder="Pin Code"
                                                        name="m_pincode[<?php echo $add_type ?>]" type="text" value=""
                                                        class="form-control input-md" data-parsley-type="digits"
                                                        data-parsley-length="[5, 8]"
                                                        data-parsley-error-message="Enter a valid pin-code, only digits">
                                                        <input type="hidden" id="old_pin_code_<?php echo $add_type ?>" name="old_m_pincode[<?php echo $add_type ?>]" value="">
                                                </div>
                                            </div>
                                            <button style="margin-left: 10px;width:100px;" type="button"
                                                id="add_mother_<?php echo $add_type ?>" class="btn address-btn"
                                                data-update="0" data-id="<?php echo $add_type ?>"
                                                data-type="mother" data-add_type="<?= $add ?>">Save</button>
                                            <?php  }
                      echo "</div>";
                      $add_type++;
                    } ?>
                                        </div>
                                        <?php endif ?>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- mother -->

                <!-- guardian -->
                <?php if ($show_guardian) {
      $guardian_pic = base_url() . 'assets/img/icons/profile.png';
      $guardian_name = '';
      $guardian_firstname = '';
      $guardian_lastname = '';
      $guardian_email = '';
      $guardian_contact_no = '';
      $guardian_id = 0;
      if (!empty($guardianData)) {
        if ($guardianData->picture_url != '' || $guardianData->picture_url != NULL) {
          $guardian_pic = $this->filemanager->getFilePath($guardianData->picture_url);
        }
        $guardian_id = $guardianData->id;
        $guardian_name = $guardianData->name;
        $guardian_firstname = $guardianData->first_name;
        $guardian_lastname = $guardianData->last_name;
        $guardian_email = $guardianData->email;
        $guardian_contact_no = $guardianData->mobile_no;
      }
    ?>
                <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

                    <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                        <div class="row d-flex"
                            style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                            <h3 class="card-title">
                                <strong> Guardian </strong>
                                <?php if ($this->settings->isProfile_profile_enabled('GUARDIAN_NAME')) : ?>
                                <?php echo ucfirst($guardian_name); ?>
                                <?php endif ?>
                            </h3>
                        </div>
                        <?php if ($this->settings->isProfile_edit_enabled('GUARDIAN_PHOTO')) : ?>
                        <span style="color:red">* If guardian is not applicable, upload the picture of parent who visits
                            school frequently</span>
                        <?php endif ?>

                    </div>
                    <input type="hidden" name="guardian_id" id="guardian_id" value="<?php echo $guardian_id; ?>">
                    <div class="card-body">

                        <div class="row" style="margin: 0px;">
                            <div class="col-md-12">
                                <div class="col-md-2">

                                    <?php if ($this->settings->isProfile_edit_enabled('GUARDIAN_PHOTO')) : ?>
                                    <?php 
                    $showError = 1;
                    if (empty($guardian_id)) {
                      $showError = 0;
                    } ?>
                                    <div class="text-center">
                                        <img onclick="$('#gUpload').click();"
                                            class="img-responsive img-circle <?php echo ($showError == '0')? 'guardian_upload_disabled':'' ?>"
                                            id="guardian_photo" style="width:100px;height:100px"
                                            src="<?php echo $guardian_pic; ?>">
                                        <i onclick="$('#gUpload').click();" id="guardian_edit_pencil"
                                            class="fa fa-pencil <?php echo ($showError == '0')? 'guardian_upload_disabled':'' ?>"></i>
                                        <input hidden="hidden" type="file" id="gUpload" class="file"
                                            data-preview-file-type="jpeg" name="guardian_photo" accept="image/*">
                                        <span class="help-block">Allowed file types: JPEG, JPG, and PNG; Allowed size:
                                            up to <?php if($this->settings->getSetting('student_parent_guardian_photo_size')){ echo $this->settings->getSetting('student_parent_guardian_photo_size')/1000; echo "kb"; }else{
                    echo "10mb"  ;
                }?> </span>
                                        <span style="color: red;" id="g_message"></span>
                                        <span id="fileuploadError3"
                                            style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>

                                        <span id="guardian_error"
                                            style="display: <?php echo ($showError == '0')?'block':'none' ?>;padding-top:5px;padding-bottom:5px;font-weight: 600; color: red;">
                                            Save name before saving photo
                                        </span>
                                        <span id="percentage_completed_guardian"
                                            style="font-size: 20px; display: none; position: absolute;top: 34px;left: 0;right: 0;">0
                                            %</span>
                                        <button id="photo_guardian" type="button"
                                            onclick="save_profile_photos('guardian', <?php echo $guardian_id; ?>)"
                                            style="display: none;margin: auto;width:100px;"
                                            class="btn photo_btn guardian-btn">Upload</button>
                                    </div>

                                    <?php endif ?>
                                </div>

                                <div class="col-md-10">
                                    <?php if ($this->settings->isProfile_edit_enabled('GUARDIAN_NAME')) { ?>
                                    <div class="form-group">
                                        <label class="col-md-2 control-label" for="student_dob"> First Name </label>
                                        <div class="col-md-4">
                                            <div class="input-group mb-3">
                                                <input type="text" name="g_first_name" placeholder="First name"
                                                    required="" class="form-control" id="g_first_name"
                                                    value="<?php echo $guardian_firstname  ?>">
                                                    <input type="hidden" id="old_g_first_name" name="old_g_first_name" value="<?php echo $guardian_lastname  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn guardian-btn"
                                                        data-id="g_first_name" data-name="first_name"
                                                        data-type="guardian">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="col-md-2 control-label" for="student_dob"> First Name </label>
                                        <div class="col-md-4">
                                            <div class="input-group mb-3">
                                                <input type="text" name="g_last_name" placeholder="Last name"
                                                    class="form-control" id="g_last_name"
                                                    value="<?php echo $guardian_lastname  ?>">
                                                    <input type="hidden" id="old_g_last_name" name="old_g_last_name" value="<?php echo $guardian_lastname  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn guardian-btn"
                                                        data-id="g_last_name" data-name="last_name"
                                                        data-type="guardian">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php } ?>

                                    <div class="form-group">

                                        <?php if ($this->settings->isProfile_edit_enabled('GUARDIAN_CONTACT_NO')) : ?>
                                        <label class="col-md-2 control-label"><strong>Mobile</strong></label>
                                        <div class="col-md-4">
                                            <div class="input-group mb-3">
                                                <input type="text" data-parsley-pattern="^[0-9 -()+]+$"
                                                    data-parsley-length="[8, 20]" pattern="\d{10}"
                                                    data-parsley-error-message="Enter Valid Contact Number"
                                                    name="g_mobile_no" placeholder="Mobile No." class="form-control"
                                                    id="g_mobile_no" value="<?php echo $guardian_contact_no  ?>">
                                                    <input type="hidden" id="old_g_mobile_no" name="old_g_mobile_no" value="<?php echo $guardian_contact_no  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn guardian-btn"
                                                        data-id="g_mobile_no" data-name="mobile_no"
                                                        data-type="guardian">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>
                                        <?php if ($this->settings->isProfile_edit_enabled('GUARDIAN_EMAIL')) : ?>
                                        <label class="col-md-2 control-label"><strong>Email</strong></label>
                                        <div class="col-md-4">
                                            <div class="input-group mb-3">
                                                <input type="email" name="g_email" placeholder="Email Id"
                                                    class="form-control" id="g_email"
                                                    value="<?php echo $guardian_email;  ?>">
                                                    <input type="hidden" id="old_g_email" name="old_g_email" value="<?php echo $guardian_email;  ?>">
                                                <div class="input-group-append">
                                                    <button class="input-group-text save_btn guardian-btn"
                                                        data-id="g_email" data-name="email"
                                                        data-type="guardian">Save</button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif ?>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card" style="box-shadow: none;border:none;border-radius: 8px;margin-bottom: 1rem;">

                    <?php if ($this->settings->isProfile_edit_enabled('FAMILY_PHOTO')) : ?>
                    <div class="card-header panel_heading_new_style_padding" style="border-radius: 8px;padding: 15px;">
                        <div class="row d-flex"
                            style="margin: 0px;border-bottom: solid 1px #eee;padding-bottom: 1.2rem;">
                            <h3 class="card-title">
                                <strong> Family Photo </strong>
                            </h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row" style="margin: 0px;">
                            <div class="col-md-12">
                                <div class="col-md-2">

                                    <?php 
                $showfamilyerror = 1;
                if (empty($student_id)) {
                  $showfamilyerror = 0;
                } ?>
                                    <div class="text-center">

                                        <img onclick="$('#fileuploadfamily').click();"
                                            class="img-responsive img-circle <?php echo ($showfamilyerror == '0')? 'family_upload_disabled':'' ?>"
                                            id="family_photo" style="width:100px;height:100px"
                                            src="<?php echo (empty($studentData->family_picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->family_picture_url); ?>">
                                        <i onclick="$('#fileuploadfamily').click();" id="family_edit_pencil"
                                            class="fa fa-pencil <?php echo ($showfamilyerror == '0')? 'family_upload_disabled':'' ?>"></i>
                                        <input hidden="hidden" type="file" id="fileuploadfamily" class="file"
                                            data-preview-file-type="jpeg" name="family_photo" accept="image/*">
                                        <span class="help-block"></span>
                                        <span style="color: red;" id="fam_message"></span>
                                        <span id="fileuploadfamilyError"
                                            style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                                        <span id="family_error"
                                            style="display: <?php echo ($showfamilyerror == '0')?'block':'none' ?>;padding-top:5px;padding-bottom:5px;font-weight: 600; color: red;">
                                            Save name before saving photo
                                        </span>
                                        <br>
                                        <span id="percentage_completed_family"
                                            style="font-size: 20px; display: none; position: absolute;top: 230px;left: 0;right: 0;">0
                                            %</span>

                                        <button id="photo_family" type="button"
                                            onclick="save_profile_photos('family', <?php echo $student_id; ?>)"
                                            style="display: none;margin: auto;width:100px;"
                                            class="btn photo_btn">Upload</button>
                                    </div>
                                    <?php endif ?>


                                </div>


                            </div>
                        </div>
                    </div>
                </div>
                <!-- guardian -->
                <?php } ?>
            </div>
        </div>
    </div>
</div>


<style type="text/css">
#profile_id .form-control-static {
    padding: 0;
    margin: 0;
}

.guardian_upload_disabled {
    pointer-events: none;
    opacity: 0.5;
}
</style>

<script type="text/javascript">
var student_id = 0;
var father_id = 0;
var mother_id = 0;
var guardian_id = 0;
$('.form-control').keydown(function(e) {
    if (e.keyCode == 13) {
        e.preventDefault();
        return false;
    }
});
$(".save_btn").click(function(e) {
    let data_id = $(this).data('id');
    let data_name = $(this).data('name');
    if(data_name == 'mobile_no' || data_name == 'preferred_contact_no'){
        let mobileNumber = $('#'+data_id).val();
        let pattern = /^[0-9 -()+]+$/;
        if (!pattern.test(mobileNumber)) {
            alert('Please enter a valid mobile number.');
            inputField.focus();
            return false;
        }
        if (mobileNumber.length > 15) {
            alert('Mobile number must be no more than 15 characters.');
            $('#' + data_id).focus();
            return false;
        }
    }
    
    

    if(data_name == 'email'){
        let emailAddress = $('#' + data_id).val();
        let emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(emailAddress)) {
            alert('Please enter a valid email address.');
            $('#' + data_id).focus();
            return false;
        }
    }
    if(data_name == 'aadhar_no'){
        let aadharNumber = $('#' + data_id).val();
        let aadharPattern = /^\d{12}$/;
        if (!aadharPattern.test(aadharNumber)) {
            alert('Please enter a valid 12-digit Aadhar number.');
            $('#' + data_id).focus();
            return false;
        }
    }
    saveBtn = $(this);
    saveBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin" style="font-size:18px"></i>');
    var type = saveBtn.data('type');

    guardian_id = $("#guardian_id").val();
    if (type == 'guardian' && guardian_id == 0) {
        //stop updating other fields until we insert one row
        $(".guardian-btn").prop('disabled', true);
    }
    var input_id = saveBtn.data('id');
    var field_name = saveBtn.data('name');
    var old_value = $("#old_" + input_id).val();
    var field_value = $("#" + input_id).val();
    $.ajax({
        url: '<?php echo site_url('parent_controller/save_profile_data'); ?>',
        data: {
            'type': type,
            'field_value': field_value,
            'field_name': field_name,
            'student_id': student_id,
            'father_id': father_id,
            'mother_id': mother_id,
            'guardian_id': guardian_id,
            'old_value': old_value
        },
        type: "post",
        success: function(data) {
            // saveBtn.prop('disabled', false).html('<i class="fa fa-check""></i>');
            if (type == 'guardian' && guardian_id == 0) {
                $("#guardian_id").val(data);
            }
            $('#' + type + '_photo').removeClass(type + '_upload_disabled');
            $('#' + type + '_edit_pencil').removeClass(type + '_upload_disabled');
            $('#' + type + '_error').hide();
            saveBtn.html('Saved');
            setTimeout(function() {
                saveBtn.prop('disabled', false).html('Save');
                $(".guardian-btn").prop('disabled', false);
            }, 2000);
        },
        error: function(err) {
            console.log(err);
        }
    });
});

$(".address-btn").click(function() {
    saveBtn = $(this);
    var add_id = saveBtn.data('id');
    var type = saveBtn.data('type');
    var address_type = saveBtn.data('add_type');
    var update = saveBtn.data('update');
    saveBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin" style="font-size:18px"></i>');
    var add_data = {};
    var old_data = {};
    add_data['Address_line1'] = $("#line1_" + add_id).val();
    add_data['Address_line2'] = $("#line2_" + add_id).val();
    add_data['area'] = $("#area_" + add_id).val();
    add_data['district'] = $("#district_" + add_id).val();
    add_data['state'] = $("#state_" + add_id).val();
    add_data['country'] = $("#country_" + add_id).val();
    add_data['pin_code'] = $("#pin_code_" + add_id).val();
    add_data['avatar_type'] = 1;
    add_data['address_id'] = add_id;
    add_data['update'] = update;
    add_data['stakeholder_id'] = student_id;

    old_data['Address_line1'] = $("#old_line1_" + add_id).val();
    old_data['Address_line2'] = $("#old_line2_" + add_id).val();
    old_data['area'] = $("#old_area_" + add_id).val();
    old_data['district'] = $("#old_district_" + add_id).val();
    old_data['state'] = $("#old_state_" + add_id).val();
    old_data['country'] = $("#old_country_" + add_id).val();
    old_data['pin_code'] = $("#old_pin_code_" + add_id).val();
    old_data['address_type'] = address_type;
    old_data['type'] = type;

    if (type == 'father') {
        add_data['avatar_type'] = 2;
        add_data['stakeholder_id'] = father_id;
    } else if (type == 'mother') {
        add_data['avatar_type'] = 2;
        add_data['stakeholder_id'] = mother_id;
    }
    $.ajax({
        url: '<?php echo site_url('parent_controller/save_address_data'); ?>',
        data: {'add_data':add_data,'old_data':old_data},
        type: "post",
        success: function(data) {
            saveBtn.html('Saved');
            setTimeout(function() {
                saveBtn.prop('disabled', false).html('Save');
            }, 2000);
        },
        error: function(err) {
            console.log(err);
        }
    });
});

var completed_promises = 0;
var in_progress_promises = 0;
var total_promises = 0;
var current_percentage = 0;
var selectedFiles = [];

function save_profile_photos(type, id) {
    $('#percentage_completed_' + type).show();

    if (type == 'student') {
        var file_data = $('#fileupload').prop('files')[0];
        var fileSize = file_data.size;
        if (fileSize > (10 * 1024 * 1024)) {
            $('#s_message').text('File size exceeds the limit.');
            return;
        }
        $('#student_photo').css('opacity', '0.3');
    } else if (type == 'father') {
        var file_data = $('#fUpload').prop('files')[0];
        var fileSize = file_data.size;
        if (fileSize > (10 * 1024 * 1024)) {
            $('#f-message').text('File size exceeds the limit.');
            return;
        }
        $('#father_photo').css('opacity', '0.3');
    } else if (type == 'family') {
        var file_data = $('#fileuploadfamily').prop('files')[0];
        var fileSize = file_data.size;
        if (fileSize > (10 * 1024 * 1024)) {
            $('#fam_message').text('File size exceeds the limit.');
            return;
        }
        $('#family_photo').css('opacity', '0.3');
    } else if (type == 'mother') {
        var file_data = $('#mUpload').prop('files')[0];
        var fileSize = file_data.size;
        if (fileSize > (10 * 1024 * 1024)) {
            $('#m_message').text('File size exceeds the limit.');
            return;
        }
        $('#mother_photo').css('opacity', '0.3');
    } else if (type == 'guardian') {
        $('#guardian_photo').css('opacity', '0.3');
        id = $("#guardian_id").val();
        if (id == 0) {
            $("#show_g_error").show();
            $("#photo_" + type).prop('disabled', false).html('Save');
            $("#photo_" + type).hide();
            return false;
        }
        $("#show_g_error").hide();
        var file_data = $('#gUpload').prop('files')[0];
        var fileSize = file_data.size;
        if (fileSize > (10 * 1024 * 1024)) {
            $('#g_message').text('File size exceeds the limit.');
            return;
        }
    }

    $("#photo_" + type).prop('disabled', true).html('<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>');

    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    saveFileToStorage(file_data, type, id);
}

function saveFileToStorage(file, usertype, id) {
    if (usertype == 'family') {
        let folder = 'profilefamily';
    }
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0, usertype);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    savePhoto(path, usertype, id, file);
                    $('#percentage_completed_' + usertype).hide();
                    $('#' + usertype + '_photo').css('opacity', '1');
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function single_file_progress(percentage, usertype) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    // var progress = document.getElementById('single-file-percentage');
    // progress.style.width = current_percentage+'%';
    $("#percentage_completed_" + usertype).html(`${current_percentage} %`);
    return false;
}

function savePhoto(orginalsizepath, type, id, file_data) {
    var form_data = new FormData();
    form_data.append('file', file_data);
    form_data.append('type', type);
    form_data.append('id', id);
    form_data.append('high_quality', orginalsizepath);

    $.ajax({
        url: '<?php echo site_url('parent_controller/save_profile_photo') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
            $("#photo_" + type).html('Saved');
            setTimeout(function() {
                $("#photo_" + type).prop('disabled', false).html('Save');
                $("#photo_" + type).hide('500');
            }, 2000);
        }
    });
}


function submitBtn1() {
    $("#submitButton").prop('disabled', true).html('Updating...');
    $("#p-form1").submit();
}

$(document).ready(function() {
    student_id = <?php echo $student_id; ?>;
    father_id = <?php echo $fatherData->id; ?>;
    mother_id = <?php echo $motherData->id; ?>;
    var dob_maxdate = new Date();
    dob_maxdate.setFullYear(dob_maxdate.getFullYear() + 1);

    var dob_mindate = new Date();
    dob_mindate.setFullYear(dob_mindate.getFullYear() - 30);

    $('#dob_dtpicker, #student_dob').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    });
});



$('#fileupload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileuploadError')) {
        $("#fileuploadError").html("");
        readURL(this);
        $('#student_photo').css('opacity: 0.5');
        $("#photo_student").show();
    } else {
        this.value = null;
        $('#fileuploadSuccess').hide();
        $("#save_std_photo").hide();
    }
});

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#student_photo').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}
// Family Photo
$('#fileuploadfamily').change(function() {
    var src = $(this).val();

    console.log(this.files);
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileuploadfamilyError')) {
        $("#fileuploadfamilyError").html("");
        read_family_URL(this);
        $('#family_photo').css('opacity', '0.5');
        $("#photo_family").show();
    } else {
        this.value = null;

        $("#photo_family").hide();
    }
});

function read_family_URL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#family_photo').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}
//     

function readURL1(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#father_photo').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

$('#fUpload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileuploadError1')) {
        $("#fileuploadError1").html("");
        readURL1(this);
        $("#photo_father").show();
    } else {
        this.value = null;
        $("#photo_father").hide();
    }
});

function readURL3(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#guardian_photo').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

$('#gUpload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileuploadError3')) {
        $("#fileuploadError3").html("");
        readURL3(this);
        $("#photo_guardian").show();
    } else {
        this.value = null;
        $("#photo_guardian").hide();
    }
});

function readURL2(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#mother_photo').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

$('#mUpload').change(function() {
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if (src && validatePhoto(this.files[0], 'fileuploadError2')) {
        $("#fileuploadError2").html("");
        readURL2(this);
        $("#photo_mother").show();
    } else {
        this.value = null;
        $("#photo_mother").hide();
    }
});

function validatePhoto(file, errorId) {
    var fileSizeBytes = '<?php echo $this->settings->getSetting('student_parent_guardian_photo_size') ?>';
    var bytes = '10485760';
    if (fileSizeBytes) {
        var bytes = fileSizeBytes;
    }
    var retrunres = formatBytes(bytes);
    if (file.size > bytes || file.fileSize > bytes) {
        $("#" + errorId).html("Allowed file size exceeded. " + retrunres + "")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function formatBytes(bytes, decimals = 2) {
    if (!+bytes) return '0 Bytes'
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
}
if (document.getElementById('s_aadhar_no')) {
    document.getElementById('s_aadhar_no').addEventListener('input', function(event) {
        var input = event.target;
        var value = input.value;

        // Remove any non-digit characters
        value = value.replace(/\D/g, '');

        // Trim to 12 digits
        if (value.length > 12) {
            value = value.slice(0, 12);
        }
        input.value = value;
    });
}
if (document.getElementById('f_aadhar_no')) {
    document.getElementById('f_aadhar_no').addEventListener('input', function(event) {
        var input = event.target;
        var value = input.value;

        // Remove any non-digit characters
        value = value.replace(/\D/g, '');

        // Trim to 12 digits
        if (value.length > 12) {
            value = value.slice(0, 12);
        }
        input.value = value;
    });
}
if (document.getElementById('m_aadhar_no')) {
    document.getElementById('m_aadhar_no').addEventListener('input', function(event) {
        var input = event.target;
        var value = input.value;

        // Remove any non-digit characters
        value = value.replace(/\D/g, '');

        // Trim to 12 digits
        if (value.length > 12) {
            value = value.slice(0, 12);
        }
        input.value = value;
    });
}
</script>

<style type="text/css">
.control-label {
    padding-top: 10px;
}

.fa-pencil {
    padding: 8px;
    background: #6893ca;
    border-radius: 50%;
    margin-left: -15%;
    margin-bottom: 5%;
    vertical-align: bottom;
    cursor: pointer;
}

.address-input {
    margin: 5px 0px;
}

.address-btn {
    background: #6893ca !important;
    border-color: #6893ca !important;
    color: #000 !important;
    margin-left: 10px;
    width: 100px;
}

.save_btn,
.photo_btn {
    background: #6893ca !important;
    border-color: #6893ca !important;
    color: #000 !important;
}
</style>