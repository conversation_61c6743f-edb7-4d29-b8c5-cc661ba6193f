<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/index') ?>">Student Certificate</a></li>
    <li>Issue Certificate</li>
</ul>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php  echo site_url('student/Certificates_controller/index'); ?>">
						<span class="fa fa-arrow-left"></span>
						</a> 
						Issue Certificate <strong><?php // echo ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) ?></strong>
					</h3>
				</div>
				<!-- <div class="col-md-2">
					<a href="<?php // echo site_url('student/Certificates_controller/generate_ceritificates/' . $student_uid); ?>" class="btn btn-primary" style="float: right;"> Issue New Certificate</a>
				</div> -->
			</div>
		</div>

        <div class="card-body">
      <div class="col-12">

        <div class="col-md-3" style="margin-left:-10px">
          <label class="col-md-12" for="gradeView" style="margin-left:-3px">Student Name</label>
          <div class="col-md-12">
           <div class="row">
            <div class="col-md-10">
              <input type="text" class="form-control displayStudentLIst"  autofocus="" autocomplete="off" placeholder="Search by Student name" id="staff_studentid" name="staff_student">
            </div>
          </div>
          </div> 
        </div>

        <div class="col-md-3" style="margin-left:-50px">
          <label class="col-md-12" for="gradeView">Admission No.</label>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-10">
                <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
              </div>
              <div class="col-md-2">
                <input type="button" value="Get" id="getIssuedCertificate" class="input-md btn  btn-primary">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
		<div class="card-body">

    <div class="row" style="margin: 0px;margin-bottom:5px;"> <div class="col-md-10"></div><div class="col-md-2" id="new_certificate_button">
				</div></div>
    <div id="data_table"></div>
    </div>
  </div>
</div>

<!-- modal -->

<?php $i = 1;
		if (!empty($cert_array)) {
			foreach ($cert_array as $val) {  ?>
				<div class="modal fade" id="<?php echo 'exampleModal' . $i++; ?>" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
					<div class="modal-dialog" role="document">
						<div class="modal-content" style="width: 55%;margin: auto;margin-top: 8%;border-radius: .75rem;">
							<div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
								<h4 class="modal-title" id="exampleModalLabel">Certificate of <?php echo '<strong>' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . '</strong> (Class: ' . $stdData->className . ' / Section: ' . $stdData->sectionName . ')' ?></h4>
								<button type="button" class="close" data-dismiss="modal" aria-label="Close">
									<i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
								</button>
							</div>
							<div class="modal-body">
								<iframe src="<?php echo $this->filemanager->getFilePath($val); ?>" width="100%" height="500px"> </iframe>
							</div>

						</div>
					</div>
				</div>
			<?php }
		}
?>
<!-- Modal -->
<div class="modal fade" id="pdfModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Certificate</h5>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <iframe id="pdfViewer" src="" frameborder="0" style="width: 100%; height: 500px;"></iframe>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<script>
  let student_id=<?php if($student_id){ echo $student_id;} else{ echo 0;} ?>;
  if(student_id != 0){
    $(document).ready(function(){
      onchange_student_selection(student_id);
    });
  }
    
  var aMerge = [];
$(document).ready(function(){
    var s_names = JSON.parse('<?php echo json_encode($all_names); ?>');

    for (var i = 0; i < s_names.student.length; i++) {
        aMerge.push({
            name: s_names.student[i].s_name.trim() + '('+s_names.student[i].class_name + s_names.student[i].section_name+')'+' ('+s_names.student[i].student_id+')',
            id: s_names.student[i].student_id,
            promotion_status: s_names.student[i].promotion_status,
            admission_status: s_names.student[i].admission_status
        });
    }

  // for(var j=0; j < s_names.staff.length; j++){
  //     aMerge.push(s_names.staff[j].s_name + ' ('+'staff'+')'+' ('+s_names.staff[j].id_number+')');
  // }

});

  autocomplete(document.getElementById("staff_studentid"), aMerge);
var merge = '';
//console.log(aMerge);
 // alert(aMerge);
function autocomplete(inp, arr) {
  // alert(inp);
     /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
        var a, b, i, val = this.value;
        /*close any already open lists of autocompleted values*/
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        /*create a DIV element that will contain the items (values):*/
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        /*append the DIV element as a child of the autocomplete container:*/
        this.parentNode.appendChild(a);
        /*for each item in the array...*/
        for (i = 0; i < arr.length; i++) {
            /*check if the item starts with the same letters as the text field value:*/
            if (arr[i].name.toUpperCase().startsWith(val.toUpperCase())) {
            /*create a DIV element for each matching element:*/
            b = document.createElement("DIV");
            c = document.createElement("DIV");
            b.style.cursor = 'pointer';

            var optionColor = ""; // Default color
                if (arr[i].promotion_status == 4 || arr[i].promotion_status == 5) {
                    optionColor = "red";
                } else if (arr[i].admission_status == 1) {
                    optionColor = "";
                } else if (arr[i].admission_status == 3) {
                    optionColor = "";
                }
            /*make the matching letters bold:*/
            var stdNameSplit = arr[i].name.split('\(');

            var merge = stdNameSplit[0];
            var split1 = '('+stdNameSplit[1];
            var card_number = stdNameSplit[2];
          
            var cardNumberSplit = card_number.split(')');

            // b.innerHTML = "<strong style='color:" + optionColor + ";'>" + arr[i].name.substr(0, val.length) + "</strong>";
            // b.innerHTML += arr[i].name.substr(val.length);
            b.innerHTML = "<span style='color:" + optionColor + ";'>" + arr[i].name + "</span>";

            // Store full value in input
            b.innerHTML += "<input type='hidden' value='" + arr[i].name + "_" + arr[i].id + "'>";
            /*execute a function when someone clicks on the item value (DIV element):*/
            b.addEventListener("click", function(e) {
                /*insert the value for the autocomplete text field:*/
                // inp.value = this.getElementsByTagName("input")[0].value;
                inp.setAttribute("data-value", this.getElementsByTagName("input")[0].value); // Keep full value
                inp.value = this.getElementsByTagName("input")[0].value.split('_')[0];
                sepstdName = this.getElementsByTagName("input")[0].value;
               // alert(JSON.stringify(sepstdName));
               var nameSplit = sepstdName.split('_');
               
               var student_name = sepstdName.split('\(');
               var cNo = nameSplit[1];
               var lcNumber = cNo.split(',');
               // alert(lcNumber[0]);
                var output='';
                output+='<option value="'+lcNumber[0]+'">'+merge+' </option>';
                $("#selectStudents").html(output); 
                
                  onchange_student_selection();
                
                /*close the list of autocompleted values,
                (or any other open lists of autocompleted values:*/
                closeAllLists();
            });
            a.appendChild(b);
          }
        }
    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
        var x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) {
            /*If the arrow DOWN key is pressed,
            increase the currentFocus variable:*/
            currentFocus++;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 38) { //up
            /*If the arrow UP key is pressed,
            decrease the currentFocus variable:*/
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 13) {
            /*If the ENTER key is pressed, prevent the form from being submitted,*/
            e.preventDefault();
            if (currentFocus > -1) {
            /*and simulate a click on the "active" item:*/
            if (x) x[currentFocus].click();
            }
        }
    });
    function addActive(x) {
        /*a function to classify an item as "active":*/
        if (!x) return false;
        /*start by removing the "active" class on all items:*/
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        /*add class "autocomplete-active":*/
        x[currentFocus].classList.add("autocomplete-active");
    }
    function removeActive(x) {
        /*a function to remove the "active" class from all autocomplete items:*/
      for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
      }
    }
    function closeAllLists(elmnt) {
        /*close all autocomplete lists in the document,
        except the one passed as an argument:*/
      var x = document.getElementsByClassName("autocomplete-items");
      for (var i = 0; i < x.length; i++) {
        if (elmnt != x[i] && elmnt != inp) {
        x[i].parentNode.removeChild(x[i]);
        }
      }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function (e) {
        closeAllLists(e.target);
    });
}
    var studentId_global;
    function onchange_student_selection(studentId=''){
      btn_status.disabled=true;
      btn_status.value="Please wait...";
      $("#data_table").html(`<center><div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
      </div></center>`);
        if(studentId==''){
          studentId =document.getElementById("staff_studentid").getAttribute("data-value");
        }
        studentId_global=studentId;
        $.ajax({
            url: '<?php echo site_url('student/Certificates_controller/get_student_issued_certificates'); ?>',
            data: {studentId: studentId},
            type: "post",
            success: function (data) {
              btn_status.disabled=false;
              btn_status.value="Get";
                var chrdata = $.parseJSON(data);
                html = construct_issued_certificates(chrdata);     
                $('#new_certificate_button').html('<a href="<?php echo site_url('student/Certificates_controller/generate_ceritificates/'); ?>'+chrdata['stdData']['id']+'" class="btn btn-warning" style="float: right;"> Issue New Certificate</a>');
                $('#data_table').html(html);   
                $('#data_table_certificates').DataTable( {
                  ordering:false,
                  scrollY :'40vh',
                  "language": {
                          "search": "",
                          "searchPlaceholder": "Enter Search..."
                      },
                      "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                      "pageLength": 10,
                      dom: 'lBfrtip',
                      buttons: [
                      ]
                  } );
            }
        });
    }

    function construct_issued_certificates(data){
      let html='<h4>Student name: <span style="color:green">'+ data['stdData']['stdName'] +'</span></h4>';
      html+=`<table class="table table-bordered" id="data_table_certificates">
					<thead>
						<tr>
							<th>#</th>
							<th>Action</th>
							<th>Name</th>
							<th>Issued On</th>
							<th>Issued By</th>
							<th>Status</th>
							<th>Visible to parents</th>
						</tr>
					</thead>
					<tbody>`;
          for (let i = 0; i < data['certificates'].length; i++) {
            const element = data['certificates'][i];
            // console.log(element);
            html+=`<tr>
                    <td>${i+1}</td>
                    <td> <button class="btn btn-secondary dropdown-toggle" type="button" id="actionButton${i}" onclick="showMenu(event,'${element['scId']}','${i+1}','${element['parent_visibility']}','${element['pdf_path']}','${element['template_name']}','${element['pdf_status']}','${data['stdData']['stdName']}')">
                        Actions
                    </button></td>
                    <td>${element['template_name']}</td>
                    <td>${element['issued_date']}</td>
                    <td>${element['friendly_name']}</td>`;
                    if(element['pdf_status']=='Active'){
                      html+=`<td style="background-color: lightgreen;">${element['pdf_status']}</td>`;
                    }else{
                      html+=`<td style="background-color:  #f23933;">${element['pdf_status']}</td>`;
                    }

                    if(element['parent_visibility']=='Yes'){
                      html+=`<td style="background-color: lightgreen;">${element['parent_visibility']}</td>`;
                    }else{
                      html+=`<td style="background-color:  #f23933;">${element['parent_visibility']}</td>`;
                    }
                html+=` </tr>`;
          }
          html+=`</tbody></table>`;
          return html;

    }

    function showMenu(event, id,index,parent_visibility,pdf_url,template_name,pdf_status,student_name) {
      console.log(parent_visibility);
    // Close any previously opened menus
    closeMenu();

    // Create the menu dynamically
    const menu = document.createElement('div');
    menu.classList.add('absolute-menu');
    menu.setAttribute('id', 'absoluteMenu' + id);
    var pdf_status_name= pdf_status == 'Active' ? 'Deactivate' : 'Activate';
    var pdf_status_parent_visibility=pdf_status == 'Active' ? '' : 'disabled-class';

    // Menu contents
    menu.innerHTML = `
        <ul class="dropdown-menu show">
            <li><a class="dropdown-item" href="#" onclick="viewPdf('${pdf_url}')">View certificate</a></li>
            <li><a class="dropdown-item" href="#" onclick="download_pdf('${template_name}','${pdf_url}','${student_name}')">Download certificate</a></li>
            <li><a class="dropdown-item" href="<?php echo site_url('student/Certificates_controller/update_issued_certificate/') ?>${id}">Re-issue certificate</a></li>
            <li><a class="dropdown-item" href="<?php echo site_url('student/Certificates_controller/reissue_certificate_new/') ?>${id}">Re-issue with revised certificate</a></li>
            <li><a class="dropdown-item" href="#" onclick="change_status('${pdf_status}','${id}','${template_name}')">${pdf_status_name}</a></li>
            <li><a class="dropdown-item ${pdf_status_parent_visibility}" href="javascript:void(0);" onclick="${pdf_status == 'Active' ? `toggle_parent_visibility('${parent_visibility}','${id}')` : 'return false;'}">Parent visibility</a></li>
        </ul>
    `;

    // <li><a class="dropdown-item" href="#" onclick="certificate_soft_delete(${id})">Delete certificate</a></li>

    // Append the menu to the body
    document.body.appendChild(menu);

    // Get the button's bounding rectangle
    const rect = event.target.getBoundingClientRect();
    const menuHeight = menu.offsetHeight; // Height of the dropdown menu

    // Calculate available space below and above the button
    const spaceBelow = window.innerHeight - rect.bottom; // Space below the button
    const spaceAbove = rect.top; // Space above the button

    // Position the menu
    menu.style.position = 'absolute';
    menu.style.left = `${rect.left + window.scrollX}px`;

    // Decide whether to open the menu upwards or downwards
    if (spaceBelow < menuHeight && spaceAbove >= menuHeight) {
        // Not enough space below, open upwards
        menu.style.top = `${rect.top + window.scrollY - menuHeight}px`;
    } else {
        // Open downwards if there is enough space
        menu.style.top = `${rect.bottom + window.scrollY}px`;
    }
}
function closeMenu() {
    const openMenu = document.querySelector('.absolute-menu');
    if (openMenu) {
        openMenu.remove();
    }
}

document.addEventListener('click', function(event) {
    const isButtonClick = event.target.classList.contains('dropdown-toggle');
    if (!isButtonClick) {
        closeMenu();
    }
});
const style = document.createElement('style');
style.innerHTML = `
    .disabled-class {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
    }
`;
document.head.appendChild(style);
// function certificate_soft_delete(certificate_id) {

  // Swal.fire({
  //       title: "Are you sure to delete this certificate? Once it is deleted, it can not be retrived.",
  //       showCancelButton: true,
  //       confirmButtonText: "Yes",
  //       cancelButtonText: "No",
  //     }).then((result) => {
  //       if (result.isConfirmed) {

  //         $.ajax({
	// 					url: "<?php // echo site_url('student/Certificates_controller/delete_certificate/'); ?>" + certificate_id,
	// 					data: {
	// 						'cert_id': certificate_id
	// 					},
	// 					type: "post",
	// 					success: function(data) {
							// $(".action" + certificate_id).hide();
							// $("." + certificate_id).hide();
              // Swal.fire("Deleted!", "", "success");
							// $("#" + certificate_id).html("<a class='btn btn-warning' style='margin-left: 8px' data-placement='top' data-toggle='tooltip' data-original-title='Disable The Certificate ' >Deleted...</a>");
							// console.log(data);
							// $('#loader').hide();
							// $('.summaryFee').css('opacity', '5');
							// $('#feeGenerationReport').html(data);
  //             onchange_student_selection(studentId_global);

	// 					}
	// 				});

  //       }
  //   });
	// }

  function toggle_parent_visibility(parent_visibility,id){
    let visibility=0;
    let popup_html='';
    if(parent_visibility=='Yes'){
      popup_html="Do you want to make it hidden for parent?";
    }else{
      popup_html="Do you want to make it visibile to parent?";
      visibility=1;
    }
      Swal.fire({
            title: popup_html,
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "No",
        }).then((result) => {
            if (result.isConfirmed) {
              $.ajax({
                url: '<?php echo site_url('student/Certificates_controller/toggle_parent_visibility'); ?>',
                type: "post",
                data: {visibility,id},
                success(data) {
                    var parsed_data = $.parseJSON(data);
                    onchange_student_selection(studentId_global);
                    Swal.fire("Done!", "", "success");
                }
            });
            }
        });
  }

  $("#getIssuedCertificate").click(function(){
  getCertificate();
});
btn_status=document.getElementById("getIssuedCertificate");
  function getCertificate(){
    btn_status.disabled=true;
    btn_status.value="Please wait...";
    var admin_no = $("#admission_no").val().trim();
    console.log(admin_no);
    if(admin_no==''){
      Swal.fire({
        icon: "warning",
        title: "Admission number is requried.",
      });
      btn_status.disabled=false;
      btn_status.value="Get";
      return ;
    }
    $("#data_table").html(`<center><div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
      </div></center>`);
    $('#staff_studentid').val('');

    $.ajax({
        url: '<?php echo site_url('student/Certificates_controller/get_student_issued_certificates_admission_no'); ?>',
        data: {admin_no: admin_no},
        type: "post",
        success: function (data) {
          btn_status.disabled=false;
          btn_status.value="Get";
            var chrdata = $.parseJSON(data);
            html = construct_issued_certificates(chrdata);     
            $('#new_certificate_button').html('<a href="<?php echo site_url('student/Certificates_controller/generate_ceritificates/'); ?>'+chrdata['student_id']+'" class="btn btn-warning" style="float: right;"> Issue New Certificate</a>');
            $('#data_table').html(html);  
            $('#data_table_certificates').DataTable( {
                  ordering:false,
                  scrollY :'40vh',
                  "language": {
                          "search": "",
                          "searchPlaceholder": "Enter Search..."
                      },
                      "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                      "pageLength": 10,
                      dom: 'lBfrtip',
                      buttons: [
                      ]
                  } ); 
        }
    });
  }

  function viewPdf(pdf_url){
    // Set the PDF URL in the iframe
    document.getElementById('pdfViewer').src = pdf_url;
    console.log(pdf_url);

    // Show the modal
    const pdfModal = new bootstrap.Modal(document.getElementById('pdfModal'));
    pdfModal.show();
  }

  
function download_pdf(name,file_path,student_name){
    Swal.fire({
            title: "Download certificate of "+name+" ?",
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "No",
            }).then((result) => {
                if (result.isConfirmed) {
                    const downloadUrl = file_path;
                    const fileName = student_name+" "+name+" certificate.pdf";
                    
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', downloadUrl, true);
                    xhr.responseType = 'blob';

                    xhr.onload = function () {
                        if (xhr.status === 200) {
                            const blob = new Blob([xhr.response], { type: xhr.getResponseHeader('Content-Type') });
                            const a = document.createElement('a');
                            a.href = window.URL.createObjectURL(blob);
                            a.download = fileName;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            window.URL.revokeObjectURL(a.href);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Download Failed!',
                                text: 'Error downloading the file.',
                                timer: 1500,
                                showConfirmButton: false
                            });
                        }
                    };

                    xhr.onerror = function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Download Failed!',
                            text: 'Network error occurred.',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    };

                    xhr.send();
                }
        });
}

  function change_status(status, certificate_id,template_name){
    let pdf_status=0;
    let popup_html='';
    if(status=='Active'){
      popup_html="Do you want to make "+template_name +" Inactive?";
    }else{
      popup_html="Do you want to make "+template_name +" Active?";
      pdf_status=1;
    }
      Swal.fire({
            title: popup_html,
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "No",
        }).then((result) => {
            if (result.isConfirmed) {
              $.ajax({
                url: '<?php echo site_url('student/Certificates_controller/toggle_pdf_status'); ?>',
                type: "post",
                data: {pdf_status,certificate_id},
                success(data) {
                    var parsed_data = $.parseJSON(data);
                    onchange_student_selection(studentId_global);
                    Swal.fire("Done!", "", "success");
                }
            });
            }
        });
  }

</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style type="text/css">
  #staff_studentidautocomplete-list{
    height: 120px;
    overflow: scroll;
}
.input-group .form-control{
  z-index: auto;
}
.btn{
    border-radius: 4px;
  }

  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: auto ;
    width: 61%;
    height: 300px
  }
  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
  /* styles for DataTable end */
  .dataTables_scrollBody{
    margin-top: -13px;
  }
</style>