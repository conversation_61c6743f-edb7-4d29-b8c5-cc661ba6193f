<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard_controller extends CI_Controller {
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('parentdashboard', 'refresh');
		}
	
	}

	public function index() {
		
		$data['children'] = [];
		
		// Get attendance summary
		$data['attendance_summary'] = [];
		
		// Get upcoming events
		$data['upcoming_events'] = [];
		
		// Get recent communications
		$data['recent_communications'] = [];
		
		// Get fee status
		$data['fee_status'] = [];
		
		$data['main_content'] = 'dashboard/parent_v3/dashboard';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}

	public function profile() {
		$data['main_content'] = 'dashboard/parent_v3/profile';
		$this->load->view('dashboard/parent_v3/inc/index', $data);
	}
}

?>
