<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript" src="<?php echo site_url('assets/js/morris.js') ?>"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<?php $this->load->view("msm_v3/scripts/color_theme") ?>

<script>
    var json_school_list = '<?php echo $json_school_list ?>';
    var school_list = JSON.parse(json_school_list);
    var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';
    const overall_admission_array = [];
    const overall_leads_array = [];
    const overall_statistics = [];

    function waitOneSecondSync() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve();
            }, 400);
        });
    }

    $(document).ready(async function () {
        google.charts.load("current", {packages: ["corechart", "bar"]});
        $(".staff_tb_btn").css("display", "block");
        $(".top_bar_staff_count").css("display", "block");
        $(".overview_tb_dropdown").css("display", "block");
        $(".overview_tb_btn").css("display", "none");

        load_theme();
        sessionStorage.removeItem('msm_selectedSchools');
        var today = new Date().toISOString().split('T')[0];
        document.getElementById('attendance_date').value = today;
        display_staff_widget(school_list, attendance_date);
        await waitOneSecondSync();
        display_schools_dropdown_staff(school_list);
        display_staff_attendance_trend_widget(school_list);
        await waitOneSecondSync();
        display_staff_gender_widget(school_list);
        await waitOneSecondSync();
        // display_schools_dropdown_staff_qual(school_list);
        display_staff_qualification_widget(school_list);
        await waitOneSecondSync();
        display_schools_dropdown_staff_attr(school_list, default_acad_year);
        display_staff_attrition_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_staff_birthday(school_list, default_acad_year);
        display_staff_birthday_widget(school_list);
    });

    const selectElement = document.getElementById('acad_select');
    selectElement.value = default_acad_year;
    selectElement.addEventListener('change', function() {
        default_acad_year = selectElement.value;
        sessionStorage.setItem("msm_acad_year", selectElement.value);

        display_staff_attrition_widget(school_list, default_acad_year);
    });

    $("#attendance_date").on("change", function () {
        const newDate = $(this).val();
        display_staff_widget(school_list, newDate);
    });
    
    /* Staff Attendance Graph*/
    function display_staff_widget(school_list, attendance_date, type="Bar") {
        $('#staff_attendance_graph').html('<center>Loading Staff Attendance Statistics...</center>');
        $("#staff_att_per").html('...');
        const overall_staff_attendance = [];
        attendance_date = $("#attendance_date").val();
        // Call each school's data
        var total_checkin = 0;
        const mapLoop = async () => {
        const promises = school_list.map(async (school) => {
            try {
            const response = await get_staff_attendance_data(school.school_code, school.school_domain, attendance_date);
            if (response) {
                const totalStaff = response.total_staff || 0;
                const check_in = parseInt(response.check_in) || 0;
                const leave = parseInt(response.leave) || 0;
                total_checkin += check_in;
                overall_staff_attendance.push([
                school.school_code.toUpperCase(),
                totalStaff,
                check_in,
                leave,
                response.staffs_on_leave
                ]);
            }
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);

        // Arrange in a sequence
        const temp_array = [];
        school_list.forEach((sl) => {
            overall_staff_attendance.forEach((ola) => {
            if (sl.school_code.toUpperCase() === ola[0]) {
                temp_array.push(ola);
                return false;
            }
            });
        });
        var total_staff_count = 0;
        temp_array.forEach(obj => {
            total_staff_count += parseInt(obj[1]);
        })

        var total_checkin_percent = (total_checkin / total_staff_count) * 100;
        $('#top_bar_staff_count').html(`${total_staff_count}`);
        $('#staff_count_stat').html(`${total_staff_count}`);
        $("#staff_att_per").html(`${total_checkin_percent.toFixed(1)}`);
        _construct_staff_view(temp_array.slice(), type);
        _construct_staff_counts_table(temp_array);
        };

        google.charts.setOnLoadCallback(mapLoop);
    }

    function get_staff_attendance_data(school_code, school_domain, attendance_date) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'from_date': attendance_date,
                    'api': 'get_staff_attendance_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_staff_view(input_array, type) {
        var dataArray = [['School Code', 'Checked In', { role: 'annotation' }, 'Not Checked In', { role: 'annotation' }, 'Leave', { role: 'annotation' }, 'staffs']];
        input_array.forEach(function(item) {
        var schoolCode = item[0] || '';
        var checkIn = parseInt(item[2]) || 0;
        var totalStaff = parseInt(item[1]) || 0;
        var leave = parseInt(item[3]) || 0;
        var checkInAnnotation = (checkIn !== 0) ? checkIn.toString() : null;
        var totalStaffAnnotation = (totalStaff !== 0) ? totalStaff.toString() : null;
        var leaveAnnotation = (leave !== 0) ? leave.toString() : null;
        dataArray.push([schoolCode, checkIn, checkInAnnotation, totalStaff-checkIn-leave < 0 ? 0 : totalStaff-checkIn-leave, totalStaffAnnotation, leave, leaveAnnotation, item[4]]);
        });
        google.charts.load('current', { packages: ['corechart','gauge'] });
        google.charts.setOnLoadCallback(drawChart);

        function drawChart() {
        var data = google.visualization.arrayToDataTable(dataArray);
        var options;
        var view;
        var formatPercent = new google.visualization.NumberFormat({
            pattern: '#,##0%'
        });
        options = {
            isStacked: 'percent',
            height: 450,
            // width: 1400,
            chartArea: {left: '20%', top: '15%', width: '70%', height: '80%', bottom: '15%'},
            hAxis: {
                title: "% Attendance",
                titleTextStyle: {
                    color: '#1450A3',
                    fontName: 'Arial',
                    fontSize: '16',
                    bold: true,
                    italic: false
                },
                bar: { groupWidth: "10%" },
                axisTitlesPosition: 'out',
            },
            vAxis: {
                    title: 'Instituitions',
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                },
            annotations: {
                alwaysOutside: false,
                textStyle: {
                fontSize: 10,
                bold: true
                },
            },
            series: {
                0: {
                    color: color3,
                    areaOpacity: 0.85,
                    visibleInLegend: true,
                    annotations: {
                    stem: {
                        color: 'transparent',
                        length: 16
                    }
                    },
                },
                1: {
                    color: neg_color,
                    areaOpacity: 0.85,
                    visibleInLegend: true,
                    annotations: {
                    stem: {
                        color: 'transparent',
                        length: 16
                    }
                    },
                },
                2: {
                    color: color2,
                    areaOpacity: 0.85,
                    visibleInLegend: true,
                    annotations: {
                    stem: {
                        color: 'transparent',
                        length: 16
                    }
                    },
                }
            },
            legend: {
                position: 'top', alignment: "center"
            }
        };

        var view = new google.visualization.DataView(data);
        view.setColumns([
            0,
            1,
            {
            sourceColumn: 1,
            type: "string",
            calc: function(dt, row) {
                return ''+dt.getValue(row, 1);
            },
            role: "annotation",
            },
            3,
            {
            sourceColumn: 3,
            type: "string",
            calc: function(dt, row) {
                return ''+dt.getValue(row, 3);
            },
            role: "annotation",
            },
            5,
            {
            sourceColumn: 5,
            type: "string",
            calc: function(dt, row) {
                return ''+dt.getValue(row, 5);
            },
            role: "annotation",
            }
        ]);

        var chartType = type+"Chart";
        var chartConstructor = google.visualization[chartType];
        var chart = new chartConstructor(document.getElementById('staff_attendance_graph'));
        google.visualization.events.addListener(chart, 'select', function() {
            var selectedItem = chart.getSelection()[0];
            if (selectedItem) {
                var staffs = data.getValue(selectedItem.row, 7);
                var html = `<table class="table table-bordered">
                            <thead style="color: #0F256E">
                                <th style="color: #0F256E">Staff ID</th>
                                <th style="color: #0F256E">Name</th>
                                <th style="color: #0F256E">Leave Type</th>
                            </thead>
                            <tbody>`;
                if(staffs.length > 0){
                    for (const element of staffs) {
                        html += `<tr>
                                    <td>${element.staff_id}</td>
                                    <td>${element.first_name}</td>
                                    <td>${element.category_name}</td>
                                </tr>
                            `;
                    }
                    html += `</tbody>
                        </table>`;
                    $('#modal-content').html(html);
                    $('#infoModal').modal('show');
                }
                else{
                    $('#modal-content').text(`No Staffs are on Leave`);
                    $('#infoModal').modal('show');
                }
            }
        });
        chart.draw(view, options);
        }
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Checked In</span>
                    <div style="width: 15px; height: 15px; background-color: ${neg_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Not Checked In</span>   
                    </div>`;
        $("#staff_attendance_legend_div").html(html);
    }

    function _construct_staff_counts_table(input_array) {
        var html = '';
        var i = 1;
        input_array.forEach(obj => {
            var res = obj[1]-obj[2]-obj[3];
            html += `
                <tr>
                    <td class="align-middle text-sm">${i}</td>
                    <td class="align-middle text-sm">${obj[0]}</td>
                    <td class="align-middle text-sm">${obj[1]}</td>
                    <td class="align-middle text-sm">${obj[2]}</td>
                    <td class="align-middle text-sm">${res}</td>
                    <td class="align-middle text-sm">${obj[3]}</td>
                </tr>
            `;
            i ++;
        });
        $('#staff_attendance_table').html(html);
    }

    function close_info_modal(){
        $('#infoModal').modal('hide');
    }

    /* Staff Attendance Trend*/
    function display_schools_dropdown_staff(school_list) {
        let html = '';
        school_list.forEach((school) => {
        html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_staff'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            const storedSchools = sessionStorage.getItem('msm_selectedSchools');
            if (storedSchools) {
                const selectedSchools = JSON.parse(storedSchools);
                selectedSchools.forEach(school => {
                    const optionValue = `${school.school_code}-${school.school_domain}`;
                    const option = Array.from(dropdown.options).find(opt => opt.value === optionValue);
                    if (option) {
                        option.selected = true;
                    }
                });
            }
            dropdown.addEventListener('change', change_graph_staff);
        }
        });
    }

    function change_graph_staff(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        sessionStorage.setItem('msm_selectedSchools', JSON.stringify(selectedSchools));
        display_staff_attendance_trend_widget(selectedSchools); // Pass acad_year as a parameter
    }

    function display_staff_attendance_trend_widget(school_list) {
        disableTableButtons();
        $("#staff_attendance_trend_graph").html("<center>Loading Staff Attendance Trend...</center>");
        const staff_attendance_trend_statistics = [];
        const storedSchools = sessionStorage.getItem('msm_selectedSchools');
        if (storedSchools) {
            school_list = JSON.parse(storedSchools);
        }
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const response = await get_staff_attendance_trend_data(school.school_code, school.school_domain);
                    //Convert to array
                    const response_arr = Object.keys(response).map(key => response[key]);
                    if (response_arr) {
                    response_arr.unshift(school.school_code.toUpperCase());
                        staff_attendance_trend_statistics.push(response_arr);
                    }
                } catch (err) {
                    console.log(err);
                }
            });
            await Promise.all(promises);
            const temp_array_satw = [school_list[0]].map(obj => {
                const foundSchool = staff_attendance_trend_statistics.find(stat => stat[0] === obj.school_code.toUpperCase());
                return foundSchool || [obj.school_code.toUpperCase()];
            });
            const final_array = [['Day', ...[school_list[0]].map(obj => obj.school_code.toUpperCase())]];
            const final_array_str = [['Day', ...[school_list[0]].map(obj => obj.school_code.toUpperCase())]];
            for (let i = 1; i < temp_array_satw[0].length; i++) {
                const dayRow = [temp_array_satw[0][i].created_on];
                const dayRowStr = [temp_array_satw[0][i].created_on];
                for (let j = 0; j < temp_array_satw.length; j++) {
                    if (temp_array_satw[j][i].total_count != 0) {
                    var perc = parseInt(temp_array_satw[j][i].present_count || 0) / parseInt(temp_array_satw[j][i].total_count) * 100;
                    dayRow.push(parseInt(perc.toFixed(0)));
                    dayRowStr.push(`${temp_array_satw[j][i].present_count} / ${temp_array_satw[j][i].total_count} (${parseInt(perc.toFixed(0))}%)`);
                    } else {
                    dayRow.push(0);
                    dayRowStr.push(0);
                    }
                }
                final_array.push(dayRow);
                final_array_str.push(dayRowStr);
            }
            _construct_staff_attendance_trend_view(final_array.slice(), [school_list[0]].length);
            _construct_staff_attendance_trend_counts_table(final_array_str);
        };
        mapLoop();
    }

    function get_staff_attendance_trend_data(school_code, school_domain) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'api': 'get_staff_attendance_trend_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_staff_attendance_trend_view(input_array, no_of_schools) {
        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(input_array);
            var options = {
                height: 330,
                curveType: 'function',
                // width: 1400,
                chartArea: {left: '15%', top: '15%', width: '75%', height: '80%', bottom: '10%'},
                hAxis: {
                    title: "Date",
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                    bar: { groupWidth: "90%" },
                    axisTitlesPosition: 'out',
                    curveType: 'function',
                    legend: {
                    position: 'labeled'
                    }
                },
                vAxis: {
                    title: '% Staff',
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                },
                legend: {
                    position: 'top', alignment: 'center'
                }
            };

            var view = new google.visualization.DataView(data);
            var columns = [0, 1];
            for (var i = 0; i < no_of_schools; i++) {
                columns.push({
                    sourceColumn: i + 1,
                    type: "number",
                    role: "annotation",
                    color: "#000000"
                });
                if (i < (no_of_schools-1)) {
                    columns.push(i + 2);
                }
            }
            view.setColumns(columns);
            var chart = new google.visualization.LineChart(document.getElementById('staff_attendance_trend_graph'));
            chart.draw(view, options);
        });
    }

    function _construct_staff_attendance_trend_counts_table(input_array) {
        enableTableButtons();
        var html = `<table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">`;
        html += `<thead style="color: #0F256E">`;
        html += `<th class="font-weight-bolder opacity-7">#</th>`;
        html += `<th class="font-weight-bolder opacity-7">INSTITUTION</th>`;

        for(i=1;i<input_array.length;i++) {
            html += `<th class="font-weight-bolder opacity-7">${input_array[i][0]}</th>`;
        }
        html += `</thead>`;
        html += `<tbody>`;

        for(j=1;j<input_array[0].length;j++) {
            html += `<tr><td class="align-middle" style="color:#510540">${j}</td>`;
            html += `<td class="align-middle">${input_array[0][j]}</td>`;
            for (i=1;i<input_array.length;i++) {
            html += `<td class="align-middle">${input_array[i][j]}</u></td>`;
            }
            html += '</tr>';
        }

        html += `</tbody>`;
        html += `</table>`;

        $('#staff_attendance_trend_table_card').html(html);
    }

    /* Staff Gender*/
    function display_staff_gender_widget(school_list) {
        disableTableButtons();
        $("#staff_gender_graph").html("<center>Loading Staff Gender Statistics...</center>");

        const overall_staff_statistics = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const num_promise = await get_staff_gender_data(school.school_code, school.school_domain)
            .then((response) => {
                if (!response) return true;
                overall_staff_statistics[overall_staff_statistics.length] = [school.school_code.toUpperCase(), parseInt(response.total_staff), parseInt(response.males), parseInt(response.females)];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        //Arrange in a sequence
        var temp_array = [];
        school_list.forEach((sl) => {
            overall_staff_statistics.forEach((ola) => {
            if (sl.school_code.toUpperCase() == ola[0]) {
                temp_array.push(ola);
                return false;
            }
            });
        });

        var total_staff_count = 0;
        temp_array.forEach((obj) => {
            total_staff_count += parseInt(obj[1]);
        });

        _construct_staff_gender_view(temp_array.slice());
        _construct_staff_gender_table(temp_array);
        };
        mapLoop();
    }

    function get_staff_gender_data(school_code, school_domain) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            api: "get_staff_gender_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_staff_gender_view(input_array) {
        var dataArray = [["School Code", "Male", { role: "annotation" }, "Female", { role: "annotation" }]];
        input_array.forEach(function (item) {
        var schoolCode = item[0] || "";
        var maleCount = parseInt(item[2]) || 0;
        var femaleCount = parseInt(item[3]) || 0;
        dataArray.push([schoolCode, maleCount, maleCount.toString(), femaleCount, femaleCount.toString()]);
        });
        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback( function() {
        var data = google.visualization.arrayToDataTable(dataArray);

        var options = {
            isStacked: "percent",
            height: 350,
            chartArea: {  left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%"  },
            hAxis: {
            title: "Staff %",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            bar: { groupWidth: "80%" },
            axisTitlesPosition: "out",
            },
            vAxis: {
            title: "Institutions",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            },
            annotations: {
            alwaysOutside: false,
            textStyle: {
                fontSize: 12,
                color: "black",
                bold: true,
            },
            },
            series: {
            0: {
                color: boys_color,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                stem: {
                    color: "transparent",
                    length: 0,
                },
                },
            },
            1: {
                color: girls_color,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: false,
                stem: {
                    color: "transparent",
                    length: 16,
                },
                },
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        var formatPercent = new google.visualization.NumberFormat({
            pattern: "#,##0%",
        });

        var view = new google.visualization.DataView(data);
        view.setColumns([
            0,
            1,
            {
            sourceColumn: 1,
            type: "string",
            calc: function (dt, row) {
                var male = dt.getValue(row, 1);
                var female = dt.getValue(row, 3);
                var total = male + female;
                var percentage = (male / total) * 100 || 0;
                return male + " (" + formatPercent.formatValue(percentage / 100) + ")";
            },
            role: "annotation",
            },
            3,
            {
            sourceColumn: 3,
            type: "string",
            calc: function (dt, row) {
                var male = dt.getValue(row, 1);
                var female = dt.getValue(row, 3);
                var total = male + female;
                var percentage = (female / total) * 100 || 0;
                return female + " (" + formatPercent.formatValue(percentage / 100) + ")";
            },
            role: "annotation",
            },
        ]);

        var chart = new google.visualization.BarChart(document.getElementById("staff_gender_graph"));
        chart.draw(view, options);
        })
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${boys_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Male</span>
                    <div style="width: 15px; height: 15px; background-color: ${girls_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Female</span>
                    </div>`;
        $("#staff_gender_legend_div").html(html);
    }

    function _construct_staff_gender_table(input_array) {
        enableTableButtons();
        var html = "";
        var i = 1;
        var totalMale = 0;
        var totalFemale = 0;
        var grandTotal = 0;
        input_array.forEach((obj) => {
            html += `
                        <tr>
                            <td>${i}</td>
                            <td>${obj[0]}</td>
                            <td>${obj[2]}</td>
                            <td>${obj[3]}</td>
                            <td>${obj[1]}</td>
                        </tr>
                    `;
            totalMale += parseInt(obj[2] || 0);
            totalFemale += parseInt(obj[3] || 0);
            grandTotal += parseInt(obj[1] || 0);
            i++;
        });
        html += `
            <tr>
                <td colspan="2" style="text-align:right;"><strong>Total</strong></td>
                <td><strong>${totalMale}</strong></td>
                <td><strong>${totalFemale}</strong></td>
                <td><strong>${grandTotal}</strong></td>
            </tr>
        `;
        $("#staff_gender_table").html(html);
    }

    // function display_schools_dropdown_staff_qual(school_list){
    //     let html = '';
    //     school_list.forEach((school) => {
    //     html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
    //     });
    //     const dropdowns = ['dropdownSchool_staff_qual'];
    //     dropdowns.forEach((dropdownId) => {
    //     const dropdown = document.getElementById(dropdownId);
    //     //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
    //     if (dropdown){
    //         dropdown.innerHTML = html;
    //         const storedSchools = sessionStorage.getItem('msm_selectedSchools');
    //         if (storedSchools) {
    //             const selectedSchools = JSON.parse(storedSchools);
    //             selectedSchools.forEach(school => {
    //                 const optionValue = `${school.school_code}-${school.school_domain}`;
    //                 const option = Array.from(dropdown.options).find(opt => opt.value === optionValue);
    //                 if (option) {
    //                     option.selected = true;
    //                 }
    //             });
    //         }
    //         dropdown.addEventListener('change', change_graph_staff_qua);
    //     }
    //     });
    // }

    // function change_graph_staff_qua(event) {
    //     const dropdown = event.target;
    //     const selectedOptions = Array.from(dropdown.selectedOptions);
    //     const selectedSchools = selectedOptions.map(option => {
    //     const [schoolCode, schoolDomain] = option.value.split('-');
    //     return { school_code: schoolCode, school_domain: schoolDomain };
    //     });
    //     sessionStorage.setItem('msm_selectedSchools', JSON.stringify(selectedSchools));
    //     display_staff_qualification_widget(selectedSchools); // Pass acad_year as a parameter
    // }

    /* Staff Qualification*/  
    function display_staff_qualification_widget(school_list) {
        disableTableButtons();
        // $("#staff_qualification_graph").html("<center>Loading Staff Qualifications Statistics...</center>");
        $("#staff_qualification_table").html("<center>Loading Staff Qualifications Statistics...</center>");
        // const storedSchools = sessionStorage.getItem('msm_selectedSchools');
        // if (storedSchools) {
        //     school_list = JSON.parse(storedSchools);
        // }
        const overall_staff_quals = [];
        
        // Call each school's data
        const mapLoop = async () => {
            const promises = await school_list.map(async (school) => {
                const num_promise = await get_staff_qualification_data(school.school_code, school.school_domain)
                .then((response) => {
                    if (!response) return true;
                    overall_staff_quals[overall_staff_quals.length] = [school.school_code.toUpperCase(), response];
                    return true;
                })
                .catch((err) => {
                    console.log(err);
                    return false;
                });
            });
            await Promise.all(promises);

            // Arrange in a sequence
            // var temp_array = [];
            // school_list.forEach((sl) => {
            //     overall_staff_quals.forEach((ola) => {
            //     if (sl.school_code.toUpperCase() == ola[0]) {
            //         temp_array.push(ola);
            //         return false;
            //     }
            //     });
            // });

            var nestedArray = [];
            
            overall_staff_quals.forEach((sl) => {
                const schoolCode = sl[0];
                
                sl[1].forEach((pm) => {
                    const degreeType = pm.degree_type;
                    const qualCount = parseInt(pm.qual_count);

                    if (!nestedArray[degreeType]) {
                        nestedArray[degreeType] = {};
                    }
                    nestedArray[degreeType][schoolCode] = (nestedArray[degreeType][schoolCode] || 0) + qualCount;
                });
            });
            // var degreeTypeCounts = {};
            // overall_staff_quals.forEach((sl) => {
            //     sl[1].forEach((pm) => {
            //     var degreeType = pm.degree_type;
            //     var qualCount = parseInt(pm.qual_count);

            //     if (!isNaN(qualCount)) {
            //         if (degreeTypeCounts.hasOwnProperty(degreeType)) {
            //         degreeTypeCounts[degreeType] += qualCount;
            //         } else {
            //         degreeTypeCounts[degreeType] = qualCount;
            //         }
            //     }
            //     });
            // });
            // var degreeTypeArray = Object.entries(degreeTypeCounts);
            // nestedArray.push(degreeTypeArray);
            // _construct_staff_qualification_view(nestedArray);
            _construct_staff_qualification_table_new(nestedArray, school_list);
        };

        mapLoop();
    }

    function get_staff_qualification_data(school_code, school_domain) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
                school_code: school_code,
                school_domain: school_domain,
                api: "get_staff_qualification_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_staff_qualification_view(input_array) {
        var dataArray = [["Degree Type", "Count", { role: "annotation" }]];

        input_array.forEach((item) => {
            item.forEach((type) => {
                var degreeType = type[0];
                var count = type[1];
                dataArray.push([degreeType, count, count.toString()]);
            });
        });

        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback(function() {
        var data = google.visualization.arrayToDataTable(dataArray);

        var options = {
            height: 350,
            chartArea: {  left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%"  },
            hAxis: {
            title: "Degree Type",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            axisTitlesPosition: "in",
            },
            vAxis: {
            title: "Count",
            titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
            },
            },
            series: {
            0: {
                color: color1,
                areaOpacity: 0.25,
                visibleInLegend: true,
                annotations: {
                alwaysOutside: true,
                textStyle: {
                    fontSize: 12,
                    bold: true,
                    italic: false,
                },
                },
            },
            },
            legend: {
            position: "top", alignment: "center"
            },
        };

        var chart = new google.visualization.ColumnChart(document.getElementById("staff_qualification_graph"));
        chart.draw(data, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Count</span>
                    </div>`;
        $("#staff_qualification_legend_div").html(html);
    }

    function _construct_staff_qualification_table(nestedArray, school_list) {
        enableTableButtons();
        var html = "";
        var i = 1;
        input_array.forEach((obj) => {
            obj.forEach((ins) => {
                html += `
                    <tr>
                        <td>${i}</td>
                        <td>${ins[0]}</td>
                        <td>${ins[1]}</td>
                    </tr>
                    `;
                i++;
            });
        });
        $("#staff_qualification_table").html(html);
    }

    function _construct_staff_qualification_table_new(nestedArray, school_list){
        console.log(nestedArray);
        
        var html = `<table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">`;
        html += `<thead style="color: #0F256E">`;
        html += `<tr>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">#</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Qualification</th>`;

        school_list.forEach((school) => {
            html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">${school.school_code.toUpperCase()}</th>`;
        });

        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Total</th>`;
        html += `</tr>`;
        html += `</thead>`;

        const qualificationTypes = ["Doctorate", "Post Graduate", "Graduate", "Diploma"];
        let schoolTotals = Array(school_list.length).fill(0);
        let grandTotal = 0;

        html += `<tbody>`;

        qualificationTypes.forEach((qualification, index) => {
            let rowTotal = 0;
            html += `<tr>`;
            html += `<td>${index + 1}</td>`;
            html += `<td>${qualification}</td>`;

            school_list.forEach((school, schoolIndex) => {
                const schoolCode = school.school_code.toUpperCase();
                const schoolCount = Number(nestedArray[qualification]?.[schoolCode]) || 0;

                html += `<td>${schoolCount}</td>`;
                schoolTotals[schoolIndex] += schoolCount;
                rowTotal += schoolCount;
            });

            html += `<td><strong>${rowTotal}</strong></td>`;
            html += `</tr>`;
            grandTotal += rowTotal;
        });

        html += `<tr>`;
        html += `<td colspan="2" style="text-align: right;"><strong>Total</strong></td>`;

        schoolTotals.forEach((schoolTotal) => {
            html += `<td><strong>${schoolTotal}</strong></td>`;
        });

        html += `<td><strong>${grandTotal}</strong></td>`;
        html += `</tr>`;

        html += `</tbody></table>`;

        $("#staff_qualification_table").html(html);
    }

    function _get_array_value(arr, value) {
        var count = 0;
        arr.forEach((item) => {
        if (item.degree_type == value) {
            count = parseInt(item.qual_count);
            return;
        }
        });
        return count;
    }

    /*Staff Attrition*/
    function display_schools_dropdown_staff_attr(school_list, acad_year) {
        disableTableButtons();
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_staff_attr'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            const storedSchools = sessionStorage.getItem('msm_selectedSchools');
            if (storedSchools) {
                const selectedSchools = JSON.parse(storedSchools);
                selectedSchools.forEach(school => {
                    const optionValue = `${school.school_code}-${school.school_domain}`;
                    const option = Array.from(dropdown.options).find(opt => opt.value === optionValue);
                    if (option) {
                        option.selected = true;
                    }
                });
            }
            dropdown.addEventListener('change', (event) => change_graph_staff_attr(event, acad_year));
        }
        });
    }

    function change_graph_staff_attr(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        sessionStorage.setItem('msm_selectedSchools', JSON.stringify(selectedSchools));
        display_staff_attrition_widget(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_staff_attrition_widget(school_list, acad_year) {
        $("#staff_attrition_trend_graph").html("<center>Loading Staff Attrition Statistics...</center>");

        const countsPerMonth = Array(12).fill(0); // Initialize an array to store counts for each month

        // Call each school's data
        const mapLoop = async () => {
        const promises = [school_list[0]].map(async (school) => {
            try {
            const response = await get_staff_attrition_data(school.school_code, school.school_domain, acad_year);
            if (response) {
                response.forEach((item) => {
                const month = parseInt(item.month || "0"); // Convert month to integer (1-12)
                const count = parseInt(item.count || "0"); // Convert count to integer

                // Update the count for the corresponding month
                countsPerMonth[month - 1] += count;
                });
            }
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);

        // Create the array for charting, excluding the first array
        const temp_array = [["Month", "Count"]];
        const inp_array = [];
        const monthNames = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        for (let i = 1; i <= 12; i++) {
            temp_array.push([monthNames[i], countsPerMonth[i - 1]]);
            inp_array.push([monthNames[i], countsPerMonth[i - 1]]);
        }
        _construct_staff_attrition_view(temp_array.slice());
        _construct_staff_attrition_table(inp_array.slice());
        };

        google.charts.setOnLoadCallback(mapLoop);
    }

    function get_staff_attrition_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_staff_attrition_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_staff_attrition_view(input_array) {
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const dataArray = input_array.slice(1);

        // Create the data table
        const data = new google.visualization.DataTable();
        data.addColumn("string", "Month");
        data.addColumn("number", "Count");
        data.addRows(dataArray);

        // Set chart options
        const options = {
        height: 350,
        chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
        hAxis: {
            title: "Month",
            titleTextStyle: {
            color: "#1450A3",
            fontName: "Arial",
            fontSize: "16",
            bold: true,
            italic: false,
            },
        },
        vAxis: {
            title: "Attrition Count",
            titleTextStyle: {
            color: "#1450A3",
            fontName: "Arial",
            fontSize: "16",
            bold: true,
            italic: false,
            },
        },
        pointSize: 10, // Set the point size
        series: {
            0: { color: color1, type: "line" }, // Color for the line
        },
        annotations: {
            textStyle: {
            fontSize: 12,
            color: "black",
            bold: true,
            },
            stem: {
            length: 10,
            color: "transparent",
            },
        },
        legend: {
            position: "top", alignment: "center"
        },
        };
        var view = new google.visualization.DataView(data);
        view.setColumns([
        0,
        1,
        {
            sourceColumn: 1,
            type: "number",
            role: "annotation",
            color: "#000000",
        },
        ]);

        // Create and draw the chart
        const chart = new google.visualization.LineChart(document.getElementById("staff_attrition_trend_graph"));
        chart.draw(view, options);
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Count</span>
                    </div>`;
        $("#staff_attrition_legend_div").html(html);
    }

    function _construct_staff_attrition_table(input_array) {
        enableTableButtons();
        // console.log(input_array);
        var html = 0;
        input_array.forEach(function(item) {
            html += `
            <tr>
                <td>${item[0]}</td>
                <td>${item[1]}</td>
            </tr>
        `;
        });
        $("#staff_attrition_table").html(html);
    }

    /*Staff Birthday*/
    function display_schools_dropdown_staff_birthday(school_list, acad_year) {
        disableTableButtons();
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_staff_birthday'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            const storedSchools = sessionStorage.getItem('msm_selectedSchools');
            if (storedSchools) {
                const selectedSchools = JSON.parse(storedSchools);
                selectedSchools.forEach(school => {
                    const optionValue = `${school.school_code}-${school.school_domain}`;
                    const option = Array.from(dropdown.options).find(opt => opt.value === optionValue);
                    if (option) {
                        option.selected = true;
                    }
                });
            }
            dropdown.addEventListener('change', (event) => change_graph_staff_birthday(event, acad_year));
        }
        });
    }

    function change_graph_staff_birthday(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        sessionStorage.setItem('msm_selectedSchools', JSON.stringify(selectedSchools));
        display_staff_birthday_widget(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_staff_birthday_widget(school_list) {
        $("#staff_birthday_pie_card").html("<center>Loading Staff Birthday Statistics...</center>");
        
        // Initialize an empty array to store birthday data
        var birthday_data = [];

        // Function to process each school's data
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const response = await get_staff_birthday_list(school.school_code, school.school_domain);
                    if (response) {
                        // console.log(response);
                        birthday_data.push({
                            school_code: school.school_code,
                            staff_just_celebrated: response.staff_just_celebrated,
                            staff_today_celebrated: response.staff_today_celebrated,
                            staff_tomorrow_celebrating: response.staff_tomorrow_celebrating,
                            staff_upcoming_celebrating: response.staff_upcoming_celebrating
                        });
                    }
                } catch (err) {
                    console.error(err);
                }
            });
            
            // Wait for all promises to resolve
            await Promise.all(promises);
            console.log(birthday_data);
            displayStaffBirthdayList(birthday_data);
        };
        mapLoop();
    }

    function get_staff_birthday_list(school_code, school_domain) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            api: "staff_birthday_list",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function displayStaffBirthdayList(birthdayData) {
        const container = document.getElementById("staff_birthday_pie_card");
        container.innerHTML = "";

        function createStaffTableRows(staffList, label, bgColor) {
            if (!staffList.length) {
                return `
                    <tr>
                        <td style=><b>${label}</b></td>
                        <td colspan="2" style="text-align: center;">No Data</td>
                    </tr>
                `;
            }

            let rows = '';
            staffList.forEach((staff, index) => {
                rows += `
                    <tr>
                        ${index === 0 ? `<td rowspan="${staffList.length}" style="vertical-align: middle;"><b>${label}</b></td>` : ''}
                        <td>${staff.staff_name}</td>
                        <td>${staff.dobDisplay}</td>
                    </tr>
                `;
            });
            return rows;
        }

        birthdayData.forEach(school => {
            let tableHTML = `
                <h3 style="text-align: center; margin-bottom: 20px;">${school.school_code.toUpperCase()}</h3>
                <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                    <thead style="color: #0F256E">
                        <tr>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Category</th>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Staff Name</th>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">DOB</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${createStaffTableRows(school.staff_just_celebrated, "Just Celebrated", pie_color1)}
                        ${createStaffTableRows(school.staff_today_celebrated, "Today", pie_color2)}
                        ${createStaffTableRows(school.staff_tomorrow_celebrating, "Tomorrow", pie_color3)}
                        ${createStaffTableRows(school.staff_upcoming_celebrating, "Upcoming", pie_color4)}
                    </tbody>
                </table>
            `;
            container.innerHTML += tableHTML;
        });
    }


    // Utility
    function flip_staff_view(type, id, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');
        switch(id){
            case 'staff_attendance_card':
                switch (type) {
                    case 'column_chart':
                        $('#staff_attendance_pie_card').css('display', 'block');
                        $('#staff_attendance_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#staff_attendance_table_card').css('display', 'block');
                        $('#staff_attendance_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'staff_attendance_trend_card':
                switch (type) {
                    case 'column_chart':
                        $('#staff_attendance_trend_pie_card').css('display', 'block');
                        $('#staff_attendance_trend_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#staff_attendance_trend_table_card').css('display', 'block');
                        $('#staff_attendance_trend_pie_card').css('display', 'none');
                    default:
                        break;
                }
                display_staff_attendance_trend_widget(school_list);
                break;
            case 'staff_gender_card':
                switch (type) {
                    case 'column_chart':
                        $('#staff_gender_pie_card').css('display', 'block');
                        $('#staff_gender_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#staff_gender_table_card').css('display', 'block');
                        $('#staff_gender_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'staff_qualification_card':
                switch (type) {
                    case 'column_chart':
                        $('#staff_qualification_pie_card').css('display', 'block');
                        $('#staff_qualification_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#staff_qualification_table_card').css('display', 'block');
                        $('#staff_qualification_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'staff_attrition_card':
                switch (type) {
                    case 'column_chart':
                        $('#staff_attrition_pie_card').css('display', 'block');
                        $('#staff_attrition_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#staff_attrition_table_card').css('display', 'block');
                        $('#staff_attrition_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
        }
        
    }

    function disableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.setAttribute('disabled', true);
        }
    }

    // Function to enable table buttons
    function enableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.removeAttribute('disabled');
        }
    }
</script>
