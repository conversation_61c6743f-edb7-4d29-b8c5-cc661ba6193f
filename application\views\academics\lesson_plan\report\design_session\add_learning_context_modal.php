<div class="modal fade" id="add_learning_context" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Learning Context</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="context_session_id" id="context_session_id">
                <div class="form-group">
                    <label for="l_context" class="control-label">Learning Context <font style="color: red;">*</font></label>
                    <textarea class="form-control" id="l_context" name="l_context" style="height: 11rem;"placeholder="Wanna Describe?"></textarea>
                    <span id="learningContectError" style="display; none;"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_learning_context_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_learning_context_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="update()">Update</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // $("#learning_context").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $("#l_context").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#learningContectError").hide();
            if ($("#l_context").val() == "") {
                $("#learningContectError").html("Please Enter Learning Context").css("color", "red").show();
                return;
            }
            updateLearningContext();
            loadLearningContext();
        }
    })

    function updateLearningContext() {
        const id = $("#context_session_id").val();
        const value = $("#l_context").val();
        const visible_to_students = $("#visible_learning_context_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/updateLearningContext"); ?>',
            type: "POST",
            data: { id, value, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_learning_context").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        getSessionData(id);
                        showMainModal();
                        loadLearningContext();
                    });
                } else {
                    $("#add_learning_context").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Something went wrong!",
                    }).then(() => {
                        $("#add_learning_context").modal('show');
                    });
                }
            },
            error(error) {
                console.log(err);
                $("#add_learning_context").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Something went wrong!",
                }).then(() => {
                    $("#add_learning_context").modal('show');
                });
            }
        })
    }

    function update() {
        $("#learningContectError").hide();
        if ($("#l_context").val() == "") {
            $("#learningContectError").html("Please Enter Learning Context").css("color", "red").show();
            return;
        }
        updateLearningContext();
        loadLearningContext();
    }
</script>