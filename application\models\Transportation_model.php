<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Transportation_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
        parent::__construct();
        $this->acad_year->loadAcadYearDataToSession();
        $this->yearId =  $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
        date_default_timezone_set("Asia/Kolkata");
    }

    public function Kolkata_time($time) {
        $time = date('Y-m-d h:i:s a', strtotime($time));
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime($time, $timezone);
        $dtobj = $date->format('H:i:s');
        return $dtobj;
    }

// Stops 
    public function add_stops() {
        $data_info = array(
            'name'=>$this->input->post('stopName'),
            'kilometer'=>$this->input->post('kilometer'),
            'time'=>$this->input->post('time'),

        );
        return $this->db->insert('transport_stops', $data_info);
    }

    public function getStopList() {
        $this->db->select('*');
        $this->db->from('transport_stops');
        return $this->db->get()->result();
    }

  
    public function editStopById($id){
        $this->db->select('*');
        $this->db->from('transport_stops');
        $this->db->where('id',$id);
        return $this->db->get()->row();
    }
    public function updateStop($id){
        $data_info = array(
            'name'=>$this->input->post('stopName'),
            'kilometer'=>$this->input->post('kilometer'),
            'time'=>$this->input->post('time'),
        );
            $this->db->where('id',$id);
        return $this->db->update('transport_stops', $data_info);
    }

    public function deleteStop($id){
                $this->db->where('id', $id);
        return $this->db->delete('transport_stops');
    }

// Fee Structure

    public function get_fee_masterDetails(){
        $structure_data =  $this->db->select('fs.stop_id,fs.pickup_mode,fs.amount,fs.gprs')
            ->from('transport_fee_structure fs')
          ->get()->result();
        $route_data = $this->db->select('rs.id,rs.name')
            ->from('transport_stops rs')
          ->get()->result();
        

        if (empty($structure_data)) {
          return array();
        }
          foreach ($route_data as &$fm) {
            $fcString = '';
            foreach ($structure_data as $fc) {
              if ($fm->id == $fc->stop_id) {
                if (!empty($fcString))
                  $fcString = $fcString . '<br>';
                $fcString = $fcString . $fc->pickup_mode . ': ' . $fc->amount;
              }
            }
            $fm = array_merge((array)$fm, array('fee_structure' => $fcString));
            // $fm = array_merge((array)$fm, array('GPRS'=>isset($fc->gprs) ? $fc[$k]->gprs : null ));
            $fm = array_merge((array)$fm, array('GPRS' => $fc->gprs));
          }
        return $route_data;

    }

    public function get_stopAll(){
        $this->db->distinct();
        $this->db->select('id,name,kilometer,time');
        $this->db->from('transport_stops');
        // $this->db->order_by('kilometer','asc');
        $this->db->order_by('name','asc');
        return $this->db->get()->result();
    }
    public function get_assignStop($stdId){
     return $this->db->select('sts.id as stsId,sts.stop_id, vd.id as routeId, ts.name as stopName,sts.usage_mode, vd.routeNo,sts.land_mark')
        ->from('student_to_stops sts')
        ->join('transport_stops ts','sts.stop_id=ts.id')
        ->join('vehicle_data vd','sts.route=vd.id','left')
        ->where('sts.student_id',$stdId)
        ->get()->row();
    }
    public function get_stopnamesAll(){
        $slaveData =  $this->db->select('rs.id as stop_id,rs.name,fs.amount as PDamount, fs1.amount as Pamount, fs2.amount as Damount')
            ->from('transport_stops rs')
            ->join('transport_fee_structure fs','rs.id=fs.stop_id')
            ->join('transport_fee_structure fs1','rs.id=fs1.stop_id')
            ->join('transport_fee_structure fs2','rs.id=fs2.stop_id')
            ->where('fs.pickup_mode', 'Pickup and Drop')
            ->where('fs1.pickup_mode','Pickup')
            ->where('fs2.pickup_mode','Drop')
            ->get()->result();

        $masterData = $this->db->select('rs.id,rs.name,rs.kilometer')
                    ->from('transport_stops rs')
                     ->order_by('name','asc')
                    ->get()->result();
        foreach ($slaveData as $key => $val) {
            $slave[$val->stop_id] =  $val;
        }
        foreach ($masterData as $key => $ma) {
            $master[$ma->id] =  $ma;
        }

        foreach ($master as $k => &$fc) {
          $fc = (object) array_merge((array)$fc, array('PDamount'=>isset($slave[$k]->PDamount) ? $slave[$k]->PDamount : null ));
          $fc = (object) array_merge((array)$fc, array('Pamount'=>isset($slave[$k]->Pamount) ? $slave[$k]->Pamount : null ));
          $fc = (object) array_merge((array)$fc, array('Damount'=>isset($slave[$k]->Damount) ? $slave[$k]->Damount : null ));
        }
        return $master;
    }
    
    public function submit_fee_structureData(){
      $stop_nameArry = $this->input->post('stop_name');
      $pd_amount = $this->input->post('pd_amount');
      $p_amount = $this->input->post('p_amount');
      $d_amount = $this->input->post('d_amount');
      $gprs = $this->input->post('gprs');
      $this->db->trans_start();
      foreach ($stop_nameArry as $key => $name) {
         //Insert PD
        $PDdata[] = array(
              'stop_id'=>$name,
              'pickup_mode'=>'Pickup and Drop',
              'amount'=>$pd_amount[$key],
              'gprs'=>$gprs[$key],
              );
        $this->db->where('pickup_mode','Pickup and Drop');
        $this->db->where('stop_id',$name);
        $querypd = $this->db->get('transport_fee_structure')->row();

       
        if (!empty($querypd)) {
          $this->db->where('pickup_mode','Pickup and Drop');
          $this->db->where('stop_id',$name);
          $this->db->delete('transport_fee_structure');
        }
        //Insert P
        $Pdata[] = array(
              'stop_id'=>$name,
              'pickup_mode'=>'Pickup',
              'amount'=>$p_amount[$key],
              'gprs'=>$gprs[$key],
              ); 
        $this->db->where('pickup_mode','Pickup');
        $this->db->where('stop_id',$name);
        $queryp = $this->db->get('transport_fee_structure')->row();

        if (!empty($queryp)) {
          $this->db->where('pickup_mode','Pickup');
          $this->db->where('stop_id',$name);
          $this->db->delete('transport_fee_structure');
        }
        //Insert D
        $Ddata[] = array(
              'stop_id'=>$name,
              'pickup_mode'=>'Drop',
              'amount'=>$d_amount[$key],
              'gprs'=>$gprs[$key],
            );
        $this->db->where('pickup_mode','Drop');
        $this->db->where('stop_id',$name);
        $queryd = $this->db->get('transport_fee_structure')->row();

        if (!empty($queryd)) {
          $this->db->where('pickup_mode','Drop');
          $this->db->where('stop_id',$name);
          $this->db->delete('transport_fee_structure');
        }
      }
    
      $this->db->insert_batch('transport_fee_structure',$PDdata);
      $this->db->insert_batch('transport_fee_structure',$Pdata);
      $this->db->insert_batch('transport_fee_structure',$Ddata);
      $this->db->trans_complete();
      return $this->db->trans_status();

    }

    // Vehicle

     public function get_info_trans(){
        $this->db->order_by('routeNo','asc');
      return $this->db->get('vehicle_data')->result();
    }

    public function submitvehicle_details(){
        $data = array(
            'tax_paid_upto'         => date('Y-m-d', strtotime($this->input->post('tax_paid_upto'))), 
            'emission_expiry_date'  => date('Y-m-d',strtotime($this->input->post('emission_expiry_date'))) , 
            'routeNo'               => $this->input->post('routeNo'), 
            'permiteNo'             => $this->input->post('permiteNo'), 
            'fcNo'                  => $this->input->post('fcNo'), 
            'insuranceNo'           => $this->input->post('insuranceNo'), 
            'rc_certificateNo'      => $this->input->post('rc_certificateNo'), 
            'permite_expiry_date'   => date('Y-m-d',strtotime($this->input->post('permite_expiry_date'))), 
            'fcNo_date'             => date('Y-m-d',strtotime($this->input->post('fcNo_date'))) , 
            'insuranceNo_date'      => date('Y-m-d',strtotime($this->input->post('insuranceNo_date'))) , 
            'rc_certificateNo_date' => date('Y-m-d',strtotime($this->input->post('rc_certificateNo_date'))), 
            'last_modified_by'      => $this->authorization->getAvatarId()
        );

       return $this->db->insert('vehicle_data',$data); 
           
    }

     public function vehicledataEditbyId($id){
            $this->db->where('id',$id);
     return $this->db->get('vehicle_data')->row();
    }

    public function updatevehicle_details($id){
        $update_data = array(
         'tax_paid_upto'         => date('Y-m-d', strtotime($this->input->post('tax_paid_upto'))), 
          'emission_expiry_date'  => date('Y-m-d',strtotime($this->input->post('emission_expiry_date'))) , 
          'routeNo'               => $this->input->post('routeNo'), 
          'permiteNo'             => $this->input->post('permiteNo'), 
          'fcNo'                  => $this->input->post('fcNo'), 
          'insuranceNo'           => $this->input->post('insuranceNo'), 
          'rc_certificateNo'      => $this->input->post('rc_certificateNo'), 
          'permite_expiry_date'   => date('Y-m-d',strtotime($this->input->post('permite_expiry_date'))), 
          'fcNo_date'             => date('Y-m-d',strtotime($this->input->post('fcNo_date'))) , 
          'insuranceNo_date'      => date('Y-m-d',strtotime($this->input->post('insuranceNo_date'))) , 
          'rc_certificateNo_date' => date('Y-m-d',strtotime($this->input->post('rc_certificateNo_date'))), 
          'last_modified_by'      => $this->ion_auth->get_user_id()
        );
        if (!empty($this->input->post('routeNo'))) {
                $this->db->where('id',$id);
        return $this->db->update('vehicle_data',$update_data); 
        }else{
          return false;
        }   
    }

    public function delete_trans_tab_1($id){
      $this->db->where('id',$id);
      return $this->db->delete('vehicle_data');
    }


    // Route Allocation

    public function get_allroute(){
        return $this->db->select('id,routeNo')->order_by('routeNo')->get('vehicle_data')->result();
    }

    public function insert_stopnamebyroute(){
        $stop_name = $this->input->post('stop_name');
        $route_name = $this->input->post('route_name');
        foreach ($stop_name as $key => $name) {
          $data[] = array(
                  'stop_id'=>$name,
                  'route_no'=>$route_name,
                );
        }
         $this->db->where('route_no',$route_name);
          $query = $this->db->get('route_allocate')->row();
          if (!empty($query)) {
              $this->db->where('route_no',$route_name);
              $query = $this->db->delete('route_allocate');
          }
        return $this->db->insert_batch('route_allocate',$data);
    }

    public function get_routwiseAllocatedStopnames($route_name){
        return $this->db->select('rs.id,rs.name')
          ->from('route_allocate ra')
          ->where('ra.route_no',$route_name)
          ->join('transport_stops rs','rs.id=ra.stop_id')
           ->order_by('rs.name')
          ->get()->result();
    }


    // Assing Student

    public function get_pickup_pointStopWiseRoute($stopId){
        return $this->db->select('vd.id as routeId, vd.routeNo')
            ->from('route_allocate ra')
            ->where('ra.stop_id',$stopId)
            ->join('vehicle_data vd','ra.route_no=vd.routeNo')
            ->get()->result();   
    }


    //
    public function getClassList(){
        $this->db->select('class.id as classid,class.class_name');
        $this->db->from('class');
        $this->db->where('acad_year_id',$this->yearId);
        $this->db->where('is_placeholder!=','1');
        if($this->current_branch) {
            $this->db->where('branch_id',$this->current_branch);
        }
        return $this->db->get()->result();
    }

    public function getSectionList($classid) {
        $this->db->select('cs.section_name,cs.class_id,cs.id,class_name');
        $this->db->from('class_section as cs');
        $this->db->join('class', 'class.id = cs.class_id');
        $this->db->where('cs.class_id =', $classid);
        return $this->db->get()->result();
    }

    public function getAsginedStudentList($class_id, $class_section) {
        $this->db->select('student.first_name,student.last_name,student.id as studentid,student_to_stops.stop_id,transport_stops.name as stopname');
        $this->db->join('class', 'class.id = student.class_id');
        $this->db->join('student_to_stops', 'student_to_stops.student_id=student.id','left');
        $this->db->join('class_section', 'class_section.id=student.class_section_id');
        $this->db->join('transport_stops', 'transport_stops.id=student_to_stops.stop_id','left');
        $this->db->where('class_section.class_id ', $class_id); // $this->db->join("student_to_stops",'student_to_stops.student_id != student.id');
        if ($class_section != 0)
            $this->db->where('class_section.id ', $class_section);
        $this->db->from('student');
        return $this->db->get()->result();
        //echo "<pre>";print_r($test);die();
    }


    public function assignStudentStop($stdId){
 
        $usage_mode = $this->input->post('usage_mode');
        $pick_up_point = $this->input->post('pick_up_point');
        $route = $this->input->post('route');
        $land_mark = $this->input->post('land_mark');
        $stopData = array(
                'student_id'=>$stdId,
                'stop_id'=>$pick_up_point,
                'route'=>$route,
                'usage_mode'=>trim($usage_mode),
                'land_mark'=>$land_mark,
                'last_modified_by' => $this->authorization->getAvatarId()
            );    
        return $this->db->insert('student_to_stops', $stopData);    
     }

     public function updateAssignStudentStop($stdId){
        $usage_mode = $this->input->post('usage_mode');
        $pick_up_point = $this->input->post('pick_up_point');
        $route = $this->input->post('route');
        $land_mark = $this->input->post('land_mark');
        $stopData = array(
                'student_id'=>$stdId,
                'stop_id'=>$pick_up_point,
                'route'=>$route,
                'usage_mode'=>trim($usage_mode),
                'land_mark'=>$land_mark,
                'last_modified_by' => $this->authorization->getAvatarId()
            );
     
         $this->db->where('student_id',$stdId);
        return $this->db->update('student_to_stops', $stopData); 
         
     }

    //


    // public function insert_priceStopwise(){
    //     $this->db->trans_start();
    //     $data_info = array(
    //         'item_id' => 0,
    //         'item_name' => 'Transport',
    //         'item_type' => 'stop',
    //         'var_option' => $this->input->post('stop_id'),
    //         'last_modified_by' => $this->authorization->getAvatarId()
    //     );
    //     $this->db->where('item_type','stop');
    //     $this->db->where('var_option',$this->input->post('stop_id'));
    //     $query = $this->db->get('variant')->row();
    //     if ($query) {
    //         return 0;
    //     }
    //     $this->db->insert('variant', $data_info);
    //     $lastInsertId = $this->db->insert_id();
    //     $price = $this->input->post('stop_price');
    //     $data = array(
    //         'quantity' =>  0,
    //         'variant_id' => $lastInsertId,
    //         'price' => $price,
    //         );
    //     $this->db->insert('variant_qty', $data);
    //     return  $this->db->trans_complete();         
    // }
    
    // public function getAllStopPriseList(){
    //     return $this->db->select('v.id,v.item_name,v.item_type,vq.quantity,vq.price,ts.name as optionName')
    //     ->from('variant v')
    //     ->join('variant_qty vq','vq.variant_id=v.id')
    //     ->join('transport_stops ts','ts.id=v.var_option')
    //     ->where('item_type','stop')
    //     ->get()->result();
    // }

    // public function edit_stopId($id){
    //     return  $this->db->select('v.id,v.item_name,v.var_option as stopId, v.item_type,vq.quantity,vq.price,ts.name as optionName')
    //             ->from('variant v')
    //             ->join('variant_qty vq','vq.variant_id=v.id')
    //             ->join('transport_stops ts','ts.id=v.var_option')
    //             ->where('v.id',$id)
    //             ->get()->row();
    // }
    
    //  public function update_priceStopwise($id){
    //     $this->db->trans_start();
    //     $data_info = array(
    //         'item_id' => 0,
    //         'item_name' => 'Transport',
    //         'item_type' => 'stop',
    //         'var_option' => $this->input->post('stop_id'),
    //         'last_modified_by' => $this->authorization->getAvatarId()
    //     );
    //     $this->db->where('id',$id);
    //     $this->db->update('variant', $data_info);
    //     $price = $this->input->post('stop_price');
    //     $data = array(
    //         'quantity' =>  0,
    //         'variant_id' => $id,
    //         'price' => $price,
    //         );
    //     $this->db->where('variant_id',$id);
    //     $this->db->update('variant_qty', $data);
    //     return  $this->db->trans_complete();         
    // }

    // public function deletePriceStopwise($id){
    //         $this->db->where('item_type','stop');
    //         $this->db->where('v.id', $id);
    //         $this->db->delete('variant v');
    //         $this->db->where('variant_id', $id);
    //     return $this->db->delete('variant_qty');
    // }



    /* Transportation for gps tracking  
    Buses  
    */
    public function getBuses() {
      return $this->db_readonly->select("th.*, CONCAT(ifnull(dr.first_name,''),' ', ifnull(dr.last_name,'')) as driverName")
              ->from('tx_things th')
              ->join('tx_drivers dr', 'dr.id=th.driver_id', 'left')
              ->get()->result();
    }

    public function getBusNames() {
      return $this->db_readonly->select('id,thing_name,refresh_required,thing_reg_number')->get('tx_things')->result();
    }

    public function get_student_attendance_summary_across_days($from_date, $to_date, $filter_pick_drop, $journey_ids) {
      $journey_type_str = '';

      $this->db_readonly->select("
              tj.journey_name,
              tsj.entity_source_id as student_id,
              txs.stop_name,
              CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) as student_name,
              CONCAT(cs.class_name, cs.section_name) as section_name,
              REPLACE(LTRIM(REPLACE(sa.rfid_number, '0', ' ')), ' ', '0') as rfid_number,
              tsj.journey_id,
              IFNULL(sa.admission_no, '-') as admission_no,
              IFNULL(sa.enrollment_number, '-') as enrollment_no
          ")
        ->from('tx_student_journeys tsj')
        ->join('tx_stops txs', 'txs.id=tsj.stop_id')
        ->join('student_admission sa', 'sa.id=tsj.entity_source_id and sa.admission_status=2')
        ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId and sy.promotion_status != 4 and sy.promotion_status !=5")
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('tx_journeys tj', "tj.id=tsj.journey_id")
        ->where('entity_type', 'Student')
        ->where('day', 'Monday')
        ->order_by('journey_id, sa.first_name');

      if (!empty($journey_ids)) {
        $this->db_readonly->where_in('journey_id', $journey_ids);
      }

      if ($filter_pick_drop != 'All') {
        $this->db_readonly->where('tj.journey_type', $filter_pick_drop);
      }

      $result = $this->db_readonly->get()->result();

      $this->db_readonly->select("group_concat(date_format(ADDTIME(l.updated_at, '05:30:00'), '%d-%b-%y %H:%i')) as taps, l.rfid, l.journey_id")
        ->from("tx_attendance l")
        ->where("DATE_FORMAT(l.updated_at, '%d-%m-%Y') >= '$from_date' and DATE_FORMAT(l.updated_at, '%d-%m-%Y') <= '$to_date'")
        ->group_by('l.rfid');

      if ($filter_pick_drop != 'All') {
        $this->db_readonly->where('l.journey_type', $filter_pick_drop);
      }
      
      if (!empty($journey_ids)) {
        $this->db_readonly->where_in('l.journey_id', $journey_ids);
      }

      $att_rfids = $this->db_readonly->get()->result();

      foreach ($result as $res) {
        $found = 0;
        foreach ($att_rfids as $att) {
          if ($res->rfid_number == $att->rfid && $res->journey_id == $att->journey_id) {
            $res->taps = $att->taps;
            $found = 1;
            break;
          }
        }
        if ($found == 0) {
          $res->taps = '-';
        }
      }

      //Dedupe the times
      foreach ($result as $res) {
        if ($res->taps != '-') {
          $ret_array = $this->_dedupeAndSortTimes(explode(',', $res->taps));
          $res->taps = $ret_array[0];
          $res->tap_count = $ret_array[1];
        } else {
          $res->tap_count = 0;
        }
      }

      return $result;
    }

    private function _dedupeAndSortTimes($dateTimes) {
      // Create an associative array to store unique date strings as keys
      // and the corresponding time strings as values
      $uniqueDates = [];
  
      // Iterate through the date-time strings
      foreach ($dateTimes as $dateTime) {
          // Parse the date-time string into a DateTime object
          $dateTimeObj = new DateTime($dateTime);
          
          // Extract the date part in 'Y-m-d' format
          $dateKey = $dateTimeObj->format('Y-m-d');
          
          // If the date is not already in the uniqueDates array, add it
          if (!isset($uniqueDates[$dateKey])) {
              $uniqueDates[$dateKey] = $dateTimeObj->format('d-M H:i');
          }
      }
      
      // Sort the unique date-time strings by date
      ksort($uniqueDates);
      
      // Return the sorted unique date-time strings as an array
      return [array_values($uniqueDates), count($uniqueDates)];
    }

    public function get_unique_journeys_by_thing_id($thing_id) {
      return $this->db_readonly->select('id, journey_name')->from('tx_journeys')->where('thing_id', $thing_id)->get()->result();
    }

    public function addBus() {
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      //echo "<pre>"; print_r($input); die();
      $busData = array(
        'thing_name' => $input['thing_name'],
        'thing_reg_number' => $input['thing_reg_number'],
        'tracking_url' => $input['tracking_url'],
        'driver_id' => $input['driver_id'],
        'created_by' => $this->authorization->getAvatarId(),
        'last_modified_by' => $this->authorization->getAvatarId(),
        'bus_capacity' => $input['bus_capacity'],
        'permit_number' => $input['permit_number'],
        'permit_number_expiry_date' => $input['permit_number_expiry_date'] ? $input['permit_number_expiry_date'] : null,
        'rc_number' => $input['rc_number'],
        'rc_expiry_date' => $input['rc_expiry_date'] ? $input['rc_expiry_date'] : null,
        'insurance_number' => $input['insurance_number'],
        'insurance_number_expiry_date' => $input['insurance_number_expiry_date'] ? $input['insurance_number_expiry_date'] : null,
      );
      return $this->db->insert('tx_things', $busData);
    }

    public function deleteBus($id) {
      $this->db->where('id',$id);
      return $this->db->delete('tx_things');
    }

    /* 
    Routes  
    */

    // public function getRoutes() {
    //   return $this->db->select('*')->get('tx_routes')->result();
    // }

    // public function getRouteNames() {
    //   return $this->db->select('id,route_name')->get('tx_routes')->result();
    // }

    public function getStopNames() {
      return $this->db->select('id,stop_name')->order_by('stop_name')->get('tx_stops')->result();
    }

    public function add_journey_stops($journey_ids, $stops) {
      $data = array();
      foreach ($journey_ids as $journey_id) {
        foreach ($stops as $stop_id) {
            $this->db->where('journey_id', $journey_id);
            $this->db->where('stop_id', $stop_id);
            $exists = $this->db->get('tx_journey_stops')->num_rows();
            
            if ($exists == 0) {
                $data[] = array(
                    'journey_id' => $journey_id,
                    'stop_id' => $stop_id
                );
            }
        }
      }
      // echo "<pre>";print_r($data);die();
      $status = $this->db->insert_batch('tx_journey_stops', $data);
      // $this->updateStopOrder($stops);
      return $status;
    }

    // public function addRoute() {
    //   $input = $this->input->post();
    //   $routeData = array(
    //     'route_name' => $input['route_name'],
    //     'total_km' => $input['total_km'],
    //     'created_by' => $this->authorization->getAvatarId(),
    //     'last_modified_by' => $this->authorization->getAvatarId()
    //   );
    //   return $this->db->insert('tx_routes', $routeData);
    // }

    // public function deleteRoute($id) {
    //   $this->db->where('id',$id);
    //   return $this->db->delete('tx_routes');
    // }

    /* 
    Stops 
    */

    public function getStops() {
      // return $this->db->select('s.*, r.route_name')->join('tx_routes r', 'r.id=s.route_id')->get('tx_stops s')->result();
      return $this->db->select('s.*')->order_by('s.stop_name')->get('tx_stops s')->result();
    }

    private function order_stops($route_id) {
      $stops = $this->db->select('id, tentative_reach_time, order')->where('route_id', $route_id)->get('tx_stops')->result_array();
      usort($stops, function($a, $b) { 
        $a1 = strtotime($a['tentative_reach_time']);
        $b1 = strtotime($b['tentative_reach_time']);
        return $a1 - $b1; 
      });
      $i = 1;
      foreach ($stops as $k => $stop) {
        $stops[$k]['order'] = $i++;
      }
      return $this->db->update_batch('tx_stops', $stops, 'id');
    }

    public function addStop() {
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      // $tentative_reach_time = date('H:i:s', strtotime($input['tentative_reach_time']));
      $stopData = array(
        'stop_name' => $input['stop_name'],
        'distance_to_stop' => 0,//$input['distance_to_stop'],
        'tentative_reach_time' => date('H:i:s'),//$tentative_reach_time,
        'landmark' => $input['landmark'],
        'created_by' => $this->authorization->getAvatarId(),
        'last_modified_by' => $this->authorization->getAvatarId()
      );
      return $this->db->insert('tx_stops', $stopData);
      // return $this->order_stops($input['route_id']);
    }

    public function updateRouteStop() {
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      // $tentative_reach_time = date('H:i:s', strtotime($input['tentative_reach_time']));
      $stopData = array(
        'stop_name' => $input['stop_name'],
        'distance_to_stop' => 0,//$input['distance_to_stop'],
        'tentative_reach_time' => date('H:i:s'),//$tentative_reach_time,
        'landmark' => $input['landmark'],
        'last_modified_by' => $this->authorization->getAvatarId()
      );
      $this->db->where('id', $input['stop_id']);
      return $this->db->update('tx_stops', $stopData);
      // return $this->order_stops($input['route_id']);
    }

    public function removeStop($stop_id) {
      $this->db->trans_start();
      $this->db->where('id',$stop_id)->delete('tx_stops');
      $this->db->where('stop_id', $stop_id)->delete('tx_journey_stops');
      $this->db->where('stop_id', $stop_id)->delete('tx_staff_journeys');
      $this->db->where('stop_id', $stop_id)->delete('tx_student_journeys');
      $this->db->where('stop_id', $stop_id)->delete('tx_student_journeys_override');
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    public function getClassSections() {
        $this->db->select('cs.section_name,cs.class_id,cs.id,c.class_name');
        $this->db->from('class_section as cs');
        $this->db->join('class c', 'c.id = cs.class_id');
        $this->db->where('c.acad_year_id',$this->yearId);
        $this->db->where('cs.is_placeholder',0);
        if($this->current_branch) {
            $this->db->where('c.branch_id',$this->current_branch);
        }
        $return = $this->db->get()->result();
        return $return;
    }

    public function getStopsByRoute($route_id) {
      return $this->db->select('id, stop_name')->from('tx_stops')->where('route_id', $route_id)->get()->result();
    }

    public function addJourneys() {
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      // echo "<pre>";print_r($input);die();
      $startTime = date('H:i:s', strtotime($input['tentative_start_time']));
      $endTime = date('H:i:s', strtotime($input['tentative_end_time']));
      $days = json_encode($input['days']);
      $data = array(
          'journey_name' => $input['journey_name'],
          'thing_id' => $input['thing_id'],
          'journey_type' => $input['journey_type'],
          'tentative_start_time' => $startTime,
          'tentative_end_time' => $endTime,
          'days' => $days
        );
      
      $this->db->trans_start();
      $this->db->insert('tx_journeys', $data);
      $journey_id = $this->db->insert_id();

      $journey_stops = array();
      $stop_ids = array();
      if(isset($input['stop_ids'])) {
        foreach ($input["stop_ids"] as $stop_id) {
          $journey_stops[] = array(
            'journey_id' => $journey_id,
            'stop_id' => $stop_id
          );
          array_push($stop_ids, $stop_id);
        }
      }
      
      if(!empty($journey_stops)) {
        $this->db->insert_batch('tx_journey_stops', $journey_stops);
      }
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return 0;
      }
      // $this->updateStopOrder($stop_ids);
      $this->db->trans_commit();
      return 1;
      // echo "<pre>"; print_r($input); die();
    }

    public function update_journey($input) {
      // $input = array_map('trim', $input);
      $startTime = date('H:i:s', strtotime($input['tentative_start_time']));
      $endTime = date('H:i:s', strtotime($input['tentative_end_time']));
      $days = json_encode($input['days']);
      $data = array(
        'journey_name' => $input['journey_name'],
        'journey_type' => $input['journey_type'],
        'tentative_start_time' => $startTime,
        'tentative_end_time' => $endTime,
        'days' => $days,
        'status' => $input['status']
      );
      $this->db->where('id', $input['id']);
      return $this->db->update('tx_journeys', $data);
    }

    public function getFirstThingId() {
      $thing = $this->db->select('id')->get('tx_things')->row();
      if(empty($thing)) {
        return 0;
      }
      return $thing->id;
    }

    public function getJourneysByThing($thing_id) {
      $journeys = $this->db->select('j.id, j.journey_name, j.journey_type, j.days, TIME_FORMAT(j.tentative_start_time, "%h:%i %p") as tentative_start_time, TIME_FORMAT(j.tentative_end_time, "%h:%i %p") as tentative_end_time, s.stop_name, s.id as stop_id, s.tentative_reach_time, j.refresh_required, s.order, j.status, j.thing_id, js.stop_time as stop_time')
          ->from('tx_journeys j')
          ->join('tx_journey_stops js', 'js.journey_id=j.id','left')
          ->join('tx_stops s', 's.id=js.stop_id','left')
          ->where('j.thing_id', $thing_id)
          ->order_by('j.tentative_start_time, s.stop_name')
          ->get()->result();

      function sortByStopTime($a, $b) {
          if (empty($a->stop_time) && empty($b->stop_time)) {
              return 0;
          } elseif (empty($a->stop_time)) {
              return 1;
          } elseif (empty($b->stop_time)) {
              return -1;
          } else {
              return strcmp($a->stop_time, $b->stop_time);
          }
      }

      usort($journeys, 'sortByStopTime');

      $journey_data = array('PICKING' => array(), 'DROPPING' => array());

      foreach ($journeys as $key => $journey) {
        $jId = $journey->id;
        if(!array_key_exists($jId, $journey_data[$journey->journey_type])) {
          $journey_data[$journey->journey_type][$jId] = array();
          $journey_data[$journey->journey_type][$jId]['id'] = $jId;
          $journey_data[$journey->journey_type][$jId]['journey_name'] = $journey->journey_name;
          $journey_data[$journey->journey_type][$jId]['status'] = $journey->status;
          $journey_data[$journey->journey_type][$jId]['journey_type'] = $journey->journey_type;
          $journey_data[$journey->journey_type][$jId]['thing_id'] = $journey->thing_id;
          $journey_data[$journey->journey_type][$jId]['days'] = json_decode($journey->days);
          $journey_data[$journey->journey_type][$jId]['tentative_start_time'] = $journey->tentative_start_time;
          $journey_data[$journey->journey_type][$jId]['tentative_end_time'] = $journey->tentative_end_time;
          $journey_data[$journey->journey_type][$jId]['refresh_required'] = $journey->refresh_required;
          $journey_data[$journey->journey_type][$jId]['stops'] = array();
        }
        if ($journey->stop_id != null) {
            $formattedTime = null;
            if(isset($journey->stop_time)){
              $formattedTime = strtotime($journey->stop_time) > '12:00:00' ? date('h:i A', strtotime($journey->stop_time)) : date('h:i a', strtotime($journey->stop_time)) ;
            }

            $stop = array(
                'id' => $journey->stop_id,
                'name' => $journey->stop_name,
                'time' => date('h:i a', strtotime($journey->tentative_reach_time)),
                'stop_time' => $formattedTime
            );
            $journey_data[$journey->journey_type][$jId]['stops'][] = $stop;
        }
      }
      return $journey_data;
    }

    public function delete_journey($journey_id) {
      $this->db->trans_start();
      $this->db->where('id', $journey_id)->delete('tx_journeys');
      $this->db->where('journey_id', $journey_id)->delete('tx_journey_stops');
      $this->db->where('journey_id', $journey_id)->delete('tx_staff_journeys');
      $this->db->where('journey_id', $journey_id)->delete('tx_student_journeys');
      $this->db->where('stored_journey_id', $journey_id)->delete('tx_mismatch_journeys');
      $this->db->where('actual_journey_id', $journey_id)->delete('tx_mismatch_journeys');
      $this->db->where('journey_id', $journey_id)->delete('tx_student_journeys_override');
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    public function checkStudentsInJourney($journey_id, $stop_id=0) {
      $this->db->select('id')
      ->from('tx_student_journeys')
      ->where('journey_id', $journey_id);
      if($stop_id)
        $this->db->where('stop_id', $stop_id);
      $result = $this->db->get()->result();
      if(empty($result))
        return 0;
      return 1;
    }

    public function deleteJourneyStop($status, $journey_id, $stop_id) {
      if($status == 'none'){
        return $this->delete_journey_stop_in_db($journey_id, $stop_id);
      }elseif($status == 'other_journeys'){
        $other_journeys = $this->get_other_journeys_of_bus($journey_id);
        $other_journeys = $this->get_other_journeys_of_bus($journey_id);
        foreach ($other_journeys as $journey) {
            $result = $this->delete_journey_stop_in_db($journey->id, $stop_id);
            if (!$result) {
              return 0;
            }
        }
        return 1;
      }
    }

    private function delete_journey_stop_in_db($journey_id, $stop_id){
      $this->db->trans_start();

      $this->db->where('journey_id', $journey_id)->where('stop_id', $stop_id)->delete('tx_journey_stops');
      $this->db->where('journey_id', $journey_id)->where('stop_id', $stop_id)->delete('tx_staff_journeys');
      $this->db->where('journey_id', $journey_id)->where('stop_id', $stop_id)->delete('tx_student_journeys');

      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    private function get_other_journeys_of_bus($thingId){
      $this->db_readonly->select('id');
      $this->db_readonly->from('tx_journeys s');
      $this->db_readonly->where('thing_id', $thingId);
      $query = $this->db_readonly->get()->result();
      return $query;
    }

    // public function getNotRefreshedJourneys($thing_id) {
    //   return $this->db->select('j.id, j.journey_name, j.journey_type, j.days, TIME_FORMAT(j.tentative_start_time, "%h:%i %p") as tentative_start_time, TIME_FORMAT(j.tentative_end_time, "%h:%i %p") as tentative_end_time, r.route_name, r.id as route_id, j.refresh_required')
    //               ->from('tx_journeys j')
    //               ->join('tx_routes r', 'r.id=j.route_id')
    //               ->where('thing_id', $thing_id)
    //               // ->where('refresh_required', 0)
    //               ->get()->result();
    // }

    public function getJourneysByThingId($thing_id) {
      return $this->db->select('j.id, j.journey_name, j.journey_type, j.days, TIME_FORMAT(j.tentative_start_time, "%h:%i %p") as tentative_start_time, TIME_FORMAT(j.tentative_end_time, "%h:%i %p") as tentative_end_time, j.refresh_required')
                  ->from('tx_journeys j')
                  ->where('thing_id', $thing_id)
                  ->get()->result();
    }

    public function getStopsByJourney($journey_id) {
      return $this->db->select('s.id as stopId, stop_name as stopName, TIME_FORMAT(tentative_reach_time, "%H:%i") as stopTime, landmark')
      ->from('tx_stops s')
      ->join('tx_journey_stops js', 'js.stop_id=s.id')
      ->where('js.journey_id' ,$journey_id)
      ->order_by('order')
      ->get()
      ->result();
    }

    public function getThingJourneys($thing_id) {
      $this->db->select('j.*, th.thing_reg_number as thingId, th.thing_name as thingName');
      $this->db->from('tx_journeys j');
      $this->db->join('tx_things th', 'th.id=j.thing_id');
      // $this->db->join('tx_routes r', 'r.id=j.route_id');
      $this->db->where('thing_id', $thing_id);
      $this->db->where('refresh_required', 0);
      $result = $this->db->get()->result();
      // return $result;
      echo "<pre>"; print_r($result); die();
    }

    public function getStopsByRouteId($route_id) {
      return $this->db->select('s.id as stopId, s.stop_name as stopName, TIME_FORMAT(s.tentative_reach_time, "%H:%i") as stopTime, s.landmark')
      ->from('tx_journey_stops tjs')
      ->join('tx_stops s', 'tjs.stop_id= s.id')
      ->where('tjs.journey_id' ,$route_id)
      ->order_by('s.id')
      ->get()
      ->result();
    }

    public function getThingById($thing_id) {
      return $this->db->select('id, thing_name, thing_reg_number')
              ->where('id', $thing_id)
              ->get('tx_things')->row();
    }

    public function updateTrackingUrl($thing_id, $response) {
      $data = json_decode($response);
      $tracking_url = $data->tracking_url;
      $tracking_url=str_ireplace("\u003D","=",$tracking_url);
      $tracking_url=str_ireplace("\u0026","&",$tracking_url);
      return $this->db->where('id', $thing_id)->update('tx_things', array('tracking_url' => $tracking_url, 'refresh_required' => 0));
    }

    public function update_journeys_refreshed($journey_ids) {
      return $this->db->where_in('id', $journey_ids)->update('tx_journeys', array('refresh_required' => 1));
    }

    // public function getStudentsBySection($sectionId) {
    //   $this->db->select("sa.id, sa.admission_no, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, pick_st.id as pickStopId, pick_st.stop_name as pickStopName, drop_st.id as dropStopId, drop_st.stop_name as dropStopName, pick_rt.id as pickRouteId, pick_rt.route_name as pickRouteName, drop_rt.id as dropRouteId, drop_rt.route_name as dropRouteName");
    //   $this->db->from('student_admission sa');
    //   $this->db->join('student_year sy', 'sy.student_admission_id=sa.id');
    //   $this->db->join("tx_student_journeys e", "e.entity_source_id=sa.id and e.entity_type='Student'", "left");
    //   $this->db->join('tx_stops pick_st', 'pick_st.id=e.usual_pickup_stop_id', 'left');
    //   $this->db->join('tx_stops drop_st', 'drop_st.id=e.usual_drop_stop_id', 'left');
    //   $this->db->join('tx_routes pick_rt', 'pick_rt.id=e.usual_pickup_route_id', 'left');
    //   $this->db->join('tx_routes drop_rt', 'drop_rt.id=e.usual_drop_route_id', 'left');
    //   $this->db->where('sy.class_section_id', $sectionId);
    //   $this->db->where('sy.acad_year_id', $this->yearId);
    //   $this->db->where('sa.admission_status', 2);
    //   $this->db->where('sy.promotion_status!=', '4');
    //   $this->db->where('sy.promotion_status!=', '5');
    //   $result = $this->db->get()->result();
    //   return $result;
    //   // echo $this->db->last_query();
    //   // echo "<pre>"; print_r($result); die();
    // }

    public function getStudentsByClass($classId) {
      $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, CONCAT(cs.class_name,' ', cs.section_name) as csName");
      $this->db->from('student_admission sa');
      $this->db->join('student_year sy', 'sy.student_admission_id=sa.id');
      $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
      $this->db->where('sy.class_id', $classId);
      $this->db->where('sa.admission_status', 2);
      $this->db->where('sy.promotion_status!=', '4');
      $this->db->where('sy.promotion_status!=', '5');
      $this->db->where('sy.acad_year_id', $this->yearId);
      $this->db->order_by('cs.id, sa.first_name');
      $result = $this->db->get()->result();
      return $result;
      // echo $this->db->last_query();
      // echo "<pre>"; print_r($result); die();
    }

    public function getStudentsByClassSection($classId, $classSectionId, $stdName, $admission_no) {
        $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, CONCAT(cs.class_name,' ', cs.section_name) as csName");
        $this->db->from('student_admission sa');
        $this->db->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId");
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->where('sa.admission_status', 2);
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where('sy.acad_year_id', $this->yearId);
        // $this->db->where('sa.admission_acad_year_id', $this->yearId);
        $this->db->order_by('cs.id, sa.first_name');
        if ($classId) {
            $this->db->where('sy.class_id', $classId);
        }
        if ($classSectionId) {
            $this->db->where('sy.class_section_id', $classSectionId);
        }
        if ($stdName) {
            $this->db->where("(LOWER(sa.first_name) like '%$stdName%' OR (LOWER(sa.last_name) like '%$stdName%'))");
        }
        if ($admission_no) {
            $this->db->where('sa.admission_no', $admission_no);
        }
        $result = $this->db->get()->result();
        foreach($result as $res){
          $res->has_journey = $this->confirm_journey($res->id);
        }
        return $result;
    }

    private function confirm_journey($id){
      $query = $this->db_readonly->select('*')
                                ->from('tx_student_journeys')
                                ->where('entity_source_id', $id)
                                ->get();

      if ($query->num_rows() > 0) {
          return 'has_journey';
      } else {
          return 'none';
      }
    }

    public function assignStudentStops() {
      $input = $this->input->post();
      //add new assignments
      $assign = array();
      foreach ($input['students_new'] as $stdId) {
        $assign[] = array(
          'entity_type' => 'Student',
          'entity_source_id' => $stdId,
          'usual_pickup_route_id' => $input['pick_route'],
          'usual_pickup_stop_id' => $input['pick_stop'],
          'usual_drop_route_id' => $input['drop_route'],
          'usual_drop_stop_id' => $input['drop_stop'],
          'are_journeys_assigned' => 0
        );
      }

      $this->db->trans_begin();
      $this->db->insert_batch('tx_student_journeys', $assign);
      //update old assignments
      foreach ($input['students_old'] as $stdId) {
        $update = array(
          'usual_pickup_route_id' => $input['pick_route'],
          'usual_pickup_stop_id' => $input['pick_stop'],
          'usual_drop_route_id' => $input['drop_route'],
          'usual_drop_stop_id' => $input['drop_stop']
        );
        $this->db->where('entity_source_id', $stdId)->update('tx_student_journeys', $update);
      }

      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    public function getDrivers($site) {
      $this->db_readonly->select("dr.id, CONCAT(IFNULL(dr.first_name, ''), ' ', IFNULL(dr.last_name, '')) as name, dr.phone_number, dr.driver_licence, dr.driver_licence_expiry_date, dr.status, dr.attender_name, dr.attender_number")
        ->from('tx_drivers dr');
    
      if ($site != 'manage_drivers') {
          $this->db_readonly->where('dr.status', 1);
      }
      $this->db_readonly->order_by('dr.first_name');
      $result = $this->db_readonly->get()->result();

      // foreach($result as $res){
      //   $res->assigned = 0;
      //   $this->db_readonly->where('driver_id', $res->id);
      //   $driver_present = $this->db_readonly->get('tx_things')->row();

      //   if ($driver_present) {
      //       $res->assigned = 1;
      //   }
      // }

      // usort($result, function($a, $b) {
      //     return $a->assigned > $b->assigned;
      // });

      return $result;
    }

    private function _insertIntoUsers($username, $email) {
        $ci = & get_instance();
        //Remove all space (left, right and in-between); convert to lower case
        $username = strtolower(str_replace(' ','',$username));

        $userId = $ci->ion_auth->register_1($username, 'welcome123', $email);
        return $userId;
    }

    private function _insertIntoAvatar($userId, $avatar_type, $insert_id, $friendly_name) {
        $ci = & get_instance();
        $param = array(
            'user_id' => $userId,
            'avatar_type' => $avatar_type,
            'stakeholder_id' => $insert_id,
            'friendly_name' => $friendly_name,
            'last_modified_by' => $this->authorization->getAvatarId()
        );

        return $ci->db->insert('avatar', $param);
    }

    private function first($users) {
        do {
            $username = $this->generateRandomCode(4,0);//generating random string
            $username .= $this->generateRandomCode(2,1);//generating random number
        }while(in_array($username,$users, TRUE));
        
        return $username;
    }

    private function second($name, $users) {
        $name = substr($name, 0, 6);
        if(!in_array($name, $users, TRUE))
            return $name;
        $len = strlen($name);
        $random = '';
        $num = 6 - $len;
        do {
            $times = pow(10, $num);
            for ($i=0; $i < $times; $i++) { 
                $random = $this->generateRandomCode($num, 1);
            }
            $num++;
        } while(in_array($name.$random, $users, TRUE));
        return $name.$random;
    }

    private function third($firstName, $lastName, $users) {
        $username = substr($firstName, 0, 4).substr($lastName, 0, 2);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 2).substr($lastName, 0, 4);
        if(!in_array($username, $users,TRUE))
            return $username;

        $username = substr($lastName, 0, 4).substr($firstName, 0, 2);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 2).substr($firstName, 0, 4);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 3).substr($lastName, 0, 3);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 3).substr($firstName, 0, 3);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 5).substr($lastName, 0, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 1).substr($lastName, 0, 5);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 5).substr($firstName, 0, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 1).substr($firstName, 0, 5);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 6);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 6);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 4).substr($lastName, 0, 2).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 4).substr($firstName, 0, 2).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 3).substr($lastName, 0, 3).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 3).substr($firstName, 0, 3).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($firstName, 0, 5).substr($lastName, 0, 1).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE))
            return $username;

        $username = substr($lastName, 0, 5).substr($firstName, 0, 1).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users, TRUE)) {
            return $username;
        } else {
            $username = substr($lastName, 0, 3).substr($firstName, 0, 2).$this->generateRandomCode(3, 1);
        }
        return $username;
    }

    private function generateRandomCode($length = 6, $isNumber=1) {
        if($isNumber)
            return substr(str_shuffle(str_repeat($x='1234567890', ceil($length/strlen($x)) )),1,$length);
        else 
            return substr(str_shuffle(str_repeat($x='abcdefghijklmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    private function generateUsername($firstName, $lastName) {
        $names = $this->db->select('username')->get('users')->result();
        $users = array();
        if(!empty($names)) {
            foreach ($names as $val) {
                array_push($users, $val->username);   
            }
        }
        $firstName = preg_replace('/\s+/','', $firstName);
        $lastName = preg_replace('/\s+/','', $lastName);
        $firstName = preg_replace('/[^A-Za-z0-9]/', '', $firstName);
        $lastName = preg_replace('/[^A-Za-z0-9]/', '', $lastName);
        if($firstName == '' && $lastName == '') {
            $username = $this->first($users);
        } else {
          $username = $this->second($firstName, $users);
        }
        // else if($firstName == '') {
        //     if(in_array($lastName, $users, TRUE)) {
        //         $username = $this->second($lastName, $users);
        //     } else {
        //         $username = substr($lastName, 0, 6);
        //     }
        // }
        // else if ($lastName == '') {
        //     if(in_array($firstName, $users, TRUE)) {
        //         $username = $this->second($firstName, $users);
        //     }else {
        //         $username = substr($firstName, 0, 6);
        //     }
        // } else {
        //     $username = $this->third($firstName, $lastName, $users);
        // }
        return $username;
    }

    public function addDriver($input) {
      $this->db->trans_start();

      // $input = array_map('trim', $input);

      $avatarId = $this->authorization->getAvatarId();

      $driver_license_expiry_date = !empty($input['driver_licence_expiry_date']) ? date("Y-m-d H:i:s", strtotime($input['driver_licence_expiry_date'])) : null;

      $data = array(
        'first_name' => $input['first_name'],
        'last_name' => $input['last_name'],
        'phone_number' => ($input['phone_number'] == '')?NULL:$input['phone_number'],
        'alternative_number' => ($input['alternative_number'] == '')?NULL:$input['alternative_number'],
        'attender_name' => ($input['attender_name']=='')?NULL:$input['attender_name'],
        'attender_number' => ($input['attender_number'] == '')?NULL:$input['attender_number'],
        'driver_licence' => $input['driver_licence'],
        'driver_licence_expiry_date' => $driver_license_expiry_date,
        'status' => $input['status'],
        'created_by' => $avatarId,
        'last_modified_by' => $avatarId
      );

      //add driver data into tx_drivers table
      $result = $this->db->insert('tx_drivers', $data);
      if (!$result) {
        $this->db->trans_rollback();
        return 0;
      }

      $driver_id = (int)$this->db->insert_id();
      
      //add driver into users table
      $username = $this->generateUsername(strtolower($input['first_name']), strtolower($input['last_name']));
      $userId = (int)$this->_insertIntoUsers($username, '');
      if($userId === FALSE) {
        $this->db->trans_rollback();
        return 0;
      }

      //Avatar Type for driver is 5
      $avatarId = $this->_insertIntoAvatar($userId, '5', $driver_id, $input['first_name'].' '.$input['last_name']);
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    public function getDriver($id) {
      return $this->db->select("*")->where('id', $id)->get('tx_drivers')->row();
    }

    public function getDriverByBus($thing_id) {
      $result = $this->db->select("*")->where("id in (select driver_id from tx_things where id=$thing_id)")->get('tx_drivers')->row();

      if ($result && isset($result->driver_licence_expiry_date)) {
          $result->driver_licence_expiry_date = date('d-m-Y', strtotime($result->driver_licence_expiry_date));
      }else{
        $result->driver_licence_expiry_date = '-';
      }
      return $result;
    }

    public function check_driver_status($id){
      $this->db_readonly->select('count(*) as count');
      $this->db_readonly->from('tx_things');
      $this->db_readonly->where('driver_id', $id);
      $query = $this->db_readonly->get()->row();
      if ($query->count > 0) {
          return 1;
      } else {
          return 0;
      }
    }

    public function updateDriver($id) {
      $id = trim($id);
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      $avatarId = $this->authorization->getAvatarId();
      $driver_license_expiry_date = !empty($input['driver_licence_expiry_date']) ? date("Y-m-d H:i:s", strtotime($input['driver_licence_expiry_date'])) : null;
      $data = array(
        'first_name' => $input['first_name'],
        'last_name' => $input['last_name'],
        'phone_number' => ($input['phone_number'] == '')?NULL:$input['phone_number'],
        'alternative_number' => ($input['alternative_number'] == '')?NULL:$input['alternative_number'],
        'attender_name' => ($input['attender_name']=='')?NULL:$input['attender_name'],
        'attender_number' => ($input['attender_number'] == '')?NULL:$input['attender_number'],
        'driver_licence' => $input['driver_licence'],
        'driver_licence_expiry_date' => $driver_license_expiry_date,
        'status' => $input['status'],
        'last_modified_by' => $avatarId
      );
      // echo "<pre>";print_r($data);die();
      return $this->db->where('id', $id)->update('tx_drivers', $data);
    }

    public function getUnallocatedDrivers() {
      $this->db->select("id, CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) as name");
      $this->db->where('status', 1);
      $this->db->order_by('first_name');
      // $this->db->where('id not in (select driver_id from tx_things where driver_id is not null)');
      $result = $this->db->get('tx_drivers')->result();

      // foreach($result as $res){
      //   $res->assigned = 0;
      //   $this->db_readonly->where('driver_id', $res->id);
      //   $driver_present = $this->db_readonly->get('tx_things')->row();

      //   if ($driver_present) {
      //       $res->assigned = 1;
      //   }
      // }

      // usort($result, function($a, $b) {
      //     return $a->assigned > $b->assigned;
      // });

      // echo "<pre>";print_r($result);die();
      return $result;
    }

    public function removeBusDriver($id) {
      return $this->db->where('id', $id)->update('tx_things', array('driver_id' => null));
    }

    public function getBus($id) {
      return $this->db->select("th.*, CONCAT(ifnull(dr.first_name,''),' ', ifnull(dr.last_name,'')) as driverName")->join('tx_drivers dr', 'dr.id=th.driver_id', 'left')->where('th.id', $id)->get('tx_things th')->row();
    }

    public function updateBus($id) {
      $input = $this->input->post();
      // $input = array_map('trim', $input);
      // echo "<pre>";print_r($input);die();
      $busData = array(
        'thing_name' => $input['thing_name'],
        'thing_reg_number' => $input['thing_reg_number'],
        'tracking_url' => $input['tracking_url'],
        'driver_id' => ($input['driver_id']=='')?null:$input['driver_id'],
        'last_modified_by' => $this->authorization->getAvatarId(),
        'tracking_url' => $input['tracking_url'],
        'bus_capacity' => $input['bus_capacity'],
        'permit_number' => $input['permit_number'],
        'permit_number_expiry_date' => $input['permit_number_expiry_date'] ? $input['permit_number_expiry_date'] : null,
        'rc_number' => $input['rc_number'],
        'rc_expiry_date' => $input['rc_expiry_date'] ? $input['rc_expiry_date'] : null,
        'insurance_number' => $input['insurance_number'],
        'insurance_number_expiry_date' => $input['insurance_number_expiry_date'] ? $input['insurance_number_expiry_date'] : null,
      );
      return $this->db->where('id', $id)->update('tx_things', $busData);
    }

    public function getDriverBuses($driver_id) {
      return $this->db->select('id,thing_name')
            ->from('tx_things')
            ->where("driver_id ", $driver_id)
            ->get()->result();
    }

    public function getDriverJourneys($thing_id, $date_sel=0) {
      $journeys = $this->db->select('id,journey_name,journey_type,days')
            ->from('tx_journeys')
            ->where("thing_id", $thing_id)
            ->get()->result();
      if($date_sel == 0) {
        return $journeys;
      }

      $day = strtoupper(date('l',strtotime($date_sel)));

      foreach ($journeys as $k => $journey) {
        $days = json_decode($journey->days);
        if(!in_array($day, $days)) {
          unset($journeys[$k]);
        }
      }
      return array_values($journeys);
    }

    public function getBusJourneys($thing_id) {
      $journeys = $this->db->select('id,journey_name,journey_type,days')
            ->from('tx_journeys')
            ->where("thing_id", $thing_id)
            ->get()->result();
      return $journeys;
    }

    public function getJourneyStudents($journeyId, $date=null) {
      if($date == null) {
        $date = date('Y-m-d');
      }
      $date = date('Y-m-d', strtotime($date));
      $day = date('l', strtotime($date));

      $this->db->select("sa.id as stdId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.admission_no, s.stop_name, s.id as stop_id, 'Absent' as status, CONCAT(cs.class_name, '', cs.section_name) as csName, CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name,CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, ifnull(m.mobile_no,'')  as mother_mobile_no, ifnull(f.mobile_no,'') as father_mobile_no")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      ->join('class_section cs', 'sy.class_section_id=cs.id')
      ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id')
      ->join('tx_stops s', 's.id=sro.stop_id')
      ->where('sro.journey_id', $journeyId)
      ->where_in('sa.admission_status', [1,2])
      ->where("DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date'");
      $this->db->where('sy.acad_year_id', $this->yearId);
      if(!empty($stdIds))
        $this->db->where_not_in('sa.id', $stdIds);
      $this->db->order_by('sa.first_name');
      $stdOverrides = $this->db->get()->result();
      foreach ($stdOverrides as $key => $std) {
        $stdIds[] = $std->stdId;
      }

      $this->db->select("sa.id as stdId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.admission_no, s.stop_name, s.id as stop_id, 'Absent' as status, CONCAT(cs.class_name, '', cs.section_name) as csName, CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name,CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, ifnull(m.mobile_no,'')  as mother_mobile_no, ifnull(f.mobile_no,'') as father_mobile_no")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      ->join('class_section cs', 'sy.class_section_id=cs.id')
      ->join('tx_student_journeys e', 'e.entity_source_id=sa.id')
      ->join('tx_stops s', 's.id=e.stop_id');
      $this->db->where('e.entity_type', 'Student');
      $this->db->where('e.journey_id', $journeyId);
      $this->db->where_in('sa.admission_status', [1,2]);
      $this->db->where('sy.acad_year_id', $this->yearId);
      $this->db->where('e.day', $day);
      if(!empty($stdIds))
        $this->db->where_not_in('sa.id', $stdIds);
      $this->db->order_by('sa.first_name');
      $stdData = $this->db->get()->result();

      $stdArr = array_merge($stdOverrides, $stdData);
      return $stdArr;
    }

    public function getJourneyStopsStudents($journeyId, $stopId, $date=null) {
      if($date == null) {
        $date = date('Y-m-d');
      }
      $date = date('Y-m-d', strtotime($date));
      $day = date('l', strtotime($date));

      $this->db->select("sa.id as stdId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.admission_no, s.stop_name, s.id as stop_id, 'Absent' as status, CONCAT(cs.class_name, ' ', cs.section_name) as csName, CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name, CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, ifnull(m.mobile_no,'-')  as mother_mobile_no, ifnull(f.mobile_no,'-') as father_mobile_no, 'Student' as type, ifnull(sa.enrollment_number, '-') as enrollment_no")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      ->join('class_section cs', 'sy.class_section_id=cs.id')
      ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id')
      ->join('tx_stops s', 's.id=sro.stop_id')
      ->where('sro.journey_id', $journeyId)
      ->where_in('sa.admission_status', [1,2])
      ->where("DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date'");
      $this->db->where('sy.acad_year_id', $this->yearId);
      if(!empty($stopId)){
        $this->db->where_in('sro.stop_id', $stopId);
      }
      if(!empty($stdIds))
        $this->db->where_not_in('sa.id', $stdIds);
      $this->db->order_by('sa.first_name');
      $stdOverrides = $this->db->get()->result();
      foreach ($stdOverrides as $key => $std) {
        $stdIds[] = $std->stdId;
      }

      $this->db->select("sa.id as stdId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.admission_no, s.stop_name, s.id as stop_id, 'Absent' as status, CONCAT(cs.class_name, '', cs.section_name) as csName, CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name,CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, ifnull(m.mobile_no,'')  as mother_mobile_no, ifnull(f.mobile_no,'') as father_mobile_no, 'Student' as type, ifnull(sa.enrollment_number, '-') as enrollment_no")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      ->join('class_section cs', 'sy.class_section_id=cs.id')
      ->join('tx_student_journeys e', 'e.entity_source_id=sa.id')
      ->join('tx_stops s', 's.id=e.stop_id');
      $this->db->where('e.entity_type', 'Student');
      $this->db->where('e.journey_id', $journeyId);
      if(! empty($stopId)){
        $this->db->where_in('s.id', $stopId);
      }
      $this->db->where_in('sa.admission_status', [1,2]);
      $this->db->where('sy.acad_year_id', $this->yearId);
      $this->db->where('e.day', $day);
      if(!empty($stdIds))
        $this->db->where_not_in('sa.id', $stdIds);
      $this->db->order_by('sa.first_name');
      $stdData = $this->db->get()->result();

      $stdArr = array_merge($stdOverrides, $stdData);
      foreach($stdArr as $key => $value){
        $value->employee_code = '-';
        $value->staffId = '-';
        $value->number = '-';
      }
      // echo "<pre>";print_r($stdArr);die();
      return $stdArr;
    }

    public function addAttendance() {
      $stdId = $_POST['stdId'];
      $journey_id = $_POST['journey_id'];
      $event = $_POST['event'];
      $stop_id = $_POST['stop_id'];
      $data = array(
        'student_id' => $stdId,
        'journey_id' => $journey_id,
        'event' => $event,
        'stop_id' => $stop_id
      );
      return $this->db->insert('tx_attendance', $data);
    }

    public function getStudentsByStop($stop_id, $journey_type) {
      $event = 'DROP';
      if($journey_type == 'PICKING') {
        $event = 'PICK';
      }
      $date = date('Y-m-d');
      $day = strtoupper(date('l'));

      $this->db->select("sa.id as stdId")
      ->from('student_admission sa')
      ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id')
      ->where('sro.event', $event)
      ->where('sro.stop_id', $stop_id)
      ->where("((sro.type='day' AND sro.day='$day') OR (sro.type='date' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')='$date') OR (sro.type='range' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date'))");
      $stdOverrides = $this->db->get()->result();
      $stdIds = array();
      foreach ($stdOverrides as $key => $std) {
        $stdIds[] = $std->stdId;
        $studentIds[] = $std->stdId;
      }

      $this->db->select("sa.id as stdId")
      ->from('student_admission sa')
      ->join('tx_student_journeys e', 'e.entity_source_id=sa.id');
      if($journey_type == 'PICKING') {
        $this->db->where('e.usual_pickup_stop_id', $stop_id);
      } else {
        $this->db->where('e.usual_drop_stop_id', $stop_id);
      }
      $this->db->where('e.entity_type', 'Student');
      if(!empty($stdIds))
        $this->db->where_not_in('e.entity_source_id', $stdIds);
      $stdData = $this->db->get()->result();
      $studentIds = array();
      foreach ($stdData as $key => $std) {
        $studentIds[] = $std->stdId;
      }
      return $studentIds;
    }

    public function getStudentsByJourneyStop($stop_id, $journey_id) {
      $date = date('Y-m-d');
      $day = strtoupper(date('l'));

      $this->db->select("sa.id as stdId")
      ->from('student_admission sa')
      ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id')
      ->where('sro.stop_id', $stop_id)
      ->where('sro.journey_id', $journey_id)
      ->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')");
      $stdOverrides = $this->db->get()->result();
      $stdIds = array();
      $studentIds = array();
      foreach ($stdOverrides as $key => $std) {
        $studentIds[] = $std->stdId;
      }

      $this->db->select("sa.id as stdId")
      ->from('student_admission sa')
      ->join('tx_student_journeys e', 'e.entity_source_id=sa.id');
      $this->db->where('e.stop_id', $stop_id);
      $this->db->where('e.journey_id', $journey_id);
      $this->db->where('e.day', $day);
      if(!empty($studentIds))
        $this->db->where_not_in('e.entity_source_id', $studentIds);
      $stdData = $this->db->get()->result();
      foreach ($stdData as $key => $std) {
        $studentIds[] = $std->stdId;
      }

      return $studentIds;
    }

    public function getStaffByJourneyStop($stop_id, $journey_id) {
      $day = strtoupper(date('l'));

      $staffIds = array();

      $this->db->select("sj.staff_id as staffId")
      ->from('tx_staff_journeys sj');
      $this->db->where('sj.stop_id', $stop_id);
      $this->db->where('sj.journey_id', $journey_id);
      $this->db->where('sj.day', $day);
      $staffData = $this->db->get()->result();
      foreach ($staffData as $key => $staff) {
        $staffIds[] = $staff->staffId;
      }

      return $staffIds;
    }

    public function getSectionStudents($sectionId) {
      $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName,");
      $this->db->from('student_admission sa');
      $this->db->join('student_year sy', 'sa.id=sy.student_admission_id');
      $this->db->where('sy.class_section_id', $sectionId);
      $this->db->where('sy.acad_year_id', $this->yearId);
      return $this->db->get()->result();
    }

    public function getJourneyStops($journeyId) {
      return $this->db->select('id, stop_name')
            ->from('tx_stops')
            ->where("id in (select stop_id from tx_journey_stops where journey_id=$journeyId)")
            ->get()->result();
    }

    public function changeBus($thing_id, $new_thing_id, $journey_id) {
      $this->db->trans_begin();
      //update journey table
      $this->db->where('thing_id', $thing_id);
      if($journey_id != 'all') {
        $this->db->where('id', $journey_id);
      }
      $this->db->update('tx_journeys', array('thing_id' => $new_thing_id, 'refresh_required' => 1));

      //update thing table saying refresh required 1
      $things = [$thing_id, $new_thing_id];
      $this->db->where_in('id', $things);
      $this->db->update('tx_things', array('refresh_required' => 1));

      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      }
      $this->db->trans_commit();
      return 1;
    }

    public function getJourney($journey_id) {
      return $this->db->select('j.id, j.journey_name, j.thing_id, j.journey_type, j.days, TIME_FORMAT(j.tentative_start_time, "%h:%i %p") as tentative_start_time, TIME_FORMAT(j.tentative_end_time, "%h:%i %p") as tentative_end_time, s.stop_name, s.id as route_id, j.refresh_required')
                  ->from('tx_journeys j')
                  ->join('tx_journey_stops js', 'js.journey_id=j.id')
                  ->join('tx_stops s', 's.id=js.stop_id')
                  ->where('j.id', $journey_id)
                  ->get()->row();
    }

    public function getJourneyData($journey_id) {
      $result = $this->db_readonly->select('j.thing_id, j.id, j.journey_name, t.thing_name, j.journey_type')
        ->from('tx_journeys j')
        ->join('tx_things t', 'j.thing_id=t.id')
        ->where('j.id', $journey_id)
        ->get()->row();

      if (empty($result)) {
        $temp = new stdClass();
        $temp->id = '0';
        $temp->thing_id = '0';
        $temp->thing_name = 'Unknown';
        $temp->journey_type = 'Unknown';
        $temp->journey_name = 'Not Started';
        return $temp;
      }
      return $result;
    }

    public function getThingDataUsingRegNumber($thing_reg_number) {
      $result = $this->db_readonly->select('id as thing_id, thing_name, "Not Started" as journey_name, "0" as id, "Unknown" as journey_type')
        ->from('tx_things')
        ->where('thing_reg_number', $thing_reg_number)
        ->get()->row();

      if (empty($result)) {
        $temp = new stdClass();
        $temp->id = '0';
        $temp->thing_id = '0';
        $temp->thing_name = 'Unknown';
        $temp->journey_type = 'Unknown';
        $temp->journey_name = 'Not Started';
        return $temp;
      }

      return $result;
    }

    public function getThingByJourneyId($journey_id) {
      return $this->db->select('j.thing_id')->where('id', $journey_id)->get('tx_journeys j')->row()->thing_id;
    }

    public function getStopName($stop_id) {
      $stop = $this->db->select('s.stop_name')->where('id', $stop_id)->get('tx_stops s')->row();
      return $stop ? $stop->stop_name : '-';
    }

    public function getStudentsByAttendance($journeyId, $journey_type, $date) {
      $date = date('Y-m-d');
      $day = strtoupper(date('l'));

      $this->db->select("sro.student_id as stdId")
      ->from('tx_student_journeys_override sro')
      ->where('sro.journey_id', $journeyId)
      ->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')");
      $stdOverrides = $this->db->get()->result();
      $studentIds = array();
      foreach ($stdOverrides as $key => $std) {
        $studentIds[] = $std->stdId;
      }

      $this->db->select("e.entity_source_id as stdId")
      ->from('tx_student_journeys e')
      ->where('e.journey_id', $journeyId)
      ->where('e.day', $day);
      if(!empty($studentIds))
        $this->db->where_not_in('e.entity_source_id', $studentIds);
      $stdData = $this->db->get()->result();
      foreach ($stdData as $key => $std) {
        $studentIds[] = $std->stdId;
      }

      return $studentIds;

      /*$event = 'drop_at_school';
      if($journey_type == 'DROPPING') {
        $event = 'pick_at_school';
      }
      $result = $this->db->select('ta.student_id')
                ->from('tx_attendance ta')
                ->where('ta.journey_id', $journeyId)
                ->where('event', $event)
                ->where("DATE_FORMAT(ta.time, '%Y-%m-%d')='$date'")
                ->get()->result();

      $students = array();
      foreach ($result as $key => $value) {
        $students[] = $value->student_id;
      }
      return $students;*/
    }

    public function getStop($stop_id) {
      return $this->db->select("*")->where('id', $stop_id)->get('tx_stops')->row();
    }

    // public function getRoutesAndStops() {
    //   $result = $this->db->select("rt.id as routeId, rt.route_name, rt.total_km, st.stop_name, st.tentative_reach_time as stopTime")
    //             ->from('tx_routes rt')
    //             ->join('tx_stops st', 'rt.id=st.route_id', 'left')
    //             ->order_by('rt.id,st.order')
    //             ->get()->result();
    //   if(empty($result)) {
    //     return $result;
    //   }
    //   $routes = array();
    //   foreach ($result as $key => $value) {
    //     if(!array_key_exists($value->routeId, $routes)) {
    //       $routes[$value->routeId]['route_name'] = $value->route_name;
    //       $routes[$value->routeId]['total_km'] = $value->total_km;
    //       $routes[$value->routeId]['stops'] = array();
    //     }
    //     array_push($routes[$value->routeId]['stops'], array('name' => $value->stop_name, 'time' => $value->stopTime));
    //   }
    //   return $routes;
    // }

    public function getUniqueStops() {
      return $this->db->select('id, stop_name as stop_name')
      ->from('tx_stops')
      ->group_by('stop_name')
      ->get()->result();
    }

    public function getStopById($stop_id) {
      return $this->db->select('id, stop_name, landmark, TIME_FORMAT(tentative_reach_time, "%h:%i %p") as tentative_reach_time, distance_to_stop')->where('id', $stop_id)->get('tx_stops')->row();
    }

    public function getStudentData($stdIds) {
      if(empty($stdIds)) {
        return array();
      }
      $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, CONCAT(cs.class_name,'',cs.section_name) as csName, cs.class_id, cs.id as section_id");
      $this->db->from('student_admission sa');
      $this->db->join('student_year sy', 'sy.student_admission_id=sa.id');
      $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
      $this->db->where_in('sa.id', $stdIds);
      $this->db->where('sy.acad_year_id', $this->yearId);
      return $this->db->get()->result();
    }

    public function getStaffData($staff) {
      $this->db->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as stdName, 'Staff' as csName");
      $this->db->from('staff_master sm');
      $this->db->where_in('sm.id', $staff);
      return $this->db->get()->result();
    }

    // public function getStudentActualRoutes($stdId) {
    //   $this->db->select('rt1.route_name as pickRoute, rt2.route_name as dropRoute, st1.stop_name as pickStop, st2.stop_name as dropStop');
    //   $this->db->from('tx_student_journeys sr');
    //   $this->db->join('tx_routes rt1', 'rt1.id=sr.usual_pickup_route_id', 'left');
    //   $this->db->join('tx_routes rt2', 'rt2.id=sr.usual_drop_route_id', 'left');
    //   $this->db->join('tx_stops st1', 'st1.id=sr.usual_pickup_stop_id', 'left');
    //   $this->db->join('tx_stops st2', 'st2.id=sr.usual_drop_stop_id', 'left');
    //   $this->db->where('sr.entity_type','Student');
    //   $this->db->where('sr.entity_source_id',$stdId);
    //   return $this->db->get()->row();
    //   // echo $this->db->last_query(); die();
    // }

    public function getStudentOverrides($stdId) {
      $this->db->select("sro.id as sroId, st.stop_name, j.journey_name, sro.type, sro.day, sro.journey_type, DATE_FORMAT(sro.from_date,'%d-%m-%Y') as startDate, DATE_FORMAT(sro.to_date, '%d-%m-%Y') as endDate");
      $this->db->from('tx_student_journeys_override sro');
      $this->db->join('tx_journeys j', 'j.id=sro.journey_id');
      $this->db->join('tx_stops st', 'st.id=sro.stop_id');
      $this->db->where('sro.student_id', $stdId);
      return $this->db->get()->result();
    }

    public function addStudentOverride($input) {
      $avatarId = $this->authorization->getAvatarId();
      $data = array(
          'student_id' => $input['student_id'],
          'journey_id' => $input['journey_id'],
          'stop_id' => $input['stop_id'],
          'journey_type' => $input['journey_type'],
          'type' => $input['type'],
          'day' => ($input['journey_day'])?$input['journey_day']:NULL,
          'created_by' => $avatarId,
          'last_modified_by' => $avatarId
        );
      $data['from_date'] = null;
      if($input['from_date'] != '') {
        $data['from_date'] = date('Y-m-d', strtotime($input['from_date']));
      }

      $data['to_date'] = null;
      if($input['to_date'] != '') {
        $data['to_date'] = date('Y-m-d', strtotime($input['to_date']));
      }
      return $this->db->insert('tx_student_journeys_override', $data);
    }

    public function deleteOverride($overrideId) {
      return $this->db->where('id', $overrideId)->delete('tx_student_journeys_override');
    }

    public function driverActivation($userId, $active){
      return $this->db->where('id', $userId)->update('users', array('active' => $active));
    }

    public function getClasses(){
        $this->db->select('id, class_name');
        $this->db->from('class');
        $this->db->where('is_placeholder',0);
        $this->db->where('acad_year_id',$this->yearId);
        if($this->current_branch) {
            $this->db->where('branch_id',$this->current_branch);
        }
        return $this->db->get()->result();
    }

    public function getStduentsAndJourneys($section_id, $std_name) {
      // echo "<pre>";print_r($std_name);die();
      $this->db->select("sa.id as stdId, sa.admission_no, sy.roll_no, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, ifnull(txs.stop_name, '') as stop_name, ifnull(txj.journey_name,'') as journey_name, ifnull(txsr.day,'') as day, ifnull(TIME_FORMAT(txj.tentative_start_time, '%h:%i %p'), '') as start_time, ifnull(TIME_FORMAT(txj.tentative_end_time, '%h:%i %p'),'') as end_time, txj.journey_type, txj.id as journeyId, txjo.journey_name as override_journey, txso.stop_name as override_stop, DATE_FORMAT(sro.from_date, '%d-%m-%Y') as from_date, DATE_FORMAT(sro.to_date, '%d-%m-%Y') as to_date, sro.id as sroId, txsr.id as stdJourneyId, sro.type as sroType, sro.day as sroDay, concat(c.class_name, '', ifnull(cs.section_name, '')) as class_section")
      ->from('student_admission sa')
      ->join('student_year sy', 'sy.student_admission_id=sa.id')
      ->join('class c', 'c.id=sy.class_id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->where('sa.admission_status', '2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->join('tx_student_journeys txsr', "txsr.entity_source_id=sa.id and txsr.entity_type='Student'", 'left')
      ->join('tx_stops txs', 'txs.id=txsr.stop_id', 'left')
      ->join('tx_journeys txj', 'txj.id=txsr.journey_id', 'left')
      ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id', 'left')
      ->join('tx_stops txso', 'txso.id=sro.stop_id', 'left')
      ->join('tx_journeys txjo', 'txjo.id=sro.journey_id', 'left');
      if($section_id != "none"){
        $this->db->where('sy.class_section_id', $section_id);
      }
      if($std_name != 'none'){
        // $this->db->where("sa.first_name LIKE", "%{$std_name}%");
        $this->db->where("CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) LIKE '%{$std_name}%'");
      }
      return $this->db->where('sy.acad_year_id', $this->yearId)
                    ->get()
                    ->result();
    }

    public function deletestdJourney($stdJourneyId) {
      return $this->db->where('id', $stdJourneyId)->delete('tx_student_journeys');
    }

    public function getJourneysByType($type = 'All') {
      $this->db_readonly->select('txj.id, txj.journey_name')->from('tx_journeys txj');

      if ($type != 'All') {
        $this->db_readonly->where('txj.journey_type', $type);
      }

      $result = $this->db_readonly->get()->result();

      return $result;
    }

    public function getAllJourneys() {
      return $this->db_readonly->select('txj.id, txj.journey_name')
      ->from('tx_journeys txj')
      ->get()->result();
    }

    public function getStopsByJourneyId($journey_id) {
      return $this->db_readonly->select('s.id,s.stop_name')
      ->from('tx_stops s')
      ->where("s.id in (select stop_id from tx_journey_stops where journey_id=$journey_id)")
      ->get()->result();
    }

    public function addStudentJourneys($input) {
      foreach ($input['student_ids'] as $stdId) {
        if(isset($input['pick_days'])) {
          foreach ($input['pick_days'] as $day) {
            $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $stdId,
              'journey_id' => $input['pick_journey'],
              'stop_id' => $input['pick_stop'],
              'day' => $day,
              'journey_type' => 'PICKING'
            );
          }
        }

        if(isset($input['drop_days'])) {
          foreach ($input['drop_days'] as $day) {
            $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $stdId,
              'journey_id' => $input['drop_journey'],
              'stop_id' => $input['drop_stop'],
              'day' => $day,
              'journey_type' => 'DROPPING'
            );
          }
        }
      }
      
      return $this->db->insert_batch('tx_student_journeys', $data);
      // echo "<pre>"; print_r($data); die();
    }

    private function recordExists($student_id, $journey_id, $stop_id, $day) {
        $this->db->select('id');
        $this->db->from('tx_student_journeys');
        $this->db->where('entity_source_id', $student_id);
        $this->db->where('journey_id', $journey_id);
        $this->db->where('stop_id', $stop_id);
        $this->db->where('day', $day);
        $query = $this->db->get();
        return $query->row() ? true : false;
      }

    public function submitStudentEditJourneys($input) {

      $data = [];

      

      if ($input['journey_type'] == 'both') {
        foreach ($input['days'] as $day) {
          if (!$this->recordExists($input['student_id'], $input['journey_id'], $input['stop_id'], $day)) {
            $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $input['student_id'],
              'journey_id' => $input['journey_id'],
              'stop_id' => $input['stop_id'],
              'day' => $day,
              'journey_type' => 'PICKING'
            );
          }
        }

        $this->db->select('journey_name');
        $this->db->from('tx_journeys');
        $this->db->where('id', $input['journey_id']);
        $query = $this->db->get();
        $result = $query->row();

        if (!$result) {
            return false;
        }

        $journey_name = strtolower(trim($result->journey_name));
        $drop_journey_name = str_replace('pickup', 'drop', $journey_name);
        $drop_journey_name = strtolower(trim($drop_journey_name));

        $this->db->select('id');
        $this->db->from('tx_journeys');
        $this->db->where('lower(journey_name)', $drop_journey_name);
        $query = $this->db->get();
        $drop_result = $query->row();

        if ($drop_result && $drop_result->id != $input['journey_id']) {
          // return false;
        // }

          $drop_journey_id = $drop_result->id;

          foreach ($input['days'] as $day) {
            if (!$this->recordExists($input['student_id'], $drop_journey_id, $input['stop_id'], $day)) {
              $data[] = array(
                'entity_type' => 'Student',
                'entity_source_id' => $input['student_id'],
                'journey_id' => $drop_journey_id,
                'stop_id' => $input['stop_id'],
                'day' => $day,
                'journey_type' => 'DROPPING'
              );
            }
          }
        }
      } else {
        foreach ($input['days'] as $day) {
          if (!$this->recordExists($input['student_id'], $input['journey_id'], $input['stop_id'], $day)) {
            $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $input['student_id'],
              'journey_id' => $input['journey_id'],
              'stop_id' => $input['stop_id'],
              'day' => $day,
              'journey_type' => $input['journey_type']
            );
          }
        }
      }

      if (!empty($data)) {
        return $this->db->insert_batch('tx_student_journeys', $data);
      } else {
        return false;
      }
    }

    public function getStudentJourneys($stdId) {
      $result = $this->db->select("txs.id as stdJourneyId, txj.id as journeyId, txj.journey_name, txj.journey_type, txst.id as stopId, txst.stop_name, txs.day, TIME_FORMAT(txj.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(txj.tentative_end_time, '%h:%i %p') as endTime")
      ->from('tx_student_journeys txs')
      ->join('tx_journeys txj', 'txj.id=txs.journey_id')
      ->join('tx_stops txst', 'txst.id=txs.stop_id')
      ->where('txs.entity_source_id', $stdId)
      ->get()->result();
      
      $journeys = array();
      foreach ($result as $key => $journey) {
        if(!array_key_exists($journey->day, $journeys)) {
          $journeys[$journey->day] = array();
        }
        $journeys[$journey->day][] = $journey;
      }

      $sorted = array();
      $weekdays = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      foreach ($weekdays as $day) {
        if(array_key_exists($day, $journeys))
          $sorted[$day] = $journeys[$day];
      }

      return $sorted;
      // echo "<pre>"; print_r($this->db->last_query()); die();
    }

    public function getJourneyDays($journey_id) {
      $days_json = $this->db->select('days')->where('id', $journey_id)->get('tx_journeys')->row()->days;
      return json_decode($days_json);
    }

    public function removeStudentJourney($stdJourneyId) {
      return $this->db->where('id', $stdJourneyId)->delete('tx_student_journeys');
    }

    public function removeStaffJourney($staff_journey_id) {
      return $this->db->where('id', $staff_journey_id)->delete('tx_staff_journeys');
    }

    public function addLog($log_data) {
      return $this->db->insert('tx_logs', $log_data);
    }

    public function getTxLogs($date, $bus) {
      $logs = $this->db->select('th.*, DATE_FORMAT(th.event_time, "%d-%m-%Y %r") AS display_event_time')
      ->from('tx_logs th')
      ->where("DATE_FORMAT(event_time, '%d-%m-%Y')='$date' and thing_id='$bus'")
      ->order_by('id', "DESC")
      ->get()->result();

      $data = array();
      foreach ($logs as $key => $log) {
        $std_json = json_decode($log->notifications_sent_to);
        $response = json_decode(json_decode($log->notification_response));
        $std = array();
        if(!empty($std_json)) {
          foreach ($std_json as $key => $stdj) {
            $std[] = $stdj->stdName.' ('.$stdj->csName.')';
          }
        }
        $data[$log->id] = $log;
        $data[$log->id]->students = $std;
        $total_recipients = 2 * count($std);
        if(isset($response->success)) {
          $sent = $response->success + $response->failure;
          $noToken = $total_recipients - $sent;
          $data[$log->id]->status = "Success: ".$response->success."<br>Failure: ".$response->failure."<br>No Token: ".$noToken;
        } else {
          $data[$log->id]->status = "Success: 0<br>Failure: 0<br>No Token: ".$total_recipients;
        }
      }
      return $data;
    }

    public function getLogData($logId) {
      $result = $this->db->select("*")->where('id', $logId)->get('tx_logs')->row();

      $data = json_decode($result->notification_json);

      $user_ids = array_map(function($data) {
        return $data->user_id;
      }, $data);

      $text_sent_to_ids = array_map(function($data) {
        return $data->text_sent_to_id;
      }, $data);
      // echo "<pre>";print_r(implode(',', $text_sent_to_ids));die();

      $users_data = $this->db->select("
          p.id as parent_id,
          CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.student_middle_name, ''), ' ', IFNULL(sa.last_name, '')) as student_name,
          CONCAT(cs.class_name, ' ', cs.section_name) as class_section_name,
          COALESCE(tst.status, 'NO_STATUS') as status
      ")
      ->from('parent p')
      ->join('avatar a', 'a.stakeholder_id = p.id')
      ->join('users u', 'u.id = a.user_id')
      ->join('student_admission sa', 'sa.id = p.student_id')
      ->join('student_year sy', 'sa.id = sy.student_admission_id')
      ->join('class_section cs', 'sy.class_section_id = cs.id')
      ->join('class c', 'c.id = cs.class_id')
      ->join('text_sent_to tst', 'tst.stakeholder_id = p.id AND tst.id IN (' . implode(',', $text_sent_to_ids) . ')', 'left')
      ->where('sy.acad_year_id', $this->yearId)
      ->where('c.acad_year_id', $this->yearId)
      ->where_in('u.id', $user_ids)
      ->where('a.avatar_type', 2)
      ->order_by('cs.class_name', 'asc')
      ->order_by('sa.first_name', 'asc')
      ->get()
      ->result();
      $result->total = count($users_data);
      $parent_ids = array_map(function($users_data) {
        return $users_data->parent_id;
      }, $users_data);

      $grouped_by_status = [];
      foreach ($users_data as $parent) {
          $status = $parent->status;

          if (!isset($grouped_by_status[$status])) {
              $grouped_by_status[$status] = [];
          }

          $grouped_by_status[$status][] = $parent->student_name . ' (' . $parent->class_section_name . ')';
      }

      foreach ($grouped_by_status as $status => $names) {
          $grouped_by_status[$status] = implode(', ', $names);
      }

      if (isset($grouped_by_status['NO_STATUS'])) {
          $grouped_by_status['NO_TOKENS']= $grouped_by_status['NO_STATUS'];
          unset($grouped_by_status['NO_STATUS']);
      }

      if(isset($grouped_by_status['NOT_FOUND'])){
        $grouped_by_status['USER_INACTIVE'] = $grouped_by_status['NOT_FOUND'];
        unset($grouped_by_status['NOT_FOUND']);
      }

      if(isset($grouped_by_status['INVALID_ARGUMENT'])){
        $grouped_by_status['INVALID_TOKEN'] = $grouped_by_status['INVALID_ARGUMENT'];
        unset($grouped_by_status['INVALID_ARGUMENT']);
      }

      $result->status = $grouped_by_status;

      return $result;
    }

    public function getStaffJourneys($staff_name) {
      $this->db->select("sm.id as staff_id, sj.id as staffJourneyId, j.id as journeyId, s.id as stopId, CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staffName, j.journey_name, sj.day, sj.journey_type, s.stop_name")
      ->from('staff_master sm')
      ->join('tx_staff_journeys sj', 'sj.staff_id=sm.id', 'left')
      ->join('tx_journeys j', 'j.id=sj.journey_id', 'left')
      ->join('tx_stops s', 's.id=sj.stop_id', 'left')
      ->where('sm.status', 2);
      if ($staff_name != '') {
          $this->db->where("CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) LIKE '%{$staff_name}%'");
      }
      $this->db->order_by('sm.first_name');
      $result = $this->db->get()->result();
      return $result;
    }

    public function getStaffJourney($staffId) {
      $result = $this->db->select("sj.id as staffJourneyId, j.id as journey_id, sj.staff_id, j.journey_name, s.id as stopId, sj.day, sj.journey_type, s.stop_name, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as endTime")
      ->from('tx_staff_journeys sj')
      ->join('tx_journeys j', 'j.id=sj.journey_id')
      ->join('tx_stops s', 's.id=sj.stop_id')
      ->where('sj.staff_id', $staffId)
      ->get()->result();

      // echo "<pre>"; print_r($this->db->last_query()); die();

      $journeys = array();
      foreach ($result as $key => $journey) {
        if(!array_key_exists($journey->day, $journeys)) {
          $journeys[$journey->day] = array();
        }
        $journeys[$journey->day][] = $journey;
      }

      $sorted = array();
      $weekdays = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      foreach ($weekdays as $day) {
        if(array_key_exists($day, $journeys))
          $sorted[$day] = $journeys[$day];
      }

      return $sorted;
    }

    public function saveStaffJourneys() {
      $input = $this->input->post();
      foreach ($input['days'] as $day) {
        $data[] = array(
          'staff_id' => $input['staff_id'],
          'journey_id' => $input['journey_id'],
          'stop_id' => $input['stop_id'],
          'day' => $day,
          'journey_type' => $input['journey_type']
        );
      }
      
      return $this->db->insert_batch('tx_staff_journeys', $data);
    }

    public function addAttendanceData($journey_data) {
      //Check for redundancy
      $laps_time = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', time()).' -1 minute'));
      $att = $this->db->select('id')->from('tx_attendance')->where('rfid', $journey_data['rfid'])->where('thing_id', $journey_data['thing_data_id'])->where("updated_at>=' $laps_time'")->get()->row();
      if(!empty($att)) {
        return 0;
      }
      $data = array(
        'rfid' => $journey_data['rfid'],
        'journey_id' => $journey_data['journey_id'],
        'journey_type' => $journey_data['journey_type'],
        'thing_id' => $journey_data['thing_data_id'],
        'latitude' => $journey_data['latitude'],
        'longitude' => $journey_data['longitude']
      );
      $status = $this->db->insert('tx_attendance', $data);
      return $status;

    }

    public function getStudentByRFID($rfid) {
      return $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name")->where("REPLACE(LTRIM(REPLACE(rfid_number,'0',' ')),' ','0')='$rfid'")->get('student_admission sa')->row();
    }

    public function changeJourney($input) {
      return $this->db->where('journey_id', $input['journey_id'])
      ->where_in('entity_source_id', $input['student_ids'])
      ->where('entity_type', 'Student')
      ->update('tx_student_journeys', array('journey_id' => $input['change_journey_id'], 'stop_id' => $input['change_stop_id']));
    }

    private function _getStoredJourneys($student_id, $journey_type) {
      $date = date('Y-m-d');
      $day = date('l');
      $journeys = array();
      $stdOverrides = $this->db->select("sro.journey_id")
          ->from('tx_student_journeys_override sro')
          ->where('sro.student_id', $student_id)
          ->where('sro.journey_type', $journey_type)
          ->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')")->get()->result();
      $jIds = [];
      foreach ($stdOverrides as $k => $std) {
        $journeys[] = $std->journey_id;
        $jIds[] = $std->journey_id;
      }

      $this->db->select("tsj.journey_id")
        ->from('tx_student_journeys tsj')
        ->where('tsj.entity_source_id', $student_id)
        ->where('tsj.entity_type', 'Student')
        ->where('tsj.journey_type', $journey_type)
        ->where("tsj.day", $day);
        if(!empty($jIds))
          $this->db->where_not_in('tsj.journey_id', $jIds);
        $stdRoute = $this->db->get()->result();

      foreach ($stdRoute as $k => $std) {
        $journeys[] = $std->journey_id;
      }

      return $journeys;
    }

    private function _checkMismatchExists($student_id, $journey_type, $actual_journey, $stored_journey=NULL) {
      $this->db->select('id')
      ->from('tx_mismatch_journeys')
      ->where('student_id', $student_id)
      ->where('journey_type', $journey_type)
      ->where('resolved', 0)
      ->where('actual_journey_id', $actual_journey);
      if($stored_journey) {
        $this->db->where('stored_journey_id', $stored_journey);
      } else {
        $this->db->where('stored_journey_id is null');
      }
      $mismatch = $this->db->get()->row();
      if(empty($mismatch)) {
        return 0;
      }
      return $mismatch->id;
    }

    public function update_mismatch_if_exist($student_id, $journey_id, $journey_type) {
      $stored_journeys = $this->_getStoredJourneys($student_id, $journey_type);
      $stored_journey_id = NULL;
      if(empty($stored_journeys)) {
        $mismatch_id = $this->_checkMismatchExists($student_id, $journey_type, $journey_id);
        $remarks = 'Student not assigned to any journey';
      } else {
        if(in_array($journey_id, $stored_journeys)) {
          return 1;
        }
        $stored_journey_id = $stored_journeys[0];
        $remarks = 'Mismatch occurred';
        if(count($stored_journeys) > 1) {
          $remarks = 'There are '.count($stored_journeys).' '.$journey_type.' journeys.';
        }
        $mismatch_id = $this->_checkMismatchExists($student_id, $journey_type, $journey_id, $stored_journey_id);
      }

      if($mismatch_id) {
        $this->db->where('id', $mismatch_id);
        $this->db->set('frequency', '`frequency`+ 1', FALSE);
        $this->db->set('remarks', $remarks);
        $this->db->update('tx_mismatch_journeys');
      } else {
        $data = array(
          'actual_journey_id' => $journey_id,
          'stored_journey_id' => $stored_journey_id,
          'journey_type' => $journey_type,
          'student_id' => $student_id,
          'remarks' => $remarks,
          'frequency' => 1
        );
        $this->db->insert('tx_mismatch_journeys', $data);
      }
    }

    public function getMismatchJourneys($class_id) {
      $this->db->select("mj.id as mismatchId,sa.id as student_id, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) as student_name, ifnull(sa.admission_no, '-') as admission_no, ifnull(sa.enrollment_number, '-') as enrollment_no, CONCAT(cs.class_name,cs.section_name) as class_section, j1.journey_name as actual_journey, j2.journey_name as stored_journey, mj.journey_type, remarks, frequency, resolved")
      ->from('tx_mismatch_journeys mj')
      ->join('tx_journeys j1', 'j1.id=mj.actual_journey_id', 'left')
      ->join('tx_journeys j2', 'j2.id=mj.stored_journey_id', 'left')
      ->join('student_admission sa', 'sa.id=mj.student_id')
      ->join('student_year sy', 'sa.id=sy.student_admission_id')
      ->join('class_section cs', 'cs.id=sy.class_section_id');
      if($class_id) {
        $this->db->where('sy.class_id', $class_id);
      }
      $data = $this->db->get()->result();
      return $data;
      // echo "<pre>"; print_r($data); die();
    }

    public function resolveMismatch($mismatchId) {
      return $this->db->where('id', $mismatchId)->update('tx_mismatch_journeys', array('resolved' => 1));
    }

    public function getDailyJourneys($journey_type, $date) {
      $result = $this->db->select("j.id as journey_id, j.thing_id, j.journey_name, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as endTime, l.event_type, t.tracking_url, t.thing_name, j.days, (ADDTIME(CURTIME(),'05:30:00') BETWEEN j.tentative_start_time AND j.tentative_end_time AND DATE_FORMAT(CURDATE(), '%d-%m-%Y')='$date') as show_tracking, t.thing_reg_number as thing_reg_number")
        ->from('tx_journeys j')
        ->join('tx_things t', 't.id=j.thing_id')
        ->join('tx_logs l', "j.id=l.journey_id AND DATE_FORMAT(l.event_time, '%d-%m-%Y')='$date'", 'left')
        ->where('j.journey_type', $journey_type)
        ->order_by('j.id')
        ->get()->result();

      // echo '<pre>';print_r($this->db->last_query());die();

      $journeys = array();
      $current_time = strtotime(date('H:i:s'));
      $today = date('d-m-Y');
      $day = strtoupper(date('l', strtotime($date)));

      foreach ($result as $key => $res) {
        $days = json_decode($res->days);
        if(!in_array($day, $days)) {
          continue;
        }
        if(!array_key_exists($res->journey_id, $journeys)) {
          $journeys[$res->journey_id] = array();
          $journeys[$res->journey_id]['geo_fence_reminder'] = 0;
          $journeys[$res->journey_id]['eta_reminder'] = 0;
          $journeys[$res->journey_id]['journey_id'] = $res->journey_id;
          $journeys[$res->journey_id]['journey_name'] = $res->journey_name;
          $journeys[$res->journey_id]['thing_id'] = $res->thing_id;
          $journeys[$res->journey_id]['startTime'] = $res->startTime;
          $journeys[$res->journey_id]['endTime'] = $res->endTime;
          $journeys[$res->journey_id]['status'] = 'Not Started';
          $journeys[$res->journey_id]['event_type'] = $res->event_type;
          $journeys[$res->journey_id]['tracking_url'] = $res->tracking_url;
          $journeys[$res->journey_id]['thing_name'] = $res->thing_name;
          $journeys[$res->journey_id]['show_tracking'] = $res->show_tracking;
          $journeys[$res->journey_id]['thing_reg_number'] = $res->thing_reg_number;
        }
        if ($res->event_type == 'Eta') {
          $journeys[$res->journey_id]['eta_reminder'] ++;
        }
        if ($res->event_type == 'Geofence') {
          $journeys[$res->journey_id]['geo_fence_reminder'] ++;
        }
        $startTime = strtotime($res->startTime);
        $endTime = strtotime($res->endTime);
        if($journey_type == 'PICKING') {
          if($res->event_type == 'Attendance' || $res->event_type == 'Eta') {
            $journeys[$res->journey_id]['status'] = 'Running';
          } else if($res->event_type == 'Geofence'){
            $journeys[$res->journey_id]['status'] = 'Completed';
          }
        } else {
          if($res->event_type == 'Attendance' || $res->event_type == 'Eta') {
            $journeys[$res->journey_id]['status'] = 'Running';
          } else if($res->event_type == 'Geofence'){
            $journeys[$res->journey_id]['status'] = 'Running';
          }
        }
        if($res->event_type != '' || $res->event_type != NULL) {
          if($date == $today) {
            if($endTime<$current_time) {
              $journeys[$res->journey_id]['status'] = 'Completed';
            }
          } else {
            $journeys[$res->journey_id]['status'] = 'Completed';
          }
        }
      }

      //Add the number of students in each journey
      $total_students = $this->db_readonly->query("select journey_id, count(id) as no_students from tx_student_journeys where day='$day' and journey_type='$journey_type' and entity_type='Student' and entity_source_id in (select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id and acad_year_id=$this->yearId where sa.admission_status=2 and sy.promotion_status!= 4 and sy.promotion_status!=5) group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $jour['total_students'] = 0;
        foreach ($total_students as $tot) {
          if ($jour['journey_id'] == $tot->journey_id) {
            $jour['total_students'] = $tot->no_students;
            break;
          }
        }
      }

      //Add the number of students present in each journey
      $present_students = $this->db_readonly->query("select txa.journey_id, count(distinct(sa.rfid_number)) as no_students from tx_attendance txa join student_admission sa on REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0')=txa.rfid where sa.rfid_number is not null and date_format(updated_at, '%d-%m-%Y')='$date' and journey_type='$journey_type' group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $found = 0;
        foreach ($present_students as $pres) {
          if ($jour['journey_id'] == $pres->journey_id) {
            $jour['present_all_students'] = $pres->no_students;
            $found = 1;
            break;
          }
        }
        if ($found == 0) {
          $jour['present_all_students'] = 0;
        }
      }

      $present_registered_students = $this->db_readonly->query("select txa.journey_id, count(distinct(sa.rfid_number)) as no_students from tx_attendance txa join student_admission sa on REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0')=txa.rfid join tx_student_journeys tsj on sa.id=tsj.entity_source_id and tsj.entity_type='Student' and tsj.journey_id=txa.journey_id where sa.rfid_number is not null and date_format(updated_at, '%d-%m-%Y')='$date' and tsj.journey_type='$journey_type' group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $found = 0;
        foreach ($present_registered_students as $pres_reg) {
          if ($jour['journey_id'] == $pres_reg->journey_id) {
            $jour['present_registered_students'] = $pres_reg->no_students;
            $found = 1;
            break;
          }
        }
        if ($found == 0) {
          $jour['present_registered_students'] = 0;
        }
      }

      //Add the number of stops in each journey
      $total_stops = $this->db_readonly->query("select journey_id, count(id) as no_stops from tx_journey_stops group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $jour['total_stops'] = 0;
        foreach ($total_stops as $stop) {
          if ($jour['journey_id'] == $stop->journey_id) {
            $jour['total_stops'] = $stop->no_stops;
            break;
          }
        }
      }

      return $journeys;
    }

    private function _getAttendanceStudents($journey_id, $date) {
      $day = date('l', strtotime($date));
      $students = $this->db_readonly->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, 1 as existing, CONCAT(cs.class_name, cs.section_name) as cs_name, REPLACE(LTRIM(REPLACE(ifnull(sa.rfid_number, '-'),'0',' ')),' ','0') as rfid")
        ->from('tx_student_journeys sj')
        ->join('student_admission sa', 'sa.id=sj.entity_source_id')
        ->join('student_year sy', "sy.student_admission_id=sa.id and acad_year_id=$this->yearId")
        ->join('class_section cs', 'sy.class_section_id=cs.id')
        ->where('sj.day', $day)
        ->where('sj.journey_id', $journey_id)
        ->where('sa.admission_status=2 and sy.promotion_status!= 4 and sy.promotion_status!=5')
        ->order_by('sa.first_name')
        ->get()->result();

      $att_rfids = $this->db_readonly->select("sa.id as student_id, l.rfid as rfid_number, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, l.updated_at as tap_time")
        ->from("tx_attendance l")
        ->join('student_admission sa', "REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0')=l.rfid", 'left')
        ->where('l.journey_id', $journey_id)
        ->where("DATE_FORMAT(l.updated_at, '%d-%m-%Y')='$date'")
        ->get()->result();

      $rfids = array();
      foreach ($att_rfids as $key => $val) {
        if(!array_key_exists($val->student_id, $rfids)) {
          $rfids[$val->student_id] = array();
          $rfids[$val->student_id]['student_name'] = $val->student_name;
          $rfids[$val->student_id]['taps'] = array();
          $rfids[$val->student_id]['rfid'] = $val->rfid_number;
        }
        $tap = date('h:i a', strtotime($this->timezone_setter($val->tap_time)));
        array_push($rfids[$val->student_id]['taps'], $tap);
      }

      foreach ($students as $key => $std) {
        $students[$key]->taps = array();
        if(array_key_exists($std->student_id, $rfids)) {
          $students[$key]->taps = $rfids[$std->student_id]['taps'];
          $students[$key]->rfid = $rfids[$std->student_id]['rfid'];
          unset($rfids[$std->student_id]);
        }
      }
      if(!empty($rfids)) {
        foreach ($rfids as $std_id => $val) {
          $student = new stdClass();
          $student->student_id = $std_id;
          $student->student_name = $val['student_name'];
          $student->taps = $val['taps'];
          $student->rfid = $val['rfid'];
          $student->existing = 0; //student is not assigned but he tapped the rfid
          array_push($students, $student);
        }
      }

      return $students;
    }

    public function getDailyJourneyDetails($journey_id, $date) {
      $data['journey'] = $this->db->select("j.id as journey_id, j.thing_id, j.journey_name, j.journey_type, t.thing_name, t.tracking_url, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as endTime")->from('tx_journeys j')->join('tx_things t','t.id=j.thing_id')->where('j.id', $journey_id)->get()->row();

      $data['eta'] = $this->db->select("s.id as stop_id, s.stop_name, l.stop_id as eta_stop, l.event_time, DATE_FORMAT(ADDTIME(l.event_time, '05:30:00'), '%H-%i %p') as eta_reminder_sent_at")
      ->from('tx_stops s')
      ->join('tx_journey_stops js', 'js.stop_id=s.id', 'left')
      ->join('tx_logs l', "l.journey_id=js.journey_id AND s.id=l.stop_id AND DATE_FORMAT(l.event_time, '%d-%m-%Y')='$date' AND event_type='Eta'", 'left')
      ->where('js.journey_id', $journey_id)
      ->get()->result();

      //Get the attendance data
      $data['attendance'] = $this->_getAttendanceStudents($journey_id, $date);
      
      //Summarizing Attendance data
      $number_of_students = 0;
      $present_students = 0;
      $number_of_other_students = 0;
      foreach ($data['attendance'] as $key => $att) {
        if(!empty($att->taps)) {
          $present_students ++;
        }
        if($att->existing == 0) {
          $number_of_other_students ++;
        } else {
          $number_of_students ++;
        }
      }
      $data['number_of_students'] =$number_of_students;
      $data['present_students'] =$present_students;
      $data['number_of_other_students'] =$number_of_other_students;

      $geofence = array();
      $result = $this->db->select("l.journey_id, l.event_time")
      ->from('tx_logs l')
      ->where("DATE_FORMAT(l.event_time, '%d-%m-%Y')='$date'")
      ->where('l.journey_id', $journey_id)
      ->where('l.event_type', 'Geofence')
      ->get()->row();
      if($data['journey']->journey_type == 'PICKING') {
        if(!empty($result)) {
          $geofence['status'] = 'Reached School';
          $geofence['time'] = date('h:i a', strtotime($this->timezone_setter($result->event_time)));
        } else {
          $geofence['status'] = 'Not Started';
          $geofence['time'] = '';
          $attendance = $this->db->select('stop_id')->where('journey_id',$journey_id)->where('event_type','Attendance')->where("DATE_FORMAT(event_time, '%d-%m-%Y')='$date'")->get('tx_logs')->result();
          if(!empty($attendance)) {
            $geofence['status'] = 'In Journey';
          } else {
            $eta = $this->db->select('stop_id')->where('journey_id',$journey_id)->where('event_type','Eta')->where("DATE_FORMAT(event_time, '%d-%m-%Y')='$date'")->get('tx_logs')->result();
            if(!empty($eta)) {
              $geofence['status'] = 'In Journey';
            }
          }
        }
      } else {
        $geofence['status'] = 'Not Started';
        $geofence['time'] = '';
        $current_time = strtotime(date('H:i:s'));
        $today = date('d-m-Y');
        $startTime = strtotime($data['journey']->startTime);
        $endTime = strtotime($data['journey']->endTime);
        if(!empty($result)) {
          $geofence['status'] = 'In Journey';
          $geofence['time'] = date('h:i a', strtotime($this->timezone_setter($result->event_time)));
          if($date == $today) {
            if($endTime<$current_time) {
              $journeys[$result->journey_id]['status'] = 'Completed';
            }
          } else {
            $journeys[$result->journey_id]['status'] = 'Completed';
          }
        }
      }
      $data['geofence'] = $geofence;
      return $data;
      // echo '<pre>';print_r($data);die();
    }

    public function timezone_setter($input) {
      $myDateTime = new DateTime($input, new DateTimeZone('GMT'));
      $myDateTime->setTimezone(new DateTimeZone('Asia/Kolkata'));
      return $myDateTime->format('Y-m-d H:i:s');
    }

    public function getTodaysJourney($studentId) {
      $journeys = array();
      $date = date('Y-m-d');
      $day = date('l');
      $stdOverrides = $this->db->select("th.thing_name, th.id as thingId, th.tracking_url, j.id as journeyId, j.journey_name, CONCAT(ifnull(d.first_name,''), ' ', ifnull(d.last_name, '')) as driverName, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_type, d.phone_number, d.attender_number, d.attender_name, ts.stop_name, ts.id as stopId, j.status, 'no' as journey_change, d.first_name as driverName, 0 as attendance")
          ->from('tx_things th')
          ->join('tx_drivers d', 'd.id=th.driver_id')
          ->join('tx_journeys j', 'j.thing_id=th.id')
          ->join('tx_student_journeys_override sro', 'sro.journey_id=j.id')
          ->join('tx_stops ts', 'ts.id=sro.stop_id')
          ->where('sro.student_id', $studentId)
          ->where("(sro.type='all' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date') OR (sro.type='single' AND sro.day='$day' AND DATE_FORMAT(sro.from_date, '%Y-%m-%d')<='$date' AND DATE_FORMAT(sro.to_date, '%Y-%m-%d')>='$date')")->get()->result();
      $jIds = array();
      foreach ($stdOverrides as $k => $std) {
        $jIds[] = $std->journeyId;
        $journeys[] = $std;
      }

      $this->db->select("th.thing_name, th.id as thingId, th.tracking_url, j.id as journeyId, j.journey_name, CONCAT(ifnull(d.first_name,''), ' ', ifnull(d.last_name, '')) as driverName, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_type, d.phone_number, ts.stop_name, ts.id as stopId, j.status, 'no' as journey_change, d.attender_number, d.attender_name, 0 as attendance")
        ->from('tx_things th')
        ->join('tx_drivers d', 'd.id=th.driver_id')
        ->join('tx_journeys j', 'j.thing_id=th.id')
        ->join('tx_student_journeys tsj', 'tsj.journey_id=j.id')
        ->join('tx_stops ts', 'ts.id=tsj.stop_id')
        ->where('tsj.entity_source_id', $studentId)
        ->where('tsj.entity_type', 'Student')
        ->where("tsj.day", $day);
      
      if (!empty($jIds))
        $this->db->where_not_in('j.id', $jIds);

      $stdRoute = $this->db->get()->result();

      foreach ($stdRoute as $k => $std) {
        $journeys[] = $std;
      }
      return $journeys;
    }

    public function getRFID($studentId) {
      return $this->db->select("REPLACE(LTRIM(REPLACE(rfid_number,'0',' ')),' ','0') as rfid_number")->where('sa.id', $studentId)->get('student_admission sa')->row()->rfid_number;
    }

    public function rfidJourneys($rfid ,$date) {
      $attendance = array();
      $journeys = array('PICKING' => array(), 'DROPPING' => array());
      $result = $this->db->select("ta.id as att_id, t.tracking_url, ta.thing_id as thingId, ta.journey_id as journeyId, ta.journey_type, TIME_FORMAT(ta.updated_at, '%h:%i %p') as tap_at,DATE_FORMAT(ta.updated_at, '%W') as tap_on, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_name, 0 as stopId, 1 as status, 'Running' as tracking, d.phone_number, d.first_name as driverName, 1 as attendance")
        ->from('tx_attendance ta')
        ->join('tx_things t', 't.id=ta.thing_id')
        ->join('tx_drivers d', 't.driver_id=d.id')
        ->join('tx_journeys j', 'j.id=ta.journey_id')
        ->where("ta.rfid", $rfid)
        ->where("DATE_FORMAT(ta.updated_at, '%Y-%m-%d') = '$date'")
        // ->order_by('ta.id', 'desc')
        ->get()->result();
      foreach ($result as $key => $value) {
        $value->tap_at = date('h:i a',strtotime($this->timezone_setter($value->tap_at)));
        if(!array_key_exists($value->journeyId, $attendance)) {
          $attendance[$value->journeyId] = $value;
          $attendance[$value->journeyId]->taps = array();
        }
        if(!in_array($value->tap_at, $attendance[$value->journeyId]->taps))
          $attendance[$value->journeyId]->taps[] = $value->tap_at;
      }

      foreach ($attendance as $key => $value) {
        $journeys[$value->journey_type][] = $value;
      }
      return $journeys;
    }

    public function getstudentNames(){
        $this->db->select("ss.id as stdYearId, sd.first_name AS stdName, cs.section_name, c.class_name ");
        $this->db->from('student_admission sd');
        $this->db->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db->where('ss.acad_year_id',$this->yearId);
        $this->db->where('sd.admission_status',2);
        $this->db->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db->join("class c", "ss.class_id=c.id",'left');
        return $this->db->get()->result();

    }

    public function checkStudentHoliday($date) {
        $sql = "SELECT `id` FROM `school_calender` WHERE (`event_type`=2 OR `event_type`=3) AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date')) AND (`applicable_to`=2 OR `applicable_to`=3)";
        $result = $this->db->query($sql)->result();
        if(empty($result))
          return 0;
        return 1;
    }

    public function checkStaffHoliday($date) {
        $sql = "SELECT `id` FROM `school_calender` WHERE `event_type`=2 OR `event_type`=3 AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date')) AND (`applicable_to`=1 OR `applicable_to`=3)";
        $result = $this->db->query($sql)->result();
        if(empty($result))
          return 0;
        return 1;
    }

    public function getStudentsOfJourneys($journey_id) {
      $sql = "select sj.id as std_journey_id, sj.stop_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name, '')) as student_name, sa.id as student_id, st.stop_name, cs.class_name, cs.section_name, sj.day, sj.journey_id, sj.entity_source_id
              from tx_student_journeys sj 
              left join tx_stops st on st.id=sj.stop_id 
              join student_admission sa on sa.id=sj.entity_source_id 
              join student_year sy on sa.id=sy.student_admission_id 
              join class_section cs on cs.id=sy.class_section_id 
              where sj.journey_id=$journey_id 
              and sy.acad_year_id=$this->yearId 
              order by cs.class_name,cs.section_name,sa.first_name";
      $result = $this->db->query($sql)->result();
      $students = array();
      foreach ($result as $key => $val) {
        $student_id = $val->student_id;
        if(!array_key_exists($student_id, $students)) {
          $students[$student_id] = array();
          $students[$student_id]['id'] = $student_id;
          $students[$student_id]['journey_id'] = $val->journey_id;
          $students[$student_id]['entity_source_id'] = $val->entity_source_id;
          $students[$student_id]['name'] = $val->student_name;
          $students[$student_id]['class_name'] = $val->class_name;
          $students[$student_id]['section_name'] = $val->section_name;
          $students[$student_id]['stops'] = array();
        }
        if(!array_key_exists($val->stop_id, $students[$student_id]['stops'])) {
          $students[$student_id]['stops'][$val->stop_id] = array();
          $students[$student_id]['stops'][$val->stop_id]['stop_name'] = $val->stop_name;
          $students[$student_id]['stops'][$val->stop_id]['days'] = array();
        }
        $students[$student_id]['stops'][$val->stop_id]['days'][] = $val->day;
      }
      usort($students, function($a, $b) { 
        $one = $a['class_name'].''.$a['section_name'];
        $two = $b['class_name'].''.$b['section_name'];
        return strcmp($one, $two); 
      });
      return $students;
      // echo "<pre>"; print_r($students); die();
    }

    public function allocateStudentJourneys() {
      $days = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"];
      $students = $_POST['students'];
      $journey_id = $_POST['journey_id'];
      $journey_type = $_POST['journey_type'];

      $data = array();
      foreach ($students as $std_id) {
        $result = $this->db->select('id')->where('entity_source_id', $std_id)->where('entity_type', 'Student')->get('tx_student_journeys')->result();
        if(empty($result)) {
          foreach ($days as $day) {
            $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $std_id,
              'journey_id' => $journey_id,
              'stop_id' => 0,
              'day' => $day,
              'journey_type' => $journey_type
            );
          }
        }
      }
      if(!empty($data)) {
        return $this->db->insert_batch('tx_student_journeys', $data);
      }
      return 1;
    }

    public function saveStudentStops() {
      $input = $_POST;
      $journey_id = $_POST['journey_id'];
      $student_id = $_POST['student_id'];
      $journey_type = $_POST['journey_type'];
      $stop_days = $_POST['stop_days'];
      $journey_days = $_POST['new_journeys'];
      $insert_data = array();
      $update_data = array();
      
      $this->db->trans_start();

      foreach ($stop_days as $day => $stop_id) {
        $row = $this->db->select('id')->where('entity_type', 'Student')->where('entity_source_id', $student_id)->where('journey_id', $journey_id)->where('day', $day)->where('journey_type', $journey_type)->get('tx_student_journeys')->row();
        if(empty($row)) {
          if($stop_id != 0) {
            $insert_data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $student_id,
              'journey_id' => $journey_days[$day],
              'stop_id' => $stop_id,
              'day' => $day,
              'journey_type' => $journey_type
            );
          }
        } else {
          if($stop_id == 0) {
            $this->db->where('id', $id)->delete('tx_student_journeys');
          } else {
            $update_data[] = array(
              'id' => $row->id,
              'journey_id' => $journey_days[$day],
              'stop_id' => $stop_id
            );
          }
        }
      }

      if(!empty($insert_data)) {
        $this->db->insert_batch('tx_student_journeys', $insert_data);
      }
      if(!empty($update_data)) {
        $this->db->update_batch('tx_student_journeys', $update_data, 'id');
      }
      $this->db->trans_complete();

      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      } else {
          $this->db->trans_commit();
          return 1;
      }
    }

    public function getJourneyTimes($journeyId) {
      $journey = $this->db->select('id, journey_name, journey_type')->where('id', $journeyId)->get('tx_journeys')->row();
      $actual = $this->db->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name, '')) as student_name, CONCAT(ifnull(cs.class_name,''),'', ifnull(cs.section_name, '')) as class_section, REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0') as rfid_number")
      ->from('tx_student_journeys sj')
      ->join('student_admission sa', 'sa.id=sj.entity_source_id')
      ->join('student_year sy', 'sy.student_admission_id=sa.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->where('sj.entity_type', 'Student')
      ->where('sj.journey_id', $journeyId)
      ->where('sa.rfid_number is not null')
      ->group_by('sj.entity_source_id')
      ->get()->result();

      if(empty($actual)) {
        return array('status' => 0, 'journey' => $journey);
      }
      
      $actual_data = [];
      foreach ($actual as $key => $val) {
        $actual_data[$val->rfid_number] = $val;
      }
      unset($actual);
      $rfids = array_keys($actual_data);

      // $today = date('Y-m-d', strtotime('2019-08-23'));
      $today = date('Y-m-d');
      $last_day = date('Y-m-d', strtotime('-7 day', strtotime($today)));

      $this->db->select("rfid, updated_at, DATE_FORMAT(updated_at, '%Y-%m-%d') as log_date")
      ->from('tx_attendance')
      ->where_in('rfid', $rfids)
      ->where('journey_id', $journeyId)
      ->where("DATE_FORMAT(updated_at, '%Y-%m-%d')<='$today' AND DATE_FORMAT(updated_at, '%Y-%m-%d')>='$last_day'");
      if($journey->journey_type == 'PICKING')
        $this->db->group_by("DATE_FORMAT(updated_at, '%Y-%m-%d'), rfid");
      $attendance = $this->db->get()->result();

      if(empty($attendance)) {
        return array('status' => 0, 'journey' => $journey);
      }

      $att_data = $this->_constructAttendanceData($attendance, $journey->journey_type);
      unset($attendance);

      $geofence = $this->db->select("event_time, DATE_FORMAT(event_time, '%Y-%m-%d') as log_date")
      ->from('tx_logs logs')
      ->where('event_type', 'Geofence')
      ->where('journey_id', $journeyId)
      ->where("DATE_FORMAT(event_time, '%Y-%m-%d')<='$today' AND DATE_FORMAT(event_time, '%Y-%m-%d')>='$last_day'")
      ->get()->result();

      $geo_data = [];
      foreach ($geofence as $key => $geo) {
        $geo_data[$geo->log_date] = date('Y-m-d h:i a', strtotime($this->timezone_setter($geo->event_time)));
      }
      unset($geofence);

      $times = $this->_getTimings($att_data, $geo_data, $journey->journey_type);
      unset($att_data);
      unset($geo_data);

      foreach ($actual_data as $rfid => $actual) {
        $actual_data[$rfid]->trips = 0;
        $actual_data[$rfid]->time = 0;
        $actual_data[$rfid]->avg_time = 0;
        if(array_key_exists($rfid, $times)) {
          $actual_data[$rfid]->trips = $times[$rfid]['trips'];
          $actual_data[$rfid]->time = round($times[$rfid]['time']);
          $actual_data[$rfid]->avg_time = ($times[$rfid]['trips'])?round($times[$rfid]['time']/$times[$rfid]['trips']):0;
        }
      }

      return array('status' => 1, 'students' => $actual_data, 'journey' => $journey);
    }

    private function _constructAttendanceData($attendance, $journey_type) {
      $att_data = [];
      if($journey_type == 'PICKING') {
        foreach ($attendance as $key => $att) {
          if(!array_key_exists($att->rfid, $att_data)) {
            $att_data[$att->rfid] = array();
          }
          $att_data[$att->rfid][$att->log_date] = date('Y-m-d h:i a', strtotime($this->timezone_setter($att->updated_at)));
          // $att_data[$att->rfid][$att->log_date] = $att->updated_at;
        }
      } else {
        foreach ($attendance as $key => $att) {
          if(!array_key_exists($att->rfid, $att_data)) {
            $att_data[$att->rfid] = array();
          }
          $att_data[$att->rfid][$att->log_date][] = date('Y-m-d h:i a', strtotime($this->timezone_setter($att->updated_at)));
          // $att_data[$att->rfid][$att->log_date][] = $att->updated_at;
        }
        foreach ($att_data as $rfid => $att) {
          foreach ($att as $date => $val) {
            if(count($val) > 1) {
              $att_data[$rfid][$date] = $val[count($val) - 1];
            } else {
              unset($att_data[$rfid][$date]);
            }
          }
        }
      }
      return $att_data;
    }

    private function _getTimings($att_data, $geo_data, $journey_type) {
      $times = [];
      if($journey_type == 'PICKING') {
        foreach ($att_data as $rfid => $att) {
          $times[$rfid] = array();
          $times[$rfid]['trips'] = 0;
          $times[$rfid]['time'] = 0;
          foreach ($geo_data as $geo_date => $geo_time) {
            if(array_key_exists($geo_date, $att)) {
              $times[$rfid]['trips']++;
              $times[$rfid]['time'] += $this->_getTimeDiffInMinutes($att[$geo_date], $geo_time);
            }
          }
        }
      } else {
        foreach ($att_data as $rfid => $att) {
          $times[$rfid] = array();
          $times[$rfid]['trips'] = 0;
          $times[$rfid]['time'] = 0;
          foreach ($geo_data as $geo_date => $geo_time) {
            if(array_key_exists($geo_date, $att)) {
              $times[$rfid]['trips']++;
              $times[$rfid]['time'] += $this->_getTimeDiffInMinutes($geo_time, $att[$geo_date]);
            }
          }
        }
      }
      return $times;
    }

    private function _getTimeDiffInMinutes($time1, $time2) {
      $from_time = strtotime($time1);
      $to_time = strtotime($time2);
      return round(abs($to_time - $from_time) / 60,2);
    }
   
  public function clone_journey($input) {
      // $input = array_map('trim', $input);
      $startTime = date('H:i:s', strtotime($input['tentative_start_time']));
      $endTime = date('H:i:s', strtotime($input['tentative_end_time']));
      $days = json_encode($input['days']);
      $data = array(
          'journey_name' => $input['journey_name'],
          'thing_id' => $input['thing_id1'],
          'journey_type' => $input['journey_type'],
          'tentative_start_time' => $startTime,
          'tentative_end_time' => $endTime,
          'days' => $days
        );
      
      $this->db->trans_start();
      $this->db->insert('tx_journeys', $data);
      $journey_id = $this->db->insert_id();

      $journey_stops = array();
      if(isset($input['stop_ids'])) {
        foreach ($input["stop_ids"] as $stop_id) {
          $journey_stops[] = array(
            'journey_id' => $journey_id,
            'stop_id' => $stop_id
          );
        }
      }
      
      if(!empty($journey_stops)) {
        $this->db->insert_batch('tx_journey_stops', $journey_stops);
      }
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return 0;
      }
      // $this->updateStopOrder($stop_ids);
      $this->db->trans_commit();
      return 1;
      // echo "<pre>"; print_r($input); die();
    }

    public function deletestdmassJourney($journey_id, $entity_source_id) {
      return $this->db->where('entity_source_id', $entity_source_id) ->where('journey_id', $journey_id)
      ->delete('tx_student_journeys');
    }

    public function get_class_section_student_data($classSectionId){
        $this->db->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, CONCAT(cs.class_name,' ', cs.section_name) as csName");
        $this->db->from('student_admission sa');
        $this->db->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId");
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->where('sa.admission_status', 2);
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where('sy.acad_year_id', $this->yearId);
        $this->db->order_by('cs.id, sa.first_name');
        if ($classSectionId) {
            $this->db->where('sy.class_section_id', $classSectionId);
        }
        $result = $this->db->get()->result();

        $allocated_data = $this->db_readonly->select('distinct(entity_source_id) as student_id')
          ->from('tx_student_journeys')
          ->where('entity_type', 'Student')
          ->get()->result();

        foreach ($result as &$res) {
          $res->is_allocated = 0;
          foreach ($allocated_data as $adata) {
            if ($res->id == $adata->student_id) {
              $res->is_allocated = 1;
              break;
            }
          }
        }

        return $result;
     }

     public function getJourneyStopsStaff($journeyId, $stopId, $date=null) {

        if($date == null) {
        $date = date('Y-m-d');
      }
      $date = date('Y-m-d', strtotime($date));
      $day = date('l', strtotime($date));

      $this->db->select("sm.id as staffId, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as stdName, ifnull(sm.contact_number, '-') as number, ifnull(sm.employee_code, '-') as employee_code, s.stop_name, s.id as stop_id, '-' as status, '-' as csName, '' as mother_name, '' as father_name, '' as mother_mobile_no, '' as father_mobile_no, 'Staff' as type, '-' as admission_no, '-' as enrollment_no")
      ->from('staff_master sm')
      ->join('tx_staff_journeys e', 'e.staff_id=sm.id')
      ->join('tx_stops s', 's.id=e.stop_id');
      $this->db->where('e.journey_id', $journeyId);
      if(! empty($stopId)){
        $this->db->where_in('s.id', $stopId);
      }
      $this->db->where('e.day', $day);
      if(!empty($stdIds))
        $this->db->where_not_in('sm.id', $stdIds);
      $this->db->order_by('sm.first_name');
      $stdData = $this->db->get()->result();

      return $stdData;
     }

    public function getexport_Stops() {
      return $this->db->select('id, stop_name')
            ->from('tx_stops')
            ->get()->result();
    }

    public function get_generate_export_student($days, $status, $_routes){

      $this->db->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name, '')) as student_name, CONCAT(ifnull(cs.class_name,''),'', ifnull(cs.section_name, '')) as class_section, tj.journey_name, ts.stop_name, tt.thing_name, tt.thing_reg_number, sa.admission_no, sa.enrollment_number, sj.day, sj.journey_type")
      ->from('tx_student_journeys sj')
      ->join('tx_stops ts', 'sj.stop_id=ts.id')
      ->join('tx_journeys tj', 'tj.id=sj.journey_id')
      ->join('tx_things tt', 'tj.thing_id=tt.id')
      ->join('student_admission sa', 'sa.id=sj.entity_source_id')
      ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->where('sj.entity_type', 'Student');
      if($days != 'ALL_DAYS'){
        $this->db->where('sj.day', $days);
      }
      if($status != 'BOTH'){
        $this->db->where('sj.journey_type', $status);
      }
      $this->db->where_in('tt.id', $_routes)
      ->where_in('sa.admission_status', [1,2])
      ->where('sy.acad_year_id', $this->yearId);
      $result = $this->db->get()->result();
      return $result;
    }

    public function get_generate_export_staff($days, $status, $_routes){
        return $this->db->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name, '')) as staff_name, tj.journey_name, ts.stop_name, tt.thing_name, tt.thing_reg_number ")
      ->from('tx_staff_journeys sj')
      ->join('tx_stops ts', 'sj.stop_id=ts.id')
      ->join('tx_journeys tj', 'tj.id=sj.journey_id')
      // ->join('tx_logs tl', 'tl.journey_id=sj.journey_id')
      ->join('tx_things tt', 'tj.thing_id=tt.id')
      ->join('staff_master sm', 'sm.id=sj.staff_id')
      ->where('sj.day', $days)
      ->where('sj.journey_type', $status)
      ->where_in('tt.id', $_routes)
      ->get()->result();

    }

    public function getDailyJourneys_email($journey_type, $date) {
      //echo "<pre>"; print_r($journey_type); die();

      $result = $this->db->select("j.id as journey_id, j.thing_id, j.journey_name, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as startTime, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as endTime, l.event_type, t.tracking_url, t.thing_name, j.days, (ADDTIME(CURTIME(),'05:30:00') BETWEEN j.tentative_start_time AND j.tentative_end_time AND DATE_FORMAT(CURDATE(), '%d-%m-%Y')='$date') as show_tracking")
        ->from('tx_journeys j')
        ->join('tx_things t', 't.id=j.thing_id')
        ->join('tx_logs l', "j.id=l.journey_id AND DATE_FORMAT(l.event_time, '%d-%m-%Y')='$date'", 'left')
        ->where('j.journey_type', $journey_type)
        ->order_by('j.id')
        ->get()->result();

      //  echo '<pre>';print_r($this->db->last_query());die();

      $journeys = array();
      $current_time = strtotime(date('H:i:s'));
      $today = date('d-m-Y');
      $day = strtoupper(date('l', strtotime($date)));

      foreach ($result as $key => $res) {
        $days = json_decode($res->days);
        if(!in_array($day, $days)) {
          continue;
        }
        if(!array_key_exists($res->journey_id, $journeys)) {
          $journeys[$res->journey_id] = array();
          $journeys[$res->journey_id]['geo_fence_reminder'] = 0;
          $journeys[$res->journey_id]['eta_reminder'] = 0;
          $journeys[$res->journey_id]['journey_id'] = $res->journey_id;
          $journeys[$res->journey_id]['journey_name'] = $res->journey_name;
          $journeys[$res->journey_id]['thing_id'] = $res->thing_id;
          $journeys[$res->journey_id]['startTime'] = $res->startTime;
          $journeys[$res->journey_id]['endTime'] = $res->endTime;
          $journeys[$res->journey_id]['status'] = 'Not Started';
          $journeys[$res->journey_id]['event_type'] = $res->event_type;
          $journeys[$res->journey_id]['tracking_url'] = $res->tracking_url;
          $journeys[$res->journey_id]['thing_name'] = $res->thing_name;
          $journeys[$res->journey_id]['show_tracking'] = $res->show_tracking;
        }
        if ($res->event_type == 'Eta') {
          $journeys[$res->journey_id]['eta_reminder'] ++;
        }
        if ($res->event_type == 'Geofence') {
          $journeys[$res->journey_id]['geo_fence_reminder'] ++;
        }
        $startTime = strtotime($res->startTime);
        $endTime = strtotime($res->endTime);
        if($journey_type == 'PICKING') {
          if($res->event_type == 'Attendance' || $res->event_type == 'Eta') {
            $journeys[$res->journey_id]['status'] = 'Running';
          } else if($res->event_type == 'Geofence'){
            $journeys[$res->journey_id]['status'] = 'Completed';
          }
        } else {
          if($res->event_type == 'Attendance' || $res->event_type == 'Eta') {
            $journeys[$res->journey_id]['status'] = 'Running';
          } else if($res->event_type == 'Geofence'){
            $journeys[$res->journey_id]['status'] = 'Running';
          }
        }
        if($res->event_type != '' || $res->event_type != NULL) {
          if($date == $today) {
            if($endTime<$current_time) {
              $journeys[$res->journey_id]['status'] = 'Completed';
            }
          } else {
            $journeys[$res->journey_id]['status'] = 'Completed';
          }
        }
      }

      
      $total_students = $this->db_readonly->query("select journey_id, count(id) as no_students from tx_student_journeys where day='$day' and journey_type= '$journey_type'  and entity_type='Student' and entity_source_id in (select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id and acad_year_id=$this->yearId where sa.admission_status=2 and sy.promotion_status!= 4 and sy.promotion_status!=5) group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $jour['total_students'] = 0;
        foreach ($total_students as $tot) {
          if ($jour['journey_id'] == $tot->journey_id) {
            $jour['total_students'] = $tot->no_students;
            break;
          }
        }
      }

      //Add the number of students present in each journey
      $present_students = $this->db_readonly->query("select txa.journey_id, count(distinct(sa.rfid_number)) as no_students from tx_attendance txa join student_admission sa on REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0')=txa.rfid where sa.rfid_number is not null and date_format(updated_at, '%d-%m-%Y')='$date' and journey_type= '$journey_type' group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $found = 0;
        foreach ($present_students as $pres) {
          if ($jour['journey_id'] == $pres->journey_id) {
            $jour['present_all_students'] = $pres->no_students;
            $found = 1;
            break;
          }
        }
        if ($found == 0) {
          $jour['present_all_students'] = 0;
        }
      }

      $present_registered_students = $this->db_readonly->query("select txa.journey_id, count(distinct(sa.rfid_number)) as no_students from tx_attendance txa join student_admission sa on REPLACE(LTRIM(REPLACE(sa.rfid_number,'0',' ')),' ','0')=txa.rfid join tx_student_journeys tsj on sa.id=tsj.entity_source_id and tsj.entity_type='Student' and tsj.journey_id=txa.journey_id where sa.rfid_number is not null and date_format(updated_at, '%d-%m-%Y')='$date' and tsj.journey_type= '$journey_type' group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $found = 0;
        foreach ($present_registered_students as $pres_reg) {
          if ($jour['journey_id'] == $pres_reg->journey_id) {
            $jour['present_registered_students'] = $pres_reg->no_students;
            $found = 1;
            break;
          }
        }
        if ($found == 0) {
          $jour['present_registered_students'] = 0;
        }
      }

      //Add the number of stops in each journey
      $total_stops = $this->db_readonly->query("select journey_id, count(id) as no_stops from tx_journey_stops group by journey_id")->result();

      foreach ($journeys as &$jour) {
        $jour['total_stops'] = 0;
        foreach ($total_stops as $stop) {
          if ($jour['journey_id'] == $stop->journey_id) {
            $jour['total_stops'] = $stop->no_stops;
            break;
          }
        }
      }

      return $journeys;
    }

    public function addStopTime($input){
      $stop_time = $input['stop_time'];
      $stop_id = $input['stop_id'];
      $journey_id = $input['journey_id'];

      $stop_time = date("H:i:s", strtotime($stop_time));
      // echo "<pre>";print_r($stop_time);die();
      $this->db->where('journey_id', $journey_id);
      $this->db->where('stop_id', $stop_id);
      $this->db->update('tx_journey_stops', array('stop_time' => $stop_time));
      // echo "<pre>";print_r($this->db->affected_rows());die();
      if ($this->db->affected_rows() >= 0) {
          $this->db->select('stop_time');
          $this->db->from('tx_journey_stops');
          $this->db->where('journey_id', $journey_id);
          $this->db->where('stop_id', $stop_id);
          $row = $this->db->get()->row();
          // echo "<pre>";print_r($query);die();
          if(isset($row->stop_time)){
            $formattedTime = strtotime($row->stop_time) > '12:00:00' ? date('h:i A', strtotime($row->stop_time)) : date('h:i a', strtotime($row->stop_time)) ;
            // echo "<pre>";print_r($formattedTime);die();
            return $formattedTime;
          }else{
            return null;
          }
      }else{
        return null;
      }
    }

    public function get_stops_sorted_by_timings($journey_id){
      // echo "<pre>";print_r($journey_id);die();
      $result = $this->db_readonly->select('ts.id as id, ts.stop_name as name, js.stop_time as stop_time')
                                  ->from('tx_journey_stops js')
                                  ->join('tx_stops ts', 'ts.id = js.stop_id', 'left')
                                  ->where('js.journey_id', $journey_id)
                                  ->get()->result();

      function sortByStopTime($a, $b) {
          if (empty($a->stop_time) && empty($b->stop_time)) {
              return 0;
          } elseif (empty($a->stop_time)) {
              return 1;
          } elseif (empty($b->stop_time)) {
              return -1;
          } else {
              return strcmp($a->stop_time, $b->stop_time);
          }
      }

      usort($result, 'sortByStopTime');
      foreach($result as $res){
        if(isset($res->stop_time)){
          $res->stop_time = strtotime($res->stop_time) > '12:00:00' ? date('h:i A', strtotime($res->stop_time)) : date('h:i a', strtotime($row->stop_time)) ;
        }
      }
      return $result;
    }

    public function removeStopTime($input){
      $stop_id = $input['stop_id'];
      $journey_id = $input['journey_id'];
      $this->db->where('journey_id', $journey_id);
      $this->db->where('stop_id', $stop_id);
      $this->db->update('tx_journey_stops', array('stop_time' => null));
      if ($this->db->affected_rows() >= 0) {
        return 1;
      }else{
        return 0;
      }
    }

    public function delete_all_journeys_by_std_id($stdJourneyId) {
      return $this->db->where('entity_source_id', $stdJourneyId)->delete('tx_student_journeys');
    }

    public function mass_upload_stops() {
      $input = $this->input->post();

      $stop_name = isset($input['stop_name']) ? trim($input['stop_name']) : null;
      $landmark = isset($input['landmark']) ? trim($input['landmark']) : '';

      $this->db->where('stop_name', $stop_name);
      $query = $this->db->get('tx_stops');

      if ($query->num_rows() > 0) {
          return json_encode(array('status' => false));
      }

      $stopData = array(
        'stop_name' => $stop_name,
        'distance_to_stop' => 0,
        'tentative_reach_time' => date('H:i:s'),
        'landmark' => $landmark,
        'created_by' => $this->authorization->getAvatarId(),
        'last_modified_by' => $this->authorization->getAvatarId()
      );
      // echo "<pre>";print_r($stopData);die();
      $result = $this->db->insert('tx_stops', $stopData);

      if ($result) {
        return json_encode(array('status' => true));
      } else {
        return json_encode(array('status' => false));
      }
    }

    public function mass_add_journey_stops() {
      $input = $this->input->post();

      $select_option = isset($input['select_option']) ? trim($input['select_option']) : null;
      $journey_id = isset($input['journey_id']) ? $input['journey_id'] : null;
      $stop_name = isset($input['stop_name']) ? trim($input['stop_name']) : null;

      $this->db->select('id');
      $this->db->from('tx_stops');
      $this->db->where('stop_name', $stop_name);
      $stop_query = $this->db->get();

      if ($stop_query->num_rows() == 0) {
        $data = array(
          'stop_name' => $stop_name,
          'distance_to_stop' => 0,
          'tentative_reach_time' => date('H:i:s'),
          'landmark' => '',
          'created_by' => $this->authorization->getAvatarId(),
          'last_modified_by' => $this->authorization->getAvatarId()
        );
        $this->db->insert('tx_stops', $data);
        $stop_id = $this->db->insert_id();
      }else{
        $stop_id = $stop_query->row()->id;
      }

      $insert_journey_stop = function($journey_id, $stop_id) {

        $this->db->select('id');
        $this->db->from('tx_journey_stops');
        $this->db->where('journey_id', $journey_id);
        $this->db->where('stop_id', $stop_id);
        $existing_query = $this->db->get();

        if ($existing_query->num_rows() > 0) {
            return json_encode(array('status' => false));
        }

        $data = array(
            'journey_id' => $journey_id,
            'stop_id' => $stop_id
        );
        return $this->db->insert('tx_journey_stops', $data);
      };

      if (!$insert_journey_stop($journey_id, $stop_id)) {
        return json_encode(array('status' => false));
      }

      if ($select_option == 'both') {
        $this->db->select('journey_name');
        $this->db->from('tx_journeys');
        $this->db->where('id', $journey_id);
        $journey_query = $this->db->get();
        
        if ($journey_query->num_rows() == 0) {
          return json_encode(array('status' => false));
        }

        $journey_name = $journey_query->row()->journey_name;
        $journey_name = strtolower(trim($journey_name));
        $drop_journey_name = str_replace('pickup', 'drop', $journey_name);
        $drop_journey_name = strtolower(trim($drop_journey_name));
        $this->db->select('id');
        $this->db->from('tx_journeys');
        $this->db->where('lower(journey_name)', $drop_journey_name);
        $drop_query = $this->db->get();
        if ($drop_query->num_rows() == 0) {
          return json_encode(array('status' => false));
        }
        $drop_journey_id = $drop_query->row()->id;

        if (!$insert_journey_stop($drop_journey_id, $stop_id)) {
          return json_encode(array('status' => false));
        }
      }

      return json_encode(array('status' => true));
    }

    public function getPassengersInBothJourneysStudents($thingId, $days){
        $this->db->select('id')
                ->from('tx_journeys')
                ->where('thing_id', $thingId);
        $journeyIdsResult = $this->db->get()->result();

        $studentsArray = [];

        foreach ($journeyIdsResult as $row) {
            $studentsArray = array_merge($studentsArray, $this->getPassengersInJourneysStudents($row->id, $days));
        }

        return $studentsArray;
    }

    public function getPassengersInBothJourneysStaffs($thingId, $days){
        $this->db->select('id')
                ->from('tx_journeys')
                ->where('thing_id', $thingId);
        $journeyIdsResult = $this->db->get()->result();

        $staffArray = [];

        foreach ($journeyIdsResult as $row) {
            $staffArray = array_merge($staffArray, $this->getPassengersInJourneysStaffs($row->id, $days));
        }

        return $staffArray;
    }

    public function getPassengersInJourneysStudents($journeyId, $days){
      $stdArr = [];
      $stdIds = [];

      $this->db->select("sa.id as stdId, 
                        CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, 
                        sa.admission_no, 
                        s.stop_name, 
                        s.id as stop_id, 
                        'Absent' as status, 
                        CONCAT(cs.class_name, ' ', cs.section_name) as csName, 
                        CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name, 
                        CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, 
                        ifnull(m.mobile_no,'-') as mother_mobile_no, 
                        ifnull(f.mobile_no,'-') as father_mobile_no, 
                        'Student' as type, 
                        ifnull(sa.enrollment_number, '-') as enrollment_no")
          ->from('student_admission sa')
          ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
          ->join('student_relation srf', 'sa.id=srf.std_id')
          ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
          ->join('student_relation srm', 'sa.id=srm.std_id')
          ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
          ->join('class_section cs', 'sy.class_section_id=cs.id')
          ->join('tx_student_journeys_override sro', 'sro.student_id=sa.id')
          ->join('tx_stops s', 's.id=sro.stop_id')
          ->where('sro.journey_id', $journeyId)
          ->where_in('sa.admission_status', [1,2])
          ->where('sy.acad_year_id', $this->yearId)
          ->order_by('sa.first_name');

      $stdOverrides = $this->db->get()->result();

      foreach ($stdOverrides as $std) {
          $stdIds[] = $std->stdId;
      }

      foreach ($days as $day) {
          $this->db->select("sa.id as stdId, 
                            CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, 
                            sa.admission_no, 
                            s.stop_name, 
                            e.day as day,
                            e.journey_type as journey_type,
                            s.id as stop_id, 
                            'Absent' as status, 
                            CONCAT(cs.class_name, '', cs.section_name) as csName, 
                            CONCAT(ifnull(m.first_name,''),' ', ifnull(m.last_name,'')) as mother_name, 
                            CONCAT(ifnull(f.first_name,''),' ', ifnull(f.last_name,'')) as father_name, 
                            ifnull(m.mobile_no,'') as mother_mobile_no, 
                            ifnull(f.mobile_no,'') as father_mobile_no, 
                            'Student' as type, 
                            ifnull(sa.enrollment_number, '-') as enrollment_no")
              ->from('student_admission sa')
              ->join('student_year sy', 'sy.student_admission_id=sa.id and sy.promotion_status not in (4,5)')
              ->join('student_relation srf', 'sa.id=srf.std_id')
              ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
              ->join('student_relation srm', 'sa.id=srm.std_id')
              ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
              ->join('class_section cs', 'sy.class_section_id=cs.id')
              ->join('tx_student_journeys e', 'e.entity_source_id=sa.id')
              ->join('tx_stops s', 's.id=e.stop_id')
              ->where('e.entity_type', 'Student')
              ->where('e.journey_id', $journeyId)
              ->where_in('sa.admission_status', [1,2])
              ->where('sy.acad_year_id', $this->yearId)
              ->where('e.day', $day);

          if (!empty($stdIds)) {
              $this->db->where_not_in('sa.id', $stdIds);
          }

          $this->db->order_by('sa.first_name');
          $stdData = $this->db->get()->result();

          $stdArr = array_merge($stdArr, $stdData);
      }

      // Add overrides to the final array
      $stdArr = array_merge($stdOverrides, $stdArr);
      // Additional processing for final array
      foreach ($stdArr as $value) {
          $value->employee_code = '-';
          $value->staffId = '-';
          $value->number = '-';
          // if(!empty($value->stop_id) || isset($value->stop_id)){
            $stop_time = $this->db->select("ifnull(stop_time, '-') as stop_time")
                                    ->from('tx_journey_stops')
                                    ->where('journey_id', $journeyId)
                                    ->where('stop_id', $value->stop_id)
                                    ->get()->row();
          if(!empty($stop_time) && $stop_time->stop_time != '-') {
            $value->stop_time = $stop_time->stop_time;
            $value->stop_time = strtotime($value->stop_time) > '12:00:00' ? date('h:i A', strtotime($value->stop_time)) : date('h:i a', strtotime($value->stop_time)) ;
          }else{
            $value->stop_time = '-';
          }
          // }
      }
      return $stdArr;
    }

    public function getPassengersInJourneysStaffs($journeyId, $days){
      $stfData = [];
      foreach ($days as $day) {
        $this->db->select("sm.id as staffId, 
                          CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as stdName, 
                          ifnull(sm.contact_number, '-') as number, 
                          ifnull(sm.employee_code, '-') as employee_code, 
                          s.stop_name, 
                          s.id as stop_id, 
                          'Staff' as type, 
                          e.day as day,
                          e.journey_type as journey_type,
                          '-' as status, 
                          '-' as csName, 
                          '' as mother_name, 
                          '' as father_name, 
                          '' as mother_mobile_no, 
                          '' as father_mobile_no, 
                          '-' as admission_no, 
                          '-' as enrollment_no")
              ->from('staff_master sm')
              ->join('tx_staff_journeys e', 'e.staff_id = sm.id')
              ->join('tx_stops s', 's.id = e.stop_id')
              ->where('e.journey_id', $journeyId)
              ->where('e.day', $day)
              ->order_by('sm.first_name');

        $result = $this->db->get()->result();
        $stfData = array_merge($stfData, $result);
      }
      
      foreach($stfData as $stf){
        $stf->stop_time = $this->db->select("ifnull(stop_time, '-') as stop_time")
                                    ->from('tx_journey_stops')
                                    ->where('journey_id', $journeyId)
                                    ->where('stop_id', $stf->stop_id)
                                    ->get()->row()->stop_time;
          if($stf->stop_time != '-')
            $stf->stop_time = strtotime($stf->stop_time) > '12:00:00' ? date('h:i A', strtotime($stf->stop_time)) : date('h:i a', strtotime($stf->stop_time)) ;
      }
      return $stfData;
    }

    public function mass_assign_student_journey($input){
      $std_id = '';
      if(isset($input['selected_column']) && !empty($input['selected_column'])){
        if($input['selected_column'] == 'Student Id'){
          $this->db->select('id');
          $this->db->from('student_admission');
          $this->db->where('id', trim($input['std_id']));
          $this->db->where('admission_status', 2);
          $query = $this->db->get();
          if ($query->num_rows() == 1) {
            $std_id = $input['std_id'];
          } else {
            return json_encode(array('status' => 0, 'remarks' => 'No Student '.$input['std_id']));
          }
        }else{
          $this->db_readonly->select('id');
          $this->db_readonly->from('student_admission');
          $this->db_readonly->where('admission_status', '2');
          if ($input['selected_column'] == 'Admission Number') {
              $this->db_readonly->where('admission_no', trim($input['std_id']));
          } else if($input['selected_column'] == 'Enrollment Number'){
            $this->db_readonly->where('enrollment_number', trim($input['std_id']));
          }
          $std_query = $this->db_readonly->get();

          if ($std_query->num_rows() == 0) {
              return (array('status' => 0, 'remarks' => 'No Student '.$input['std_id']));
          }else{
            $result = $std_query->row();
            $std_id = $result->id;
          }
        }
      }else{
        return (array('status' => 0, 'remarks' => 'No Primary Index'));
      }

      $journey_type = strtolower(trim($input['journey_type'])) == 'pick' ? 'PICKING' : 'DROPPING';

      $this->db_readonly->select('id');
      $this->db_readonly->from('tx_student_journeys');
      $this->db_readonly->where('entity_source_id', $std_id);
      $this->db_readonly->where('journey_type', $journey_type);
      $std_present = $this->db_readonly->get();

      if ($std_present->num_rows() > 0) {
        return (array('status' => 0, 'remarks' => 'Journey Already Allocated'));
      }

      $this->db_readonly->select('id');
      $this->db_readonly->from('tx_journeys');
      $this->db_readonly->where('UPPER(journey_name)', strtoupper(trim($input['journey'])));
      $journey_query = $this->db_readonly->get();
      
      if ($journey_query->num_rows() == 0) {
          return (array('status' => 0, 'remarks' => 'No Journey Found',));
      }

      $journey_id = $journey_query->row()->id;

      $this->db->select('id');
      $this->db->from('tx_stops');
      $this->db->where('stop_name', trim($input['stop_name']));
      $stop_query = $this->db->get();
      
      if ($stop_query->num_rows() == 0) {
        $data = array(
          'stop_name' => trim($input['stop_name']),
          'distance_to_stop' => 0,
          'tentative_reach_time' => date('H:i:s'),
          'landmark' => trim($input['stop_name']),
          'created_by' => $this->authorization->getAvatarId(),
          'last_modified_by' => $this->authorization->getAvatarId()
        );
        $this->db->insert('tx_stops', $data);
        $stop_id = $this->db->insert_id();
      }else{
        $stop_id = $stop_query->row()->id;
      }

      $days_of_week = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];

      $data = array();
      foreach ($days_of_week as $day) {
          $data[] = array(
              'entity_type' => 'Student',
              'entity_source_id' => $std_id,
              'journey_id' => $journey_id,
              'stop_id' => $stop_id,
              'day' => $day,
              'journey_type' => $journey_type
          );
      }

      $result = $this->db->insert_batch('tx_student_journeys', $data);
      if($result){
        return (array('status' => 1, 'remarks' => 'Allocated'));
      }else{
        return (array('status' => 0, 'remarks' => 'Error'));
      }
    }

    public function validate_student_pickup_journeys($input){
      $student_id = $input['student_id'];
      $journey_id = $input['journey_id'];
      $stop_id = $input['stop_id'];
      $days = $input['days'];

      $this->db->select('journey_id, stop_id, day');
      $this->db->from('tx_student_journeys');
      $this->db->where('entity_source_id', $student_id);
      $query = $this->db->get();
      $existing_records = $query->result();

      $days_conflict = false;
      $conflicting_days = [];
      if (empty($existing_records)) {
          return [true, '', ''];
      } else {
          foreach ($existing_records as $val) {
              $existing_days = explode(',', $val->day);
              $common_days = array_intersect($existing_days, $days);

              if (!empty($common_days)) {
                  $days_conflict = true;
                  $conflicting_days = array_unique(array_merge($conflicting_days, $common_days));
              }
          }

          if ($days_conflict) {
              $conflicting_days_list = implode(', ', $conflicting_days);
              return [false, 'Selected days conflict with existing journey. <br>Already added days: <span style="font-weight:600;font-size:15px;color:blue">' . $conflicting_days_list . '</span>.<br> Please adjust the days.', 'error'];
          }
      }

      $existing_days = [];

      foreach ($days as $day) {
        $this->db->select('id');
        $this->db->from('tx_student_journeys');
        $this->db->where('entity_source_id', $student_id);
        $this->db->where('journey_id', $journey_id);
        $this->db->where('stop_id', $stop_id);
        $this->db->where('day', $day);
        $query = $this->db->get();
        $result = $query->row();
        
        if ($result) {
          $existing_days[] = $day;
        }
      }

      sort($existing_days);
      sort($days);

      if ($existing_days == $days) {
        $days = count($existing_days) == 6 ? 'All the days of the week' : 'the selected days' ;
        $message = "Already added to <br><span style='font-weight:600;font-size:15px;color:blue'>" . $days . "</span>.<br> Cannot be added.";
        return [false, $message, 'error'];
      }

      $missing_days = array_diff($days, $existing_days);

      if (!empty($missing_days) && count($missing_days) < count($days) && count($days) > 1) {
        $message = "You Can Add only to these days, <br><span style='font-weight:600;font-size:15px;color:blue'>" . implode(',', $missing_days) . "</span>";
        return [false, $message, 'info'];
      }

      if (count($missing_days) == count($days) || (count($days) == 1 && !empty($missing_days))) {
        return [true, '', ''];
      }

      return [true, '', ''];
    }

    public function validate_student_drop_journeys($input) {
        $student_id = $input['student_id'];
        $journey_id = $input['journey_id'];
        $stop_id = $input['stop_id'];
        $days = $input['days'];

        $this->db->select('journey_id, stop_id, day');
        $this->db->from('tx_student_journeys');
        $this->db->where('entity_source_id', $student_id);
        $this->db->where('journey_type', 'DROPPING');
        $query = $this->db->get();
        $existing_records = $query->result();
        $conflicting_days = [];

        if (!empty($existing_records)) {
            foreach ($existing_records as $record) {
                if (in_array($record->day, $days)) {
                    array_push($conflicting_days, $record->day);
                }
            }
        }
        if (!empty($conflicting_days)) {
            $conflicting_days_list = implode(', ', $conflicting_days);
            return [false, 'Selected days conflict with existing journey. <br>Already added days: <span style="font-weight:600;font-size:15px;color:blue">' . $conflicting_days_list . '</span>.<br> Please adjust the days.', 'error'];
        }

        return [true, '', ''];
    }

    public function get_all_journeys_of_the_bus($thingId, $journey_id){
      $this->db_readonly->select('id, journey_name');
      $this->db_readonly->from('tx_journeys s');
      $this->db_readonly->where('thing_id', $thingId);
      $this->db_readonly->where('id !=', $journey_id);
      $query = $this->db_readonly->get()->result();
      // echo "<pre>";print_r($query);die();
      return $query;
    }

    public function get_bus_tracking_url($thing_id){
      $tracking_url = $this->db_readonly->select('tracking_url')->from('tx_things')->where('id', $thing_id)->get()->row();
      if(!empty($tracking_url)){
        return $tracking_url->tracking_url;
      } else {
        return false;
      }
    }

    public function getclass() {
      $this->db_readonly->select('c.id,c.class_name');
      $this->db_readonly->from('class c');
      $this->db_readonly->order_by('c.display_order, c.id');
      $this->db_readonly->where('acad_year_id',$this->yearId);
      if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $this->db_readonly->where('c.is_placeholder!=',1);
      return $this->db_readonly->get()->result();
  }

  public function get_transportation_stu_ids($classId,$stop){
    $this->db_readonly->select('sa.id')
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class c', "sy.class_id=c.id")
    ->where('sa.admission_status',2)
    ->where("sy.promotion_status!=",4)
    ->order_by('sa.first_name')
    ->order_by('sy.class_id');  
    if ($classId) {
      $this->db_readonly->where_in('sy.class_id',$classId);
    }
    if ($stop) {
      $this->db_readonly->where('sy.stop',$stop);
    }

    $stdResult = $this->db_readonly->get()->result();
    $student_ids = [];
    foreach ($stdResult as $key => $val) {
      array_push($student_ids, $val->id);
    }
    return $student_ids;

  }

  public function get_fee_Stop_list(){
    return $this->db->order_by('name','asc')->get('feev2_stops')->result();
  }


  public function get_class_section_all(){
    return $this->db_readonly->select("c.id as cId, cs.id as csId, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection")
      ->from('class c')
      ->where('c.acad_year_id',$this->yearId)
      ->join('class_section cs','c.id=cs.class_id')
      ->where('cs.is_placeholder!=1')
      ->get()->result();
  }

  public function get_fee_km_list(){
		return $this->db->get('feev2_km')->result();
	}

  // public function getBusCapacity($journeyId){
  //   $this->db_readonly->select('thing_id');
  //   $this->db_readonly->from('tx_journeys');
  //   $this->db_readonly->where('id', $journeyId);
  //   $thingId = $this->db_readonly->get()->row();
  //   if (!$thingId) {
  //     return 0;
  //   }
  //   $this->db_readonly->select('ifnull(bus_capacity, 0) as bus_capacity');
  //   $this->db_readonly->from('tx_things');
  //   $this->db_readonly->where('id', $thingId->thing_id);
  //   $capacity = $this->db_readonly->get()->row();
  //   if (!$capacity) {
  //     return 0;
  //   }
  //   return $capacity->bus_capacity;
  // }

}