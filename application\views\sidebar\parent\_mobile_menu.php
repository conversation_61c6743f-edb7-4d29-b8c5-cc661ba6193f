<div id="mySidenav" class="sidenav">
    <ul id="overHidden" class="x-navigation">
        <div class="w3-sidebar w3-bar-block w3-animate-left" style="display:none;z-index:5" id="mySidebar">
        <li class="xn-logo" style="background: #fff;text-align:center;">
            <a style="font-size: 20px;color:#6893CA;" href="#"> <?= $this->settings->getSetting('company_name'); ?></a>
        </li>
        <li class="xn-profile">
            <div class="profile" style="background: #fff;">
                <div class="profile-image">
                    <img style="float: none; height: 100%;" src="<?php echo base_url() . $this->settings->getSetting('company_logo'); ?>" alt=""/>
                </div>
                <div class="profile-data">
                    <div class="profile-data-name" style="color:#000 !important;"><?php echo $this->settings->getSetting('school_name'); ?></div>
                </div>
            </div>
        </li>
       <li class="active">
            <a href="<?php echo site_url('dashboard');?>">
                <div style="width:40px;display: inline-block;"><?php $this->load->view('svg_icons/dashboard.svg') ?></div>

                <span style="line-height: 30px;font-size: 16px;font-weight: 500;color: #4a4a4a;">Dashboard</span> 
           </a>
        </li>
        <li class="active">
            <a href="<?php echo site_url('auth/change_password')?>" class="list-group-item">
                <div style="width:40px;display: inline-block;"><?php $this->load->view('svg_icons/master.svg') ?></div>
                <span style="line-height: 30px;font-size: 16px;font-weight: 500;color: #4a4a4a;">Change Password</span>

            </a>
        </li>

        <?php if($this->session->userdata('avatar_count') > 1) { ?>
            <li>
                <a href="<?php echo site_url('avatars')?>" class="list-group-item">
                    <div style="width:40px;display: inline-block;"><?php $this->load->view('svg_icons/swtichprof.svg') ?></div>
    
                    <span style="line-height: 30px;font-size: 16px;font-weight: 500;color: #4a4a4a;">Switch Profile</span>
                </a>
            </li>
       <?php } ?>

        <li><a href="<?php echo site_url("parent_controller/help_support");?>">
            <div style="width:40px;display: inline-block;"><?php $this->load->view('svg_icons/faq.svg') ?></div>

            <span>Help & Support</span>
            </a>
        </li>
            <li>
                <a href="#" class="list-group-item mb-control" data-box="#mb-signout">
                    <div style="width:40px;display: inline-block;"><?php $this->load->view('svg_icons/logout.svg') ?></div>

                <span style="line-height: 30px;font-size: 17px;font-weight: 500;">Logout</span>
                </a>
            </li>

        </div>
        
    </ul>
</div> 

<script type="text/javascript">
 $(function() {
      $( 'ul.nav li' ).on( 'click', function() {
            $( this ).parent().find( 'li.active' ).removeClass( 'active' );
            $( this ).addClass( 'active' );
      });
});
</script>

<?php 
    if($this->settings->getSetting('school_short_name') == 'ifs'){ ?>
    <style type="text/css">
        
        @media only screen and (max-width: 768px) {
            .profile .profile-image img {
                border-radius: 0;
            }
        }
    </style>
    <?php }
?>

<style type="text/css">

.x-navigation li {
    float: left;
    display: block;
    width: 100%;
    padding: 0px;
    margin: 0px;
    position: relative;
}

.x-navigation > li.xn-profile {
    padding: 0px;
    border-bottom: 1px solid #14171b;
}

.profile {
    width: 100%;
    float: left;
    padding: 15px 10px;
    position: relative;
    border-bottom: 1px solid #14171b;
    /*background: #080a0e;*/
}

.profile .profile-image {
    float: left;
    width: 100% !important;
    margin: 0px 0px 10px;
    text-align: center !important;
}

.profile .profile-data {
    width: 100%;
    float: left;
    text-align: center;
}

.x-navigation li img{
    float: left;
}  
#overHidden li a{
    background-color: #fff;
} 
#overHidden li.active > a{
    background-color: #fff !important;
}
.x-navigation li span{
 
   padding-left: 16px;
   font-size: 16px;
   font-weight:500;
   line-height: 30px;
   color: #4a4a4a;
   line-height: 30px;
} 

    #mySidenav{
    z-index: 9999;
}
.sidenav {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #111;
    /* overflow-x: hidden; */
    transition: 0.5s;
   /* padding-top: 60px;*/
}

.sidenav a {
    /*padding: 8px 8px 8px 32px;*/
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sidenav a:hover {
    color: #f1f1f1;
}

.sidenav .closebtn {
    position: relative;
    top: 0;
    right: -67%;
    font-size: 36px;
    margin-left: 50px;
    color: #fff;
}

#main {
    transition: margin-left .5s;
   /* padding: 16px;*/
}

@media screen and (max-height: 450px) {
  .sidenav {padding-top: 0;}
  .sidenav a {font-size: 18px;}
}
</style>

<style type="text/css">
.w3-animate-left {
position: relative;
animation: animateleft 0.4s;
}
.w3-sidebar {
    height: 100%;
    width: 260px;
    background-color: #ffffff !important;
    position: fixed!important;
    z-index: 1;
    overflow: auto;
}
.w3-overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2
}
@keyframes animateleft {
    from {
        left: -300px;
        opacity: 0
    }
    to {
        left: 0;
        opacity: 1
    }
}

.w3-animate-right {
    position: relative;
    animation: animateright 0.4s
}
    
.x-navigation li.a:before {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f196";
    font-size: 14px;
    width: 20px;
    height: 20px;
    color: #8c8c8c;
    right: 0px;
    top: 12px;
    cursor: pointer;
}

.x-navigation li.active > a {
    background: #1b1e24;
    color: #fff;
    -webkit-transition: all 200ms ease;
    -moz-transition: all 200ms ease;
    -ms-transition: all 200ms ease;
    -o-transition: all 200ms ease;
    transition: all 200ms ease;
}

#parent_menu .x-navigation li.active > a{
     background: #76d275;
}
.x-navigation li > a:hover {
    background: #eeeeee;
    color: #fff;
    -webkit-transition: all 200ms ease;
    -moz-transition: all 200ms ease;
    -ms-transition: all 200ms ease;
    -o-transition: all 200ms ease;
    transition: all 200ms ease;
}
.x-navigation li > ul li > a {
    font-size: 12px;
    line-height: 19px;
    font-weight: 400;
    padding: 12px 10px;
    border-bottom-color: #8c8c8c;
}

.x-navigation li > ul li > a:hover {
    background:#eeeeee;
    
}
    
.x-navigation > li > ul > li > a:hover {
    padding-left: 35px !important;
}   
.x-navigation li > ul li > a {
      padding-left: 25px;
   
}
</style>