<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>Provision User</li>
</ul>
<hr>


<div class="col-md-12">
    <form id="demo-form" autocomplete="off" action="<?php echo site_url('student/Student_controller/submitusername/'.$student_uid); ?>" class="form-horizontal"  data-parsley-validate method="post" >
        <input type="hidden" name="father_uid" value='<?php echo ($fatherDetailsPresent == 'yes')?$fatherUserName->id:0; ?>'/>
        <input type="hidden" name="mother_uid" value='<?php echo ($motherDetailsPresent == 'yes')?$motherUserName->id:0; ?>'/>

        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px;">
                    <div class="d-flex justify-content-between" style="width:100%;">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                            <span class="fa fa-arrow-left"></span>
                            </a> 
                            Provision User
                        </h3>
                        <?php if($this->authorization->isSuperAdmin()){ ?>
                            <ul class="panel-controls">
                                <button type="button" class="btn btn-danger" id="resetSession_<?php echo $student_uid ?>" onclick="session_clear_f_m_users(<?php echo $student_uid ?>)">Reset Session </button>
                            </ul>  
                        <?php } ?>
                       
                    </div>
                  
                </div>
            </div>
            <div class="card-body">
                <div class="card cd_border">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <div class="row" style="margin: 0px">
                            <div class="col-md-7">
                                <h3 class="card-title panel_title_new_style_staff">
                                Father Details
                            </h3>
                            </div>
                            <div class="col-md-5">
                                <?php $count=0; if($fatherDetailsPresent == 'yes') { $count++;?>
                                <ul class="panel-controls">
                                    <?php if($fatherDetailsPresent == 'yes' && $permit_temp_reset) { 
                                        if($fatherUserName->restore_password == NULL) { ?>
                                            <button type="button" onclick="f_m_temp_reset_password(<?= $fatherUserName->id ?>)" class="btn btn-primary">Temp Reset</button>
                                        <?php } else { ?>
                                            <button type="button" onclick="f_m_restore_password(<?= $fatherUserName->id ?>)" class="btn btn-warning">Restore</button>
                                        <?php }
                                    } ?>
                                    <input type="button" onclick="f_m_reset_password(<?= $fatherUserName->id ?>)" id="father_reset_password" value="Reset password" class="btn btn-primary"/>
                                </ul>
                                <?php } ?>
                            </div>
                        </div>
                    </div>            
                    <div class="card-body">
                        <div class="col-md-12">
                            <?php if($fatherDetailsPresent == 'yes') { ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="usernameId">Username<font color="red">*</font></label>  
                                <div class="col-md-8">
                                    <input readonly="" value="<?= $fatherUserName->username; ?>"  name="fatherusername_old" type="text"  class="form-control input-md" >        
                                </div>  
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="usernameId">Change Username if required</label>  
                                <div class="col-md-8">
                                    <input placeholder="Enter Username" id="usernameId" name="fatherusername_new" type="text" class="form-control input-md" onkeyup="checkUsernameAvailability()">
                                    <div id="exit_error"></div>
                                    <label class="control-label" for="active"><input type="checkbox" name="father_active" value='1' <?php if ($fatherUserName->active == 1) echo 'checked'; ?> >  Activate/Provision User</label>
                                </div>
                            </div>
                            <?php } else { 
                                echo 'Father details not present';
                            } ?>
                        </div>
                        
                        <div class="clearfix"></div>
                    </div>  
                </div>
                <div class="card cd_border mt-3">
                    <div class="card-header panel_heading_new_style_staff_border">
                        <div class="row" style="margin: 0px">
                            <div class="col-md-7">
                                <h3 class="card-title panel_title_new_style_staff">
                                Mother Details
                            </h3>
                            </div>
                            <div class="col-md-5">
                                <?php if($motherDetailsPresent == 'yes') { $count++; ?>
                                    <ul class="panel-controls">
                                        <?php if($motherDetailsPresent == 'yes' && $permit_temp_reset) { 
                                            if($motherUserName->restore_password == NULL) { ?>
                                                <button type="button" onclick="f_m_temp_reset_password(<?= $motherUserName->id ?>)" class="btn btn-primary">Temp Reset</button>
                                            <?php } else { ?>
                                                <button type="button" onclick="f_m_restore_password(<?= $motherUserName->id ?>)" class="btn btn-warning">Restore</button>
                                            <?php }
                                        } ?>
                                        <input type="button" onclick="f_m_reset_password(<?= $motherUserName->id ?>)" id="mother_reset_password" value="Reset password" class="btn btn-primary"/>
                                    </ul>
                                <?php } ?>
                            </div>
                        </div>
                    </div>              
                    <div class="card-body">
                        <div class="col-md-12">
                            <?php if($motherDetailsPresent == 'yes') { ?>
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="usernameId">Username<font color="red">*</font></label>  
                                <div class="col-md-8">
                                    <input readonly="" value="<?= $motherUserName->username; ?>"  name="motherusername_old" type="text"  class="form-control input-md" >        
                                </div>  
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="usernameId">Change Username if required</label>  
                                <div class="col-md-8">
                                    <input placeholder="Enter Username" id="usernameId1" name="motherusername_new" type="text" class="form-control input-md" onkeyup="checkUsernameAvailability1()">
                                    <div id="exit_error1"></div>
                                    <label class="control-label" for="active"><input type="checkbox" name="mother_active" value='1' <?php if ($motherUserName->active == 1) echo 'checked'; ?> >  Activate/Provision User</label>
                                </div>
                            </div>
                            <?php } else { 
                                echo 'Mother details not present.';
                            } ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                </div>
            </div>
            <div class="card-footer panel_footer_new">
                <center>
                <?php if($count) { ?>
                    <button id="btnSubmit" type="submit" class="btn btn-primary">Submit</button>
                <?php } ?>
                    <a class="btn btn-warning" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Back</a>
                </center>
            </div>            
        </div>

        <div class="clearfix"></div>    
    </form>
</div>


<script type="text/javascript">

    function f_m_reset_password(fmid) {      
       $.ajax({
            url: '<?php echo site_url('student/student_controller/reset_parent_password'); ?>',
            type: 'post',
            data: {'fmid':fmid},
            success: function(data) {
                if (data) {
                    $(function(){
                      new PNotify({
                        title: 'Success',
                        text:  'Password reset successfully.',
                        type: 'success',
                      });
                    });
                }else{
                    $(function(){
                      new PNotify({
                        title: 'Error',
                        text:  'Failed to reset password.',
                        type: 'error',
                      });
                    });
                }
            }
        });
    }

    function f_m_temp_reset_password(fmid) {
       $.ajax({
            url: '<?php echo site_url('student/student_controller/temp_reset_parent_password'); ?>',
            type: 'post',
            data: {'fmid':fmid},
            success: function(data) {
                if (data) {
                    $(function(){
                      new PNotify({
                        title: 'Success',
                        text:  'Password reset successfully.',
                        type: 'success',
                      });
                    });
                    location.reload();
                }else{
                    $(function(){
                      new PNotify({
                        title: 'Error',
                        text:  'Failed to reset password.',
                        type: 'error',
                      });
                    });
                }
            }
        });
    }

    function f_m_restore_password(fmid) {
       $.ajax({
            url: '<?php echo site_url('student/student_controller/restore_parent_password'); ?>',
            type: 'post',
            data: {'fmid':fmid},
            success: function(data) {
                if (data) {
                    $(function(){
                      new PNotify({
                        title: 'Success',
                        text:  'Password restored successfully.',
                        type: 'success',
                      });
                    });
                    location.reload();
                }else{
                    $(function(){
                      new PNotify({
                        title: 'Error',
                        text:  'Failed to reset password.',
                        type: 'error',
                      });
                    });
                }
            }
        });
    }

    function checkUsernameAvailability() {
        var username = $("#usernameId").val();
        $.ajax({
            url: '<?php echo site_url('staff/staff_controller/checkUsername'); ?>',
            type: 'post',
            data: {'username':username},
            success: function(data) {
               if (!$.trim(data)) {
                    $('#exit_error').html('');
                    $("#btnSubmit").prop('disabled',false);
                }else{
                    $('#exit_error').html(data);
                    $("#btnSubmit").prop('disabled',true);
                }
            }
        });
    }
     function checkUsernameAvailability1() {
        var username = $("#usernameId1").val();
        $.ajax({
            url: '<?php echo site_url('staff/staff_controller/checkUsername'); ?>',
            type: 'post',
            data: {'username':username},
            success: function(data) {
               if (!$.trim(data)) {
                    $('#exit_error1').html('');
                    $("#btnSubmit").prop('disabled',false);
                }else{
                    $('#exit_error1').html(data);
                    $("#btnSubmit").prop('disabled',true);
                }
            }
        });
    }

    function session_clear_f_m_users(student_id) {
       $("#resetSession_"+student_id).prop('disabled',true);
        $.ajax({
            url: '<?php echo site_url('student/student_controller/resetSessionStudent'); ?>',
            type: 'post',
            data: {'student_id':student_id},
            success: function(data) {
               if ($.trim(data)) {
                    $("#resetSession_"+student_id).prop('disabled',false);
                    $('#exit_error1').html('');
                    $("#btnSubmit").prop('disabled',false);
                    $(function(){
                      new PNotify({
                        title: 'Success',
                        text:  'Reset sesson successfully.',
                        type: 'success',
                      });
                    });
                }else{
                    $(function(){
                      new PNotify({
                        title: 'Error',
                        text:  'Failed to reset session.',
                        type: 'error',
                      });
                    });
                    $('#exit_error1').html(data);
                    $("#btnSubmit").prop('disabled',true);
                }
            }
        });
    }


</script>