<?php
class Certificates_Model extends CI_Model {
  private $yearId;
  public function __construct() {
      parent::__construct();
      $this->yearId =  $this->acad_year->getAcadYearId();
      $this->load->library('filemanager');

  }

    //Get Student Details
  public function getFullStudentDataById($stdId){
    $this->db->select("sa.*,sa.id,sa.admission_no,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) AS stdName,cs.section_name as sectionName, c.class_name as className, cs.id as csId, sa.preferred_contact_no as contact_no, sa.dob as dob, sa.nationality as nationality, sa.gender as gender, sa.religion as religion, sa.category as category, sa.caste as caste,sa.student_sub_caste, sa.aadhar_no as aadhar_no, CONCAT(ifnull(sa.birth_taluk,''),',', ifnull(sa.birth_district,'')) AS birthplace, a.id as aid, u.email as email, sy.admission_type, sy.is_rte, sy.board, sy.boarding, sy.medium, sy.roll_no, sa.date_of_joining, sy.student_house, sy.donor, sa.emergency_info, sy.picture_url, sa.sts_number, sy.tc_number, ifnull(sy.combination,'') as combination, ifnull(mother_tongue,'') as mother_tongue, sa.blood_group, sa.custom1, sa.custom2, sa.custom3, sa.custom4, sa.custom5, sa.custom6, sa.custom7, sa.custom8, sa.custom9, sa.custom10, DATE_FORMAT(sa.date_of_joining, '%Y') as joining_year,DATE_FORMAT(sy.terminate_date,'%d-%m-%Y') as terminate_date,sa.enrollment_number,sa.passport_number,sa.identification_mark1,ay.acad_year as admission_acad_year,ays.acad_year as current_acad_year,IF(pc.class_name IS NULL, '', CONCAT(pc.class_name, ' ', IFNULL(cs.section_name, ''))) AS promotion_class_name,ifnull(cmc.combination_name,'') as combination_name");
    $this->db->from("student_admission sa");
    $this->db->join("student_year sy", "sa.id=sy.student_admission_id");
    $this->db->join("class_master_combinations cmc", "sy.combination_id=cmc.id","left");
    $this->db->where("sa.id", $stdId);
    $this->db->where('sy.acad_year_id',$this->yearId);        
    $this->db->join("class_section cs", "sy.class_section_id=cs.id",'left');
    $this->db->join("class c", "sy.class_id=c.id",'left');
    $this->db->join("class pc", "c.promotion_class=pc.id", 'left');
    $this->db->join("avatar a", "sa.id=a.stakeholder_id",'left');
    $this->db->join("users u", "a.id=u.id",'left');
    $this->db->join("academic_year ay", "ay.id=sa.admission_acad_year_id",'left');
    $this->db->join("academic_year ays", "ays.id=sy.acad_year_id",'left');
    $this->db->where("a.avatar_type", 1);
    $student = $this->db->get()->row();
    if (!empty($student->board)) {
      $board = $this->settings->getSetting('board');
  
      if (!empty($board) && (isset($board[$student->board]))) {
        $student->board = $board[$student->board];
      } else {
          $student->board = ''; 
      }
  }


    if (isset($student->category)!= ''&& isset($student->category) !='0') {
       foreach ($this->settings->getSetting('category') as $value => $name) {
        if ($student->category == $value) {
          $student->category = $name;
        }
      }
    }else{
      $student->category = 'NA';
    }
    
    $student_electives = $this->db->select("sem.id as elective_id, sub.subject_name")
    ->from("elective_student_master sem")
    ->join('elective_master_group_subjects gs','gs.id=sem.elective_master_group_subject_id','left')
    ->join('subject_master sub','sub.id=gs.subject_master_id','left')
    ->where("sem.student_admission_id", $stdId)
    ->get()->result();

    $student->electives = '';
    foreach ($student_electives as $key => $val) {
      if ($val->elective_id) {
        $student->electives .= $val->subject_name.',';
      }
    }
    $student->electives = trim($student->electives,',');
    //Get Health Details
    $health = $this->db->select("h.student_id,h.blood_group,h.physical_disability,h.learning_disability,h.physical_disability_reason,h.learning_disability_reason,h.allergy,h.family_history,h.anaemia,h.fit_to_participate,h.height,h.weight,h.hair,h.skin,h.ear,h.nose,h.throat,h.neck,h.respiratory,h.cardio_vascular,h.abdomen,h.nervous_system,h.left_eye,h.right_eye,h.extra_oral,h.bad_breath,h.tooth_cavity,h.plaque,h.gum_inflamation,h.stains,h.gum_bleeding,h.soft_tissue")
                ->from('student_health h')
                ->where('student_id', $student->id)
                ->get()->row();
                $student->health = $health;

    //Get Fee Details
    // $fee = $this->db->select("f.total_amount_paid,f.total_concession,f.discount_amount,f.fine_amount,f.card_charge_amount,f.fee_installment_template")
            // ->from('fee_transaction f')
            // ->where('student_id', $student->id)
            // ->get()->row();
            // $student->fee = $fee;
    //Get Competition Details
    $competition = $this->db->select("csr.competition_id,csr.remarks,csr.other_remarks,cm.competition_name,cm.venue_address,cm.organizer,cm.description")
                    ->from('competition_student_registration csr')
                    ->where('std_userId', $student->id)
                    ->join("competition_master cm", "csr.competition_id=cm.id",'left')
                    ->get()->result();
                    $student->competition = $competition;

    $addressTypes = $this->settings->getSetting('student_address_types');
    $no_student_address = 0;
    if(!empty($addressTypes)){
        $atype = 1;
        $stakeholder_id = $student->id;
    }else{
      $no_student_address = 1;
    }
    
    if($no_student_address==0){
      $address = $this->db->select('a.Address_line1, a.Address_line2, a.area, a.district, a.state, a.country, a.pin_code, a.address_type')
          ->from('address_info a')
          ->where_in('avatar_type', $atype)
          ->where('stakeholder_id', $stakeholder_id)
          ->get()->result();
          
          if(!empty($address)){
            $student->address = $address;
          }else{
            $student->address = array();
          }
    }else{
      $student->address = array();
    }  
     return $student;
  }

  public function get_first_joined_class($std_id){
    return $this->db->select("c.class_name")
    ->from("student_admission sa")
    ->join("student_year sy", "sa.id=sy.student_admission_id")
    ->join('class c','c.id=sy.class_id')
    ->where("sa.id", $std_id)
    ->get()->row()->class_name;
  }

  public function getFullStudentDocumentDataById($stdId){
    return $this->db->select('id, document_type')
    ->from('student_documents')
    ->where('student_id',$stdId)
    ->get()->result();
  }

  public function getFullStudentPreviousSchoolDataById($stdId){

  
    $prevResult = $this->db->select("id as spsid, year_id as year, school_name, class, board, medium_of_instruction, board_other, school_address, ifnull(total_marks,'') as total_marks, ifnull( total_marks_scored,'') as total_marks_scored, ifnull(total_percentage,'') as total_percentage")
    ->from('student_prev_school sps')
    ->where('sps.student_id',$stdId)
    ->get()->result();

    if (empty($prevResult)) {
        return false;
    } 
    $spsId = [];
    foreach ($prevResult as $key => $val) {
        $spsId[] = $val->spsid;
    }
    $prev_marks_Result = $this->db->select('spsm.id as spsmId, sps_id, sub_name,grade, percentage, marks, marks_scored')
    ->from('student_prev_school_marks spsm')
    ->where_in('spsm.sps_id',$spsId)
    ->get()->result();
    foreach ($prevResult as $key => &$val) {
        foreach ($prev_marks_Result as $key => $res) {
           if ($val->spsid == $res->sps_id) {
                $val->marks[] = $res;
           } 
        }
    }
     return $prevResult;
  }

  public function get_application_no_from_application_form($student_id){
    $result = $this->db->select('af.application_no')
    ->from('student_admission sa')
    ->where('sa.id',$student_id)
    ->join('admission_forms af','sa.admission_form_id=af.id')
    ->get()->row();
    if (!empty($result)) {
      return $result->application_no;
    }else{
      return false;
    }
  }

  public function get_admission_form_data($student_id){
    return $this->db->select('af.sibling_school_name, af.sibling_student_name, af.sibling_student_class, af.physical_disability, af.learning_disability')
    ->from('student_admission sa')
    ->where('sa.id',$student_id)
    ->join('admission_forms af','sa.admission_form_id=af.id')
    ->get()->row();
  }

  //Get Parents Details
  public function getParentDataById($stdId,$parenttype){
      $this->db->select("sa.id, u.email, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS pName, p.id as parentId, p.qualification, p.occupation, p.company, p.annual_income, p.mobile_no, p.aadhar_no, p.designation,p.blood_group,p.picture_url");
      $this->db->from("student_admission sa");
      $this->db->where("sa.id",$stdId);
      $this->db->join("student_relation sr", "sa.id=sr.std_id",'left');
      $this->db->where("sr.relation_type", $parenttype);
      $this->db->join("parent p", "sr.relation_id=p.id",'left');
      $this->db->join("avatar a", "p.id=stakeholder_id",'left');
      $this->db->where("a.avatar_type",'2');
      $this->db->join("users u", "a.id=u.id",'left');
      $parent = $this->db->get()->row();
      if (!empty($parent)) {
         //Get addresses
        if ($parenttype == 'Father')
            $addressTypes = $this->settings->getSetting('father_address_types');
        else 
            $addressTypes = $this->settings->getSetting('mother_address_types');
            
        $addresses = $this->db_readonly->select('a.Address_line1, a.Address_line2, a.area, a.district, a.state, a.country, a.pin_code, a.address_type')
            ->from('address_info a')
            ->where('stakeholder_id', $parent->parentId)
            ->where('avatar_type',2)
            ->get()->result();
        $aTypes = array();
        foreach ($addresses as $ad) {
            if(empty($addressTypes)) {
                $parent->addresses = array();
            } else {
                foreach ($addressTypes as $key => $val) {
                    if ($ad->address_type == $key) {
                        $tmpAddress = new stdClass();
                        $tmpAddress->address_type = $val;
                        $addressParts = [
                          $ad->Address_line1,
                          $ad->Address_line2,
                          $ad->area,
                          $ad->district,
                          $ad->state,
                          $ad->country,
                          $ad->pin_code
                      ];
                      
                        $tmpAddress->address = implode(', ', array_filter($addressParts));
                        $tmpAddress->Address_line1 = $ad->Address_line1;
                        $tmpAddress->Address_line2 = $ad->Address_line2;
                        $tmpAddress->area = $ad->area;
                        $tmpAddress->state = $ad->state;
                        $tmpAddress->district = $ad->district;
                        $tmpAddress->country = $ad->country;
                        $tmpAddress->pin_code = $ad->pin_code;
                        $parent->addresses [] = $tmpAddress;
                        break;
                    }
                }
            }
        }
      }
      return $parent;
     
  }

  public function getGuardianDataById($stdId,$parenttype){
    $this->db->select("sa.id, p.email, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS pName, p.id as parentId, p.qualification, p.occupation, p.company, p.annual_income, p.mobile_no, p.aadhar_no, p.designation");
    $this->db->from("student_admission sa");
    $this->db->where("sa.id",$stdId);
    $this->db->join("student_relation sr", "sa.id=sr.std_id");
    $this->db->where("sr.relation_type", $parenttype);
    $this->db->join("parent p", "sr.relation_id=p.id");
    return $this->db->get()->row();
  }

  //Get Template 
  public function getTemplate($templateid){
      $this->db->select("ct.id,ct.html_content,ct.template_name,ct.purpose,ct.type,ct.template_background,ifnull(ct.receipt_number_id,0) as receipt_number_id,ct.date_format");
      $this->db->from("cert_templates ct");
      $this->db->where("id", $templateid);
      return $this->db->get()->row();
  }

  //Creating a template
  public function create_cert_template(){
    $avatar_id = $this->authorization->getAvatarId();
    $id = $this->db->select("a.stakeholder_id")
          ->from("avatar a")
          ->where("a.id",$avatar_id)
          ->get()->row();
    $data = array(
      'created_by' => $id->stakeholder_id,
      'template_name' => $this->input->post('name'),
      'purpose' => $this->input->post('purpose'),
      'html_content' => $this->input->post('html_content'),
      'type' => 'custom'
    );
    return $this->db->insert('cert_templates', $data);
  }

  //Get Predefined Templates List
  public function getPreTemplatesList(){
    $this->db->select("ct.id,ct.template_name,ct.html_content,ct.purpose,ct.created_by,ct.type");
    $this->db->from("cert_templates ct");
    $this->db->where("ct.type","predefined");
    return $this->db->get()->result();
  }

  //Get Custom Templates List
  public function getCusTemplatesList(){
    $this->db->select("ct.id,ct.template_name,ct.html_content,ct.purpose,ct.created_by,ct.type,date_format(ct.created_on,'%d-%b-%Y  %h:%i %p') as created_on");
    $this->db->from("cert_templates ct");
    $this->db->where("ct.type","custom");
    $result= $this->db->get()->result();

    foreach ($result as $key => $value) {
      $value->created_by = $this->_getAvatarNameById($value->created_by);
    }
    return $result;
  }

  public function convert_number($number) {
    if (($number < 0) || ($number > 999999999)) {
        throw new Exception("Number is out of range");
    }
    $Gn = floor($number / 1000000);
    /* Millions (giga) */
    $number -= $Gn * 1000000;
    $kn = floor($number / 1000);
    /* Thousands (kilo) */
    $number -= $kn * 1000;
    $Hn = floor($number / 100);
    /* Hundreds (hecto) */
    $number -= $Hn * 100;
    $Dn = floor($number / 10);
    /* Tens (deca) */
    $n = $number % 10;
    /* Ones */
    $res = "";
    if ($Gn) {
        $res .= $this->convert_number($Gn) .  "Million";
    }
    if ($kn) {
        $res .= (empty($res) ? "" : " ") .$this->convert_number($kn) . " Thousand";
    }
    if ($Hn) {
        $res .= (empty($res) ? "" : " ") .$this->convert_number($Hn) . " Hundred";
    }
    $ones = array("", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eightteen", "Nineteen");
    $tens = array("", "", "Twenty", "Thirty", "Fourty", "Fifty", "Sixty", "Seventy", "Eigthy", "Ninety");
    if ($Dn || $n) {
        if (!empty($res)) {
            $res .= " and ";
        }
        if ($Dn < 2) {
            $res .= $ones[$Dn * 10 + $n];
        } else {
            $res .= $tens[$Dn];
            if ($n) {
                $res .= " " . $ones[$n];
            }
        }
    }
    if (empty($res)) {
        $res = "zero";
    }
    return $res;
  }

  public function update_cert_template(){
    $avatar_id = $this->authorization->getAvatarId();
    $id = $this->db->select("a.stakeholder_id")
          ->from("avatar a")
          ->where("a.id",$avatar_id)
          ->get()->row();
    $data = array(
      'created_by' => $id->stakeholder_id,
      'template_name' => $this->input->post('name'),
      'purpose' => $this->input->post('purpose'),
      'html_content' => $this->input->post('html_content'),
      'type' => $this->input->post('template_type')
    );
    // echo "<pre>"; print_r($data); die();
    $template_id = $this->input->post('template_id');
    $this->db->where('id', $template_id);
    return $this->db->update('cert_templates', $data);
  }

  public function getTemplateContent($template_id) {
    return $this->db->query('select html_content from cert_templates where id='.$template_id)->row()->html_content;
  }

  public function addCertificate() {
    $created_by = $this->authorization->getAvatarId();
    $this->db->trans_start();
    $data = array(
      'created_by' => $created_by,
      'last_modified_by' => $created_by,
      'template_name' => $this->input->post('name'),
      'purpose' => $this->input->post('purpose'),
      'template_background' => $this->input->post('template_background'),
      'html_content' => $this->input->post('html_content'),
      'type' => 'custom',
      'receipt_number_id' => $this->input->post('running_number'),
      'date_format' => $this->input->post('date_format')
    );
    $this->db->insert('cert_templates', $data);
    $insert_id=$this->db->insert_id();
    if(! empty($this->input->post('accessible_by_staff'))){
    $staff_insert_batch=[];
    foreach ($this->input->post('accessible_by_staff') as $staff_id) {
      $staff_insert_batch[]=array(
        'cert_temp_id' =>$insert_id,
        'staff_id' => $staff_id
      );
    }
    $this->db->insert_batch('certificate_template_access_staff', $staff_insert_batch);
    }

    $this->db->trans_complete();
    if($this->db->trans_status()){
        return 1;
    }else{
        return 0;
    }
  }

  public function updateCertificate($id) {
    $modified_by = $this->authorization->getAvatarId();
    $template_background= $this->input->post('template_background');
    // echo '<pre>'; print_r($this->input->post()); die();
    $this->db->trans_start();
    $data = array(
      'template_name' => $this->input->post('name'),
      'purpose' => $this->input->post('purpose'),
      'html_content' => $this->input->post('html_content'),
      'type' => $this->input->post('certificate_type'),
      'last_modified_by' => $modified_by,
      'receipt_number_id' => $this->input->post('running_number'),
      'date_format' => $this->input->post('date_format')
    );
    if(! empty($template_background)){
      $data['template_background'] = $template_background;
    }
    $this->db->where('id', $id)->update('cert_templates', $data);

    // **Delete old staff assignments for this template**
    $this->db->where('cert_temp_id', $id);
    $this->db->delete('certificate_template_access_staff');

    // staff accessability data is added
    if(! empty($this->input->post('accessible_by_staff'))){
      $staff_insert_batch=[];
      foreach ($this->input->post('accessible_by_staff') as $staff_id) {
        $staff_insert_batch[]=array(
          'cert_temp_id' =>$id,
          'staff_id' => $staff_id
        );
      }
      $this->db->insert_batch('certificate_template_access_staff', $staff_insert_batch);
      }
  
      $this->db->trans_complete();
      if($this->db->trans_status()){
          return 1;
      }else{
          return 0;
      }
  }

  public function getCertificateTemplate($id) {
    return $this->db->select('*')->where('id', $id)->get('cert_templates')->row();
  }

  public function getCertificates($stdId){    
    $this->db_readonly->select("ct.id,ct.template_name,ct.purpose,ct.created_by,ct.type,sc.pdf_path,  ,(case when sc.pdf_status=1 then 'Active' else 'Inactive' end) as pdf_status ,sc.id as scId, sc.publish_status, DATE_FORMAT(sc.issued_on,'%D %b %Y') as issued_date, a.friendly_name,(case when sc.parent_visibility=1 then 'Yes' else 'No' end) as parent_visibility");
    $this->db_readonly->from("cert_templates ct");
    $this->db_readonly->join('student_certificate sc',"ct.id=sc.certificate_id and sc.student_id=$stdId",'left');
    $this->db_readonly->join('avatar a',"sc.issued_by=a.id",'left');
    $this->db_readonly->where_in("sc.publish_status", ["ISSUED", "Deactive"]);
    $this->db_readonly->order_by('sc.id', 'DESC');

    $data= $this->db_readonly->get()->result();
    foreach ($data as $key => $value) {
      $value->pdf_path = $this->filemanager->getFilePath($value->pdf_path);
    }
    return $data;
    // echo '<pre>'; print_r($return);
    // echo $this->db->last_query(); die();
  }

  public function get_issued_certificates_template($stdId, $scId){
    return  $this->db->select("sc.*, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName")
    ->from('student_certificate sc')
    ->where('sc.id',$scId)
    ->where('sc.student_id',$stdId)
    ->join('student_admission sd',"sd.id=sc.student_id")
    ->get()->row();
  }

  public function getCertificates_all(){

    $this->db_readonly->select('cert_temp_id');
    $this->db_readonly->from('certificate_template_access_staff');
    $this->db_readonly->where('staff_id',$this->authorization->getAvatarStakeHolderId());
    $staff_access=$this->db_readonly->get()->row();
    
    $this->db_readonly->select("ct.id,ct.template_name,ct.html_content,ct.purpose,ct.created_by,ct.type");
    $this->db_readonly->from("cert_templates ct");
    if(! empty($staff_access)){
      $this->db_readonly->join("certificate_template_access_staff ctas",'ctas.cert_temp_id=ct.id');
      $this->db_readonly->where("ctas.staff_id",$this->authorization->getAvatarStakeHolderId());
    }
    $data= $this->db_readonly->get()->result();
    return $data;
  }

  

  public function search_std_class_wise_certificate($classId, $certificate_id){

    return $this->db->select("sc.id as scId, sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, publish_status, sd.admission_no")
    ->from('student_year sy')
    ->join('student_admission sd',"sy.student_admission_id=sd.id and sy.acad_year_id=$this->yearId")
    ->join('class c','sy.class_id=c.id')
    ->join('student_certificate sc',"sd.id=sc.student_id and sc.certificate_id = $certificate_id",'left')
    ->where('sy.class_id',$classId)
    ->where('sd.admission_status',2)
    ->order_by('sd.first_name')
    ->get()->result();
  }

    public function search_std_adm_no_wise_certificate($adNo, $certificate_id){
    return  $this->db->select("sc.id as scId, sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, publish_status, sd.admission_no")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->join('student_certificate sc',"sd.id=sc.student_id and sc.certificate_id = $certificate_id",'left')
    ->where("sd.admission_no", $adNo)
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sd.admission_status',2)
    ->order_by('sd.first_name')
    ->get()->result();
  }

   public function getALLTemplatesList(){
    $this->db->select("ct.id,ct.template_name,ct.html_content,ct.purpose,ct.created_by,ct.type");
    $this->db->from("cert_templates ct");
    return $this->db->get()->result();
  }

  public function search_std_detials_for_certificate($stdId){
    return  $this->db->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, sd.admission_no")
    ->from('student_year sy')
    ->join('student_admission sd','sy.student_admission_id=sd.id')
    ->join('class c','sy.class_id=c.id')
    ->where("sd.id", $stdId)
    ->get()->row();
  }

  public function update_certificate_html($html, $certificate_id, $stdId){
    $data = array(
      'certificate_id' =>$certificate_id, 
      'student_id' =>$stdId, 
      'html_template' =>$html, 
    );
    $this->db->where('certificate_id',$certificate_id);
    $this->db->where('student_id',$stdId);
    $query = $this->db->get('student_certificate')->row();
    if (!empty($query)) {
        $this->db->where('certificate_id',$certificate_id);
        $this->db->where('student_id',$stdId);
     return $this->db->update('student_certificate',$data);
    }
    return $this->db->insert('student_certificate',$data);
  }

  public function updateCertificatePath($path, $certificate_id, $stdId){
    $this->db->where('certificate_id',$certificate_id);
    $this->db->where('student_id',$stdId);
    return $this->db->update('student_certificate', array('pdf_path'=> $path, 'pdf_status' => 0));
  }

  public function updateCertificatePdfLink($path, $status) {
    $this->db->where('pdf_path',$path);
    return $this->db->update('student_certificate', array('pdf_status' => $status));
  }

  public function download_Ceritifcate($id){
    return $this->db->select('pdf_path')->where('id', $id)->get('student_certificate')->row()->pdf_path;
  }
  
  public function save_certificate_for_students(){
    $data = array(
      'certificate_id' =>$_POST['template_id'], 
      'student_id' =>$_POST['stdId'], 
      'html_template' =>$_POST['html'],
      'publish_status'=>'ISSUED',
    );
    $this->db->where('certificate_id',$_POST['template_id']);
    $this->db->where('student_id',$_POST['stdId']);
    $query = $this->db->get('student_certificate')->row();
    if (!empty($query)) {
      $this->db->where('certificate_id',$_POST['template_id']);
      $this->db->where('student_id',$_POST['stdId']);
     return $this->db->update('student_certificate',$data);
    }
    return $this->db->insert('student_certificate',$data);
  }

  public function getPDFLink($id){
  //   $this->db->select("pdf_path")
  //   ->from('student_certificate')
  //   ->where('student_certificate.id ', $certificate_id);
  //   echo $this->db->get()->row();
  // echo $this->db->last_query(); die();
  return $this->db->select('pdf_path')->where('id', $id)->get('student_certificate')->row()->pdf_path;
  }

  public function delete_certificate($certificate_id){
    $data = array('publish_status' => 'Deleted');
    $this->db->where('id',$certificate_id);
    return  $this->db->update('student_certificate',$data);
    
  }


  public function inactive_certificate($certificate_id){
    // echo "hi"; die();
    $data = array('publish_status' => 'Deactive');
    $this->db->where('id',$certificate_id);
    return  $this->db->update('student_certificate',$data);
    // echo $return; 
    // echo $this->db->last_query(); die();
  }


  public function active_certificate($certificate_id){
    // echo "hi"; die();
    $data = array('publish_status' => "ISSUED");
    $this->db->where('id',$certificate_id);
    return  $this->db->update('student_certificate',$data);
    // echo $return; 
    // echo $this->db->last_query(); die();
  }

  public function get_school_logo(){
    $result = $this->db_readonly->select('value')->from('config')->where('name', 'school_logo')->get()->row();

    if (empty($result))
      return '';
    else 
     return $result->value;
  }
  public function get_school_seal(){
    $result = $this->db_readonly->select('value')->from('config')->where('name', 'school_seal')->get()->row();
    
    if (empty($result))
      return '';
    else 
     return $result->value;
  }
  public function get_signature(){
    $result = $this->db_readonly->select('value')->from('config')->where('name', 'school_signature')->get()->row();

    if (empty($result))
        return '';
    else 
      return $result->value;
  }

  public function get_certificates_parent($studentId){
    $this->db->select("ct.id,ct.template_name,sc.pdf_path,sc.id as scId, DATE_FORMAT(sc.issued_on,'%D %b %Y') as issued_date");
    $this->db->from("cert_templates ct");
    $this->db->join('student_certificate sc',"ct.id=sc.certificate_id",'left');
    $this->db->where("sc.publish_status", "ISSUED");
    $this->db->where("sc.parent_visibility", 1);
    $this->db->where("sc.student_id", $studentId);
    

    // $this->db->join('avatar a',"sc.issued_by=a.id",'left');
    $this->db->order_by('sc.id', 'DESC');

    return $this->db->get()->result();
    // echo '<pre>'; print_r($return);


    //  echo $this->db->last_query(); die();
  }

  public function getFullStudentFeesDataById($stud_id){
    return $this->db->select('fc.friendly_name')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
    ->join('feev2_cohorts fc','fc.id=fcs.feev2_cohort_id')
    ->where('fb.acad_year_id',$this->acad_year->getAcadYearId())
    ->where('fcs.student_id',$stud_id)
    ->where('fb.is_transport',1)
    ->get()->row();
  }

  public function getissued_certificate_all($stdId){
    $this->db->select("sc.id, sc.certificate_id,sc.student_id,sc.certificate_data, ct.template_name");
    $this->db->join("cert_templates ct", "ct.id=sc.certificate_id");
    $this->db->where("sc.student_id",$stdId);
    $this->db->from("student_certificate sc");
    return $this->db->get()->result();
  }

  public function get_issued_Certificates($templateid){
      $this->db->select("sc.id,sc.certificate_id,sc.student_id,sc.edited_data, ct.id, ct.html_content,ct.template_name,sc.certificate_data,ifnull(ct.receipt_number_id,0) as receipt_number_id,template_background,ct.date_format");
      $this->db->from("student_certificate sc");
      $this->db->join("cert_templates ct", "ct.id =sc.certificate_id");
      $this->db->where("sc.id", $templateid);
      return $this->db->get()->row();
      
  }

  public function get_std_adm_data($std_id){
    $result = $this->db->select("af.student_aadhar,date_format(af.created_on,'%d-%m-%Y') as application_date,std_mother_tongue,af.passport_number,s_present_country,s_present_state,s_present_district,s_present_pincode,s_permanent_addr,s_permanent_state,s_permanent_district,s_permanent_pincode,f_position,m_position,f_company_name,m_company_name,f_company_addr,m_company_addr,know_about_us,af.s_permanent_country")
    ->from('student_admission sa')
    ->join('admission_forms af','af.id=sa.admission_form_id','left')
    ->where('sa.id',$std_id)
    ->get()->row();

    return $result;
  }

  public function get_all_staff_student_names_approved_only(){

    $student =  $this->db->select(" concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, ifnull(cs.section_name,'') as section_name,ifnull(c.class_name,'') as class_name,ifnull(sd.identification_code,'') as id_number, ifnull(sd.id,'') as student_id,sd.admission_status,sy.promotion_status")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('class c','sy.class_id=c.id','left')

      ->get()->result();

    return array('student'=>$student);

  }

  public function get_certificate_report(){
    $input=$this->input->post();

    $this->db_readonly->select("sa.id,concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name,
    concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section ,
    issued_by, date_format(sc.issued_on,'%d-%b-%Y %h:%i %p') as issued_on,certificate_id,template_name,(case when sc.parent_visibility=1 then 'Yes' else 'No' end) as parent_visibility,sc.pdf_path");
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    $this->db_readonly->join('class_section cs',"sy.class_section_id=cs.id");
    $this->db_readonly->join('class c',"sy.class_id=c.id");
    $this->db_readonly->join('student_certificate sc',"sc.student_id = sa.id");
    $this->db_readonly->join('cert_templates ct',"ct.id=sc.certificate_id");
    $this->db_readonly->where_in("sc.publish_status", ["ISSUED", "Deactive"]);
    // $this->db_readonly->where('sy.promotion_status !=',4);
    // $this->db_readonly->where('sy.promotion_status !=',5);
    // $this->db_readonly->where('sa.admission_status',2);
    $this->db_readonly->where('sc.pdf_status',1);
    if($input['class_section']){
      $this->db_readonly->where_in('cs.id',$input['class_section']);
    }
    if($input['cert_template_id']){
      $this->db_readonly->where_in('sc.certificate_id',$input['cert_template_id']);
    }
    $this->db_readonly->order_by('sc.id','desc');
    $result= $this->db_readonly->get()->result();

    foreach ($result as $key => $value) {
      $value->issued_by = $this->_getAvatarNameById($value->issued_by);
      $value->pdf_path = $this->filemanager->getFilePath($value->pdf_path);
    }
    return $result;
  }

  private function _getAvatarNameById($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return 'Admin';
    }
  }

  public function toggle_parent_visibility(){
    $input=$this->input->post();

    $this->db->set('parent_visibility',$input['visibility']);
    $this->db->where('id', $input['id']);
    return $this->db->update('student_certificate');
  }

  public function getCertificates_admission_no(){    
    $admission_id=$this->input->post();
    $admission_id= $admission_id['admin_no'];
    $this->db_readonly->select("ct.id,ct.template_name,ct.purpose,ct.created_by,ct.type,sc.pdf_path,sc.pdf_status,sc.id as scId, sc.publish_status, DATE_FORMAT(sc.issued_on,'%D %b %Y') as issued_date, a.friendly_name,(case when sc.parent_visibility=1 then 'Yes' else 'No' end) as parent_visibility,sa.id as student_id");
    $this->db_readonly->from("cert_templates ct");
    $this->db_readonly->where_in("sc.publish_status", ["ISSUED", "Deactive"]);
    $this->db_readonly->join('student_certificate sc',"ct.id=sc.certificate_id",'left');
    $this->db_readonly->join('avatar a',"sc.issued_by=a.id",'left');
    $this->db_readonly->join('student_admission sa',"sa.id=sc.student_id");
    $this->db_readonly->where('sa.admission_no',$admission_id);
    $this->db_readonly->order_by('sc.id', 'DESC');

    return $this->db_readonly->get()->result();
    // echo '<pre>'; print_r($this->db_readonly->last_query());
  }

  public function getStudent_id_admission_no(){
    $admission_id=$this->input->post();
    $admission_id=  trim($admission_id['admin_no']);
    $this->db_readonly->select('id as student_id');
    $this->db_readonly->from('student_admission');
    $this->db_readonly->where('admission_no',$admission_id);
    return $this->db_readonly->get()->row()->student_id;
  }

  public function get_reciept_number_certificate($certificate_reciept_book_id){
     
    $this->load->library('fee_library');
    $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$certificate_reciept_book_id)->get()->row();
    if (!empty($receipt_book)) {
        $receipt_number =  $this->fee_library->receipt_format_get_update($receipt_book);
    }else{
        $receipt_number =  0;
    }
    return $receipt_number;
  }

  public function certificate_update_running_number($certificate_reciept_book_id){
    $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$certificate_reciept_book_id)->get()->row();
    $this->db->where('id',$certificate_reciept_book_id);
    $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
  }

  public function getAllRecieptBook(){
    $this->db_readonly->select('id,infix');
    $this->db_readonly->from('feev2_receipt_book');
    return $this->db_readonly->get()->result();

  }

  public function toggle_pdf_status(){
    $input=$this->input->post();
    $this->db->set('pdf_status',$input['pdf_status']);
    if($input['pdf_status']==0){
    $this->db->set('parent_visibility',0);
    }
    $this->db->where('id', $input['certificate_id']);
    return $this->db->update('student_certificate');
  }

  public function get_all_staff() {
    $staffs= $this->db_readonly->select('sm.id, CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')->where('status',2)->get('staff_master sm')->result();
    return $staffs;
    // echo '<pre>'; print_r($staffs); die();
  }

  public function get_staff_accessable_template($cert_template_id){
    $this->db_readonly->select('id,staff_id');
    $this->db_readonly->from('certificate_template_access_staff');
    $this->db_readonly->where('cert_temp_id',$cert_template_id);
    return $this->db_readonly->get()->result();
  }
}

?>
