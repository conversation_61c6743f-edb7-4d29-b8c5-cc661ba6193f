<?php

class Subjects_model extends CI_Model {
    public function __construct() {
      parent::__construct();
    }
    
    public function add_or_update_subject($input) {
      $data = array(
        'subject_code' => trim($input['subject_code']) != '' || $input['subject_code'] != '-' ? $input['subject_code'] : null,
        'short_name' => $input['short_name'],
        'subject_name' => $input['subject_name'],
        'mapping_string' => $input['mapping_string'],
        'status' => 1
      );
      if ($input['mode'] === 'create') {
        $result = $this->db->insert('subject_master', $data);
        if (!$result) return $result;
        $last_id = $this->db->insert_id();
      } else {
        $result = $this->db->where('id', $input['subject_id'])->update('subject_master', $data);
        if (!$result) return $result;
        $last_id = $input['subject_id'];
        $this->db->where('subject_master_id', $last_id)->update('lp_subjects', ['subject_name' => $input['subject_name']]);
      }
      return $last_id;
    }

    public function get_subject_by_id($id) {
      $query = $this->db->select('id, IF(TRIM(IFNULL(subject_code, "")) = "", "-", TRIM(subject_code)) AS subject_code, short_name, subject_name, mapping_string, status')
                  ->from('subject_master')
                  ->where('id', $id)
                  ->get()->row();
      return $query;
    }

    public function get_subject_list() {
      return $this->db->select('id, IF(TRIM(IFNULL(subject_code, "")) = "", "-", TRIM(subject_code)) AS subject_code, short_name, subject_name, mapping_string, status')->order_by('subject_name', 'asc')->get('subject_master')->result();
    }

    public function get_subjectList() {
      $sub_list = $this->db->select('id, subject_code, short_name, subject_name, mapping_string, status')
      ->from('subject_master sm')
      ->order_by('subject_name', 'asc')
      ->where('sm.id not in (select subject_master_id from elective_master_group_subjects)')
      ->get()->result();
      return $sub_list;
    }

    public function delete_subject($subject_id) {
      $is_deletable= $this->db->where('subject_master_id', $subject_id)->get('lp_subjects')->result();
      if(!empty($is_deletable)) {
        return -1;
      }
      return $this->db->where('id', $subject_id)->delete('subject_master');
    }
}