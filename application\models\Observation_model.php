<?php
  defined('BASEPATH') OR exit('No direct script access allowed');
            
class Observation_model extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
        date_default_timezone_set('Asia/Kolkata');
    }

    public function getObservedData($staffId=0){
        $CURRENT_DATE = date('Y-m-d');
        $this->db->select("so.std_id,so.staff_id,so.created_on,CONCAT(ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) AS stdName,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName, sc.class_name, sc.section_name, so.id, so.observation, sy.class_id, sy.class_section_id, ay.acad_year");
        $this->db->from('student_admission std');
        $this->db->join('student_observation so', "so.std_id=std.id and so.acad_year_id=$this->yearId");
        $this->db->join('student_year sy', "std.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
        $this->db->join('academic_year ay','sy.acad_year_id=ay.id');
        $this->db->where('date_format(so.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("'.$CURRENT_DATE.'", INTERVAL -7 DAY),"%Y-%m-%d")');
        $this->db->join('class_section sc', 'sc.id=sy.class_section_id','left');

        $this->db->order_by('so.id','desc');

        $this->db->join('staff_master sm', 'sm.id=so.staff_id');
        if($staffId) {
            $this->db->where('so.staff_id', $staffId);
        }
        $result = $this->db->get()->result();
        // echo "<pre>"; print_r($result); die();  
        return $result;
    }

    public function get_classwiseSection($class_id){
        return $this->db->select('*')->from('class_section')->where('class_id',$class_id)->get()->result();
    }

    public function get_studentclassSectionwise($section_id) {
      $prefix_student_name = $this->settings->getSetting('prefix_student_name');
      if ($prefix_student_name == "roll_number") {
        $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
      } else if ($prefix_student_name == "enrollment_number") {
        $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
      } else if ($prefix_student_name == "admission_number") {
        $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
      } else if ($prefix_student_name == "registration_no") {
        $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
      } else if ($prefix_student_name == "alpha_rollnum") {
          $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
      }else {
        $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
      }
      $prefix_order_by = $this->settings->getSetting('prefix_order_by');
      $order_by = 'std.first_name';
      if ($prefix_order_by == "roll_number") {
        $order_by = 'sy.roll_no';
      }else if($prefix_order_by == "enrollment_number"){
        $order_by = 'std.enrollment_number';
      }else if($prefix_order_by == "admission_number"){
        $order_by = 'std.admission_no';
      }else if($prefix_order_by == "alpha_rollnum"){
        $order_by = 'sy.alpha_rollnum';
      }
      

        $this->db->select("std.id, $std_name");
        $this->db->from('student_admission std');
        $this->db->join('student_year sy', "std.id=sy.student_admission_id");
        $this->db->where('admission_status','2');
        $this->db->where('sy.promotion_status!=', '4');
        $this->db->where('sy.promotion_status!=', '5');
        $this->db->where_in('sy.class_section_id',$section_id);
        $this->db->order_by("$order_by");
        return $this->db->get()->result();
    }

    public function submitObservation(){
        $data = array(
            'std_id' => $this->input->post('student_name'),
            'staff_id' => $this->authorization->getAvatarStakeHolderId(),
            'observation' => $this->input->post('observation'),
            'last_modified_by' => $this->authorization->getAvatarId(),
            'acad_year_id' => $this->yearId
        );
        return $this->db->insert('student_observation', $data);
    }

    public function deleteObservation($id){
        $this->db->where('id', $id);
        return $this->db->delete('student_observation');
    }

    public function getObservationData($id){
        $this->db->select("so.std_id, CONCAT(ifnull(std_adm.first_name,''),' ', ifnull(std_adm.last_name,'')) as std_name, cs.class_name, cs.section_name, so.id, so.observation, sy.class_id, sy.class_section_id");
        $this->db->from('student_admission std_adm');
        $this->db->join("student_year sy", "std_adm.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
        $this->db->join('student_observation so', 'so.std_id=std_adm.id');
        $this->db->join('class_section cs', 'cs.id=sy.class_section_id');
        $this->db->where('so.id', $id);
        $result = $this->db->get()->row();
        // echo $this->db->last_query();die();

        return $result;
    }

    public function observations($stdId){
        $this->db->select("so.std_id,so.staff_id,so.created_on, CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName, cl.class_name, sc.section_name, so.id, so.observation, ss.class_id, ss.class_section_id");
        $this->db->from('student_admission sd');
        $this->db->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db->where('ss.acad_year_id',$this->yearId);
        // $this->db->from('student std');
        $this->db->join('student_observation so', 'so.std_id=sd.id');
        $this->db->join('class cl', 'cl.id=ss.class_id');
        $this->db->join('class_section sc', 'sc.id=ss.class_section_id');
        $this->db->join('staff_master sm', 'sm.id=so.staff_id');
        $this->db->where('so.std_id', $stdId);
        $this->db->order_by('so.staff_id');
        return $this->db->get()->result();
    }

    public function getObservedDataByClass($staffId=0, $class, $section){
        $this->db->select("so.std_id,so.staff_id,so.created_on,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) AS stdName,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName, cl.class_name, sc.section_name, so.id, so.observation, sy.class_id, sy.class_section_id");
        $this->db->from('student_admission sa');
        $this->db->join('student_observation so', "so.std_id=sa.id and so.acad_year_id=$this->yearId");
        $this->db->join('student_year sy',"sa.id=sy.student_admission_id and sy.class_id=$class and sy.class_section_id=$section and sy.acad_year_id=$this->yearId");
        $this->db->join('class cl', "cl.id=sy.class_id");
        $this->db->join('class_section sc', 'sc.id=sy.class_section_id');
        $this->db->join('staff_master sm', 'sm.id=so.staff_id');
        // $this->db->where("sy.class_id", $class);
        // $this->db->where("sy.class_section_id", $section);
        $this->db->order_by('so.created_on', 'DESC');
        if($staffId) {
            $this->db->where('so.staff_id', $staffId);
        }
        $result = $this->db->get()->result();

        // echo '<pre>';print_r($this->db->last_query());die();

        return $result;
    }

    public function updateObservation($id){
        $data = array(
            'observation' => $this->input->post('observation'),
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        $this->db->where('id', $id);
        return $this->db->update('student_observation', $data);
    }

    public function getClassList() {
        $this->db->select('id, class_name');
        return $this->db->get('class')->result();
    }

    public function getClassWiseCount($staff_id = 0) {
        $this->db->select("cl.id, COUNT(cl.id) AS students");
        $this->db->from('student std');
        $this->db->join('student_observation so', 'so.std_id=std.id');
        $this->db->join('class cl', 'cl.id=std.class_id');
        if($staff_id)
            $this->db->where('so.staff_id', $staff_id);
        $this->db->group_by('cl.id');
        //echo "<pre>";print_r($this->db->get()->result()); die();
        return $this->db->get()->result();
    }

    public function filterObservedData($staffId=0){
        $from_date = $this->input->get('from_date');
        $to_date = $this->input->get('to_date');
        if(empty($from_date)){
            $from_date = "2000-01-01";
        }

        $this->db->select("so.std_id, so.staff_id, so.created_on, CONCAT(ifnull(s_adm.first_name,''),' ', ifnull(s_adm.last_name,'')) as stdName,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName, cs.class_name, cs.section_name, so.id, so.observation, cs.class_id, cs.id as sectionId, ay.acad_year");
        $this->db->from('student_observation so');
        $this->db->join('student_admission s_adm', 'so.std_id=s_adm.id');
        $this->db->join('student_year sy', "s_adm.id=sy.student_admission_id");
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->where("DATE_FORMAT(so.created_on,'%Y-%m-%d') >= DATE_FORMAT('".$from_date."','%Y-%m-%d')");
        $this->db->where("DATE_FORMAT(so.created_on,'%Y-%m-%d') <= DATE_FORMAT('".$to_date."','%Y-%m-%d')");
        $this->db->join('academic_year ay','so.acad_year_id=ay.id');
        $this->db->join('staff_master sm', 'so.staff_id=sm.id');
        $this->db->where('so.acad_year_id', $this->yearId);
        $this->db->order_by('so.id', 'desc');
        if($staffId) {
            $this->db->where('so.staff_id', $staffId);
        }
        
        return $this->db->get()->result();
    }

    public function add_capacity($capacity_name){
        $capacity_data = array(
            'name'=>$capacity_name
        );
        return $this->db->insert('sov2_staff_capacity',$capacity_data);    
    }

    public function get_capacity_types(){
        $sql="select * from sov2_staff_capacity";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
        
    }

    public function delete_capacity($capacity_id){
        $this->db->where('id',$capacity_id);
        return $this->db->delete('sov2_staff_capacity');
    }

    public function add_category($category_name, $category_color,$remarks){
        $category_data = array(
            'name'=>$category_name,
            'color_code'=>$category_color,
            'default_remarks'=>$remarks
        );
        return $this->db->insert('sov2_category',$category_data);   
    }

    public function edit_category($category_name, $category_color,$category_id,$remarks_edit){
        $category_data = array(
            'name'=>$category_name,
            'color_code'=>$category_color,
            'default_remarks'=>$remarks_edit
        );
        return $this->db->where('id', $category_id)->update('sov2_category', $category_data);
    }

    public function get_category_types(){
        $sql="select * from sov2_category";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
        
    }

    public function delete_category($category_id){
        $this->db->where('id',$category_id);
        return $this->db->delete('sov2_category');
    }

    public function getClassSectionNames(){
  
        $this->db_readonly->select("cs.id, cm.id, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name");
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
        $this->db_readonly->join('class_master cm', "cm.class_name = cs.class_name");
        $this->db_readonly->order_by('c.id, cs.id');
        $this->db_readonly->where('c.is_placeholder!=1');

        $result = $this->db_readonly->get()->result();
        
        return $result;
    }
    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    
    public function add_single_student_observation($class_section_id,$student_id,$category_id,$staff_capacity_id,$observation,$observation_date, $action_taken,$file_input){
        $observation_data=[];
        foreach($student_id as $student){
            $observation_data[] = array(
                'std_admission_id'=>$student,
                'class_section_id'=>$class_section_id,
                'category_id'=>$category_id,
                'staff_capacity_id'=>$staff_capacity_id,
                'observation'=>$observation,
                'acad_year_id'=>$this->yearId,
                'observation_date'=>$observation_date,
                'staff_id'=>$this->authorization->getAvatarStakeHolderId(),
                'action_taken'=>$action_taken,
                'file_input'=>$file_input['file_name']
                // 'created_on'=>$this->Kolkata_datetime()
            );
        }
        
        return $this->db->insert_batch('sov2_observations',$observation_data);   
    }

    public function get_single_staff_observation_data($class_section_id,$from_date,$to_date){

        // if($class_section_id=='0'){
        //     $staff_id = $this->authorization->getAvatarStakeHolderId();
        //     $sql = "select so.id, so.std_admission_id, CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name, so.class_section_id, so.observation, so.status, sc.color_code,
        //     CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(so.observation_date,'%d-%m-%Y') as observation_date, so.created_on, so.tags, ifnull(sc.name, '-') as category 
        //     from sov2_observations so
        //     left join sov2_category sc on so.category_id = sc.id
        //     join class_section cs on cs.id = so.class_section_id
        //     join student_admission std on std.id = so.std_admission_id
        //     where so.staff_id='$staff_id' and so.observation_date>='$from_date' and so.observation_date<='$to_date' and so.acad_year_id='$this->yearId'
        //     order by so.created_on desc";
        // }
        // else{
            $staff_id = $this->authorization->getAvatarStakeHolderId();

            $prefix_student_name = $this->settings->getSetting('prefix_student_name');

            if ($prefix_student_name == "roll_number") {
                $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "enrollment_number") {
                $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "admission_number") {
                $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "registration_no") {
                $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "alpha_rollnum") {
                  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              }else {
                $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
              }

              $prefix_order_by = $this->settings->getSetting('prefix_order_by');
              $order_by = 'std.first_name';
              if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
              }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'std.enrollment_number';
              }else if($prefix_order_by == "admission_number"){
                $order_by = 'std.admission_no';
              }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
              }
              
            $sql = "select DISTINCT so.id, so.std_admission_id, $std_name, so.class_section_id, so.observation, so.status, sc.color_code,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y %h:%i %p') as observation_date, so.created_on, so.tags, ifnull(sc.name, '-') as category, so.action_taken,so.file_input ,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_category sc on so.category_id = sc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            where so.staff_id='$staff_id' and date(so.observation_date)>='$from_date' and date(so.observation_date)<='$to_date' and sy.acad_year_id='$this->yearId' ";

            if(!empty($class_section_id)){
                $sql.=" and so.class_section_id in (". implode(', ',$class_section_id). ") ";
            }

            $sql.=" ORDER BY cs.class_name, cs.section_name, $order_by";
        // }

        
        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        
        // echo "<pre>";print_r($this->db->last_query()); die();
        
        return $observation_data;
    }

    public function getClassNames(){
  
        $this->db_readonly->select("cs.id, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name");
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
        // $this->db_readonly->join('class_master cm', "cm.class_name = cs.class_name");
        $this->db_readonly->order_by('c.id, cs.id');
        $this->db_readonly->where('cs.is_placeholder!=1');

        $result = $this->db_readonly->get()->result();
        
        return $result;
    }

    public function get_observation_report($class_section_id,$from_date,$to_date,$category_id){

        //echo "<pre>";print_r($class_section_id); die();

        // For All sections consider 0.
        // if($class_section_id=='0'){
        //     $sql = "select so.id, so.std_admission_id, CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name, so.class_section_id, so.observation,
        //     CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(so.observation_date,'%d-%m-%Y') as observation_date, 
        //     date_format(so.created_on,'%d-%m-%Y %H:%i') as created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity , so.status, sc.color_code, ifnull(so.action_taken, '-') as action_taken,
        //     case
        //         when so.staff_id = 0 then 'Super Admin'
        //         when so.staff_id is NULL then 'NA'
        //         else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
        //     end as staff_name
        //     from sov2_observations so
        //     left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
        //     left join sov2_category sc on so.category_id = sc.id
        //     join class_section cs on cs.id = so.class_section_id
        //     join student_admission std on std.id = so.std_admission_id
        //     left join staff_master sm on so.staff_id = sm.id
        //     where so.observation_date>='$from_date' and so.observation_date<='$to_date' and so.acad_year_id='$this->yearId' and so.status='Active'";
        // }
        // else{

            
            $prefix_student_name = $this->settings->getSetting('prefix_student_name');

            if ($prefix_student_name == "roll_number") {
                $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "enrollment_number") {
                $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "admission_number") {
                $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "registration_no") {
                $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "alpha_rollnum") {
                  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              }else {
                $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
              }
              $prefix_order_by = $this->settings->getSetting('prefix_order_by');
              $order_by = 'std.first_name';
              if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
              }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'std.enrollment_number';
              }else if($prefix_order_by == "admission_number"){
                $order_by = 'std.admission_no';
              }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
              }
            $sql = "select DISTINCT so.id, so.std_admission_id,$std_name , so.class_section_id, so.observation,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y %h:%i %p') as observation_date, 
            so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, sc.color_code,
            case
                when so.staff_id = 0 then 'Super Admin'
                when so.staff_id is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as staff_name,so.action_taken,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_category sc on so.category_id = sc.id
            left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            left join staff_master sm on so.staff_id = sm.id
            where date(so.observation_date)>='$from_date' and date(so.observation_date)<='$to_date' and sy.acad_year_id='$this->yearId' ";
            if(!empty($class_section_id)){
                $sql.=" and so.class_section_id in (". implode(', ',$class_section_id). ") ";
            }
            if(!empty($category_id)){
                $sql.=" and so.category_id = '$category_id' ";
            }  
            $sql.=" and so.status='Active' ORDER BY cs.class_name, cs.section_name, $order_by";
        // }

        

        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        // echo "<pre>";print_r($this->db->last_query()); die(); 

        return $observation_data;
    }

    public function get_student_observation_report($student_id,$acad_year,$class_section_id){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          }else {
            $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
          }

          $prefix_order_by = $this->settings->getSetting('prefix_order_by');
          $order_by = 'std.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'std.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'std.admission_no';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }

        $sql = "select Distinct so.id, so.acad_year_id, so.std_admission_id,$std_name , so.class_section_id, so.observation,
        CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y  %h:%i %p') as observation_date, so.action_taken,
        so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, sc.color_code,
        case
            when so.staff_id = 0 then 'Super Admin'
            when so.staff_id is NULL then 'NA'
            else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
        end as staff_name,std.first_name,sy.roll_no,ifnull(std.enrollment_number,'-') as enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
        from sov2_observations so
        left join sov2_category sc on so.category_id = sc.id
        left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
        join class_section cs on cs.id = so.class_section_id
        join student_admission std on std.id = so.std_admission_id
        join student_year sy on sy.student_admission_id=so.std_admission_id
        left join staff_master sm on so.staff_id = sm.id
        where sy.acad_year_id='$acad_year' and so.std_admission_id = '$student_id' and so.status='Active' ";
        if(!empty($class_section_id)){
          $sql.=" and so.class_section_id = $class_section_id ";
      }
       $sql.=" ORDER BY cs.class_name, cs.section_name, $order_by";
        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        return $observation_data;
    }

    public function get_section_observation_report($from_date,$to_date){
        $sql="select cs.id, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name from class_section cs
        join class c on c.id=cs.class_id
        where c.acad_year_id = '$this->yearId' and cs.is_placeholder!='1'";

        $class_section = $this->db->query($sql)->result();
        $sql="select * from sov2_category order by name";
        $categories = $this->db->query($sql)->result();

        foreach($class_section as $item){
            foreach($categories as $cat){
                
                $class_id = $item->id;
                $category_name = $cat->name;
                $category_id = $cat->id;
                $item->$category_name = '0';
                $sql = "select count(id) as count from sov2_observations 
                where class_section_id = '$class_id' and category_id = '$category_id' 
                and status='Active' and date(observation_date)>='$from_date' and date(observation_date)<='$to_date'";
                $item->$category_name = $this->db->query($sql)->row('count');
            }
        }

        //echo "<pre>";print_r($class_section); die();
    

        return $class_section;
    }

    public function get_observation_categories(){
        $sql="select * from sov2_category
        order by name";
        $categories = $this->db->query($sql)->result();

        //echo "<pre>";print_r($categories); die();

        return $categories;
    }

    public function deactivate_observation($observation_id,$reason){

        $data = array(
            'deactived_by'=>$this->authorization->getAvatarStakeHolderId(),
            'deactived_on'=>date("Y-m-d H:i:s"),
            'status'=>'Inactive',
            'deactivated_reason'=>$reason,
        );
    
        $this->db->where('id',$observation_id);
        return $this->db->update('sov2_observations',$data);
    }

    public function get_all_observation($class_section_id,$from_date,$to_date){

        // echo "<pre>";print_r($class_section_id); die();

        // For All sections consider 0.
        // if($class_section_id=='0'){
        //     $sql = "select so.id, so.std_admission_id, CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name, so.class_section_id, so.observation,
        //     CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(so.observation_date,'%d-%m-%Y') as observation_date, 
        //     date_format(so.created_on,'%d-%m-%Y %H:%i') as created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, sc.color_code,
        //     case
        //         when so.staff_id = 0 then 'Super Admin'
        //         when so.staff_id is NULL then 'NA'
        //         else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
        //     end as staff_name
        //     from sov2_observations so
        //     left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
        //     left join sov2_category sc on so.category_id = sc.id
        //     join class_section cs on cs.id = so.class_section_id
        //     join student_admission std on std.id = so.std_admission_id
        //     left join staff_master sm on so.staff_id = sm.id
        //     where so.observation_date>='$from_date' and so.observation_date<='$to_date' and so.acad_year_id='$this->yearId'";
        // }
        // else{
            
            $prefix_student_name = $this->settings->getSetting('prefix_student_name');
            if ($prefix_student_name == "roll_number") {
                $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "enrollment_number") {
                $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "admission_number") {
                $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "registration_no") {
                $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "alpha_rollnum") {
                  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              }else {
                $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
              }

              $prefix_order_by = $this->settings->getSetting('prefix_order_by');
              $order_by = 'std.first_name';
              if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
              }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'std.enrollment_number';
              }else if($prefix_order_by == "admission_number"){
                $order_by = 'std.admission_no';
              }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
              }
              
            $sql = "select distinct so.id, so.std_admission_id, $std_name, so.class_section_id, so.observation,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y %h:%i %p') as observation_date, so.action_taken,
            so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, sc.color_code,
            case
                when so.staff_id = 0 then 'Super Admin'
                when so.staff_id is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as staff_name,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_category sc on so.category_id = sc.id
            left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            left join staff_master sm on so.staff_id = sm.id
            where date(so.observation_date)>='$from_date' and date(so.observation_date)<='$to_date' and sy.acad_year_id='$this->yearId' ";
            if(!empty($class_section_id)){
                $sql.=" and so.class_section_id in (". implode(', ',$class_section_id). ") ";
            } 
            $sql.="ORDER BY cs.class_name, cs.section_name, $order_by";
        // }

        

        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        //echo "<pre>";print_r($observation_data); die();

        return $observation_data;
    }

    public function get_deactivated_observation_report($class_section_id,$from_date,$to_date){

        // echo "<pre>";print_r(implode($class_section_id)); die();

        // For All sections consider 0.
        if(implode($class_section_id)=='0'){
            $prefix_student_name = $this->settings->getSetting('prefix_student_name');
            if ($prefix_student_name == "roll_number") {
                $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "enrollment_number") {
                $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "admission_number") {
                $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "registration_no") {
                $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "alpha_rollnum") {
                  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              }else {
                $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
              }

              $prefix_order_by = $this->settings->getSetting('prefix_order_by');
              $order_by = 'std.first_name';
              if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
              }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'std.enrollment_number';
              }else if($prefix_order_by == "admission_number"){
                $order_by = 'std.admission_no';
              }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
              }

            $sql = "select distinct so.id, so.std_admission_id, $std_name, so.class_section_id, so.observation,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y %h:%i %p') as observation_date, so.action_taken,
            so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, so.deactivated_reason,
            date_format(so.deactived_on,'%d-%b-%Y %h:%i %p') as deactived_on, sc.color_code,
            case
                when so.deactived_by = 0 then 'Super Admin'
                when so.deactived_by is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as deactived_by,
            case
                when so.staff_id = 0 then 'Super Admin'
                when so.staff_id is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as staff_name,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
            left join sov2_category sc on so.category_id = sc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            left join staff_master sm on so.staff_id = sm.id
            where date(so.observation_date)>='$from_date' and date(so.observation_date)<='$to_date' and sy.acad_year_id='$this->yearId' and so.status!='Active' 
            ORDER BY cs.class_name, cs.section_name, $order_by";
        }
        else{
            $prefix_student_name = $this->settings->getSetting('prefix_student_name');
            if ($prefix_student_name == "roll_number") {
                $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "enrollment_number") {
                $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "admission_number") {
                $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "registration_no") {
                $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              } else if ($prefix_student_name == "alpha_rollnum") {
                  $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
              }else {
                $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
              }

              $prefix_order_by = $this->settings->getSetting('prefix_order_by');
              $order_by = 'std.first_name';
              if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
              }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'std.enrollment_number';
              }else if($prefix_order_by == "admission_number"){
                $order_by = 'std.admission_no';
              }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
              }
              
            $sql = "select Distinct so.id, so.std_admission_id, $std_name, so.class_section_id, so.observation,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%m-%Y %h:%i %p') as observation_date, 
            so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, so.deactivated_reason,
            date_format(so.deactived_on,'%d-%m-%Y %H:%i') as deactived_on,
            case
                when so.deactived_by = 0 then 'Super Admin'
                when so.deactived_by is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as deactived_by,
            case
                when so.staff_id = 0 then 'Super Admin'
                when so.staff_id is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as staff_name,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_category sc on so.category_id = sc.id
            left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            left join staff_master sm on so.staff_id = sm.id
            where date(so.observation_date)>='$from_date' and date(so.observation_date)<='$to_date' and sy.acad_year_id='$this->yearId' and so.class_section_id in (". implode(', ',$class_section_id). ") and so.status!='Active'
            ORDER BY cs.class_name, cs.section_name, $order_by";
        }

        

        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        // echo "<pre>";print_r($this->db->last_query()); die();

        return $observation_data;
    }

    public function get_section_category_observation($class_section_id,$category_id,$from_date,$to_date){
        // echo $from_date;"<br>";
        // echo $to_date; die();
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          }else {
            $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
          }

          $prefix_order_by = $this->settings->getSetting('prefix_order_by');
          $order_by = 'std.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'std.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'std.admission_no';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }
          
        $sql = "select Distinct so.id, so.std_admission_id, $std_name, so.class_section_id, so.observation,
            CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_, date_format(so.observation_date,'%d-%b-%Y %h:%i %p') as observation_date, so.action_taken,
            so.created_on, so.tags, ifnull(sc.name, '-') as category, ifnull(ssc.name, '-') as capacity, so.status, 
            case
                when so.staff_id = 0 then 'Super Admin'
                when so.staff_id is NULL then 'NA'
                else concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,''))
            end as staff_name,std.first_name,sy.roll_no,std.enrollment_number,std.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name
            from sov2_observations so
            left join sov2_category sc on so.category_id = sc.id
            left join sov2_staff_capacity ssc on so.staff_capacity_id = ssc.id
            join class_section cs on cs.id = so.class_section_id
            join student_admission std on std.id = so.std_admission_id
            join student_year sy on sy.student_admission_id=std.id
            left join staff_master sm on so.staff_id = sm.id
            where so.class_section_id = '$class_section_id' and so.category_id='$category_id' and so.status='Active' and sy.acad_year_id='$this->yearId' and date(observation_date) between '$from_date' and '$to_date' 
            ORDER BY cs.class_name, cs.section_name, $order_by ";

        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        //echo "<pre>";print_r($observation_data); die();

        return $observation_data;
    }

    public function get_staff_observation_data($from_date,$to_date){

        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $sql = "select so.id, so.std_admission_id, CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name, so.class_section_id, so.observation, so.status,
        CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(so.observation_date,'%d-%m-%Y %h:%i %p') as observation_date,so.created_on, so.tags, ifnull(sc.name, '-') as category
        from sov2_observations so
        left join sov2_category sc on so.category_id = sc.id
        join class_section cs on cs.id = so.class_section_id
        join student_admission std on std.id = so.std_admission_id
        where so.staff_id='$staff_id' and so.observation_date>='$from_date' and so.observation_date<='$to_date' and so.acad_year_id='$this->yearId'
        order by so.created_on desc";

        $observation_data = $this->db->query($sql)->result();
        foreach ($observation_data as $key => $value) {
            $value->created_on = local_time($value->created_on, 'd-M-Y h:i a');
        }
        //echo "<pre>";print_r($observation_data); die();

        return $observation_data;
    }

    public function getAllStudentNames(){
        $sql = "select sa.id, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name from student_admission sa
        join student_year sy on sy.student_admission_id=sa.id
        where admission_status ='2'and sy.acad_year_id=$this->yearId";
        return $this->db_readonly->query($sql)->result();
    }

    public function getAcadYearList(){
        $sql = "select id,acad_year from academic_year where year_to_show ='1'";
        return $this->db_readonly->query($sql)->result();
    }

    public function fill_remarks($input){
        $this->db_readonly->select('id,category_id,name as default_remarks');
        $this->db_readonly->where('category_id',$input['id']);
        $this->db_readonly->from('sov2_pre_defined_remarks');
        return $this->db_readonly->get()->result();
    }

    public function add_predefined_remarks($remarks,$category_id){
        $remarks_data = array(
            'name'=>$remarks,
            'category_id'=>$category_id
        );
        return $this->db->insert('sov2_pre_defined_remarks',$remarks_data); 
    }

    public function get_predefined_remakes(){
        $this->db_readonly->select('pdr.id,pdr.name as remark_name,c.name as cat_name');
        $this->db_readonly->from('sov2_pre_defined_remarks pdr');
        $this->db_readonly->join('sov2_category c','pdr.category_id=c.id');
        return $this->db_readonly->get()->result();
        
    }

    public function delete_remarks($remarks_id){
        $this->db->where('id',$remarks_id);
        return $this->db->delete('sov2_pre_defined_remarks');
    }

    public function get_mass_observation_data($input){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name";
          } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name";
          }else {
            $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS std_name";
          }

          $prefix_order_by = $this->settings->getSetting('prefix_order_by');
          $order_by = 'sa.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sa.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'sa.admission_no';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }
        $this->db_readonly->select("sa.id,sa.admission_no,sa.first_name, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name_,$std_name,sa.first_name,sy.roll_no,sa.enrollment_number,sa.admission_no,sy.alpha_rollnum,cs.section_name,cs.class_name");
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id');
        $this->db_readonly->join('class_section cs','cs.id= sy.class_section_id');
        $this->db_readonly->join('class c','c.id=cs.class_id');
        $this->db_readonly->where('sy.acad_year_id',$this->yearId);
        $this->db_readonly->where('sa.admission_status','2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where('sy.class_section_id',$input['class_section_id']);
        $this->db_readonly->order_by('cs.class_name, cs.section_name,'.$order_by);
        $result['data']= $this->db_readonly->get()->result();

        $this->db_readonly->select('*');
        $this->db_readonly->from('sov2_pre_defined_remarks');
        $this->db_readonly->where('category_id',$input['category_type_id']);

        $result['category']= $this->db_readonly->get()->result();

        return $result;
    }

    public function add_mass_observation_data($input){
        $observation_data=[];
        foreach($input as $student){
            if(is_array($student['observation'])){
                foreach($student['observation'] as $observation){
                    $observation_data[] = array(
                        'std_admission_id'=>$student['admission_no'],
                        'class_section_id'=>$student['class_section_id'],
                        'category_id'=>$student['category_type_id'],
                        'staff_capacity_id'=>$student['capacity_type_id'],
                        'observation'=>$observation,
                        'acad_year_id'=>$this->yearId,
                        'observation_date'=>$this->Kolkata_datetime(),
                        'staff_id'=>$this->authorization->getAvatarStakeHolderId()
                    );
                }
            }else{
                $observation_data[] = array(
                    'std_admission_id'=>$student['admission_no'],
                    'class_section_id'=>$student['class_section_id'],
                    'category_id'=>$student['category_type_id'],
                    'staff_capacity_id'=>$student['capacity_type_id'],
                    'observation'=>$student['observation'],
                    'acad_year_id'=>$this->yearId,
                    'observation_date'=>$this->Kolkata_datetime(),
                    'staff_id'=>$this->authorization->getAvatarStakeHolderId()
                );
            }
        }
        
        return $this->db->insert_batch('sov2_observations',$observation_data);   
    }
}