<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Account-level summary</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin:0px">
        <div class="col-md-6 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Account-level summary
          </h3>
        </div>
      </div>
    </div>

    <div class="card-body">
      <form class="form-horizontal" style="margin-top: 20px;">
        <div class="form-group" style="margin-left: 0;">
          <label for="from_date" class="control-label" style="margin-bottom: 8px; margin-left: 3px;">Date</label>
          <div style="display: flex; align-items: center; gap: 10px;">
            <div class="input-group date" id="datePicker" style="max-width: 300px;">
              <input class="form-control" value="<?php echo date('d-m-Y') ?>" autocomplete="off" type="text" id="from_date" name="from_date" placeholder="Select Date">
              <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
            </div>
            <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
          </div>
        </div>
      </form>

      <div class="row" style="margin-top: 20px; margin-bottom: 10px;">
        <div class="col-md-12 text-right" id="exportButtons" style="display: none;">
          <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="printProfile()">
            <span class="fa fa-print" aria-hidden="true"></span> Print
          </button>
          <button class="btn btn-info" style="margin-left:3px; border-radius: 8px !important;" onclick="exportToExcel_daily()">
            <span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel
          </button>
        </div>
      </div>

      <div id="printArea">
        <div class="text-center mt-2">
          <div id="progress" class="progress" style="display: none; height: 8px; background-color: #e0e0e0;">
            <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%; height: 100%; background-color: #007bff;">
            </div>
          </div>
        </div>
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Account-level summary</h4>
          <h5>From <span id="fromDate"></span></h5>
        </div>

        <div class="panel-body day_book_account table-responsive hidden-xs">
          <div id="loader" class="loaderclass" style="display:none;"></div>
          <h3>Select Date to get report</h3>
        </div>

        <div class="panel-body day_book_account_sales table-responsive hidden-xs">
          <div id="loader" class="loaderclass" style="display:none;"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  $(document).ready(function() {
    $('.date').datetimepicker({
      maxDate: "now",
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });

    $('#search').on('click',function(){
      $("#progress").show(); 
      $("#exportButtons").show();
      $(this).prop('disabled', true).val('Please wait...'); 
      var from_date = $('#from_date').val();
      $('#fromDate').html(from_date);
      $.ajax({
        url: '<?php echo site_url('feesv2/reports/generate_report_for_day_book_account'); ?>',
        type: 'post',
        data: {'from_date':from_date},
        success: function(data) {
          var rData =JSON.parse(data);
          console.log(rData);
          $(".day_book_account").html(rData.fees);
          $("#progress").hide();
          $('#search').prop('disabled', false).val('Get Report');     
          // $(".day_book_account_sales").html(rData.sales);    
        }
      });
    });
  });

</script>

<style type="text/css">
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table {
    font-family: 'Poppins', sans-serif !important;
  }
  

  .card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 20px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }

  table th,
  table td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
  }

  table tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  table tbody tr:hover {
    background-color: #f1f5f9;
  }

  table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .btn-primary:hover,
  .btn-primary:focus {
    background-color: #0069d9;
    border-color: #0062cc;
  }
</style>

<script>
  function printProfile() {
    const printVisible = document.getElementById('print_visible')?.outerHTML || '';
    const mainContent = document.querySelector('.day_book_account')?.outerHTML || '';
    const salesContent = document.querySelector('.day_book_account_sales')?.outerHTML || '';

    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <html>
        <head>
            <title>Account-level Summary</title>
            <style>
                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                h3, h4, h5 {
                    margin: 5px 0;
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    font-size: 11px;
                    background-color: #ffffff;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px 14px;
                    text-align: left;
                }
                thead th {
                    background-color: #f1f5f9;
                    color: #111827;
                    font-weight: 500;
                }
                #print_visible {
                    display: block !important;
                }
                @media print {
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            ${printVisible}
            ${mainContent}
            ${salesContent}
            <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
  }



  function exportToExcel_daily(){
      var htmls = "";
      var uri = 'data:application/vnd.ms-excel;base64,';
      var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
      var base64 = function(s) {
          return window.btoa(unescape(encodeURIComponent(s)))
      };

      var format = function(s, c) {
          return s.replace(/{(\w+)}/g, function(m, p) {
              return c[p];
          })
      };

      var summaryTable = $("#print_visible").html();
      var mainTable = $(".day_book_account").html();
      var salesAccTable = $(".day_book_account_sales").html();

      htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable + '<br><br>' + salesAccTable;

      var ctx = {
          worksheet : 'Spreadsheet',
          table : htmls
      }


      var link = document.createElement("a");
      link.download = "export.xls";
      link.href = uri + base64(format(template, ctx));
      link.click();

  }
</script>