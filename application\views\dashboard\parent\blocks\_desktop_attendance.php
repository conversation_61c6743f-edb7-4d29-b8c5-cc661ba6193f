
<div class="card" style="border-radius: 16px;height: 33rem">
    <div class="card-body profile">
        <?php 
          $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/female-graduate-student.png';
          $gender = 'Female';
          if($studentData->gender == 'M'){
            $picUrl = $this->config->item("s3_base_url").'/nextelement-common/Parent App Icons 48px/graduate-student-avatar.png';
            $gender = 'Male';
        }?>
        
        <div class="profile-image">
            <img src="<?php echo (empty($studentData->picture_url)) ? $picUrl :  $this->filemanager->getFilePath($studentData->picture_url); ?>" alt="photo" style="height:75px">
        </div>
        <div class="profile-data">
            <div style="color: #000; font-size: 14px;" class="profile-data-name"><?= $studentData->stdName ?></div>
            <div style="color: #000; font-size: 14px;" class="profile-data-title">Class / Section : <?= $studentData->className .'/'.$studentData->sectionName ?>
                <a style="color: #fff" href="<?php echo site_url('dashboard') ?>" class="panel-refresh pull-right">
                    <span class="fa fa-refresh"></span>
                </a>
            </div>
        </div>
    </div>
    <div class="card-body list-group p-4">
        <a href="#" class="list-group-item"><span class="fa fa-calendar"></span> Year <span class="pull-right"><?php echo $att_v2_data['academic_year']?></span></a>
        <a href="#" class="list-group-item"><span class="fa fa-check-circle-o"></span> Present Days <span class="pull-right"><?php echo $att_v2_data['present_days']?></span></a> 
         <a href="#" class="list-group-item"><span class="fa fa-circle-o"></span> Absence Days <span class="pull-right"> <?php echo $att_v2_data['absence_days']?></span></a> 
        <a href="#" class="list-group-item"><span class="fa fa-sign-out"></span> No. of late arrival days <span class="pull-right"><?php echo $att_v2_data['late_days']?></span></a>
    </div>                            
</div>

<style type="text/css">
    .profile .profile-image img{
            width: 91px;
    }
    .profile .profile-image{
        margin: 0;
    }
    .panel .panel-body{
        padding: 4px;
    }
    .profile{
        background:transparent;
    }
</style>