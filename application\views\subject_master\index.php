<?php $isMobile = $this->mobile_detect->isMobile(); ?>
<?php $modelWidth = $isMobile ? '60%' : '30%'; ?>
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('school/school_menu'); ?>">School Menu</a></li>
    <li class="active">Subject Master</li>
</ul>

<div class="col-md-12">
    <?php if(!$isMobile) { ?>
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px;">
                        <div class="d-flex justify-content-between" style="width:100%;">
                            <h3 class="card-title panel_title_new_style_staff">
                                <a class="back_anchor" href="<?php echo site_url('school/school_menu'); ?>">
                                    <span class="fa fa-arrow-left"></span>
                                </a>
                                Subject Master
                            </h3>
                            <div class="new_circleShape_res" style="background-color:#fe970a;float:right;">
                                <a class="control-primary" style="cursor:pointer;" data-toggle="modal" data-target="#_add_subject_modal" data-mode="create" title="Add New Subject">
                                    <span class="fa fa-plus" style="line-height:3.2rem"></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body pt-0" id="pBody" style="overflow-x:scroll;">
                    <div class="no-data-display" id="displayLoading">Loading...</div>
                    <div class="table-responsive" id="displayTable" style="display:none;">
                        <table class="table" id="subjects_data_table">
                            <thead>
                                <th>#</th>
                                <th>Code</th>
                                <th>Short Name</th>
                                <th>Subject Name</th>
                                <th>Mapping String</th>
                                <th>Status</th>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php } else { ?>
            <div class="card panel_new_style" style="border: none !important;">
                <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;text-align: center;">
                    <h3 class="card-title panel_title_new_style">
                        <strong>Subject Master</strong>
                    </h3>
                    <div class="new_circleShape_res" style="background-color:#fe970a;float:right;">
                        <a class="control-primary" style="cursor:pointer;" data-toggle="modal" data-target="#_add_subject_modal" data-mode="create" title="Add New Subject">
                            <span class="fa fa-plus" style="line-height:3.2rem"></span>
                        </a>
                    </div>
                </div>
                <div class="card-body pt-1 px-1" id="pBody" style="overflow-x:scroll;">
                    <div class="no-data-display" id="displayLoading">Loading...</div>
                    <div class="table-responsive" id="displayTable" style="display:none;">
                        <table class="table" id="subjects_data_table">
                            <thead>
                                <th>#</th>
                                <th>Code</th>
                                <th>Short Name</th>
                                <th>Subject Name</th>
                                <th>Mapping String</th>
                                <th>Actions</th>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php } ?>
</div>

<div id="_add_subject_modal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;border-radius: 8px;width:<?= $modelWidth;?>;margin:auto;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="modalHeader">Add New Subject</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal" onclick="resetForm()">&times;</button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_subject_form">
                    <div class="col-md-12">
                        <input type="hidden" id="mode" name="mode" value='create'>
                        <input type="hidden" id="subject_id" name="subject_id" value=''>
                        <div class="form-group">
                            <label class="control-lable">Subject Name <font style="color:red;">*</font></label>
                            <input required placeholder="Enter Subject Name" id="subject_name" maxlength=50 name="subject_name" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label class="control-lable">Short Name <font style="color:red;">*</font></label>
                            <input required placeholder="Enter Short Name" maxlength=10 id="short_name" name="short_name" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label class="control-lable">Mapping String <font style="color:red;">*</font></label>
                            <input required placeholder="Enter Mapping String" id="mapping_string" maxlength="100" name="mapping_string" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label class="control-lable">Subject Code</label>
                            <input placeholder="Enter Subject Code" id="subject_code" maxlength=10 name="subject_code" type="text" class="form-control">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" data-dismiss="modal" onclick="resetForm()">Close</button>
                <button class="btn btn-primary mt-0" id="add_subject_button" type="button" onclick="create_or_update_subject()">Create</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
.dataTables_length{
    width: 49% !important;
}

#subjects_data_table_filter{
    width: 51% !important;
}

.new_circleShape_res {
    border-radius: 50% !important;
    color: white !important;
    font-size: 18px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
}

/* BootBox Extension*/
.confirmWidth {
    width: 400px;
    margin: 0 auto;
}

.selected {
    background-color: #ceb6b6;
}

.deleted {
    background-color: #ffffff;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script language="javascript">
function resetForm() {
    var form = $('#add_subject_form');
    form[0].reset();
    form.parsley().reset();
}

$(document).ready(function() {
    //Initializing the Subjects data table
    var t = $('#subjects_data_table').DataTable({
        "columnDefs": [{
            "searchable": false,
            "orderable": false,
            "targets": 0
        }],
        "pageLength": 50,
        "order": [
            [4, 'asc']
        ],
        "ordering": false,
    });

    t.on('order.dt search.dt', function() {
        t.column(0, {
            search: 'applied',
            order: 'applied'
        }).nodes().each(function(cell, i) {
            cell.innerHTML = i + 1;
        });
    }).draw();

    //Fetching Subjects data
    _fetch_construct_subjects_table();

    $('#_add_subject_modal').on('show.bs.modal', function(event) {
        var subject_mode = $(event.relatedTarget).data('mode');

        if (!subject_mode || subject_mode == 'create') {
            // console.log('here in create!!!');
            //Initialize all the fields for creating a new subject
            $(this)
                .find("input[type=hidden],input[type=text],textarea")
                .val('')
                .end()
                // .find("input[type=checkbox]")
                // .prop("checked", "")
                .end();

            $(this).find('.modal-body #mode').val('create');

            //Set the title and button accordingly
            $(this).find('.modal-title').text(`Add New Subject`);
            button = $(this).find('.modal-footer #add_subject_button').text('Create');

            //Enable the button in case it is disabled
            $("#add_subject_button").prop('disabled', false);

            return;
        }

        $('#_add_subject_modal').on('hidden.bs.modal', function() {
            _select_row(null, 'unselect');
        });

        //Mode is edit!
        var subject_id = $(event.relatedTarget).data('subjectid');
        var short_name = $(event.relatedTarget).data('short_name');
        var subject_name = $(event.relatedTarget).data('subject_name');
        var subject_code = $(event.relatedTarget).data('subject_code');
        var mapping_string = $(event.relatedTarget).data('mapping_string');

        $(this).find('.modal-body #subject_id').val(subject_id);
        $(this).find('.modal-body #short_name').val(short_name);
        $(this).find('.modal-body #subject_name').val(subject_name);
        $(this).find('.modal-body #subject_code').val(subject_code);

        $(this).find('.modal-body #mapping_string').val(mapping_string);
        $(this).find('.modal-body #mode').val('update');

        //Enable the button in case it is disabled
        $("#add_subject_button").prop('disabled', false);

        //Set the title and button accordingly
        $(this).find('.modal-title').html(`Edit Subject <b>${subject_name}</b>`);
        $(this).find('.modal-footer #add_subject_button').text('Update');
    });
});

function _fetch_construct_subjects_table() {
    $('#displayLoading').show();
    $('#displayTable').hide();
    $.ajax({
        url: '<?php echo site_url('school/Subject_master/subject_list'); ?>',
        type: 'post',
        async: false,
        success: function(data) {
            $('#displayLoading').hide();
            $('#displayTable').show();
            parsed_data = JSON.parse(data);
            if(!parsed_data['subjects'] || parsed_data['subjects'].length == 0){
                $('#displayLoading').html('No Data Found').show();
                $('#displayTable').hide();
                return;
            }
            subject_data = parsed_data['subjects'];
            for (var i = 0; i < subject_data.length; i++) {
                insert_one_subject_row(subject_data[i]);
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function insert_one_subject_row(subject_obj) {
    // console.log(subject_obj);
    var dt = $('#subjects_data_table').DataTable();

    is_activity = subject_obj.is_activity == 0 ? 'No' : 'Yes';
    is_physical = subject_obj.is_physical == 0 ? 'No' : 'Yes';
    is_multisection = subject_obj.is_multisection == 0 ? 'No' : 'Yes';
    is_timetable_only = subject_obj.is_timetable_only == 0 ? 'No' : 'Yes';

    if (subject_obj.display_color) {
        subject_color = `<td style="background-color:${subject_obj.display_color}">${subject_obj.display_color}</td>`;
    } else {
        subject_color = '<td>Not Assigned</td>';
    }
    row = `
        <tr>
            <td>#</td>
            <td>${subject_obj.subject_code}</td>
            <td>${subject_obj.short_name}</td>
            <td>${subject_obj.subject_name}</td>
            <td>${subject_obj.mapping_string}</td>
            <td>
                <a href="#" class="btn btn-info" onclick="_select_row(this, 'select')"  data-toggle="modal" data-target="#_add_subject_modal" data-subjectid="${subject_obj.id}" data-short_name='${subject_obj.short_name}' data-subject_name='${subject_obj.subject_name}' data-subject_code='${subject_obj.subject_code}' data-mapping_string='${subject_obj.mapping_string}' data-mode='edit' title="Edit Subject"><i class='fa fa-edit mr-0'></i></a>
                <a onclick="_delete_subject(this,'${subject_obj.id}')" class="btn btn-danger"title="Delete Subject"><i class='fa fa-trash-o mr-0'></i></a>
            </td>
        </tr>
        `;
    dt.row.add($(row)).draw();
}

var selected_row;

function _select_row(thisobj, action) {
    switch (action) {
        case 'select':
            selected_row = $(thisobj).closest('tr');
            selected_row.addClass('selected');
            break;
        case 'unselect':
            selected_row.removeClass('selected');
            break;
        case 'delete':
            var dt = $('#subjects_data_table').DataTable();
            dt.row('.selected').remove().draw(false);
            break;
    }
}

function _delete_subject(obj, subject_id) {
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!',
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('school/Subject_master/delete'); ?>',
                type: 'post',
                data: {
                    'subject_id': subject_id
                },
                async: false,
                success: function(data) {
                    //Remove table row
                    var data = JSON.parse(data);
                    if (data != '-1' && data != -1) {
                        _select_row(obj, 'delete');
                        //Show alert
                        show_alert('success', 'Subject successfully deleted!');
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'This subject is already used for academics. Contact to your administrator.',
                        });
                        return;
                    }
                },
                error: function(err) {
                    console.log(err);
                    show_alert('error', 'Subject delete failed!');
                }
            });
        }
    });
}

function create_or_update_subject() {
    $("#add_subject_button").prop('disabled', true);

    var form = $('#add_subject_form');
    if (form.parsley().validate()) {
        var form = $('#add_subject_form')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('school/Subject_master/add_or_update'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            async: false,
            success: function(data) {
                resetForm();
                parsed_data = $.parseJSON(data);

                if (parsed_data['result'] == 0) {
                    show_alert('error', 'Subject creation/update failed!');
                    return;
                }

                if (formData.get("mode") == 'update') {
                    _select_row(null, 'delete');
                }
                insert_one_subject_row(parsed_data['subject']);
                $("#_add_subject_modal").modal('hide');
                show_alert('success', 'Subject creation/udpate successful!');
            },
            error: function(err) {
                console.log(err);
                show_alert('error', 'Subject creation/update failed!');
            }
        });
    } else {
        $("#add_subject_button").prop('disabled', false);
    }
}

function show_alert(which_alert, text) {
    switch (which_alert) {
        case 'success':
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: text,
            })
            break;
        case 'error':
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: text,
            })
            break;
    }
}
</script>