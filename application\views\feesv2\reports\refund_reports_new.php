<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Refund Report</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0">
        <h3 class="card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a>
            Refund Report
        </h3>
      </div>
    </div>
<style type="text/css">
  p{
    margin-bottom: .5rem;
  }
  input[type=checkbox]{
    margin: 0px 4px;
  }
</style>
    <div class="card-body">
      <div class="row" style="margin: 0px">
        <div class="col-md-2 form-group">
          <p>Date Range</p>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2 form-group" id="classSection">
          <p>Class</p>
          <?php 
            $array = array();
            foreach ($classes as $key => $class) {
              $array[$class->classId] = $class->className; 
            }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All' class='form-control classId select '");
          ?>
        </div>

        <div class="col-md-3 form-group">
          <p>Select Fee Type</p>
          <select class="form-control multiselect select" multiple title='All' id="fee_type" name="fee_type">
            <?php foreach ($fee_blueprints as $key => $val) { ?>
              <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
            <?php } ?>
          </select>
        </div>

        <div class="col-sm-2 col-md-2" style="height: 4.5rem;">
          <p style="margin-top: 2rem"></p>
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
        </div>
      </div>
    </div>

    <div class="panel-body">
      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h4>Refund Fee Report</h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>
        <div id="refundTableControls" class="refund-table-controls" style="margin-bottom: 10px;"></div>
        <ul class="panel-controls pull-right" id="exportIcon" style="display: none;">
          <button id="stu_print" class="btn btn-danger" onClick="printProfile();"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
          <a style="margin-left:3px;" onClick="exportToExcel_daily()" class="btn btn-primary pull-right"><span class="fa fa-file-text-o"></span> Export</a>
        </ul>
        <div style="clear: both"></div>
        <div class="panel-body refund_tx_table_wrapper table-responsive hidden-xs refund-table-scroll" style="padding: 0; max-height: 500px; overflow-x: auto; overflow-y: auto;">
          <div class="refund_tx">
            <h3>Select filter to get report</h3>
          </div>
        </div>
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>


</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>

<style>
  
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

table, #refundTransaction table {
    font-family: 'Poppins', sans-serif !important;
}
#refundTransaction {
width: 100%;
border-collapse: collapse;
background-color: #ffffff;
box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
opacity: 1 !important;
transition: none !important;
}

#refundTransaction thead th {
position: sticky !important;
top: 0;
background-color: #f1f5f9;
color: #111827;
font-size: 11px;
font-weight: 500;
z-index: 10;
text-align: left;
padding: 12px 16px;
}

#refundTransaction th, #refundTransaction td {
padding: 10px 14px;
border-bottom: 1px solid #e5e7eb;
font-size: 11px;
font-weight: 400;
}

#refundTransaction tbody tr:nth-child(even) {
background-color: #f9fafb;
}

#refundTransaction tbody tr:hover {
background-color: #f1f5f9;
}

#refundTransaction tfoot tr {
background-color: #f3f4f6;
font-weight: 500;
}

.dataTables_filter {
  border-bottom: none !important;
  padding: none !important;
}


  .buttons-print{
    padding: 2px  !important;
}
.buttons-colvis{
    padding: 2px !important;
}

.buttons-excel{
  border: none !important;
  background: none  !important;
  padding: 0  !important;
  margin: 0  !important;
}
.dt-button{
  border: none !important;
  background: none  !important;
}
.btn-info{
    border-radius: 8px !important; 
}
  .dt-button-collection .buttons-columnVisibility:before {
  content: ' ';
  margin-top: -6px;
  margin-left: 10px;
  border: 1px solid black;
  border-radius: 3px;
}

.dt-button-collection .buttons-columnVisibility:before, .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
}

.dt-button-collection .buttons-columnVisibility span {
  margin-left: 20px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
  content: '\2714'; /* Unicode checkmark character */
  margin-top: -10px;
  margin-left: 10px;
  text-align: center;
  text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
}

#refundTransaction_wrapper .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }

div.dt-button-collection {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: 1em 0;
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: white;
    overflow: hidden;
    z-index: 2002;
    border-radius: 5px;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
}

</style>

<style>
  .gap-left {
    margin-left: 20px !important;
  }
  .search-box {
    display: inline-block;
    position: relative;
    margin-right: 2px;
    vertical-align: middle;
    margin-bottom: -5px !important; 
  }
  .input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
  }
  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }
</style>

<style>
.refund-table-scroll {
  max-height: 500px;
  overflow-x: auto;
  overflow-y: auto;
  position: relative;
}

/* Custom scrollbar for the table wrapper */
.refund-table-scroll::-webkit-scrollbar {
  height: 16px;
  width: 16px;
}
.refund-table-scroll::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  border-radius: 8px;
}
.refund-table-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}
/* For Firefox */
.refund-table-scroll {
  scrollbar-width: thick;
  scrollbar-color: #bdbdbd #f1f1f1;
}

/* Make DataTables controls horizontal and above the table */
.refund-table-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 10px;
}

/* Ensure DataTables search box and buttons look good */
.refund-table-controls .search-box {
  margin-bottom: 0;
}
</style>

<script>

$('#search').on('click',function(){
  get_refund_tx_report();
}); 

function get_refund_tx_report() {
  var classId = $('#classId').val();
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var fee_type = $('#fee_type').val();
  $('#fromDate').html(from_date);
  $('#toDate').html(to_date);
  $(".loading-icon").show();
  $('.refund_tx').html('');
  $('#search').prop('disabled', true).val('Please wait...');
  $.ajax({
    url:'<?php echo site_url('feesv2/refund_controller/get_refund_reports'); ?>',
    type: 'post',
    data: {'from_date':from_date, 'to_date':to_date,'classId':classId,'fee_type':fee_type},
      success: function(data) {
        var refund = JSON.parse(data);
        if (refund.length > 0) {
          $('#search').prop('disabled', false).val('Get Report');
          $('.refund_tx').html(construct_refund_table(refund));

          let table = $('#refundTransaction').DataTable({
          ordering: false,
          paging: false, 
          dom: 'Bfrtip', 
          info: false,
          buttons: [
            {
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              action: function (e, dt, node, config) {
                print_fees_status();
              }
            },
            {
              extend: 'excel',
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Excel</button>',
              title: 'Fee Refund report',
              footer: true,
              exportOptions: {
                columns: ':visible', 
              },
            },
          ],
          initComplete: function() {
            let $searchInput = $('#refundTransaction_filter input');
            $searchInput
              .attr('placeholder', 'Enter Search...')
              .removeClass()
              .addClass('input-search');

            $searchInput.wrap('<div class="search-box"></div>');

            $('#refundTransaction_filter label').contents().filter(function(){
              return this.nodeType === 3;
            }).remove();
          }
        });
        initializeColVisButton(table, 'refundTransaction_wrapper');

        // Move DataTables controls above the table
        setTimeout(function() {
          var dtWrapper = $('#refundTransaction_wrapper');
          var dtButtons = dtWrapper.find('.dt-buttons');
          var dtFilter = dtWrapper.find('.dataTables_filter');
          var controls = $('#refundTableControls');
          controls.empty();
          controls.append(dtFilter);
          controls.append(dtButtons);
        }, 0);

        }else{
          $('.refund_tx').html('<h3>No result found</h3>');
          $('#exportIcon').hide();
          $('#search').prop('disabled', false).val('Get Report');
        }
        
        $(".loading-icon").hide();
        $('#search').prop('disabled', false).val('Get Report');
    }
  });
}

function print_fees_status() {
  const printWindow = window.open('', '_blank');
  const mainTable = document.getElementById('refundTransaction')?.outerHTML || '';

  printWindow.document.write(`
    <html>
      <head>
        <title>Fee Refund Report</title>
        <style>
          body {
            font-family: 'Poppins', sans-serif;
            padding: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 12px;
          }
          h3 {
            margin: 15px 0;
          }
          @media print {
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <h3>Fee Refund Report</h3>
        ${mainTable}
        <script>
          window.onload = function() {
            window.print();
          };
          window.onafterprint = function() {
            window.close();
          };
        <\/script>
      </body>
    </html>
  `);

  printWindow.document.close();
}


function initializeColVisButton(table, wrapperId) {
  // Add a column visibility button to the given table
  new $.fn.dataTable.Buttons(table, {
    buttons: [
      {
        extend: 'colvis',
        text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
        className: 'btn btn-info',
      },
    ],
  }).container().appendTo($(`#${wrapperId} label`));
}

function construct_refund_table(refund) {
  var html = '';
  html += `<table class="table table-bordered" id="refundTransaction" > 
      <thead>
       <tr>
        <th>#</th>
        <th>Fee Type</th>
        <th>Receipt Number</th>
        <th>Refund Date</th>
        <th>Student Name</th>
        <th>Class Section</th>
        <th>Refund Amount</th>
        <th>Payment Type</th>
        <th>Remarks</th>
        <th>Status</th>
        <th>Created By</th>
      </tr> 
      </thead>
    <tbody>`;
    for (var i = 0; i < refund.length; i++) {
      html+= '<tr>';
      html+= '<td>'+(i+1)+'</td>';
      html+= '<td>'+refund[i].blueprint_name+'</td>';
      html+= '<td>'+refund[i].receipt_number+'</td>';
      html+= '<td>'+refund[i].refund_date+'</td>';
      html+= '<td>'+refund[i].stdName+'</td>';
      html+= '<td>'+refund[i].classSection+'</td>';
      html+= '<td>'+refund[i].refund_amount+'</td>';
      html+= '<td>'+refund[i].payment_type+'</td>';
      html+= '<td>'+refund[i].remarks+'</td>';
      html+= '<td>'+refund[i].refund_status+'<br><span style="color:red">'+refund[i].transaction_status+'<span></td>';
      html+= '<td>'+refund[i].refunded_by+'</td>';

      html+= '</td>';
    }
   html+= '</tbody>';
   html+= '</table>';
   return html;
}

  function printProfile(){
    var restorepage = document.body.innerHTML;
    $('#print_visible').css('display','block');
    $('#exportIcon').css('display','none');
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }


  function exportToExcel_daily(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var summaryTable = $("#print_visible").html();
    var mainTable = $(".refund_tx").html();

    htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "daily_refund_detailed_report.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>