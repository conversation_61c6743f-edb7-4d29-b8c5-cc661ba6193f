<style type="text/css">
	.loader-background {
	    width: 100%;
	    height: 25%;            
	    position: absolute;
	    display: none;
	    top: 0;
	    left: 0;
	    opacity: 0.8;
	    z-index: 10;
	    background-color: #000;
	    border-radius: 8px;
	}
	.unread_box_no_style_new{
		position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
	.new_circleShape {
     padding: 3px; 
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3.6rem;
    width: 3.6rem;
    text-align: center;
    vertical-align: middle;
	}

.new_circleShape_leftBtn {
    /* padding: 8px 14px; */
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3.6rem;
    width: 3.6rem;
    text-align: center;
    vertical-align: middle;
    line-height: 1.6;
}

	.btn .fa{
		margin-right: 0px;
	}
	.label{
		border-radius: .45em;
	}
	.fa-check-circle{
		color: white;
	}
	/* .btn-primary, .btn-danger,.btn-warning,.btn-success{
		border-radius: .65rem;
	} */
	.form-control{
		border-radius: .45rem;
	}
	.input-group-addon{
		border-radius: .45rem;
	}
	p{
		margin-bottom: .5rem;
	}
	input[type=checkbox]{
		margin: 0px 4px;
	}
	label{
		font-size: 14px;		
	}
</style>
<div id="opacity">
<div class="col-md-12">
	<div class="card panel_new_style">
		<div class="card-header panel_heading_new_style_padding padding8px">
      		<div class="row m-0">
				<div class="col-xs-9 p-0">
				  	<h3 class="card-title panel_title_new_style mb-0">
					  	<strong><?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></strong>
					</h3>
				</div>

				<div class="col-xs-3 p-0">
					<div class="new_circleShape" style="background-color:#fe970a;float:right;">
						<!-- <a class="control-primary" style="cursor:pointer;" data-toggle="modal" data-target="#add_task_modal"> -->
							<a href="<?php echo site_url('student_tasks/tasks/addTask')?>" class="control-primary" style="cursor:pointer; color:#fff;">
							<span class="fa fa-plus" style="line-height:3.2rem"></span>
						</a>					
					</div>
				</div>
			</div>
		</div>
		<div class="card-body padding8px">
			<div class="row m-0">
			<div class="col-sm-12 mb-2 p-0">
				<label>Select Filter</label>
				<select class="form-control select" name="filter_type" id="filter_type" >
				     <option value="staff" >Staff wise</option> 
					<option value="class">Subject wise</option>
					
				</select>
			</div>
			 <div class="col-sm-12 mb-2 p-0" id="staff_details">
				
				<label>Staff List</label>
					<select class="form-control select" name="staff_list" id="staff_list">
					<option value="all">select staff</option>
					
					<?php
						foreach($staff_option as $sl){?>
							
							<option <?php if($selected_staff_option == $sl->MasterId){ echo 'selected'; } ?> value ="<?= $sl->MasterId?>"><?= $sl->name ?></option>	
						

						<?php }
					?>
					</select>
				
			</div> 
			<div class="col-sm-12 mb-2 p-0" id="class_details" style="display:none" >
				<label>Select Class</label>
				<select class="form-control select" name="section_id_main" id="section_id_main" onchange="getSubjetsList()">
				<option value="all">Select Class</option>
				<?php 
					foreach ($classSectionsList as $key => $value) {
						echo '<option value="'.$value->sectionId.'">'.$value->class_name.$value->section_name.'</option>';
					}
				?>
				</select>
			</div>
			<div class="col-sm-12 mb-2 p-0 " id="subject_details"  style="display:none">
				<label>Select Subject</label>
				<select class="form-control" name="subject_id_main" id="subject_id_main" >
					<option value="">Select Subject</option>
				</select>
			</div>
			<div id="date-range" class="col-sm-12 mb-2 p-0 ">
				<div class="col-sm-12 mb-2 p-0"> 
					<label>From</label>
					<div class="input-group date" id="start_date_picker"> 
						<input readonly autocomplete="off" type="text" min="10" max="10" value="<?php echo date('d-m-Y', strtotime('-5 days')); ?>" class="form-control" id="from_date" name="from_date" >
						<span class="input-group-addon">
							<span class="glyphicon glyphicon-calendar"></span>
						</span>
					</div>
				</div>
				<div class=" col-sm-12 mb-2 p-0"> 
					<label>To</label>
					<div class="input-group date" id="end_date_picker"> 
						<input readonly autocomplete="off" type="text" value="<?php echo date('d-m-Y');?>" class="form-control " id="end_date" name="to_date" >
						<span class="input-group-addon">
							<span class="glyphicon glyphicon-calendar"></span>
						</span>
					</div>
				</div>
				<div class="col-sm-12 p-0 mt-4">
					<input type="button" style="width: 100%;" onclick="callGetTasks()" class="btn btn-md btn-primary" value="Get Tasks" id="getBtn" />
				</div>
			</div>
			</div>
		</div>


<!-- Loader when ajax call happens -->
<div id="loader" class="loaderclass" style="display:none;"></div>
<!-- Student Tasks Date for the selected filters -->
<div class="mt-5 padding8px">
	<div class="col-sm-12 p-0 mb-3">
		<div class="col-sm-12 mb-2 p-0"><span style="font-size: 16px"><b>Sort by: </b></span></div>
		<div class="row">
			<div class="col-xs-6 mb-2 pl-0">
				<select class="form-control select" name="task_type_main" id="task_type_main"  onchange="callGetTasks()">
					<option value="all">All Types</option>
					<option value="Reading">Reading</option>
					<option value="Writing-Submission">Writing with Submission</option>
					<option value="Writing-NoSubmission">Writing without Submission</option>
					<option value="Viewing">Viewing</option>
				</select>
			</div>			

			<div class="col-xs-6 mb-2 pr-0">
				<select class="form-control select" name="status_main" id="status_main" onchange="callGetTasks()">
					<option value="all">All Status</option>
					<option value="published">Published</option>
					<option value="disabled">UnPublished</option>
				</select>
			</div>					

		</div>

	</div>
	<div class="col-xs-12" style="padding: 0;">
		<div class="list-group" id="tasks-data">
			<center><h4 style="color:#888;"><strong>Select the filters to get the tasks</strong></h4></center>
		</div>
	</div>
</div>
</div>
</div>

<a href="<?php echo site_url('student_tasks/tasks/dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<script type="text/javascript">
var is_task_admin=<?php echo $is_task_admin?>;
var staff_login=<?php echo $staff_login?>;
var selected_resources_count=0;
$(document).ready(function() {
	$('#datePicker,#task_last_date').datepicker({
		format: 'dd-mm-yyyy',
		"autoclose": true,
		startDate: new Date()
	});

	$('#start_date_picker').datepicker({
		format: 'dd-mm-yyyy',
		"autoclose": true
	});
	$('#end_date_picker').datepicker({
	    format: 'dd-mm-yyyy',
	    "autoclose": true,
	    endDate: new Date()
	});
	setTimeout(callGetTasks, 1000);
});

function formatDateFutureTime(date) {
    const currentDate = new Date();
    const timestamp = date.getTime();
    const currentTimestamp = currentDate.getTime();
    const difference = timestamp - currentTimestamp; // Calculating difference with future time
    const seconds = Math.floor(difference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);

    if (seconds < 60) {
        return "In " + seconds + "s";
    } else if (minutes < 60) {
        return "In " + minutes + "m";
    } else if (hours < 24) {
        return "In " + hours + "h";
    } else if (days < 7) {
        return "In " + days + "d";
    } else if (months < 12) {
        return "In " + Math.floor(days / 7) + "w";
    } else {
        return "In " + Math.floor(months / 12) + "y";
    }
}

function formatDatePastTime(date) {
    const currentDate = new Date();
    const timestamp = date.getTime();
    const currentTimestamp = currentDate.getTime();
    const difference = currentTimestamp - timestamp;
    const seconds = Math.floor(difference / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);

    if (seconds < 60) {
        return seconds + "s ago";
    } else if (minutes < 60) {
        return minutes + "m ago";
    } else if (hours < 24) {
        return hours + "h ago";
    } else if (days < 7) {
        return days + "d ago";
    } else if (months < 12) {
        return Math.floor(days / 7) + "w ago";
    } else {
        return Math.floor(months / 12) + "y ago";
    }
}

function formatDateValid(inputDateStr) {
  const date = new Date(inputDateStr);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // handle midnight
  const formattedHours = String(hours).padStart(2, '0');
  
  return `${day}-${month}-${year} at ${formattedHours}:${minutes} ${ampm}`;
}

function formatDate(date) {
  var year = date.getFullYear();
  var month = String(date.getMonth() + 1).padStart(2, '0');
  var day = String(date.getDate()).padStart(2, '0');
  var hours = String(date.getHours()).padStart(2, '0');
  var minutes = String(date.getMinutes()).padStart(2, '0');
  var seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function __get_time_to_publish(task_id, timestamp) {
	var currentDate = new Date();
	var formattedDate = formatDate(currentDate);

	var d1 = new Date(timestamp.toString());
  	var d2 = new Date(formattedDate.toString());
	  var formatDateValid1= formatDateValid(timestamp.toString());

	// const diff = Math.abs(d2 - d1);
	// const minutes = Math.floor((diff / (1000 * 60)) % 60);
	// const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
	// const days = Math.floor(diff / (1000 * 60 * 60 * 24));

	if (d1.getTime() === d2.getTime()) {
		return "Published now";
	} else if (d1 < d2) {
		var past_time= formatDatePastTime(d1);
		return `<small style="padding:2px 13.5px;" class="label label-default label-form active_new mt-0" id="task_status_${task_id}">Published ${past_time}</small>`;
	} else {
		var future_time= formatDateFutureTime(d1);
		return `<small style="padding:2px 13.5px;" class="btn-warning label label-default label-form active_new mt-0" id="task_status_${task_id}">publish ${future_time}</small>`;
	}
}

$('#filter_type').on('change',function(){
	var filter = $('#filter_type').val();
	if(filter == 'staff'){
		$('#staff_details').show();
		$('#class_details').hide();
		$('#subject_details').hide();
		$("#getBtn").prop('disabled',false);
	}else{
		$('#staff_details').hide();
        $('#class_details').show();
		$('#subject_details').show();
	}
});

function getSubjetsList(){
	var class_section_id = $("#section_id_main").val();
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getSubjectsList') ?>',
		type:'post',
		data: {'class_section_id':class_section_id},
		success : function(data){
			var data = $.parseJSON(data);
			console.log('data', data);
			var subjects_options = '';
			if(data.length==0){
				$("#subject_id_main").prop('disabled',true);
            	$("#from_date").prop('disabled',true);
            	$("#end_date").prop('disabled',true);
            	$("#getBtn").prop('disabled',true);
            	$("#task_type_main").prop('disabled',true);
            	$("#status_main").prop('disabled',true);
				bootbox.dialog({
                	title: "Warning....",
                    message: "<h4>There are no subjects for the selected Class</h4>",
                    className: "medium",
                    buttons: {
                    		ok: {
                        	label: "Ok",
                        	className: 'btn btn-primary'
                    	}
                 	}
                });
                $( "#section_id_main" ).focus();
			}
			else{
				subjects_options = '<option value="all">All Subjects</option>';
				for(var i=0;i<data.length;i++){
            		subjects_options+='<option value="'+data[i].subject_id+'">'+data[i].subject_name+'</option>';
            	}
            	$("#subject_id_main").html(subjects_options);
            	$("#subject_id_main").prop('disabled',false);
            	$("#from_date").prop('disabled',false);
            	$("#end_date").prop('disabled',false);
            	$("#getBtn").prop('disabled',false);
            	$("#task_type_main").prop('disabled',false);
            	$("#status_main").prop('disabled',false);
            }
		}
  	});
}

function callGetTasks() {
	var section_id = $("#section_id_main").val(); 
	var subject_id = $("#subject_id_main").val();
	var staff_id = $("#staff_list").val();
	var filter = $('#filter_type').val();
	var from_date = $("#from_date").val();
	var end_date = $("#end_date").val();
	var task_type = $("#task_type_main").val();
	var status = $("#status_main").val();
	if(from_date == '' || end_date == '') {
		bootbox.dialog({
			title: "Warning....",
			message: "<h4>Please select From and To dates</h4>",
			className: "medium",
			buttons: {
				ok: {
					label: "Ok",
					className: 'btn btn-primary'
				}
			}
		});
		return false;
	}
	
	var id = staff_id;
	if(filter=='class') {
		id = section_id;
	}
	$.ajax({
		url:'<?php echo site_url('student_tasks/Tasks/getTasks') ?>',
		type:'post',
		data: {'subject_id': subject_id,'from_date':from_date,'end_date':end_date,'task_type':task_type,'status':status,'id':id, 'type': filter},
        beforeSend: function() {
        	$('#opacity').css('opacity','0.5');
	        $('#loader').show();
	    },
		success : function(data){
			var data = $.parseJSON(data);
			var tasks = data.tasks;
			var html='';
			if(tasks.length == 0) {
		      $("#tasks-data").html('<div style="border: solid 2px #ccc; border-radius:.7rem; padding:.5rem .8rem; margin-top: 4%;"><h4 style="color:#e04b4a;">No tasks available for the selected filters</h4></div>');
		      $("#information").html('');
		      $("#options").html('');
		    }
		    else{
		    	for(var i=0;i<tasks.length;i++){
					var task_id = tasks[i].task_id;
		    		var disabled_btn = '';
		    		var badge='';
		    		var ev_status = '';
		    		var task_description='';
					var timestamp_status= __get_time_to_publish(task_id, tasks[i].task_publish_timestamp);
		    		if(tasks[i].status=='disabled'){
		    			disabled_btn='<span style="padding:1.5px 5.5px;" class="label label-default label-form discard mt-0" id="task_status_'+tasks[i].task_id+'">UnPublished</span>';
		    		}
		    		else{
		    			disabled_btn=timestamp_status;
		    		}
		    		if(tasks[i].task_description==''){
						task_description='No Description';
					}
					else{
						task_description=tasks[i].task_description;
					}

	                if(tasks[i].task_type=='Reading'){
	                    badge='R';
	                }
	                else if(tasks[i].task_type=='Writing-Submission'){
	                    badge='WS';
	                }
	                else if(tasks[i].task_type=='Writing-NoSubmission'){
	                    badge='W';
	                }
	                else{
	                    badge='V';
	                }


		    		if(tasks[i].require_evaluation==1){
		    			ev_status='<span class="pull-right"><b>Evaluations : </b>'+tasks[i].evaluation_count+'/'+tasks[i].total_count+'</span>';
					}
					var url="<?php echo site_url('student_tasks/tasks/view_task_data/')?>"+tasks[i].task_id+'/'+id+'/'+filter;

		    		html += '<a href="'+url+'" class="list-group-item px-1" id="task_'+tasks[i].task_id+'">';
			        html += '<div class="circular-panel-xs read"><div class="row"><div class="col-2 pl-0"><div class="new_circleShape_leftBtn" style="background-color: #fe970a;">';
			        html += '<span style="vertical-align: middle;">'+badge+'</span></div></div>';
			        html += '<div class="col-10 pr-0"><p class=" col-12 mb-1 p-0 d-flex justify-content-end">'+disabled_btn+'</p><p class="col-12 p-0" style="color:#444;font-size: 20px;font-weight: 500;">'+tasks[i].task_name+'</p></div></div>';
			        html += '<div class="row"><div class="col-12 p-0 mt-3"><p style="color:black;font-size:14px;font-weight:500;">';

					html += '<span style="margin:0 0 5px;"><b>Class : </b>';
					if(tasks[i].group_name != null) {
		    			html += '('+tasks[i].group_name+') ';
		    		}
					html += tasks[i].class_section+'</span><br>';
					if(tasks[i].task_type == 'Reading' || tasks[i].task_type == 'Viewing' || tasks[i].task_type == 'Writing-NoSubmission'){
						html += '<span style="margin:0 0 5px;"><b>Acknowledgements : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+'</span></p>';	
					}else{
						html += '<span style="margin:0 0 5px;"><b>Submissions : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+''+ev_status+'</span></p>';
					}
                    html += '<p style="color:black;font-size:14px;font-weight:500;margin:0 0 5px;"><b>Created By : </b><span style="color:#6893ca;font-weight:700;">'+tasks[i].created_by+'</span><br><span><b>Created On : </b><span style="color:#6893ca;font-weight:700;">'+moment(tasks[i].created_onTime).format('DD-MM-YYYY hh:mm A')+'</span></span></p></div></div></div></a>';
		    	}
		    	$("#tasks-data").html(html);
		    } 
		},
        complete: function() {
	        $('#loader').hide();
	        $('#opacity').css('opacity','');
	    },
        error: function (err) {
        	//console.log(err);
        }
  	});

}

	/*function callGetTasksOld(){
		var section_id_main =$("#section_id_main").val(); 
		var subject_id = $("#subject_id_main").val();
		var staff_id=$("#staff_list").val();
		console.log(staff_id);
		var filter = $('#filter_type').val();
		var from_date = $("#from_date").val();
		var end_date = $("#end_date").val();
		var task_type = $("#task_type_main").val();
		var status = $("#status_main").val();
		if(filter=='class')
		{
		$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getSelectedTasks') ?>',
			type:'post',
			data: {'subject_id':subject_id,'from_date':from_date,'end_date':end_date,'section_id':section_id_main,'task_type':task_type,'status':status,'staff_id':staff_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var tasks = data.tasks;
				var html='';
				if(tasks.length == 0) {
			      $("#tasks-data").html('<div style="border: solid 2px #ccc; border-radius:.7rem; padding:.5rem .8rem; margin-top: 4%;"><h4 style="color:#e04b4a;">No tasks available for the selected filters</h4></div>');
			      $("#information").html('');
			      $("#options").html('');
			    }
			    else{
			    	for(var i=0;i<tasks.length;i++){
			    		var disabled_btn = '';
			    		var badge='';
			    		var ev_status = '';
			    		var task_description='';
			    		if(tasks[i].status=='disabled'){
			    			disabled_btn='<span style="padding:1.5px 5.5px;" class="label label-default label-form discard mt-0" id="task_status_'+tasks[i].task_id+'">UnPublished</span>';
			    		}
			    		else{
			    			disabled_btn='<span style="padding:2px 13.5px;" class="label label-default label-form active_new mt-0" id="task_status_'+tasks[i].task_id+'">Published</span>';
			    		}
			    		if(tasks[i].task_description==''){
							task_description='No Description';
						}
						else{
							task_description=tasks[i].task_description;
						}

		                if(tasks[i].task_type=='Reading'){
		                    badge='R';
		                }
		                else if(tasks[i].task_type=='Writing-Submission'){
		                    badge='WS';
		                }
		                else if(tasks[i].task_type=='Writing-NoSubmission'){
		                    badge='W';
		                }
		                else{
		                    badge='V';
		                }


			    		if(tasks[i].require_evaluation==1){
			    			ev_status='<span class="pull-right"><b>Evaluations : </b>'+tasks[i].evaluation_count+'/'+tasks[i].total_count+'</span>';
						}
						var url="<?php echo site_url('student_tasks/tasks/view_task_details/')?>"+tasks[i].task_id+'/'+section_id_main;

			    		html += '<a href="'+url+'" class="list-group-item px-1" id="task_'+tasks[i].task_id+'">';
                		html += '<div class="circular-panel-xs read"><div class="row"><div class="col-2 pl-0"><div class="new_circleShape_leftBtn" style="background-color: #fe970a;">';
                        html += '<span style="vertical-align: middle;">'+badge+'</span></div></div>';
                        html += '<div class="col-10 pr-0"><p class=" col-12 mb-1 p-0 d-flex justify-content-end">'+disabled_btn+'</p><p class="col-12 p-0" style="color:#444;font-size: 20px;font-weight: 500;">'+tasks[i].task_name+'</p></div></div>';
                    	html += '<div class="row"><div class="col-12 p-0 mt-3">';


						if(tasks[i].group_name != null) {
			    			html += '<p style="color:black;font-size:14px;font-weight:500;"><span style="margin:0 0 5px;"><b>Group: </b>'+tasks[i].group_name+'</span></p>';
			    		}
                    	html += '<p style="color:black;font-size:14px;font-weight:500;">';
						if(tasks[i].task_type == 'Reading' || tasks[i].task_type == 'Viewing' || tasks[i].task_type == 'Writing-NoSubmission'){
							html += '<span style="margin:0 0 5px;"><b>Acknowledgements : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+'</span></p>';	
						}else{
    					html += '<span style="margin:0 0 5px;"><b>Submissions : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+''+ev_status+'</span></p>';
						}

                        html += '<p style="color:black;font-size:14px;font-weight:500;margin:0 0 5px;"><b>Created By : </b><span style="color:#6893ca;font-weight:700;">'+tasks[i].created_by+'</span><br><span><b>Created On : </b><span style="color:#6893ca;font-weight:700;">'+moment(tasks[i].created_on).format('DD-MM-YYYY')+'</span></span></p></div></div></div></a>';
			    	}
			    	$("#tasks-data").html(html);
					getSingleTaskDetailsButtons(tasks[0].task_id,tasks[0].status);
					
			    } 
			},
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    },
            error: function (err) {
            	//console.log(err);
            }
      	});
	}
	else{
			$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getSelectedTasksStaffWise') ?>',
			type:'post',
			data: {'from_date':from_date,'end_date':end_date,'task_type':task_type,'status':status,'staff_id':staff_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			
			success : function(data){
				var data = $.parseJSON(data);
				var tasks = data.tasks;
				var html='';
				if(tasks.length == 0) {
			      $("#tasks-data").html('<div style="border: solid 2px #ccc; border-radius:.7rem; padding:.5rem .8rem; margin-top: 4%;"><h4 style="color:#e04b4a;">No tasks available for the selected filters</h4></div>');
			      $("#information").html('');
			      $("#options").html('');
			    }
			    else{
			    	for(var i=0;i<tasks.length;i++){
			    		var disabled_btn = '';
			    		var badge='';
			    		var ev_status = '';
			    		var task_description='';
			    		if(tasks[i].status=='disabled'){
			    			disabled_btn='<span style="padding:1.5px 5.5px;" class="label label-default label-form discard mt-0" id="task_status_'+tasks[i].task_id+'">UnPublished</span>';
			    		}
			    		else{
			    			disabled_btn='<span style="padding:2px 13.5px;" class="label label-default label-form active_new mt-0" id="task_status_'+tasks[i].task_id+'">Published</span>';
			    		}
			    		if(tasks[i].task_description==''){
							task_description='No Description';
						}
						else{
							task_description=tasks[i].task_description;
						}

		                if(tasks[i].task_type=='Reading'){
		                    badge='R';
		                }
		                else if(tasks[i].task_type=='Writing-Submission'){
		                    badge='WS';
		                }
		                else if(tasks[i].task_type=='Writing-NoSubmission'){
		                    badge='W';
		                }
		                else{
		                    badge='V';
		                }


			    		if(tasks[i].require_evaluation==1){
			    			ev_status='<span class="pull-right"><b>Evaluations : </b>'+tasks[i].evaluation_count+'/'+tasks[i].total_count+'</span>';
						}
						var url="<?php echo site_url('student_tasks/tasks/view_task_details_staff/')?>"+tasks[i].task_id+'/'+staff_id;

			    		html += '<a href="'+url+'" class="list-group-item px-1" id="task_'+tasks[i].task_id+'">';
				        html += '<div class="circular-panel-xs read"><div class="row"><div class="col-2 pl-0"><div class="new_circleShape_leftBtn" style="background-color: #fe970a;">';
				        html += '<span style="vertical-align: middle;">'+badge+'</span></div></div>';
				        html += '<div class="col-10 pr-0"><p class=" col-12 mb-1 p-0 d-flex justify-content-end">'+disabled_btn+'</p><p class="col-12 p-0" style="color:#444;font-size: 20px;font-weight: 500;">'+tasks[i].task_name+'</p></div></div>';
				        html += '<div class="row"><div class="col-12 p-0 mt-3"><p style="color:black;font-size:14px;font-weight:500;">';

						html += '<span style="margin:0 0 5px;"><b>Class : </b>';
						if(tasks[i].group_name != null) {
			    			html += '('+tasks[i].group_name+') ';
			    		}
						html += tasks[i].class_section+'</span><br>';
						if(tasks[i].task_type == 'Reading' || tasks[i].task_type == 'Viewing' || tasks[i].task_type == 'Writing-NoSubmission'){
							html += '<span style="margin:0 0 5px;"><b>Acknowledgements : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+'</span></p>';	
						}else{
    						html += '<span style="margin:0 0 5px;"><b>Submissions : </b>'+tasks[i].submission_count+'/'+tasks[i].total_count+''+ev_status+'</span></p>';
						}
                        html += '<p style="color:black;font-size:14px;font-weight:500;margin:0 0 5px;"><b>Created By : </b><span style="color:#6893ca;font-weight:700;">'+tasks[i].created_by+'</span><br><span><b>Created On : </b><span style="color:#6893ca;font-weight:700;">'+moment(tasks[i].created_on).format('DD-MM-YYYY')+'</span></span></p></div></div></div></a>';
			    	}
			    	$("#tasks-data").html(html);
					getSingleTaskDetailsButtons(tasks[0].task_id,tasks[0].status);
					
			    } 
			},
            complete: function() {
		        $('#loader').hide();
		        $('#opacity').css('opacity','');
		    },
            error: function (err) {
            	//console.log(err);
            }
      	});
		}
	}*/
</script>
<style type="text/css">
.modal-open{
	overflow:inherit;
}
	.circular-panel-xs {
    overflow-y: hidden;
    overflow-wrap: break-word;
    min-height: 50px;
    padding-left: 5px !important;
    padding-right: 5px !important;
    border-bottom: 1px solid #ccc;
}
 #video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}
	.resources_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}
	.resources_main_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}

	.names {
	border: 1px solid #ccc;
    margin-bottom: .5rem;
    border-radius: 10px;
    display: flex;
    height: 8rem;
    overflow: auto;
    padding: .5rem 0.2rem;
	}
	.dialogWide > .modal-dialog {
    	width: 50% !important;
    	margin-left: 25%;
	}
	.list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
	    cursor: pointer;
	}
	.list-group-item.active_new{
	    background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;
	}
	.list-group-item.active_new, .list-group-item.active_new:hover, .list-group-item.active_new:focus{
	    background: #ebf3f9;
	    color: #737373;
	}
	.list-group-item{
		border:none;
	}
	.loaderclass {
		border: 8px solid #eee;
		border-top: 8px solid #7193be;
		border-radius: 50%;
		width: 48px;
		height: 48px;
		position: fixed;
		z-index: 1;
		animation: spin 2s linear infinite;
		margin-top: 30%;
		margin-left: 40%;
		position: absolute;
		z-index: 99999;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.active_new{
		background: #6893ca;
	}
	.discard{
		background: #C82333;
	}
	.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
	}
</style>