<div class="col-md-12">
  <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
      <div class="row d-flex align-items-center">
        <div class="col-xs-9 p-0">
          <h3 class="card-title panel_title_new_style mb-0">
            <strong>Add & View Objectives</strong>
          </h3>
        </div>
        <div class="col-xs-3 p-0">
          <a href="" class="new_circleShape_res" style="background-color: #fe970a; float: right;" data-toggle="modal" data-target="#addNewObjectiveModal">
            <span class="fa fa-plus" style="font-size: 19px;"></span>
          </a>
        </div>
      </div>
    </div>
    <div class="card-body px-2 py-1">
      <div id="objectivesDiv">
        <div class="no-data-display">Loading...</div>
      </div>
    </div>
  </div>
</div>

<a href="<?php echo site_url('academics/academics_menu'); ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
<div class="modal fade" id="addNewObjectiveModal" tabindex="-1" role="dialog" aria-labelledby="addNewObjectiveModal" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important;margin: auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="addNewObjectiveModal">Add New Objective</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetForm()">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="card-body">
                    <div class="col-md-12 pb-3">
                        <form action="" data-parsley-validate="true" class="form-horizontal mb-2" id="objectives">
                            <div class="form-group">
                                <label for="objective_name" class="col-md-2">Name <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input type="text" name="objective_name" id="objective_name" class="form-control" placeholder="Enter Objective Name" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-2">Description</label>
                                <div class="col-md-10">
                                    <textarea name="objective_description" id="objective_description" class="form-control" placeholder="Enter Description"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetForm()">Close</button>
                <button type="button" id="add_objective" onclick="submitObjective()" class="btn btn-primary mt-0">Submit</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }

  .new_circleShape_res1 {
    padding: 5px 8px;
    border-radius: 50% !important;
    font-size: 16px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
  }

  .loader-background {
    width: 100%;
    height: 100%;
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #fff;
    border-radius: 8px;
  }

  ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
  }

  /* Create a custom scrollbar */
  ::-webkit-scrollbar-track {
    background-color: #f2f2f2;
    border-radius: 10px;
  }

  /* Create a thumb for the scrollbar */
  ::-webkit-scrollbar-thumb {
    /* background-color: #007bff; */
    background-color: #777;
    border-radius: 0px;
  }

  /* Make the scrollbar visible when hovering over the track */
  ::-webkit-scrollbar-track-piece-over:hover {
    background-color: #F5F5F5;
  }

  /* Make the scrollbar thumb visible when hovering over it */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #777;
  }

  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
  }

  ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
  }

  #objectivesTable_filter{
    width: 51% !important;
  }

  .dataTables_length{
    width: 49% !important;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function () {
  getAllObjectives();
});

function getAllObjectives() {
  $.ajax({
    url: '<?php echo site_url('academics/objectives/getAllObjectives') ?>',
    type: 'post',
    success: function (data) {
      let parsedData = JSON.parse(data);
      if (parsedData.length == 0) {
        $("#objectivesDiv").html('<div class="no-data-display">No Data Found</div>');
      } else {
        $("#objectivesDiv").html(constructObjectivesTable(parsedData));
        $("#objectivesTable").DataTable({
          "ordering": false,
          "pageLength": 10,
          "lengthMenu": [10, 20, 50, 100, 200, 500],
          "dom": '<"top"fl>rt<"bottom"ip><"clear">',
          "language": {
            "search": "Search:",
            "paginate": {
              "previous": "Previous",
              "next": "Next",
              "first": "First",
              "last": "Last"
            }
          }
        });
      }
    },
    error: function (data) {
      console.log(data);
      $("#objectivesDiv").html('<h4>Something went wrong!, please try again later.</h4>');
    }
  });
}

function constructObjectivesTable(data) {
  let html = '<table class="table table-bordered" id="objectivesTable">';
  html += '<thead><tr><th>#</th><th>Name</th><th>Description</th><th>Created By</th></tr></thead><tbody>';
  for (let i = 0; i < data.length; i++) {
    html += '<tr>';
    html += '<td>' + (i + 1) + '</td>';
    html += '<td>' + data[i].objective_name + '</td>';
    html += '<td>' + (data[i].objective_description ? data[i].objective_description : '-') + '</td>';
    html += '<td>' + data[i].friendly_name + '</td>';
    html += '</tr>';
  }
  html += '</tbody></table>';
  return html;
}

function resetForm(){
  var form = $('#objectives');
  form[0].reset();
  form.parsley().reset();
}

function submitObjective() {
  const form = $("#objectives");
  if (form.parsley().validate()) {
    $("#add_objective").prop('disabled', true).html('Please wait...');
    var objective_name = $("#objective_name").val();
    var objective_description = $("#objective_description").val();

    $.ajax({
      url: '<?php echo site_url('academics/objectives/submitObjective') ?>',
      type: 'post',
      data: { 'objective_name': objective_name, 'objective_description': objective_description },
      success: function (data) {
        data = JSON.parse(data);
        if (data == -1) {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Objective already exists!",
          }).then(e => {
            resetForm();
            $("#add_objective").prop('disabled', false).html('Submit');
          });
          return;
        }
        if (data == 1) {
          Swal.fire({
            icon: "success",
            title: "Objective Added",
            text: "New objective added successfully!",
          }).then(e => {
            $("#add_objective").prop('disabled', false).html('Submit');
            $('#addNewObjectiveModal').modal('hide');
            resetForm();
            getAllObjectives();
          })
        } else {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong!",
          }).then(e => {
            $("#add_objective").prop('disabled', false).html('Submit');
            $('#addNewObjectiveModal').modal('hide');
            resetForm();
            getAllObjectives();
          });
        }
      }
    });
  }
}
</script>