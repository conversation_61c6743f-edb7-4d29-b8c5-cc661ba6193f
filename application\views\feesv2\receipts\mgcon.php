<div class="row">
  <div class="panel panel-default">
    <div class="panel-heading" style="border-bottom: none;">
      <h3 id="stu_print"  class="panel-title">Fee Receipt</h3>
        <ul class="panel-controls">
          <?php 
          if (!empty($cancel)== 1) { ?>
            <a class="btn btn-info" id="stu_print" onclick="close_window()"  href="javascript:void(0)"><i class="fa fa-mail-reply"></i>Close</a>
          <?php }else{ ?>
            <a class="btn btn-info" id="stu_print" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints/'.$fee_trans->student_id);?>"><i class="fa fa-mail-reply"></i>Next fee collection</a>
          <?php } ?>
          <button id="stu_print" class="btn btn-danger" onclick="print_receipt()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        </ul>
    </div>
    <div class="panel-body" id="printArea">
      <div class="col-md-12" id="panel_border">
      <?php for($m=1; $m <= 1; $m++) {
           switch ($m) {
            case '1':
              // $copy = '(OFFICE COPY)';
              $copy = '';
              break;
            case '2':
              // $copy = '(STUDENT COPY)';
              $copy = '';
              break;
            }
        ?>
        <?php 
          $borderBottom ='';
        if($m == 1){ 
           $borderBottom ='';
        } ?>
         <?php 
            switch ($fee_trans->student->clsName) {
              case 'BSc-Nursing 2019':
                if ($fee_trans->comp[0]->insName == 'First Year') {
                  $Acadyear = '2019-20';
                }else if($fee_trans->comp[0]->insName == 'Second Year') {
                  $Acadyear = '2020-21';
                }else if($fee_trans->comp[0]->insName == 'Third Year') {
                  $Acadyear = '2021-22';
                }else{
                  $Acadyear = '2022-23';
                } 
                break;
              case 'BSc-Nursing 2020':
                if ($fee_trans->comp[0]->insName == 'First Year') {
                  $Acadyear = '2020-21';
                }else if($fee_trans->comp[0]->insName == 'Second Year') {
                  $Acadyear = '2021-22';
                }else if($fee_trans->comp[0]->insName == 'Third Year') {
                  $Acadyear = '2022-23';
                }else{
                  $Acadyear = '2023-24';
                } 
                break;
              case 'BSc-Nursing 2021':
                if ($fee_trans->comp[0]->insName == 'First Year') {
                  $Acadyear = '2021-22';
                }else if($fee_trans->comp[0]->insName == 'Second Year') {
                  $Acadyear = '2022-23';
                }else if($fee_trans->comp[0]->insName == 'Third Year') {
                  $Acadyear = '2023-24';
                }else{
                  $Acadyear = '2024-25';
                } 
                break;
              case 'BSc-Nursing 2022':
                if ($fee_trans->comp[0]->insName == 'First Year') {
                  $Acadyear = '2022-23';
                }else if($fee_trans->comp[0]->insName == 'Second Year') {
                  $Acadyear = '2023-24';
                }else if($fee_trans->comp[0]->insName == 'Third Year') {
                  $Acadyear = '2024-25';
                }
                else if($fee_trans->comp[0]->insName == 'Fourth Year') {
                  $Acadyear = '2025-26';
                }else{
                  $Acadyear = '2022-23';
                } 
                break;
              case 'BSc-Nursing 2023':
                  if ($fee_trans->comp[0]->insName == 'First Year') {
                    $Acadyear = '2023-24';
                  }else if($fee_trans->comp[0]->insName == 'Second Year') {
                    $Acadyear = '2024-25';
                  }else if($fee_trans->comp[0]->insName == 'Third Year') {
                    $Acadyear = '2025-26';
                  }
                  else if($fee_trans->comp[0]->insName == 'Fourth Year') {
                    $Acadyear = '2027-28';
                  }else{
                    $Acadyear = '2023-24';
                  } 
                  break;
              case 'Post BSc-Nursing-2023':
                if ($fee_trans->comp[0]->insName == '1st Semester') {
                  $Acadyear = '2023-24';
                }else if($fee_trans->comp[0]->insName == '2nd Semester') {
                  $Acadyear = '2023-24';
                }else if($fee_trans->comp[0]->insName == '3rd Semester') {
                  $Acadyear = '2024-25';
                }
                else if($fee_trans->comp[0]->insName == '4th Semester') {
                  $Acadyear = '2024-25';
                }else{
                  $Acadyear = '2023-24';
                } 
                break;
                case 'MSc-Nursing-2023':
                  if ($fee_trans->comp[0]->insName == '1st Semester') {
                    $Acadyear = '2023-24';
                  }else if($fee_trans->comp[0]->insName == '2nd Semester') {
                    $Acadyear = '2023-24';
                  }else if($fee_trans->comp[0]->insName == '3rd Semester') {
                    $Acadyear = '2024-25';
                  }
                  else if($fee_trans->comp[0]->insName == '4th Semester') {
                    $Acadyear = '2024-25';
                  }else{
                    $Acadyear = '2023-24';
                  } 
                  break;
                  case 'Post BSc-Nursing-2024':
                  case 'MSc-Nursing-2024':
             
                    if ($fee_trans->comp[0]->insName == '1st Semester') {
                      $Acadyear = '2024-25';
                    }else if($fee_trans->comp[0]->insName == '2nd Semester') {
                      $Acadyear = '2024-25';
                    }else if($fee_trans->comp[0]->insName == '3rd Semester') {
                      $Acadyear = '2025-26';
                    }
                    else if($fee_trans->comp[0]->insName == '4th Semester') {
                      $Acadyear = '2025-26';
                    }else{
                      $Acadyear = '2024-25';
                    } 
                break;
                case 'BSc-Nursing 2024':
                  if ($fee_trans->comp[0]->insName == 'First Year') {
                    $Acadyear = '2024-25';
                  }else if($fee_trans->comp[0]->insName == 'Second Year') {
                    $Acadyear = '2025-26';
                  }else if($fee_trans->comp[0]->insName == 'Third Year') {
                    $Acadyear = '2026-27';
                  }
                  else if($fee_trans->comp[0]->insName == 'Fourth Year') {
                    $Acadyear = '2028-29';
                  }else{
                    $Acadyear = '2024-25';
                  } 
                  break;
                case 'MSc-Nursing-2025':
                  if ($fee_trans->comp[0]->insName == '1st Semester') {
                    $Acadyear = '2025-26';
                  }else if($fee_trans->comp[0]->insName == '2nd Semester') {
                    $Acadyear = '2025-26';
                  }else if($fee_trans->comp[0]->insName == '3rd Semester') {
                    $Acadyear = '2026-27';
                  }
                  else if($fee_trans->comp[0]->insName == '4th Semester') {
                    $Acadyear = '2026-27';
                  }else{
                    $Acadyear = '2024-25';
                  } 
                  break;
              default:
                if ($fee_trans->comp[0]->insName == 'First Year') {
                  $Acadyear = '2020-21';
                }else if($fee_trans->comp[0]->insName == 'Second Year') {
                  $Acadyear = '2021-22';
                }else if($fee_trans->comp[0]->insName == 'Third Year') {
                  $Acadyear = '2022-23';
                }else{
                  $Acadyear = '2023-24';
                } 
                break;
            }
            ?>
          <div class="col-md-12" style="margin-bottom:2rem; <?php echo $borderBottom ?>" >
            <?php if ($cancel == 1) {
              echo "<h3 class='canceled'>Canceled</h3>";  
            } ?>
            <table class="table table-bordered bgColor"  style="margin-bottom: 0;background: #71e5ef !important;">
              <tr style="background: #34b6c1 !important;">
                 <td colspan="3" style="text-align: center; background: #34b6c1 !important;">   
                 <img height="90px" style="float: left;" class="logo" src="<?php echo base_url().'assets/img/matha-gugri.png' ?>">
                  <center><h2>MATA GUJRI UNIVERSITY</h3>
                  <h3>COLLEGE OF NURSING</h3>
                  <h6><?php echo $this->settings->getSetting('school_name_line1'); ?></h6>
                </center>
                </td>
              </tr>
              <tr>
                <td colspan="3" style="text-align: center; border-bottom: none; padding-top: 0;background: #71e5ef !important;"><span style="font-size: 18px; font-weight: 600;"><?php echo strtoupper($fee_trans->no_of_comp->blueprint_name) ?> - SESSION <?php echo $Acadyear ?> - FEES RECEIPT <?php echo $copy ?></span></td>
              </tr>
              <tr>
                <td style="background: #71e5ef !important;" ><strong>Transaction ID: </strong>NA</td>
                <td style="background: #71e5ef !important;"><strong>Receipt Date: </strong><?php echo date('d-m-Y', strtotime($fee_trans->paid_datetime)) ?></td>
                <td style="background: #71e5ef !important;"><b>Receipt No:</b> <?= $fee_trans->receipt_number;?></td>                
              </tr>
              <tr>
                <?php //echo '<pre>';print_r($fee_trans->student);die(); ?>
                <td style="background: #71e5ef !important;"><b>Student Name:</b> <?= $fee_trans->student->stdName;?></td>
                <td style="background: #71e5ef !important;"><b>Father Name:</b> <?= $fee_trans->student->fName;?></td>
                <td style="background: #71e5ef !important;"><b>Mother Name:</b> <?= $fee_trans->student->mName;?></td>
              </tr>
              <tr>
                <?php //echo '<pre>';print_r($fee_trans->student);die(); ?>
                <td style="background: #71e5ef !important;"><b>Student Mobile Number:</b> <?= $fee_trans->student->mobile_no;?></td>
                <td style="background: #71e5ef !important;" ><b>Address:</b> <?php 
                if (!empty($fee_trans->student_address)) {
                  echo $fee_trans->student_address->address;
                }else{
                   echo 'NA';
                }
                ?></td>
                 <td style="background: #71e5ef !important;" ><strong>Combination:</strong><?= $fee_trans->student->combination;?></td>
              </tr>
              <tr>
               
                <td style="background: #71e5ef !important;"><b>Admission No:</b> <?= $fee_trans->student->admission_no;?></td>
                <td style="background: #71e5ef !important;"><b>Course:</b> <?= $fee_trans->student->clsName;?></td>
                <td style="background: #71e5ef !important;"><b>Transaction Date:</b> 
                  <?php 
                    if ($fee_trans->reconciliation_status == 1) {
                      $recon = 'Not Reconciled';
                    }else if($fee_trans->reconciliation_status == 2){
                      $recon = date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date));
                    }else{
                      $recon = date('d-m-Y', strtotime($fee_trans->paid_datetime));
                    }
                    echo $recon ;
                  ?>
                </td>
              </tr>
             
            </table>
            <img class="background_logo" style="position: absolute;opacity: 0.1; right: 40%; height:180px" src="<?php echo base_url().'assets/img/matha-gugri.png' ?>">
             <table class="table table-bordered" style="margin-bottom: 0; border-bottom: none;background:#f1f1ac;">
                <tr style="background: #adad0a">
                  <th style="background: #adad0a !important;" width="10%">Sl#</th>
                  <th style="background: #adad0a !important;">Installment</th>
                  <th style="background: #adad0a !important;">Particulars</th>
                  <th style="background: #adad0a !important;">Amount</th>
                </tr>
              <?php  $noOfComponents = []; foreach ($fee_trans->comp as $key => $val) {
                if (!array_key_exists($val->insCompId, $noOfComponents)) {
                    $noOfComponents[$val->insCompId]=0;
                  }
                $noOfComponents[$val->insCompId]++;  
              } ?>
              <tbody>
                <?php
                $i=1; 
                $cnt= 0; 
                $sl=0;
                $totalAmount = 0;
                $trustAmount = 0;
                $comp_count = count($fee_trans->comp);
                foreach ($fee_trans->comp as $key => $val) {  
                    $totalAmount += $val->amount_paid + $val->concession_amount;
                    $total_amount_val = $val->amount_paid + $val->concession_amount;
                    ?>
                    <?php if($total_amount_val !='0') { ?>
                      <tr>
                        <td style="vertical-align: middle;background: #f1f1ac !important;"><?php echo $i++ ?></td>
                        <td style="background: #f1f1ac !important;"><?= $val->insName ?></td>
                        <td style="background: #f1f1ac !important;"><?php echo $val->compName ?></td>
                        <td style="background: #f1f1ac !important;"><?php echo  moneyFormatIndia($val->amount_paid + $val->concession_amount)  ?></td>
                      </tr>
                    <?php } ?>
                    
                <?php }
                  //Adding empty rows
                  $total_rows = 10;
                  for ($i=$i;$i<=$total_rows;$i++) {
                    echo '<tr>
                      <td style="background: #f1f1ac !important;"></td>
                      <td style="background: #f1f1ac !important;"></td>
                      <td style="background: #f1f1ac !important;"></td>
                      <td style="background: #f1f1ac !important;"></td>
                    </tr>';
                  }  
                ?>
                
              </tbody>
              <tfoot>
                <tr>
                  <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Total Fee</th>
                  <th style="background: #f1f1ac !important;"><?php echo moneyFormatIndia($totalAmount); ?></th> 
                </tr>

                <?php if ($fee_trans->concession_amount != 0) { ?>
                  <tr>
                    <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Scholarship/Concession (-)</th>
                    <th style="background: #f1f1ac !important;"><?php echo moneyFormatIndia($fee_trans->concession_amount) ?></th>
                  </tr>
                <?php } ?>
               
                
                <?php if ($fee_trans->discount_amount != 0) { ?>
                  <tr>
                    <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Discount (-)</th>
                    <th style="background: #f1f1ac !important;"><?php echo $fee_trans->discount_amount ?></th>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->fine_amount != 0) { ?>
                  <tr>
                    <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Fine Amount</th>
                    <th style="background: #f1f1ac !important;"><?php echo $fee_trans->fine_amount ?></th>
                  </tr>
                <?php } ?>

                <?php if ($fee_trans->card_charge_amount != 0) { ?>
                  <tr>
                    <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Card Charge Amount</th>
                    <th style="background: #f1f1ac !important;"><?php echo $fee_trans->card_charge_amount ?></th>
                  </tr>
                <?php } ?>
                <tr>
                  <th colspan="3" style="text-align: right;background: #f1f1ac !important;">Total amount paid</th>
                  <th style="background: #f1f1ac !important;">
                  <?php 
                    echo moneyFormatIndia($fee_trans->amount_paid + $fee_trans->fine_amount); ?>
                  </th> 
                </tr>
              </tfoot>
            </table>

            <table class="table no-border" style="width: 100%; border: 1px solid #ccc;margin-bottom:0;background: #f1f1ac;">
            <?php if($fee_trans->payment_type =='10'){
              echo "<tr>";
              echo "<td><b>Payment Type:</b> Online Payment</td>";
              echo "</tr>";
            }else{
               foreach ($payment_modes as $key => $type) {
                if ($type->value == $fee_trans->payment_type) { 
                  echo "<tr>";
                    if ($type->value == '1' || $type->value == '4') { ?>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Payment Type: </b>' .strtoupper($type->name); ?>
                        </td>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Drawn On: </b>' . date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)); ?>
                        </td>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Bank: </b>' .$fee_trans->bank_name . ' ' . $fee_trans->bank_branch; ?>
                        </td>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Number: </b>' .$fee_trans->cheque_dd_nb_cc_dd_number; ?>
                        </td>
                   <?php }elseif($type->value == '8' || $type->value == '2' || $type->value == '3' || $type->value == '11' || $type->value == '67'){ ?>
                      <td style="background: #f1f1ac !important;">
                          <?= '<b>Payment Type: </b>' .strtoupper(str_replace('_',' ',$type->name)); ?>
                        </td>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Drawn On: </b>' . date('d-m-Y',strtotime($fee_trans->cheque_or_dd_date)); ?>
                        </td>
                        <td style="background: #f1f1ac !important;">
                          <?= '<b>Reference Number: </b>' .$fee_trans->cheque_dd_nb_cc_dd_number; ?>
                        </td>
                    <?php }else{ ?>
                    <td style="background: #f1f1ac !important;">
                      <?= '<b>Payment Type: </b>' .strtoupper($type->name); ?>
                    </td>
                  <?php }
                  echo "</tr>";
                  ?>                     
                <?php } 
                  echo "</tr>";

              }
            } ?>
          </table>
            <table class="table table-bordered" style="margin-bottom: 0;background: #f1f1ac  !important;;">
            <tr>
              <td style="background: #f1f1ac !important;"><strong>Total Amount in Words: </strong><span id="words_amount<?php echo $m ?>"></span></td>
            </tr>
             <?php if (!empty($fee_trans->remarks)) { ?>
              <tr>
                <td style="background: #f1f1ac !important;"><strong>Remarks: </strong> <?php echo $fee_trans->remarks ?></td>
              </tr>
            <?php } ?>
          </table>
          <br>
          <p class="pull-right" style="margin-right:20px;">_________________________<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Accounts Officer</p>
          <?php if($m == 1){ ?>
            <br>
            <br>
          <?php } ?>
        </div>
      <?php } ?>
    </div>
  </div>
  </div>
</div>
<?php

function moneyFormatIndia($num){
     $pos = strpos((string)$num, ".");
     if ($pos === false) {
        $decimalpart="00";
     }
     if (!($pos === false)) {
        $decimalpart= substr($num, $pos+1, 2); $num = substr($num,0,$pos);
     }

     if(strlen($num)>3 & strlen($num) <= 12){
         $last3digits = substr($num, -3 );
         $numexceptlastdigits = substr($num, 0, -3 );
         $formatted = makeComma($numexceptlastdigits);
         $stringtoreturn = $formatted.",".$last3digits.".".$decimalpart ;
     }elseif(strlen($num)<=3){
        $stringtoreturn = $num.".".$decimalpart ;
     }elseif(strlen($num)>12){
        $stringtoreturn = number_format($num, 2);
     }

     if(substr($stringtoreturn,0,2)=="-,"){
        $stringtoreturn = "-".substr($stringtoreturn,2 );
     }

     return $stringtoreturn;
 }


 function makeComma($input){ 
   if(strlen($input)<=2)
   { return $input; }
   $length=substr($input,0,strlen($input)-2);
   $formatted_input = makeComma($length).",".substr($input,-2);
   return $formatted_input;
 }
?>
<script type="text/javascript">
  function close_window() {
    window.close();
  }
</script>

<style type="text/css">
  .canceled{
  position: absolute;
  z-index: 99999;
  top: 155px;
  left:0;
  transform: rotate(332deg);
  font-size: 120px;
  opacity: 0.1;
}

@media print {
body {-webkit-print-color-adjust: exact;}
}

@media print{
  .box-text {

    font-size: 27px !important; 
    color: blue !important;
    -webkit-print-color-adjust: exact !important;
  }
}

</style>

<script type="text/javascript">

  function print_receipt(){
    $('.col-md-6').css('width','50%');
    $('.col-md-6').css('float','right');
    $('.canceled').css('position', 'absolute');
    $('.canceled').css('z-index', '99999');
    $('.canceled').css('font-size','120px');
    $('.canceled').css('top','155px');
    $('.canceled').css('left','0');
    $('.canceled').css('transform','rotate(332deg)');
    $('.canceled').css('opacity','0.1');
    $('table tr td').css('padding','2px 6px');
    $('table tr th').css('padding','2px 6px');
    $('#forPNcc1').css('padding-bottom','10%');
    $('#cashier1').css('padding-bottom','10%');
    $('#forPNcc2').css('padding-bottom','10%');
    $('#cashier2').css('padding-bottom','10%');
    $('.bgColor tr td').css('background','#71e5ef !important');
    $('.background_logo').css('position','absolute');
    $('.background_logo').css('opacity','0.1');
    $('.background_logo').css('right','40%');
    $('.background_logo').css('height','180px');
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }
  

 $(document).ready(function() {
    var amount="<?php echo $fee_trans->amount_paid + $fee_trans->fine_amount?>";
    var words = new Array();
    words[0] = 'Zero';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }
        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crores ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakhs ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred " ; 
            }
        }

         words_string = words_string.split(" ").join(" ")  +"Rupees Only";
    }  
    $('#words_amount1').html(words_string);
    $('#words_amount2').html(words_string);
});     
</script>
