<div class="" id="form-container" style="margin-top:9%">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style text-center">
                <!-- <strong>Take Attendance</strong> -->
                <strong class="label label-danger" id="heading">Attendance</strong>
            </h3>
        </div>
        <div class="card-body" style="padding: 0px;min-height: 50vh;">
            <div class="attendanceInputFields">
                <div class="col-12">
                    <label class="pl-0" for="attendance">Attendance Date</label>
                    <div class="input-group date" id="attendance_date_picker">
                        <input required="" autocomplete="off" type="text" value="<?php echo date('d-m-Y'); ?>"
                            class="form-control" id="attendance_date" name="attendance_date">
                        <span class="input-group-addon">
                            <span class="glyphicon glyphicon-calendar"></span>
                        </span>
                    </div>
                </div>

                <div class="col-12 form-group">
                    <label class="control-label">Section</label>
                    <select required="" class="form-control" onchange="<?php if ($is_semester_scheme != 1) {
                        echo 'getSubjectOrSessions()';
                    } else {
                        echo 'getSemesters()';
                    } ?>" name="class_section_id" id="class_section_id">
                        <option value="0">Select section</option>
                        <?php
                        foreach ($sections as $key => $section) {
                            $is_enabled = 'disabled style="color: lightgray"';
                            if ($section->is_section_enabled) {
                                $is_enabled = '';
                            }
                            $selected = ($section->id == $selected_section_id) ? 'selected' : '';
                            if ($is_semester_scheme == 1) {
                                echo '<option ' . $is_enabled . ' value="' . $section->id . '" ' . $selected . '><span>' . $section->class_name . $section->section_name . '</span></option>';
                            } else {
                                echo '<option ' . $is_enabled . ' value="' . $section->id . '"' . $selected . '>' . $section->class_name . $section->section_name . '</option>';
                            }
                            // echo '<option value="'.$section->id.'">'.$section->class_name.$section->section_name.'</option>';
                        }
                        ?>
                    </select>
                </div>

                <?php if ($is_semester_scheme == 1) { ?>
                    <div class="col-md-2 form-group">
                        <label class="control-label">Semester</label>
                        <select class="form-control" id="class_semester_id" onchange="getSubjectOrSessions()">
                            <option value="0">Select Semester</option>

                        </select>
                    </div>
                <?php } ?>


            </div>

            <div class="col-12 mt-4">
                <!-- <button class="btn btn-block btn-primary">Get Students</button> -->
            </div>
            <!-- <div class="d-flex justify-content-between">
                <span><b><i class="fa fa-calendar" style="color: #6893ca;"></i>&nbsp;&nbsp;<?php //echo $date; ?></b></span>
                <span><b><?php //echo $section->class_name . $section->section_name; ?>: <?php //echo $attendance_type_name; ?></b></span>
            </div> -->


            <div class="mt-3">
                <?php if ($isSortingEnabled) { ?>
                    <div id="student-filter" style="width: 17rem;display: none;padding: 0 1rem 1rem 1rem;">
                        <label for="active-student-filter-type">Choose Student Filter</label>
                        <select class="form-control select2" name="" id="active-student-filter-type"
                            onchange="filterStudents()">
                            <option selected value="default" class="student-filter-option">
                                <?php echo $prefix_student_name_formatted ?>
                            </option>
                            <option value="first_name" class="student-filter-option">Student Name</option>
                        </select>
                    </div>
                <?php } ?>

                <div id="info-block">

                </div>
                <div id="student-list">

                </div>
            </div>


            <div class="periods-table" id="periods-table" style="overflow: auto;">
                <div id="period_template">

                </div>
            </div>
            <div class="col-12 text-center loading-icon" id="loading_icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            </div>
        </div>
        <div class="color_codes" style="display: flex;padding-bottom: 10px;justify-content:space-around">
            <div style="background:#aba3a3;height: 37px;">
                <p class="attendanceColorNames" style="padding: 5px;">Not Taken</p>
            </div>
            <div style="background:#46a946;height: 37px;">
                <p class="attendanceColorNames" style="padding: 5px;">Present</p>
            </div>
            <div style="background:#dd6d6d;height: 37px;">
                <p class="attendanceColorNames" style="padding: 5px;">Absent</p>
            </div>
            <div style="background:#ddc72b;height: 37px;">
                <p class="attendanceColorNames" style="padding: 5px;">Late</p>
            </div>
        </div>
    </div>
</div>

<div class="visible-xs">
    <a href="<?php echo site_url('attendance_v2/attendance/take_attendance'); ?>" id="backBtn" onclick="loader()"><span
            class="fa fa-mail-reply"></span></a>
</div>

<div class="modal fade" id="audit-modal" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="margin: auto;">
            <div class="modal-header">
                <h4 class="modal-title">History of <b id="student-name"></b></h4>
                <button type="button" class="close" data-dismiss="modal" style="font-size: 2rem;"><i
                        class="fa fa-times"></i></button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Status</th>
                            <th>Action By</th>
                            <th>Action On</th>
                        </tr>
                    </thead>
                    <tbody id="audit-data">

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="notify-modal" role="dialog">
    <div class="modal-dialog">
        <form id="student-messages-form">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Notify Absentees & Late-comers by SMS/Notification</h4>
                    <!-- <button type="button" class="close" data-dismiss="modal" style="font-size: 2rem;"><i class="fa fa-times"></i></button> -->
                </div>
                <div class="modal-body">
                    <input type="hidden" id="notify-attendance-master-id" value="0">
                    <div class="form-group" style="font-size: 16px;">
                        <!-- <label class="control-label mr-3">Mode </label> -->

                        <?php if ($notification_mode == 'notif-only' || $notification_mode == 'both') { ?>
                            <label class="radio-inline" for="notification">
                                <input style="height: 18px; width: 18px;" type="radio" name="communication_mode"
                                    id="notification" value="notification" <?php echo ($notification_mode == 'notif-only' || $notification_mode == 'both') ? 'checked=""' : '' ?>>&nbsp;Notification
                            </label>
                        <?php } ?>

                        <?php if ($notification_mode == 'sms-only' || $notification_mode == 'both') { ?>
                            <label class="radio-inline" for="sms">
                                <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="sms"
                                    value="sms" <?php echo ($notification_mode == 'sms-only') ? 'checked=""' : '' ?>>&nbsp;SMS
                            </label>
                        <?php } ?>

                        <select id="text_send_to" class="form-control" name="text_send_to" style="display:none;">
                            <option value="Father">Father</option>
                            <option value="Mother">Mother</option>
                            <option value="Both">Both</option>
                            <option value="preferred">Preferred Parent</option>
                        </select>
                    </div>
                    <div class="table-responsive" id="notify-content" style="overflow-y:auto;max-height:450px;">

                    </div>
                </div>
                <div class="modal-footer">
                    <button style="width: 120px;" type="button" class="btn btn-warning my-0"
                        data-dismiss="modal">Cancel</button>
                    <button id="confirmBtn" onclick="send_messages()" style="width: 120px;" type="button"
                        class="btn btn-primary my-0">Confirm</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
    integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script type="text/javascript">
    let period_no = "";
    let classSectionId
    let isEdit = 0
    var attendance_type = 'subject_wise';
    var date = '';
    var section_id = 0;
    var attendance_type_id = 0;
    var attendance_type_name = '';
    var section_name = '';
    let absent_message = ``;
    let late_message = ``;
    let enable_notification = 0;
    const can_edit_all_subjects = "<?php echo $can_edit_all_subjects; ?>";

    $(document).ready(function () {
        // date = '<?php //echo $date; ?>';
        enable_notification = <?php echo $enable_notification ?>;
        // attendance_type_name = '<?php //echo $attendance_type_name; ?>';
        absent_message = '<?php echo $notification_absentee_message; ?>';
        late_message = '<?php echo $notification_late_message; ?>';
        // attendance_type = '<?php //echo $attendance_type; ?>';
        // section_id = '<?php //echo $section_id; ?>';
        // attendance_type_id = '<?php //echo $attendance_type_id; ?>';
        section_name = '<?php echo $section->section_name; ?>';
        // takeAttendance();
        $(".color_codes").hide()
    });

    $(function () {
        const can_staff_take_previous_date_attendance = "<?php echo $can_staff_take_previous_date_attendance; ?>";
        $('#attendance_date, #attendance_date_picker').datepicker({
            format: 'dd-mm-yyyy',
            autoclose: true,
            endDate: new Date(),
            startDate: `${can_staff_take_previous_date_attendance == 0 ? "-0m" : ""}`
        }).on('changeDate', function (ev) {
            $("#info-block").html('');
            $("#student-list").html('');
        });
    });

    // gather students data
    let studentsData = [];
    let subjectsData;
    let allStudentsData = {};

    function filterStudents() {
        const students = JSON.parse(JSON.stringify(studentsData));;
        const filterType = $("#active-student-filter-type").val();

        if (filterType !== "default") {
            students.sort((a, b) => a[filterType].localeCompare(b[filterType]));
        }

        destroyStudentList();

        const { type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type } = allStudentsData;

        constructStudentList(type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type, students, subjectsData);
    }

    function destroyStudentList() {
        const stdentLists = document.querySelectorAll(".student-name-list");
        const stdentAttenLists = document.querySelectorAll(".student-attendance-list");

        if (stdentLists.length) {
            stdentLists.forEach(s => {
                s.remove();
            })
        }

        if (stdentAttenLists.length) {
            stdentAttenLists.forEach(s => {
                s.remove();
            })
        }
    }

    function constructStudentList(type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type, students, subject) {
        $("#student-filter").show();;

        $("#bottom-back-button").html('<a onClick="goBack();"><i class="fa fa-chevron-left" aria-hidden="true"></i><p>Back</p></a>')

        $("#student-list").show()
        isEdit = is_edit
        period_no = period_no

        $("#periods-tables").hide();
        $("#periods-table").hide();
        $(".color_codes").hide()
        $(".attendanceInputFields").hide()
        $("#info-block").html('');
        var date = $("#attendance_date").val();
        var section_id = $("#class_section_id").val();
        var section_name = $("#class_section_id option:selected").text();
        // var type_id = 0;
        var type_name = '';
        if (!isElective) {
            if (attendance_type == 'subject_wise') {
                // type_id = $("#subject_id_" + period_no).val();
                type_name = $("#subject_id_" + period_no + "_" + type_id + " option:selected").text();
            } else {
                type_id = $("#session_id").val();
                type_name = $("#session_id option:selected").text();
            }
        } else {
            type_id = $("#elective_subjects").val()
            type_name = $("#elective_subjects option:selected").text()
        }

        var parameter_is_elective = (isElective ? 1 : 0);

        var html = `
                <h3 style="text-align: center;font-weight: 600;">Subject : ${subject}</h3>
                <form id="attendance-form"><div class="d-flex justify-content-end mb-2" style="width: 100%;">
                    <button class="btn btn-success" id="save-attendance-btn" style="margin: 5px;" onclick="saveAttendance('${parameter_is_elective}','${timetable_id}','${type_id}')" type="button">Save</button>
                </div>
                <input type="hidden" name="source" value="Manual">`;
        html += `<div class="d-flex" style="max-height: 500px;overflow: auto;">
                 <input type="hidden" id="period_no" name="period_no" value="${period_no}">
                <input type="hidden" id="timetable_id" name="timetable_id" value="${timetable_id}">
                <input type="hidden" id="isElective" name="isElective" value="${isElective}">
                <input type="hidden" name="is_edit" value="${is_edit}" />
                <input type="hidden" name="attendance_master_id" value="${attendance_master_id}" />
                <input type="hidden" name="attendance_type" value="${attendance_type}" />
                <input type="hidden" name="attendance_type_id" value="${type_id}" />
                <input type="hidden" name="section_id" value="${section_id}" />
                <input type="hidden" name="subject_name" value="${subject}" />
                <input type="hidden" name="date" value="${date}" />`;
        html += `<table class="table table-bordered">
              <thead><tr><th class="text-center">Not Taken (<span id="not-taken-count"></span>)</th><th class="text-center">Present (<span id="present-count"></span>)</th><th class="text-center">Absent (<span id="absent-count"></span>)</th><th class="text-center">Late (<span id="late-count"></span>)</th><th class="text-center">Absent With Permission(<span id="absent-by-permission-count"></span>)</th></thead><tbody>
              <thead><th class="text-center" id="not-taken-all"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="present-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="absent-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="late-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th class="text-center" id="absent-by-permission-all"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th></thead>`;
        for (var i = 0; i < students.length; i++) {
            var student_id = students[i].student_id;
            var status = (is_edit) ? students[i].status : 0;
            html += `<tr class="student-name-list">
                    <td colspan="4">${i + 1}. ${students[i].student_name}`;
            if (is_edit) {
                html += `<button style="float: right;" type="button" id="att-${students[i].student_attendance_id}" data-student_id="${student_id}" data-student_name="${students[i].student_name}" onclick="getAttendanceAudit(${students[i].student_attendance_id})" class="btn btn-primary"><i class="fa fa-eye m-0"></i></button>`;
            }
            html += `<input type="hidden" id="old-status-${student_id}" name="old_status[${student_id}]" data-student_id="${student_id}" class="old-status" value="${status}"/>
                    <input type="hidden" id="new-status-${student_id}" name="new_status[${student_id}]" data-student_id="${student_id}" class="new-status" value="${status}"/>
                    </td>
                  </tr>
                  <tr class="student-attendance-list">
                    <td style="border-bottom: 2px solid #555;" data-status="0" data-student_id="${student_id}" class="att-status not-taken ${(status == 0) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 0) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="1" data-student_id="${student_id}" class="att-status present ${(status == 1) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 1) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="2" data-student_id="${student_id}" class="att-status absent ${(status == 2) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 2) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="3" data-student_id="${student_id}" class="att-status late ${(status == 3) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 3) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="4" data-student_id="${student_id}" class="att-status absent-by-permission ${(status == 4) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 4) ? 'checked' : ''}/></td>
                  </tr>`;
        }
        html += `</tbody></div></form>`;
        $("#student-list").html(html);
        updateCounts();

        $(".att-status").click(function () {
            var student_id = $(this).data('student_id');
            var status = $(this).data('status');
            $("#new-status-" + student_id).val(status);
            var tr = $(this).parent();
            var input = $(this).children();
            input.prop('checked', true);
            // tr.children('.att-status').html('');
            tr.children('.att-status').removeClass('active');
            $(this).addClass('active');
            updateCounts();
            // $(this).html('<i class="fa fa-check"></i>');
        });

        $("#not-taken-all").click(function () {
            $(".not-taken").click();
            $("#not-taken-all>input").prop('checked', true);
        });
        $("#present-all").click(function () {
            // $(".present").click();
            $(".not-taken.active").siblings(".present").click();
            $("#present-all>input").prop('checked', true);
        });
        $("#absent-all").click(function () {
            // $(".absent").click();
            $(".not-taken.active").siblings(".absent").click();
            $("#absent-all>input").prop('checked', true);
        });
        $("#late-all").click(function () {
            $(".not-taken.active").siblings(".late").click();
            $("#late-all>input").prop('checked', true);
        });
        $("#absent-by-permission-all").click(function () {
            $(".not-taken.active").siblings(".absent-by-permission").click();
            $("#absent-by-permission-all>input").prop('checked', true);
        });
    }

    $("#student-messages-form").click(e => {
        const msgType = e.target;
        if (msgType.type === "radio") {
            if (msgType.id === "sms") {
                $("#text_send_to").css("display", "inline-block");
            } else {
                $("#text_send_to").css("display", "none");
            }
        }
    })

    const msg = `<div style="color:red;text-align:center;
    color: #000;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 15px;
    background: #ebf3ff;">
    Choose Section To Proceed
    </div>`;

    const noTemplateMsg = `
  <div style="color:red;text-align:center;
  color: #000;
  border: 2px solid #fffafa;
  text-align: center;
  border-radius: 6px;
  position: relative;
  margin-left: 14px;
  padding: 10px;
  font-size: 14px;
  margin-top: 15px;
  background: #ebf3ff;">
  Template Not Available, Please Create The Attendance Template To Proceed
  </div>`

    getSubjectOrSessions();

    function getSubjectOrSessions() {
        $('#period_template').html('');
        $(".attendanceInputFields").show()
        const currentDate = $("#attendance_date").val().split("-").reverse().join("-")
        let date = currentDate.split(" ").splice(0, 5).join(" ")
        const weekDay = new Date(date).getDay()
        var section_id = $("#class_section_id").val();
        classSectionId = section_id
        $("#periods-table").show()
        $("#periods-tables").show()
        $(".color_codes").show()
        $("#display_msg").html('')

        if (attendance_type == 'subject_wise') {
            $("#subject_id").html('<option value="0">Select subject</option');
        } else {
            $("#session_id").html('<option value="0">Select session</option');
        }
        if (section_id == 0) {
            return false;
        }

        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/getSectionSubjectsORSessions'); ?>',
            type: 'post',
            data: {
                'section_id': section_id,
                'attendance_type': attendance_type,
                "weekDay": weekDay,
                "date": currentDate
            },
            beforeSend: function () {
                $('#loading_icon').show();
                $(".color_codes").hide();
            },
            complete: function () {
                $('#loading_icon').hide();
                $(".color_codes").show();
            },
            success: function (data) {
                data = JSON.parse(data);
                const display_array = data.display_list;
                const subjects = data.subjects;

                if (display_array.length) {
                    let period_templates = `<table class="table table-bordered text-center" id="periods-tables">
                    <tr class="bg-light"></tr>`;

                    display_array.forEach((period, i) => {
                        const startHour = moment(period.start_time, "HH:mm").format("h:mm A");
                        const endHour = moment(period.end_time, "HH:mm").format("h:mm A");

                        if (!period.is_taken) {
                            period_templates += `<tr>
                                 <div class="card" style="margin: 10px 10px 46px 10px;box-shadow: 0px 0px 10px #725c5c;border-radius: 10px;">
                                 <div class="card-body" style="border-radius: 5px;">
                                 <div class="information d-flex" style="justify-content: space-between">
                                  <p class="label label-warning" style="font-size: 10px;">${startHour} <br>To<br> ${endHour}</p>
                                 </div>
                       <div style="text-left: center;font-weight: 600;">
                         <p style="font-size: 15px;"><span class="label label-danger" id="${period.short_name}" value="${period.short_name}">${period.short_name}</span></p>
                       </div>
                       <div style="">
                         <div id="subject-wise" class="">
                           <select class="form-control subject_id" id="subject_id_${period.short_name}" style="height: 35px;font-size: 15px;border-radius: 5px;" onchange="enableAttendance('${period.short_name}')">
                             <option value="0"> </option>
                           </select>
                         </div>
                       </div>
                         <div class="" style="">
                           <button onclick="takeAttendance('${period.id}','${period.short_name}',${false},'','${startHour}','${endHour}')" disabled id="get-students-btn_${period.short_name}" class="btn btn-primary" style="width: 100%;padding: 10px 0;margin: 15px 0 0 0;">Take Attendance</button>
                         </div>
                       </div>
                       </div>
                     </tr>`
                        } else {
                            let getAttendanceBtn = `<button onclick="getAttendanceStatus('${period.class_section_id}','${period.type_id}','${period.short_name}')" id="getAttendanceStatus_${period.short_name}_${period.type_id}" class="btn btn-info getAttendanceStatus" style="width: 100%;padding: 10px 0;">Get</button>`

                            const sendNotification = `<button onclick="notify_students('${period.id}','${period.short_name}','${period.type_id}','','${startHour}','${endHour}')" style="width: 100%;padding: 10px 0;margin: 10px 0 0 0;" class="btn btn-info btn-block">Send</button>`

                            let editPermission = '<?php echo $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.EDIT_ATTENDANCE_SUBJECTWISE') ?>';

                            let editBtn = `<button ${editPermission == 0 && "disabled"} onclick="getStudents('${period.type_id}',false,'${period.ttp_id}','${period.short_name}','1','${period.id}','${startHour}','${endHour}')" style="padding: 10px 0;" class="btn btn-warning btn-block">Edit</button>`

                            period_templates += `<tr>
                                 <div class="card" style="margin: 10px 10px 46px 10px;box-shadow: 0px 0px 10px #725c5c;border-radius: 10px;">
                                 <div class="card-body" style="border-radius: 5px;">
                                 <div class="information d-flex" style="justify-content: space-between">
                                  <p class="label label-warning" style="font-size: 10px;">${startHour} To ${endHour}</p>
                                  <p class="label label-danger" style="font-size: 10px;" id="taken_by_${period.short_name}">${period.taken_by_name}</p>
                                  <p class="label label-success" style="font-size: 10px;" id="taken_on_${period.short_name}">${period.take_on_local}</p>
                                 </div>
                       <div style="text-left: center;font-weight: 600;">
                         <p style="font-size: 15px;"><span class="label label-danger" id="${period.short_name}" value="${period.short_name}">${period.short_name}</span></p>
                       </div>
                       <div style="">
                         <div id="subject-wise" class="">
                           <select class="form-control" id="subject_id_${period.short_name}_${period.type_id}" style="height: 35px;font-size: 15px;border-radius: 5px;" onchange="enableAttendance('${period.short_name}')">
                             <option value="0">Subject removed for this Grade</option>
                           </select>
                         </div>
                       </div>

                       <div id="attendance_status" style="text-align: center;margin: 15px 0 0 0;">
                         <div id="getAttendence_${period.short_name}">${getAttendanceBtn}</div>
                         <label style="background: #aba3a3;font-size:15px;" class="label label-control" id="not_taken_${period.short_name}_${period.type_id}"></label>
                         <label style="background: #46a946;font-size:15px;" class="label label-control" id="present_${period.short_name}_${period.type_id}"></label>
                         <label style="background: #dd6d6d;font-size:15px;" class="label label-control" id="absent_${period.short_name}_${period.type_id}"></label>
                         <label style="background: #ddc72b;font-size:15px;" class="label label-control" id="late_${period.short_name}_${period.type_id}"></label>
                        <label style="background: #46a946;font-size:15px;" class="label label-control" id="absent_by_permission_${period.short_name}_${period.type_id}"></label>
                       </div>
                       <div style="">
                         <div id="send_${period.short_name}">${sendNotification}</div>
                       </div>
                       <div class="" style="">
                         <div id="edit_${period.short_name}" style="width: 100%;margin: 15px 0 0 0;">${can_edit_all_subjects==1 && editBtn || period.login_staff_id == 0 && editBtn || period.taken_by == period.login_staff_id && editBtn || ""}</div>
                       </div>
                       <div id="take_elective_${period.short_name}_${period.type_id}"></div>
                       </div>
                       </div>
                     </tr>`
                        }
                    })

                    period_templates += `</table>`
                    $("#period_template").html(period_templates)

                    display_array.forEach((period, i) => {
                        if (period.is_taken) {
                            subjects.forEach((subject, i) => {
                                if (subject.id == period.type_id) {
                                    $(`#subject_id_${period.short_name}_${period.type_id}`).html(`<option value=${period.type_id}>${subject.subject_name}</option>`).prop("disabled", true)
                                }

                                if (period.elective.length) {
                                    let take_elective = `<button type="button" style="margin: 10px 0 0 0;padding: 10px 0;" data-toggle="modal" data-target="#getElectiveSubjects_modal" 
                                    data-class_section_id="${period.class_section_id}" data-date="${period.date}" data-period_no="${period.short_name}" data-ttp_id="${period.ttp_id}" data-elective_master_group_id="${period.elective[0].elective_master_group_id}" data-class_master_id="${subjects[i].class_master_id}" data-type_id="${period.type_id}" class="btn btn-danger btn-block">Take Elective</button>`
                                    $(`#take_elective_${period.short_name}_${period.type_id}`).html(take_elective)
                                }
                            })
                        }
                    })
                }
                if (data.attendance_type == 'subject_wise') {
                    if ('<?php echo $is_semester_scheme; ?>' == '1') {
                        getSemesterSubjectOrSessions();
                    } else {
                        var options = '<option value="0">Select subjects</option>';
                        for (var i = 0; i < subjects.length; i++) {
                            options += `<option ${subjects[i].is_subject_enabled == 1 ? '' : 'disabled style="color: lightgray"'} value="${subjects[i].id}">${subjects[i].subject_name}</option>`;
                            $(".subject_id").html(options);
                        }
                    }
                }
            }
        });
    }

    function getSemesters() {
        var class_section_id = $("#class_section_id").val();
        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/get_class_semesters') ?>',
            type: 'post',
            data: { 'class_section_id': class_section_id },
            success: function (data) {
                var data = $.parseJSON(data);
                var semesters = data.semesters;
                var options = '<option value="">Select Semester</option>';
                for (var i = 0; i < semesters.length; i++) {
                    options += `<option value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
                }
                $("#class_semester_id").html(options);
            }
        });
    }

    function getSemesterSubjectOrSessions() {
        var class_master_id = $("#class_section_id").val();
        var semester_main_screen_id = $("#class_semester_id").val() || '';
        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/get_subjects') ?>',
            type: 'post',
            data: { 'class_master_id': class_master_id, 'semester_main_screen_id': semester_main_screen_id },
            success: function (data) {
                var data = $.parseJSON(data);
                var viewSubjects = data.viewSubjects;
                // console.log(viewSubjects);

                if (viewSubjects.length) {
                    var options = `<option value="0">Select Subject</option>`;
                    for (var v of viewSubjects) {
                        options += `<option ${v.is_subject_enabled == 1 ? '' : 'disabled style="color: lightgray"'} value="${v.subject_master_id}">${v.subject_name}</option>`;
                    }
                    $(".subject_id").html(options);

                }

            }
        });
    }

    function getAttendanceStatus(classSectionId, subjectId, period_no) {
        var date = $("#attendance_date").val();
        date = date.split("-").reverse().join("-")
        $(`#getAttendanceStatus_${period_no}_${subjectId}`).css("display", "none");

        $.ajax({
            url: "<?php echo site_url('attendance_v2/attendance/getAttendanceStatus') ?>",
            type: "POST",
            data: {
                "classSectionId": classSectionId,
                "subjectId": subjectId,
                "period_no": period_no,
                "date": date
            },
            success: function (data) {
                data = JSON.parse(data);
                data.forEach((data, i) => {
                    $(`#not_taken_${period_no}_${subjectId}`).text(`${data.not_taken}`)
                    $(`#present_${period_no}_${subjectId}`).text(`${data.present}`)
                    $(`#absent_${period_no}_${subjectId}`).text(`${data.absent}`)
                    $(`#late_${period_no}_${subjectId}`).text(`${data.late}`)
                    $(`#absent_by_permission_${period_no}_${subjectId}`).text(`${data.absent_by_permission}`)
                })
            }
        })
    }

    function enableAttendance(periodNo) {
        $(`#get-students-btn_${periodNo}`).text("Take Attendance").prop("disabled", false)
    }

    function takeAttendance(timetable_id, period_name, isElective = false, electiveTypeId, startHour, endHour) {
        window.sessionStorage.setItem("periodStartHour", startHour);
        window.sessionStorage.setItem("periodEndHour", endHour);
        window.sessionStorage.setItem("isFirstTimeAttendanceTaken", false);

        resetStudentFilter();


        isEdit = 0
        period_no = period_name

        let isPeriodExist = checkPeriodExists(period_no, classSectionId, timetable_id);

        isPeriodExist.then(data => {
            if (data.length) {
                if (!isElective) {
                    $("#attendance-form").hide();
                    $("#student-filter").hide();;
                    getSubjectOrSessions()
                    $("#periods-tables").show()
                    $("#periods-table").show()
                    // return bootbox.alert("Already Taken Attendance For Period " + period_no)
                    return bootbox.confirm({
                        title: 'ALERT',
                        message: 'Attendance already taken for this period ' + period_no,
                        buttons: {
                            cancel: {
                                label: '<i class="fa fa-times" style="display:none">Cancel</i>'
                            },
                            confirm: {
                                label: '<i class="fa fa-check"></i> OK'
                            }
                        },
                        callback: function (result) {
                            console.log('This was logged in the callback: ' + result);
                        }
                    });
                }
            }
            $("#periods-tables").hide()
            $("#periods-table").hide()
            $("#info-block").html('');
            $("#student-list").html('');

            $(".color_codes").hide()
            $(".attendanceInputFields").hide()

            var date = $("#attendance_date").val();
            var section_id = $("#class_section_id").val();
            if (section_id == 0) return false;
            var section_name = $("#class_section_id option:selected").text();
            var type_name = '';
            if (attendance_type == 'subject_wise') {
                var type_id = $("#subject_id_" + period_no).val();
                if (isElective) {
                    type_id = electiveTypeId;
                }
                type_name = $("#subject_id" + period_no + " option:selected").text();
            } else {
                var type_id = $("#session_id").val();
                type_name = $("#session_id option:selected").text();
            }
            if (!type_id) return false;

            $.ajax({
                url: '<?php echo site_url('attendance_v2/attendance/checkAttendanceTaken'); ?>',
                type: 'post',
                data: {
                    'date': date,
                    'section_id': section_id,
                    'type_id': type_id,
                    'type': attendance_type,
                    "timetable_id": timetable_id
                },
                success: function (data) {
                    data = JSON.parse(data);
                    var startHour = window.sessionStorage.getItem("periodStartHour", startHour);
                    var endHour = window.sessionStorage.getItem("periodEndHour", endHour);
                    getStudents(type_id, isElective, timetable_id, period_no, 0, 0, startHour, endHour);
                }
            });
        })
    }

    function resetStudentFilter() {
        // reset the student filter default to selected
        const studentFilterOptions = document.querySelectorAll(".student-filter-option");
        studentFilterOptions.forEach(o => {
            o.removeAttribute("selected");
        });

        for (let i = 0; i < studentFilterOptions.length; i++) {
            if (studentFilterOptions[i].getAttribute("value") === "default") {
                studentFilterOptions[i].setAttribute("selected", true);
                break;
            }
        }
    }

    function getStudents(type_id, isElective, timetable_id = "0", period_no = "0", is_edit = 0, attendance_master_id = 0, startHour, endHour) {
        window.sessionStorage.setItem("periodStartHour", startHour);
        window.sessionStorage.setItem("periodEndHour", endHour);

        resetStudentFilter();

        $("#student-filter").show();;

        $("#bottom-back-button").html('<a onClick="goBack();"><i class="fa fa-chevron-left" aria-hidden="true"></i><p>Back</p></a>')

        $("#student-list").show()
        isEdit = is_edit
        period_no = period_no

        $("#periods-tables").hide();
        $("#periods-table").hide();
        $(".color_codes").hide()
        $(".attendanceInputFields").hide()
        $("#info-block").html('');
        var date = $("#attendance_date").val();
        var section_id = $("#class_section_id").val();
        var section_name = $("#class_section_id option:selected").text();
        // var type_id = 0;
        var type_name = '';
        if (!isElective) {
            if (attendance_type == 'subject_wise') {
                // type_id = $("#subject_id_" + period_no).val();
                type_name = $("#subject_id_" + period_no + "_" + type_id + " option:selected").text();
            } else {
                type_id = $("#session_id").val();
                type_name = $("#session_id option:selected").text();
            }
        } else {
            type_id = $("#elective_subjects").val()
            type_name = $("#elective_subjects option:selected").text()
        }

        $("#heading").text((is_edit ? 'Editing Attendance' : 'Taking Attendance'));
        $("#info-block").html('');

        allStudentsData = { type_id, isElective, timetable_id, period_no, is_edit, attendance_master_id, startHour, endHour, date, section_id, section_name, type_name, attendance_type }


        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/getSubjectSectionStudents'); ?>',
            type: 'post',
            data: {
                'date': date,
                'section_id': section_id,
                'attendance_type': attendance_type,
                'attendance_type_id': type_id,
                'attendance_master_id': attendance_master_id,
                'period_no': period_no,
            },
            success: function (data) {
                data = JSON.parse(data);
                var students = data.students;
                const subject = data.subject;

                studentsData = students;
                subjectsData = subject;

                var parameter_is_elective = (isElective ? 1 : 0);

                var html = `
                <h3 style="text-align: center;font-weight: 600;">Subject : ${subject}</h3>
                <form id="attendance-form"><div class="d-flex justify-content-end mb-2" style="width: 100%;">
                    <button class="btn btn-success" id="save-attendance-btn" style="margin: 5px;" onclick="saveAttendance('${parameter_is_elective}','${timetable_id}','${type_id}')" type="button">Save</button>
                </div>
                <input type="hidden" name="source" value="Manual">`;
                html += `<div class="d-flex" style="max-height: 500px;overflow: auto;">
                 <input type="hidden" id="period_no" name="period_no" value="${period_no}">
                <input type="hidden" id="timetable_id" name="timetable_id" value="${timetable_id}">
                <input type="hidden" id="isElective" name="isElective" value="${isElective}">
                <input type="hidden" name="is_edit" value="${is_edit}" />
                <input type="hidden" name="attendance_master_id" value="${attendance_master_id}" />
                <input type="hidden" name="attendance_type" value="${attendance_type}" />
                <input type="hidden" name="attendance_type_id" value="${type_id}" />
                <input type="hidden" name="section_id" value="${section_id}" />
                <input type="hidden" name="subject_name" value="${subject}" />
                <input type="hidden" name="date" value="${date}" />`;
                html += `<table class="table table-bordered">
              <thead><tr><th class="text-center">Not Taken (<span id="not-taken-count"></span>)</th><th class="text-center">Present (<span id="present-count"></span>)</th><th class="text-center">Absent (<span id="absent-count"></span>)</th><th class="text-center">Late (<span id="late-count"></span>)</th><th class="text-center">Absent With Permission(<span id="absent-by-permission-count"></span>)</th></thead><tbody>
              <thead><th class="text-center" id="not-taken-all"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="present-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="absent-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th id="late-all" class="text-center"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th><th class="text-center" id="absent-by-permission-all"><input type="radio" name="select_all" style="width:1.5rem;height:1.5rem;"/></th></thead>`;
                for (var i = 0; i < students.length; i++) {
                    var student_id = students[i].student_id;
                    var status = (is_edit) ? students[i].status : 0;
                    html += `<tr class="student-name-list">
                    <td colspan="4">${i + 1}. ${students[i].student_name}`;
                    if (is_edit) {
                        html += `<button style="float: right;" type="button" id="att-${students[i].student_attendance_id}" data-student_id="${student_id}" data-student_name="${students[i].student_name}" onclick="getAttendanceAudit(${students[i].student_attendance_id})" class="btn btn-primary"><i class="fa fa-eye m-0"></i></button>`;
                    }
                    html += `<input type="hidden" id="old-status-${student_id}" name="old_status[${student_id}]" data-student_id="${student_id}" class="old-status" value="${status}"/>
                    <input type="hidden" id="new-status-${student_id}" name="new_status[${student_id}]" data-student_id="${student_id}" class="new-status" value="${status}"/>
                    </td>
                  </tr>
                  <tr class="student-attendance-list">
                    <td style="border-bottom: 2px solid #555;" data-status="0" data-student_id="${student_id}" class="att-status not-taken ${(status == 0) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 0) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="1" data-student_id="${student_id}" class="att-status present ${(status == 1) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 1) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="2" data-student_id="${student_id}" class="att-status absent ${(status == 2) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 2) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="3" data-student_id="${student_id}" class="att-status late ${(status == 3) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 3) ? 'checked' : ''}/></td>
                    <td style="border-bottom: 2px solid #555;" data-status="4" data-student_id="${student_id}" class="att-status absent-by-permission ${(status == 4) ? 'active' : ''}"><input type="radio" name="att_${student_id}" ${(status == 4) ? 'checked' : ''}/></td>
                  </tr>`;
                }
                html += `</tbody></div></form>`;
                $("#student-list").html(html);
                updateCounts();

                $(".att-status").click(function () {
                    var student_id = $(this).data('student_id');
                    var status = $(this).data('status');
                    $("#new-status-" + student_id).val(status);
                    var tr = $(this).parent();
                    var input = $(this).children();
                    input.prop('checked', true);
                    // tr.children('.att-status').html('');
                    tr.children('.att-status').removeClass('active');
                    $(this).addClass('active');
                    updateCounts();
                    // $(this).html('<i class="fa fa-check"></i>');
                });

                $("#not-taken-all").click(function () {
                    $(".not-taken").click();
                    $("#not-taken-all>input").prop('checked', true);
                });
                $("#present-all").click(function () {
                    // $(".present").click();
                    $(".not-taken.active").siblings(".present").click();
                    $("#present-all>input").prop('checked', true);
                });
                $("#absent-all").click(function () {
                    // $(".absent").click();
                    $(".not-taken.active").siblings(".absent").click();
                    $("#absent-all>input").prop('checked', true);
                });
                $("#late-all").click(function () {
                    $(".not-taken.active").siblings(".late").click();
                    $("#late-all>input").prop('checked', true);
                });
                $("#absent-by-permission-all").click(function () {
                    $(".not-taken.active").siblings(".absent-by-permission").click();
                    $("#absent-by-permission-all>input").prop('checked', true);

                });
            }
        });
    }

    function goBack() {
        $("#bottom-back-button").html('<a onClick="window.history.back();"><i class="fa fa-chevron-left" aria-hidden="true"></i><p>Back</p></a>')
        $("#heading").text("Attendance")
        $("#student-list").hide()
        $(".attendanceInputFields").show()
        $("#periods-table").show();

        $("#student-filter").hide();;
    }

    function updateCounts() {
        var not_taken = $(".not-taken.active").length;
        var present = $(".present.active").length;
        var absent = $(".absent.active").length;
        var late = $(".late.active").length;
        var absent_by_permission = $(".absent-by-permission.active").length;
        $("#not-taken-count").html(not_taken);
        $("#present-count").html(present);
        $("#absent-count").html(absent);
        $("#late-count").html(late);
        $("#absent-by-permission-count").html(absent_by_permission);
    }

    function getAttendanceAudit(student_attendance_id) {
        $("#audit-modal").modal('show');
        var student_name = $("#att-" + student_attendance_id).data('student_name');
        var student_id = $("#att-" + student_attendance_id).data('student_id');
        $("#student-name").html(student_name);
        $("#audit-data").html('<tr><td colspan="4"><i style="font-size: 1.5rem;" class="fa fa-spinner fa-spin"></i></td></tr>');

        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/getStudentAttendanceAudit'); ?>',
            type: 'post',
            data: {
                'student_attendance_id': student_attendance_id
            },
            success: function (data) {
                data = JSON.parse(data);
                var history = data.history;
                if (history.length == 0) {
                    $("#audit-data").html('<tr><td colspan="4">No data available</td></tr>');
                } else {
                    var status_array = {
                        0: '<td class="not-taken active">Not Taken</td>',
                        1: '<td class="present active">Present</td>',
                        2: '<td class="absent active">Absent</td>',
                        3: '<td class="late active">Late</td>'
                    }
                    var html = '';
                    for (var i = 0; i < history.length; i++) {
                        html += `<tr>
              <td>${i + 1}</td>
              ${status_array[history[i].status]}
              <td>${history[i].action_by}</td>
              <td>${history[i].action_on}</td>
            </tr>`;
                    }
                    $("#audit-data").html(html);
                }
            }
        });
    }

    async function checkPeriodExists(period_no, classSectionId, timetable_id) {
        const currentDate = $("#attendance_date").val().split("-").reverse().join("-")
        let periodsLength
        await $.ajax({
            url: "<?php echo site_url('attendance_v2/attendance/checkPeriodExists') ?>",
            type: "POST",
            data: {
                "period_no": period_no,
                "class_section_id": classSectionId,
                timetable_id,
                "date": currentDate
            },
            success: function (data) {
                data = JSON.parse(data)
                periodsLength = data
            }
        })
        return periodsLength
    }

    function saveAttendance(isElective, timetable_id, type_id) {
        $("#bottom-back-button").html('<a onClick="window.history.back();"><i class="fa fa-chevron-left" aria-hidden="true"></i><p>Back</p></a>')

        if (isEdit) {
            $("#attendance-form").show();
            $("#student-filter").show();;
            $("#save-attendance-btn").html('Please Wait...').prop('disabled', true);
            var has_not_taken = 0;
            $(".new-status").each(function () {
                if ($(this).val() == 0) {
                    has_not_taken++;
                }
            });
            if (has_not_taken) {
                Swal.fire({
                    title: 'Save Attendance?',
                    html: '<p>There are students whose status is <br><b>Not-Taken.</b><br>Still want to save?</p>',
                    confirmButtonText: 'Confirm',
                    showCancelButton: true,
                    showLoaderOnConfirm: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        saving_attendance();
                    } else {
                        $("#save-attendance-btn").html('Save').prop('disabled', false);
                    }
                });
            } else {
                saving_attendance();
            }
            return
        }
        let isPeriodExist = checkPeriodExists(period_no, classSectionId, timetable_id);
        isPeriodExist.then(data => {
            if (data.length) {
                let sameElectiveSubject = false;

                data.forEach(e => {
                    if (e.type_id == type_id) {
                        sameElectiveSubject = true;
                    }
                })

                if (isElective == 0 || sameElectiveSubject) {
                    $("#attendance-form").hide();
                    $("#student-filter").hide();;
                    getSubjectOrSessions()
                    $("#periods-tables").show()
                    $("#periods-table").show()
                    // return bootbox.alert("Already Taken Attendance For Period " + period_no)
                    return bootbox.confirm({
                        title: 'ALERT',
                        message: 'Attendance already taken for this period ' + period_no,
                        buttons: {
                            cancel: {
                                label: '<i class="fa fa-times" style="display:none">Cancel</i>'
                            },
                            confirm: {
                                label: '<i class="fa fa-check"></i> OK'
                            }
                        },
                        callback: function (result) {
                            console.log('This was logged in the callback: ' + result);
                        }
                    });
                }
            }
            $("#periods-tables").hide()
            $("#periods-table").hide()
            $("#attendance-form").show();
            $("#student-filter").show();;
            $("#save-attendance-btn").html('Please Wait...').prop('disabled', true);
            var has_not_taken = 0;
            $(".new-status").each(function () {
                if ($(this).val() == 0) {
                    has_not_taken++;
                }
            });

            const isFirstTimeAttendanceTaken = window.sessionStorage.getItem("isFirstTimeAttendanceTaken");

            if (has_not_taken) {
                Swal.fire({
                    title: 'Save Attendance?',
                    html: '<p>There are students whose status is <br><b>Not-Taken.</b><br>Still want to save?</p>',
                    confirmButtonText: 'Confirm',
                    showCancelButton: true,
                    showLoaderOnConfirm: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        if (isFirstTimeAttendanceTaken == "false") {
                            saving_attendance();
                            window.sessionStorage.setItem("isFirstTimeAttendanceTaken", true);
                        }
                    } else {
                        $("#save-attendance-btn").html('Save').prop('disabled', false);
                    }
                });
            } else {
                if (isFirstTimeAttendanceTaken == "false") {
                    saving_attendance();
                    window.sessionStorage.setItem("isFirstTimeAttendanceTaken", true);
                }
            }
        })
    }

    async function saving_attendance() {
        $("#heading").text("Attendance")

        var form = $('#attendance-form')[0];
        var formData = new FormData(form);

        let subjectName = formData.get("subject_name")
        let period_short_name = formData.get("period_no")
        let attendance_type_id = formData.get("attendance_type_id")

        await $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/save_attendance_data_period_wise_mobile'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function (data) {
                data = $.parseJSON(data)
                if (parseInt(data.attendance_master_id) != 0) {
                    // $(function() {
                    //     new PNotify({
                    //         title: 'Success',
                    //         text: 'Attendance saved successfully',
                    //         type: 'success',
                    //     });
                    // });
                    const startHour = window.sessionStorage.getItem("periodStartHour");
                    const endHour = window.sessionStorage.getItem("periodEndHour");

                    if (enable_notification) notify_students(parseInt(data.attendance_master_id), period_short_name, attendance_type_id, subjectName, startHour, endHour);
                    hideStudentDataTable()
                } else {
                    $(function () {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong!!',
                            type: 'error',
                        });
                    });
                }
            }
        });
    }

    function hideStudentDataTable() {
        $("#student-filter").hide();;

        $("#info-block").html('');
        $("#student-list").html('');
        getSubjectOrSessions()
        $("#periods-table").show()
    }

    function notify_students(attendance_id, period_short_name, attendance_type_id, subjectName, start_hour, end_hour) {
        let date = $("#attendance_date").val();

        let subject = $(`#subject_id_${period_short_name}_${attendance_type_id} option:selected`).text() || subjectName;

        let school_name = '<?php echo $this->settings->getSetting('school_name') ?>';

        $("#notify-attendance-master-id").val(attendance_id);
        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/get_absentees_latecomers'); ?>',
            type: 'post',
            data: {
                'attendance_master_id': attendance_id
            },
            success: function (data) {
                let students = JSON.parse(data);
                if (students.length) {
                    let html = '';
                    $("#notify-modal").modal('show');
                    html = `<table class="table"><thead><tr><th>#</th><th style="min-width: 120px;">Student</th><th>Section</th><th>Reason</th><th>Message</th></tr></thead><tbody>`;
                    for (let i = 0; i < students.length; i++) {
                        let std_msg = (students[i].status == '2') ? absent_message : late_message;
                        std_msg = std_msg.replace('%%student_name%%', students[i].student_name);
                        std_msg = std_msg.replace('%%class_section%%', students[i].class_name + '' + students[i].section_name);
                        std_msg = std_msg.replace('%%class_name%%', students[i].class_name);
                        std_msg = std_msg.replace('%%section_name%%', students[i].section_name);
                        std_msg = std_msg.replace('%%date%%', date);
                        // std_msg = std_msg.replace('%%subject%%', attendance_type_name);
                        if (subject?.length > 30) {
                            subject = subject.substring(0, 27).padEnd(30, ".");
                        }

                        std_msg = std_msg.replace('%%subject%%', subject);

                        if (std_msg.includes("%%start_hour%%")) {
                            std_msg = std_msg.replace('%%start_hour%%', start_hour);
                        }

                        if (std_msg.includes("%%end_hour%%")) {
                            std_msg = std_msg.replace('%%end_hour%%', end_hour);
                        }

                        std_msg = std_msg.replace('${school_name}', school_name);
                        html += `
              <tr>
                  <td>${i + 1}</td>
                  <td>${students[i].student_name}</td>
                  <td>${students[i].class_name + '' + students[i].section_name}</td>
                  <td>${(students[i].status == '2') ? 'Absent' : 'Late'}</td>
                  <td>
                      ${std_msg}
                      <input type="hidden" name="student_messages[${students[i].student_id}]" value="${std_msg}" />
                  </td>
              </tr>
            `;
                    }
                    html += '</tbody></table>';

                    // console.log(html);
                    $("#notify-content").html(html);
                } else {
                    // takeAttendance();
                }
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function send_messages() {
        $("#confirmBtn").attr('disabled', true).html('Please Wait...');
        let formData = new FormData(document.getElementById('student-messages-form'));
        formData.append('attendance_master_id', $("#notify-attendance-master-id").val());
        $.ajax({
            url: '<?php echo site_url('attendance_v2/attendance/send_messages'); ?>',
            type: 'post',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                $("#notify-modal").modal('hide');
                let response = JSON.parse(data);
                if (response.status == 1) {
                    $(function () {
                        new PNotify({
                            title: 'Success',
                            text: 'Messages Sent Successfully',
                            type: 'success',
                        });
                    });
                    // takeAttendance();
                } else {
                    $(function () {
                        new PNotify({
                            title: 'Error',
                            text: response.error,
                            type: 'error',
                        });
                    });
                    // takeAttendance();
                }
                $("#confirmBtn").attr('disabled', false).html('Confirm');
            },
            error: function (err) {
                // takeAttendance();
                $("#notify-modal").modal('hide');
                console.log(err);
                $(function () {
                    new PNotify({
                        title: 'Error',
                        text: 'Sending messages failed!!',
                        type: 'error',
                    });
                });
                $("#confirmBtn").attr('disabled', false).html('Confirm');
            }
        });
    }
</script>


<style type="text/css">
    .attendance-status {
        padding: 0.2rem 0.8rem;
        background-color: #ccc;
        border-radius: 1.2rem;
        font-size: 0.8rem;
        font-weight: 300;
    }

    .present-status {
        background-color: #DCFFDC;
    }

    .absent-status {
        background-color: #ffaeae;
    }

    .late-status {
        background-color: #fffc96;
    }

    input[type="radio"] {
        width: 1.5rem;
        height: 1.5rem;
        margin: 0px;
    }

    .att-status {
        /*background: #ccc;*/
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
    }

    .att-status>i {
        font-size: 1.5rem;
    }

    .att-status:hover {
        background: #ccc;
    }

    .absent.active {
        background-color: #ff9696 !important;
    }

    .present.active {
        background-color: #91fcb9 !important;
    }

    .late.active {
        background-color: #fffc96 !important;
    }

    .bootbox-body {
        text-align: center;
    }

    .modal-dialog {
        position: relative;
        /* top: 39%; */
    }

    .modal-footer button {
        width: "101px";
    }
</style>


<?php $this->load->view("attendance_v2/getElectiveSubjects_modal") ?>