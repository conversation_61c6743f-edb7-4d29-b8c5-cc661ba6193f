<!-- Switch Profile Page - Mobile UI -->
<style>
    body {
        background: #f7f8fa;
        font-family: 'Inter', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    }
    .switch-profile-header {
        background: #fff;
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        display: flex;
        align-items: center;
        position: relative;
    }
    .switch-profile-header .back-btn {
        font-size: 1.5rem;
        color: #7b5cff;
        background: none;
        border: none;
        margin-right: 0.5rem;
        padding: 0;
        cursor: pointer;
    }
    .switch-profile-header-title {
        flex: 1;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
    }
    .switch-profile-list {
        background: #fff;
        border-radius: 18px 18px 0 0;
        margin: 2.5rem 0 0 0;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
        padding: 1.2rem 0.5rem 2rem 0.5rem;
        max-width: 440px;
        margin-left: auto;
        margin-right: auto;
    }
    .switch-profile-item {
        display: flex;
        align-items: center;
        background: #f7f8fa;
        border-radius: 14px;
        margin-bottom: 1.1rem;
        padding: 0.8rem 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        cursor: pointer;
        transition: box-shadow 0.15s, background 0.15s;
        text-decoration: none;
    }
    .switch-profile-item:last-child {
        margin-bottom: 0;
    }
    .switch-profile-item:active {
        background: #f0f0ff;
        box-shadow: 0 4px 16px rgba(123,92,255,0.10);
    }
    .switch-profile-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 1rem;
        border: 2px solid #e0e0e0;
    }
    .switch-profile-info {
        flex: 1;
    }
    .switch-profile-name {
        font-size: 1.08rem;
        font-weight: 700;
        color: #222;
        margin-bottom: 0.2rem;
    }
    .switch-profile-class {
        color: #888;
        font-size: 0.98rem;
    }
    @media (min-width: 600px) {
        .switch-profile-header, .switch-profile-list {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }
    }
</style>

<!-- FontAwesome CDN (for icons, if needed) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />

<div class="switch-profile-header" style="margin-top: 2rem;">
    <button class="back-btn" onclick="window.history.back()">
        <i class="fa fa-arrow-left"></i>
    </button>
    <div class="switch-profile-header-title">Switch Profile</div>
    <div style="width:32px;"></div>
</div>

<div class="switch-profile-list">
    <?php
    // Example: Replace this with your real data from the controller
    $profiles = isset($profiles) ? $profiles : [
        (object)[
            'id' => 1,
            'avatar_url' => 'https://randomuser.me/api/portraits/men/32.jpg',
            'name' => 'Aadi Kumar Sharma',
            'class' => 'Grade 2-A'
        ],
        (object)[
            'id' => 2,
            'avatar_url' => 'https://randomuser.me/api/portraits/women/44.jpg',
            'name' => 'Shreya Sharma',
            'class' => 'Grade 2-A'
        ],
        (object)[
            'id' => 3,
            'avatar_url' => 'https://randomuser.me/api/portraits/men/65.jpg',
            'name' => 'Abhishek Sharma',
            'class' => 'Grade 2-A'
        ]
    ];
    ?>
    <?php foreach ($profiles as $profile): ?>
        <a href="<?php echo site_url('parent/switch_profile/'.$profile->id); ?>" class="switch-profile-item">
            <img src="<?php echo $profile->avatar_url; ?>" class="switch-profile-avatar" alt="<?php echo $profile->name; ?>">
            <div class="switch-profile-info">
                <div class="switch-profile-name"><?php echo $profile->name; ?></div>
                <div class="switch-profile-class"><?php echo $profile->class; ?></div>
            </div>
        </a>
    <?php endforeach; ?>
</div>
