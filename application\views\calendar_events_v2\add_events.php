<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('calendar_events_v2/Calendar_events_v2'); ?>">Calendar V2</a></li>
  <li><a href="<?php echo site_url('calendar_events_v2/Calendar_events_v2/Calendar_types'); ?>">Manage Calendar Templates</a></li>
  <li>Add Calendar Events</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border_v2 d-flex justify-content-between align-items-center">
            <div class="col-md-4">
                <h3 class="card-title panel_title_new_style_staff mb-0">
                    <a class="back_anchor" href="<?php echo site_url('calendar_events_v2/Calendar_events_v2/Calendar_types') ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Add Events
                </h3>
            </div>
            <div class="switch-wrapper">
                <label class="switch">
                    <input class="viewchange" onchange="UIviewchange()" checked type="checkbox" />
                    <span class="switch-toggle"></span>
                </label>
                <span class="switch-label">Calendar View</span>
            </div>




        </div>

        <div class="card-body">
            <div class="row">
                
                <div class="col-md-9">
                    
                    <div class="calendar" style="width:100%;">                                
                        <div id="calendar_control">
                            <div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="height: 10%; width: 100%;" id="list_data">
                                <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                            </div>
                        </div> 
                        <div id="form_UI" style="display:none">
                            <form id="addEventForm_new_uI" class="custom-form">
                                <div class="form-group">
                                    <label for="event_name_new_uI">Event Name</label>
                                    <input type="text" class="form-control" id="event_name_new_uI" name="event_name" placeholder="Enter event name">
                                </div>
                                <div class="form-group">
                                    <label for="event_type_new_uI">Event Type</label>
                                    <select id="event_type_new_uI" name="event_type" class="form-control select" onchange="eventTypeOnchange()">
                                        <option value="">Select event type</option>
                                        <option value="holiday" selected>Holiday</option>
                                        <option value="holiday_range">Holiday Range</option>
                                        <option value="event">Event</option>
                                        <option value="event_range">Event Range</option>
                                    </select>
                                </div>
                                <div class="form-group" id="UI_from_date" style="display:none">
                                    <label for="event_start_date_new_uI">From Event Date</label>
                                    <input type="text" class="form-control manual-datepicker" id="event_start_date_new_uI" name="event_date" readonly>
                                    <small class="text-muted">Date must be within template range</small>
                                </div>
                                <div class="form-group" id="UI_to_date" style="display:none">
                                    <label for="event_end_date_new_uI">To Event Date</label>
                                    <input type="text" class="form-control manual-datepicker" id="event_end_date_new_uI" name="event_date" readonly>
                                    <small class="text-muted">Date must be within template range</small>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-primary" id="form_save" onclick="validateAndSaveManual()">Save Event</button>
                                    <a class="btn btn-danger" href="<?php echo site_url('calendar_events_v2/Calendar_events_v2/Calendar_types') ?>">Cancel</a>
                                </div>
                            </form>
                        </div>  
                    </div>
                </div>
                <div class="col-md-3">
                    <div id="unassigned_tasks" class="card bg-white mx-auto text-white" style="background: rgb(255, 255, 255);border-radius:3%;box-shadow:#007bfb -5px 0px 0px 0px;position:relative;height:75vh;overflow-x: auto;">
                        <span class="m-2 d-flex justify-content-between align-items-center" style="height: 40px; border-radius: 5px;">
                            
                            <div class="color-index d-flex">
                                <div class="index-item">
                                    <span class="color-dot" style="background: #36A2EB;"></span>
                                    <span class="index-text">Event</span>
                                </div>
                                <div class="index-item">
                                    <span class="color-dot" style="background: #9966FF;"></span>
                                    <span class="index-text">Event Range</span>
                                </div>
                                <div class="index-item">
                                    <span class="color-dot" style="background: #FF6384;"></span>
                                    <span class="index-text">Holiday</span>
                                </div>
                                <div class="index-item">
                                    <span class="color-dot" style="background: #FF9F40;"></span>
                                    <span class="index-text">Holiday Range</span>
                                </div>
                            </div>
                        </span>
                        <span class="text-dark">
                                <h4 style="margin-bottom: 0;"class="center" >Events List</h4>
                            </span>
                        <div class="card-body text-white tasks-body list-name" style="min-width:275px; overflow-y:auto; min-height:auto; max-height:485px;">
                            <div id='getadded_events'>
                                
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                </div>
                
            </div>
        </div>
    </div>
</div>

<!-- pop-up- Modal -->
<div class="modal fade" id="addEventModal" tabindex="-1" aria-labelledby="addEventModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addEventModalLabel">Add Event</h5>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
      </div>
      <div class="modal-body">
        <form id="addEventForm">
          <div class="form-group">
            <label for="event-name">Event Name</label>
            <input type="text" class="form-control" id="event-name" name="event_name" placeholder="Enter event name" required>
          </div>
          <div class="form-group">
            <label for="event-type">Event Type</label>
            <select id="event-type" name="event_type" class="form-control" onchange="eventTypeOnchange()">
                <option value="">Select event type</option>
                <option value="holiday">Holiday</option>
                <option value="holiday_range">Holiday Range</option>
                <option value="event">Event</option>
                <option value="event_range">Event Range</option>
            </select>
          </div>
          <div class="form-group" id="from_date" style="display:none">
            <label for="event-date">From Event Date</label>
            <input type="text" class="form-control datepicker" id="event-start-date" name="event_date" readonly>
            <small class="text-muted">Date must be within template range</small>
          </div>
          <div class="form-group" id="to_date" style="display:none">
            <label for="event-date">To Event Date</label>
            <input type="text" class="form-control datepicker" id="event-end-date" name="event_date" readonly>
            <small class="text-muted">Date must be within template range</small>
          </div>
          
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-primary me-2" id="modal_save" onclick="validateAndSave()">Save Event</button>
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Add Edit Event Modal -->
<div class="modal fade" id="editEventModal" tabindex="-1" aria-labelledby="editEventModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editEventModalLabel">Edit Event</h5>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
      </div>
      <div class="modal-body">
        <form id="editEventForm">
          <input type="hidden" id="edit_event_id">
          <div class="form-group">
            <label for="edit_event_name">Event Name</label>
            <input type="text" class="form-control" id="edit_event_name" name="event_name" required>
          </div>
          <div class="form-group">
            <label for="edit_event_type">Event Type</label>
            <select id="edit_event_type" name="event_type" class="form-control" onchange="editEventTypeOnchange()">
                <option value="">Select event type</option>
                <option value="holiday">Holiday</option>
                <option value="holiday_range">Holiday Range</option>
                <option value="event">Event</option>
                <option value="event_range">Event Range</option>
            </select>
          </div>
          <div class="form-group" id="edit_from_date" style="display:none">
            <label for="edit_event_start_date">From Event Date</label>
            <input type="text" class="form-control datepicker" id="edit_event_start_date" name="event_date" readonly>
            <small class="text-muted">Date must be within template range</small>
          </div>
          <div class="form-group" id="edit_to_date" style="display:none">
            <label for="edit_event_end_date">To Event Date</label>
            <input type="text" class="form-control datepicker" id="edit_event_end_date" name="event_date" readonly>
            <small class="text-muted">Date must be within template range</small>
          </div>
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-primary me-2" id="edit_save" onclick="validateAndUpdateEvent()">Update Event</button>
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>




<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/index.global.min.js'></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>


<script>
  $(document).ready(function() {
            $('#start_date,#end_date,#update_start_date,#update_end_date').datepicker({
            format: 'dd-mm-yyyy',
            todayHighlight: true,
            "autoclose": true
            });
        });
        
        function UIviewchange() {
            var checkbox = document.querySelector('.viewchange');
            var value = checkbox.checked ? 1 : 0;

            const switchLabel = document.querySelector('.switch-label');
            switchLabel.textContent = value ? 'Calendar View' : 'Manual View';

            if (value == 1) {
                $('#form_UI').hide();
                $('#calendar_control').show();
                if (window.calendar) {
                    window.calendar.destroy();
                }
                initializeCalendar();
            } else {
                $('#form_UI').show();
                $('#calendar_control').hide();
                initializeManualDatepickers();
            }
        }

        function initializeCalendar() {
            $.ajax({
                url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_calendar_detail'); ?>',
                type: 'POST',
                data: { calendar_id: <?php echo $id; ?> },
                success: function(response) {
                    response = JSON.parse(response);
                    if (response.status === 'success') {
                        let data = response.data;
                        let startDate = moment(data.start_date, 'DD-MM-YYYY').toDate();
                        let endDate = moment(data.end_date, 'DD-MM-YYYY').toDate();

                        let monthsDiff = moment(endDate).diff(moment(startDate), 'months', true);
                        
                        var calendarEl = document.getElementById('calendar_control');
                        var calendar = new FullCalendar.Calendar(calendarEl, {
                            initialView: 'dayGridMonth',
                            headerToolbar: {
                                left: 'prev,next',
                                center: 'title',
                                right: ''
                            },
                            validRange: (function() {
                                var attData = <?php echo json_encode($attData); ?>;
                                if (attData) {
                                    var today = new Date();
                                    today.setHours(0,0,0,0);
                                    // Allow adding events for today and future
                                    return {
                                        start: today,
                                        end: moment(endDate).add(1, 'days').toDate()
                                    };
                                } else {
                                    return {
                                        start: startDate,
                                        end: moment(endDate).add(1, 'days').toDate()
                                    };
                                }
                            })(),
                            events: function(fetchInfo, successCallback, failureCallback) {
                                $.ajax({
                                    url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_display'); ?>',
                                    type: 'POST',
                                    data: { 'id': <?php echo $id; ?> },
                                    success: function(result) {
                                        var events = [];
                                        var data = JSON.parse(result);
                                        
                                        data.forEach(function(event) {
                                            events.push({
                                                title: event.event_name,
                                                start: moment(event.from_date, 'DD-MMM-YYYY').format('YYYY-MM-DD'),
                                                end: moment(event.to_date, 'DD-MMM-YYYY').add(1, 'days').format('YYYY-MM-DD'),
                                                color: EVENT_COLORS[event.event_type],
                                                description: event.event_type
                                            });
                                        });
                                        successCallback(events);
                                    }
                                });
                            },
                            selectable: true, // Always allow selection, but validate in select handler
                            select: function(info) {
                                var attData = <?php echo json_encode($attData); ?>;
                                var selectedStartDate = new Date(info.startStr);
                                selectedStartDate.setHours(0,0,0,0);
                                var today = new Date();
                                today.setHours(0,0,0,0);

                                if (attData && selectedStartDate < today) {
                                    Swal.fire({
                                        icon: 'info',
                                        title: 'Not Allowed',
                                        text: 'You cannot add events for past dates as attendance already exists.'
                                    });
                                    return false;
                                }

                                const selectedEndDate = new Date(info.endStr);
                                selectedEndDate.setDate(selectedEndDate.getDate() - 1);

                                var formattedStartDate = `${selectedStartDate.getDate()}-${selectedStartDate.toLocaleString('en-US', { month: 'short' })}-${selectedStartDate.getFullYear()}`;
                                var formattedEndDate = `${selectedEndDate.getDate()}-${selectedEndDate.toLocaleString('en-US', { month: 'short' })}-${selectedEndDate.getFullYear()}`;
                                
                                document.getElementById('event-start-date').value = formattedStartDate;
                                document.getElementById('event-end-date').value = formattedEndDate;

                                if (formattedStartDate === formattedEndDate) {
                                    document.getElementById('event-type').value = 'holiday';
                                    $('#from_date').show();
                                    $('#to_date').hide();
                                } else {
                                    document.getElementById('event-type').value = 'holiday_range';
                                    $('#from_date').show();
                                    $('#to_date').show();
                                }

                                $('#addEventModal').modal('show');
                            },
                            eventDidMount: function(info) {
                                $(info.el).tooltip({
                                    title: `${info.event.title} (${info.event.extendedProps.description})`,
                                    placement: 'top',
                                    trigger: 'hover',
                                    container: 'body'
                                });
                            }
                        });

                        calendar.render();
                        window.calendar = calendar;
                    }
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeCalendar();
        });

    $(document).ready(function() {
        getevents();
});

const EVENT_COLORS = {
    'event': '#36A2EB',         // Blue for single events
    'event_range': '#9966FF',   // Purple for event ranges
    'holiday': '#FF6384',       // Red for holidays
    'holiday_range': '#FF9F40'  // Orange for holiday ranges
};

function eventTypeOnchange() {
    var options = $('#event-type option:selected').val();
    var UIoptions = $('#event_type_new_uI option:selected').val();
    
    if (options) {
        $('#event-start-date, #event-end-date').css('border-left', `4px solid ${EVENT_COLORS[options]}`);
    }

    if (UIoptions) {
        $('#event_start_date_new_uI, #event_end_date_new_uI').css('border-left', `4px solid ${EVENT_COLORS[UIoptions]}`);
    }

    if (options == 'holiday' || options == 'event') {
        $('#from_date').show();
        $('#to_date').hide();
        $('#from_date label').text('Select Date');  // Change label text
    } else {
        $('#from_date').show();
        $('#to_date').show();
        $('#from_date label').text('From Event Date');  // Reset label text
    }

    if (UIoptions == 'holiday' || UIoptions == 'event') {
        $('#UI_from_date').show();
        $('#UI_to_date').hide();
        $('#UI_from_date label').text('Select Date');
    } else {
        $('#UI_from_date').show();
        $('#UI_to_date').show();
        $('#UI_from_date label').text('From Event Date');
    }
}


function getevents(){
    var id=<?php echo json_encode($id) ?>;
    $.ajax({
        url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_display'); ?>",
        type: "POST",
        data: {'id': id },
        success: function(data) {
            try {
                data = JSON.parse(data);
                if(data.length == 0){
                    var html = '<div class="no-data-display">No Events Added for This Calendar </div>';
                } else {
                    var html = '<div class="card-container">';
                    for (var s = 0; s < data.length; s++) {
                        var dateDisplay = '';
                        var eventType = data[s].event_type || 'event'; // Default to 'event' if undefined
                        
                        if (eventType && eventType.includes('_range')) {
                            dateDisplay = `<p class="card-text">
                                <span class="date-label">From:</span> ${data[s].from_date}<br>
                                <span class="date-label">To:</span> ${data[s].to_date}
                            </p>`;
                        } else {
                            dateDisplay = `<p class="card-text">
                                <span class="date-label">Date:</span> ${data[s].from_date}
                            </p>`;
                        }

                        html += `
                            <div class="card" style="border-radius: 10px;margin-bottom:1rem">
                                <div class="card-body" style="color:${EVENT_COLORS[eventType]};border-radius: 10px;padding: 1rem;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);border-left: 4px solid ${EVENT_COLORS[eventType]};">
                                    <h5 class="card-title d-flex justify-content-between align-items-center">
                                        ${data[s].event_name}
                                        <span>
                                            <span class="edit-icon" style="cursor: pointer; color: #007bff; margin-right:10px;" onclick="editEvent(${data[s].id})">
                                                <i class="fa fa-edit"></i>
                                            </span>
                                            <span class="delete-icon" style="cursor: pointer; color: red;" onclick="deleteEvent(${data[s].id})">
                                                <i class="fa fa-trash"></i>
                                            </span>
                                        </span>
                                    </h5>
                                    ${dateDisplay}
                                    <span class="badge" style="background-color: ${EVENT_COLORS[eventType]};text-transform: capitalize;">
                                        ${eventType.replace('_', ' ')}
                                    </span>
                                </div>
                            </div>`;
                    }
                    html += '</div>';
                }
                $('#getadded_events').html(html);
            } catch (e) {
                console.error('Error parsing JSON:', e);
                console.log('Raw data:', data);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
        }
    });
}

function deleteEvent(eventId) {
    // Prevent deleting events in the past or today if attendance exists
    var attData = <?php echo json_encode($attData); ?>;
    if (attData) {
        $.ajax({
            url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_display'); ?>",
            type: "POST",
            data: { 'id': <?php echo json_encode($id); ?> },
            success: function(data) {
                try {
                    data = JSON.parse(data);
                    var event = data.find(function(ev) { return ev.id == eventId; });
                    if (event) {
                        var eventDate = moment(event.from_date, 'DD-MMM-YYYY').toDate();
                        var today = new Date();
                        today.setHours(0,0,0,0);
                        if (eventDate <= today) {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Not Allowed',
                                text: 'You cannot delete past or today\'s events as attendance already exists for this calendar.'
                            });
                            return;
                        }
                    }
                    proceedDeleteEvent(eventId);
                } catch (e) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Could not validate event date for deletion.'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Could not validate event date for deletion.'
                });
            }
        });
        return;
    }
    // If no attendance, allow delete as normal
    proceedDeleteEvent(eventId);
}

function proceedDeleteEvent(eventId) {
    Swal.fire({
        title: 'Are you sure?',
        text: "You want to delete this event calendar!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/delete_event'); ?>",
                type: "POST",
                data: { 'event_id': eventId },
                success: function(response) {
                    response = JSON.parse(response);
                    if (response.status === 'success') {
                        Swal.fire(
                            'Deleted!',
                            'Your event has been deleted.',
                            'success'
                        );
                        getevents();
                        initializeCalendar(); // Refresh the events list
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete the event.',
                            'error'
                        );
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    Swal.fire(
                        'Error!',
                        'An error occurred while deleting the event.',
                        'error'
                    );
                }
            });
        }
    });
}

window.Saveventsdata = function() {
    var eventName = document.getElementById('event-name').value || document.getElementById('event_name_new_uI').value;
    var eventStartDate = document.getElementById('event-start-date').value || document.getElementById('event_start_date_new_uI').value;
    var eventEndDate = document.getElementById('event-end-date').value || document.getElementById('event_end_date_new_uI').value;
    var eventType = document.getElementById('event-type').value || document.getElementById('event_type_new_uI').value;

    if (eventName && eventStartDate && eventEndDate) {
        const endDate = new Date(eventEndDate);
        endDate.setDate(endDate.getDate() + 1);
        calendar.addEvent({
            title: eventName,
            start: new Date(eventStartDate).toISOString(),
            end: new Date(endDate).toISOString(),
            description: eventType,
            color: EVENT_COLORS[eventType] 
        });
        $('#addEventModal').modal('hide');

        var id = <?php echo json_encode($id); ?>;
        var eventDetails = {
            eventName: eventName,
            eventStartDate: eventStartDate,
            eventEndDate: eventEndDate,
            eventType: eventType
        };
        $.ajax({
            url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/save_calendar_events'); ?>",
            type: "POST",
            data: { 'events': eventDetails, 'id': id },
            success: function(data) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Events added to the calendar.',
                    showConfirmButton: false,
                    timer: 1500
                });
                $('#addEventForm')[0].reset();
                $('#addEventForm_new_uI')[0].reset();
                $('#from_date').hide();
                $('#to_date').hide();
                getevents();
                
            },
            error: function(xhr, status, error) {
                console.error('Error saving events:', error);
            }
        });
    } else {
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Please fill in the details.',
            showConfirmButton: true
        });
        return false;
    }
};



function validateAndSave() {
   

    var eventName = $('#event-name').val();
    var eventType = $('#event-type').val();
    var startDate = $('#event-start-date').val();
    var endDate = $('#event-end-date').val();

    if (!eventName || !eventType || !startDate) {
        Swal.fire({
            icon: 'error',
            title: 'Required Fields',
            text: 'Please fill in all required fields'
        });
        return;
    }

    if (eventType == 'holiday' || eventType == 'event') {
        endDate = startDate;
    }
    
    if ((eventType == 'holiday_range' || eventType == 'event_range') && !endDate) {
        Swal.fire({
            icon: 'error',
            title: 'End Date Required',
            text: 'Please select an end date for range events'
        });
        return;
    }


    Saveventsdata();
}

$(document).ready(function() {
    
    $('.datepicker').datepicker({
        format: 'dd-M-yyyy',
        autoclose: true,
        todayHighlight: true
    });
});

$(document).ready(function() {
    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_calendar_detail'); ?>',
        type: 'POST',
        data: { calendar_id: <?php echo $id; ?> },
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                let data = response.data;
                let startDate = moment(data.start_date, 'DD-MM-YYYY').toDate();
                let endDate = moment(data.end_date, 'DD-MM-YYYY').toDate();
                
                $('.datepicker').datepicker({
                    format: 'dd-M-yyyy',
                    autoclose: true,
                    todayHighlight: true,
                    startDate: startDate,
                    endDate: endDate,
                    beforeShowDay: function(date) {
                        return {
                            enabled: date >= startDate && date <= endDate,
                            classes: date < startDate || date > endDate ? 'disabled' : ''
                        };
                    }
                });

                window.templateStartDate = startDate;
                window.templateEndDate = endDate;

                
            }
        }
    });
});



function validateDateRange() {
    var startDate = moment($('#event-start-date').val(), 'DD-MMM-YYYY');
    var endDate = moment($('#event-end-date').val(), 'DD-MMM-YYYY');
    var eventType = $('#event-type').val();

    if (!startDate.isValid() || (endDate && !endDate.isValid())) {
        Swal.fire({
            icon: 'error',
            title: 'Invalid Date',
            text: 'Please select valid dates'
        });
        return false;
    }

    if (eventType && (eventType === 'holiday_range' || eventType === 'event_range')) {
        if (endDate.isBefore(startDate)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Date Range',
                text: 'End date cannot be before start date'
            });
            $('#event-end-date').val('');
            return false;
        }
    }

    if (startDate.isBefore(window.templateStartDate) || endDate.isAfter(window.templateEndDate)) {
        Swal.fire({
            icon: 'error',
            title: 'Invalid Date Range',
            text: `Please select dates between ${moment(window.templateStartDate).format('DD-MMM-YYYY')} and ${moment(window.templateEndDate).format('DD-MMM-YYYY')}`
        });
        return false;
    }

    return true;
}

function validateAndSaveManual() {
    var eventName = $('#event_name_new_uI').val();
    var eventType = $('#event_type_new_uI').val();
    var startDate = $('#event_start_date_new_uI').val();
    var endDate = $('#event_end_date_new_uI').val();
    console.log(startDate);

    if (!eventName || !eventType || !startDate) {
        Swal.fire({
            icon: 'error',
            title: 'Required Fields',
            text: 'Please fill in all required fields'
        });
        return;
    }

    if (eventType === 'holiday' || eventType === 'event') {
        $('#event_end_date_new_uI').val(startDate);
        endDate = startDate;
    }

    if ((eventType === 'holiday_range' || eventType === 'event_range') && !endDate) {
        Swal.fire({
            icon: 'error',
            title: 'End Date Required',
            text: 'Please select an end date for range events'
        });
        return;
    }

    Saveventsdata();
}

function initializeManualDatepickers() {
    var today = moment().format('DD-MMM-YYYY');
    $('#event_start_date_new_uI').val(today);
    $('#event_end_date_new_uI').val(today);
    
    $('.datepicker, .manual-datepicker').datepicker({
        format: 'dd-M-yyyy',
        autoclose: true,
        todayHighlight: true
    });

    $('#event_type_new_uI').trigger('change');
    $('#UI_from_date').show(); // Show the date field by default
}

$(document).ready(function() {
    $('#event-type').val('holiday');
    
    var today = moment().format('DD-MMM-YYYY');
    $('#event-start-date').val(today);
    $('#event-end-date').val(today);
    
    $('#from_date').show();
    $('#event-type').trigger('change');
});

$(document).ready(function() {
    initializeManualDatepickers();
});

function validateDateRangeCommon(startDate, endDate, eventType) {
    if (!startDate) return false;
    
    let start = moment(startDate, 'DD-MMM-YYYY');
    let end = endDate ? moment(endDate, 'DD-MMM-YYYY') : start;
    
    if (eventType && (eventType === 'holiday_range' || eventType === 'event_range')) {
        if (!endDate) {
            Swal.fire({
                icon: 'error',
                title: 'End Date Required',
                text: 'Please select an end date for range events'
            });
            return false;
        }
        if (end.isBefore(start)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Date Range',
                text: 'End date cannot be before start date'
            });
            return false;
        }
    }
    return true;
}

function enableDateRangeEditing() {
    $('#event-start-date, #event-end-date').removeAttr('readonly');
}

$('#addEventModal').on('shown.bs.modal', function () {
    enableDateRangeEditing();
});

$(document).ready(function() {
    var attData = <?php echo json_encode($attData); ?>;
    if (attData) {
        $('.datepicker, .manual-datepicker').each(function() {
            var $input = $(this);
            var dateVal = $input.val();
            if (dateVal) {
                var inputDate = moment(dateVal, ['DD-MMM-YYYY', 'DD-MMM-YYYY', 'DD-MM-YYYY']).toDate();
                var today = new Date();
                today.setHours(0,0,0,0);
                if (inputDate < today) {
                    $input.prop('disabled', true);
                } else {
                    $input.prop('disabled', false);
                }
            } else {
                $input.prop('disabled', false);
            }
        });
        $('#form_save, #modal_save').prop('disabled', false);
    }
});

function editEvent(eventId) {
    var attData = <?php echo json_encode($attData); ?>;
    if (attData) {
        $.ajax({
            url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_display'); ?>",
            type: "POST",
            data: { 'id': <?php echo json_encode($id); ?> },
            success: function(data) {
                try {
                    data = JSON.parse(data);
                    var event = data.find(function(ev) { return ev.id == eventId; });
                    if (event) {
                        var eventDate = moment(event.from_date, 'DD-MMM-YYYY').toDate();
                        var today = new Date();
                        today.setHours(0,0,0,0);
                        if (eventDate <= today) {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Not Allowed',
                                text: 'You cannot edit past or today\'s events as attendance already exists for this calendar.'
                            });
                            return;
                        }
                        showEditEventModal(event);
                    }
                } catch (e) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Could not validate event date for editing.'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Could not validate event date for editing.'
                });
            }
        });
        return;
    }
    $.ajax({
        url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_display'); ?>",
        type: "POST",
        data: { 'id': <?php echo json_encode($id); ?> },
        success: function(data) {
            try {
                data = JSON.parse(data);
                var event = data.find(function(ev) { return ev.id == eventId; });
                if (event) {
                    showEditEventModal(event);
                }
            } catch (e) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Could not load event for editing.'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Could not load event for editing.'
            });
        }
    });
}

function showEditEventModal(event) {
    $('#edit_event_id').val(event.id);
    $('#edit_event_name').val(event.event_name);
    $('#edit_event_type').val(event.event_type);
    $('#edit_event_start_date').val(event.from_date);
    $('#edit_event_end_date').val(event.to_date);

    if (event.event_type === 'holiday' || event.event_type === 'event') {
        $('#edit_from_date').show();
        $('#edit_to_date').hide();
    } else {
        $('#edit_from_date').show();
        $('#edit_to_date').show();
    }
    $('#editEventModal').modal('show');
}

function editEventTypeOnchange() {
    var eventType = $('#edit_event_type').val();
    if (eventType === 'holiday' || eventType === 'event') {
        $('#edit_from_date').show();
        $('#edit_to_date').hide();
    } else {
        $('#edit_from_date').show();
        $('#edit_to_date').show();
    }
}

function validateAndUpdateEvent() {
    var eventId = $('#edit_event_id').val();
    var eventName = $('#edit_event_name').val();
    var eventType = $('#edit_event_type').val();
    var startDate = $('#edit_event_start_date').val();
    var endDate = $('#edit_event_end_date').val();

    if (!eventName || !eventType || !startDate) {
        Swal.fire({
            icon: 'error',
            title: 'Required Fields',
            text: 'Please fill in all required fields'
        });
        return;
    }

    if (eventType === 'holiday' || eventType === 'event') {
        endDate = startDate;
    }

    if ((eventType === 'holiday_range' || eventType === 'event_range') && !endDate) {
        Swal.fire({
            icon: 'error',
            title: 'End Date Required',
            text: 'Please select an end date for range events'
        });
        return;
    }

    var attData = <?php echo json_encode($attData); ?>;
    if (attData) {
        var eventDate = moment(startDate, 'DD-MMM-YYYY').toDate();
        var today = new Date();
        today.setHours(0,0,0,0);
        if (eventDate < today) {
            Swal.fire({
                icon: 'warning',
                title: 'Not Allowed',
                text: 'You cannot edit past events as attendance already exists for this calendar.'
            });
            return;
        }
    }

    $.ajax({
        url: "<?php echo site_url('calendar_events_v2/Calendar_events_v2/update_event'); ?>",
        type: "POST",
        data: {
            event_id: eventId,
            event_name: eventName,
            event_type: eventType,
            from_date: startDate,
            to_date: endDate
        },
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Event updated successfully.',
                    showConfirmButton: false,
                    timer: 1500
                });
                $('#editEventModal').modal('hide');
                getevents();
                initializeCalendar();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: response.message || 'Failed to update event.'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'An error occurred while updating the event.'
            });
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
    .event-list {
    list-style-type: none;
    padding: 0;
}

.event-card {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    display: block;
    color: red; /* Or any color you prefer */
    font-size: 16px;
}
    .color-input {
            border: 1px solid #ccc;
            height: 20px; /* Adjust height as needed */
            width: 20px; /* Adjust width as needed */
            padding: 0;
            margin-right: 5px; /* Optional margin for spacing */
            vertical-align: middle;
        }
    .fc .fc-button-primary {
      background-color: #1c7dd6; 
      border-color: #1c7dd6; 
    }

    .fc .fc-button-secondary {
      background-color: #6c757d; 
      border-color: #6c757d; 
    }
    #calendar_control {
        width: 100%;
        height: 75vh;
    }

    .modal{
      width:60%;
      margin:auto;
      margin-top:2%;
    }
    .switch-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.switch {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 15px;
    border-radius: 25px;
    background: #f8f9fa;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    height: 35px;
    width: max-content;
}

.switch input {
    display: none; /* Hide default checkbox */
}

.switch-toggle {
    width: 45px;
    height: 24px;
    background: #ccc;
    border-radius: 15px;
    position: relative;
    transition: background 0.3s;
    display: inline-block;
}

.switch-toggle:before {
    content: '';
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.switch input:checked + .switch-toggle {
    background: #007bff;
}

.switch input:checked + .switch-toggle:before {
    transform: translateX(21px);
}

.switch-label {
    font-size: 14px;
    font-weight: 600;
    color: #000;
    user-select: none;
    margin-left: 10px; /* Keep label next to the toggle */
    white-space: nowrap; /* Prevent label from wrapping */
}




.custom-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.manual-datepicker[readonly] {
    background-color: #fff !important;
    cursor: pointer !important;
}
       
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 0.8em;
}

.form-control {
    transition: border-left-color 0.3s ease;
}

/* Add these new styles */
.color-index {
    background: white;
    padding: 2px 8px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    gap: 10px; /* Space between items */
}

.index-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
    margin-left: 8px;
}

.color-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 4px;
}

.index-text {
    color: #666;
    font-size: 11px;
}

.date-label {
    font-weight: bold;
    color: #666;
    display: inline-block;
    width: 45px;
}

.card-text {
    margin-bottom: 0.5rem;
    color: #333;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 0.8em;
    text-transform: capitalize;
}

.no-data-display {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.card-title {
    margin-bottom: 1rem;
    font-weight: 600;
}
    
.datepicker {
    background-color: #fff !important;
    cursor: pointer !important;
}

.form-group {
    margin-bottom: 1rem;
}

/* Add this CSS to the <style> section */

.delete-icon {
    font-size: 1.2rem;
    color: red;
    cursor: pointer;
    transition: color 0.3s ease;
}

.delete-icon:hover {
    color: darkred;
}
</style>
