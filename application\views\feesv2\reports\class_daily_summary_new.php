<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Class Level Date-wise Summary</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
            Class Level Date-wise Summary
            </h3>
           <!--   <label class="checkbox-inline"><input type="checkbox" name="class_section" id="classSectionID"><span style="font-size:16px; margin-left: 10px;">Class/Sections</span></label> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: 2px;">Select Fee Type <font color="red">*</font></p>
            <select class="form-control" id="blueprint_type" required="" name="fee_type">
              <option value=""><?php echo 'Select Fee Type' ?></option>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>

          <div class="col-md-2 form-group">
            <p style="margin-bottom: 2px;">Date range <font color="red">*</font></p>
            <div id="reportrange" class="dtrange" style="width: 100%">                                            
                <span></span>
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
            </div>
        </div>

          <div class="col-md-2 form-group">
            <br>
            <input type="button" value="Get Report" id="getReport" class="btn btn-primary" style=" margin-bottom: -1rem; ">
          </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
      </div>
     
      <div class="col-md-12 pt-2" style="overflow: hidden;">

        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
        <div id="printArea">
          <div class="total_summary">
          </div>

          <div id="fees_student_status" class="fee_balance pt-3 table-responsive">
            
          </div>
        </div>

      </div>
    </div>


  </div> 
</div>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
  $(document).ready(function() {
    $("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
      applyClass: 'btn-small btn-primary',
      cancelClass: 'btn-small',
      format: 'DD-MM-YYYY',
      separator: ' to ',
      startDate: moment().subtract(6, 'days'),
      endDate: moment()            
      },function(start, end) {
      $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
      $('#from_date').val(start.format('DD-MM-YYYY'));
      $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));
  });
  function print_fees_status() {
    const printWindow = window.open('', '_blank');

    // Get the summary and main table HTML
    const summaryTable = document.querySelector('.total_summary')?.outerHTML || '';
    const mainTable = document.getElementById('fee_summary_data')?.outerHTML || '';

    printWindow.document.write(`
      <html>
        <head>
          <title>Class Wise Fee Summary Report</title>
          <style>
            body {
              font-family: 'Poppins', sans-serif;
              padding: 20px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 15px 0;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              font-size: 12px;
            }
            h3 {
              margin: 15px 0;
            }
            a {
              color: inherit !important;
              text-decoration: none !important;
            }
            @media print {
              table { page-break-inside: auto; }
              tr { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <h3>Class Wise Fee Summary</h3>
          ${summaryTable}
          <h3>Class Wise Details</h3>
          ${mainTable}
          <script>
            window.onload = function() {
              window.print();
            };
            window.onafterprint = function() {
              window.close();
            };
          <\/script>
        </body>
      </html>
    `);

    printWindow.document.close();
  }

  function exportToExcel_fee_status(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var mainTable = $("#printArea").html();

    htmls =mainTable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }
</script>
<style type="text/css">
  /* input['range'] {
    margin: 0 auto;
    width: 100px;
  } */

  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }
</style>

<script>

  var loan_column = '<?php echo $loan_column ?>';
  var adjust = '<?php echo $adjustment ?>';
  var fineAmount_config = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
  var refund =  '<?php echo $feeRefund ?>';
  var completed = 0;
  var total_students =0;
  var total_fee = 0;
  var total_collected_amount = 0;
  var concession = 0;
  var adjustment = 0;
  var total_fine_amount = 0;
  var balance = 0;
  var std_count = 0;
  var fineAmount = 0;
  var trfundAmount = 0;
  var  total_loan_provider_charges = 0;
  var paid_amount=0;
  $(document).ready(function(){
    $('#getReport').on('click',function(){
      var fee_type = $('#blueprint_type').val();
      if (fee_type =='') {
        return false;
      }
      $('#getReport').prop('disabled', true).val('Please wait...');
      $('.loading-icon').show();
      total_students = 0;
      completed = 0;
      total_fee = 0;
      total_collected_amount = 0;
      concession = 0;
      adjustment = 0;
      total_fine_amount = 0;
      balance = 0;
      paid_amount = 0;
      std_count = 0;
      fineAmount = 0;
      trfundAmount = 0;
      total_loan_provider_charges = 0;
      total_discount = 0
      $('.fee_balance').html('');
      $('.total_summary').html('');

      $('input[type="checkbox"]').click(function(){
        if($(this).prop("checked") == true){
          $('#classSectionID').val('1');
        }
        else if($(this).prop("checked") == false){
          $('#classSectionID').val('0');
        }
      });
      var  classSectionId = '';
      if($(this).prop("checked") == true){
        classSectionId = 1;
      }

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/get_fee_class_daily_summary_student_count'); ?>',
        data: {'fee_type':fee_type, 'classSectionId':classSectionId},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          var data = JSON.parse(data);
          var students = data;
          classidS = students;
          total_students = parseInt(2* (classidS.length - 2)) + parseInt(classidS[classidS.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*2+'%';
          $("#progress").show();
          callReportGetter(0);
        },
        error: function (err) {
          $('.loading-icon').hide();
          $('#getReport').prop('disabled', false).val('Get Report');
          console.log(err);
        }
      });
    });
  });

  function getReport(index) {
    var from_date = $("#from_date").val();
    var to_date = $("#to_date").val();
    var class_ids = classidS[index];
    var  classSectionId = '';
    if($(this).prop("checked") == true){
      classSectionId = 1;
    }
    var fee_type = $('#blueprint_type').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_fee_class_daily_summary_student_details'); ?>',
      data: {class_ids:class_ids, 'fee_type':fee_type,'classSectionId':classSectionId, 'from_date':from_date, 'to_date':to_date},
      type: "post",
      success: function (data) {
        var data = JSON.parse(data);
        var classSummaryStudent = data;

        // console.log(students);
        if (index == 0) {
          constructConcessionHeader();
        }
        construct_balance_summary(classSummaryStudent);
        completed += Object.keys(classSummaryStudent).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*2+'%';
        construct_concession_table(index, classSummaryStudent);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  
  function constructConcessionHeader(header) {
    var h_output = '<div class="sticky-table-container"><table id="fee_summary_data" class="table table-bordered">';
    h_output += '<thead>';
    h_output += '<tr>';
    h_output += '<th>Class</th>';
    h_output += '<th># of Fee Assigned Students</th>';
    h_output += '<th>Fee Amount</th>';
    h_output += '<th>Collected Amount</th>';
    if (refund) {
       h_output += '<th>Refund</th>';
    }
    h_output += '<th>Concession</th>';
    if (adjust) {
      h_output += '<th>Adjustment</th>';
    }
    
    if (loan_column !=0) {
      h_output += '<th>Loan Provider Chargess</th>';
    }
    h_output += '<th>Discount</th>';
    h_output += '<th>Balance</th>';
    if (fineAmount_config) {
      h_output += '<th>Fine</th>';
    }
  
    h_output += '</tr>';
    h_output += '</thead>';
    h_output += '<tbody>';
    h_output += '</tbody>';
    h_output += '</table></div>';
    $('.fee_balance').html(h_output);
  }

  function construct_concession_table(index, classSummaryStudent) {
    // console.log(classSummaryStudent);
    var fee_type = $('#blueprint_type').val();
    html = '';
    var j=0;
    for(var i in classSummaryStudent) {
      html += '<tr>';
      html += '<td>'+classSummaryStudent[i].class_name+'</td>';
      html += '<td><a target="_blank" href="<?php echo site_url('feesv2/reports_v2/student_wise_fees_details/') ?>'+fee_type+'/'+i+' ">'+classSummaryStudent[i].student_count+'</a> </td>';
      html += '<td>'+numberToCurrency(classSummaryStudent[i].total_fee)+'</td>';
      html += '<td>'+numberToCurrency(classSummaryStudent[i].paid_amount)+'</td>';
      if (refund) {
        html += '<td> ( '+numberToCurrency(classSummaryStudent[i].refund_amount)+' )</td>';
      }
      html += '<td> ( '+numberToCurrency(classSummaryStudent[i].concession)+' ) </td>';
      if (adjust) {
        html += '<td> ( '+numberToCurrency(classSummaryStudent[i].adjustment)+' )</td>';
      }
      
      if (loan_column) {
         html += '<td>'+numberToCurrency(classSummaryStudent[i].loan_provider_charges)+'</td>';
      }
      html += '<td>'+numberToCurrency(classSummaryStudent[i].discount)+'</td>';
      html += '<td>'+numberToCurrency(classSummaryStudent[i].balance + classSummaryStudent[i].refund_amount)+'</td>';
      if (fineAmount_config) {
        html += '<td> ( '+numberToCurrency(classSummaryStudent[i].fine_amount)+' )</td>';
      }
     
      html += '</tr>';
      j++;
    }
    $('#fee_summary_data').append(html);
    index++;
    callReportGetter(index);
  }

  function construct_balance_summary(classSummaryStudent) {
    for(key in classSummaryStudent){
      total_fee += parseFloat(classSummaryStudent[key].total_fee);
      total_collected_amount += parseFloat(classSummaryStudent[key].total_collected_amount);
      concession += parseFloat(classSummaryStudent[key].concession);
      adjustment += parseFloat(classSummaryStudent[key].adjustment);
      total_fine_amount += parseFloat(classSummaryStudent[key].fine_amount);
      balance += parseFloat(classSummaryStudent[key].balance);
      std_count += parseFloat(classSummaryStudent[key].student_count);
      total_loan_provider_charges += parseFloat(classSummaryStudent[key].loan_provider_charges);
      paid_amount += parseFloat(classSummaryStudent[key].paid_amount);
      trfundAmount += parseFloat(classSummaryStudent[key].refund_amount);
      total_discount += parseFloat(classSummaryStudent[key].discount);
    }
  }

  function callReportGetter(index){
    if(index < classidS.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $('#getReport').prop('disabled', false).val('Get Report');
      $('#exportIcon').show();
      $('#send_fee_sms').show();
      con_summary = '<div class="table-responsive">';
      con_summary +='<table class="table table-bordered">';
      con_summary +='<thead>';
      con_summary +='<tr>';
      con_summary += '<th># of Fee Assigned Students</th>';
      con_summary += '<th>Total Fee Amount</th>';
      con_summary += '<th>Total Collected Amount</th>';
      if (refund) {
         con_summary +='<th>Total Refund</th>';
      }
      con_summary += '<th>Total Concession</th>';
      if (adjust) {
        con_summary += '<th>Total Adjustment</th>';
      }
      
      if (loan_column) {
         con_summary += '<th>Loan Provider Chargess</th>';
      }
      con_summary += '<th>Total Discount</th>';
      con_summary += '<th>Total Balance</th>';
      if (fineAmount_config) {
         con_summary +='<th>Total Fine</th>';
      }
      
      con_summary +='</tr>';
      con_summary +='</thead>';
      con_summary +='<tbody>';
      con_summary += '<tr>';
      con_summary +='<th>'+std_count+'</th>';
      con_summary +='<th>'+numberToCurrency(total_fee)+'</th>';
      con_summary +='<th>'+numberToCurrency(paid_amount)+'</th>';
      if (refund) {
        con_summary +='<th>'+numberToCurrency(trfundAmount)+'</th>';
      }

      con_summary +='<th> ( '+numberToCurrency(concession)+' ) </th>';
      if (adjust) {
        con_summary +='<th> ( '+numberToCurrency(adjustment)+' ) </th>';
      }
      if (loan_column) {
        con_summary += '<th>'+numberToCurrency(total_loan_provider_charges)+'</th>';
      }
      con_summary +='<th>'+numberToCurrency(total_discount)+'</th>';
      con_summary +='<th>'+numberToCurrency(balance + trfundAmount)+'</th>';
      if (fineAmount_config) {
        con_summary +='<th> ( '+numberToCurrency(total_fine_amount)+' ) </th>';
      }
     
      con_summary += '</tr>';
      con_summary +='</tbody>';
      con_summary +='</table>';
      con_summary += '</div>';
      $(".total_summary").html(con_summary);

      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        paging: false, 
        dom: 'Brtip',
        info: false,
        buttons: [
          {
            extend: 'colvis',
            text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
            className: 'btn btn-info',
          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            action: function () {
              print_fees_status();
            },
            title: 'Fee Class wise Summary report',
            footer: true,
            exportOptions: {
              columns: ':visible',
            },
          },
          {
            text: '<button class="btn btn-info" style=" margin-left: -3.5px; "><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
            title: 'Fee Class wise Summary report',
            action: function () {
              exportToExcel_fee_status();
            },
          },
        ],
      });

      // Create a flex container for all controls
      let controlsHtml = `
        <div class="custom-controls">
          <div class="search-box">
            <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
          </div>
          <div class="dt-buttons-search-colvis-print-excel"></div>
        </div>
      `;
      $('.sticky-table-container').before(controlsHtml);

      // Move DataTables buttons into the new container in the desired order
      let $dtButtons = $('#fee_summary_data_wrapper .dt-buttons').children();
      // Order: search (already present), column, print, excel
      $('.dt-buttons-search-colvis-print-excel').append($dtButtons.eq(0)); // Columns
      $('.dt-buttons-search-colvis-print-excel').append($dtButtons.eq(1)); // Print
      $('.dt-buttons-search-colvis-print-excel').append($dtButtons.eq(2)); // Excel
      $('#fee_summary_data_wrapper .dt-buttons').remove(); // Remove the original container

      // Attach search event
      $.fn.dataTable.ext.search.push(
        function(settings, data, dataIndex) {
          var input = $('#table-search').val();
          if (!input) return true; // No filter, show all

          // Join all columns' text for this row, remove extra spaces and formatting
          var rowText = data.join(' ').replace(/[\(\)\₹\,]/g, '').replace(/\s+/g, ' ').toLowerCase();
          input = input.replace(/[\(\)\₹\,]/g, '').replace(/\s+/g, ' ').toLowerCase().trim();

          // Exact phrase match (case-insensitive)
          return rowText.indexOf(input) !== -1;
        }
      );

      // Redraw table on search input
      $(document).on('keyup', '#table-search', function() {
        table.draw();
      });
      
    }
    
  }

  function exportToExcel_fee_status() {
    var htmls = "";
    var uri = "data:application/vnd.ms-excel;base64,";
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="(link unavailable)">' +
      '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
      '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function (s) {
        return window.btoa(unescape(encodeURIComponent(s)));
    };
    var format = function (s, c) {
        return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
        });
    };

    var mainTable = $("#fee_summary_data").clone();
    var summaryTable = $(".total_summary").clone();

    mainTable.find(".dt-buttons").remove();
    summaryTable.find(".distrubanceAmount").remove();
    
    var titleRow = '';
    htmls = titleRow + summaryTable.prop("outerHTML") + "<br>" +mainTable.prop("outerHTML");
    var ctx = { worksheet: "Spreadsheet", table: htmls };
    if (navigator.msSaveOrOpenBlob) {
        var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
        navigator.msSaveOrOpenBlob(blob, "fees_class_wise_summary.xls");
    } else {
        var link = document.createElement("a");
        link.download = "fees_class_wise_summary.xls";
        link.href = uri + base64(format(template, ctx));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>

<style>
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table,
  #fee_summary_data,
  .total_summary table {
    font-family: 'Poppins', sans-serif !important;
  }

  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }

  #fee_summary_data,
  .total_summary table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  #fee_summary_data thead th,
  .total_summary table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }

  #fee_summary_data th,
  #fee_summary_data td,
  .total_summary table th,
  .total_summary table td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
  }

  #fee_summary_data tbody tr:nth-child(even),
  .total_summary table tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  #fee_summary_data tbody tr:hover,
  .total_summary table tbody tr:hover {
    background-color: #f1f5f9;
  }

  #fee_summary_data tfoot tr,
  .total_summary table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }


  .search-box {
    display: flex;
    align-items: center;
    margin-right: 5px;
  }

  .input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
  }

  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }

  .buttons-print,
  .buttons-colvis {
    padding: 2px !important;
  }

  .buttons-excel {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .dt-button {
    border: none !important;
    background: none !important;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .custom-controls {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: flex-end;
  }

  .dt-buttons-search-colvis-print-excel {
    display: flex;
    align-items: center;
  }

  .sticky-table-container {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;
    position: relative;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  #fee_summary_data thead th {
    position: sticky !important;
    top: 0;
    z-index: 10;
    background: #f1f5f9;
  }

  .sticky-table-container::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }

  .sticky-table-container::-webkit-scrollbar-thumb {
    background: #b0b0b0;
    border-radius: 8px;
    border: 4px solid #f1f5f9;
  }

  .sticky-table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 8px;
  }

  /* For Firefox */
  .sticky-table-container {
    scrollbar-width: thick;
    scrollbar-color: #b0b0b0 #f1f5f9;
  }

   .dt-button-collection .buttons-columnVisibility:before {
  content: ' ';
  margin-top: -6px;
  margin-left: 10px;
  border: 1px solid black;
  border-radius: 3px;
}

.dt-button-collection .buttons-columnVisibility:before, .dt-button-collection .buttons-columnVisibility.active span:before {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 0;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
}

.dt-button-collection .buttons-columnVisibility span {
  margin-left: 20px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
  content: '\2714'; /* Unicode checkmark character */
  margin-top: -10px;
  margin-left: 10px;
  text-align: center;
  text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

div.dt-button-collection .dt-button {
    position: relative;
    left: 0;
    right: 0;
    width: 100%;
    display: block;
    float: none;
    background: none;
    margin: 0;
    padding: .5em 1em;
    border: none;
    text-align: left;
    cursor: pointer;
    color: inherit;
}

#fee_summary_data_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }

div.dt-button-collection {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    margin-top: 3px;
    margin-bottom: 3px;
    padding: .75em 0;
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: white;
    overflow: hidden;
    z-index: 2002;
    border-radius: 5px;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
}
</style>