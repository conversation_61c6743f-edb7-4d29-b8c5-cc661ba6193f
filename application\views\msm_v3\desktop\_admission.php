<?php $this->load->view('msm_v3/styles/_landing_css'); ?>
<div class = "card landing_card">
    <div class="container-fluid py-2 pt-3">
        <div class="row mx-0"><?php $this->load->view('msm_v3/desktop/_top_bar'); ?></div>
    </div>
    <div class="container-fluid py-2 pt-3 custom-scrollbar" style="overflow: auto; overflow-x: hidden; padding-left: 1.5rem">

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <!-- <div class="graph_top">
                    <div class="row graph_top_card_row">
                        <div class="col-md-3">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Leads
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_leads">...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Applications
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_applications">...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Converted
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_converted">...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3" style="display: flex;justify-content: end;flex-wrap: wrap;align-content: space-between;">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'admission_management_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'admission_management_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-4" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Admission Funnel</h4>
                            </div>
                            <!-- <div class="col-md-7" id="admission_pipeline_legend_div"></div> -->
                            <div class="col-md-3" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'admission_pipeline_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'admission_pipeline_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Insights Section -->
                        <div class="mt-3" style="margin-left: 1.5rem; margin-right: 1rem; display: none" id="admission_insight_pipeline_div">
                            <div class="card card-body" style="border: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);" id="admission_insights_pipeline_body"></div>
                        </div>
                        <div class="animate__animated animate__pulse" id="admission_pipeline_pie_card" style="display: block; margin: 1rem;">
                            <div id="admission_pipeline_graph" width>
                                <center>Loading Admission Pipleline Graph</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="admission_pipeline_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                                <tr>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7">#</th>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7">Institution</th>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7"># Leads</th>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7"># Applications</th>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7"># Converted</th>
                                    <th class="text-left  font-weight-bolder align-middle text-center text-m text-uppercase opacity-7">% Converted</th>
                                </tr>
                            </thead>
                            <tbody id="pipeline_trend_table">
                            <tr>
                                <td colspan="5" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <button class="btn vertical-text mx-1" style="border: 1px solid #00CC83">Summary <</button>
            </div> -->
        </div>

        <div class="d-flex align-items-center">
            <div class="card mx-0 graph_cards">
                <!-- <div class="graph_top">
                    <div class="row graph_top_card_row">
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Leads
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Admissions
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Converted
                                </div>
                                <div class="card-body">
                                    100
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: auto;">
                    <div style="min-height: 60vh;">
                        <!-- Header with Title and Buttons -->
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="col-md-5" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E; font-weight: bold; font-size: 1.2rem;">Admission Target Vs Converted Applications</h4>
                            </div>

                            <div class="col-md-3" style="display: flex; justify-content: flex-end; margin-right: 1.5rem;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: flex-end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" 
                                            onclick="flip_admission_view('column_chart', 'admission_statistics_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>" alt="Graph View">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" 
                                            onclick="flip_admission_view('table', 'admission_statistics_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>" alt="Table View">
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Insights Section -->
                        <div class="mt-3" style="margin-left: 1.5rem; margin-right: 1rem; display: none" id="admission_insight_div">
                            <div class="card card-body" style="border: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);" id="admission_insights_body"></div>
                        </div>

                        <!-- Graph and Table Cards -->
                        <div class="animate__animated animate__pulse" id="admission_statistics_pie_card" style="display: block; margin: 1rem;">
                            <div id="admission_target_vs_converted_graph">
                                <center>Loading Admission Target Vs Converted Graph...</center>
                            </div>
                        </div>

                        <div class="animate__animated animate__pulse" id="admission_statistics_table_card" style="display: none; margin: 1rem;">
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                                <thead style="color: #0F256E !important;">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Institution</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Target</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Target Achieved</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Pending</th>
                                    </tr>
                                </thead>
                                <tbody id="admission_target_vs_converted_table">
                                    <tr>
                                        <td colspan="6" class="text-left font-weight-bolder text-m text-uppercase opacity-7">
                                            Loading...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div class="mt-3" style="display: flex; justify-content: space-between;">
                            <div class="col-md-3" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Admission Statistics</h4>
                            </div>
                            <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'admission_tvsc_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'admission_tvsc_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="admission_tvsc_pie_card" style="display: block; margin: 1rem;">
                            <div id="admission_statistics_graph">
                                <center>Loading Admissions Statistics Graph</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="admission_tvsc_table_card" style="display: none; margin: 1rem;">
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                                <thead style="color: #0F256E">
                                <tr>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">Institution</th>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">Total Applications</th>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">Converted</th>
                                </tr>
                                </thead>
                                <tbody id="admission_statistics_table">
                                <tr>
                                    <td colspan="6" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
    
        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Incoming Application Trend</h4>
                        </div>
                        <!-- <div class="col-md-3" id="admission_application_legend_div"></div> -->
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_adm_appl" style="width: 50%; margin-right: 3%" aria-label="Select Schools">
                            </select>
                            <!-- <button type="button" id="application_loadButton" class="btn btn-outline highlight" style="margin-right: 3%">Load</button> -->
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'admission_application_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'admission_application_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="admission_application_pie_card" style="display: block; margin: 1rem;">
                        <div id="admission_application_trend_graph">
                            <center>Loading Admissions Application Trend Graph</center>
                        </div>
                        <div id="admission_application_date_type" style="display: none; justify-content: center">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;" id="admission_application_date_type_inner">
                                <button type="button" class="btn btn-outline highlight" id="weekButton" style="border-color: #00CC83" onclick="change_dates_for_application_trend('week', this)">
                                    Week
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="change_dates_for_application_trend('month', this)">
                                    Month
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="admission_application_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Date</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="admission_application_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between; align-items: center;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Admission Statuswise Graph</h4>
                        </div>
                        <div class="col-md-5" style="display: flex; justify-content: flex-end; margin-right: 1%">
                            <select class="form-select" id="dropdownSchool_admission" style="width: 55%;" aria-label="Select Schools"></select>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse row" id="admission_statuswise_pie_card" style="display: none; margin: 1rem;">
                        <div class="col-md-6 d-flex justify-content-center" id="admission_statuswise_reporting_graph">
                            <div id="chart2" style="width: 100%; height: 400px;">
                                <center>Loading Admission Status Wise Graph</center>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex justify-content-center" id="admission_statuswise_user_graph">
                            <div id="chart1" style="width: 100%; height: 400px;">
                                <center>Loading Admission Status Wise Graph</center>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="admission_statuswise_table_card" style="display: flex; flex-wrap: wrap; margin: 1rem;">
                        <!-- Overall Status Graph Section -->
                        <div class="col-md-6 d-flex flex-column align-items-center" style="margin-bottom: 1.5rem;">
                            <h6 class="text-center font-weight-bold">Overall Status Graph</h6>
                            <table class="table-sm table-bordered mb-0" width="90%">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Status</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                    </tr>
                                </thead>
                                <tbody id="admission_statuswise_reporting_table">
                                    <tr>
                                        <td colspan="3" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Granular Status Graph Section -->
                        <div class="col-md-6 d-flex flex-column align-items-center" style="margin-bottom: 1.5rem;">
                            <h6 class="text-center font-weight-bold">Granular Status Graph</h6>
                            <table class="table-sm table-bordered mb-0" width="90%">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Status</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                    </tr>
                                </thead>
                                <tbody id="admission_statuswise_user_table">
                                    <tr>
                                        <td colspan="3" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="card mx-0 graph_cards">
            <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Admission Activity Trend</h4>
                        </div>
                        <div class="col-md-3" id="admission_activity_legend_div"></div>
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_adm_act" style="width: 55%; margin-right: 5%" aria-label="Select Schools">
                            </select>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'admission_activity_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'admission_activity_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="admission_activity_pie_card" style="display: block; margin: 1rem;">
                        <div id="admission_activity_trend_graph">
                            <center>Loading Admissions Activity Trend Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="admission_activity_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Date</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="admission_activity_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- <div class="card mx-0 graph_cards">
            <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-5" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Leads Converted</h4>
                        </div>
                        <div class="col-md-3" id="leads_conversion_statistics_legend_div"></div>
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'leads_conversion_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'leads_conversion_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3" style="margin-left: 2%; display: none" id="leads_insight_div">
                        <div class="card card-body" style="border: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);" id="leads_insights_body"></div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_conversion_pie_card" style="display: block; margin: 1rem;">
                        <div id="leads_conversion_statistics_graph">
                        <center>Loading Leads Conversion Statistics Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_conversion_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-center text-uppercase opacity-7">#</th>
                                <th class="font-weight-bolder text-m text-center text-uppercase opacity-7">Institution</th>
                                <th class="font-weight-bolder text-m text-center text-uppercase opacity-7">Total</th>
                                <th class="font-weight-bolder text-m text-center text-uppercase opacity-7">Converted</th>
                            </tr>
                            </thead>
                            <tbody id="leads_conversion_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div> -->

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Incoming Leads Trend</h4>
                        </div>
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_lead_eq" style="width: 55%; margin-right: 5%" aria-label="Select Schools">
                            </select>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'leads_enquiry_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'leads_enquiry_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_enquiry_pie_card" style="display: block; margin: 1rem;">
                        <div id="leads_enquiry_trend_graph">
                            <center>Loading Leads Enquiry Trend Graph</center>
                        </div>
                        <div id="leads_enquiry_date_type" style="display: none; justify-content: center">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;" id="leads_enquiry_date_type_inner">
                                <button type="button" class="btn btn-outline highlight" id="weekButton_leads_eq" style="border-color: #00CC83" onclick="change_dates_for_leads_enquiry('week', this)">
                                    Week
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="change_dates_for_leads_enquiry('month', this)">
                                    Month
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_enquiry_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Date</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="leads_enquiry_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-3" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Leads Enquiry Comparision</h4>
                        </div>
                        <div class="col-md-8" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_lead_comp" style="width: 25%; margin-right: 2%" aria-label="Select Schools">
                            </select>
                            <select id="dropdownSchool_lead_comp_ay" class="form-control select2" style="height: 100px" multiple="multiple">
                                <option value="">Select Year</option>    
                                <option value="22">2022-23</option>
                                <option value="23">2023-24</option>
                                <option value="24">2024-25</option>
                                <option value="25">2025-26</option>
                            </select>
                            <button type="button" id="leadsComp_loadButton" class="btn btn-outline highlight" style="margin-right: 2%; margin-left: 2%" onclick="load_schools_for_comparision()">Load</button>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'leads_enquiry_comparision_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'leads_enquiry_comparision_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_enquiry_comparision_pie_card" style="display: block; margin: 1rem;">
                        <div id="leads_enquiry_comparision_trend_graph">
                            <center>Loading Leads Enquiry Trend Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_enquiry_comparision_table_card" style="display: none; margin: 1rem;">
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Leads Activity Trend</h4>
                        </div>
                        <!-- <div class="col-md-3" id="leads_activity_legend_div"></div> -->
                        <div class="col-md-6" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_lead_act" style="width: 55%; margin-right: 5%;" aria-label="Select Schools">
                            </select>
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_admission_view('column_chart', 'leads_activity_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_admission_view('table', 'leads_activity_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_activity_pie_card" style="display: block; margin: 1rem;">
                        <div id="leads_activity_trend_graph">
                            <center>Loading Incoming Leads Trend Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_activity_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Date</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="leads_activity_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between; align-items: center;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Leads Statuswise Graph</h4>
                        </div>
                        <div class="col-md-5" style="display: flex; justify-content: flex-end; margin-right: 1%">
                            <select class="form-select" id="dropdownSchool_leads" style="width: 55%;" aria-label="Select Schools"></select>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse row" id="leads_statuswise_pie_card" style="display: none; margin: 1rem;">
                        <div class="col-md-6 d-flex justify-content-center" id="leads_statuswise_reporting_graph">
                            <div id="chart2" style="width: 100%; height: 400px;">
                                <center>Loading Leads Status Wise Graph</center>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex justify-content-center" id="leads_statuswise_user_graph">
                            <div id="chart1" style="width: 100%; height: 400px;">
                                <center>Loading Leads Status Wise Graph</center>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_statuswise_table_card" style="display: flex; flex-wrap: wrap; margin: 1rem;">
                        <div class="col-md-6 d-flex flex-column align-items-center" style="margin-bottom: 1.5rem;">
                            <h6 class="text-center font-weight-bold">Overall Status Graph</h6>
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 0 auto;">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Status</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                    </tr>
                                </thead>
                                <tbody id="leads_statuswise_reporting_table">
                                    <tr>
                                        <td colspan="3" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="col-md-6 d-flex flex-column align-items-center" style="margin-bottom: 1.5rem;">
                            <h6 class="text-center font-weight-bold">Granular Status Graph</h6>
                            <table class="table-sm table-bordered mb-0" width="90%">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Status</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                    </tr>
                                </thead>
                                <tbody id="leads_statuswise_user_table">
                                    <tr>
                                        <td colspan="3" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh;">
                    <div class="mt-3" style="display: flex; justify-content: space-between; align-items: center;">
                        <div class="col-md-4" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Leads Sourcewise Graph</h4>
                        </div>
                        <div class="col-md-5" style="display: flex; justify-content: flex-end; margin-right: 1%">
                            <select class="form-select" id="dropdownSchool_leads_source" style="width: 55%;" aria-label="Select Schools"></select>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="leads_sourcewise_table_card" style="display: flex; flex-wrap: wrap; margin: 1rem;">
                        <div style="width: 100%;">
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 0 auto;">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">#</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Source</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Converted Count</th>
                                        <th class="font-weight-bolder text-m text-uppercase opacity-7">Converted %</th>
                                    </tr>
                                </thead>
                                <tbody id="leads_sourcewise_table">
                                    <tr>
                                        <td colspan="5" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-6" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Count Prediction</h4>
                        </div>
                        <div class="col-md-4" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_student_tc" aria-label="Select Schools" style="width: 50%; margin-right: 5%">
                            </select>
                            <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="export_to_excel()">
                                <img src="<?php echo base_url('assets/msm_v3/img/download_green_svg.svg'); ?>">
                            </button>
                        </div>
                    </div>
                    
                    <div class="animate__animated animate__pulse custom-scrollbar" id="student_tc_table_card" style="display: block; margin: 1rem; overflow-y: auto; height: 60vh;">
                        <div id="student_tc_table" class="table-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('msm_v3/styles/_admission_css'); ?>
<?php $this->load->view('msm_v3/scripts/_admission_script'); ?>