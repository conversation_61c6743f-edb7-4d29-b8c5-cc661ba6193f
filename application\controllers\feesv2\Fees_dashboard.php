<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  12 Jan 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_dashboard
 */
class Fees_dashboard extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEESV2')) {
      redirect('dashboard', 'refresh');
    }
  } 

  public function index(){

    /*$data['permit_collect_fees'] = $this->authorization->isAuthorized('FEESV2.COLLECT_FEES');
    $data['permit_view_daily_report'] = $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT');
    $data['permit_view_balance_report'] = $this->authorization->isAuthorized('FEESV2.VIEW_BALANCE_REPORT');
    $data['permit_view_online_tx_repot'] = $this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT');
    $data['permit_view_concessions'] = $this->authorization->isAuthorized('FEESV2.VIEW_CONCESSIONS');
    $data['permit_assign_invoice'] = $this->authorization->isAuthorized('FEESV2.ASSIGN_INVOICE');
    $data['permit_view_fee_summary'] = $this->authorization->isAuthorized('FEESV2.FEE_SUMMARY');
    $data['denomination'] = $this->authorization->isAuthorized('FEESV2.DENOMINATION');
    // $data['reconciled'] = $this->authorization->isAuthorized('FEESV2.RECONCILED');
    $data['non_reconciled'] = $this->authorization->isAuthorized('FEESV2.NON_RECONCILED');
    $data['daily_report_sms'] = $this->authorization->isAuthorized('FEESV2.DAILY_REPORT_SMS');
    $data['class_wise_report'] = $this->authorization->isAuthorized('FEESV2.CLASS_WISE_REPORT');
    $data['permit_assign_stop'] = $this->authorization->isAuthorized('FEESV2.PERMIT_ASSING_STOP');
    $data['permit_fast_fee_collection'] = $this->authorization->isAuthorized('FEESV2.FAST_COLLECTION');
    $data['receipt_cancel_report'] = $this->authorization->isAuthorized('FEESV2.CANCELED_REPORT');
    $data['receipts_change'] = $this->authorization->isAuthorized('FEESV2.RECEIPT_CHANGE');
    $data['consolidatedReport'] = $this->authorization->isAuthorized('FEESV2.CONSOLIDATED_FEE_REPORT');
    $data['classwiseDatewiseReport'] = $this->authorization->isAuthorized('FEESV2.CLASS_WISE_DAILY_REPORT');
    $data['fine_amount'] = $this->authorization->isAuthorized('FEESV2.FINE_AMOUNT_ASSIGN');
    $data['daily_tx_prarthana'] = $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT_PRARTHANA');
    $data['concessions_assign'] = $this->authorization->isAuthorized('FEESV2.CONCESSIONS_ASSIGN');
    $data['day_book_aps'] = $this->authorization->isAuthorized('FEESV2.DAY_BOOK_APS');
    $data['refund_reports'] = $this->authorization->isAuthorized('FEESV2.REFUND_REPORTS');*/

    $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Fee Collection',
          'sub_title' => 'Fast Fee Collection for students',
          'icon' => 'svg_icons/collectfees.svg',
          'url' => $site_url.'feesv2/fees_collection',
          'permission' => $this->authorization->isAuthorized('FEESV2.COLLECT_FEES')
        ],
        // [
        //   'title' => 'Fast Fee Collection',
        //   'sub_title' => 'Collect All fees for students',
        //   'icon' => 'svg_icons/collectfees.svg',
        //   'url' => $site_url.'feesv2/fees_fast_collection',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.FAST_COLLECTION')
        // ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['daily_tiles'] = array(
        [
          'title' => 'Daily Transaction',
          'sub_title' => 'View Daily Transaction reports',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'feesv2/reports/day_books1',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT')
        ],
        [
          'title' => 'Daily Transaction New',
          'sub_title' => 'View Daily Transaction reports',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'feesv2/reports_v3/day_books1',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Daily Transaction online',
          'sub_title' => 'View Daily Transaction online settlement',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'feesv2/reports/daily_transaction',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_ONLINE_TX')
        ],
        [
          'title' => 'Account-level summary',
          'sub_title' => 'View account amount student wise',
          'icon' => 'svg_icons/daywiseaccountdetails.svg',
          'url' => $site_url.'feesv2/reports/day_books_account',
          'permission' => $this->authorization->isAuthorized('FEESV2.DAY_BOOK_APS')
        ],
        [
          'title' => 'Account-level Transaction',
          'sub_title' => 'View account amount transactions wise',
          'icon' => 'svg_icons/daywiseaccountdetails.svg',
          'url' => $site_url.'feesv2/reports/day_books_account_transaction',
          'permission' => $this->authorization->isAuthorized('FEESV2.DAY_BOOK_APS')
        ],
        [
          'title' => 'Daily Transactions Fee Type',
          'sub_title' => '(Prarthana)',
          'icon' => 'svg_icons/feetype.svg',
          'url' => $site_url.'feesv2/reports/daily_transcation_prarthana_new',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT_PRARTHANA')
        ],
        // [
        //   'title' => 'Class-level Summary',
        //   'sub_title' => 'Daily transactions',
        //   'icon' => 'svg_icons/feetype.svg',
        //   'url' => $site_url.'feesv2/reports/class_wise_date_wise_report',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.CLASS_WISE_DAILY_REPORT')
        // ],
        [
          'title' => 'Daily Challan Details',
          'sub_title' => 'View Receipt Amount and Bank Amount (WPL)',
          'icon' => 'svg_icons/staffreport.svg',
          'url' => $site_url.'feesv2/reports/daily_transcation_summary',
          'permission' => $this->authorization->isAuthorized('FEESV2.DAILY_REPORT_SMS')
        ],
        [
          'title' => 'Tally Report',
          'sub_title' => 'Tally',
          'icon' => 'svg_icons/feetype.svg',
          'url' => $site_url.'feesv2/reports/tally_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.TALLY_DAILY_REPORT')
        ],
        [
          'title' => 'Fee Collection summary Date Wise',
          'sub_title' => 'Fee Collection summary Date wise',
          'icon' => 'svg_icons/management.svg',
          'url' => $site_url.'feesv2/reports_v2/management_summary_date_wise',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_COLLECTION_SUMMARY_DATE_WISE')
        ]
    );
    $data['daily_tiles'] = checkTilePermissions($data['daily_tiles']);

    $data['std_tiles'] = array(
        [
          'title' => 'Balance / SMS Report',
          'sub_title' => 'Send SMS',
          'icon' => 'svg_icons/balancereport.svg',
          'url' => $site_url.'feesv2/reports_v2/balance_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_BALANCE_REPORT')
        ],
        [
          'title' => 'Fee Detail Report',
          'sub_title' => 'Student-wise Fees Report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'feesv2/reports_v2/student_wise_fees_details',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_SUMMARY')
        ],
        [
          'title' => 'Fee Detail Report (Component Wise)',
          'sub_title' => 'Student-wise Fees Report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'feesv2/reports_v2/student_component_wise_fees_details',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_COMPONENT_DETAIL_REPORT')
        ],
        [
          'title' => 'Fee Detail Summary Report',
          'sub_title' => 'Student-wise Summary Report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'feesv2/reports_v2/student_wise_fees_summary_details',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_SUMMARY_REPORT')
        ],
        [
          'title' => 'Fee Detail Summary Report V2',
          'sub_title' => 'Student-wise Summary Report (Beta)',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'feesv2/reports_v2/student_wise_fees_summary_details_v2',
          'permission' => $this->authorization->isSuperAdmin()
        ],
        [
          'title' => 'Fee collection Status',
          'sub_title' => 'Fee collection student wise status',
          'icon' => 'svg_icons/student.svg',
          'url' => $site_url.'feesv2/reports_v2/fees_collection_status',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEES_COLLECTION_STATUS')
        ],
        [
          'title' => 'Fee Component Detail Report',
          'sub_title' => 'Fee Component Detail Report',
          'icon' => 'svg_icons/summary.svg',
          'url' => $site_url.'feesv2/reports_v2/fees_component_detail',
          'permission' => $this->authorization->isSuperAdmin()
        ]
       
    );
    $data['std_tiles'] = checkTilePermissions($data['std_tiles']);

    $data['cls_tiles'] = array(
        [
          'title' => 'Class-level summary',
          'sub_title' => 'Overall Class wise summary',
          'icon' => 'svg_icons/class.svg',
          'url' => $site_url.'feesv2/reports/class_summary_new',
          'permission' => $this->authorization->isAuthorized('FEESV2.CLASS_WISE_REPORT')
        ],
        [
          'title' => 'Class-level Date-wise summary',
          'sub_title' => 'Overall Class and Date wise summary',
          'icon' => 'svg_icons/class.svg',
          'url' => $site_url.'feesv2/reports/class_daily_summary_new',
          'permission' => $this->authorization->isAuthorized('FEESV2.CLASS_WISE_DATE_WISE_REPORT')
        ],
        [
          'title' => 'Management Fee summary',
          'sub_title' => 'Overall Fee summary',
          'icon' => 'svg_icons/management.svg',
          'url' => $site_url.'feesv2/reports_v2/management_summary',
          'permission' => $this->authorization->isAuthorized('FEESV2.MANAGEMENT_SUMMARY')
        ],
        [
          'title' => 'Fees assigned summary',
          'sub_title' => 'Class-wise fee assigned summary',
          'icon' => 'svg_icons/assigninvoicetostudents.svg',
          'url' => $site_url.'feesv2/fees_student_v2/view_fee_assign_view_count',
          'permission' => $this->authorization->isAuthorized('FEESV2.CLASSWISE_STUDENT_FEE_COUNT')
        ]
    );
    $data['cls_tiles'] = checkTilePermissions($data['cls_tiles']);

    $data['online_tiles'] = array(
        [
          'title' => 'Transaction Report',
          'sub_title' => 'Overall Online payment status',
          'icon' => 'svg_icons/transactionreport.svg',
          'url' => $site_url.'payment_controller/online_transaction_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')
        ],

        [
          'title' => 'Receipt Not Genrated Online Transaction Report',
          'sub_title' => 'Receipt Not Genrated transacations',
          'icon' => 'svg_icons/transactionreport.svg',
          'url' => $site_url.'payment_controller/failed_online_transaction_report',
          'permission' => $this->authorization->isSuperAdmin()
        ],

        [
          'title' => 'Online Challan Report',
          'sub_title' => 'Online Challan payment status',
          'icon' => 'svg_icons/transactionreport.svg',
          'url' => $site_url.'payment_controller/online_challan_payment_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_CHALLAN_TX_REPORT')
        ],
        [
          'title' => 'Settlement Report',
          'sub_title' => 'Settlement amount date wise',
          'icon' => 'svg_icons/settlementreport.svg',
          'url' => $site_url.'payment_controller/online_settlement_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.ONLINE_SETTLEMENT_REPORT')
        ],
        [
          'title' => 'Overview Settlement Report',
          'sub_title' => 'Overview Settlement amount date wise',
          'icon' => 'svg_icons/settlementreport.svg',
          'url' => $site_url.'payment_controller/online_settlement_report_consolidated',
          'permission' => $this->authorization->isAuthorized('FEESV2.OVERVIEW_ONLINE_SETTLEMENT_REPORT')
        ],
        [
          'title' => 'Online Refund Report',
          'sub_title' => 'Online Refund amount date wise',
          'icon' => 'svg_icons/settlementreport.svg',
          'url' => $site_url.'payment_controller/online_refund_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.ONLINE_REFUND_TX_REPORT')
        ]
    );
    $data['online_tiles'] = checkTilePermissions($data['online_tiles']);

    $data['other_tiles'] = array(
        [
          'title' => 'Concession Report',
          'sub_title' => 'Concession amount student wise',
          'icon' => 'svg_icons/concessionreport.svg',
          'url' => $site_url.'feesv2/reports/concessions_new',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_CONCESSIONS')
        ],
        [
          'title' => 'Concession Day Report',
          'sub_title' => 'Concession amount student wise',
          'icon' => 'svg_icons/concessionreport.svg',
          'url' => $site_url.'feesv2/reports/concessions_day_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_CONCESSIONS_DATE_WISE')
        ],
        [
          'title' => 'Adjustment Report',
          'sub_title' => 'Search Adjustment amount student wise',
          'icon' => 'svg_icons/fees.svg',
          'url' => $site_url.'feesv2/reports_v2/adjustment_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.ADJUSTMENT_REPORT')
        ],
        [
          'title' => 'Fine Report',
          'sub_title' => 'Search Fine amount student wise',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'feesv2/reports_v2/fine_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.ADJUSTMENT_REPORT')
        ],
        [
          'title' => 'Fine Waiver Report',
          'sub_title' => 'Search Fine Waiver amount student wise',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'feesv2/reports_v2/fine_waiver_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.FINE_WAIVER_REPORT')
        ],
        [
          'title' => 'Excess Report',
          'sub_title' => 'view Excess Report',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'feesv2/reports_v2/excess_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.EXCESS_REPORT')
        ],
        [
          'title' => 'Non Reconciled Report',
          'sub_title' => 'View and reconciled amount.',
          'icon' => 'svg_icons/nonreconciledreport.svg',
          'url' => $site_url.'feesv2/reports/reconciled_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.NON_RECONCILED')
        ],
        [
          'title' => 'Refund Report',
          'sub_title' => 'View date wise report',
          'icon' => 'svg_icons/refundreport.svg',
          'url' => $site_url.'feesv2/refund_controller/refund_reports',
          'permission' => $this->authorization->isAuthorized('FEESV2.REFUND_REPORTS')
        ],
        [
          'title' => 'Cancellation Report',
          'sub_title' => 'View cancelled receipt.',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'feesv2/reports/receipt_canceled_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.CANCELED_REPORT')
        ],
        [
          'title' => 'Fee History',
          'sub_title' => 'Search Student wise fee history',
          'icon' => 'svg_icons/fees.svg',
          'url' => $site_url.'feesv2/reports_v2/search_fee_receipt_number',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_STUDENT_HISTORY')
        ],
        [
          'title' => 'Denominations',
          'sub_title' => 'Denominations pdf format download.',
          'icon' => 'svg_icons/denomination.svg',
          'url' => $site_url.'feesv2/reports/denominations',
          'permission' => $this->authorization->isAuthorized('FEESV2.DENOMINATION')
        ],
        [
          'title' => 'Transportation Report',
          'sub_title' => 'Transportation Report.',
          'icon' => 'svg_icons/denomination.svg',
          'url' => $site_url.'feesv2/reports/transportation_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.TRANSPORTATION_REPORT')
        ],
        [
          'title' => 'Edit History Report',
          'sub_title' => 'Edit History Report.',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'feesv2/reports/fees_edit_history_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEES_EDIT_HISTORY_REPORT')
        ],
        [
          'title' => 'Cheques Report',
          'sub_title' => 'View Cheques Report.',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'feesv2/reports/cheques_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.CHEQUE_DEPOSIT_REPORT')
        ]
       
       
    );
    $data['other_tiles'] = checkTilePermissions($data['other_tiles']);

    $data['old_reports'] = array(
        // [
        //   'title' => 'Daily Transaction',
        //   'sub_title' => 'Daily Transactions Date wise',
        //   'icon' => 'svg_icons/dailytransaction.svg',
        //   'url' => $site_url.'feesv2/reports/daily_transcation',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT')
        // ],
       
        // [
        //   'title' => 'Day Book',
        //   'sub_title' => 'View Sales and Fee\'s merged details',
        //   'icon' => 'svg_icons/daybook.svg',
        //   'url' => $site_url.'feesv2/reports/day_books',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.DAY_BOOK_APS')
        // ],
        // [
        //   'title' => 'Balance Report',
        //   'sub_title' => 'View Student wise Balance and send balance SMS',
        //   'icon' => 'svg_icons/balancereport.svg',
        //   'url' => $site_url.'feesv2/reports/fee_balance',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_BALANCE_REPORT')
        // ],
        // [
        //   'title' => 'Summary',
        //   'sub_title' => 'Overall Student wise Fee Amount',
        //   'icon' => 'svg_icons/summary.svg',
        //   'url' => $site_url.'feesv2/reports/student_wise_fee_summary',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.FEE_SUMMARY')
        // ],
        // [
        //   'title' => 'Conoslidated Report',
        //   'sub_title' => 'Overall summary of student fees. Can send balance SMS.',
        //   'icon' => 'svg_icons/consolidatedreport.svg',
        //   'url' => $site_url.'feesv2/reports/consolidated_blueprint_wise_report',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.CONSOLIDATED_FEE_REPORT')
        // ],
        // [
        //   'title' => 'Class-level summary',
        //   'sub_title' => 'Overall Class wise summary',
        //   'icon' => 'svg_icons/class.svg',
        //   'url' => $site_url.'feesv2/reports/class_summary',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.CLASS_WISE_REPORT')
        // ],
        // [
        //   'title' => 'Concession Report',
        //   'sub_title' => 'Concession amount student wise',
        //   'icon' => 'svg_icons/concessionreport.svg',
        //   'url' => $site_url.'feesv2/reports/concessions',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_CONCESSIONS')
        // ],
        // [
        //   'title' => 'Daily Transactions Fee Type',
        //   'sub_title' => '(Prarthana)',
        //   'icon' => 'svg_icons/feetype.svg',
        //   'url' => $site_url.'feesv2/reports/daily_transcation_prarthana',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.VIEW_DAILY_TX_REPORT_PRARTHANA')
        // ],
        
    );
    $data['old_reports'] = checkTilePermissions($data['old_reports']);

    $data['school_tiles'] = array(
        // [
        //   'title' => 'Assign Invoice to Student',
        //   'sub_title' => 'Assign Invoice to Student',
        //   'icon' => 'svg_icons/assigninvoicetostudents.svg',
        //   'url' => $site_url.'feesv2/fees_student/student_assign_publish',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.ASSIGN_INVOICE')
        // ],
        // [
        //   'title' => 'Assign Invoice to Student V2',
        //   'sub_title' => 'Assign Invoice to Student',
        //   'icon' => 'svg_icons/assigninvoicetostudents.svg',
        //   'url' => $site_url.'feesv2/fees_student_v2/student_assign_publish',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.ASSIGN_INVOICE')
        // ],
        [
          'title' => 'Assign Invoice to Student',
          'sub_title' => 'Assign Invoice to Student',
          'icon' => 'svg_icons/assigninvoicetostudents.svg',
          'url' => $site_url.'feesv2/fees_student_v2/fee_assign_to_student',
          'permission' => $this->authorization->isAuthorized('FEESV2.ASSIGN_INVOICE')
        ],
        [
          'title' => 'Receipt Number & Date Change',
          'sub_title' => 'Receipt Number & Date Change',
          'icon' => 'svg_icons/receiptnumberanddatechange.svg',
          'url' => $site_url.'feesv2/fees_student/receipt_number_change',
          'permission' => $this->authorization->isAuthorized('FEESV2.RECEIPT_CHANGE')
        ],
        // [
        //   'title' => 'Transaction Amount Change',
        //   'sub_title' => 'Transaction Amount Change',
        //   'icon' => 'svg_icons/transactionamountchange.svg',
        //   'url' => $site_url.'feesv2/fees_student/transaction_amount_change',
        //   'permission' => $this->authorization->isSuperAdmin()
        // ],
        [
          'title' => 'Assign Fine amount',
          'sub_title' => 'Assign Fine amount',
          'icon' => 'svg_icons/assignfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/fine_amount',
          'permission' => $this->authorization->isAuthorized('FEESV2.FINE_AMOUNT_ASSIGN')
        ],
        [
          'title' => 'Assign Fine Waiver',
          'sub_title' => 'Assign Fine Waiver',
          'icon' => 'svg_icons/assignfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/waiver_amount',
          'permission' => $this->authorization->isAuthorized('FEESV2.FINE_AMOUNT_WAIVER')
        ],
        // [
        //   'title' => 'Assign Concession',
        //   'sub_title' => 'Assign Concession',
        //   'icon' => 'svg_icons/assignconcession.svg',
        //   'url' => $site_url.'feesv2/fees_student/concession',
        //   'permission' => $this->authorization->isAuthorized('FEESV2.CONCESSIONS_ASSIGN')
        // ],
        [
          'title' => 'Assign/Approve Concession',
          'sub_title' => 'Assign Concession (Approver)',
          'icon' => 'svg_icons/assignconcession.svg',
          'url' => $site_url.'feesv2/fees_student_v2/assing_concession',
          'permission' => $this->authorization->isAuthorized('FEESV2.CONCESSIONS_ASSIGN_APPROVER')
        ],
        [
          'title' => 'Re-generate PDF Receipt',
          'sub_title' => 'Re-generate PDF Receipt',
          'icon' => 'svg_icons/regeneratepdfreceipt.svg',
          'url' => $site_url.'feesv2/fees_collection/re_generate_pdf',
          'permission' => $this->authorization->isAuthorized('FEESV2.RE_GENERATE_PDF_RECEIPTS')
        ],
        [
          'title' => 'Fee Audit Log',
          'sub_title' => 'Fee Audit Log report',
          'icon' => 'svg_icons/reportcardnew.svg',
          'url' => $site_url.'feesv2/fees_student/fee_audit_log',
          'permission' => $this->authorization->isAuthorized('FEESV2.FEE_AUDIT_LOG')
        ],
        [
          'title' => 'Pre-defined concessions',
          'sub_title' => 'Pre-defined concessions',
          'icon' => 'svg_icons/reportcardnew.svg',
          'url' => $site_url.'feesv2/fees_student/pre_defined_concession',
          'permission' => $this->authorization->isAuthorized('FEESV2.PREDEFINED_CONCESSION')
        ],
        [
          'title' => 'Refund-Transaction',
          'sub_title' => 'Refund-Transaction',
          'icon' => 'svg_icons/reportcardnew.svg',
          'url' => $site_url.'feesv2/fees_student/refund_transaction',
          'permission' => $this->authorization->isAuthorized('FEESV2.REFUND_TRANSACTION')
        ],
        [
          'title' => 'Edit Fees Amount',
          'sub_title' => 'Edit Fees Amount',
          'icon' => 'svg_icons/editfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/edit_fee_structre',
          'permission' => $this->authorization->isAuthorized('FEESV2.EDIT_FEES_AMOUNT')
        ],
        [
          'title' => 'Create invoices',
          'sub_title' => 'Create Invoice',
          'icon' => 'svg_icons/editfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/create_invoice',
          'permission' => $this->authorization->isAuthorized('FEESV2.CREATE_INVOICES')
        ],

        [
          'title' => 'Fees Inovice/Statement',
          'sub_title' => 'Assign Inovice / Statment',
          'icon' => 'svg_icons/editfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/fees_invoice_statement',
          'permission' => $this->authorization->isAuthorized('FEESV2.MASS_INVOICE_STATEMENT')
        ],
        [
          'title' => 'Inovice Statement Email Report',
          'sub_title' => 'Inovice Statement Email Report',
          'icon' => 'svg_icons/editfeeamount.svg',
          'url' => $site_url.'feesv2/fees_student/inovice_statement_email_report',
          'permission' => $this->authorization->isAuthorized('FEESV2.MASS_INVOICE_STATEMENT')
        ]
    );
    $data['school_tiles'] = checkTilePermissions($data['school_tiles']);

    $data['admin_tiles'] = array();
    if($this->authorization->isSuperAdmin()) {
        $data['admin_tiles'] = array(
            [
              'title' => 'Installment Types',
              'sub_title' => 'Installment Types',
              'icon' => 'svg_icons/installmenttypes.svg',
              'url' => $site_url.'feesv2/fees_inst_types',
              'permission' => 1
            ],
            [
              'title' => 'Fee Blueprint',
              'sub_title' => 'Fee Blueprint',
              'icon' => 'svg_icons/feeblueprint.svg',
              'url' => $site_url.'feesv2/fees_blueprint',
              'permission' => 1
            ],
            [
              'title' => 'Fee Blueprint New',
              'sub_title' => 'Fee Blueprint',
              'icon' => 'svg_icons/feeblueprint.svg',
              'url' => $site_url.'feesv2/fees_blueprint/fee_blueprints_new',
              'permission' => 1
            ],
            [
              'title' => 'Fee Structure',
              'sub_title' => 'Fee Structure',
              'icon' => 'svg_icons/feestructure.svg',
              'url' => $site_url.'feesv2/fees_cohorts',
              'permission' => 1
            ],
            [
              'title' => 'Receipt Template',
              'sub_title' => 'Receipt Template',
              'icon' => 'svg_icons/receipttemplate.svg',
              'url' => $site_url.'feesv2/fees_collection/fee_receipt_template',
              'permission' => 1
            ],
            [
              'title' => 'Accounts',
              'sub_title' => 'Accounts',
              'icon' => 'svg_icons/account.svg',
              'url' => $site_url.'Acc_controller',
              'permission' => 1
            ],
            [
              'title' => 'Receipt Book',
              'sub_title' => 'Receipt Book',
              'icon' => 'svg_icons/receiptbook.svg',
              'url' => $site_url.'feesv2/fees_blueprint/add_receipt_book',
              'permission' => 1
            ],
           
            [
              'title' => 'Import Fees',
              'sub_title' => 'Import Fees',
              'icon' => 'svg_icons/editfeeamount.svg',
              'url' => $site_url.'feesv2/fees_import',
              'permission' => 1
            ],

            [
              'title' => 'Assign Fees different Amount',
              'sub_title' => 'Assing Fees Each Student',
              'icon' => 'svg_icons/editfeeamount.svg',
              'url' => $site_url.'feesv2/fees_import/fees_assing_fee_csv',
              'permission' => 1
            ],
            [
              'title' => 'Assign Excess Amount',
              'sub_title' => 'Assing Excess Amount',
              'icon' => 'svg_icons/editfeeamount.svg',
              'url' => $site_url.'feesv2/fees_import/fees_excess_fee_csv',
              'permission' => 1
            ],
            // [
            //   'title' => 'Fee Receipts Update',
            //   'sub_title' => 'Fee Receipts Update',
            //   'icon' => 'svg_icons/editfeeamount.svg',
            //   'url' => $site_url.'feesv2/fees_import/auto_generate_fee_receipt_update',
            //   'permission' => 1
            // ],
            [
              'title' => 'Fee Audit',
              'sub_title' => 'Total Fee Amount, Order wise Receipt No',
              'icon' => 'svg_icons/feereceiptsupdate.svg',
              'url' => $site_url.'feesv2/reports_v2/fee_audit',
              'permission' => 1
            ],
            [
              'title' => 'Fee Paid Amount Edit',
              'sub_title' => 'Fee Amount Paid',
              'icon' => 'svg_icons/edit.svg',
              'url' => $site_url.'feesv2/reports_v2/fee_paid_amount_edit',
              'permission' => 1
            ],
            [
              'title' => 'Add Fee Additional Amount',
              'sub_title' => 'Only for Super Admin Use',
              'icon' => 'svg_icons/edit.svg',
              'url' => $site_url.'feesv2/fees_student/addition_insstrans_fee_assigned',
              'permission' => 1
            ],
            [
              'title' => 'Sample Format Reports',
              'sub_title' => 'datatable export',
              'icon' => 'svg_icons/edit.svg',
              'url' => $site_url.'feesv2/fees_student/sample_fee_report_format',
              'permission' => 1
            ]
        );

        $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'feesv2/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'feesv2/index_mobile';
    }else{
      $data['main_content'] = 'feesv2/index';    	
    }

    $this->load->view('inc/template', $data);
  }
  
}