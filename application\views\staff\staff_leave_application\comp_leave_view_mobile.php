<div class="col-md-12" >
    <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding d-flex justify-content-between  padding8px">
        <h3 class="card-title panel_title_new_style mb-0"><strong>Compensatory  Leaves</strong></h3>
        <ul class="panel-controls" style="float:right">
        <li><a class="new_circleShape_res" style="background-color: #fe970a;" href="<?php echo site_url('staff/leaves/add_comp_leave');?>" class="control-primary"><span class="fa fa-plus"></span></a></li>
        </ul>
    </div>
   
    <div class="card"> 
        <div class="col-md-12 padding8px">
        	<div class="leaveData">
                 <div class="form-group">
                    <select class="form-control" name="leaves_filed_type" id="leaves_filed_type" onchange="getCompLeaves()">
                        <option value="1" <?php echo $leaves_filed_type == 1 ? "Selected" : "" ?>>My Leaves</option>
                        <option value="2" <?php echo $leaves_filed_type != 1 ? "Selected" : "" ?>>All Leaves Filed By Me</option>
                    </select>
                </div>
            <?php
                if(!$comp_leave_details){
                    echo "<h4>Compensatory leave not applied.</h4>";
                }
            ?>
            <?php  $i=1;
                                         
                foreach ($comp_leave_details as  $cl) { ?>
                    <div id="<?php echo "leave_" . $i; ?>">
                    <div class="leave-card">
                    <div class="card-title"><?php echo $cl->staff_name; ?></div>
                    <div><span class="text-muted">Applied On Date: </span><?php echo date('d-m-Y', strtotime($cl->requested_on));?></div>
                    <div><span class="text-muted">Applied For Date: </span><?php echo date('d-m-Y', strtotime($cl->worked_on));?></div>
                    <div><span class="text-muted">Leave Take For: </span><?php echo $cl->no_of_days_in_words; ?></div>
                    <div><span class="text-muted">Reason: </span><?php echo $cl->comments;?></div>
                    <div><span class="text-muted">Status: </span><?php 
                        if($cl->status == '0'){
                            echo "Pending";
                        }
                        elseif($cl->status == '1'){
                            echo "<span class='text-success'>Approved</span>";
                        }
                        elseif($cl->status == '2'){
                            echo "<span class='text-danger'>Rejected</span>";
                        }
                        ?></div>
                    <div><span class="text-muted">Approved By: </span><?php echo $cl->approved_by_name ? $cl->approved_by_name : "Admin"; ?></div>
                    <div><span class="text-muted">Approval / Reject Remarks: </span><?php echo $cl->approval_comment ? $cl->approval_comment : "NA";?></div>
                    </div>
                    </div>
            <?php  }?>


        		
        	</div>
        </div>
    </div>
</div>
</div>

<a href="<?php echo site_url('staff/leaves/dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<script type="text/javascript">
    function getCompLeaves(){
        let leaves_filed_type=$("#leaves_filed_type").val();
        if(leaves_filed_type==""){
            // setting default to as "All my leaves"
            leaves_filed_type=1;
        }

        let URL="<?php echo site_url('staff/Leaves/view_comp_leave') ?>";
        window.location.href = `${URL}/${leaves_filed_type}`;
    }
</script>

<!-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
var approveLeave = 0;
var staffId = 0;
$(document).ready(function(){
    approveLeave = <?php //echo $applyLeave; ?>;
    staffId = <?php //echo $staff; ?>;
    var val = 1;
    if(approveLeave) {
        getLeaves();
    }
    getLeaves(val);
    $("#leaves").on('change', callGetLeaves);
});

function callGetLeaves() {
    var val = $("#leaves").val();
    getLeaves(val);
}

function getLeaves() {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/staffLeaves'); ?>',
        data: {'val': val},
        type: "post",
        success: function (data) {
            var leaves = JSON.parse(data);
            if(leaves.length == 0) {
		        $(".leaveData").html('<h4>Great! Nobody applied leave.</h4>');
		        return false;
		    }

		    var html = '';
		    for(var i=0; i<leaves.length; i++) {
		    	html += '<div id="leave_'+leaves[i].id+'">';
		    	html += constructLeave(leaves[i]);
		    	html += '</div>';
		    }
		    $(".leaveData").html(html);
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function constructLeave(leave) {
    var status_array = {
        '0' : '<span>Pending</span>',
        '1' : '<span class="text-success">Approved</span>',
        '2' : '<span class="text-success">Approved</span>',
        '3' : '<span class="text-danger">Rejected</span>',
        '4' : '<span class="text-warning">Cancelled</span>',
    }
    var html = '<div class="leave-card">';
    html += '<div class="card-title">'+leave.staff_name+' <span style="float: right;">'+leave.short_name+'</span></div>';
    html += '<div>'+leave.from_date+' to '+leave.to_date+' <span style="float: right;">'+parseFloat(leave.noofdays)+' day(s)</span></div>';
    html += '<div class="text-muted"><span style="font-weight: bold;">Reason:</span> '+leave.reason+'</div>';
    html += '<div class="text-muted"><span style="font-weight: bold;">Status:</span> '+status_array[leave.status]+'</div>';
    if(leave.status == '3' && leave.description != '') {
        html += '<div class="text-muted"><span style="font-weight: bold;">Comment:</span> '+leave.description+'</div>';
    }
    if(leave.status == '4' && leave.cancel_reason != '') {
        html += '<div class="text-muted"><span style="font-weight: bold;">Comment:</span> '+leave.cancel_reason+'</div>';
    }
    html += '<div class="mt-2">';
    if(staffId == leave.staff_id) {
        if((leave.date_passed == 0 && leave.status != '4') || (leave.date_passed == 1 && leave.status == '0' || leave.status == '3')) {
            html += '<button class="btn btn-sm btn-warning btn-block" onclick="cancelLeave('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+')">Cancel Leave</button>';
        }
    }/* else {
        if (leave.status == '0' && approveLeave) {
            html += '<button class="btn btn-sm btn-primary btn-block" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+', 1)">Approve/Reject</button>';
        } else if (leave.status == '3') {
            html += '<button class="btn btn-sm btn-secondary btn-block" style="border-radius: 8px;" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+', 1)">Approve</button>';
        } else if(leave.status != '4'){
            html += '<button class="btn btn-sm btn-secondary btn-block" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\','+leave.staff_id+', 0)">Reject</button>';
        }
    }*/
    html += '</div>';
    html += '</div>';
    return html;
}

function cancelLeave(leave_id, staff_name, staff_id, status) {
    var html = '<textarea placeholder="Enter reason here..." class="form-control" id="cancel_reason" rows="5"></textarea>';
    Swal.fire({
      title: staff_name,
      html: html,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      preConfirm: function() {
        // var reason = $("#cancel_reason").val().trim();
        // if(reason == '') {
        //   Swal.showValidationMessage('Enter the reason');
        // }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        var reason = $("#cancel_reason").val().trim();
        var input = {
          'leave_id' : leave_id,
          'reason' : reason
        };
        $.ajax({
            url: '<?php echo site_url('staff/leaves/cancelLeave'); ?>',
            type: 'post',
            data: input,
            success: function(data) {
                var status = parseInt(data);
                if(status == 0) {
                    Swal.fire({
                        title: "Error",
                        text: "Failed to cancel leave",
                        icon: "error",
                    });
                } else {
                    Swal.fire({
                        title: "Success",
                        text: "Leave cancelled successfully",
                        icon: "success",
                    });
                    getLeaves();
                }
            }
        });
      }
    });
}

function getStatus(status_val) {
    var status = "Pending";
    if (status_val == '1') {
        status = "<span class='text-success'>Approved</span>";
    } else if (status_val == '2') {
        status = "<span class='text-success'>Auto Approved</span>";
    } else if (status_val == '3') {
        status = "<span class='text-danger'>Rejected</span>";
    }
    return status;
}

function updateLeaveStatus(leave_id, staff_name, staff_id, status) {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/staffLeave'); ?>',
        data: {'leave_id': leave_id},
        type: "post",
        success: function (data) {
            var leave = JSON.parse(data);
            leave.staff_name = staff_name;
            var html = '<table class="table table-bordered" style="text-align:left;">';
            html += '<tr><th style="width: 20%;">Staff</th><td>'+staff_name+'</td><tr>';
            html += '<tr><th>Leave type</th><td>'+leave.leave_type+'</td><tr>';
            html += '<tr><th>From date</th><td>'+leave.from_date+'</td><tr>';
            html += '<tr><th>To date</th><td>'+leave.to_date+'</td><tr>';
            html += '<tr><th>No. of days</th><td>'+leave.noofdays+'</td><tr>';
            html += '<tr><th>Reason</th><td>'+leave.reason+'</td><tr>';
            html += '<tr><th>Status</th><td>';
            html += '<label class="radio-inline"><input value="1" type="radio" '+(status==1?'checked':'')+' name="status">Approve</label>';
            html += '<label class="radio-inline"><input value="3" type="radio" '+(status==0?'checked':'')+' name="status">Reject</label>';
            html += '</td><tr>';
            html += '<tr><th>Remarks</th><td><textarea id="description" class="form-control" id="description"></textarea></td><tr>';
            html += '<tr><td colspan="2" onclick="viewHistory('+staff_id+', '+leave_id+', \''+staff_name+'\')" style="text-align: right;"><button class="btn btn-primary btn-sm">View History</button></td></tr>';
            html += '</table>';
            Swal.fire({
                title: 'Update Leave',
                html: html,
                confirmButtonText: 'Save',
                showCancelButton: true,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    var status = $("input[name='status']:checked").val();
                    var description = $("#description").val();
                    var data = {
                        'id' : leave_id,
                        'status' : status,
                        'description' : description
                    };
                    saveLeaveStatus(data, leave);
                }
            });
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function saveLeaveStatus(input, leave) {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/saveLeaveStatus'); ?>',
        data: input,
        type: "post",
        success: function (data) {
            if(parseInt(data)){
                Swal.fire({
                  title: "Successful",
                  text: "Updated status successfully",
                  icon: "success",
                });
                leave.status = input.status;
                leave.description = input.description;
            } else{
                Swal.fire({
                  title: "Error",
                  text: "Failed to update status",
                  icon: "error",
                });
            }
            var html = constructLeave(leave);
            $("#leave_"+input.id).html(html);
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function viewHistory(staff_id, leave_id, staff_name) {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/getLeavesHistory'); ?>',
        data: {'staff_id': staff_id},
        type: "post",
        success: function (data) {
            var leaves = JSON.parse(data);
            var html = constructDetails(leaves, staff_id, leave_id);
            Swal.fire({
                title: staff_name,
                html: html,
                allowOutsideClick: false,
                confirmButtonText: 'Okay',
                showConfirmButton: true,
            }).then((result) => {
                updateLeaveStatus(leave_id, staff_name, staff_id);
            });
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function constructDetails(leaves, staff_id, leave_id) {
    var html = '<div class="table-responsive">';
    html += '<table class="table table-bordered">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';
    html += '<th>From</th>';
    html += '<th>To</th>';
    html += '<th>Type</th>';
    html += '<th>Days</th>';
    html += '<th>Status</th>';
    html += '<th>Applied By</th>';
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';
    var j = 1;
    for(var i in leaves) {
        if(leave_id == leaves[i].leave_id) continue;
        var status = (leaves[i].status == 0)?'Pending':((leaves[i].status == 3)?'<span class="text-danger">Rejected</span>':'<span class="text-success">Approved</span>');
        var filed_by = (leaves[i].leave_filed_by == 0)?'Admin':leaves[i].staff_name;
        html += '<tr>';
        html += '<td>'+(j++)+'</td>';
        html += '<td>'+leaves[i].from_date+'</td>';
        html += '<td>'+leaves[i].to_date+'</td>';
        html += '<td>'+leaves[i].short_name+'</td>';
        html += '<td>'+parseFloat(leaves[i].noofdays)+'</td>';
        html += '<td>'+status+'</td>';
        html += '<td>'+filed_by+'</td>';
        html += '</tr>';
    }
    html += '</tbody>';
    html += '</table>';
    html += '</div>';
    return html;
}
</script> -->

<style type="text/css">
.swal2-popup {
	padding: 1.25rem 0 !important;
}
.leave-card {
    border-radius: 1rem;
    /* box-shadow: 0px 0px 5px #ccc; */
    margin-bottom: 1rem;
    padding: 10px 15px;
    font-size: 16px;
    border: solid 1.5px #bebebe;
}
.leave-footer {
	margin-top: 1rem;
}
.leave-card .card-title {
	font-weight: 600;
	margin-bottom: 0.25rem;
}
.btn{
    margin-right: 14px;
    padding: 4px 20px;
    font-size: 16px;    
}
.card{
    border:none;
}
.card-header{
    background-color: white;
}
.row{
    margin: 0px;
}
.box_trail_new{
    height: auto;
    width: 100%;
    border: solid 2px #eee;
    padding: 15px 8px;
    border-radius: 8px;
    box-shadow: 0px 2px 3px #eee;
    margin-bottom: 12px;
    font-size: 16px;

}
p{
    margin-bottom: .5rem;
}
.form-control{
    font-size: 1rem;
}

.btn_align{
    margin-bottom: 4px;
    width: 32px;
}
  ul.panel-controls>li>a {
    border-radius: 50%;
}
.card{
    margin-bottom: 1rem;
    border-radius: 0.75rem;
}
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
</style>