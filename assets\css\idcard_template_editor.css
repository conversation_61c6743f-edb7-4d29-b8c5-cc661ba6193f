/* Card Preview Container */
.card-preview {
    position: relative;
    width: 100%;
    height: 300px;
    border: 1px solid #ddd;
    background: #fff;
    overflow: hidden;
}

.card-content {
    position: relative;
    width: 100%;
    height: 100%;
    transform-origin: top left;
}

/* Grid Styles */
.card-content.grid-none {
    background: none;
}

.card-content.grid-basic {
    background-image: 
        linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 25% 25%, 25% 25%;
}

.card-content.grid-1x1 {
    background-image: 
        linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 1mm 1mm, 1mm 1mm;
}

.card-content.grid-2x2 {
    background-image: 
        linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 2mm 2mm, 2mm 2mm;
}

.card-content.grid-5x5 {
    background-image: 
        linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 5mm 5mm, 5mm 5mm;
}

/* Element Styles */
.element {
    position: absolute;
    cursor: move;
    user-select: none;
    border: 1px solid transparent;
    transition: border-color 0.2s;
}

.element:hover {
    border-color: #007bff;
}

.element.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.element-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* Shape Styles */
.shape-rectangle {
    background: #e9ecef;
    border: 1px solid #dee2e6;
}

.shape-circle {
    background: #e9ecef;
    border: 1px solid #dee2e6;
    border-radius: 50%;
}

.shape-triangle {
    width: 0;
    height: 0;
    border-left: 50% solid transparent;
    border-right: 50% solid transparent;
    border-bottom: 100% solid #e9ecef;
}

/* Position and Size Indicators */
.position-indicator,
.size-indicator {
    position: fixed;
    bottom: 10px;
    padding: 5px 10px;
    background: rgba(0,0,0,0.8);
    color: #fff;
    border-radius: 3px;
    font-size: 12px;
    z-index: 1000;
}

.position-indicator {
    left: 10px;
}

.size-indicator {
    left: 150px;
}

/* Selection Mode Indicator */
.selection-mode-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    background: rgba(0,0,0,0.8);
    color: #fff;
    border-radius: 3px;
    font-size: 12px;
    z-index: 1000;
    display: none;
}

/* Alignment Toolbar */
.alignment-toolbar {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.alignment-toolbar .btn-group {
    margin-bottom: 5px;
}

/* Element Properties Modal */
.modal-body {
    padding: 20px;
}

.properties-section {
    margin-bottom: 20px;
}

.properties-section h6 {
    margin-bottom: 10px;
    color: #495057;
}

/* Preview Button */
#previewTemplateBtn {
    margin-right: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-preview {
        height: 200px;
    }
    
    .position-indicator,
    .size-indicator {
        display: none;
    }
} 