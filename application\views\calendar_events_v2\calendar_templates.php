<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">


<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('calendar_events_v2/Calendar_events_v2') ?>">Calendar V2</a></li>
    <li>Manage Calendar Templates</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border" style="border: none;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-4">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('calendar_events_v2/Calendar_events_v2') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Calendar Templates
                    </h3>
                </div>
                <div class="col-md-8">
                    <a data-toggle="modal" data-original-title="Add Event" data-target="#addCalendartemplateModal" class="new_circleShape_res pull-right" style="background-color: #fe970a;">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <div id="event_types_data">
                <div class="col-md-12 text-center no-data-display" id="calendar_data">
                    <i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i> Loading...
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addCalendartemplateModal" tabindex="-1" role="dialog" aria-labelledby="addEventModalLabel" aria-modal="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="addEventModalLabel">Create Calendar Template</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addEventForm">
                    <!-- Calendar Template Name -->
                    <div class="form-group row">
                        <label for="Calendar_name" class="col-sm-4 col-form-label text-right">Template Name<font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" name="Calendar_name" id="Calendar_name"
                                placeholder="Enter Calendar Template Name">
                        </div>
                    </div>

                    <!-- Academic Year Selection -->
                    <div class="form-group row">
                        <label for="academic_year" class="col-sm-4 col-form-label text-right">Academic Year<font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <select class="form-control" name="academic_year" id="add_academic_year">
                                <option value="" selected disabled>Select Academic Year</option>
                                <?php foreach ($acad_years as $year): ?>
                                <option value="<?= $year->acad_year_id; ?>"><?= $year->acad_year_name; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Target Group Selection (Students or Staff) -->
                    <div class="form-group row">
                        <label for="target_group" class="col-sm-4 col-form-label text-right">Select Group <font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <select class="form-control" name="target_group" id="target_group"
                                onchange="toggleSessionsBlock()">
                                <option value="staff">Staff</option>
                                <option value="students">Students</option>
                            </select>
                        </div>
                    </div>

                    <!-- Add Start Date -->
                    <div class="form-group row">
                        <label for="start_date" class="col-sm-4 col-form-label text-right">Start Date <font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="text" class="form-control datePicker" id="start_date"
                                    name="start_date" placeholder="Select Date" required=""
                                    value="<?php echo date('d-m-Y'); ?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add End Date -->
                    <div class="form-group row">
                        <label for="end_date" class="col-sm-4 col-form-label text-right">End Date <font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="text" class="form-control datePicker" id="end_date" name="end_date"
                                    placeholder="Select Date" required="" value="<?php echo date('d-m-Y'); ?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="sessions_block" class="form-group" style="display: none;">
                        <label class="font-weight-bold">Sessions Per Day</label>
                        <div class="table-responsive">
                            <table class="table table-bordered text-center">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Day</th>
                                        <th>Default Sessions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Monday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Monday]" value="2" min="0" max="2">
                                            <span class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Tuesday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Tuesday]" value="2" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                    <tr>
                                        <td>Wednesday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Wednesday]" value="2" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                    <tr>
                                        <td>Thursday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Thursday]" value="2" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                    <tr>
                                        <td>Friday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Friday]" value="2" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                    <tr>
                                        <td>Saturday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Saturday]" value="1" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                    <tr>
                                        <td>Sunday</td>
                                        <td><input type="number" class="form-control text-center"
                                                name="sessions[Sunday]" value="0" min="0" max="2"><span
                                                class="text-muted" style="font-size: 10px;">Max 2 sessions can
                                                be added</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <input type="hidden" id="save_calendar_url" value="">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" id="modal_save" onclick="addCalendar()">Create</button>
            </div>
        </div>
    </div>
</div>




<div class="modal fade" id="editCalendartemplateModal" tabindex="-1" role="dialog" aria-labelledby="editEventModalLabel" aria-modal="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 1% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="editEventModalLabel">Update Calendar Template</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCalendarForm">
                    <input type="hidden" class="form-control" name="calendar_id" id="calendar_id" value="">

                    <!-- Calendar Template Name -->
                    <div class="form-group row">
                        <label for="update_calendar_name" class="col-sm-4 col-form-label text-right">Template
                            Name<font color="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" name="calendar_name"
                                id="update_calendar_name">
                        </div>
                    </div>

                    <!-- Academic Year Selection -->
                    <div class="form-group row">
                        <label for="update_academic_year" class="col-sm-4 col-form-label text-right">Academic
                            Year<font color="red"> *</font></label>
                        <div class="col-sm-8">
                            <select class="form-control" name="update_academic_year" id="update_academic_year">
                                <?php foreach ($acad_years as $year): ?>
                                <option value="<?= $year->acad_year_id; ?>">
                                    <?= $year->acad_year_name; ?>
                                </option>
                                <?php endforeach; ?>

                            </select>
                        </div>
                    </div>

                    <!-- Target Group Selection -->
                    <div class="form-group row">
                        <label for="update_target_group" class="col-sm-4 col-form-label text-right">Select Group
                            <font color="red"> *</font></label>
                        <div class="col-sm-8">
                            <select class="form-control" name="target_group" readonly id="update_target_group"
                                onchange="toggleUpdateSessionsBlock()">
                                <option value="staff">Staff</option>
                                <option value="students">Students</option>
                            </select>
                        </div>
                    </div>

                    <!-- Add Start Date -->
                    <div class="form-group row">
                        <label for="update_start_date" class="col-sm-4 col-form-label text-right">Start Date
                            <font color="red"> *</font></label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="text" class="form-control datePicker" id="update_start_date"
                                    name="start_date" placeholder="Select Date" required=""
                                    value="<?php echo date('d-m-Y'); ?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="update_end_date" class="col-sm-4 col-form-label text-right">End Date<font
                                color="red"> *</font></label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="text" class="form-control datePicker" id="update_end_date"
                                    name="end_date" placeholder="Select Date" required=""
                                    value="<?php echo date('d-m-Y'); ?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Block -->
                    <div id="update_sessions_block" class="form-group" style="display: none;">
                        <label class="font-weight-bold">Sessions Per Day <font color="red"> *</font></label>
                        <div class="table-responsive">
                            <table class="table table-bordered text-center">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Day</th>
                                        <th>Sessions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                            $days = array('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday');
                            foreach ($days as $day):
                            ?>
                                    <tr>
                                        <td><?php echo $day; ?></td>
                                        <td>
                                            <input type="number" class="form-control text-center"
                                                name="update_sessions[<?php echo $day; ?>]"
                                                id="update_session_<?php echo strtolower($day); ?>" min="0"
                                                max="2">
                                            <span class="text-muted" style="font-size: 10px;color: #f00;">
                                                Max 2 sessions can be added
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>

                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" id="update_template" onclick="UpdateCalendar()">Update Calendar</button>
            </div>
        </div>
    </div>
</div>

<!-- Map Template Modal -->
<div class="modal fade" id="mapTemplateModal" tabindex="-1" role="dialog" aria-labelledby="mapTemplateModalLabel" aria-modal="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 1% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="mapTemplateModalLabel">Map Calendar Template: <span id="calendar_template_name"></span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="map_calendar_id" value="">
                <div id="mapping_content">
                    <div class="col-md-12 text-center no-data-display">
                        <i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i> Loading...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.disabled-btn {
    opacity: 0.6;
    cursor: not-allowed !important;
    pointer-events: none;
}

.action-btn {
    margin: 2px;
}

.lock-btn {
    background-color: #6c757d;
    color: white;
}

.lock-btn:hover {
    background-color: #5a6268;
    color: white;
}

[data-original-title] {
    position: relative;
}

[data-original-title]:hover:after {
    content: attr(data-original-title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 12px;
    z-index: 1000;
}
</style>

<script>
$(document).ready(function() {
    $('#start_date,#end_date,#update_start_date,#update_end_date').datepicker({
        format: 'dd-mm-yyyy',
        todayHighlight: true,
        "autoclose": true
    });

    $(document).on('mouseenter', '[data-original-title]', function() {
        $(this).tooltip({
            container: 'body',
            placement: 'top',
            trigger: 'hover'
        }).tooltip('show');
    });

    geteventsType();


});


function addCalendar() {
    $('#modal_save').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating...');
    var academicYearSelect = $('#add_academic_year').val();
    var formData = {
        calendar_name: document.getElementById("Calendar_name").value,
        academic_year: academicYearSelect, // use the selected value
        target_group: document.getElementById("target_group").value,
        start_date: document.getElementById("start_date").value,
        end_date: document.getElementById("end_date").value,
        sessions: {}
    };

    if (formData.target_group == "students") {
        var sessions = {};
        document.querySelectorAll("#sessions_block input[name^='sessions']").forEach(function(input) {
            var day = input.name.replace('sessions[', '').replace(']', '');
            sessions[day] = input.value;
        });
        formData.sessions = sessions;
    }

    if (!formData.calendar_name || !formData.academic_year || !formData.target_group) {
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Please fill in all required details.',
            showConfirmButton: true
        });
        $('#modal_save').prop('disabled', false).html('Create');
        return false;
    }

    $.ajax({
        type: "POST",
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/save_calendar_name'); ?>',
        data: formData,
        dataType: "json",
        success: function(response) {
            $('#modal_save').prop('disabled', false).html('Create');
            if (response.status == "success") {
                Swal.fire({
                    icon: "success",
                    title: "Success!",
                    text: "Calendar created successfully.",
                    showConfirmButton: true
                }).then(() => {
                    $('#addEventForm')[0].reset();
                    $('#sessions_block').hide(); // Hide sessions block if visible
                    $('#addCalendartemplateModal').modal('hide');
                    geteventsType();
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Error!",
                    text: response.message || "Failed to create calendar.",
                    showConfirmButton: true
                });
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", error);
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: "Something went wrong. Please try again.",
                showConfirmButton: true
            });
            $('#modal_save').prop('disabled', false).html('Create');
        }
    });
}

function toggleSessionsBlock() {
    var targetGroup = document.getElementById("target_group").value;
    var sessionsBlock = document.getElementById("sessions_block");
    sessionsBlock.style.display = targetGroup === "students" ? "block" : "none";
}

function geteventsType() {
    const isSuperAdmin = <?= $this->authorization->isSuperAdmin() ? 'true' : 'false' ?>;
    $.ajax({
        type: "POST",
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_events_type'); ?>',
        success: function(data) {
            try {

                if (typeof data === 'string') {
                    data = JSON.parse(data);
                }

                var html = '';
if (!Array.isArray(data) || data.length == 0) {
    html +='<div class="no-data-display">No Calendar Templates Added</div>';
} else {
    html +=
        '<div class="panel-body"><table class="table table-bordered" style="table-layout: fixed; width: 100%;">';
    html +=
                        // Added Status column header
        '<thead><tr><th style="width: 2%;">#</th><th style="width: 3%;">Actions</th><th style="width: 13%;">Template Name</th><th style="width: 25%;">Mapped To</th><th style="width: 5%;">Status</th></tr></thead>';
    html += '<tbody>';
    for (var s = 0; s < data.length; s++) {
        const isLocked = data[s].status == 1;
        const disabledClass = isLocked ? 'disabled-btn' : '';
        const disabledAttr = isLocked ? 'disabled="disabled"' : '';
        const tooltipAttr = isLocked ? 'data-original-title="This template is locked"' : '';
        const dataToggle = isLocked ? '' : 'modal';

        html += '<tr data-locked="' + (isLocked ? '1' : '0') + '">';
        html += '<td>' + (s + 1) + '</td>';
        html += `<td>
            <div class="dropdown action-dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton${s}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton${s}">
                    <a class="dropdown-item ${disabledClass}" data-id="${data[s].id}" href="<?php echo site_url("calendar_events_v2/Calendar_events_v2/add_events/"); ?>${data[s].id}" ${disabledAttr} ${tooltipAttr} ${isLocked ? 'onclick="return false"' : ''}>
                        <i class="fa fa-calendar-plus text-info"></i> Add Schedule
                    </a>
                    <a style ="cursor:pointer" class="dropdown-item ${disabledClass}" data-id="${data[s].id}" data-toggle="${dataToggle}" data-target="#editCalendartemplateModal" ${disabledAttr} ${tooltipAttr} onclick="return ${isLocked ? 'false' : `handleButtonClick(this, 'Loading...', function() { get_calendar_details(${data[s].id}); })`}">
                        <i class="fa fa-pencil text-primary"></i> Edit Template
                    </a>
                    <a style ="cursor:pointer" class="dropdown-item ${disabledClass}" ${disabledAttr} ${tooltipAttr} onclick="${isLocked ? 'return false' : `openMapTemplateModal(${data[s].id})`}">
                        <i class="fa fa-map text-secondary"></i> Map Template
                    </a>
                    <a style ="cursor:pointer" class="dropdown-item lock-btn" onclick="locktemplate(${data[s].id})">
                        <i class="fa fa-${isLocked ? 'unlock' : 'lock'} text-warning"></i> ${isLocked ? 'Unlock' : 'Lock'} Template
                    </a>
                    <a style ="cursor:pointer" class="dropdown-item ${disabledClass}" id="cloneTemplate${data[s].id}" ${disabledAttr} ${tooltipAttr} onclick="${isLocked ? 'return false' : `clonetemplate(${data[s].id}, '${data[s].calendar_name}')`}">
                        <i class="fa fa-clone text-success"></i> Clone Template
                    </a>`;

        if (isSuperAdmin) {
            html += `
                <a style ="cursor:pointer" class="dropdown-item ${disabledClass}" data-id="${data[s].id}" ${disabledAttr} ${tooltipAttr}
                    onclick="return ${isLocked ? 'false' : `handleButtonClick(this, 'Deleting...', function() { deleteCalenderTemplate(${data[s].id}); })`}">
                    <i class="fa fa-trash text-danger"></i> Delete Template
                </a>`;
        }
        html += `</div></div></td>`;
        
        html += '<td>' + data[s].calendar_name + '</td>';
        html += '<td id="mapped_info_' + data[s].id +
        '" style="word-wrap: break-word; white-space: pre-wrap;"><i class="fa fa-spinner fa-spin"></i> Loading...</td>';
        html += `<td><span class="badge badge-${isLocked ? 'danger' : 'success'}">${isLocked ? 'Locked' : 'Unlocked'}</span></td>`;
        html += '</tr>';

    }
    html += '</tbody></table></div>';
}
$('#event_types_data').html(html);

if (Array.isArray(data) && data.length > 0) {
    data.forEach(function(calendar) {
        fetchMappingInfo(calendar.id);
    });
}
} catch (e) {
    console.error("JSON parsing error:", e);
    $('#event_types_data').html('<div class="alert alert-danger">Error loading calendar data. Please try refreshing the page.</div>');
}
},
error: function(xhr, status, error) {
    console.error("AJAX Error:", error);
    $('#event_types_data').html('<div class="alert alert-danger">Error loading calendar data. Please try refreshing the page.</div>');
}
});
}

function clonetemplate(id, calendarName) {
    Swal.fire({
        title: 'Clone Calendar Template',
        html: `Are you sure you want to clone the calendar template<br><b>"${calendarName}"</b>?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, clone it!',
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            const $cloneBtn = $(`#cloneTemplate${id}`);
            const originalHtml = $cloneBtn.html();
            $cloneBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

            $.ajax({
                url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/clone_calendar_template'); ?>',
                type: 'POST',
                data: {
                    calendar_id: id
                },
                success: function(response) {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        response = response;
                    }

                    if (response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message,
                            showConfirmButton: true
                        }).then(() => {
                            geteventsType(); // Refresh the calendar list
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response.message || 'Failed to clone calendar template',
                            showConfirmButton: true
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'An error occurred while cloning the calendar template',
                        showConfirmButton: true
                    });
                },
                complete: function() {
                    $cloneBtn.prop('disabled', false).html(originalHtml);
                }
            });
        }
    });
}

function fetchMappingInfo(calendarId) {
    $.ajax({
        type: "POST",
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_mapping_info'); ?>',
        data: {
            calendar_id: calendarId
        },
        success: function(response) {
            const staffType = <?php echo json_encode($this->settings->getSetting('staff_type')) ?>;
            try {
                if (typeof response === 'string') {
                    response = JSON.parse(response);
                }
                let mappingHtml = '';

                if (response.sections && response.sections.length > 0) {
                    mappingHtml += '<strong>Classes:</strong> ' + response.sections.join(', ') +
                        '<br>';
                }

                if (response.staff_types && response.staff_types.length > 0) {
                    let mappedStaff = response.staff_types.map(function(val) {
                        return staffType[val] || val;
                    });
                    mappingHtml += '<strong>Staff Types:</strong> ' + mappedStaff.join(', ');
                }

                if (!mappingHtml) {
                    mappingHtml = '<span class="text-muted">Not mapped</span>';
                }

                $('#mapped_info_' + calendarId).html(mappingHtml);
            } catch (e) {
                console.error("JSON parsing error:", e);
                $('#mapped_info_' + calendarId).html(
                    '<span class="text-danger">Error loading mapping info</span>');
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", error);
            $('#mapped_info_' + calendarId).html(
                '<span class="text-danger">Error loading mapping info</span>');
        }
    });
}

function handleButtonClick(button, loadingText, callback) {
    const originalText = button.innerHTML;
    const originalDisabled = button.disabled;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + loadingText;

    if (callback) {
        callback();

        setTimeout(() => {
            button.disabled = originalDisabled;
            button.innerHTML = originalText;
        }, 500);

        return false;
    } else if (button.tagName.toLowerCase() === 'a') {
        setTimeout(() => {
            button.disabled = originalDisabled;
            button.innerHTML = originalText;
        }, 30000);
        return true;
    }

    return false;
}

function deleteCalenderTemplate(id) {
    Swal.fire({
        title: 'Are you sure?',
        text: "You want to delete this calendar template!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/deleteCalandarTemplate'); ?>',
                type: 'POST',
                data: {
                    id: id
                },
                success: function(response) {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: "Invalid response from the server.",
                            showConfirmButton: true
                        });
                        return;
                    }

                    if (response.status === 'success') {
                        Swal.fire({
                            icon: "success",
                            title: "Success!",
                            text: response.message ||
                                "Calendar template deleted successfully.",
                            showConfirmButton: true
                        }).then(() => {
                            geteventsType();
                        });
                    } else if (response.is_mapped) {
                        Swal.fire({
                            icon: "warning",
                            title: "Cannot Delete",
                            html: `${response.message}`,
                            confirmButtonColor: '#3085d6',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: response.message ||
                                "Something went wrong. Please try again.",
                            showConfirmButton: true
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: "error",
                        title: "Error!",
                        text: "Something went wrong. Please try again.",
                        showConfirmButton: true
                    });
                }
            });
        }
    });
}




function get_calendar_details(id) {
    $.ajax({
        type: "POST",
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_calendar_detail'); ?>',
        data: {
            calendar_id: id
        },
        dataType: 'json',
        success: function(response) {
            try {
                if (typeof response === 'string') {
                    response = JSON.parse(response);
                }

                if (response.status === "success") {
                    let data = response.data;

                    $("#calendar_id").val(data.id);
                    $("#update_calendar_name").val(data.calendar_name);
                    $("#update_academic_year").val(data.academic_year);
                    $("#update_target_group").val(data.target_group);
                    $("#update_start_date").val(data.start_date);
                    $("#update_end_date").val(data.end_date);

                    if (data.sessions) {
                        $.each(data.sessions, function(day, sessionCount) {
                            $("#update_session_" + day.toLowerCase()).val(sessionCount);
                        });
                    }

                    $("#editCalendartemplateModal").modal("show");
                    toggleUpdateSessionsBlock();
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error!",
                        text: response.message || "Failed to retrieve calendar details.",
                        showConfirmButton: true
                    });
                }
            } catch (error) {
                console.error("Error parsing response:", error);
                Swal.fire({
                    icon: "error",
                    title: "Error!",
                    text: "Invalid response from server.",
                    showConfirmButton: true
                });
            }
        },
        error: function(xhr, status, error) {
            console.error("AJAX Error:", error);
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: "Failed to retrieve calendar details. Please try again.",
                showConfirmButton: true
            });
        }
    });
}


function UpdateCalendar() {
    $('#update_template').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
    let calendar_id = $("#calendar_id").val();
    let calendar_name = $("#update_calendar_name").val();
    let academic_year = $("#update_academic_year").val();
    let target_group = $("#update_target_group").val();
    let start_date = $("#update_start_date").val();
    let end_date = $("#update_end_date").val();

    let formData = {
        calendar_id: calendar_id,
        calendar_name: calendar_name,
        academic_year: academic_year,
        target_group: target_group,
        start_date: start_date,
        end_date: end_date,
        sessions: {}
    };

    if (target_group === "students") {
        let sessions = {};
        $("input[id^='update_session_']").each(function() {
            let id = $(this).attr("id");
            if (id) {
                let day = id.replace("update_session_", "");
                day = day.charAt(0).toUpperCase() + day.slice(1);
                sessions[day] = $(this).val() || "0";
            }
        });
        formData.sessions = sessions;
    }


    $.ajax({
        type: "POST",
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/update_calendar'); ?>',
        data: formData,
        success: function(response) {
            $('#update_template').prop('disabled', false).html('Update Calendar');
            response = JSON.parse(response);

            if (response.status === "success") {
                Swal.fire({
                    title: "Success!",
                    text: "Calendar updated successfully.",
                    icon: "success",
                    confirmButtonText: "OK"
                }).then(() => {
                    $("#editCalendartemplateModal").modal("hide");
                    geteventsType();
                });
            } else {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to update calendar. Please try again.",
                    icon: "error",
                    confirmButtonText: "OK"
                });
            }
        },
        error: function() {
            Swal.fire({
                title: "Error!",
                text: "An error occurred while updating the calendar.",
                icon: "error",
                confirmButtonText: "OK"
            });
        }
    });
}


$('#editCalendartemplateModal').on('shown.bs.modal', function() {
    toggleUpdateSessionsBlock();
});

$('#update_target_group').change(function() {
    toggleUpdateSessionsBlock();
});


function toggleUpdateSessionsBlock() {
    var targetGroup = $('#update_target_group').val();
    $('#update_sessions_block').toggle(targetGroup === 'students');
}

function openMapTemplateModal(calendarId) {
    $('#map_calendar_id').val(calendarId);
    $('#mapTemplateModal').modal('show');
    loadMappingContent(calendarId);

    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_calendar_detail'); ?>',
        type: 'POST',
        data: {
            calendar_id: calendarId
        },
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                $('#calendar_template_name').text(response.data.calendar_name);
            }
        }
    });
}

function locktemplate(calendarId) {
    const $lockBtn = $(event.currentTarget);
    const $row = $lockBtn.closest('tr');
    const isLocked = $row.attr('data-locked') === '1';
    const actionText = isLocked ? 'unlock' : 'lock';
    const confirmTitle = isLocked ? 'Unlock Calendar Template' : 'Lock Calendar Template';
    const confirmText = isLocked
        ? 'Are you sure you want to unlock this calendar template?'
        : 'Are you sure you want to lock this calendar template?';

    Swal.fire({
        title: confirmTitle,
        text: confirmText,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: `Yes, ${actionText} it!`,
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            $lockBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

            $.ajax({
                url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/lock_calendar_template'); ?>',
                type: 'POST',
                data: {
                    calendar_id: calendarId
                },
                success: function(response) {
                    response = JSON.parse(response);
                    if (response.status === 'success') {
                        const isLockedNow = response.lock_status === 1;
                        $lockBtn.html(`<i class="fa fa-${isLockedNow ? 'unlock' : 'lock'}"></i>`);
                        $lockBtn.attr('title', isLockedNow ? 'Unlock template' : 'Lock template');

                        // Update the correct column for status (last column)
                        $row.find('td').eq(4).html(
                            `<span class="badge badge-${isLockedNow ? 'danger' : 'success'}">${isLockedNow ? 'Locked' : 'Unlocked'}</span>`
                        );

                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message,
                            showConfirmButton: true,
                            timer: 2000
                        });

                        $row.attr('data-locked', isLockedNow ? '1' : '0');

                        const $actionButtons = $row.find('.action-btn');
                        const calendarName = $row.find('td:eq(1)').text().trim();

                        $actionButtons.each(function() {
                            const $btn = $(this);
                            const isLockBtn = $btn.hasClass('lock-btn');

                            if (!isLockBtn) {
                                if (isLockedNow) {
                                    $btn.prop('disabled', true)
                                        .addClass('disabled-btn')
                                        .attr('data-original-title', 'This template is locked');

                                    if ($btn.find('.fa-calendar-plus').length) {
                                        $btn.attr('onclick', 'return false');
                                    } else if ($btn.find('.fa-pencil').length) {
                                        $btn.attr('data-toggle', '')
                                            .attr('onclick', 'return false');
                                    } else if ($btn.find('.fa-map').length) {
                                        $btn.attr('onclick', 'return false');
                                    } else if ($btn.find('.fa-clone').length) {
                                        $btn.attr('onclick', 'return false');
                                    } else if ($btn.find('.fa-trash').length) {
                                        $btn.attr('onclick', 'return false');
                                    }
                                } else {
                                    $btn.prop('disabled', false)
                                        .removeClass('disabled-btn')
                                        .removeAttr('data-original-title');

                                    if ($btn.find('.fa-calendar-plus').length) {
                                        $btn.removeAttr('onclick');
                                    } else if ($btn.find('.fa-pencil').length) {
                                        $btn.attr('data-toggle', 'modal')
                                            .attr('onclick', `return handleButtonClick(this, 'Loading...', function() { get_calendar_details(${calendarId}); })`);
                                    } else if ($btn.find('.fa-map').length) {
                                        $btn.attr('onclick', `openMapTemplateModal(${calendarId})`);
                                    } else if ($btn.find('.fa-clone').length) {
                                        $btn.attr('onclick', `clonetemplate(${calendarId}, '${calendarName}')`);
                                    } else if ($btn.find('.fa-trash').length) {
                                        $btn.attr('onclick', `return handleButtonClick(this, 'Deleting...', function() { deleteCalenderTemplate(${calendarId}); })`);
                                    }
                                }
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response.message || 'Failed to update template status.',
                            showConfirmButton: true
                        });
                        $lockBtn.html(`<i class="fa fa-lock"></i>`);
                    }
                    $lockBtn.prop('disabled', false);
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'An error occurred while updating the template status.',
                        showConfirmButton: true
                    });
                    $lockBtn.prop('disabled', false).html(`<i class="fa fa-lock"></i>`);
                }
            });
        }
    });
}

function loadMappingContent(calendarId) {
    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_calendar_detail'); ?>',
        type: 'POST',
        data: {
            calendar_id: calendarId
        },
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                let data = response.data;
                if (data.target_group === 'staff') {
                    loadStaffTypes(calendarId);
                } else {
                    loadClassSections(calendarId);
                }
            }
        }
    });
}

function loadStaffTypes(calendarId) {
    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_staff_type'); ?>',
        type: 'POST',
        success: function(staffTypesResponse) {
            let staffTypesObj = JSON.parse(
            staffTypesResponse); // expects object { "0": "Teaching", ... }
            let staffTypes = [];
            for (let key in staffTypesObj) {
                if (staffTypesObj.hasOwnProperty(key)) {
                    staffTypes.push({
                        id: key,
                        name: staffTypesObj[key]
                    });
                }
            }
            $.ajax({
                url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_selected_staff_types'); ?>',
                type: 'POST',
                data: {
                    calendar_id: calendarId
                },
                success: function(selectedResponse) {
                    let selectedTypes = JSON.parse(selectedResponse);
                    let selectedTypeArray = selectedTypes.map(item => parseInt(item
                        .assigned_staff_type));
                    let html = `
                <div class="form-group">
                    <div class="staff-types-container text-center">`;
                    staffTypes.forEach(function(staff) {
                        let isChecked = selectedTypeArray.includes(parseInt(
                            staff.id));
                        html += `
                    <button data-id="${staff.id}" class="btn btn-${isChecked ? 'success' : 'secondary'} m-2"
                            onclick="toggleStaffType(${staff.id}, ${calendarId}, this)"
                            style="min-width: 150px;">
                        <i class="fa fa-${isChecked ? 'times' : 'plus'}"></i> ${staff.name}
                    </button>`;
                    });
                    html += `</div></div>`;
                    $('#mapping_content').html(html);
                }
            });
        }
    });
}

function loadClassSections(calendarId) {
    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_class_calendar'); ?>',
        type: 'POST',
        data: {
            calendarID: calendarId
        },
        success: function(response) {
            let classes = JSON.parse(response);
            let html = `
        <div class="form-group">
            <div class="classes-container text-center">`;

            classes.forEach(function(cls) {
                let isChecked = cls.isChecked;
                html += `
            <button class="btn btn-${isChecked ? 'success' : 'secondary'} m-2"
                    onclick="toggleClassSection('${cls.classID}_${cls.sectionID}', ${calendarId}, this)"
                    style="min-width: 150px;">
                <i class="fa fa-${isChecked ? 'times' : 'plus'}"></i> ${cls.class_name} - ${cls.section_name}
            </button>`;
            });

            html += `</div></div>`;
            $('#mapping_content').html(html);
        }
    });
}

function deleteMapping(calendarId, type, value, button) {
    let actionUrl = '';
    let data = {
        id: calendarId
    };

    if (type === 'staff') {
        actionUrl = '<?php echo site_url('calendar_events_v2/Calendar_events_v2/delete_staff_type'); ?>';
        data.staff = [value];
    } else if (type === 'classSection') {
        actionUrl = '<?php echo site_url('calendar_events_v2/Calendar_events_v2/delete_class_section'); ?>';
        data.classSection = [value];
    }

    $.ajax({
        url: actionUrl,
        type: 'POST',
        data: data,
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                $(button).toggleClass('btn-success btn-secondary');
                $(button).find('i').toggleClass('fa-times fa-plus');
                fetchMappingInfo(calendarId);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Error!",
                    text: response.message || "Failed to delete mapping.",
                    showConfirmButton: true
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: "Something went wrong. Please try again.",
                showConfirmButton: true
            });
        }
    });
}

function toggleStaffType(type, calendarId, button) {
    let isAdding = $(button).hasClass('btn-secondary');

    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/calendar_staff_type'); ?>',
        type: 'POST',
        data: {
            id: calendarId,
            staff: [type],
            action: 'add'
        },
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                $(button).toggleClass('btn-secondary btn-success');
                $(button).find('i').toggleClass('fa-plus fa-times');
                saveMapping(calendarId);
                fetchMappingInfo(calendarId);
            } else if (response.status === 'error') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Already Mapped',
                    text: `Staff type/Section is already mapped to other calendar template`,
                    confirmButtonText: 'OK'
                });
            }
        }
    });
}

function toggleClassSection(classSection, calendarId, button) {
    let isAdding = $(button).hasClass('btn-secondary');
    if (!isAdding) {
        $.ajax({
            url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_AttDataIdWise'); ?>',
            type: 'POST',
            data: {
                calendar_id: calendarId
            },
            dataType: 'json',
            success: function(attData) {
                if (attData.status === true) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Not Allowed',
                        text: 'Unmapping is not allowed as attendance already exists for this calendar.'
                    });
                    return;
                } else {
                    deleteMapping(calendarId, 'classSection', classSection, button);
                }
            }
        });
        return;
    }
    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/get_AttDataIdWise'); ?>',
        type: 'POST',
        data: {
            calendar_id: calendarId
        },
        dataType: 'json',
        success: function(attData) {
            if (attData.status === true) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Not Allowed',
                    text: 'Mapping is not allowed as attendance already exists for this calendar.'
                });
                return;
            } else {
                $.ajax({
                    url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/calendar_class'); ?>',
                    type: 'POST',
                    data: {
                        id: calendarId,
                        classSection: [classSection],
                        action: 'add'
                    },
                    success: function(response) {
                        response = JSON.parse(response);
                        if (response.status === 'success') {
                            $(button).toggleClass('btn-secondary btn-success');
                            $(button).find('i').toggleClass('fa-plus fa-times');
                            saveMapping(calendarId);
                            fetchMappingInfo(calendarId);
                        } else if (response.status === 'error') {
                            let [className, sectionName] = classSection.split('_');
                            Swal.fire({
                                icon: 'warning',
                                title: 'Already Mapped',
                                text: `Class-Section "${className}-${sectionName}" is already mapped to calendar template "${response.template_name || 'Unknown'}"`,
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                });
            }
        }
    });
}

function saveMapping(calendarId) {
    let selectedStaff = [];
    $('.staff-types-container .btn-success').each(function() {
        selectedStaff.push($(this).data('id'));
        6
    });

    let selectedClasses = [];
    $('.classes-container .btn-success').each(function() {
        selectedClasses.push($(this).attr('onclick').match(/'([^']+)'/)[1]);
    });

    $.ajax({
        url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/save_mapping'); ?>',
        type: 'POST',
        data: {
            calendar_id: calendarId,
            staff: selectedStaff,
            classSections: selectedClasses
        },
        success: function(response) {},
        error: function() {
            console.error('Error saving mapping.');
        }
    });
}

function deleteMapping(calendarId, type, value, button) {
    let actionUrl = '';
    let data = {
        id: calendarId
    };

    if (type === 'staff') {
        actionUrl = '<?php echo site_url('calendar_events_v2/Calendar_events_v2/delete_staff_type'); ?>';
        data.staff = [value];
    } else if (type === 'classSection') {
        actionUrl = '<?php echo site_url('calendar_events_v2/Calendar_events_v2/delete_class_section'); ?>';
        data.classSection = [value];
    }

    $.ajax({
        url: actionUrl,
        type: 'POST',
        data: data,
        success: function(response) {
            response = JSON.parse(response);
            if (response.status === 'success') {
                $(button).toggleClass('btn-success btn-secondary');
                $(button).find('i').toggleClass('fa-times fa-plus');
                fetchMappingInfo(calendarId);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Error!",
                    text: response.message || "Failed to delete mapping.",
                    showConfirmButton: true
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: "Something went wrong. Please try again.",
                showConfirmButton: true
            });
        }
    });
}

$('#addCalendartemplateModal, #editCalendartemplateModal, #mapTemplateModal').on('shown.bs.modal', function() {
    $(this).data('lastFocus', document.activeElement);

    $(this).find('input:visible, button:visible').first().focus();
});

$('#addCalendartemplateModal, #editCalendartemplateModal, #mapTemplateModal').on('hidden.bs.modal', function() {
    let lastFocus = $(this).data('lastFocus');
    if (lastFocus) {
        lastFocus.focus();
    }
});

$('.modal').on('keydown', function(e) {
    if (e.key === 'Tab') {
        let focusableElements = $(this).find(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        let firstFocusableElement = focusableElements[0];
        let lastFocusableElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstFocusableElement) {
                lastFocusableElement.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastFocusableElement) {
                firstFocusableElement.focus();
                e.preventDefault();
            }
        }
    }
});
</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style type="text/css">
.text-muted {
    color: rgb(164, 176, 186) !important;
}

.title_edit_class {
    margin-left: -14px
}

.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
    cursor: pointer;
    /* Add this line */
}

.edit_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 14px;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
}

.status-tag {
    padding: 2px 8px;
    border-radius: 16px;
}

.success-status {
    r-color: #cdffcf;
}

.warning-status {
    background-color: blanchedalmond;
}

.default-status {
    background-color: #eaeaea;
}

.danger-status {
    background-color: #ffab9d;
}

.btn {
    margin-right: 4px;
    border-radius: 1.2rem;
}

.btn .fa {
    margin-right: 0px;
}

.btn-warning,
.btn-info {
    width: auto;
}

.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
}

.modal-dialog {
    width: 60%;
    margin: auto;
}

.switch {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.switch input {
    display: none;
}

.switch span {
    width: 40px;
    height: 20px;
    background: #ccc;
    border-radius: 20px;
    position: relative;
    transition: background 0.3s;
    margin-right: 10px;
}

.switch span:before {
    content: '';
    width: 18px;
    height: 18px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: 1px;
    transition: transform 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.switch input:checked+span {
    background: #007bff;
}

.switch input:checked+span:before {
    transform: translateX(20px);
}

#addCalendartemplateModal .col-form-label,
#editCalendartemplateModal .col-form-label {
    text-align: right !important;
}

.modal:focus {
    outline: none;
}

.modal-dialog:focus {
    outline: none;
}

.modal-content {
    outline: none;
}

.close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

#update_target_group {
    pointer-events: none;
}
</style>