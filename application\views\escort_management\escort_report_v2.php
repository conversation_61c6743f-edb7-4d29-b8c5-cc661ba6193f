<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student_tracking/student_tracking_controller');?>">Student Tracking</a></li>
    <li>Escort Report</li>  
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3>
                    <a class="back_anchor" href="<?php echo site_url('student_tracking/student_tracking_controller') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Escort Report
                </h3>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-3"><label for="date">Filter</label>
            <select class="form-control" id="date" onchange="onchangeDateFilter()">
                <option value="1">Today</option>
                <option value="2">Yesterday</option>
                <option value="3">Last 7 Days</option>
                <option value="4">Last 30 Days</option>
                <option value="5">Last 90 Days</option>
                <option value="6">Custom Range</option>
            </select></div>
            <div class="col-md-3"><label for="from_date">From Date</label>
            <input id="from_date" readonly class="existing-class form-control" placeholder="" value="" autofocus="" type="text" name=""></div>
            <div class="col-md-3"><label for="to_date">To Date</label>
            <input id="to_date" readonly class="existing-class form-control" placeholder="" value="" autofocus="" type="text" name=""></div>
            <div class="col-md-3">
                <label for="get" style="opacity: 0;">---</label><br>
                <button style="display: none; width: 4.7rem;" id="get" class="btn btn-md btn-info form-control text-center">Get</button>
            </div>
        </div>
        <div class="panel-body" style="margin-bottom: 10px; height: 530px; overflow: auto;" id="escort_report_div">
            <div class="no-data-display">No Data</div>
        </div>
    </div>
</div>

<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

    $(document).ready(function() {
        $("#from_date").val(moment().format('DD-MM-YYYY'));
        $("#to_date").val(moment().format('DD-MM-YYYY'));

        $('#from_date, #to_date').datepicker({
            todayBtn: "linked",
            language: "it",
            autoclose: true,
            todayHighlight: true,
            format: 'dd-mm-yyyy',
            orientation: "top",
            endDate: "today"
        });

        get_all_report_v2();
    });

    $("#get").click(function() {
        get_all_report_v2();
    });

    function onchangeDateFilter() {
        var date_range_name_id= $("#date").val();
        switch(Number(date_range_name_id)) {
            case 1: {
                $("#from_date").val(moment().format('DD-MM-YYYY'));
                $("#to_date").val(moment().format('DD-MM-YYYY'));
            } break;
            case 2: {
                $("#from_date").val(moment().subtract('days', 1).format('DD-MM-YYYY'));
                $("#to_date").val(moment().subtract('days', 1).format('DD-MM-YYYY'));
            } break;
            
            case 3: {
                $("#from_date").val(moment().subtract('days', 6).format('DD-MM-YYYY'));
                $("#to_date").val(moment().format('DD-MM-YYYY'));

            } break;
            case 4: {
                $("#from_date").val(moment().subtract('days', 29).format('DD-MM-YYYY'));
                $("#to_date").val(moment().format('DD-MM-YYYY'));

            } break;
            case 5: {
                $("#from_date").val(moment().subtract('days', 89).format('DD-MM-YYYY'));
                $("#to_date").val(moment().format('DD-MM-YYYY'));

            } break;
            default: {
                $("#from_date").prop('readonly', false).attr('maxlength', '10').val('DD-MM-YYYY').addClass('datepicker');
                $("#to_date").prop('readonly', false).attr('maxlength', '10').val('DD-MM-YYYY').addClass('datepicker');

            } break;
        }
        if(+date_range_name_id !== 6) {
            $("#get").hide()
        } else {
            $("#get").show()
        }
        onchange_date(+date_range_name_id);
    }


    function onchange_date(date_range_name_id) {
        var date_range= $("#date").val();
        if(+date_range == 1 || +date_range == 2) {
            $("#filter_button").hide();
        } else {
            $("#filter_button").show();
        }
        if(date_range_name_id !== 6) {
            get_all_report_v2();
        } else {
            $("#escort_report_div").html('');
        }
    }

    function get_all_report_v2() {
        $("#escort_report_div").html(`Loading...`);
        var from_date= $("#from_date").val();
        var to_date= $("#to_date").val();
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/get_all_report_v2'); ?>',
            type: "post",
            data: {from_date, to_date},
            success(data) {
                var p_data = JSON.parse(data);
                // console.log('DATA= ', p_data);
                if(p_data == -1) {
                    $("#escort_report_div").html(`<div class="no-data-display">Data Not Found</div>`);
                    return;
                }
                let record= `<table class="table table-bordered table-hover" style="">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Status</th>
                                        <th>Person Name</th>
                                        <th>Checkin Time</th>
                                        <th>Checkin Security</th>
                                        <th>Checkout Time</th>
                                        <th>Checkout Security</th>
                                        <th>Drop By</th>
                                        <th>Pickup By</th>
                                    </tr>
                                </thead>
                                <tbody>`;

                let srno= 1;
                for(var index in p_data) {
                    let disp= '';
                    if(p_data[index].droper_name == ' (Student)' || p_data[index].droper_name == '-' || p_data[index].droper_name == 'Self') {
                        disp= "display: none;";
                    }
                    let disp_pick= '';
                    if(p_data[index].escorter_name == ' (Student)' || p_data[index].escorter_name == '-' || p_data[index].escorter_name == 'Self') {
                        disp_pick= "display: none;";
                    }
                    let disp_person= '';
                    if(p_data[index].person_type == 'Student') {
                        disp_person= "display: none;";
                    }

                    record += ` <tr>
                                    <td>${srno++}</td>
                                    <td>${p_data[index].status == 'Out' ? '<font color="green>">Check-out</font>' : '<font color="red">Check-in</font>'}</td>
                                    <td>${p_data[index].person_name} <button style="${disp_person}" class="btn btn-sm btn-warning pull-right" onclick="show_pic('${p_data[index].person_type}', ${p_data[index].person_id})">View Photo</button></td>
                                    <td>${p_data[index].checkin_timestamp}</td>
                                    <td>${p_data[index].checkin_security || '-'}</td>
                                    <td>${p_data[index].checkout_timestamp || '-'}</td>
                                    <td>${p_data[index].checkout_security || '-'}</td>
                                    <td>${p_data[index].droper_name || '-'} <button style="${disp}" class="btn btn-sm btn-warning pull-right" onclick="show_pic('${p_data[index].drop_person_type}', ${p_data[index].drop_person_id})">View Photo</button></td>
                                    <td>${p_data[index].escorter_name || '-'}  <button style="${disp_pick}" class="btn btn-sm btn-warning pull-right" onclick="show_pic('${p_data[index].escort_type}', ${p_data[index].escort_person_id})">View Photo</button></td>
                                </tr>`;
                }
                record += `</tbody>
                        </table>`;

                $("#escort_report_div").html(record);
                
            }
        });
    }

    function show_pic(type, id) {
        $.ajax({
            url: '<?php echo site_url('escort_management/escort_controller/get_droper_pcker_pic'); ?>',
            type: "post",
            data: {type, id},
            success(data) {
                var srcObj = JSON.parse(data);
                let src= srcObj.p1;
                if(src.trim() == '') {
                    src= srcObj.p2;
                }

                if(src == '' || src == undefined || src == 'NA' || src == 0) {
                    src= `https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/default_image.jpg`;
                }
                Swal.fire({
                    imageUrl: src,
                    imageWidth: 320,
                    imageHeight: 270,
                    imageAlt: 'Custom image',
                });
                
            }
        });
    }

</script>