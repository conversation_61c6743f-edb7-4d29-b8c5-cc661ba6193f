<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller');?>"><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></li>
  <li>Add <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Create <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?>
          </h3>
        </div>
      </div>
    </div>
    <div class="modal-body">
      <div class="col-md-12">

        <div class="col-md-4">
          <div class="form-group">
            <label class="control-label" for="section_id">Select Date</label>
            <div class="input-group date" id="datePicker"> 
              <input type="text" class="form-control" id="date" name="date" placeholder="Select Date" required="" value="<?php echo date('d-m-Y'); ?>" >
              <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
            </div> 
          </div>
        </div>

        <div class="col-md-4">
          <div class="form-group">
            <label class="control-label" for="section_id">Class/Section</label>
            <select required="" class="form-control" id="class_section" name="class_section" onchange="classSection_wise_search_std()">
            </select>
          </div>
        </div>

      </div>
    </div>

    <div class="card-body">
      <div id="student_data">
      </div>
    </div>
  </div>
</div>

<script>
  function add_chronicles(sl, std_id, std_name) {
    $('chronicle_content', $(".summernote").code(''));
    $('#chronicalStudentId').val(std_id);
    $('#stdnamepopup').html(std_name);
    $('#clone_atthaced_file').val('');
    $('#clone_download_button').hide();
  }
</script>

<div class="modal fade" id="add_chronicles" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Add Chronicles - <span id="stdnamepopup"> </span> </h4>

        <button type="button" id="copy-from" class="btn btn-primary" style="float:right;">Clone From...</button>
      </div>
      <form enctype="multipart/form-data" id="addChronical" class="form-horizontal" data-parsley-validate method="post">
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <input type="hidden" name="student_id" id="chronicalStudentId">
          <div class="form-group">
          
           <div class="col-md-4" style="float: right;">
            <select class="form-control input-md" id="template_id" onchange="applyTemplate()" name="template_id">
              <?php foreach ($templates as $template) {
                  echo '<option value="' . $template['name'] .'">' . $template['name'] . '</option>';
                } ?>
            </select>
          </div>
        </div>

          <textarea name="chronicle_content" id="chronicle_content" data-parsley-required-message="Please enter content" placeholder="Body" class="summernote form-control" style="height: 100px;"></textarea><br>

          <div class="form-group">
          <div class="col-md-12">
            <input type="hidden" name="clone_atthaced_file" id="clone_atthaced_file">
            <div id="cloneAttachedView"></div>
                <input type="file" class="form-control verify-size" data-parsley-required-message="Please select a document to upload" name="certificate_name" id="cerificate_img_Id" type="file" accept="application/pdf">
                <span class="help-block">Allowed file types - pdf; Allowed size - upto 5Mb</span>
                <span id="file-size-error" style="color: red;"></span>
            <span id="resource-file_error" style="color: red;"></span>
            <p id="error1" style="color:red;"></p>
            </div>
        </div>
         
        </div>
        <div class="modal-footer">
          <button type="button" onclick="save_chronicles()" id="confirmBtn"  class="btn btn-success">Send <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></button>
        <button type="button" class="btn btn-danger" id="cleardata" data-dismiss="modal">Close</button>

        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade" id="view_chronicles" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> - <span id="stdnamepopup1"></h4>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <input type="hidden" name="student_id" id="chronicalStudentId1">

          <div id="chronicles-detail">
                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>

<div id="copy-from-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">

        <div class="modal-dialog" style="width: 60%;margin:2% auto;">

          <!-- Modal content-->
          <div class="modal-content">
              <div class="modal-header">
                <h4 class="modal-title">Clone data from</h4>
              </div>
              <div class="modal-body">
                <label class="control-label">Select <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> to be copied</label>
                <select class="form-control" onchange="select_clone_chronicles()" name="selected_chronicles_id" id="selected_chronicles_id">
                  <option value="">Select <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></option>
                </select>
              </div>
              <div class="modal-footer" style="justify-content: center;">
                <button type="button" id="cancelModal" style="width: 120px;" class="btn btn-danger my-0" data-dismiss="modal">Close</button>
                <button type="button" disabled style="width: 120px;" onclick="copy_chronicles()" id="confirmCloneBtn"  class="btn btn-primary my-0">Confirm</button>
              </div>
          </div>
      </div>
</div>


<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>


<script type="text/javascript">
  $(document).ready(function(){
    select_clone_chronicles();
  });

  function select_clone_chronicles() {
    var cloneSelect = $('#selected_chronicles_id').val();
    if(cloneSelect !=''){
      $('#confirmCloneBtn').removeAttr('disabled');
    }else{
      $('#confirmCloneBtn').attr('disabled','disabled');
    }
  }
  function applyTemplate() {
      var templateName = $("#template_id option:selected").val();
      $.ajax({
        url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getTemplate'); ?>',
        data: {'templateName': templateName},
        type: "post",
        success: function (data) {
          templateData = $.parseJSON(data);
          console.log(templateData);
          $('.summernote').code(templateData);
        },
        error: function (err) {
          console.log(err);
        }
    });
  }
  $(document).ready(function() {
        get_class_section();
        checkvalueDisabled();
    });

    function get_class_section(){
        $('#student_name').hide();
    $('#search').hide();
        $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_class_section'); ?>',
          type: "post",
          success: function (data) {
          var clsSection = $.parseJSON(data);
          option = '<option value="">Class Section</option>';
          for (i = 0; i < clsSection.length; i++) {
          option +='<option value="'+clsSection[i].csId+'">'+clsSection[i].classSection+'</option>'
          }
          $('#class_section').html(option);
          },

          error: function (err) {
            console.log(err);
          }
    });
    }


   function classSection_wise_search_std() {
        var sectionId = $("#class_section").val();
        if(sectionId ==''){
          $("#student_data").html('');  
        }
        var selected_date = $('#date').val();
        if(sectionId) {
            $.ajax({
                url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'sectionId':sectionId,'selected_date':selected_date},
                success: function(data) {
                  var std = JSON.parse(data);
                  console.log(std);
                  $("#student_data").html(prepare_student_table(std, selected_date));  
                  console.log(std); 
                }
            });
        }
    }
    function prepare_student_table (std, selected_date) {
    var html = '';
    if(!std)
        html += "<h4>Student Not Found</h4>";
    else {
        html += `
            <table id="customers2" class="table table-bordered datatable" style="width:70%; margin:auto;">
                <thead>
                    <tr>
                        <th style="width:5%">#</th>
                        <th style="width:20%">Student Name</th>
                        <th style="width:15%">Actions</th>
                    </tr>
                </thead>
                <tbody>
            `;
        for (i=0;i < std.length; i++) {
          var checked = '';
          var disabled = '';
          var enable = '';
          if (std[i].sent_check == 1) {
            checked = 'checked';
            disabled = 'disabled';
          }else if (std[i].sent_check == 0) {
            checked = 'unchecked';
            enable = 'disabled';
          }
            html += "<tr><td>" + (i+1) + "</td>";
            html += "<td>"+std[i].student_name + "</td>";
            html += '<td><a onclick="add_chronicles('+i+','+std[i].id+', \''+std[i].student_name+'\')" id="add_button'+i+'" data-target="#add_chronicles" '+disabled+' data-toggle="modal" class="btn btn-success" >Add <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a> <a href="#" onclick="view_chronicles('+std[i].id+',\''+selected_date+'\', \''+std[i].student_name+'\' )" data-target="#view_chronicles" '+enable+' data-toggle="modal" id="view_button'+i+'" class="btn btn-primary">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></td>';

        }
        html += '</tbody></table>';
    }
    return html;
}

function view_chronicles(student_id, selected_date, std_name) {
  $('#chronicalStudentId1').val(student_id);
    $('#stdnamepopup1').html(std_name);
  $.ajax({
    url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_chronicles_by_student_id'); ?>',
    type: 'post',
    data: {
      'student_id': student_id,
      'selected_date': selected_date,
    },
    success: function(data) {
      var resData = $.parseJSON(data);
      console.log(resData);
      $('#chronicles-detail').html(construct_view_chronicles(resData));
    }
  });
}

function construct_view_chronicles(resData) {
  var viewhtml = '';
  viewhtml += '<div class="content-div" style="height:350px; overflow-y: auto;""> <b> Contant : </b> <br>' +resData.chronicle_content+ '</div><br>';
  viewhtml += '<br>';
  if(resData.file_path != ''){
    viewhtml += '<b>Attachment: <b>';
    viewhtml += '<br>';
    viewhtml += '<a onclick="download_chronicles('+resData.id+')" class="btn btn-primary"  ><i class="fa fa-download"> </i> Download </a>';
  }
 

  return viewhtml;
}

function download_chronicles(rowid) {
  window.location.href = '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/download_chronicles_by_rowid/'); ?>' + rowid;
}

function checkvalueDisabled(i) {
  var checkvalue = $('#check'+i).val();
  const checkBox=$('#check'+i);

  if ($(checkBox).is(':checked')) {
    // $(checkBox).attr('value', 'true');
    $('#add_button'+i).prop('disabled',true).addClass('btn btn-success');
    $('#view_button'+i).prop('disabled',false);
  } else {
    // $(checkBox).attr('value', 'false');
    $('#add_button'+i).prop('disabled',false).addClass('btn btn-danger');
    $('#view_button'+i).prop('disabled',true);
  }

 }

 $("#fileupload").change(function (){
    var files = $(this)[0].files;
    fileCount = files.length;
    $("#fileCount").html(fileCount+' Attachments');
  });

 function save_chronicles() {
  var selectedDate = $('#date').val();
    var $form = $('#addChronical');
    if ($form.parsley().validate()){
      var form = $('#addChronical')[0];
      var form_data = new FormData(form);
      form_data.append('chronicle_content', $(".summernote").code());
      var file_data = $('#fileupload').prop('files');
      for(var i in file_data) {
            form_data.append('attachment[]', file_data[i]);
        }
      form_data.append('created_on', selectedDate);
      $('#addChronical').trigger("reset");
      $('#clone_atthaced_file').trigger("reset");
      $('#cloneAttachedView').trigger("reset");

      $("#confirmBtn").html('Please wait..').attr('disabled', 'disabled');
      $.ajax({
        url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/save_chronicles') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
          console.log(data);
          $('#add_chronicles').modal('hide');
          classSection_wise_search_std();
          $("#confirmBtn").html('Submit').removeAttr('disabled', 'disabled');
      form_data.append('chronicle_content', $(".summernote").code(''));


        }
      });
     }
    }
</script>

<script type="text/javascript">
  $(document).ready(function() {

    $('#datePicker').datetimepicker({
     viewMode: 'days',
      format: 'DD-MM-YYYY',
      minDate:new Date()
    }).change(dateChanged)
    .on('dp.change',dateChanged);
  });
  function dateChanged(ev) {
    $('#class_section').val('');
    classSection_wise_search_std();
  }

  $("#cerificate_img_Id").change(function() {
    var file = document.getElementById('cerificate_img_Id');
    if (!file.files[0]) {
      document.getElementById('resource-file_error').innerHTML = 'Resource is required';
      return false;
    }
    document.getElementById('resource-file_error').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
      $("#file-size-error").html('File size exceeded.');
      $("#cerificate_img_Id").val('');
    } else {
      $("#file-size-error").html('');
    }
  });
</script>
<script type="text/javascript">
  $("#copy-from").click(function() {
  var selectedDate = $('#date').val();
  $("#copy-from-modal").modal('show');
  $('#confirmCloneBtn').prop('disabled',true);
 $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getchronicles_clone'); ?>',
          data: {'selectedDate':selectedDate},
          type: "post",
          success: function (data) {
            var chronicles = $.parseJSON(data);
            var options = '<option value="">Select <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></option>';
            for (var i = 0; i < chronicles.length; i++) {
              options += `<option value="${chronicles[i].id}">${chronicles[i].student_name}</option>`;
            }
            $("#selected_chronicles_id").html(options);

          },
          error: function (err) {
            console.log(err);
          }
      });
  });

  function copy_chronicles() {
    var chronicles_id = $("#selected_chronicles_id").val();
      $("#copy-from-modal").modal('hide');
    $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getchroniclesById'); ?>',
          data: {chronicles_id: chronicles_id},
          type: "post",
          success: function (data) {
            var chrdata = $.parseJSON(data);
            $('.summernote').code(chrdata.chronicle_content); 

            var viewhtml = '';
            if(chrdata.file_path != ''){
              viewhtml += '<b>Clone Attachment: <b>';
              viewhtml += '<br>';
              viewhtml += '<a onclick="download_chronicles('+chrdata.id+')" class="btn btn-primary" id="clone_download_button"><i class="fa fa-download"> </i> Download </a>';
            }
            $('#cloneAttachedView').html(viewhtml);
            $('#clone_atthaced_file').val(chrdata.file_path);
          },
          error: function (err) {
            console.log(err);
          }
      });
  }

  
</script>

<style type="text/css">
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
    max-height: 30%;
    overflow-y: scroll;
}
</style>