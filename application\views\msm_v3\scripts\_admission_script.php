<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript" src="<?php echo site_url('assets/js/morris.js') ?>"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<?php $this->load->view("msm_v3/scripts/color_theme") ?>

<script>
    var json_school_list = '<?php echo $json_school_list ?>';
    var school_list = JSON.parse(json_school_list);
    var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';
    const selectElement = document.getElementById('acad_select');
    selectElement.value = default_acad_year;
    function waitOneSecondSync() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve();
            }, 400);
        });
    }
    $("#dropdownSchool_lead_comp_ay").select2({
        placeholder: "Select year"
    });

    selectElement.addEventListener('change', async function() {
        default_acad_year = selectElement.value;
        sessionStorage.setItem("msm_acad_year", selectElement.value);
        <?php if ($this->authorization->isAuthorized('MSM.ADMISSIONS') && $this->authorization->isModuleEnabled('MSM')) { ?>
            display_admission_pipeline(school_list, default_acad_year);
            await waitOneSecondSync();
            display_target_vs_converted_applications(school_list, default_acad_year);
            await waitOneSecondSync();
            // display_admission_statistics_graph(school_list, default_acad_year);
            // await waitOneSecondSync();
            display_schools_dropdown_adm_appl(school_list, default_acad_year);
            display_admission_application_trend_last_7_days(school_list, default_acad_year, 'week');
            await waitOneSecondSync();
            display_schools_dropdown_admission(school_list, default_acad_year);
            display_admission_statuswise_trend(school_list, default_acad_year);
            await waitOneSecondSync();
            // display_schools_dropdown_adm_act(school_list, default_acad_year);
            // display_admission_activity_trend_last_7_days(school_list, default_acad_year);
            // await waitOneSecondSync();
            // display_leads_conversion_graph(school_list, default_acad_year);
            // await waitOneSecondSync();
            display_schools_dropdown_lead_eq(school_list, default_acad_year);
            display_lead_enquiry_trend_last_7_days(school_list, default_acad_year, 'week');
            await waitOneSecondSync();
            display_schools_dropdown_lead_eq_comparision(school_list);
            display_lead_enquiry_comparision_trend_last_7_days(school_list);
            await waitOneSecondSync();
            display_schools_dropdown_lead_act(school_list, default_acad_year);
            display_lead_activity_trend_last_7_days(school_list, default_acad_year);
            await waitOneSecondSync();
            display_schools_dropdown_leads(school_list, default_acad_year);
            display_leads_statuswise_trend(school_list, default_acad_year);
            await waitOneSecondSync();
            display_schools_dropdown_leads_source(school_list, default_acad_year);
            display_leads_sourcewise_trend(school_list, default_acad_year);
            await waitOneSecondSync();
            display_schools_dropdown_student_tc(school_list, default_acad_year);
            display_student_tc_data(school_list, default_acad_year);
        <?php } ?>
    });
    const overall_admission_array = [];
    const overall_leads_array = [];
    const overall_statistics = [];

    $(document).ready(async function () {
        google.charts.load("current", {packages: ["corechart", "bar"]});
        $(".admission_tb_btn").css("display", "block");
        $(".overview_tb_dropdown").css("display", "block");
        $(".overview_tb_btn").css("display", "none");
        $(".total_adm_tb_btn").css("display", "block");
        $(".total_leads_tb_btn").css("display", "block");
        $(".total_conv_tb_btn").css("display", "block");

        load_theme();

        display_admission_pipeline(school_list, default_acad_year);
        await waitOneSecondSync();
        display_target_vs_converted_applications(school_list, default_acad_year);
        await waitOneSecondSync();
        // display_admission_statistics_graph(school_list, default_acad_year);
        // await waitOneSecondSync();
        display_schools_dropdown_adm_appl(school_list, default_acad_year);
        display_admission_application_trend_last_7_days(school_list, default_acad_year, 'week');
        await waitOneSecondSync();
        display_schools_dropdown_admission(school_list, default_acad_year);
        display_admission_statuswise_trend(school_list, default_acad_year);
        await waitOneSecondSync();
        // display_schools_dropdown_adm_act(school_list, default_acad_year);
        // display_admission_activity_trend_last_7_days(school_list, default_acad_year);
        // await waitOneSecondSync();
        // display_leads_conversion_graph(school_list, default_acad_year);
        // await waitOneSecondSync();
        display_schools_dropdown_lead_eq(school_list, default_acad_year);
        display_lead_enquiry_trend_last_7_days(school_list, default_acad_year, 'week');
        await waitOneSecondSync();
        display_schools_dropdown_lead_eq_comparision(school_list);
        display_lead_enquiry_comparision_trend_last_7_days(school_list);
        await waitOneSecondSync();
        display_schools_dropdown_lead_act(school_list, default_acad_year);
        display_lead_activity_trend_last_7_days(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_leads(school_list, default_acad_year);
        display_leads_statuswise_trend(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_leads_source(school_list, default_acad_year);
        display_leads_sourcewise_trend(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_student_tc(school_list, default_acad_year);
        display_student_tc_data(school_list, default_acad_year);
    });

    function flip_admission_view(type, id, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');
        switch(id){
            case 'admission_pipeline_card':
                switch (type) {
                    case 'column_chart':
                        $('#admission_pipeline_pie_card').css('display', 'block');
                        $('#admission_pipeline_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#admission_pipeline_table_card').css('display', 'block');
                        $('#admission_pipeline_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'admission_tvsc_card':
                switch (type) {
                    case 'column_chart':
                        $('#admission_tvsc_pie_card').css('display', 'block');
                        $('#admission_tvsc_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#admission_tvsc_table_card').css('display', 'block');
                        $('#admission_tvsc_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'admission_statistics_card':
                switch (type) {
                    case 'column_chart':
                        $('#admission_statistics_pie_card').css('display', 'block');
                        $('#admission_statistics_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#admission_statistics_table_card').css('display', 'block');
                        $('#admission_statistics_pie_card').css('display', 'none');
                    default:
                        break;
                }
                break;
            case 'admission_application_card':
                switch (type) {
                    case 'column_chart':
                        $('#admission_application_pie_card').css('display', 'block');
                        $('#admission_application_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#admission_application_table_card').css('display', 'block');
                        $('#admission_application_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'admission_activity_card':
                switch (type) {
                    case 'column_chart':
                        $('#admission_activity_pie_card').css('display', 'block');
                        $('#admission_activity_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#admission_activity_table_card').css('display', 'block');
                        $('#admission_activity_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'leads_conversion_card':
                switch (type) {
                    case 'column_chart':
                        $('#leads_conversion_pie_card').css('display', 'block');
                        $('#leads_conversion_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#leads_conversion_table_card').css('display', 'block');
                        $('#leads_conversion_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'leads_enquiry_card':
                switch (type) {
                    case 'column_chart':
                        $('#leads_enquiry_pie_card').css('display', 'block');
                        $('#leads_enquiry_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#leads_enquiry_table_card').css('display', 'block');
                        $('#leads_enquiry_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'leads_enquiry_comparision_card':
                switch (type) {
                    case 'column_chart':
                        $('#leads_enquiry_comparision_pie_card').css('display', 'block');
                        $('#leads_enquiry_comparision_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#leads_enquiry_comparision_table_card').css('display', 'block');
                        $('#leads_enquiry_comparision_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'leads_activity_card':
                switch (type) {
                    case 'column_chart':
                        $('#leads_activity_pie_card').css('display', 'block');
                        $('#leads_activity_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#leads_activity_table_card').css('display', 'block');
                        $('#leads_activity_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
        }
        
    }

    //Admission Pipeline
    function display_admission_pipeline(school_list, acad_year) {
        disableTableButtons();
        $("#total_adm_tb_btn").html('...');
        $("#total_leads_tb_btn").html('...');
        $("#total_conv_tb_btn").html('...');
        $("#admission_pipeline_graph").html("<center>Loading Pipeline Statistics...</center>");
        $("#pipeline_trend_table").html('<tr><td colspan="5">Loading Pipeline Statistics...</td></tr>');
        var total_leads = 0, total_applications = 0, total_converted = 0;
        const mapLoop = async () => {
            const promises = await school_list.map(async (school) => {
                const response = await get_pipeline_data(school.school_code, school.school_domain, acad_year)
                    .then(response => {
                        var leads = 0;
                        var admissions = 0;
                        var converted = 0;
                        var converted_percentage = 0;
                        var values_array = [school.school_code.toUpperCase()];
                        Object.keys(response).forEach(key => {
                            const value = response[key];
                            if (key.includes("# Leads")) {
                                leads += parseInt(value);
                                total_leads += parseInt(value);
                            } else if (key.includes("# Applications")) {
                                admissions += parseInt(value);
                                total_applications += parseInt(value);
                            } else if (key.includes("# Converted")) {
                                converted += parseInt(value);
                                total_converted += parseInt(value);
                            }
                            converted_percentage = (parseInt(leads) > 0) ? Math.min((parseInt(converted) / parseInt(leads)) * 100, 100) : 0;
                        });
                        values_array.push(leads, admissions, converted, parseFloat(converted_percentage.toFixed(1)));
                        return values_array;
                    })
                    .catch(err => {
                        console.log(err);
                        return [school.school_code];
                    });

                return response;
            });

            const values_arrays = await Promise.all(promises);

            // Arrange in a sequence
            var temp_array = [], inp_array = [];
            school_list.forEach(sl => {
                values_arrays.forEach(ola => {
                    if (sl.school_code.toUpperCase() == ola[0]) {
                        temp_array.push(ola);
                        inp_array.push(ola);
                        return false;
                    }
                });
            });

            $("#total_leads_tb_btn").html(total_leads);
            $("#total_adm_tb_btn").html(total_applications);
            $("#total_conv_tb_btn").html(total_converted);
            _construct_individual_pipeline(temp_array)
            _construct_individual_pipeline_table(inp_array.slice());
            _construct_admission_pipeline_insights(inp_array);
        }
        mapLoop();
    }

    function get_pipeline_data(school_code, school_domain, acad_year) {    
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'acad_year': acad_year,
                    'api': 'get_admission_pipeline_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_individual_pipeline(input_array) {
        input_array.unshift(['School Code','# Leads', '# Applications', '# Converted', 'Converted Percentage']);
        console.log(input_array);
        
        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(input_array);

            var options = {
                height: 450,
                chartArea: {left: "10%", top: "10%", width: "80%", height: "70%", bottom: "15%"},
                vAxes: {
                    0: {
                        bar: { groupWidth: "100%" },
                        axisTitlesPosition: 'in',
                        maxValue: 1000,
                        viewWindow: {
                            max: 1000,
                        }
                    },
                    1: {
                        bar: { groupWidth: "10%" },
                        axisTitlesPosition: 'in',
                        maxValue: 120,
                        viewWindow: {
                            max: 120,
                        }
                    },
                },
                annotations: {
                    textStyle: {
                    fontSize: 11,
                    bold: true
                    },
                },
                seriesType: "bars",
                series:{
                    0: { 
                        color: color1,
                        areaOpacity: 0.25,
                        visibleInLegend: true,
                        annotations: {
                            alwaysOutside : true,
                            stem: {
                                length: 30,
                            },
                        },
                        targetAxisIndex: 0,
                    },
                    1: { 
                        color: color2,
                        areaOpacity: 0.25,
                        visibleInLegend: true,
                        annotations: {
                            alwaysOutside : false,
                        },
                        targetAxisIndex: 0,
                    },
                    2: { 
                        color: color3,
                        areaOpacity: 0.25,
                        visibleInLegend: true,
                        annotations: {
                            alwaysOutside : false,
                        },
                        targetAxisIndex: 0,
                    },
                    3: { 
                        color: color3, 
                        targetAxisIndex: 1, 
                        type: "line" 
                    },
                },
                legend: {
                    position: 'top', alignment: 'center'
                }
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0,
                1,
                { sourceColumn: 1,
                    type: "number",
                    role: "annotation",
                    color: "#000000",
                },
                2,
                { sourceColumn: 2,
                    type: "number",
                    role: "annotation",
                    color: "#000000",
                },
                3,
                { sourceColumn: 3,
                    type: "number",
                    role: "annotation",
                    color: "#000000",
                },
                4,
                { sourceColumn: 4,
                    type: "number",
                    role: "annotation",
                    color: "#000000",
                }]);
            var chart = new google.visualization.ComboChart((document.getElementById(`admission_pipeline_graph`)));
            chart.draw(view, options);
            var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;"># Leads</span>
                    <div style="width: 15px; height: 15px; background-color: ${color2}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;"># Applications</span>
                    <div style="width: 15px; height: 15px; background-color: ${color3}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;"># Converted</span>
                    </div>`;
            $("#admission_pipeline_legend_div").html(html);
        });
    }

    function _construct_individual_pipeline_table(input_array, sc) {
        enableTableButtons();
        // console.log(input_array);
        var html = '';
        var i = 1;
        let total1=0, total2 = 0, total3 = 0, total4 = 0;
        input_array.forEach(obj => {
            const val1 = parseFloat(obj[1]) || 0;
            const val2 = parseFloat(obj[2]) || 0;
            const val3 = parseFloat(obj[3]) || 0;
            const val4 = parseFloat(obj[4]) || 0;
            total1 += val1;
            total2 += val2;
            total3 += val3;
            html += `
                <tr>
                    <td class="text-uppercase align-middle text-center text-sm">${i}</td>
                    <td class="text-uppercase align-middle text-center text-sm">${obj[0]}</td>
                    <td class="align-middle text-center text-sm">${obj[1]}</td>
                    <td class="align-middle text-center text-sm">${obj[2]}</td>
                    <td class="align-middle text-center text-sm">${obj[3]}</td>
                    <td class="align-middle text-center text-sm">${obj[4]}</td>
                </tr>
            `;
            i++;
        });
        html += `
            <tr>
                <td class="text-uppercase align-middle text-center text-sm"><strong>${i}</strong></td>
                <td class="text-uppercase align-middle text-center text-sm"><strong>Total</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total1}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total2}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total3}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${((total3 / total1) * 100).toFixed(1)}</strong></td>
            </tr>
        `;
        $('#pipeline_trend_table').html(html);
    }

    function _construct_admission_pipeline_insights(input_array) {
        $("#admission_insights_pipeline_body").html(''); // Clear previous content

        var success_threshold_perc = 20;
        var failure_threshold_perc = 20;

        // Arrays to store highlights and lowlights
        let highlights = [];
        let lowlights = [];

        // Process data to classify as highlights or lowlights
        input_array.forEach(arr => {
            let sc_code = arr[0];
            let leads = parseInt(arr[1]);
            let converted = parseInt(arr[3]);

            let achieved_rate = leads > 0 ? (converted / leads) * 100 : 0;
            if (achieved_rate > success_threshold_perc) {
                highlights.push(`<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has converted ${achieved_rate.toFixed(1)}% from the ${leads} leads.`);
            } else if (achieved_rate < failure_threshold_perc) {
                lowlights.push(`<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has converted ${achieved_rate.toFixed(1)}% from the ${leads} leads.`);
            }
        });

        // Helper function to create table rows with 2 entries per row
        function createTableRows(dataArray, color) {
            let rows = "";
            for (let i = 0; i < dataArray.length; i += 2) {
                let col1 = dataArray[i] || ""; // First column
                let col2 = dataArray[i + 1] || ""; // Second column (if exists)
                rows += `<tr><td style="color: ${color}; padding: 0">${col1}</td><td style="color: ${color}; padding: 0">${col2}</td></tr>`;
            }
            return rows;
        }

        // Generate Highlights Table
        if (highlights.length > 0) {
            let highlightsTable = `
                <div>
                    <h6 style="color: #00CC83">Highlights</h6>
                    <table class="table table-borderless">
                        <tbody style="color: #00CC83">
                            ${createTableRows(highlights, '#00CC83')}
                        </tbody>
                    </table>
                </div>`;
            $("#admission_insights_pipeline_body").append(highlightsTable);
        } else {
            let no_highlights = `
                <div>
                    <h6 style="color: #00CC83">Highlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            <tr><td colspan="2" style="padding: 0">No schools achieved more than ${success_threshold_perc}% of the target.</td></tr>
                        </tbody>
                    </table>
                </div>`;
            $("#admission_insights_pipeline_body").append(no_highlights);
        }

        // Generate Lowlights Table
        if (lowlights.length > 0) {
            let lowlightsTable = `
                <div style="margin-top: 1rem;">
                    <h6 style="color: #ff6262">Lowlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            ${createTableRows(lowlights, "#ff6262")}
                        </tbody>
                    </table>
                </div>`;
            $("#admission_insights_pipeline_body").append(lowlightsTable);
        } else {
            let no_lowlights = `
                <div>
                    <h6 style="color: #ff6262">Lowlights</h6>
                    <table class="table table-borderless">
                        <tbody>
                            <tr><td colspan="2" style="padding: 0">No schools achieved less than ${failure_threshold_perc}% of the target.</td></tr>
                        </tbody>
                    </table>
                </div>`;
            $("#admission_insights_pipeline_body").append(no_lowlights);
        }

        // Display the insights section
        $("#admission_insight_pipeline_div").css('display', 'block');
    }


    //Admission Target vs Converted
    function display_target_vs_converted_applications(school_list, acad_year){
        disableTableButtons();
        $("#admission_target_vs_converted_graph").html("<center>Loading Target VS Converted Applications...</center>");
        $("#admission_target_vs_converted_table").html('<tr><td colspan="5">Loading Target VS Converted Applications...</td></tr>');

        const overall_admission_array = [];

        //Call each school's data
        const mapLoop = async () => {
        const promises = await school_list.map(async (school) => {
            const num_promise = await get_target_vs_converted_application(school.school_code, school.school_domain, acad_year)
            .then((response) => {
                overall_admission_array[overall_admission_array.length] = [school.school_code, response];
                return true;
            })
            .catch((err) => {
                console.log(err);
                return false;
            });
        });
        await Promise.all(promises);

        //Arrange in a sequence
        var temp_array = [], table_array = [];
        school_list.forEach((sl) => {
            overall_admission_array.forEach((ola) => {
            if (sl.school_code == ola[0]) {
                temp_array.push(ola);
                table_array.push(ola);
                return false;
            }
            });
        });

        _construct_admission_overall_view_expand(temp_array.slice());
        _construct_admission_overall_table(table_array);
        // _construct_admission_insights(temp_array);
        };
        mapLoop();
    }

    function get_target_vs_converted_application(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "target_vs_converted_application",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_admission_overall_view_expand(input_array) {
        const display_array = [];
        display_array.push(["School Code", "Target Achieved", "Pending", "Target"]); // Add 'Target' column header

        input_array.forEach((obj) => {
        var tobe = parseInt(obj[1].target) - parseInt(obj[1].converted) <= 0 ? 0 : parseInt(obj[1].target) - parseInt(obj[1].converted);
        display_array.push([obj[0].toUpperCase(), parseInt(obj[1].converted), tobe, parseInt(obj[1].target)]); // Include 'Target' value in the array
        });

        google.charts.setOnLoadCallback(function () {
            const data = new google.visualization.arrayToDataTable(display_array);
            var options;
            var view;
            var formatPercent = new google.visualization.NumberFormat({
                pattern: "#,##0%",
            });
            options = {
                isStacked: true,
                height: 450,
                // width: 1000,
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "20%" },
                orientation: "vertical",
                hAxis: {
                    title: "Admissions",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                    bar: { groupWidth: "10%" },
                    axisTitlesPosition: "out",
                },
                vAxis: {
                    title: "Institutions",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                },
                annotations: {
                    alwaysOutside: false,
                    textStyle: {
                        fontSize: 11,
                        bold: true,
                    },
                },
                seriesType: "bars",
                pointSize: 20,
                pointShape: "star",
                series: {
                    0: {
                        color: color3,
                        areaOpacity: 0.25,
                        visibleInLegend: true,
                        annotations: {
                            stem: {
                                color: "transparent",
                                length: 0,
                            },
                        },
                    },
                    1: {
                        color: neg_color,
                        areaOpacity: 0.25,
                        visibleInLegend: true,
                        annotations: {
                            alwaysOutside: true,
                            stem: {
                                color: "transparent",
                                length: 16,
                            },
                        },
                    },
                    2: {
                        type: "line",
                        lineWidth: 0,
                        color: "#FFBF00"
                    },
                },
                legend: {
                    position: 'top', alignment: "center"
                }
            };
            
            view = new google.visualization.DataView(data);
            view.setColumns([
                0,
                1,
                {
                    sourceColumn: 1,
                    type: "string",
                    calc: function (dt, row) {
                        var conv_percent = parseInt(((parseInt(dt.getValue(row, 1)) / parseInt(dt.getValue(row, 3)))*100))
                        return " (" + conv_percent + "%)";
                    },
                    role: "annotation",
                },
                2,
                3,
            ]);
            var chartType = "ComboChart";
            var chartConstructor = google.visualization[chartType];
            var chart = new chartConstructor(document.getElementById("admission_target_vs_converted_graph"));
            chart.draw(view, options);

            var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                        <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%;"></div>
                        <span style="margin-left: 5px;">Target Achieved</span>
                        <div style="width: 15px; height: 15px; background-color: ${neg_color}; display: inline-block; border-radius: 50%; margin-left: 5%;"></div>
                        <span style="margin-left: 5px;">Pending</span>
                        <div style="width: 15px; height: 15px; background-color: #FFBF00; display: inline-block; border-radius: 50%; margin-left: 5%;"></div>
                        <span style="margin-left: 5px;">Target</span>
                    </div>`;
            $("#adm_statistics_legend_div").html(html);

            google.visualization.events.addListener(chart, "click", function (e) {
                // match the id of the axis label
                var match = e.targetID.match(/hAxis#0#label#(\d+)/);
                if (match && match.length) {
                var row = parseInt(match[1]);
                var label = data.getValue(row, 0);
                var url = "<?php echo site_url('msm/dashboard/admissions'); ?>" + "#anchor_" + label;
                window.location = url;
                }
            });
        });
    }

    function _construct_admission_overall_table(input_array) {
        enableTableButtons();
        var html = '';
        var i = 1;
        input_array.forEach(obj => {
            const percentage = parseInt(display_perc(obj[1].converted, obj[1].target));
            
            // Determine the row class based on the percentage
            let rowColor = '';
            if (percentage < 50) {
                rowColor = '#E53935';  // Class for red background
            } else if (percentage >= 80) {
                rowColor = '#238823';  // Class for green background
            }
            let difference = obj[1].target - obj[1].converted;
            let pending = difference < 0 ? 0 : difference;
            html += `
                <tr style="color: ${rowColor}">
                    <td>${i}</td>
                    <td class="text-uppercase">${obj[0]}</td>
                    <td>${obj[1].target}</td>
                    <td>${obj[1].converted} (${percentage + '%'})</td>
                    <td>${pending}</td>
                </tr>
            `;
            i++;
        });

        $('#admission_target_vs_converted_table').html(html);
    }

    // function _construct_admission_pipeline_insights(input_array) {
    //     $("#admission_insights_pipeline_body").html(''); // Clear previous content

    //     var success_threshold_perc = 20;
    //     var failure_threshold_perc = 20;

    //     // Arrays to store highlights and lowlights
    //     let highlights = [];
    //     let lowlights = [];

    //     // Process data to classify as highlights or lowlights
    //     input_array.forEach(arr => {
    //         let sc_code = arr[0];
    //         let leads = parseInt(arr[1]);
    //         let converted = parseInt(arr[3]);

    //         let achieved_rate = leads > 0 ? (converted / leads) * 100 : 0;
    //         if (achieved_rate > success_threshold_perc) {
    //             highlights.push(`<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has converted ${achieved_rate.toFixed(1)}% from the ${leads} leads.`);
    //         } else if (achieved_rate < failure_threshold_perc) {
    //             lowlights.push(`<span style="text-decoration: underline">${sc_code.toUpperCase()}</span> has converted ${achieved_rate.toFixed(1)}% from the ${leads} leads.`);
    //         }
    //     });

    //     // Helper function to create table rows with 2 entries per row
    //     function createTableRows(dataArray, color) {
    //         let rows = "";
    //         for (let i = 0; i < dataArray.length; i += 2) {
    //             let col1 = dataArray[i] || ""; // First column
    //             let col2 = dataArray[i + 1] || ""; // Second column (if exists)
    //             rows += `<tr style="padding: 0; margin: 0;"><td style="color: ${color}; padding: 0">${col1}</td><td style="color: ${color}; padding: 0">${col2}</td></tr>`;
    //         }
    //         return rows;
    //     }

    //     // Generate Highlights Table
    //     if (highlights.length > 0) {
    //         let highlightsTable = `
    //             <div>
    //                 <h6 style="color: #00CC83">Highlights</h6>
    //                 <table class="table table-borderless" style="border-spacing: 0; padding: 0; margin: 0;">
    //                     <tbody style="color: #00CC83">
    //                         ${createTableRows(highlights, '#00CC83')}
    //                     </tbody>
    //                 </table>
    //             </div>`;
    //         $("#admission_insights_pipeline_body").append(highlightsTable);
    //     } else {
    //         let no_highlights = `
    //             <div>
    //                 <h6 style="color: #00CC83">Highlights</h6>
    //                 <table class="table table-borderless" style="border-spacing: 0; padding: 0; margin: 0;">
    //                     <tbody>
    //                         <tr style="padding: 0; margin: 0;"><td colspan="2" style="padding: 0">No schools achieved more than ${success_threshold_perc}% of the target.</td></tr>
    //                     </tbody>
    //                 </table>
    //             </div>`;
    //         $("#admission_insights_pipeline_body").append(no_highlights);
    //     }

    //     // Generate Lowlights Table
    //     if (lowlights.length > 0) {
    //         let lowlightsTable = `
    //             <div style="margin-top: 1rem;">
    //                 <h6 style="color: #ff6262">Lowlights</h6>
    //                 <table class="table table-borderless" style="border-spacing: 0; padding: 0; margin: 0;">
    //                     <tbody>
    //                         ${createTableRows(lowlights, "#ff6262")}
    //                     </tbody>
    //                 </table>
    //             </div>`;
    //         $("#admission_insights_pipeline_body").append(lowlightsTable);
    //     } else {
    //         let no_lowlights = `
    //             <div>
    //                 <h6 style="color: #ff6262">Lowlights</h6>
    //                 <table class="table table-borderless" style="border-spacing: 0; padding: 0; margin: 0;">
    //                     <tbody>
    //                         <tr style="padding: 0; margin: 0;"><td colspan="2" style="padding: 0">No schools achieved less than ${failure_threshold_perc}% of the target.</td></tr>
    //                     </tbody>
    //                 </table>
    //             </div>`;
    //         $("#admission_insights_pipeline_body").append(no_lowlights);
    //     }

    //     // Display the insights section
    //     $("#admission_insight_pipeline_div").css('display', 'block');
    // }

    //Admission Statistics
    function display_admission_statistics_graph(school_list, acad_year) {
        disableTableButtons();
        $("#admission_statistics_graph").html("<center>Loading Admission Statistics...</center>");
        $("#admission_statistics_table").html("<center>Loading Admission Statistics...</center>");
        overall_admission_array.length = 0;
        const mapLoop = async () => {
            const promises = await school_list.map(async school => {
                const num_promise = await get_admission_statistics_data(school.school_code, school.school_domain, acad_year)
                .then(response => {
                    Object.keys(response.overall_statistics).forEach((k, i) => {
                        overall_statistics.push({school_code:school.school_code, acad_year: parseInt(k), data: response.overall_statistics[k]});
                        create_admission_array(response.overall_statistics[k], 2000 + parseInt(k), school, i);
                    });
                })
                .catch(err => {
                    console.log(err);
                    return false;
                });
            });
            await Promise.all(promises);
            
            var temp_array = [];
            school_list.forEach(sl => {
                overall_admission_array.forEach(ola => {
                    if (sl.school_code.toUpperCase() == ola[0]) {
                        temp_array.push(ola);
                        return false;
                    }
                });
            });
            
            _construct_admission_expand_view(temp_array.slice());
            _construct_overall_table_view(temp_array);
        }
        mapLoop();
    }

    function create_admission_array(status_arr, acad_year, school, index) {
        var arr = [["name", "number"]];
        var total_admissions = 0;
        var convert = 0;

        status_arr.forEach((obj) => {
            // Skip the "draft" status when calculating total
            if (obj.status.toLowerCase() !== "draft") {
                arr.push([obj.status, obj.total]);
                total_admissions += parseInt(obj.total);

                // Determine the "convert" status based on school code
                if (school.school_code.toLowerCase() == "itari") {
                    if (obj.status.toLowerCase() == "admit") {
                        convert = obj.total;
                    }
                } else {
                    if (obj.status.toLowerCase() == "convert") {
                        convert = obj.total;
                    }
                }
            }
        });

        // Add data to overall view
        overall_admission_array.push([school.school_code.toUpperCase(), total_admissions, convert]);
    }

    function get_admission_statistics_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "get_admission_statistics_data",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                        resolve(JSON.parse(data.response));
                    },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_admission_expand_view(input_array) {admission_statistics_graph
        input_array.unshift(["School Code", "Total Applications", "Converted"]);

        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(input_array);

            var options = {
                height: 350,
                chartArea: { left: "10%", top: "15%", width: "90%", height: "80%", bottom: "20%" },
                hAxis: {
                title: "Institutions",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                },
                vAxis: {
                title: "Total Applications",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                },
                series: {
                0: {
                    color: color1,
                    areaOpacity: 0.25,
                    visibleInLegend: true,
                    annotations: {
                    alwaysOutside: false,
                    stem: {
                        length: 30,
                    },
                    },
                },
                1: {
                    color: color3,
                    areaOpacity: 0.25,
                    visibleInLegend: true,
                    annotations: {
                    alwaysOutside: false,
                    stem: {
                        length: 10,
                    },
                    },
                },
                },
                legend:{
                    position: "top", alignment: "center"
                }
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }, 2, { sourceColumn: 2, type: "number", role: "annotation" }]);

            var chart = new google.visualization.ColumnChart(document.getElementById("admission_statistics_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Total Applications</span>
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Converted</span>
                    </div>`;
        $("#target_vs_converted_legend_div").html(html);
    }

    function _construct_overall_table_view(input_array) {
        enableTableButtons();
        var html = "";
        var i = 1;
        input_array.forEach((obj) => {
        html += `
                    <tr>
                        <td>${i}</td>
                        <td>${obj[0]}</td>
                        <td>${obj[1]}</td>
                        <td>${obj[2]}</td>
                    </tr>
                `;
        i++;
        });
        $("#admission_statistics_table").html(html);
    }

    //Admission Application Trend
    function change_dates_for_application_trend(type, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');

        const dropdown = document.getElementById('dropdownSchool_adm_appl');
        const selectedOptions = Array.from(dropdown.selectedOptions);
        
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain] = option.value.split('-'); // Extract the school code and domain
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        
        // Pass the selected schools to the function instead of the entire school_list
        display_admission_application_trend_last_7_days(selectedSchools, default_acad_year, type);
    }

    function display_schools_dropdown_adm_appl(school_list, acad_year) {       
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });

        const dropdown = document.getElementById('dropdownSchool_adm_appl');
        // If the dropdown exists, fill it with the options
        if (dropdown) {
            dropdown.innerHTML = html;
            dropdown.addEventListener('change', change_graph_adm_appl);
        }
    }
    
    function change_graph_adm_appl(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        const buttons = document.querySelectorAll(`#admission_application_date_type_inner .btn`);
        buttons.forEach(btn => btn.classList.remove('highlight'));

        $("#weekButton").addClass('highlight');
        display_admission_application_trend_last_7_days(selectedSchools, acad_year, 'week'); // Pass acad_year as a parameter
    }

    function display_admission_application_trend_last_7_days(school_list, acad_year, date_type) {
        disableTableButtons();
        $("#admission_application_trend_graph").html("<center>Loading Applications Trend...</center>");
        const temp_arr = [];
        const mapLoop = async () => {
        await Promise.all(
            [school_list[0]].map(async school => {
                try {
                    const response = await admission_application_trend(school.school_code, school.school_domain, acad_year, date_type);
                    if (response) {
                        temp_arr.push({ school_code: school.school_code, summary_data: response });
                    }
                } catch (err) {
                    console.log(err);
                }
            })
        );
        _construct_admission_application_trend(temp_arr.slice());
        _construct_admission_application_table(temp_arr.slice());
        };
        mapLoop();
    }

    function admission_application_trend(school_code, school_domain, acad_year, date_type) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    date_type: date_type,
                    api: "admission_application_trend",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_admission_application_trend(input_array) {
        // var dataArray = [["Date", "Application Count"]];
        // const countByDate = {};
        // input_array.forEach((item) => {
        //     const summaryData = item.summary_data;
        //     Object.entries(summaryData).forEach(([_, data]) => {
        //         const admissionCount = parseInt(data.admission_count);
        //         if (countByDate[data.created_on]) {
        //             countByDate[data.created_on] += admissionCount;
        //         } else {
        //             countByDate[data.created_on] = admissionCount;
        //         }
        //     });
        // });
        // Object.entries(countByDate).map(([date, count]) => dataArray.push([date, count]));

        var dataArray = [["Date"]];
        const countByDate = {};
        const schoolCodes = new Set();

        input_array.forEach((item) => {
            const schoolCode = item.school_code;
            const summaryData = item.summary_data;
            schoolCodes.add(schoolCode);
            Object.entries(summaryData).forEach(([_, data]) => {
                const admissionCount = parseInt(data.admission_count);
                const date = data.created_on;
                if (countByDate[date]) {
                    countByDate[date][schoolCode] = (countByDate[date][schoolCode] || 0) + admissionCount;
                } else {
                    countByDate[date] = { [schoolCode]: admissionCount };
                }
            });
        });

        schoolCodes.forEach((schoolCode) => {
            dataArray[0].push(schoolCode);
        });

        Object.entries(countByDate).forEach(([date, schools]) => {
            const row = [date];
            schoolCodes.forEach((schoolCode) => {
                row.push(schools[schoolCode] || 0);
            });
            dataArray.push(row);
        });

        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(dataArray);
            var options = {
                height: 350,
                curveType: 'function',
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "20%"},
                hAxis: {
                    bar: { groupWidth: "90%" },
                    minValue: 100,
                    axisTitlesPosition: "out",
                    curveType: "function",
                    legend: {
                        position: "labeled",
                    },
                },
                vAxis: {
                    title: "# Applications",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                },
                legend:{
                    position: "top", alignment: "center"
                }
            };

            var view = new google.visualization.DataView(data);           
            let columns = [0]; 
            for (let i = 1; i < data.getNumberOfColumns(); i++) {
                columns.push(i);
                columns.push({
                    sourceColumn: i,   
                    type: "number",    
                    role: "annotation" 
                });
            }
            view.setColumns(columns);

            var chart = new google.visualization.LineChart(document.getElementById("admission_application_trend_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450a3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Application Count</span>
                </div>`;
        $("#admission_application_legend_div").html(html);
        $("#admission_application_date_type").css('display', 'flex');
    }

    function _construct_admission_application_table(input_array) {
        enableTableButtons();
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const admissionCount = parseInt(data.admission_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += admissionCount;
                } else {
                    countByDate[data.created_on] = admissionCount;
                }
            });
        });
        // console.log(countByDate);
        var html = "";
        Object.entries(countByDate).forEach(([date, count]) => {
        html += `
                    <tr>
                        <td>${date}</td>
                        <td>${count}</td>
                    </tr>
                `;
        });
        $("#admission_application_table").html(html);
    }

    //Admission Activity Trend
    function display_schools_dropdown_adm_act(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_adm_act'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_adm_act);
            }
        });
    }

    function change_graph_adm_act(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_admission_activity_trend_last_7_days(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_admission_activity_trend_last_7_days(school_list, acad_year) {
        disableTableButtons();
        $("#admission_activity_trend_graph").html("<center>Loading Activity Trend...</center>");
        const temp_arr = [];
        const mapLoop = async () => {
        await Promise.all(
            [school_list[0]].map(async school => {
            try {
                const response = await admission_activity_trend(school.school_code, school.school_domain, acad_year);
                if (response) {
                    temp_arr.push({ school_code: school.school_code, summary_data: response });
                }
            } catch (err) {
                console.log(err);
            }
            })
        );
        _construct_admission_activity_trend(temp_arr.slice());
        _construct_admission_activity_table(temp_arr.slice());
        };
        mapLoop();
    }

    function admission_activity_trend(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "admission_activity_trend",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_admission_activity_trend(input_array) {
        var dataArray = [["Date", "Activity Count"]];
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const activityCount = parseInt(data.activity_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += activityCount;
                } else {
                    countByDate[data.created_on] = activityCount;
                }
            });
        });
        Object.entries(countByDate).map(([date, count]) => dataArray.push([date, count]));
        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(dataArray);

            var options = {
                height: 350,
                curveType: 'function',
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "20%"},
                hAxis: {
                title: "Date",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                curveType: "function",
                legend: {
                    position: "labeled",
                },
                },
                vAxis: {
                title: "# Activity",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                },
                legend:{
                        position: "top", alignment: "center"
                    }
            };
            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);
            var chart = new google.visualization.LineChart(document.getElementById("admission_activity_trend_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450a3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Activity Count</span>
                </div>`;
        $("#admission_activity_legend_div").html(html);
    }

    function _construct_admission_activity_table(input_array) {
        enableTableButtons();
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const activityCount = parseInt(data.activity_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += activityCount;
                } else {
                    countByDate[data.created_on] = activityCount;
                }
            });
        });
        // console.log(countByDate);
        var html = "";
        Object.entries(countByDate).forEach(([date, count]) => {
        html += `
                    <tr>
                        <td>${date}</td>
                        <td>${count}</td>
                    </tr>
                `;
        });
        $("#admission_activity_table").html(html);
    }

    //Admission Statuswise Pie
    function display_schools_dropdown_admission(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_admission'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_admission);
            }
        });
    }

    function change_graph_admission(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_admission_statuswise_trend(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_admission_statuswise_trend(school_list, acad_year){
        $("#admission_statuswise_graph").html("<center>Loading Leads Statuswise Statistics...</center>");
        var reporting_dataArray, user_dataArray;
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const data = await admission_statuswise_data(school.school_code, school.school_domain, acad_year);
                    const user_status_data = data.user_status_data;
                    const reporting_status_data = data.reporting_status_data;
                    const reporting_status_result = reporting_status_data.map(item => [
                        item.reporting_status == null ? 'Not Assigned' : capitalizeWords(item.reporting_status),
                        parseInt(item.count)
                    ]);
                    reporting_dataArray = [];
                    reporting_status_result.forEach(item => {
                        reporting_dataArray.push(item); // Add the rows dynamically
                    });

                    const user_status_result = user_status_data.map(item => [
                        item.user_status == null ? 'Not Assigned' : capitalizeWords(item.user_status),
                        parseInt(item.count)
                    ]);
                    user_dataArray = [];
                    user_status_result.forEach(item => {
                        user_dataArray.push(item); // Add the rows dynamically
                    });
                } catch (err) {
                    console.log(err);
                }
            });
            await Promise.all(promises);
            // _construct_admission_statuswise_overall_view(user_dataArray, reporting_dataArray);
            _construct_admission_statuswise_counts_table(user_dataArray, reporting_dataArray);
        };
        mapLoop();
    }

    function admission_statuswise_data(school_code, school_domain, acad_year){
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                school_code: school_code,
                school_domain: school_domain,
                acad_year: acad_year,
                api: "admission_statuswise_data",
                },
                success: function (data) {
                data = JSON.parse(data);
                if (data.status == 0) reject(data.message);
                resolve(JSON.parse(data.response));
                },
                error: function (err) {
                reject(err);
                },
            });
        });
    }

    function _construct_admission_statuswise_overall_view(user_array, reporting_array) {
        console.log(user_array, reporting_array);
        if(reporting_array.length == 1){
            $("#admission_statuswise_graph").html("<center>No Data</center>")
        }
        else{
            google.charts.setOnLoadCallback(function () {
                var data1 = new google.visualization.DataTable();
                data1.addColumn("string", "Status");
                data1.addColumn("number", "Count");
                data1.addRows(user_array); // Ensure total_array has valid data

                var data2 = new google.visualization.DataTable();
                data2.addColumn("string", "Status");
                data2.addColumn("number", "Count");
                data2.addRows(reporting_array);

                var options1 = {
                    title: 'Granular Status Graph',
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1, offset: 0.2 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                        5: { color: color1},
                        6: { color: color2},
                        7: { color: color3}
                    },
                    sliceVisibilityThreshold: 0,
                    legend: { position: 'top', alignment: "center" },
                };

                var options2 = {
                    title: 'Overall Status Graph',
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                        5: { color: color1},
                        6: { color: color2},
                        7: { color: color3}
                    },
                    legend: { position: 'top', alignment: "center" },
                };

                var chart1 = new google.visualization.PieChart(document.getElementById("admission_statuswise_user_graph"));
                chart1.draw(data1, options1);

                var chart2 = new google.visualization.PieChart(document.getElementById("admission_statuswise_reporting_graph"));
                chart2.draw(data2, options2);
            });
        }
    }

    function _construct_admission_statuswise_counts_table(user_array, reporting_array){
        var html = '';
        var i = 1;
        user_array.forEach(obj => {
            html += `
                <tr>
                    <td class="text-uppercase text-sm">${i}</td>
                    <td class="text-uppercase text-sm" class="text-uppercase">${obj[0]}</td>
                    <td class="text-sm">${obj[1]}</td>
                </tr>
            `;
            i++;
        });
        $("#admission_statuswise_user_table").html(html);

        // Define the custom order
        const customOrder = ['Draft', 'In Progress', 'Convert', 'Closed', 'Invalid/Duplicate']; // Replace with your custom order

        // Sort the reporting_array based on the custom order
        reporting_array.sort((a, b) => {
            const indexA = customOrder.indexOf(a[0]);
            const indexB = customOrder.indexOf(b[0]);

            // Items not in customOrder will be sorted to the end
            return (indexA === -1 ? Infinity : indexA) - (indexB === -1 ? Infinity : indexB);
        });

        // Generate the HTML table after sorting
        var html = '';
        var i = 1;
        reporting_array.forEach(obj => {
            html += `
                <tr>
                    <td class="text-uppercase text-sm">${i}</td>
                    <td class="text-uppercase text-sm">${obj[0]}</td>
                    <td class="text-sm">${obj[1]}</td>
                </tr>
            `;
            i++;
        });

        $("#admission_statuswise_reporting_table").html(html);

    }

    //Leads Conversion Graph
    function display_leads_conversion_graph(school_list, acad_year) {
        disableTableButtons();
        $('#leads_conversion_statistics_graph').html('<center>Loading Lead Management Statistics...</center>');
        overall_leads_array.length = 0;
        const mapLoop = async () => {
            const promises = await school_list.map(async (school) => {
                const num_promise = await get_leads_conversion_statistics(school.school_code, school.school_domain, acad_year)
                .then((response) => {
                    Object.keys(response.overall_status).forEach((k, i) => {
                        overall_statistics.push({ school_code: school.school_code.toUpperCase(), acad_year: parseInt(k), data: response.overall_status[k] });
                        create_leads_array(response.overall_status[k], 2000 + parseInt(k), school, i);
                    });
                })
                .catch((err) => {
                    console.log(err);
                    return false;
                });
            });
            await Promise.all(promises);
            var temp_array = [];
            school_list.forEach((sl) => {
                overall_leads_array.forEach((ola) => {
                if (sl.school_code.toUpperCase() == ola[0]) {
                    temp_array.push(ola);
                    return false;
                }
                });
            });
            _construct_lead_conversion_view(temp_array.slice());
            _construct_lead_conversion_table(temp_array.slice());
            _construct_lead_insights(temp_array.slice());
        };
        mapLoop();
    }

    function create_leads_array(status_arr, acad_year, school, index) {
        var arr = [["name", "number"]];
        var total_leads = 0;

        var convert = 0;
        status_arr.forEach((obj, index) => {
            arr.push([obj.status, obj.total]);
            total_leads += parseInt(obj.total);
            if (obj.status == "convert") {
                convert = obj.total;
            }
        });
        overall_leads_array.push([school.school_code.toUpperCase(), total_leads, convert]);
    }

    function get_leads_conversion_statistics(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "leads_conversion_statistics",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_lead_conversion_view(input_array) {
        input_array.unshift(["School Code", "Total Applications", "Converted"]);
        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(input_array);

            var options = {
                height: 400,
                chartArea: { left: "10%", top: "10%", width: "80%", height: "80%", bottom: "15%" },
                hAxis: {
                title: "Institutions",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                },
                vAxis: {
                    title: "# Total Leads",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                    viewWindow : {
                        max: 1000
                    }
                },
                series: {
                0: {
                    color: color1,
                    areaOpacity: 0.25,
                    visibleInLegend: true,
                    annotations: {
                    alwaysOutside: false,
                    },
                },
                1: {
                    color: pos_color,
                    areaOpacity: 0.25,
                    visibleInLegend: true,
                    annotations: {
                    alwaysOutside: false,
                    stem: {
                        length: 10,
                    },
                    },
                },
                },
                legend: {
                position: "top", alignment: "center",
                },
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }, 2, { sourceColumn: 2, type: "number", role: "annotation" }]);

            var chart = new google.visualization.ColumnChart(document.getElementById("leads_conversion_statistics_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Total Leads</span>
                    <div style="width: 15px; height: 15px; background-color: ${pos_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Converted</span>
                    </div>`;
        $("#lead_conversion_statistcs_legend_div").html(html);
    }

    function _construct_lead_conversion_table(input_array) {
        enableTableButtons();
        var html = '';
        var i = 1;
        input_array.forEach(obj => {
            html += `
                <tr>
                    <td class="text-uppercase align-middle text-center text-sm">${i}</td>
                    <td class="text-uppercase align-middle text-center text-sm" class="text-uppercase">${obj[0]}</td>
                    <td class="align-middle text-center text-sm">${obj[1]}</td>
                    <td class="align-middle text-center text-sm">${obj[2]}</td>
                </tr>
            `;
            i++;
        });
        $("#leads_conversion_table").html(html);
    }

    function _construct_lead_insights(input_array){
        $("#leads_insights_body").html('');
        var success_threshold_perc = 80;
        var failure_threshold_perc = 50;

        //Adding positive insights for admissions
        var positive_leads_str = "";
        input_array.forEach(arr =>{
            sc_code = arr[0];
            total_leads = parseInt(arr[1]);
            converted = parseInt(arr[2]);

            var achieved_rate = parseInt(converted) / parseInt(total_leads) * 100;
            if (achieved_rate > success_threshold_perc) {
                var difference = (achieved_rate - success_threshold_perc).toFixed(2);
                if (positive_leads_str != '') {
                    positive_leads_str += ", ";
                }
                positive_leads_str += sc_code.toUpperCase();
                positive_admissions_str += ` has converted ${difference}% Leads more than the given target of ${success_threshold_perc}%`;
            }
        });
        if (positive_leads_str != '') {
            $("#leads_insights_body").append(make_insight_html(positive_admissions_str, 'positive'));
        }
        else{
            text = `No schools have achieved more than ${success_threshold_perc}% of the target`;
            $("#leads_insights_body").append(make_insight_html(text, 'neutral'));
        }

        //Adding negative insights for admissions
        var negative_leads_str = "";
        input_array.forEach(arr =>{
            sc_code = arr[0];
            total_leads = parseInt(arr[1]);
            converted = parseInt(arr[2]);

            var achieved_rate = parseInt(converted) / parseInt(total_leads) * 100;
            if (achieved_rate < failure_threshold_perc) {
                var difference = (failure_threshold_perc - achieved_rate).toFixed(2);
                if (negative_leads_str != '') {
                    negative_leads_str += ", ";
                }
                negative_leads_str += sc_code.toUpperCase();
                negative_leads_str += ` has converted ${difference}% Leads less than the given target of ${failure_threshold_perc}%`;
            }
        });
        if (negative_leads_str != '') {
            $("#leads_insights_body").append(make_insight_html(negative_leads_str, 'negative'));
        }
        else{
            text = `No schools have achieved more than ${failure_threshold_perc}% of the target`;
            $("#leads_insights_body").append(make_insight_html(text, 'neutral'));
        }
        $("#leads_insight_div").css('display', 'block');
    }

    //Leads Enquiry Trend
    function change_dates_for_leads_enquiry(type, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');

        const dropdown = document.getElementById('dropdownSchool_lead_eq');
        const selectedOptions = Array.from(dropdown.selectedOptions);
        
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain] = option.value.split('-'); // Extract the school code and domain
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        console.log(selectedSchools);
        
        
        // Pass the selected schools to the function instead of the entire school_list
        display_lead_enquiry_trend_last_7_days(selectedSchools, default_acad_year, type);
    }

    function display_schools_dropdown_lead_eq(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_lead_eq'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_lead_eq);
            }
        });
    }

    function change_graph_lead_eq(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_lead_enquiry_trend_last_7_days(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_lead_enquiry_trend_last_7_days(school_list, acad_year, date_type) {
        disableTableButtons();
        $("#leads_enquiry_trend_graph").html("<center>Loading Leads Enquiry Trend...</center>");
        $("#weekButton_leads_eq")
        const temp_arr = [];
        const mapLoop = async () => {
        await Promise.all(
            [school_list[0]].map(async school => {
                try {
                    const response = await get_leads_enquiry_trend(school.school_code, school.school_domain, acad_year, date_type);
                    if (response) {
                        temp_arr.push({ school_code: school.school_code, summary_data: response });
                    }
                } catch (err) {
                    console.log(err);
                }
            })
        );
            _construct_lead_enquiry_trend(temp_arr.slice());
            _construct_lead_enquiry_table(temp_arr.slice());
        };
        mapLoop();
    }

    function get_leads_enquiry_trend(school_code, school_domain, acad_year, date_type) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    date_type: date_type,
                    api: "leads_enquiry_trend",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_lead_enquiry_trend(input_array) {
        var dataArray = [["Date", "Lead Count"]];
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const leadsCount = parseInt(data.enquiry_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += leadsCount;
                } else {
                    countByDate[data.created_on] = leadsCount;
                }
            });
        });
        Object.entries(countByDate).map(([date, count]) => dataArray.push([date, count]));
        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(dataArray);

            var options = {
                height: 300,
                curveType: 'function',
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                hAxis: {
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                curveType: "function",
                },
                vAxis: {
                title: "# Leads",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                },
                legend:{
                        position: "top", alignment: "center"
                    }
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);

            var chart = new google.visualization.LineChart(document.getElementById("leads_enquiry_trend_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450a3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Leads Count</span>
                </div>`;
        $("#leads_enquiry_legend_div").html(html);
        $("#leads_enquiry_date_type").css('display', 'flex');
    }

    function _construct_lead_enquiry_table(input_array) {
        enableTableButtons();
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const leadsCount = parseInt(data.enquiry_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += leadsCount;
                } else {
                    countByDate[data.created_on] = leadsCount;
                }
            });
        });
        var html = "";
        Object.entries(countByDate).forEach(([date, count]) => {
        html += `
                    <tr>
                        <td>${date}</td>
                        <td>${count}</td>
                    </tr>
                `;
        });
        $("#leads_enquiry_table").html(html);
    }

    //Leads Enquiry Comparision Trend
    function display_schools_dropdown_lead_eq_comparision(school_list) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        
        // Define both dropdown IDs
        const dropdowns = ['dropdownSchool_lead_comp'];
        
        // Populate both dropdowns
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            
            // Check if the dropdown exists (in case permissions hide it)
            if (dropdown) {
                dropdown.innerHTML = html;
            }
        });
    }

    function load_schools_for_comparision() {
        const dropdownA = document.getElementById('dropdownSchool_lead_comp');
        
        // Get selected options from both dropdowns
        const selectedOptionsA = Array.from(dropdownA.selectedOptions);
        
        // Combine selections from both dropdowns
        const selectedSchools = [...selectedOptionsA].map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-');
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        
        // Pass the combined selected schools and academic year
        display_lead_enquiry_comparision_trend_last_7_days(selectedSchools);
    }

    function display_lead_enquiry_comparision_trend_last_7_days(school_list, acad_year_array = []) {
        const acad_year = document.getElementById('dropdownSchool_lead_comp_ay');
        acad_year_array = Array.from(acad_year.selectedOptions);
        acad_year_array = acad_year_array.map(option => option.value);
        if(acad_year_array.length == 0){
            acad_year_array = ['25', '24', '23', '22'];
        }
        
        disableTableButtons();
        $("#leads_enquiry_comparision_trend_graph").html("<center>Loading Leads Enquiry Trend...</center>");
        const temp_arr = [];
        const mapLoop = async () => {
        await Promise.all(
            [school_list[0]].map(async school => {
                try {
                    const response = await get_leads_enquiry_comparision_trend(school.school_code, school.school_domain, acad_year_array);
                    if (response) {
                        console.log(response);
                        
                        temp_arr.push({ school_code: school.school_code, summary_data: response });
                    }
                } catch (err) {
                    console.log(err);
                }
            })
        );
            _construct_lead_enquiry_comparision_trend(temp_arr.slice());
            _construct_lead_enquiry_comparision_table(temp_arr.slice());
        };
        mapLoop();
    }

    function get_leads_enquiry_comparision_trend(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: JSON.stringify(acad_year),
                    api: "leads_enquiry_comparision_trend",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_lead_enquiry_comparision_trend(input_array) {
        console.log(input_array);
        
        const years = Object.keys(input_array[0].summary_data);
        console.log(years);
        
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // Create the result array
        const result = [];
        result.push(['Month', ...years]); // Header row with years

        // Populate counts for each month
        months.forEach(month => {
            const row = [month]; // Start the row with the month
            years.forEach(year => {
                const schoolData = input_array[0].summary_data[year];
                // Get the enquiry count for the current month, or default to '0'
                const count = schoolData[month] ? parseInt(schoolData[month].enquiry_count) : '0';
                row.push(count);
            });
            result.push(row); // Add the row to the result
        });

        // Print the result
        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(result);

            var options = {
                height: 300,
                curveType: 'function',
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                hAxis: {
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                curveType: "function",
                legend: {
                    position: "top", alignment: "center",
                },
                },
                vAxis: {
                title: "# Leads",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                },
                legend:{
                        position: "top", alignment: "center"
                    }
            };

            var numColumns = data.getNumberOfColumns(); // Get the total number of columns
            var viewColumns = [];

            // Loop through the columns to create the view configuration
            for (var i = 0; i < numColumns; i++) {
                viewColumns.push(i); // Add original column
                if( i > 0 )
                    viewColumns.push({ sourceColumn: i, type: "number", role: "annotation" }); // Add annotation column
            }

            // Create the DataView with the dynamically generated columns
            var view = new google.visualization.DataView(data);
            view.setColumns(viewColumns);

            var chart = new google.visualization.LineChart(document.getElementById("leads_enquiry_comparision_trend_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450a3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Leads Count</span>
                </div>`;
        $("#leads_enquiry_legend_div").html(html);
    }

    function _construct_lead_enquiry_comparision_table(input_array) {
        enableTableButtons();
        console.log(input_array);
        
        const years = Object.keys(input_array[0].summary_data);
        console.log(years);
        
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // Create the result array
        const result = [];
        result.push(['Month', ...years]); // Header row with years

        // Populate counts for each month
        months.forEach(month => {
            const row = [month]; // Start the row with the month
            years.forEach(year => {
                const schoolData = input_array[0].summary_data[year];
                // Get the enquiry count for the current month, or default to '0'
                const count = schoolData[month] ? parseInt(schoolData[month].enquiry_count) : '0';
                row.push(count);
            });
            result.push(row); // Add the row to the result
        });
        var html = '<table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;"><thead style="color: #0F256E"><tr>';

        // Create table headers
        result[0].forEach(header => {
            html += `<th>${header}</th>`;
        });
        html += '</tr></thead><tbody>';

        // Create table rows
        for (let i = 1; i < result.length; i++) { // Start from 1 to skip header
            html += '<tr>';
            result[i].forEach(cell => {
                html += `<td>${cell}</td>`;
            });
            html += '</tr>';
        }

        html += '</tbody></table>'; 
        console.log(html);
        
        $("#leads_enquiry_comparision_table_card").html(html);
    }

    //Leads Activity Trend
    function display_schools_dropdown_lead_act(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_lead_act'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_lead_act);
            }
        });
    }

    function change_graph_lead_act(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_lead_activity_trend_last_7_days(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_lead_activity_trend_last_7_days(school_list, acad_year) {
        disableTableButtons();
        $("#leads_activity_trend_graph").html("<center>Loading Applications Trend...</center>");
        const temp_arr = [];
        const mapLoop = async () => {
        await Promise.all(
            school_list.map(async (school) => {
            try {
                const response = await leads_activity_trend(school.school_code, school.school_domain, acad_year);
                if (response) {
                temp_arr.push({ school_code: school.school_code, summary_data: response });
                }
            } catch (err) {
                console.log(err);
            }
            })
        );
        _construct_lead_activity_trend(temp_arr.slice());
        _construct_lead_activity_table(temp_arr.slice());
        };
        mapLoop();
    }

    function leads_activity_trend(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "leads_activity_trend",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_lead_activity_trend(input_array) {
        disableTableButtons();
        var dataArray = [["Date", "Activity Count"]];
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const activityCount = parseInt(data.activity_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += activityCount;
                } else {
                    countByDate[data.created_on] = activityCount;
                }
            });
        });
        Object.entries(countByDate).map(([date, count]) => dataArray.push([date, count]));
        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(dataArray);

            var options = {
                height: 300,
                curveType: 'function',
                chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                hAxis: {
                    title: "Date",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                bar: { groupWidth: "90%" },
                minValue: 100,
                axisTitlesPosition: "out",
                curveType: "function",
                legend: {
                    position: "top", alignment: "center",
                },
                },
                vAxis: {
                title: "# Activity",
                titleTextStyle: {
                    color: "#1450A3",
                    fontName: "Arial",
                    fontSize: "16",
                    bold: true,
                    italic: false,
                },
                },
                legend:{
                    position: "top", alignment: "center"
                }
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);

            var chart = new google.visualization.LineChart(document.getElementById("leads_activity_trend_graph"));
            chart.draw(view, options);
        });
        enableTableButtons();

        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450a3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Leads Activity Count</span>
                </div>`;
        $("#leads_activity_legend_div").html(html);
    }

    function _construct_lead_activity_table(input_array) {
        enableTableButtons();
        const countByDate = {};
        input_array.forEach((item) => {
            const summaryData = item.summary_data;
            Object.entries(summaryData).forEach(([_, data]) => {
                const activityCount = parseInt(data.activity_count);
                if (countByDate[data.created_on]) {
                    countByDate[data.created_on] += activityCount;
                } else {
                    countByDate[data.created_on] = activityCount;
                }
            });
        });
        var html = "";
        Object.entries(countByDate).forEach(([date, count]) => {
        html += `
                    <tr>
                        <td>${date}</td>
                        <td>${count}</td>
                    </tr>
                `;
        });
        $("#leads_activity_table").html(html);
    }

    //Leads Statuswise Pie
    function display_schools_dropdown_leads(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_leads'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_leads);
            }
        });
    }

    function change_graph_leads(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_leads_statuswise_trend(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_leads_statuswise_trend(school_list, acad_year){
        $("#leads_statuswise_graph").html("<center>Loading Leads Statuswise Statistics...</center>");
        var dataArray, source_dataArray;
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const data = await leads_statuswise_data(school.school_code, school.school_domain, acad_year);
                    const user_status_data = data.user_status_data;
                    const reporting_status_data = data.reporting_status_data;

                    const reporting_status_result = reporting_status_data.map(item => [
                        item.reporting_status == null ? 'Not Assigned' : capitalizeWords(item.reporting_status),
                        parseInt(item.count)
                    ]);
                    reporting_dataArray = [];
                    reporting_status_result.forEach(item => {
                        reporting_dataArray.push(item); // Add the rows dynamically
                    });

                    const user_status_result = user_status_data.map(item => [
                        item.user_status == null ? 'Not Assigned' : capitalizeWords(item.user_status),
                        parseInt(item.count)
                    ]);
                    user_dataArray = [];
                    user_status_result.forEach(item => {
                        user_dataArray.push(item); // Add the rows dynamically
                    });
                } catch (err) {
                    console.log(err);
                }
            });
            await Promise.all(promises);
            // _construct_leads_statuswise_overall_view(user_dataArray, reporting_dataArray);
            _construct_leads_statuswise_counts_table(user_dataArray, reporting_dataArray);
        };
        mapLoop();
    }

    function leads_statuswise_data(school_code, school_domain, acad_year){
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                school_code: school_code,
                school_domain: school_domain,
                acad_year: acad_year,
                api: "leads_statuswise_data",
                },
                success: function (data) {
                data = JSON.parse(data);
                if (data.status == 0) reject(data.message);
                resolve(JSON.parse(data.response));
                },
                error: function (err) {
                reject(err);
                },
            });
        });
    }

    function _construct_leads_statuswise_overall_view(user_array, reporting_array) {
        console.log(user_array, reporting_array);
        if(reporting_array.length == 1){
            $("#leads_statuswise_graph").html("<center>No Data</center>")
        }
        else{
            google.charts.setOnLoadCallback(function () {
                var data1 = new google.visualization.DataTable();
                data1.addColumn("string", "Status");
                data1.addColumn("number", "Count");
                data1.addRows(user_array); // Ensure total_array has valid data

                var data2 = new google.visualization.DataTable();
                data2.addColumn("string", "Status");
                data2.addColumn("number", "Count");
                data2.addRows(reporting_array);

                var options1 = {
                    title: 'Granular Status Graph',
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                        5: { color: color1},
                        6: { color: color2},
                        7: { color: color3}
                    },
                    sliceVisibilityThreshold: 0,
                    legend: { position: 'top', alignment: "center" },
                };

                var options2 = {
                    title: 'Overall Status Graph',
                    pieSliceText: 'value',
                    height: 400,
                    chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
                    slices: {
                        0: { color: pie_color1 },
                        1: { color: pie_color2 },
                        2: { color: pie_color3 },
                        3: { color: pie_color4 },
                        4: { color: pie_color5 },
                        5: { color: color1},
                        6: { color: color2},
                        7: { color: color3}
                    },
                    legend: { position: 'top', alignment: "center" },
                };

                var chart1 = new google.visualization.PieChart(document.getElementById("leads_statuswise_user_graph"));
                chart1.draw(data1, options1);

                var chart2 = new google.visualization.PieChart(document.getElementById("leads_statuswise_reporting_graph"));
                chart2.draw(data2, options2);
            });
        }
    }

    function _construct_leads_statuswise_counts_table(user_array, reporting_array){
        var html = '';
        var i = 1;
        var total_user_status_count = 0;
        user_array.forEach(obj => {
            total_user_status_count += obj[1]
            html += `
                <tr>
                    <td class="text-uppercase text-sm">${i}</td>
                    <td class="text-uppercase text-sm" class="text-uppercase">${obj[0]}</td>
                    <td class="text-sm">${obj[1]}</td>
                </tr>
            `;
            i++;
        });
        html += `
            <tr>
                <td class="text-uppercase text-sm"><strong>${i}</strong></td>
                <td class="text-uppercase text-sm"><strong>TOTAL</strong></td>
                <td class="text-sm"><strong>${total_user_status_count}</strong></td>
            </tr>
        `;
        $("#leads_statuswise_user_table").html(html);

        const customOrder = ['In Progress', 'Draft', 'Convert', 'Closed', 'Invalid/Duplicate']; // Replace with your custom order

        // Sort the reporting_array based on the custom order
        reporting_array.sort((a, b) => {
            const indexA = customOrder.indexOf(a[0]);
            const indexB = customOrder.indexOf(b[0]);

            // Items not in customOrder will be sorted to the end
            return (indexA === -1 ? Infinity : indexA) - (indexB === -1 ? Infinity : indexB);
        });
        var html = '';
        var i = 1;
        var total_reporting_status_count = 0;
        reporting_array.forEach(obj => {
            total_reporting_status_count += obj[1];
            html += `
                <tr>
                    <td class="text-uppercase text-sm">${i}</td>
                    <td class="text-uppercase text-sm" class="text-uppercase">${obj[0]}</td>
                    <td class="text-sm">${obj[1]}</td>
                </tr>
            `;
            i++;
        });
        html += `
            <tr>
                <td class="text-uppercase text-sm"><strong>${i}</strong></td>
                <td class="text-uppercase text-sm"><strong>TOTAL</strong></td>
                <td class="text-sm"><strong>${total_reporting_status_count}</strong></td>
            </tr>
        `;
        $("#leads_statuswise_reporting_table").html(html);
    }

        //Leads Statuswise Pie
    function display_schools_dropdown_leads_source(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}-${acad_year}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_leads_source'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
            if (dropdown){
                dropdown.innerHTML = html;
                dropdown.addEventListener('change', change_graph_leads_source);
            }
        });
    }

    function change_graph_leads_source(event) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        let acad_year; // Declare acad_year outside the map function
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain, year] = option.value.split('-'); // Extract acad_year here
            acad_year = year; // Assign to acad_year
            return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_leads_sourcewise_trend(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_leads_sourcewise_trend(school_list, acad_year){
        $("#leads_sourcewise_table").html(`<tr><td colspan="5" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading Leads Sourcewise Table...</td></tr>`);
        var source_dataArray;
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const data = await leads_sourcewise_data(school.school_code, school.school_domain, acad_year);
                    const source_status_result = {};
                    data.forEach(item => {                        
                        let key = (item.source_status == null || item.source_status.trim() === "") ? "Unknown" : capitalizeWords(item.source_status);
                        if (!source_status_result[key]) {
                            source_status_result[key] = {
                            total_count: 0,
                            converted_count: 0
                            };
                        }
                        source_status_result[key].total_count += parseInt(item.total_count || item.count || 0);
                        source_status_result[key].converted_count += parseInt(item.converted_count || 0);
                    });                    
                    source_dataArray = Object.entries(source_status_result).map(([key, value]) => ({
                        source_status: key,
                        total_count: value.total_count,
                        converted_count: value.converted_count,
                    }));
                } catch (err) {
                    console.log(err);
                }
            });
            await Promise.all(promises);
            _construct_leads_sourcewise_counts_table(source_dataArray);
        };
        mapLoop();
    }

    function leads_sourcewise_data(school_code, school_domain, acad_year){
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                school_code: school_code,
                school_domain: school_domain,
                acad_year: acad_year,
                api: "leads_sourcewise_data",
                },
                success: function (data) {
                data = JSON.parse(data);
                if (data.source == 0) reject(data.message);
                resolve(JSON.parse(data.response));
                },
                error: function (err) {
                reject(err);
                },
            });
        });
    }

    function _construct_leads_sourcewise_counts_table(source_array) {
        let html = '';
        let i = 1;
        let total_source_status_count = 0;
        let total_converted_count = 0;

        source_array.forEach(obj => {
            total_source_status_count += obj.total_count;
            total_converted_count += obj.converted_count;
            html += `
                <tr>
                    <td class="text-uppercase text-sm">${i}</td>
                    <td class="text-uppercase text-sm">${obj.source_status}</td>
                    <td class="text-sm">${obj.total_count !=0 ? obj.total_count : '-'}</td>
                    <td class="text-sm">${obj.converted_count}</td>
                    <td class="text-sm"> ${obj.total_count === 0 ? '-' : ((obj.converted_count / obj.total_count) * 100).toFixed(2) + '%'}</td>
                </tr>
            `;
            i++;
        });

        const total_percentage = total_source_status_count > 0 ? ((total_converted_count / total_source_status_count) * 100).toFixed(2) : "0.00";
        html += `
            <tr>
                <td class="text-uppercase text-sm"><strong>${i}</strong></td>
                <td class="text-uppercase text-sm"><strong>TOTAL</strong></td>
                <td class="text-sm"><strong>${total_source_status_count}</strong></td>
                <td class="text-sm"><strong>${total_converted_count}</strong></td>
                <td class="text-sm"><strong>${total_percentage}%</strong></td>
            </tr>
        `;
        $("#leads_sourcewise_table").html(html);
    }

    function capitalizeWords(str) {
        return str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }

    function display_perc(numer, denom, show_number_only = false) {
        const percentageValue = parseFloat(numer) / parseFloat(denom) * 100;
        const roundedValue = percentageValue.toFixed(0);
        if (show_number_only)
        return roundedValue;
        else
        return roundedValue + '%';
    }

    function make_insight_html(text, mode) {
        var insight_html = '';

        switch (mode) {
            case 'positive':
                bg_color = color3;
                break;
            case 'negative':
                bg_color = neg_color;
                break;
            default:
                bg_color = 'grey';
        }
        insight_html += `
            <div class="row d-flex justify-content-lg-start justify-content-center p-0">
                <div class="col-12">
                    <span style="color: ${bg_color}">${text}</span>
                </div>
            </div>
        `;
        return insight_html;
    }

    // Student TC Data
    function display_schools_dropdown_student_tc(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });

        const dropdowns = ['dropdownSchool_student_tc'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            // If relevant permission is not there, then the dropdown object is not available. Hence adding a check.
            if (dropdown) {
                dropdown.innerHTML = html;
                // Use an anonymous function to pass acad_year to change_graph_student_tc
                dropdown.addEventListener('change', (event) => change_graph_student_tc(event, acad_year));
            }
        });
    }

    function change_graph_student_tc(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain] = option.value.split('-');
            return { school_code: schoolCode, school_domain: schoolDomain };
        });

        // Pass selectedSchools and acad_year to the display function
        display_student_tc_data(selectedSchools, acad_year);
    }

    function display_student_tc_data(school_list, acad_year){
        $("#student_tc_table").html("<center>Loading Student TC Statistics...</center>");
        var tc_data = [];

        // Function to process each school's data
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async (school) => {
                try {
                    const response = await get_student_tc_list(school.school_code, school.school_domain, acad_year);
                    if (response) {
                        tc_data.push(response)
                    }
                } catch (err) {
                    console.error(err);
                }
            });
            // Wait for all promises to resolve
            await Promise.all(promises);            
            _construct_student_tc_table(tc_data, acad_year)
        };
        
        mapLoop();
    }

    function get_student_tc_list(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    api: "student_tc_list",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }

    function _construct_student_tc_table(input_array, acad_year) {
        let html = `
                <table class="table-sm table-bordered" width="95%" style="margin: 1.5rem; text-align: center;" id="student_count_prediction_table">
                <thead style="color: #0F256E">
                    <tr>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">#</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">Class Name</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year)} <br>Total Students</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year)} <br>Planned Exits</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year) + 1} <br>Opening</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year) + 1} <br>New Admissions</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year) + 1} <br>Current Strength</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year) + 1} <br>Capacity</th>
                    <th class="font-weight-bolder text-m text-uppercase align-center opacity-7">20${parseInt(acad_year) + 1} <br>Vacancy</th>
                    </tr>
                </thead>
                <tbody>
            `;

            var i = 0;
            input_array[0].forEach((row, index) => {
                i = i + 1;
                const isLastRow = index === input_array[0].length - 1; // Check if this is the last row
                html += `
                <tr style="${isLastRow ? 'font-weight: bold;' : ''}">
                    <td class="text-sm">${i}</td>
                    <td class="text-uppercase text-sm">${row.class_name}</td>
                    <td class="text-sm">${row.total_students}</td>
                    <td class="text-sm">${row.terminate_students}</td>
                    <td class="text-sm">${row.this_year_close}</td>
                    <td class="text-sm">${row.new_students}</td>
                    <td class="text-sm">${row.next_year_strength}</td>
                    <td class="text-sm">${row.total_strength}</td>
                    <td class="text-sm">${row.vacancy}</td>
                </tr>
                `;
            });

            html += `
                </tbody>
                </table>
            `;


        // Append the table to a specific container in the HTML
        document.getElementById("student_tc_table").innerHTML = html;
    }

    //Back To Home
    function adm_to_home() {
        $(".admission_cards_wrapper").css("display", "none");
        $(".overview_cards_wrapper").css("display", "flex");
        $(".admission_tb_btn").css("display", "none");
    }

    function disableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.setAttribute('disabled', true);
        }
    }

    function export_to_excel(){
        var wb = XLSX.utils.book_new();
        wb.Props = {
            Title: "Student Count Prediction",
            Subject: "Student Count Prediction Report",
            Author: "NextElement",
            CreatedDate: new Date()
        };

        wb.SheetNames.push("Student Count Prediction");
        var ws_school = XLSX.utils.table_to_sheet(document.getElementById('student_count_prediction_table'));
        wb.Sheets["Student Count Prediction"] = ws_school;

        var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
        saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), "Student Count Predictions.xlsx");
    }

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    
    }

    // Function to enable table buttons
    function enableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.removeAttribute('disabled');
        }
    }
</script>
