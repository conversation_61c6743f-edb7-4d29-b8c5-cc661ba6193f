<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance</a></li>
  <li>Student Attendance</li>
</ul>
<?php //echo '<pre>'; print_r($class_section); die(); ?>
<?php date_default_timezone_set('Asia/Kolkata'); ?>

<div class="col-lg-12">
<div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Student Attendance

          </h3>
        </div>
      </div>
    </div>
    <form id="form_data" method="post">
  <div class="card-body pt-1" style="padding: 10px 0px">
      <div class="card-body pt-1 d-flex flex-wrap" style="padding:0px;">
        <div class="col-md-6" style="margin-bottom: 10px;">
          <div class="form-group">
            <label for="pwd">Section</label>
            <select name="classsecID" id="sectionid" class="form-control" required="">
                  <option value="">Select Section</option>
                  <?php foreach ($class_section as $key => $cls_section) { ?>
                      <option  value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>"><?= $cls_section->class_name . '' . $cls_section->section_name ?></option>
                  <?php } ?>
              </select>
          </div>
        </div>
        <div class="col-md-6" style="margin-bottom: 10px;">
          <div class="form-group">
            <label for="pwd">Date</label>
            <input type="date" class="form-control datePicker" id="select_date" name="selected_date"  placeholder="Select Date" required="" value="<?php echo date('Y-m-d'); ?>"  value="<?php echo date('Y-m-d'); ?>"
              <?php if (!$this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ATTENDANCE_FOR_PREVIOUS_DATES')): ?>
                min="<?php echo date('Y-m-d'); ?>"
              <?php endif; ?>
              max="<?php echo date('Y-m-d'); ?>">
          </div>
        </div>
        <div class="col-md-12 text-center">
          <button type="button" class="btn btn-primary" onclick="takeAttendance()">Take Attendance</button>
        </div>
      </div>
  </div>
        </form>
                  <div id="displayattendance" style="margin-top:2rem" >
                  </div>

                  <div id="nodataattendance" style="margin-top:2rem" >
                  </div>


        </div>


</div>
</div>

<!-- Code For Sending Notification -->
<div class="modal fade" id="notify-modal" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Notify Absentees & Late-comers by Sms/Notification</h4>
            </div>
            <div class="modal-body">
                <form id="student-messages-form">
                    <!-- <input type="hidden" id="notify-attendance-master-id" value="0"> -->
                    <div class="form-group" style="font-size: 16px;">
                        <label class="radio-inline" for="notification">
                            <input style="height: 18px; width: 18px;" type="radio" name="communication_mode"
                                id="notification" value="notification" <?php echo ($notification_mode == 'notif-only' || $notification_mode == 'both') ? 'checked=""' : '' ?>>&nbsp;Notification 
                        </label>
                        <label class="radio-inline" for="sms">
                            <input style="height: 18px; width: 18px;" type="radio" name="communication_mode" id="sms"
                                value="sms" <?php echo ($notification_mode == 'sms-only') ? 'checked=""' : '' ?>>&nbsp;
                                SMS
                        </label>
                        <select id="text_send_to" class="form-control" name="text_send_to" style="margin: 0 0 0 15px;width: 30%;display:none">
                            <option value="">Select</option>
                            <option value="Father">Father</option>
                            <option value="Mother">Mother</option>
                            <option value="Both">Both</option>
                            <option value="preferred">Preferred Parent</option>
                        </select>
                    </div>
                    <div id="notify-content" style="overflow-y:auto;max-height:450px;">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button style="width: 120px;" type="button" class="btn btn-warning my-0" data-dismiss="modal" id="cancelButton">Cancel</button>
                <button id="confirmBtn" onclick="send_messages()" style="width: 120px;" type="button" class="btn btn-primary my-0">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- History Modal Start -->

<div id="view-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog" style="width:90%;margin:auto;top:10%;">
     <div class="modal-content">

        <div class="modal-header">
           <h4 class="modal-title" style="font-size: 1.2rem;">Attendance History</h4>
           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        </div>

        <div class="modal-body mobile">
           <div id="modal-loader" style="display: none; text-align: center;">
           <!-- ajax loader -->
           <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">
           </div>
           <b><p id="studentDetails" style="font-size: 1rem;"></p></b>
           <div id="dynamic-content" style="font-size: 0.9rem;">
           </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" style="font-size: 0.9rem;">Close</button>
        </div>

    </div>
  </div>
</div>

<!-- Late Time Modal -->
<div class="modal fade" id="lateTimeModal" tabindex="-1" role="dialog" aria-labelledby="lateTimeModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="lateTimeModalLabel">Enter Late Time</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <input type="text" id="late_time_input" class="form-control" placeholder="Select Time" readonly>
        <input type="hidden" id="late_time_student_index">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveLateTimeBtn">Save</button>
      </div>
    </div>
  </div>
</div>




<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


<script>


document.addEventListener("DOMContentLoaded", function () {
    flatpickr("#timepicker", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "h:i K",
        time_24hr: false,
    });
});

  function takeAttendance(){
    var form = $('#form_data')[0];
    var fileFormData = new FormData(form);
    var selectedSec = $('#sectionid').val();
    var validateDate = $('#select_date').val();
    if(selectedSec =='' || validateDate ==''){
      Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'Both class and Date should be Selected',
          confirmButtonText: 'OK'
      });
      return false;
    }
    $('#displayattendance').html('<div class="no-data-display">Loading...</div>');
    $.ajax({
        url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getAssignedDate'); ?>',
        type: 'post',
        data: {'selectedDate':validateDate,'selectedSec':selectedSec},
        success: function(data){
            data = JSON.parse(data);

            // No calendar template assigned
            if(!data) {
                var html = '<div class="no-data-display" style="margin:1rem;">No sessions assigned for the class</div>';
                $('#nodataattendance').html(html);
                $('#displayattendance').html('');
                return false;
            }

            // Calendar template is not locked
            if(data.is_locked === false) {
                var html = '<div class="no-data-display" style="margin:1rem;">You need to lock the calendar template assigned to this class to proceed further with mapping and events</div>';
                $('#nodataattendance').html(html);
                $('#displayattendance').html('');
                return false;
            }

            // Calendar template is locked, proceed with attendance
            getAttendanceData(fileFormData);
        }
    });
}

function getAttendanceData(fileFormData) {
    $.ajax({
        url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getstudentsclassWise'); ?>',
        type: 'post',
        data: fileFormData,
        processData: false,
        contentType: false,
        success: function(response) {
            function isJson(str) {
                try {
                    JSON.parse(str);
                } catch (e) {
                    return false;
                }
                return true;
            }
            if (!isJson(response)) {
                // Handle non-JSON response gracefully
                if (typeof response === 'string' && (response.indexOf('Attendance already taken') !== -1 || response.indexOf('success') !== -1)) {
                    // Attendance already taken, reload the view to show edit mode
                    takeAttendance();
                    return;
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response || 'An unexpected error occurred. Please try again later.',
                        confirmButtonText: 'OK'
                    });
                    return;
                }
            }
            try {
                var data = JSON.parse(response);
                var html = '';
                if (data.error) {
                    html = '<div class="no-data-display" style="margin:1rem;">' + data.message + '</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (data.students && data.students.isHoliday) {
                    html = '<div class="no-data-display" style="margin:1rem;">Selected date (' + $('#select_date').val() + ') is marked as a holiday in the calendar. Attendance cannot be taken on this day.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (data.students && data.students.students && data.students.students.summary &&
                    data.students.students.summary.sessionCount <= 0) {
                    html = '<div class="no-data-display" style="margin:1rem;">No sessions have been configured for this class.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                }

                if (!data.students || !data.students.students ||
                    !data.students.students.student_data || data.students.students.student_data.length == 0) {
                    html = '<div class="no-data-display" style="margin:1rem;">No students found in this class.</div>';
                    $('#nodataattendance').html(html);
                    $('#displayattendance').html('');
                    return false;
                } else if (data.students.students.summary.isTaken == 1) {
                    construct_edit_attendance(data);
                } else {
                    construct_add_attendance(data);
                }
                $('#nodataattendance').html(html);
            } catch (e) {
                console.error('An error occurred:', e);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An unexpected error occurred. Please try again later.',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Failed to fetch attendance data. Please try again later.',
                confirmButtonText: 'OK'
            });
        }
    });
}

function update_att_student_wise(student_id, session_id, att_id, session_column, markedAs, reason='', additionalUpdates={}) {
    var history_data = '';
    var sessionValue = '';
    var $checkbox = $('#' + session_column + '_' + att_id);

    if(reason != '') {
        sessionValue = reason;
        session_column = 'absent_remarks';
        history_data = reason;
        proceedWithAjaxCall();
    } else {
        sessionValue = $checkbox.is(':checked') ? 1 : 0;

        if(session_column === 'is_late') {
            if(sessionValue === 1) {
                // You can add late time modal logic here if needed
                return;
            } else {
                // If unchecked, allow update
                proceedWithAjaxCall();
            }
        } else {
            // For other attendance changes, show confirmation
            Swal.fire({
                title: 'Are you sure?',
                text: 'You want to update this student attendance?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    proceedWithAjaxCall();
                } else {
                    // Revert checkbox if cancelled
                    $checkbox.prop('checked', !sessionValue);
                }
            });
            return;
        }
    }

    function proceedWithAjaxCall() {
        $.ajax({
            url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/update_student_att_data'); ?>',
            type: 'POST',
            data: {
                'student_id': student_id,
                'session_id': session_id,
                'att_id': att_id,
                'session_column': session_column,
                'sessionValue': sessionValue,
                'markedAs': markedAs,
                'history_data': history_data,
                'additionalUpdates': additionalUpdates
            },
            success: function(data){
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Attendance edited successfully.',
                    confirmButtonText: 'Great!'
                });
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to update attendance. Please try again.',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
}

function construct_add_attendance(data) {
    var emptycheck = $('#sectionid').val();
    var dateemptycheck = $('#select_date').val();
    var absent = <?php echo (json_encode($absent_reasons)) ?>;

    var sessionCount = 2;
    if (data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount) {
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }

    var html = '';

    if (data.students && data.students.isHoliday) {
        html = '<div class="no-data-display">Selected date (' + $('#select_date').val() + ') is marked as a holiday in the calendar. Attendance cannot be taken on this day.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    if (!data.students || !data.students.students || !data.students.students.student_data || data.students.students.student_data.length === 0) {
        html = '<div class="no-data-display">No students found in this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    if (sessionCount <= 0) {
        html = '<div class="no-data-display">No sessions have been configured for this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    html += '<form id="attData">';
    html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';
    html += '<div>';
    for (var s = 0; s < data.students.students.student_data.length; s++) {
       var fatherNo = data.students.students.student_data[s].FatherNo;
      var displayFatherNo = (fatherNo === null || fatherNo === '') ? '-' : fatherNo;
        html += `
            <div class="card mb-3 p-3" style="border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
              <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][Std_name]" value="${data.students.students.student_data[s].std_name}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 style="margin: 0; font-weight: bold;">${data.students.students.student_data[s].std_name}</h6>
                        <p style="margin: 0; font-size: 0.9rem; color: gray;">Father: ${data.students.students.student_data[s].fatherName} (${displayFatherNo})</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">${sessionCount > 1 ? 'Morning' : 'Full Day'}</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input class="morning" data-index="${s}" checked value="1" type="checkbox" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                    ${sessionCount > 1 ? `
                    <div>
                        <label style="font-size: 0.9rem;">Afternoon</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]" value="0">
                            <input class="afternoon" data-index="${s}" checked value="1" type="checkbox" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]">
                            <span></span>
                        </label>
                    </div>` : ''}
                </div>
                <div class="mt-3">
                    <select class="form-control" id="reason_data${s}" disabled onchange="store_reason(${s})">
                        <option value="">Select Absent Reason</option>`;
        for (var i = 0; i < absent.length; i++) {
            var consider = (absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            html += `<option value="${absent[i].id}">${absent[i].reasons} - ${consider}</option>`;
        }
        html += `
                    </select>
                    <input type="hidden" id="hidden_reason${s}" name="attendance[${data.students.students.student_data[s].id}][reasons]">
                    <input type="hidden" id="hidden_reason_id${s}" name="attendance[${data.students.students.student_data[s].id}][reasons_id]">
                </div>
            </div>`;
    }
    html += '</div>';
    html += '<center><button class="btn btn-primary mt-3" type="button" id="take_attendance" onclick="sendattData()">Submit</button>';
    html += '<button class="btn btn-danger mt-3" type="button" onclick="location.reload()">Cancel</button></center>';
    html += '</form>'; // Close the form

    $('#displayattendance').html(html);

    if(sessionCount > 1) {
        $('.morning, .afternoon').on('change', function() {
            var row = $(this).closest('.card');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');

            if ($(this).hasClass('morning')) {
                $('.afternoon[data-index="' + index + '"]').prop('checked', isChecked);
            } else {
                $('.morning[data-index="' + index + '"]').prop('checked', isChecked);
            }

            var anyUnchecked = row.find('.morning, .afternoon').is(':not(:checked)');
            row.find('#reason_data'+index+'').prop('disabled', !anyUnchecked);
        });
    } else {
        $('.morning').on('change', function() {
            var row = $(this).closest('.card');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');
            row.find('#reason_data'+index+'').prop('disabled', isChecked);
        });
    }
    patchAddAttendanceLateTime();
}
document.addEventListener("DOMContentLoaded", function () {
    var cookies = document.cookie.split(';').reduce((acc, cookie) => {
        var [key, value] = cookie.split('=').map(c => c.trim());
        acc[key] = value;
        return acc;
    }, {});
    if (cookies.selected_section) {
        $('#sectionid').val(cookies.selected_section);
    }

    $('#sectionid').on('change', function () {
        var selectedSection = $(this).val();
        if (selectedSection) {
            document.cookie = "selected_section=" + selectedSection + "; path=/;";
        }
    });
});

function sendattData() {
    Swal.fire({
        title: 'Are you sure?',
        text: "You are about to submit attendance for this class and date. Do you want to proceed?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, submit'
    }).then((result) => {
        if (result.isConfirmed) {
            $("#take_attendance").attr('disabled', true).html('Please Wait...');

            var classSec = $('#sectionid').val();
            var selectedDate = $('#select_date').val();

            if (!classSec || !selectedDate) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Section and Date are required.',
                    confirmButtonText: 'OK'
                });
                $("#take_attendance").attr('disabled', false).html('Submit');
                return;
            }

            var [classId, sectionId] = classSec.split('_');

            $('.morning, .afternoon').each(function () {
                var checkbox = $(this);
                var isChecked = checkbox.is(':checked');
                var name = checkbox.attr('name');
                if (!isChecked) {
                    if (!$(`#attData input[type="hidden"][name="${name}"]`).length) {
                        $('#attData').append(`<input type="hidden" name="${name}" value="0">`);
                    }
                }
            });

            if (!$('#attData input[name="class_sec"]').length) {
                $('#attData').append(`<input type="hidden" name="class_sec" value="${classSec}">`);
            }
            if (!$('#attData input[name="date"]').length) {
                $('#attData').append(`<input type="hidden" name="date" value="${selectedDate}">`);
            }
            if (!$('#attData input[name="att_taken_date"]').length) {
                $('#attData').append(`<input type="hidden" name="att_taken_date" value="${selectedDate}">`);
            }
            if (!$('#attData input[name="class_id"]').length) {
                $('#attData').append(`<input type="hidden" name="class_id" value="${classId}">`);
            }
            if (!$('#attData input[name="section_id"]').length) {
                $('#attData').append(`<input type="hidden" name="section_id" value="${sectionId}">`);
            }

            var form = $('#attData')[0];
            var attFormData = new FormData(form);

            $.ajax({
                url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/addAttData'); ?>',
                type: 'POST',
                data: attFormData,
                processData: false,
                contentType: false,
                success: function (data) {
                  let parsedData = JSON.parse(data);
                  if(parsedData > 0){
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: 'Attendance submitted successfully.',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        const enable_notification = "<?php echo $enable_notification; ?>";
                          if (enable_notification == 1) {
                              notify_students(data);
                          } else {
                            takeAttendance();
                          }
                    });
                  } else {
                    Swal.fire({
                      icon: 'error',
                      title: 'Error!',
                      text: 'Something Wnet Wrong!. Please try again.',
                      confirmButtonText: 'OK'
                    }).then(()=>{
                      takeAttendance();
                    });
                  }
                },
                error: function (xhr, status, error) {
                    $("#take_attendance").attr('disabled', false).html('Submit');
                    console.error('AJAX Error:', xhr.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Failed to submit attendance. Please try again.',
                        confirmButtonText: 'OK'
                    }).then(()=>{
                      takeAttendance();
                    });
                }
            });
        }
    });
}

function construct_edit_attendance(data) {
    var edit_absent = <?php echo (json_encode($absent_reasons)) ?>;
    var sessionCount = 2;
    if(data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount){
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }
    var html = '';
    html += '<form id="attData">';
    html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';
    html += '<div>';
    for (var s = 0; s < data.students.students.student_data.length; s++) {
        var morningChecked = data.students.students.student_data[s].morning == 1 ? 'checked' : '';
        var afternoonChecked = data.students.students.student_data[s].afternoon == 1 ? 'checked' : '';
        var isLateChecked = data.students.students.student_data[s].is_late == 1 ? 'checked' : '';
        var disabled = (data.students.students.student_data[s].morning && data.students.students.student_data[s].afternoon == 1) ? 'disabled' : '';

        html += `
            <div class="card mb-3 p-3" style="border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][Std_name]" value="${data.students.students.student_data[s].std_name}">
                <div>
                    <h6 style="margin: 0; font-weight: bold;">${data.students.students.student_data[s].std_name}</h6>
                    <p style="margin: 0; font-size: 0.9rem; color: gray;">
                        Father: ${data.students.students.student_data[s].fatherName} (${data.students.students.student_data[s].FatherNo})
                    </p>
                </div>`;
        if(sessionCount > 1) {
            html += `
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Morning</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'morning_session_status', 'Morning')"
                                   id="morning_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_morning" data-index="${s}" ${morningChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                    <div>
                        <label style="font-size: 0.9rem;">Afternoon</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'afternoon_session_status', 'Afternoon')"
                                   id="afternoon_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_afternoon" data-index="${s}" ${afternoonChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]">
                            <span></span>
                        </label>
                    </div>
                </div>`;
        } else {
            html += `
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Full Day</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'morning_session_status', 'Full Day')"
                                   id="morning_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_morning" data-index="${s}" ${morningChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                </div>`;
        }
        html += `
                <div class="mt-3">
                    <select class="form-control" id="edit_reason_data${s}" onchange="absent_reason(${s}, ${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id})" ${disabled}>
                        <option value="">Select Absent Reason</option>`;
        for (var i = 0; i < edit_absent.length; i++) {
            var consider = (edit_absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            var selected = data.students.students.student_data[s].overide_remarks == edit_absent[i].reasons + ' - ' + consider ? 'selected' : '';
            html += `<option value="${edit_absent[i].id}" ${selected}>${edit_absent[i].reasons} - ${consider}</option>`;
        }
        html += `
                    </select>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Is Late</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][islate]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'is_late', 'Late')"
                                   id="is_late_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="is_Late" data-index="${s}" ${isLateChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][islate]">
                            <span></span>
                        </label>
                    </div>
                    <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#view-modal" data-index="${data.students.students.student_data[s].att_std_primary_id}" id="getUser">View History</button>
                </div>
            </div>`;
    }
    html += '</div>';
    html += '</form>';
    $('#displayattendance').html(html);

    $('.edit_morning, .edit_afternoon').on('change', handleSwitchChange);


    $('.edit_morning, .edit_afternoon').on('change', function() {
        var card = $(this).closest('.card');
        var bothUnchecked = !card.find('.edit_morning').is(':checked') &&
                           !card.find('.edit_afternoon').is(':checked');

        if(bothUnchecked) {
            card.find('.is_Late').prop('checked', false);
        }
    });
    $('.is_Late').on('change', function () {
        var card = $(this).closest('.card');
        var studentId = card.find('input[name*="[Std_name]"]').attr('name').match(/\[(\d+)\]/)[1];
        var sessionId = card.find('input[name="sessionId"]').val();
        var attId = $(this).attr('id').replace('is_late_', '');
        var isChecked = $(this).is(':checked');
        var index = $(this).data('index');
        var reasonField = card.find('#edit_reason_data' + index);
        var hasAfternoonSession = card.find('.edit_afternoon').length > 0;
        var $checkbox = $(this);

        if(isChecked) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to mark this student as late!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var sessionCount = 2;
                    if ($('#session_count').length) {
                        sessionCount = parseInt($('#session_count').val());
                    }
                    var morningCheckbox = card.find('.edit_morning');
                    morningCheckbox.prop('checked', true);
                    if (hasAfternoonSession && sessionCount > 1) {
                        var afternoonCheckbox = card.find('.edit_afternoon');
                        afternoonCheckbox.prop('checked', true);
                    }
                    reasonField.prop('disabled', true);
                    if (reasonField.val() !== '') {
                        reasonField.val('');
                    }
                    var updates = {
                        morning_session_status: 1,
                        absent_remarks: null,
                        override_present: 0
                    };
                    if (sessionCount > 1) {
                        updates.afternoon_session_status = 1;
                    }
                    // Show the late time modal only after confirmation
                    $('#late_time_student_index').val(index);
                    $('#lateTimeModal').modal('show');
                    update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late', '', updates);
                } else {
                    // Revert checkbox if cancelled and do NOT show modal
                    $checkbox.prop('checked', false);
                }
            });
        } else {
            $('#hidden_late_time' + index).val('');
            update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late');
        }
    });
    patchEditAttendanceLateTime();
}

// Handle switch change for edit attendance
function handleSwitchChange() {
    var card = $(this).closest('.card');
    var index = $(this).data('index');
    var isMorningChecked = card.find('.edit_morning').is(':checked');
    var isAfternoonChecked = card.find('.edit_afternoon').is(':checked');
    var reasonField = card.find('#edit_reason_data' + index);

    // If both are checked, disable reason; else enable
    if (isMorningChecked && (card.find('.edit_afternoon').length === 0 || isAfternoonChecked)) {
        reasonField.prop('disabled', true);
        if (reasonField.val() !== '') {
            reasonField.val('');
        }
    } else {
        reasonField.prop('disabled', false);
    }
}

function notify_students(data) {
    const school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
    const selectedDate= $('#select_date').val();
    const insert_id=data;
    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getabsent_stds'); ?>',
      type: 'post',
      data: {
        'selectedDate':selectedDate,
        'insert_id':insert_id
      },
      success: function (data) {
        let students = JSON.parse(data);
        if (students?.length) {
          $("#notify-modal").modal('show');
          $('#notify-modal').one('hidden.bs.modal', function () {
              $("#take_attendance").attr('disabled', false).html('Submit');
              takeAttendance();
          });
          $('#cancelButton').off('click').on('click', function () {
            $("#take_attendance").attr('disabled', false).html('Submit');
            $('#notify-modal').hide();
            takeAttendance();
          });
          html = `<table class="table">
                      <thead>
                        <tr>
                          <th>#</th>
                          <th style="min-width: 120px;">Student</th>
                          <th>Section</th>
                          <th>Reason</th>
                          <th>Mobile No.</th>
                          <th>Relation</th>
                          <th>Message</th>
                        </tr>
                      </thead>
                      <tbody>`;

          students?.forEach((s,i)=>{
            let absentTemplate="<?php echo $this->settings->getSetting('student_attendancev2_day_attendance_absentee_message'); ?>";
            if(!absentTemplate){
              absentTemplate="Your ward %%student_name%% of %%class_section%% is absent today %%date%%. Regards- Principal - %%school_name%%-NXTSMS";
            }

            absentTemplate=absentTemplate.replaceAll("%%student_name%%",`${s.std_name}`);
            absentTemplate=absentTemplate.replaceAll("%%class_section%%",`${s.class_Section}`);
            absentTemplate=absentTemplate.replaceAll("%%date%%",`${s.attendance_taken_date}`);
            absentTemplate=absentTemplate.replaceAll("%%school_name%%",`${school_name}`);

            html += `
              <tr>
                  <td>${++i}</td>
                  <td>${s.std_name}</td>
                  <td>${s.class_Section}</td>
                  <td>Absent</td>
                  <td>${s.parentMobile}</td>
                  <td>${s.parentRelation}</td>
                  <td>
                      ${absentTemplate}
                      <input type="hidden" name="student_messages[${s.student_id}]" value="${absentTemplate}" />
                      <input type="hidden" name="session_insert_id" value="${insert_id}" />
                  </td>
              </tr>
            `;
          });

          html += `</tbody>
                </table>`;

          $("#notify-content").html(html);
        }else{
          takeAttendance();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function send_messages() {
    $("#confirmBtn").attr('disabled', true).html('Please Wait...');
    let formData = new FormData(document.getElementById('student-messages-form'));
    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/send_messages'); ?>',
      type: 'post',
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function (data) {
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#notify-modal").modal('hide');
        let response = JSON.parse(data);
        if (response.status == 1) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Message Was Sent to Parents.',
            confirmButtonText: 'Great!'
          }).then(()=>{
            takeAttendance();
          });
        } else {
          Swal.fire({
            icon:"error",
            title: 'Error',
            text: `${response.error}`,
            type: 'error',
          }).then(()=>{
            takeAttendance();
          });
        }
      },
      error: function (err) {
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#notify-modal").modal('hide');
        console.log(err);
        Swal.fire({
          icon:"error",
          title: 'Error',
          text: 'Something Went Wrong!',
          type: 'error',
        }).then(()=>{
          takeAttendance();
        });
      }
    });
  }

  $("#student-messages-form").click(e => {
      const msgType = e.target;

      if (msgType.type === "radio") {
          if (msgType.id === "sms") {
              $("#text_send_to").css("display", "inline-block");
          } else {
              $("#text_send_to").css("display", "none");
          }
      }
  });



$(document).on('click', '#getUser', function(e){

  e.preventDefault();
  var uid = $(this).data('index');
  $('#dynamic-content').html('');
  $('#modal-loader').hide();

  $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getAttendanceHistory'); ?>',
      type: 'POST',
      data: 'id='+uid,
      dataType: 'html',
      success: function (data) {
        var data = JSON.parse(data);
        if(data.length ==0){
          html = '<div class="no-data-display">No History Found for Selected Student </div>';
        }else{
          html = '';
          html +='<table class="table table-bordered" cellspacing="0" width="100%">';
          html +=' <thead><tr>';
          html +='<th>Taken By</th><th>Day</th><th>Time</th><th>Remarks</th>';
          html +='</thead><tbody>';
          for (s=0; s<data.length; s++){
            var event_datetime  = data[s].event_date;
            var [date_part, time_part] = event_datetime.split(" ");

            html +='<tr><td>'+data[s].taken_by+'</td>';
            html +='<td>'+date_part+'</td>';
            html +='<td>'+time_part+'</td>';
            html +='<td>'+data[s].history_data+'</td></tr>';
          }
          html +='</tbody></table>';
        }
        $("#dynamic-content").html(html);
      }
  })
});



// Late time modal logic
$(document).ready(function() {
  flatpickr("#late_time_input", {
    enableTime: true,
    noCalendar: true,
    dateFormat: "h:i K",
    time_24hr: false
  });

  // Save late time
  $('#saveLateTimeBtn').on('click', function() {
    var index = $('#late_time_student_index').val();
    var time = $('#late_time_input').val();
    if (!time) {
      Swal.fire({ icon: 'error', title: 'Error!', text: 'Please select a time.' });
      return;
    }
    // Store in hidden input for submissionan
    $("#hidden_late_time" + index).val(time);
    $('#lateTimeModal').modal('hide');
  });

  // When modal closes, if not saved, uncheck the is_Late checkbox
  $('#lateTimeModal').on('hidden.bs.modal', function () {
    var index = $('#late_time_student_index').val();
    var time = $('#late_time_input').val();
    if (!time) {
      // Uncheck the is_Late checkbox if no time was set
      $(".is_Late[data-index='" + index + "']").prop('checked', false);
    }
    $('#late_time_input').val('');
    $('#late_time_student_index').val('');
  });
});

// Patch for add attendance: add hidden input for late time and show modal on is_Late checked
function patchAddAttendanceLateTime() {
  $(document).off('change', '.is_Late').on('change', '.is_Late', function() {
    var index = $(this).data('index');
    var $checkbox = $(this);
    if ($checkbox.is(':checked')) {
      // First show confirmation
      Swal.fire({
        title: 'Are you sure?',
        text: "You want to mark this student as late!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm it!'
      }).then((result) => {
        if (result.isConfirmed) {
          $('#late_time_student_index').val(index);
          $('#lateTimeModal').modal('show');
        } else {
          // Revert checkbox if cancelled
          $checkbox.prop('checked', false);
        }
      });
    } else {
      // Clear hidden input if unchecked
      $('#hidden_late_time' + index).val('');
    }
  });
}

// Patch for edit attendance: add hidden input for late time and show modal on is_Late checked
function patchEditAttendanceLateTime() {
  $(document).off('change', '.is_Late').on('change', '.is_Late', function() {
    var $checkbox = $(this);
    var index = $checkbox.data('index');
    if ($checkbox.is(':checked')) {
      // Temporarily uncheck, will check after confirmation
      $checkbox.prop('checked', false);
      Swal.fire({
        title: 'Are you sure?',
        text: 'You want to mark this student as late!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm it!'
      }).then((result) => {
        if (result.isConfirmed) {
          $checkbox.prop('checked', true);
          // Show modal and set index
          $('#late_time_student_index').val(index);
          $('#lateTimeModal').modal('show');
        } else {
          $checkbox.prop('checked', false);
        }
      });
    } else {
      // Clear hidden input if unchecked
      $('#hidden_late_time' + index).val('');
    }
  });
}

// Placeholder for update_att_student_wise to prevent JS errors
function update_att_student_wise(studentId, sessionId, attId, field, label, extra, updates) {
    // TODO: Implement logic to update attendance for a student
    // This is a placeholder to prevent JS errors.
    console.log('update_att_student_wise called', {studentId, sessionId, attId, field, label, extra, updates});
}

function construct_add_attendance(data) {
    var emptycheck = $('#sectionid').val();
    var dateemptycheck = $('#select_date').val();
    var absent = <?php echo (json_encode($absent_reasons)) ?>;

    var sessionCount = 2;
    if (data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount) {
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }

    var html = '';

    if (data.students && data.students.isHoliday) {
        html = '<div class="no-data-display">Selected date (' + $('#select_date').val() + ') is marked as a holiday in the calendar. Attendance cannot be taken on this day.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    if (!data.students || !data.students.students || !data.students.students.student_data || data.students.students.student_data.length === 0) {
        html = '<div class="no-data-display">No students found in this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    if (sessionCount <= 0) {
        html = '<div class="no-data-display">No sessions have been configured for this class.</div>';
        $('#nodataattendance').html(html);
        $('#displayattendance').html('');
        return;
    }

    html += '<form id="attData">';
    html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';
    html += '<div>';
    for (var s = 0; s < data.students.students.student_data.length; s++) {
       var fatherNo = data.students.students.student_data[s].FatherNo;
      var displayFatherNo = (fatherNo === null || fatherNo === '') ? '-' : fatherNo;
        html += `
            <div class="card mb-3 p-3" style="border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
              <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][Std_name]" value="${data.students.students.student_data[s].std_name}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 style="margin: 0; font-weight: bold;">${data.students.students.student_data[s].std_name}</h6>
                        <p style="margin: 0; font-size: 0.9rem; color: gray;">Father: ${data.students.students.student_data[s].fatherName} (${displayFatherNo})</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">${sessionCount > 1 ? 'Morning' : 'Full Day'}</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input class="morning" data-index="${s}" checked value="1" type="checkbox" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                    ${sessionCount > 1 ? `
                    <div>
                        <label style="font-size: 0.9rem;">Afternoon</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]" value="0">
                            <input class="afternoon" data-index="${s}" checked value="1" type="checkbox" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]">
                            <span></span>
                        </label>
                    </div>` : ''}
                </div>
                <div class="mt-3">
                    <select class="form-control" id="reason_data${s}" disabled onchange="store_reason(${s})">
                        <option value="">Select Absent Reason</option>`;
        for (var i = 0; i < absent.length; i++) {
            var consider = (absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            html += `<option value="${absent[i].id}">${absent[i].reasons} - ${consider}</option>`;
        }
        html += `
                    </select>
                    <input type="hidden" id="hidden_reason${s}" name="attendance[${data.students.students.student_data[s].id}][reasons]">
                    <input type="hidden" id="hidden_reason_id${s}" name="attendance[${data.students.students.student_data[s].id}][reasons_id]">
                </div>
            </div>`;
    }
    html += '</div>';
    html += '<center><button class="btn btn-primary mt-3" type="button" id="take_attendance" onclick="sendattData()">Submit</button>';
    html += '<button class="btn btn-danger mt-3" type="button" onclick="location.reload()">Cancel</button></center>';
    html += '</form>'; // Close the form

    $('#displayattendance').html(html);

    if(sessionCount > 1) {
        $('.morning, .afternoon').on('change', function() {
            var row = $(this).closest('.card');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');

            if ($(this).hasClass('morning')) {
                $('.afternoon[data-index="' + index + '"]').prop('checked', isChecked);
            } else {
                $('.morning[data-index="' + index + '"]').prop('checked', isChecked);
            }

            var anyUnchecked = row.find('.morning, .afternoon').is(':not(:checked)');
            row.find('#reason_data'+index+'').prop('disabled', !anyUnchecked);
        });
    } else {
        $('.morning').on('change', function() {
            var row = $(this).closest('.card');
            var index = $(this).data('index');
            var isChecked = $(this).is(':checked');
            row.find('#reason_data'+index+'').prop('disabled', isChecked);
        });
    }
    patchAddAttendanceLateTime();
}

function construct_edit_attendance(data) {
    var edit_absent = <?php echo (json_encode($absent_reasons)) ?>;
    var sessionCount = 2;
    if(data.students && data.students.students && data.students.students.summary && data.students.students.summary.sessionCount){
        sessionCount = parseInt(data.students.students.summary.sessionCount);
    }
    var html = '';
    html += '<form id="attData">';
    html += '<input type="hidden" name="session_count" id="session_count" value="' + sessionCount + '"/>';
    html += '<div>';
    for (var s = 0; s < data.students.students.student_data.length; s++) {
        var morningChecked = data.students.students.student_data[s].morning == 1 ? 'checked' : '';
        var afternoonChecked = data.students.students.student_data[s].afternoon == 1 ? 'checked' : '';
        var isLateChecked = data.students.students.student_data[s].is_late == 1 ? 'checked' : '';
        var disabled = (data.students.students.student_data[s].morning && data.students.students.student_data[s].afternoon == 1) ? 'disabled' : '';

        html += `
            <div class="card mb-3 p-3" style="border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][Std_name]" value="${data.students.students.student_data[s].std_name}">
                <div>
                    <h6 style="margin: 0; font-weight: bold;">${data.students.students.student_data[s].std_name}</h6>
                    <p style="margin: 0; font-size: 0.9rem; color: gray;">
                        Father: ${data.students.students.student_data[s].fatherName} (${data.students.students.student_data[s].FatherNo})
                    </p>
                </div>`;
        if(sessionCount > 1) {
            html += `
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Morning</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'morning_session_status', 'Morning')"
                                   id="morning_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_morning" data-index="${s}" ${morningChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                    <div>
                        <label style="font-size: 0.9rem;">Afternoon</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'afternoon_session_status', 'Afternoon')"
                                   id="afternoon_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_afternoon" data-index="${s}" ${afternoonChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_afternoon]">
                            <span></span>
                        </label>
                    </div>
                </div>`;
        } else {
            html += `
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Full Day</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][attendance_morning]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'morning_session_status', 'Full Day')"
                                   id="morning_session_status_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="edit_morning" data-index="${s}" ${morningChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][attendance_morning]">
                            <span></span>
                        </label>
                    </div>
                </div>`;
        }
        html += `
                <div class="mt-3">
                    <select class="form-control" id="edit_reason_data${s}" onchange="absent_reason(${s}, ${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id})" ${disabled}>
                        <option value="">Select Absent Reason</option>`;
        for (var i = 0; i < edit_absent.length; i++) {
            var consider = (edit_absent[i].consider_as_present == 1) ? 'Present' : 'Absent';
            var selected = data.students.students.student_data[s].overide_remarks == edit_absent[i].reasons + ' - ' + consider ? 'selected' : '';
            html += `<option value="${edit_absent[i].id}" ${selected}>${edit_absent[i].reasons} - ${consider}</option>`;
        }
        html += `
                    </select>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <label style="font-size: 0.9rem;">Is Late</label>
                        <label class="switch">
                            <input type="hidden" name="attendance[${data.students.students.student_data[s].id}][islate]" value="0">
                            <input onchange="update_att_student_wise(${data.students.students.student_data[s].id}, ${data.students.students.summary.sessionID}, ${data.students.students.student_data[s].att_std_primary_id}, 'is_late', 'Late')"
                                   id="is_late_${data.students.students.student_data[s].att_std_primary_id}"
                                   class="is_Late" data-index="${s}" ${isLateChecked} type="checkbox"
                                   name="attendance[${data.students.students.student_data[s].id}][islate]">
                            <span></span>
                        </label>
                    </div>
                    <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#view-modal" data-index="${data.students.students.student_data[s].att_std_primary_id}" id="getUser">View History</button>
                </div>
            </div>`;
    }
    html += '</div>';
    html += '</form>';
    $('#displayattendance').html(html);

    $('.edit_morning, .edit_afternoon').on('change', handleSwitchChange);


    $('.edit_morning, .edit_afternoon').on('change', function() {
        var card = $(this).closest('.card');
        var bothUnchecked = !card.find('.edit_morning').is(':checked') &&
                           !card.find('.edit_afternoon').is(':checked');

        if(bothUnchecked) {
            card.find('.is_Late').prop('checked', false);
        }
    });
    $('.is_Late').on('change', function () {
        var card = $(this).closest('.card');
        var studentId = card.find('input[name*="[Std_name]"]').attr('name').match(/\[(\d+)\]/)[1];
        var sessionId = card.find('input[name="sessionId"]').val();
        var attId = $(this).attr('id').replace('is_late_', '');
        var isChecked = $(this).is(':checked');
        var index = $(this).data('index');
        var reasonField = card.find('#edit_reason_data' + index);
        var hasAfternoonSession = card.find('.edit_afternoon').length > 0;
        var $checkbox = $(this);

        if(isChecked) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to mark this student as late!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var sessionCount = 2;
                    if ($('#session_count').length) {
                        sessionCount = parseInt($('#session_count').val());
                    }
                    var morningCheckbox = card.find('.edit_morning');
                    morningCheckbox.prop('checked', true);
                    if (hasAfternoonSession && sessionCount > 1) {
                        var afternoonCheckbox = card.find('.edit_afternoon');
                        afternoonCheckbox.prop('checked', true);
                    }
                    reasonField.prop('disabled', true);
                    if (reasonField.val() !== '') {
                        reasonField.val('');
                    }
                    var updates = {
                        morning_session_status: 1,
                        absent_remarks: null,
                        override_present: 0
                    };
                    if (sessionCount > 1) {
                        updates.afternoon_session_status = 1;
                    }
                    // Show the late time modal only after confirmation
                    $('#late_time_student_index').val(index);
                    $('#lateTimeModal').modal('show');
                    update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late', '', updates);
                } else {
                    // Revert checkbox if cancelled and do NOT show modal
                    $checkbox.prop('checked', false);
                }
            });
        } else {
            $('#hidden_late_time' + index).val('');
            update_att_student_wise(studentId, sessionId, attId, 'is_late', 'Late');
        }
    });
    patchEditAttendanceLateTime();
}

function notify_students(data) {
    const school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
    const selectedDate= $('#select_date').val();
    const insert_id=data;
    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getabsent_stds'); ?>',
      type: 'post',
      data: {
        'selectedDate':selectedDate,
        'insert_id':insert_id
      },
      success: function (data) {
        let students = JSON.parse(data);
        if (students?.length) {
          $("#notify-modal").modal('show');
          $('#notify-modal').one('hidden.bs.modal', function () {
              $("#take_attendance").attr('disabled', false).html('Submit');
              takeAttendance();
          });
          $('#cancelButton').off('click').on('click', function () {
            $("#take_attendance").attr('disabled', false).html('Submit');
            $('#notify-modal').hide();
            takeAttendance();
          });
          html = `<table class="table">
                      <thead>
                        <tr>
                          <th>#</th>
                          <th style="min-width: 120px;">Student</th>
                          <th>Section</th>
                          <th>Reason</th>
                          <th>Mobile No.</th>
                          <th>Relation</th>
                          <th>Message</th>
                        </tr>
                      </thead>
                      <tbody>`;

          students?.forEach((s,i)=>{
            let absentTemplate="<?php echo $this->settings->getSetting('student_attendancev2_day_attendance_absentee_message'); ?>";
            if(!absentTemplate){
              absentTemplate="Your ward %%student_name%% of %%class_section%% is absent today %%date%%. Regards- Principal - %%school_name%%-NXTSMS";
            }

            absentTemplate=absentTemplate.replaceAll("%%student_name%%",`${s.std_name}`);
            absentTemplate=absentTemplate.replaceAll("%%class_section%%",`${s.class_Section}`);
            absentTemplate=absentTemplate.replaceAll("%%date%%",`${s.attendance_taken_date}`);
            absentTemplate=absentTemplate.replaceAll("%%school_name%%",`${school_name}`);

            html += `
              <tr>
                  <td>${++i}</td>
                  <td>${s.std_name}</td>
                  <td>${s.class_Section}</td>
                  <td>Absent</td>
                  <td>${s.parentMobile}</td>
                  <td>${s.parentRelation}</td>
                  <td>
                      ${absentTemplate}
                      <input type="hidden" name="student_messages[${s.student_id}]" value="${absentTemplate}" />
                      <input type="hidden" name="session_insert_id" value="${insert_id}" />
                  </td>
              </tr>
            `;
          });

          html += `</tbody>
                </table>`;

          $("#notify-content").html(html);
        }else{
          takeAttendance();
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function send_messages() {
    $("#confirmBtn").attr('disabled', true).html('Please Wait...');
    let formData = new FormData(document.getElementById('student-messages-form'));
    $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/send_messages'); ?>',
      type: 'post',
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function (data) {
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#notify-modal").modal('hide');
        let response = JSON.parse(data);
        if (response.status == 1) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Message Was Sent to Parents.',
            confirmButtonText: 'Great!'
          }).then(()=>{
            takeAttendance();
          });
        } else {
          Swal.fire({
            icon:"error",
            title: 'Error',
            text: `${response.error}`,
            type: 'error',
          }).then(()=>{
            takeAttendance();
          });
        }
      },
      error: function (err) {
        $("#confirmBtn").attr('disabled', false).html('Confirm');
        $("#take_attendance").attr('disabled', false).html('Submit');
        $("#notify-modal").modal('hide');
        console.log(err);
        Swal.fire({
          icon:"error",
          title: 'Error',
          text: 'Something Went Wrong!',
          type: 'error',
        }).then(()=>{
          takeAttendance();
        });
      }
    });
  }

  $("#student-messages-form").click(e => {
      const msgType = e.target;

      if (msgType.type === "radio") {
          if (msgType.id === "sms") {
              $("#text_send_to").css("display", "inline-block");
          } else {
              $("#text_send_to").css("display", "none");
          }
      }
  });



$(document).on('click', '#getUser', function(e){

  e.preventDefault();
  var uid = $(this).data('index');
  $('#dynamic-content').html('');
  $('#modal-loader').hide();

  $.ajax({
      url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/getAttendanceHistory'); ?>',
      type: 'POST',
      data: 'id='+uid,
      dataType: 'html',
      success: function (data) {
        var data = JSON.parse(data);
        if(data.length ==0){
          html = '<div class="no-data-display">No History Found for Selected Student </div>';
        }else{
          html = '';
          html +='<table class="table table-bordered" cellspacing="0" width="100%">';
          html +=' <thead><tr>';
          html +='<th>Taken By</th><th>Day</th><th>Time</th><th>Remarks</th>';
          html +='</thead><tbody>';
          for (s=0; s<data.length; s++){
            var event_datetime  = data[s].event_date;
            var [date_part, time_part] = event_datetime.split(" ");

            html +='<tr><td>'+data[s].taken_by+'</td>';
            html +='<td>'+date_part+'</td>';
            html +='<td>'+time_part+'</td>';
            html +='<td>'+data[s].history_data+'</td></tr>';
          }
          html +='</tbody></table>';
        }
        $("#dynamic-content").html(html);
      }
  })
});



</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style type="text/css">
.title_edit_class {
    margin-left:-14px
}
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
.status-tag {
    padding: 2px 8px;
    border-radius: 16px;
}
.success-status {
    r-color: #cdffcf;
}
.warning-status {
    background-color: blanchedalmond;
}
.default-status {
    background-color: #eaeaea;
}
.danger-status {
    background-color: #ffab9d;
}
.btn {
    margin-right: 4px;
    border-radius: 1.2rem;
}
.btn .fa {
    margin-right: 0px;
}
.btn-warning, .btn-info {
    width:  90px;
}
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
}

.colorClass  {
    color: red;
}
</style>




