<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Daily Transaction</li>
</ul>
<hr>


<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="panel-heading panel_heading_new_style_staff_border" style="height:90px">
      <div class="row" style="margin: 0">
        <h3 class="panel-title card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a>
           Daily Transaction Report
        </h3>
        <ul class="panel-controls">
          <div class="more">
            <button id="more-btn" class="more-btn">
              <span class="more-dot"></span>
              <span class="more-dot"></span>
              <span class="more-dot"></span>
            </button>
            <div class="more-menu" style="right: 2%;display:none" id="display_menu">
              <div class="more-menu-caret">
                <div class="more-menu-caret-outer"></div>
                <div class="more-menu-caret-inner"></div>
              </div>

              <div class="more-menu-item action_btn" data-button-type="export" role="presentation"
                onclick="printDayBookReport()" style="min-width: 100% !important; color: #66757f !important; cursor: pointer !important; display: block !important; font-size: 13px !important; line-height: 18px !important; padding: 5px 20px !important;">
                <span class="fa fa-print"></span> Print
              </div>
              <div class="more-menu-item action_btn" data-button-type="export" role="presentation"
                id="dataTableExport">
              
              </div>
            </div>
          </div>
        </ul>
      </div>
    <hr>
    </div>
  </div>
<style type="text/css">
  p{
    margin-bottom: .5rem;
  }
  input[type=checkbox]{
    margin: 0px 4px;
  }
</style>
<div class="panel-heading panel_heading_new_style_staff">
<div class="row" style="margin: 0">
  <h4 class="" style="display: inline;">
      <a id="collapseId" role="button" data-toggle="collapse" href="#accTwoColTwo" aria-expanded="false" aria-controls="accTwoColTwo">
        <span class="fa fa-plus-circle"></span>
      </a>
      <span style="margin-left: 4px;" id="showhideFilter"> Show Filters </span> 
    </h4>

    <?php if($this->authorization->isAuthorized('FEESV2.PREDEFINED_FILTERS')) { ?>
    <div class="col-md-6" style="float: right;">
      <div class="form-group">
        <label class="col-md-4 control-label" style="text-align: right;margin-top:5px">Saved Reports</label>
          <div class="col-md-4">
            <select name="" id="filter_types" class="form-control col-12" onchange="select_filters()">
              <option value="">Select Report</option>
            </select>
          </div>
       <?php if($this->authorization->isAuthorized('FEESV2.PREDEFINED_FILTERS')) { ?>
          <div class="dt-buttons col-md-4" style="text-align:left">
            <input type="button" name="reload" id="reload_filter" class="btn btn-info col-6" style="border-radius: 8px;display:none;margin-right:2px;" value="Reload" onclick="select_filters()">
            <input type="button" name="save" id="save_filter" class="btn btn-info col-6" style="border-radius: 8px;margin-right:2px;" value="Save">
            <input type="button" name="update" id="update_filter" class="btn btn-info col-6" style="border-radius: 8px;margin-right:2px;" value="Update">
          </div>
        <?php }?>
      </div>
    </div>
    <?php } ?>
</div>
</div>
  <div class="panel panel-default new-panel-style_3" style="display: none;" id="show">
  <div class="panel-heading" style="background:#fff; border: none;">
  </div>
  <div class="panel-body collapse" id="accTwoColTwo">
    <div class="form-group col-md-12" id="showHideCheckbox">
      <label style="font-weight: 600;" class="checkbox-inline"><input style="width:14px;height: 15px;" type="checkbox" name="recon" id="recon">Consider recon</label>
      <label style="font-weight: 600;"  class="checkbox-inline"><input style="width:14px;height: 15px;" type="checkbox" name="components" id="components_include">Includes Components Summary</label>
      <label style="font-weight: 600;"  class="checkbox-inline"><input style="width:14px;height: 15px;" type="checkbox" checked name="components" id="components_exclude">Includes Components</label>
      <label style="font-weight: 600;"  class="checkbox-inline"><input style="width:14px;height: 15px;" type="checkbox" name="components_v_h" checked id="components_v_h">Display Components Horizontally</label>
      <label style="font-weight: 600;"  class="checkbox-inline"><input style="width:14px;height: 15px;" type="checkbox" name="include_delete" id="include_delete">Include Cancelled</label>
    </div>
  
      <div class="row" style="margin: 0px">
        <div class="col-md-3 form-group">
          <label><span style="color: #1e428a; font-weight: 700;" id="reconLable">Receipt</span> Date Range</label>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2 form-group" id="classSection">
          <label>Class</label>
            <select name="class_name[]" id="classId" multiple title='All' class="form-control classId select">
            <?php foreach ($classes as $key => $class) { ?>
                <option value="<?= $class->classId ?>" ><?= $class->className ?></option>
              <?php } ?>
            </select>
        </div>

        <div class="col-md-2 form-group">
          <label>Payment Method</label>
          <?php 
            $array = array();
            foreach ($payment_mode as $key => $mode) {
              $array[$mode->value] = ucfirst(str_replace('_',' ',$mode->name));
              $array['10'] = 'Online Payment';
              if ($additionalAmount) {
                $array['999'] = 'Excess Amount';
              }
            }
            echo form_dropdown("payment_type", $array, set_value("payment_type"),  "id='paymentModes' class='form-control select' multiple title='All' ");
          ?>
        </div>

      
        <div class="col-md-2 form-group">
          <label>Fee Type</label>
          <select class="form-control select" multiple title='All' id="fee_type" name="fee_type">
            <?php foreach ($fee_blueprints as $key => $val) { ?>
              <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
            <?php } ?>
            <?php if ($sales) {
              echo '<option value="sales">Sales</option>';
            } ?>
            <?php if ($admission) {
              echo '<option value="application">Applications</option>';
            } ?>
            <?php if ($additionalAmount) {
              echo '<option value="excess_amount">Excess Amount</option>';
            } ?>
          </select>
        </div>
        <div class="col-md-2 form-group">
          <label>Admission Type</label>
          <?php 
            $array = array();
            $array[0] = 'Select Admission Type';
            foreach ($admission_type as $key => $admission) {
                $array[$key] = ucfirst($admission);
            }
            echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control'");
          ?>
        </div>

      </div>

      <div class="row" style="margin: 0px">
          <div class="form-group">
          <label class="control-label mr-3">Report Format </label>
          </div>
          <div class="col-md-8">
              <label class="radio-inline" for="type-2">
                <input type="radio" name="report_type" id="type-2" value="2" checked="">Summary
              </label>
              <label class="radio-inline" for="type-3">
                <input type="radio" name="report_type" id="type-3" value="3">Summary (With Parent details)
              </label>
              <label class="radio-inline" for="type-1">
                <input type="radio" name="report_type" id="type-1" value="1">Detailed Report
              </label>
              <label class="radio-inline" for="type-1">
                <input type="radio" name="report_type" id="type-1" value="4">Installment wise Report
              </label>
          </div>
      </div>
      
      <div class="row" style="margin: 0px">
        <div class="col-sm-12 col-md-12 text-center" style="height: 4.5rem;">
          <br>
          <input type="button" name="search" id="search" class="btn btn-primary" value="Get Report">
          <?php if($this->authorization->isAuthorized('FEESV2.PREDEFINED_FILTERS')) { ?>
              <input type="button" name="" id="clear_filter" style="border-radius: 8px;" class="btn btn-secondary" value="Clear">
          <?php } ?>
        </div>
      </div>
  </div>
  </div>
  <div class="panel panel-default new-panel-style_3">
    <div class="panel-body" id="print_visible"  style="display: none;">
      <div id="printArea">
        <div class="text-center">
          <!-- <h3><?php // echo $this->settings->getSetting('school_name') ?></h3> -->
          <h3>Daily Fee Report</h3>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>
        <div style="clear: both"></div>
        <div class="pull-right">
          <div id="sliderDiv" class="mb-3" style="display: none;">
            <input id="slider" type="range" min="1" max='100' value="0" step="1">
          </div>
        </div>

        <div class="panel-body day_book  table-responsive hidden-xs" id="daybook_range" style="padding: 0">
          
        </div>

       
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div>
    </div>

    
    <div class="panel-body" id="summary">

      <div id="printArea_summary">
        <div id="print_summary" style="display: none" class="text-center">
          <!-- <h3><?php // echo $this->settings->getSetting('school_name') ?></h3> -->
          <h3 style="color:#337ab7;">Daily Fee Report</h3>
          <h5 style="color:#337ab7;">From <span id="fromDate_summary"></span> To <span id="toDate_summary"></span></h5>
        </div>
        <div style="clear: both"></div>
        <div class="panel-body day_book_summary  table-responsive hidden-xs" style="padding: 0" id="daybookFee_scrool">

        </div>

        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>

      </div>

    </div>

  </div>
</div>

<style type="text/css">
  input[type ='range'] {
    margin: 0 auto;
    width: 100px;
  }

  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .select2-checkbox-option{
      display: flex;
      align-items: start;
    }

    .select2-checkbox {
      margin-right: 5px;
    }
</style>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script type="text/javascript">

  $('#collapseId').on('click',function(){
    var expandedValue = $(this).attr("aria-expanded");
    if(expandedValue =='false'){
      $('#showhideFilter').html('Hide Filters');
      $('#collapseId span').removeClass('fa-plus-circle');
      $('#collapseId span').addClass('fa-minus-circle');
      $('#accTwoColTwo').show();
      $('#show').show();
    }else{
      $('#showhideFilter').html('Show Filters');
      $('#collapseId span').removeClass('fa-minus-circle');
      $('#collapseId span').addClass('fa-plus-circle');
      $('#accTwoColTwo').hide();
      $('#show').hide();
    }
  });

  $('#recon').on('click', function() {
    if ($(this).is(':checked')) {
      $('#reconLable').html('Recon');
    }else{
      $('#reconLable').html('Receipt');
    }
  });

  function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {

    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });

    $('#salesInclude').change(function(){
      if(this.checked) {
        $('#classSection').hide();
       }else{
        $('#classSection').show();
      }
    });
  
    get_predefined_filters()
  });
$('#search').on('click',function(){
  // $('#search').prop('disabled',true).val('Please wait...');
  get_daily_tx_report();
}); 
var columns_activeArry = [];
$('#save_filter').on('click',function(){
  columns_activeArry = [];
  
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var classId = $('#classId').val();
  var fee_type = $('#fee_type').val();
  var paymentModes = $('#paymentModes').val();
  var admission_type = $('#admission_type').val();
  var components = 0;
  if ($('#components_include').is(':checked')) {
    components = 1;
  }
  var components_v_h = 1;
  if ($('#components_v_h').is(':checked')) {
    components_v_h = 0;
  }
  var components_exclude = 0;
  if ($('#components_exclude').is(':checked')) {
    components_exclude = 1;
  }

  var include_delete = 0;
  if ($('#include_delete').is(':checked')) {
    include_delete = 1;
  }

  var recon = 0;
  if ($('#recon').is(':checked')) {
    recon = 1;
  }
  var report_type = $('input[name="report_type"]:checked').val();

  var table = $('#daily_dataTable').DataTable();
  table.columns().every(function (index) {
    if (this.visible()) {
      columns_activeArry.push(index); // Push the index of the visible column
    }
  });

  bootbox.prompt({
        inputType:'text',
        placeholder: 'Enter the Title name',
        title: "Save filters", 
        className:'widthadjust',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function (remarks) {
                if (remarks=='') {
                    return false;
                }
                if(remarks) {        
                  $.ajax({
                    url: '<?php echo site_url('feesv2/reports/save_filters'); ?>',
                    type: 'post',
                    data: {'title':remarks,'from_date':from_date,'to_date':to_date,'classId':classId,'fee_type':fee_type,'paymentModes':paymentModes,'admission_type':admission_type,'components':components,'components_v_h':components_v_h,'components_exclude':components_exclude,'include_delete':include_delete,'recon':recon,'report_type':report_type,'columns_activeArry':columns_activeArry},
                    success:function(data){
                      if(data){
                      new PNotify({
                          title: 'Success',
                          text: 'Successfully Saved',
                          type: 'success',
                        });
                      get_predefined_filters();
                      } else {
                       new PNotify({
                          title: 'Error',
                          text: 'Something went wrong',
                          type: 'Error',
                        });
                      }
                    }
                  });
                }
          }
      });
});
var setColumnDef = [];
function select_filters(){
  var filter_id = $('#filter_types').val();
  if(filter_id == ''){
    $('#reload_filter').hide();
    clear_filters();
    return false;
  }
  $('#reload_filter').show();
  $.ajax({
    url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id'); ?>',
    type: 'post',
    data: {'filter_id':filter_id},
    success: function(data) {
       var res_data = JSON.parse(data);
      //  console.log(res_data);
       setColumnDef = res_data['filters_selected'].columns_activeArry;
      // console.log(setColumnDef);
        var table = $('#daily_dataTable').DataTable();
          table.columns().every(function (index) {
              // console.log(index);

              // Convert index to string for comparison
              var isVisible = setColumnDef.includes(index.toString());

              // Set visibility correctly
              table.column(index).visible(isVisible);
          });

       if(res_data['filters_selected'].admission_type == 1){
          $('#admission_type option[value=1]').attr('selected','selected');
       }else if(res_data['filters_selected'].admission_type == 2){
          $('#admission_type option[value=2]').attr('selected','selected');
       }
       if(res_data['filters_selected'].from_date){
          $('#from_date').val(res_data['filters_selected'].from_date);
          $('#to_date').val(res_data['filters_selected'].to_date);
          $('#reportrange span').html(moment(res_data['filters_selected'].from_date, 'DD-MM-YYYY').format("MMM D, YYYY") + ' - ' + moment(res_data['filters_selected'].to_date, 'DD-MM-YYYY').format("MMM D, YYYY"));
       }
       
       if(res_data['filters_selected'].components == 1){
        $('#components_include').prop('checked', true);
       }else{
        $('#components_include').prop('checked', false);
       }

       if(res_data['filters_selected'].components_v_h == 1){
        $('#components_v_h').prop('checked', true);
       }else{
        $('#components_v_h').prop('checked', false);
       }

       if(res_data['filters_selected'].components_exclude == 1){
        $('#components_exclude').prop('checked', true);
       }else{
        $('#components_exclude').prop('checked', false);
       }

       if(res_data['filters_selected'].include_delete == 1){
        $('#include_delete').prop('checked', true);
       }else{
        $('#include_delete').prop('checked', false);
       }

       if(res_data['filters_selected'].recon == 1){
        $('#recon').prop('checked', true);
        $('#reconLable').html('Recon');
       }else{
        $('#recon').prop('checked', false);
        $('#reconLable').html('Receipt');
       }

       if(res_data['filters_selected'].classId){
        var class_names = '<?php echo json_encode($classes) ?>';
        var class_names_arr = $.parseJSON(class_names);
        $("#classId option:selected").removeAttr("selected");
          for(var i=0;i<res_data['filters_selected'].classId.length;i++){
            $("#classId").find("option[value="+res_data['filters_selected'].classId[i]+"]").prop("selected", true)
            $("#classId").multiselect()
          }
          $('#classId').selectpicker('refresh');
       }
       if(res_data['filters_selected'].paymentModes){
        $("#paymentModes option:selected").removeAttr("selected");
          for(var i=0;i<res_data['filters_selected'].paymentModes.length;i++){
            // $(`#paymentModes option[value=${res_data['filters_selected'].paymentModes[i]}]`).attr('selected','selected');
            $("#paymentModes").find("option[value="+res_data['filters_selected'].paymentModes[i]+"]").prop("selected", true)
            $("#paymentModes").multiselect()
          }
        $('#paymentModes').selectpicker('refresh');
       }
       if(res_data['filters_selected'].fee_type){
        $("#fee_type option:selected").removeAttr("selected");
          for(var i=0;i<res_data['filters_selected'].fee_type.length;i++){
            $("#fee_type").find("option[value="+res_data['filters_selected'].fee_type[i]+"]").prop("selected", true)
            $("#fee_type").multiselect()
          }
        $('#fee_type').selectpicker('refresh');
       }
       if(res_data['filters_selected'].report_type){
        $(`input[value="${res_data['filters_selected'].report_type}"]`).attr('checked','checked');
       }
       $('#search').prop('disabled',true).val('Please wait...');
       get_daily_tx_report();
    }
  });
}

function get_predefined_filters(){
  $.ajax({
    url: '<?php echo site_url('feesv2/reports/get_predefined_filters'); ?>',
    type: 'post',
    success: function(data) {
       var res_data = JSON.parse(data);
       if(res_data != ''){
        var html = '';
        html += '<option value="">Select</option>';
        for(var i=0;i<res_data.length;i++){
          html += `<option value="${res_data[i].id}">${res_data[i].title}</option>`;
        }
        $('#filter_types').html(html);
       }
    }
  });
}

$('#update_filter').on('click',function(){
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var classId = $('#classId').val();
  var fee_type = $('#fee_type').val();
  var paymentModes = $('#paymentModes').val();
  var admission_type = $('#admission_type').val();
  var components = 0;
  if ($('#components_include').is(':checked')) {
    components = 1;
  }
  var components_v_h = 0;
  if ($('#components_v_h').is(':checked')) {
    components_v_h = 1;
  }
  var components_exclude = 0;
  if ($('#components_exclude').is(':checked')) {
    components_exclude = 1;
  }

  var include_delete = 0;
  if ($('#include_delete').is(':checked')) {
    include_delete = 1;
  }

  var recon = 0;
  if ($('#recon').is(':checked')) {
    recon = 1;
  }

  var report_type = $('input[name="report_type"]:checked').val();

  var columns_activeArry = [];
  var table = $('#daily_dataTable').DataTable();
  table.columns().every(function (index) {
    if (this.visible()) {
      columns_activeArry.push(index); // Push the index of the visible column
    }
  });

  var filter_types_id = $('#filter_types').val();
  var report_type = $('input[name="report_type"]:checked').val();
  bootbox.confirm({
        title: "Update Filters",
        message: 'Are you sure, Do you want to update the filter?',

        className: "medium",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
            if(result) {
              $.ajax({
                      url: '<?php echo site_url('feesv2/reports/update_filters'); ?>',
                      type: 'post',
                      data: {'from_date':from_date,'to_date':to_date,'classId':classId,'fee_type':fee_type,'paymentModes':paymentModes,'admission_type':admission_type,'components':components,'components_v_h':components_v_h,'components_exclude':components_exclude,'include_delete':include_delete,'recon':recon,'report_type':report_type,'columns_activeArry':columns_activeArry,'filter_types_id':filter_types_id},
                      success: function(data) {
                        if (data) {
                            new PNotify({
                              title: 'Success',
                              text: 'Filters updated successfully',
                              type: 'success',
                            });
                            get_predefined_filters();
                          } else {
                            new PNotify({
                              title: 'Error',
                              text: 'Something went wrong',
                              type: 'error',
                            });
                          }
          
                        }
                });
            }
        }
    });
});

$('#clear_filter').on('click',function(){
  clear_filters();
});

function clear_filters(){
  $('#reload_filter').hide();
  $('#from_date').val(moment(new Date()).format("MMM D, YYYY"));
  $('#to_date').val(moment(new Date()).format("MMM D, YYYY"));
  $('#reportrange span').html(moment(new Date()).format("MMM D, YYYY") + ' - ' + moment(new Date()).format("MMM D, YYYY"));
  $("#classId").val('').trigger("change");
  $('.classId').selectpicker('refresh');
  $("#paymentModes").val('').trigger("change");
  $('#paymentModes').selectpicker('refresh');
  $("#fee_type").val('').trigger("change");
  $('#fee_type').selectpicker('refresh');
  $("#classId option:selected").removeAttr("selected");
  $("#paymentModes option:selected").removeAttr("selected");
  $("#fee_type option:selected").removeAttr("selected");
  $("#admission_type").val(0);
  $("#recon").prop('checked', false);
  $("#components_include").prop('checked', false);
  $("#components_exclude").prop('checked', true);
  $("#components_v_h").prop('checked', false);
  $("#include_delete").prop('checked', false);
  $('#type-2').prop('checked', true);
  $(".ranges ul li").removeClass("active");
  $(".ranges ul li:first").addClass("active");
  get_predefined_filters()
}

$('input[name="report_type"]').change(function() {
  get_daily_tx_report();
});

$(document).ready(function(){
  get_daily_tx_report();
})

function get_daily_tx_report() {
  $('#dataTableExport').html('');
  $('#dataTablePrint').html('');
  $('#slider').val('0');
  $(".day_book").html('');
  $(".day_book_summary").html('');
  $('#exportIcon').hide();
  $('#exportIcon_summary').hide();
  $(".loading-icon").show();
  $('#print_visible').hide();
  $('#print_summary').hide();
  $("#sliderDiv").hide();
  $('.prarthana_daily_reports').show();
  var from_date = $('#from_date').val();
  var to_date = $('#to_date').val();
  var fee_type = $('#fee_type').val();
  var classId = $('#classId').val();
  var paymentModes = $('#paymentModes').val();
  if (fee_type == null) {
    fee_type = ['All'];
  }
  var components = 0;
  if ($('#components_include').is(':checked')) {
    components = 1;
  }
  var components_v_h = 1;
  if ($('#components_v_h').is(':checked')) {
    components_v_h = 0;
  }
  var components_exclude = 0;
  if ($('#components_exclude').is(':checked')) {
    components_exclude = 1;
  }

  var include_delete = 0;
  if ($('#include_delete').is(':checked')) {
    include_delete = 1;
  }

  var recon = 0;
  if ($('#recon').is(':checked')) {
    recon = 1;
  }

  var column_selections = $('#column_selections').val();
  var admission_type = $('#admission_type').val();
  // if ($('#appInclude').is(':checked')) {
  //   fee_type.push('application');
  // }
  $('#fromDate').html(from_date);
  $('#toDate').html(to_date);

  $('#fromDate_summary').html(from_date);
  $('#toDate_summary').html(to_date);

  var report_type = $('input[name="report_type"]:checked').val();

  if (report_type == '2') {
    var url = '<?php echo site_url('feesv2/reports/generate_report_for_day_book_summary'); ?>';
  }else if(report_type == '3'){
    var url = '<?php echo site_url('feesv2/reports/generate_report_for_day_book_summary1'); ?>';
  }else if(report_type == '4'){
    var url = '<?php echo site_url('feesv2/reports/generate_report_for_installment_wise_day_bookV1'); ?>';
  }else{
    var url = '<?php echo site_url('feesv2/reports/generate_report_for_day_bookV1'); ?>';
  }
  var moresvc = '<?php $this->load->view('svg_icons/more.svg') ?>';

  $.ajax({
    url: url,
    type: 'post',
    data: {'from_date':from_date, 'to_date':to_date,'fee_type':fee_type,'classId':classId,'components':components,'components_v_h':components_v_h,'paymentModes':paymentModes,'components_exclude':components_exclude,'column_selections':column_selections,'include_delete':include_delete,'admission_type':admission_type,'recon':recon},
    success: function(data) {
      if (report_type == 2 || report_type == '3') {
       var daily_tx = JSON.parse(data);
        $('#exportIcon_summary').show();
        $('#print_summary').show();
        $('.day_book_summary').html(construct_summary_table(daily_tx));
        $("#sliderDiv").hide();
        var totalCollectedAmount = $('#totalCollectedAmount').val();
        // var targetValue = [];
        // for(var index in setColumnDef){
        //   targetValue.push({targets : setColumnDef[index], visible: true});
        // }
        var table = $('#daily_dataTable').DataTable({
          dom: 'lBfrtip',
          ordering:true,
          'columnDefs':  [
            { orderable: false, targets: 1 },
            {targets : 9, visible: false},
            {targets : 10, visible: false}
          ],
          stateSave:true,
          paging :true,
          "language": {
            "search": "",
            "searchPlaceholder": "Enter Search..."
          },
          "pageLength": 10,
          "bPaginate":true,
          scrollY:300,
          scrollX: true,
          scrollCollapse: true,
          buttons: [
            {
              extend: 'print',
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              titleAttr: 'Print',
              footer: true,
              exportOptions: {
                  columns: ':visible'
              },
              messageTop: function () {
                  var html = '';
                  html += `<div style="text-align: center; margin-bottom: 1rem;">
                      <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
                          Daily Transaction report
                      </h4>
                      <div>From ${from_date} To ${to_date}</div>
                  </div>`;
                  return html;
              },
              customize: function (win) {
                  var css = `
                      <style>
                          body { font-family: 'Poppins', sans-serif; padding: 20px; }
                          table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                          th, td { border: 1px solid #ddd; padding: 8px; font-size: 12px; white-space: nowrap !important; word-break: normal !important; }                          
                          @media print {
                              table { page-break-inside: auto }
                              tr { page-break-inside: avoid }
                          }
                      </style>
                  `;
                  $(win.document.head).append(css);
                  // Ensure footer shows only once
                  $(win.document.body).find('tfoot').css('display', 'table-row-group');
              }
            },
            {
              extend:    'excel',
              text:      '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              titleAttr: 'Excel',
              footer: true,
              exportOptions: {
                  columns: ':visible'
              },
              messageTop: function () {
                var html = '';
                html += 'Daily Fee Report From '+from_date+' To '+to_date+'';
                return html;
              },
              customize: function (xlsx) {
                var sheet = xlsx.xl.worksheets['sheet1.xml'];
                $('row c[r]', sheet).each(function () {
                    // Customize rows if needed.
                });
              }
            }
          ],

        //   "stateSaveCallback": function(settings, data) {
        //     var selection_columns  = data.columns;
        //     for(var k in selection_columns){
        //       if(selection_columns[k].visible == true){
        //         columnsTrueArry.push(k);
        //       }
        //     }
        // },

      });
      var buttonsColvis = new $.fn.dataTable.Buttons(table, {
       
       buttons: [
         {
         extend: 'colvis',
         text : '<span class="fa fa-columns"></span> Columns',
         className: 'btn btn-info'
         }
       ]
     }).container().appendTo($('#daily_dataTable_filter label'));

      new $.fn.dataTable.Buttons(table, {
        
        buttons: [
          {
            extend: 'print',
            text : '<span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print',
            className: 'more-menu-btn',
            footer: true,
            exportOptions: {
              columns: ':visible'
            },
            customize: function ( win ) {
              var exportBody = GetDataToExport();
              $(win.document.body).prepend(exportBody);
            },
            messageTop: function () {
              var html = '';
              html += '<center>Daily Fee Report <br>From '+from_date+' To '+to_date+'</center>';
              return html;
            },
          }
        ]
      }).container().appendTo($('#dataTablePrint'));


      new $.fn.dataTable.Buttons(table, {
        
        buttons: [
          {
            extend: 'excel',
            text : '<span style="margin-left: 1px;margin-right: 8px;" class="fa fa-file-excel-o"></span> Export',
            className: 'more-menu-btn',
            footer: true,
            exportOptions: {
              columns: ':visible'
            },
            messageTop: function () {
              var html = '';
              html += 'Daily Fee Report From '+from_date+' To '+to_date+'';
              return html;
            },
          }
        ]
      }).container().appendTo($('#dataTableExport'));

      }else{
        console.log('inss',data);
        $('#exportIcon').show();
        $('#print_visible').show();
        $("#sliderDiv").show();
        $(".day_book").html(data);
        
        var table = $('#details_summary_table').DataTable({
          dom: 'Bfrtip',
          paging :true,
          fixedHeader: true,
          "language": {
            "search": "",
            "searchPlaceholder": "Enter Search..."
          },
          "pageLength": 10,
          "bPaginate":true,
          buttons: [
            {
              extend:    'print',
              text:      '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              titleAttr: 'Print',
              footer: true,
              exportOptions: {
                columns: ':visible'
              }
            },
            {
              extend:    'excel',
              text:      '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              titleAttr: 'Excel',
              footer: true,
              exportOptions: {
                  columns: ':visible'
              }
            }
          ],
      });
        var buttonsColvis = new $.fn.dataTable.Buttons(table, {
        
        buttons: [
          {
          extend: 'colvis',
          text : '<span class="fa fa-columns"></span> Columns',
          className: 'btn btn-info'
          }
        ]
      }).container().appendTo($('#details_summary_table_filter label'));
      
      }
      $(".loading-icon").hide();
      $('#search').prop('disabled',false).val('Get Report');
      add_scroller('fee-container-scroll');
    },


  });
};

$('#daily_dataTable').on('page.dt', function () {
  var info = table.page.info();
  if (info.page + 1 !== info.pages) {
    $('tfoot').hide(); // Hide footer for non-last pages
  } else {
    $('tfoot').show(); // Show footer for the last page
  }
});

function GetDataToExport() {
  return $('.table_summary_data').html();
}


  $(document).on("click", function(event){
    if(event.target.id != 'more-btn'){
      visible = false;
      menu.style.opacity=0;
      $('.more-menu').css('display', 'none');
    }
  });



 $(function() {
    $("#slider").on('change input', function() {
      var tableWidth = $(".table").width();
      var currentWidth = $('#report-container').width();
      var width = tableWidth;
      if (tableWidth > currentWidth) {
        width = $(".table").width() - $('#report-container').width();
      }
      var posLeft = $(this).val() * width / 100;
      $(".table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td").css('left', -posLeft);
    });
  });
function construct_summary_table(daily_tx) {
  //console.log(daily_tx);
  var loan_column = '<?php echo $this->settings->getSetting('loan_provider_charges') ?>';
  var fee_adjustment_amount = '<?php echo $this->settings->getSetting('fee_adjustment_amount') ?>';
  var fee_fine_amount = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
  var fee_ins_column = '<?php echo $this->settings->getSetting('fee_daily_tx_installment_column') ?>';
  var fee_createdBy_column = '<?php echo $this->settings->getSetting('fee_daily_tx_created_by_column') ?>';
  var fee_refund_amount = '<?php echo $this->settings->getSetting('fee_refund_amount_display') ?>';
  var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
  var school_short_name = '<?php echo $this->settings->getSetting('school_short_name') ?>';
  var report_type = $('input[name="report_type"]:checked').val();

  // var totalNonReconciledAmount = 0;
  var paymentTypeSummary = [];
  var paymentTypeExcess = [];

  for(var i=0; i < daily_tx.length; i++){

    if (!(paymentTypeSummary).hasOwnProperty(daily_tx[i].payment_type)) {
      paymentTypeSummary[daily_tx[i].payment_type] = [];
      paymentTypeSummary[daily_tx[i].payment_type]['total_collected_amount'] = 0;
    }
    paymentTypeSummary[daily_tx[i].payment_type]['total_collected_amount'] += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);

    if(daily_tx[i].fee_type =='Excess Fee'){
      if (!(paymentTypeExcess).hasOwnProperty(daily_tx[i].payment_type)) {
        paymentTypeExcess[daily_tx[i].payment_type] = [];
        paymentTypeExcess[daily_tx[i].payment_type]['total_collected_amount'] = 0;
      }
      paymentTypeExcess[daily_tx[i].payment_type]['total_collected_amount'] += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);
    }


  }

  console.log(paymentTypeExcess);
  var html = '';
  html += '<div class="row">';

  var totalCollectedAmount = 0;
  var totalCollectedExcessAmount = 0;
  html += `<div class="col-md-5 table_summary_data">
              <table class="table table-bordered">

                <thead>
                  <tr>
                    <th>Payment Method</th>
                    <th>Amount</th>
                  </tr>
                </thead>

                <tbody>`;
  for(var paymentType in paymentTypeSummary){
    if(paymentType != 999){
      totalCollectedAmount += parseFloat(paymentTypeSummary[paymentType].total_collected_amount);
      html += `<tr>
              <th>${payment_type_new(paymentType, 0, school_short_name)}</th>
              <td>${numberToCurrency(paymentTypeSummary[paymentType].total_collected_amount)}</td>
            </tr>`;
    }
  }  
  html += `  </tbody>
            <thead>
            <tr>
                <th>Total</th>
                <th>${numberToCurrency(parseFloat(totalCollectedAmount))}</th>
              </tr>
            </thead>
        <tbody>`;
  for(var paymentType in paymentTypeSummary){
    if(paymentType == 999){
      totalCollectedAmount += 0;
      html += `<tr>
              <th>${payment_type_new(paymentType, 0, school_short_name)} (Debited)</th>
              <td>(${numberToCurrency(paymentTypeSummary[paymentType].total_collected_amount)})</td>
            </tr>`;
    }
  }  
  html += `  </tbody>

          </table>

      </div>`;
console.log(paymentTypeExcess);
if(paymentTypeExcess.length > 0){
  html += `<div class="col-md-5">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Excess Payment Method</th>
                  <th>Collected Total</th>
                </tr>
              </thead>

              <tbody>`;
for(var paymentType in paymentTypeExcess){
    totalCollectedExcessAmount += parseFloat(paymentTypeExcess[paymentType].total_collected_amount);
    html += `<tr>
            <th>${payment_type_new(paymentType, 0, school_short_name, '')}</th>
            <td>${numberToCurrency(paymentTypeExcess[paymentType].total_collected_amount)}</td>
          </tr>`;
}
html += `  </tbody>
          <thead>
          <tr>
              <th>Excess Amount Total</th>
              <th>${numberToCurrency(parseFloat(totalCollectedExcessAmount))}</th>
            </tr>
          </thead>
        </table>

    </div>`;
}
  html += '<input type="hidden" id="totalCollectedAmount" value='+numberToCurrency(totalCollectedAmount)+' >';
  
  html += '</div>';

  html += '<div style="justify-content: space-between!important;display: flex!important;" class="d-flex justify-content-between align-items-center"><p><b style="color: red;"> </b><b style="color: red;"> </b></p><div class="d-flex align-items-center" style="display: flex!important;"><div class="form-group" id="range-input" style="width: 300px;"></div></div></div>';
  html += '<div class="row table-responsive" id="fee-container-scroll">';

  html +='<table class="table table-bordered " id="daily_dataTable" style="width:100%; white-space: nowrap;">';
  html +='<thead>';
  html +='<tr>';
  html +='<th>#</th>';
  html +='<th>Receipt Date</th>';
  html +='<th>Receipt Number</th>';
  // html +='<th class="printShow" style="display:none;" >Receipt Number</th>';
  if (report_type == 2) {
    html +='<th>Admission Number</th>';
    html +='<th>Enrollment No</th>';
  }
  html +='<th>Student Name</th>';
  html +='<th>Class / Section</th>';
  if (is_semester_scheme == 1) {
    html +='<th>Semester</th>';
  }
  if (report_type == 3 ) {
    html +='<th>Admission No</th>';
    html +='<th>Enrollment No</th>';
    html +='<th>Father</th>';
    html +='<th>Father Address</th>';
    html +='<th>Father PAN No.</th>';
    html +='<th>Father Aadhar No.</th>';
    html +='<th>Mother</th>';
    html +='<th>Mother PAN No.</th>';
    html +='<th>Mother Aadhar No.</th>';
  }
  html +='<th>Fee Type</th>';
  html +='<th>Payment Method</th>';
  html +='<th>Bank Name</th>';
  html +='<th>DD/Cheque Date</th>';
  html +='<th>Reference No.</th>';
  html +='<th>Remarks</th>';

  if ($('#recon').is(':checked')) {
    html +='<th>Recon date</th>';
  }

  if (fee_ins_column) {
    html +='<th>Installments</th>';
  }
  html +='<th>Total Amount</th>';
  html +='<th>Concession</th>';
  if (fee_adjustment_amount) {
    html +='<th>Adjustment</th>';
  }
  if (loan_column) {
    html +='<th>Loan Provider Charges</th>';
  }
  if (fee_fine_amount) {
    html +='<th>Fine</th>';
  }
  html +='<th>Discount</th>';
  html +='<th>Collected Amount</th>';
  // if (fee_refund_amount) {
  //   html +='<th>Refund Amount</th>';
  // }
  if (fee_createdBy_column) {
    html +='<th>Collected By</th>';
  }
  html +='</tr>';
  html +='</thead>';
  html +='<tbody>';
  var totalAmount = 0;
  var totalCon = 0;
  var totalAdjust = 0;
  var totalLoan = 0;
  var totalFine = 0;
  var totalDiscount = 0;
  var totalColleted = 0;
  console.log(daily_tx);
  for(var i=0; i < daily_tx.length; i++){
    var urlStdhistory = '';
    if (daily_tx[i].student_id != undefined) {
      var feev1settings = '<?php echo $this->settings->getSetting('fee_collection_v1') ?>';
      if(daily_tx[i].fee_type == 'Excess Fee'){
        urlStdhistory = '';
      }else{
        if (feev1settings== 1) {
          urlStdhistory = '<?php echo site_url('feesv2/fees_collection/fee_reciept_viewv1/') ?>'+daily_tx[i].transId+'/0';
        }else{
          urlStdhistory = '<?php echo site_url('feesv2/fees_collection/fee_reciept_view/') ?>'+daily_tx[i].transId+'/0';
        }
      }
      
    }
    
    if(daily_tx[i].payment_type == 999){
      totalAmount += 0;
      totalColleted += 0;
    }else{
      totalAmount += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount);
      totalColleted += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);
    }
    totalCon += parseFloat(daily_tx[i].concession_amount);
    totalAdjust += parseFloat(daily_tx[i].adjustment_amount);
    totalLoan += parseFloat(daily_tx[i].loan_provider_charges);
    totalFine += parseFloat(daily_tx[i].fine_amount);
    totalDiscount += parseFloat(daily_tx[i].discount_amount);
   
    html +='<tr>';
    html +='<td>'+(i+1)+'</td>';
    html +='<td>'+daily_tx[i].receipt_date+'</td>';

    // html +='<td><a class="printHide" style="border: none;" target="_blank" href='+urlStdhistory+'>'+daily_tx[i].receipt_number+'</a><span class="printShow" style="display:none;"  >'+daily_tx[i].receipt_number+'</span></td>';

    html +='<td class="printHide" ><a class="printHide" style="border: none;" target="_blank" href='+urlStdhistory+'>'+daily_tx[i].receipt_number+'</a></td>';

    // html +='<td class="printShow" style="display:none;" >'+daily_tx[i].receipt_number+'</td>';
    if (report_type == 2) {
      if(daily_tx[i].admission_no == undefined){
        html +='<td>-</td>';
      }else{
        html +='<td>'+daily_tx[i].admission_no+'</td>';
      }
      html +='<td>'+daily_tx[i].enrollment_number+'</td>';
    }
    html +='<td>'+daily_tx[i].student_name+'</td>';
    if (daily_tx[i].class_name == null) {
      html +='<td></td>';
    }else{
      html +='<td>'+daily_tx[i].class_name+''+daily_tx[i].sectionName+'</td>';
    }

    if (is_semester_scheme == 1) {
      if (daily_tx[i].semester == null) {
        html +='<td></td>';
      }else{
        html +='<td>'+daily_tx[i].semester+'</td>';
      }
    }
    if (report_type == 3) {
      html +='<td>'+daily_tx[i].admission_no+'</td>';
      html +='<td>'+daily_tx[i].enrollment_number+'</td>';
      html +='<td>'+daily_tx[i].fName+'</td>';
      html +='<td>'+daily_tx[i].address+'</td>';
      html +='<td>'+daily_tx[i].f_pan_number+'</td>';
      html +='<td>'+daily_tx[i].f_aadhar_no+'</td>';
      html +='<td>'+daily_tx[i].mName+'</td>';
      html +='<td>'+daily_tx[i].m_pan_number+'</td>';
      html +='<td>'+daily_tx[i].m_aadhar_no+'</td>';
    }
    html +='<td>'+daily_tx[i].fee_type+'</td>';
    html +='<td>'+ payment_type_new(daily_tx[i].payment_type, daily_tx[i].reconciliation_status, school_short_name, daily_tx[i].online_tx_payment_mode)+'</td>';
    if(daily_tx[i].bank_name == null){
      daily_tx[i].bank_name = '';
    }
    html +='<td>'+daily_tx[i].bank_name+'</td>';
    if (daily_tx[i].payment_type == 1 || daily_tx[i].payment_type == 4 || daily_tx[i].payment_type == 8 || daily_tx[i].payment_type == 67) {
      html +='<td>'+daily_tx[i].cheque_or_dd_date+'</td>';
    }else{
      html +='<td></td>';
    }
    var remarks = '';
    var cheque_dd_nb_cc_dd_number = '';
    if (daily_tx[i].remarks != null) {
      remarks = daily_tx[i].remarks
    }
    if (daily_tx[i].cheque_dd_nb_cc_dd_number != null) {
      cheque_dd_nb_cc_dd_number = daily_tx[i].cheque_dd_nb_cc_dd_number
    }
    html +='<td>'+cheque_dd_nb_cc_dd_number+'</td>';
   
    html +='<td>'+remarks+'</td>';
    var reconDate = daily_tx[i].recon_submitted_on;
    if (daily_tx[i].recon_submitted_on == null || daily_tx[i].recon_submitted_on == '00-00-0000' || daily_tx[i].recon_submitted_on == undefined ) {
      reconDate = '';
    }
    if ($('#recon').is(':checked')) {
      html +='<td>'+reconDate+'</td>';
    }
    
    if (fee_ins_column) {
      if (daily_tx[i].ins_name == undefined) {
        html +='<td>-</td>';
      }else{
        html +='<td>'+daily_tx[i].ins_name+'</td>';
      }
    }
    if(daily_tx[i].payment_type == 999){
      html +='<td>('+numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount))+')</td>';
    }else{
      html +='<td>'+numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount))+'</td>';
    }
    
    
    html +='<td>('+numberToCurrency(daily_tx[i].concession_amount)+')</td>';
    if (fee_adjustment_amount) {
      html +='<td>('+numberToCurrency(daily_tx[i].adjustment_amount)+')</td>';
    }
    if (loan_column) {
      html +='<td>('+numberToCurrency(daily_tx[i].loan_provider_charges)+')</td>';
    }
    if (fee_fine_amount) {
      html +='<td>'+numberToCurrency(daily_tx[i].fine_amount)+'</td>';
    }
    html +='<td>('+numberToCurrency(daily_tx[i].discount_amount)+')</td>';

    if(daily_tx[i].payment_type == 999){
      html +='<td>('+numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount))+')</td>';
    }else{
      html +='<td>'+numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount))+'</td>';
    }
    
   
    if (fee_createdBy_column) {
      if (daily_tx[i].collected_name == undefined) {
        html +='<td>-</td>';
      }else{
        html +='<td>'+daily_tx[i].collected_name+'</td>';
      }
    }
    // if (fee_refund_amount) {
    //   html +='<th>'+daily_tx[i].refund_amount+'</th>';
    // }
    html +='</tr>';
  }
  html += '</tbody>';
  html +='<tfoot>';
  html +='<tr>';
  var colspan = '13';

  if ($('#recon').is(':checked')) {
    colspan = '14';
  }

  if (fee_ins_column) {
    colspan ='14';
  }
  if (is_semester_scheme == 1) {
    colspan ='14';
  }
  if (fee_ins_column && is_semester_scheme) {
     colspan = '15';
  }
  if(report_type == 3){
    colspan = parseInt(colspan) + 8 -1;
  }
  // for (let index = 0; index < colspan; index++) {
   
  //   if(index === colspan - 1){
  //     html +='<th>Total</th>';
  //   }else{
  //     html +='<th></th>';
  //   }
  // }
  html +='<th colspan="'+colspan+'" style="text-align: right;" class="text-right"></th>';
  html +='<th>'+numberToCurrency(totalAmount)+'</th>';
  html +='<th>'+numberToCurrency(totalCon)+'</th>';
  
  if (fee_adjustment_amount) {
    html +='<th>'+numberToCurrency(totalAdjust)+'</th>';
  }
  if (loan_column) {
    html +='<th>'+numberToCurrency(totalLoan)+'</th>';
  }
  if (fee_fine_amount) {
   html +='<th>'+numberToCurrency(totalFine)+'</th>';
  }
  html +='<th>'+numberToCurrency(totalDiscount)+'</th>';
  html +='<th>'+numberToCurrency(totalColleted)+'</th>';
  if (fee_createdBy_column) {
    html +='<th>-</th>';
  }
 
  html +='</tr>';

  html +='</tfoot>';
  html += '</table>';
  html += '</div>';
  return html;
}

function payment_type_new($paymentType, $reconStatus, $school_short_name, online_tx_mode) {
    $NC = '';          
    if ($reconStatus == 1) {
      $NC = '<span style="color:red;"> <b> (N/C) </b><span>';
    }else if($reconStatus == 2){
      $NC = '<span> <b> (C) </b><span>';
    }
    var txmode = '<span style="color:red;">( <b>'+online_tx_mode+' )</b><span>';
    if(online_tx_mode == undefined){
      txmode ='';
    }
    switch ($paymentType) {
      case '1':
        $pValue = 'DD';
        break;  
      case '2':
        $pValue = 'Credit Card';
        break;
      case '3':
        $pValue = 'Debit Card';
        break;
      case '4':
        $pValue = 'Cheque ' +$NC;
        break;
      case '5':
        $pValue = 'Wallet Payment';
        break;
      case '6':
        $pValue = 'Challan ' +$NC;;
        break;
      case '7':
        $pValue = 'Card (POS)';
        break;
      case '8':
        $pValue = 'Net Banking ' +$NC;;
        break;
      case '9':
        $pValue = 'Cash';
        break;
      case '10':
        $pValue = 'Online Payment <br>'+txmode;
        break;
     case '11':
        $pValue = 'UPI';
        break;
      case '12':
        $pValue = 'Loan';
        break;
      case '13':
        $pValue = 'Loan';
        break;
      case '20':
        $pValue = 'St.Marys A/C';
        break;
      case '21':
        $pValue = 'Mary Madha A/C';
        break;
      case '22':
       if ($school_short_name =='advitya' || $school_short_name =='advityadegree') {
          $pValue = 'QR SCAN';
        }else{
          $pValue = 'Adjusted';
        }
        break;
      case '999':
        $pValue = 'Excess Amount';
        break;
      case '777':
        $pValue = 'Online Challan Payment';
        break;
      case '30':
        $pValue = 'Transfer from Indus';
        break;
      case '31':
        $pValue = 'Bank Deposit';
        break;
      case '32':
        $pValue = 'HDFC  net banking';
        break;
      case '33':
        $pValue = 'BOB 145 net banking';
        break;
      case '34':
        $pValue = 'BOB 066 net banking';
        break;
      case '35':
        $pValue = 'Airpay';
        break;
      case '36':
				$pValue = 'Online (Manual)';
				break;
      case '66':
        $pValue = 'Grayquest';
        break;
      case '67':
        $pValue = 'DRCC';
        break;
      case '68':
        $pValue = 'CS';
        break;
      default:
        $pValue = '';
        break;
    }
    return $pValue;
}

function payment_type($paymentType, $reconStatus, $bankName, $chqDDDate, $chqDDCCNumber, $school_short_name) {
    $NC = '';          
    if ($reconStatus == 1) {
      $NC = '<span style="color:red;"> <b> (N/C) </b><span>';
    }else if($reconStatus == 2){
      $NC = '<span> <b> (C) </b><span>';
    }
    switch ($paymentType) {
      case '1':
        $pValue = 'DD '+$NC+'<br> <span"> <b> Bank Name </b> : '+$bankName+'<br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;  
      case '2':
        $pValue = 'Credit Card '+$NC;
        break;
      case '3':
        $pValue = 'Debit Card'+$NC;
        break;
      case '4':
        $pValue = 'Cheque '+$NC+'<br> <span"> <b> Bank Name </b> : '+$bankName+'<br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;
      case '5':
        $pValue = 'Wallet Payment '+$NC;
        break;
      case '6':
        $pValue = 'Challan '+$NC;
        break;
      case '7':
        $pValue = 'Card (POS) '+$NC;
        break;
      case '8':
        $pValue = 'Net Banking '+$NC+' <br> <span"> <b> Bank Name </b> : '+$bankName+'<br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;
      case '9':
        $pValue = 'Cash';
        break;
      case '10':
        $pValue = 'Online Payment';
        break;
     case '11':
        $pValue = 'UPI<br><span><b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;
      case '12':
        $pValue = 'Loan';
        break;
      case '13':
        $pValue = 'Loan';
        break;
      case '20':
        $pValue = 'St.Marys A/C '+$NC+' <br> <span"> <b> Bank Name </b> : '+$bankName+'<br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;
      case '21':
        $pValue = 'Mary Madha A/C '+$NC+' <br> <span"> <b> Bank Name </b> : '+$bankName+'<br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        break;
      case '22':
       if ($school_short_name =='advitya' || $school_short_name =='advityadegree') {
          $pValue = 'QR SCAN <br> <b> Date </b> : '+$chqDDDate+'<br> <b> Number </b> : '+$chqDDCCNumber+'</span>';
        }else{
          $pValue = 'Adjusted';
        }
        break;
      case '66':
        $pValue = 'Grayquest';
        break;
      case '67':
        $pValue = 'DRCC';
        break;
      case '68':
        $pValue = 'CS';
        break;
      case '999':
        $pValue = 'Excess Amount';
        break;
      default:
        $pValue = '';
        break;
    }
    return $pValue;
}
function numberToCurrency(amount) {
  var formatter = new Intl.NumberFormat('en-IN', {
    // style: 'currency',
    currency: 'INR',
  });
  return formatter.format(amount);
}
</script>

<style type="text/css">
 table.fee_export_excel{
  box-sizing: border-box;
  border-collapse: collapse;
}
 .fee_export_excel tr, .fee_export_excel td, .fee_export_excel th {
  border: 1px solid #ddd;
  position: relative;
  max-width: 100%;
  white-space: nowrap;
  /*padding: 10px;*/
}

.day_book tr, .day_book td, .day_book th{
  max-width: 100%;
  white-space: nowrap;
}

.vertical{
  padding: 10rem 0px !important;
}

.verticalTableHeader_1 {
  text-align:center;
  /*white-space:none;*/
/*  g-origin:50% 50%;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);*/
}

.verticalTableHeader_1 p {
  margin:0 -100% ;
  display:inline-block;
  transform: rotate(-90deg);
  /*white-space: nowrap;*/
 
  bottom: 0;
  left: 50%;
    
}
.verticalTableHeader_1 p:before{
  content:'';
  width:0;
  padding-top:110%;
  display:inline-block;
  vertical-align:middle;
}
.headerBottom{
  position:relative;
  height:350px;
  width:50px;
}
.headerBottom p{
  /*position:absolute;*/
  /*bottom:50%;*/
  /*left:-290%;*/
  width:0px;
  line-height:2;
  margin:0;
  text-align:left;
  transform:rotate(-90deg);
}


.fee_export_excel th span {
  transform-origin: 0 50%;
  transform: rotate(-90deg); 
  white-space: nowrap; 
  display: block;
  position: absolute;
  bottom: 0;
  left: 50%;
}
input[type = 'range'] {
  margin: 0 auto;
  width: 100px;
}


.buttons-print{
  border: none !important;
    background: none  !important;
    padding: 0  !important;
    margin: 0  !important;
}
.buttons-excel{
  border: none !important;
  background: none  !important;
  padding: 0  !important;
  margin: 0  !important;
}
.dt-buttons{
  text-align: right;
  float:right;
  /*position: absolute;*/
}

/*#sliderDiv {
  text-align: center;
  width: 350px;
  float: right;
}
*/
</style>

 <script>

  
function printDayBookReport() {
    var table = $('#daily_dataTable').DataTable();
    if (!table) {
        alert('No report to print!');
        return;
    }

    var data = table.rows({ search: 'applied' }).data();

    var thead = '<thead><tr>';
    table.columns().every(function() {
        if (this.visible()) {
            var colHeader = $(this.header()).text();
            thead += '<th>' + colHeader + '</th>';
        }
    });
    thead += '</tr></thead>';

    var tbody = '<tbody>';
    for (var i = 0; i < data.length; i++) {
        tbody += '<tr>';
        table.columns().every(function(idx) {
            if (this.visible()) {
                var cell = table.cell(i, idx).data();
                cell = $('<div>').html(cell).text();
                tbody += '<td>' + cell + '</td>';
            }
        });
        tbody += '</tr>';
    }
    tbody += '</tbody>';

    var tfoot = '';
    if ($(table.table().footer()).length) {
        tfoot = '<tfoot>' + $(table.table().footer()).html() + '</tfoot>';
    }

    let summaryTableHtml = '';
    let summaryTableDiv = document.querySelector('.table_summary_data');
    if (summaryTableDiv) {
        summaryTableHtml = summaryTableDiv.outerHTML;
    }

    var schoolName = "<?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>";

    var printHeader = `
        <div style="text-align: center; margin-bottom: 1rem;">
            <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
                ${schoolName}
            </h3>
            <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
                Daily Transaction report
            </h4>
        </div>
    `;

    var printContent = `
        ${printHeader}
        ${summaryTableHtml}
        <div class="table-responsive">
            <table style="width:100%;border-collapse:collapse" border="1">
                ${thead}
                ${tbody}
                ${tfoot}
            </table>
        </div>
    `;

    // Print window
    var printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Daily Fee Report</title>
            <style>
                body { font-family: 'Poppins', sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; font-size: 12px; white-space: nowrap !important; word-break: normal !important; }
                h3, h4, h5 { margin: 15px 0; }
                @media print {
                    table { page-break-inside: auto }
                    tr { page-break-inside: avoid }
                }
            </style>
        </head>
        <body>
            ${printContent}
            <script>
                window.onload = function() { window.print(); };
                window.onafterprint = function() { window.close(); };
            <\/script>
        </body>
        </html>
    `);
    printWindow.document.close();
}
  // $(function() {
    // $("#slider").on('change input', function() {
    //   console.log($('.table').width());
    //   var tableWidth = $(".table").width();
    //   var currentWidth = $('.day_book').width();
    //   var width = tableWidth;
    //   if (tableWidth > currentWidth) {
    //     width = $(".table").width() - $('.day_book').width();
    //   }
    //   var posLeft = $(this).val() * width / 100;
    //   // console.log(posLeft);
    //   $(".table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td").css('left', -posLeft);
    // });
  // });

  // function printProfile(){
  //   var restorepage = document.body.innerHTML;
  //   $('#print_visible').css('display','block');
  //   $('.headerBottom').css('position','relative');
  //   $('.headerBottom').css('height','350px');
  //   $('.headerBottom').css('width','50px');
  //   $('.headerBottom p').css('position','absolute');
  //   $('.headerBottom p').css('bottom','50%');
  //   $('.headerBottom p').css('left','-290%');
  //   $('.headerBottom p').css('width','350px');
  //   $('.headerBottom p').css('line-height','2');
  //   $('.headerBottom p').css('margin','0');
  //   $('.headerBottom p').css('text-align','left');
  //   $('.headerBottom p').css('transform','rotate(-90deg)');
  //   $('#exportIcon').css('display','none');
  //   $('.printHide').css('display','none');
  //   $('.printShow').css('display','block');
  //   var printcontent = document.getElementById('printArea').innerHTML;
  //   document.body.innerHTML = printcontent;
  //   window.print();
  //   document.body.innerHTML = restorepage;
  // }

// function print_daily_summary(){
//     var restorepage = document.body.innerHTML;
//     $('#print_summary').css('display','block');
//     $('#exportIcon_summary').css('display','none');
//     $('.printHide').css('display','none');
//     $('.printShow').css('display','block');
//     var printcontent = document.getElementById('printArea_summary').innerHTML;
//     document.body.innerHTML = printcontent;
//     window.print();
//     document.body.innerHTML = restorepage;
//   }

  // function exportToExcel_daily(){
  //   var htmls = "";
  //   var uri = 'data:application/vnd.ms-excel;base64,';
  //   var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
  //   var base64 = function(s) {
  //       return window.btoa(unescape(encodeURIComponent(s)))
  //   };

  //   var format = function(s, c) {
  //       return s.replace(/{(\w+)}/g, function(m, p) {
  //           return c[p];
  //       })
  //   };

  //   var summaryTable = $("#print_visible").html();
  //   var mainTable = $(".day_book").html();

  //   htmls ='<br><br>'+ summaryTable  + '<br><br>' + mainTable;

  //   var ctx = {
  //     worksheet : 'Spreadsheet',
  //     table : htmls
  //   }

  //   var link = document.createElement("a");
  //   link.download = "export.xls";
  //   link.href = uri + base64(format(template, ctx));
  //   link.click();

  // }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


function exportToExcel_daily_summary(argument) {
     var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };
    var mainTable = $(".day_book").html();
    htmls = mainTable;
    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }
    var link = document.createElement("a");
    link.download = "Daily_tx_detailed_report.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();
}

$(".checkbox-menu").on("change", "input[type='checkbox']", function() {
   $(this).closest("li").toggleClass("active", this.checked);
});

$(document).on('click', '.allow-focus', function (e) {
  e.stopPropagation();
});

var el = document.querySelector('.more');
	var btn = el.querySelector('.more-btn');
	var menu = el.querySelector('.more-menu');
	var visible = false;

	function showMenu(e) {
		e.preventDefault();
		if (!visible) {
			visible = true;
      $('.more-menu').css('display', 'block');
			menu.style.opacity=1;
			el.classList.add('show-more-menu');
			menu.setAttribute('aria-hidden', false);
			// document.addEventListener('mousedown', hideMenu);
		}else{
			visible=false;
			menu.style.opacity=0;
      $('.more-menu').css('display', 'none');
		}
	}

	btn.addEventListener('click', showMenu);

</script>

<style>
  .more-menu {
		width: 100px;
	}

	/* More Button / Dropdown Menu */

	.more-btn,
	.more-menu-btn {
		background: none !important;
		border: 0 none !important;
		line-height: normal !important;
		overflow: visible !important;
		-webkit-user-select: none !important;
		-moz-user-select: none !important;
		-ms-user-select: none !important;
		width: 100% !important;
		text-align: left !important;
		outline: none !important;
		cursor: pointer !important;
	}

	.more-dot {
		background-color: #aab8c2;
		margin: 0 auto;
		display: inline-block;
		width: 7px;
		height: 7px;
		margin-right: 1px;
		border-radius: 50%;
		transition: background-color 0.3s;
	}

	.more-menu {
		position: absolute;
		top: 100%;
		z-index: 900;
		float: left;
		padding: 10px 0;
		margin-top: 9px;
		background-color: #fff;
		border: 1px solid #ccd8e0;
		border-radius: 4px;
		box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
		opacity: 0;
		transform: translate(0, 15px) scale(.95);
		transition: transform 0.1s ease-out, opacity 0.1s ease-out;
		/* pointer-events: none; */
	}

	.more-menu-caret {
		position: absolute;
		top: -10px;
		right: 12px;
		width: 18px;
		height: 10px;
		float: right;
		overflow: hidden;
	}

	.more-menu-caret-outer,
	.more-menu-caret-inner {
		position: absolute;
		display: inline-block;
		margin-left: -1px;
		font-size: 0;
		line-height: 1;
	}

	.more-menu {
		right: 0% !important;
		width: 150px;
	}

	.more-menu-caret-outer {
		border-bottom: 10px solid #c1d0da;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		height: auto;
		left: 0;
		top: 0;
		width: auto;
	}

	.more-menu-caret-inner {
		top: 1px;
		left: 1px;
		border-left: 9px solid transparent;
		border-right: 9px solid transparent;
		border-bottom: 9px solid #fff;
	}

	.more-menu-items {
		margin: 0;
		list-style: none;
		padding: 0;
	}

	.more-menu-item {
		display: flex;
	}

	.more-menu-btn {
		min-width: 100% !important;
		color: #66757f !important;
		cursor: pointer !important;
		display: block !important;
		font-size: 13px !important;
		line-height: 18px !important;
		padding: 5px 20px !important;
		position: relative !important;
		white-space: nowrap !important;
	}

	.more-menu-item:hover {
		background-color: #489fe5;
	}

	.more-menu-item:hover .more-menu-btn {
		color: #fff !important;
	}

	.more-btn:hover .more-dot,
	.show-more-menu .more-dot {
		background-color: #516471;
	}

	.show-more-menu .more-menu {
		opacity: 1;
		transform: translate(0, 0) scale(1);
		pointer-events: auto;
	}

   .dt-button-collection .buttons-columnVisibility:before,
.dt-button-collection .buttons-columnVisibility.active span:before {
	display:block;
	position:absolute;
	top:1.2em;
    left:0;
	width:12px;
	height:12px;
	box-sizing:border-box;
}

.dt-button-collection .buttons-columnVisibility:before {
	content:' ';
	margin-top:-6px;
	margin-left:10px;
	border:1px solid black;
	border-radius:3px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
	content:'\2714';
	margin-top:-13px;
	margin-left:12px;
	text-align:center;
	text-shadow:1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

.dt-button-collection .buttons-columnVisibility span {
    margin-left:20px;    
}
 .exportBtn{
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
  padding-top: 0.1rem !important;
  padding-bottom: 0.1rem !important;
  background: none !important;
  border: none !important;
}

.new_circleShape_res span{
  font-size: 16px;
  text-align: center;
  vertical-align: middle;
  line-height: 1.7rem !important;
}

.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2 !important;
		border: 1px solid #ccc !important;
		border-radius: 4px !important;
		margin-right: 5px !important;
	}
.dt-button-collection .buttons-print{
  padding: 0.5em 1em !important;
}

.dt-button-collection .buttons-excel{
  padding: 0.5em 1em !important;
}


  
  .btn-success{
    color: #fff !important;
    background-color: green !important;
    border-color: green !important;
    padding: 4px 14px !important;
  }
  .btn-info{
    color: #fff !important;
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    padding: 4px 14px !important;
  }
  .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary{
    color: #fff !important;
    background-color: #6893ca;
    border-color: #6893ca;
    border-radius: 1.2rem;
    padding: 4px 14px;
  }
  .dataTables_wrapper .dataTables_filter input{
    line-height: 1.5
  }
  .glyphicon-print:before{
    margin-right: 0.6rem;
  }

  .dt-button-collection .buttons-excel .fa-file-excel-o:before {
    margin-right: 0.6rem;
  }

  div.dt-button-background{
    background:none;
    z-index: 0;
  }
  div.dt-buttons>.dt-button, div.dt-buttons>div.dt-button-split .dt-button{
    border-radius: 8px !important;
    line-height: 20px;
  }
  .btn-primary{
    border-radius: 8px !important; 
  }
  .btn-info{
    border-radius: 8px !important; 
  }
  #daily_dataTable_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }
  #details_summary_table_filter .dt-button-collection{
    height: 300px;
    overflow: scroll;
  }
  
  #daily_dataTable_length{
    width: 100%;
    display: contents;
  }

  .dataTables_scrollBody::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
}

.dataTables_scrollBody::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
}
    .widthadjust{
        width: 50%;
        margin: auto;
    }

    div.dt-button-collection .dt-button {
      padding: 1px 1rem;
    }

    .bootbox-close-button{
      display: none;
    }
    .medium{
      width: 45%;
      margin: auto;
    }
</style>