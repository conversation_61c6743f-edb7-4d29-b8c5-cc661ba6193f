<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/'); ?>">Payroll dashboard</a></li>
    <li>Manage Investment Declaration </li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-4 pl-0">
                    <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>"><span class="fa fa-arrow-left"></span></a>Manage Investment Declaration </h3>
                </div>
                <div class="col-md-8 pr-0 d-flex justify-content-end">
                    <!-- <ul class="panel-controls"> -->
                        <!-- <li> -->
                            <!-- <div id="staff_add_and_filter"> -->
                                <div class="mr-2" id="refresh_page_btn_div">
                                    <button class="btn btn-primary pull-right" id="refresh_page_btn" onclick="refresh_page()" disabled>Refresh</button>
                                </div>
                                <div class="" id="add_staffs_to_tds_btn_div">
                                    <button class="btn btn-success pull-right" id="add_staffs_to_tds_btn" onclick="get_staffs_to_be_added_to_tds()">Add Missing Staff</button>
                                </div>
                                <!-- <div class="col-md-6">
                                    <select class="form-control" name="staff_status" onchange="get_income_declaration_details()" id="staff_status"> 
                                        <option value=""><?php //echo "Select Status" ?></option>
                                        <?php //foreach ($sStatus as $val => $name) { ?>
                                            <option <?php //echo ($val == 2) ? 'selected' : ''; ?> value="<?php //echo $val; ?>"><?php //echo $name; ?></option>
                                        <?php //} ?>
                                    </select>
                                </div> -->
                            <!-- </div> -->
                            <!-- <div>
                                <label class="my-2">Financial Year</label>
                            </div> -->
                            <div class="col-md-2">
                                <select class="form-control custom-select"  onchange="adjust_declaration_window_status();" name="schedule_year" id="schedule_year">
                                    <?php foreach ($financial_year as $key => $year) { 
                                        $selected = $schedule_year == $year->id ? 'selected' : ''?>
                                        <option value="<?php echo $year->id ?>" <?php echo $selected ?>><?php echo $year->f_year ?></option>';
                                    <?php }
                                        ?>
                                </select>
                            </div>
                        <!-- </li> -->
                    <!-- </ul> -->
                </div>
            </div>
        </div>

        <div class="panel-body" id="income_open" style="display:none;">
            <div class="col-md-12">
                <div class="d-flex flex-row">
                    <div class="py-2 mr-auto">
                        <select class="form-control custom-select" name="staff_status_income" onchange="change_income_status_payroll()" id="staff_status_income" style="width:250px;">
                            <option class="staff_status_options" value="">All</option>
                            <option class="staff_status_options" value="Open">Open</option>
                            <option class="staff_status_options" value="Submitted">Submitted</option>
                            <option class="staff_status_options" value="Approved">Approved</option>
                            <option class="staff_status_options" value="Reopened">Reopened</option>
                            <option class="staff_status_options" value="Reopened (Proof Submission)">Reopened (Proof Submission)</option>
                            <option class="staff_status_options" value="Submitted (Proof Submission)">Submitted (Proof Submission)</option>
                            <option class="staff_status_options" value="Approved (Proof Submission)">Approved (Proof Submission)</option>
                        </select>
                    </div>
                    <div class="p-2" id="close_income_declaration_text" style="display:none;">
                        <label class="form-group" style="vertical-align: -webkit-baseline-middle;">Income Declaration Window Closed</label>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="p-2">
                            <label class="form-group" id="stop_income_declaration_text" style="display:none">Close Income Declaration</label>
                        </div>
                        <div class="p-2" id="stop_income_declaration_switch" style="display:none;">
                            <label class="switch">
                            <input type="checkbox" id="staying_rented_house_checkbox" onclick="income_tax_declaration_close()" disabled>
                            <span class="slider round" style="margin-bottom: -10px;"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 d-flex justify-content-center align-items-center">
                <div id="loaderIconMID" class="loadingIconMID"></div>
            </div>
            <div class="col-md-12" style="margin-top:15px">
                <div id="add_btn_msg">
                    <p class="sub_header_note mb-0">
                        <strong>Note:</strong>
                        <ol style="padding-left: 20px;">
                            <li>
                                By clicking on 
                                <span style="font-weight:600;font-size:15px;color:blue">
                                    <i class="fa fa-plus" style="font-size: 12px; margin-right: 4px; padding-top: 2px;"></i>Apply New Regime
                                </span>, 
                                the <span style="font-weight:600;font-size:15px;color:blue">New Regime will be applied</span> for the respective staff.
                            </li>
                            <li>
                                <span style="font-weight:600;font-size:15px;color:blue">Reopening a Staff Investment Declaration</span> can be done <strong>only once</strong>.
                            </li>
                            <li>
                                <span style="font-weight:600;font-size:15px;color:red">Reopening (Proof Submission)</span> indicates that the respective 
                                <span style="font-weight:600;font-size:15px;color:blue">staff member's investment documents</span> have been 
                                <span style="font-weight:600;font-size:15px;color:red">Rejected</span>. Click on the status for more information.
                            </li>
                            <li>
                                <span style="font-weight:600;font-size:15px;">New Regime<span style="color:red"> (Changed)</span></span> indicates that the respective 
                                <span style="font-weight:600;font-size:15px;color:blue">staff member's tax regime</span> has been 
                                <span style="font-weight:600;font-size:15px;color:blue">Moved from Old Regime to New Regime</span>. Click on the status for more information.
                            </li>
                        </ol>
                    </p>
                </div>
                <div id="declartion_summary">
                </div>
                <div id="list_staff" class="table-responsive">
                </div>
            </div>
        </div>
        <div class="panel-body" id="income_lock" style="display:none;">
            <div class="no-data-display">
                Open Investment Declaration Window for your Staff
                <label class="switch" style="margin-left: 10px;">
                <input type="checkbox" id="income_lock_btn" onclick="incometax_unlock_btn()">
                <span class="slider round" style="margin-bottom: -10px;"></span>
                </label>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="staffTaxDetails" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document">
        <div class="modal-content">
        <div class="modal-header bg-body-secondary rounded-top">
            <h5 class="modal-title" id="staticBackdropLabel">Declaration By <span id="staff_name"></span></h5>
            <!-- <div class="col-md-2 d-flex align-items-center">
                <button id="exportButton" class="btn btn-primary">Export as PDF</button> -->
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="stafftaxDetailsCloseBtn1">
                    <span aria-hidden="true">&times;</span>
                </button>
            <!-- </div> -->
        </div>
        <div class="modal-body bg-body-secondary">
            <table class="table table-bordered" id="taxSummaryDetails" style=" /*box-shadow: #a9a9a9 -3px 0px 2px 0px;*/ border: 2px solid #a9a9a9;">
            </table>
            <table class="table table-bordered" id="taxDetailsTable" style="/*box-shadow: #808080 -3px 0px 2px 0px;*/ border: 2px solid #808080;">
            </table>
            <table class="table table-bordered" id="taxCalculationNewRegime" style="display: none; /*box-shadow: #404040 -3px 0px 2px 0px;*/border: 2px solid #404040;">
                <thead>
                    <tr>
                        <th colspan="4" style="text-align:center;">New Regime Calculation</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td width="55%"><strong>Income from Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Basic Salary</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_basic_salary" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">House Rent Allowance </td>
                        <td width="15%"><input type="text" id="nr_hra" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Allowance</strong> </td>
                        <td width="15%"><input type="text" id="nr_other_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Additional Allowance</strong> </td>
                        <td width="15%"><input type="text" id="nr_additional_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"><button id="view_additional_allowance_break_down_new" class="btn btn-secondary">More Info</button></td>
                    </tr>
                    <tr>
                        <td width="55%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_hra_other_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <?php if($collectPerkTaxMode == 'employee') { ?>
                        <tr>
                            <td width="55%"><strong>Perquisite Income</strong></td>
                            <td width="15%"></td>
                            <td width="15%"><input type="text" id="nr_perquisite_income" style="border:none;" readonly></td>
                            <td width="15%"></td>
                        </tr>
                    <?php } ?>
                    <tr>
                        <td width="55%"><strong>Less Exempted</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Standard deduction u/s 16 </td>
                        <td width="15%"><input type="text" id="nr_sd" style="border:none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Taxable Income From Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_income_from_salary_pt" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income from other Employer</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_other_employer_income" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Gross Salary Income</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_gross_salary_income" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Taxable Income as per New Regime</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_taxable_salary" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income Tax As Per New Regime</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_basic_tax" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Tax Rebate</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_tax_rebate" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Net Income Tax</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_net_income_tax" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Surcharge</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_surcharge" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_net_income_tax_surcharge" value="" style="border:none;" readonly></td>
                    </tr>

                    <tr>
                        <td width="55%"><strong>Cess</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_cess" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Tax Including Cess (New Regime)</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><strong><input type="text" id="nr_yearly_tds" value="" style="border:none; color: red;" readonly></strong></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>TDS Deducted from Other Employer</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="nr_other_employer_tds" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>TDS to be Deducted (New Regime)</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><strong><input type="text" id="nr_final_tds" value="" style="border:none; color:red;" readonly></strong></td>
                    </tr>
                </tbody>
            </table> 
            <table class="table table-bordered" id="taxCalculationOldRegime" style="display: none;  /*box-shadow: #404040 -3px 0px 2px 0px;*/border: 2px solid #404040;">
                <thead>
                    <tr>
                        <th colspan="4" style="text-align:center;">Old Regime Calculation</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td width="55%"><strong>Income from Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Basic Salary</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_basic_salary" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">House Rent Allowance </td>
                        <td width="15%"><input type="text" id="or_hra" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Allowance</strong> </td>
                        <td width="15%"><input type="text" id="or_other_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Additional Allowance</strong> </td>
                        <td width="15%"><input type="text" id="or_additional_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"><button id="view_additional_allowance_break_down_old" class="btn btn-secondary">More Info</button></td>
                    </tr>
                    <tr>
                        <td width="55%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_hra_other_allowance" style="border: none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <?php if($collectPerkTaxMode == 'employee') { ?>
                        <tr>
                            <td width="55%"><strong>Perquisite Income</strong></td>
                            <td width="15%"></td>
                            <td width="15%"><input type="text" id="or_perquisite_income" style="border:none;" readonly></td>
                            <td width="15%"></td>
                        </tr>
                    <?php } ?>
                    <tr>
                        <td width="55%"><strong>Less Exempted</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">House Rent Allowance u/s 10(13A) </td>
                        <td width="15%"><input type="text" id="or_hra_exemption" style="border:none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>				
                    <tr>
                        <td width="55%">Standard deduction u/s 16 </td>
                        <td width="15%"><input type="text" id="or_sd" style="border:none;" readonly></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_sd_hra" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income From Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_income_from_salary" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Deduction u/s 16: Professional Tax</strong></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_pt" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Taxable Income From Salary</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_income_from_salary_pt" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income from other Employer</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_other_employer_income" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Interest Paid On Home Loan</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_sec24" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Gross Salary Income</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_gross_salary_income" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Less : Deduction Under Chapter VI A</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80C</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80c" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80CCD</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80ccd" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80D</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80d" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80DD</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80dd" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80DDB</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80ddb" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80G</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80g" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80E</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80e" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80U</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80u" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 80TTA/B</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_80ttab" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <!-- <tr>
                        <td width="55%">Total Deductions</td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="total_80_deductions" style="border:none;" readonly></td>
                    </tr> -->
                    <tr>
                        <td width="55%"><strong>Less : Deduction Under Chapter III</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Deduction Under Section 10(5) - LTA / LTC</td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_lta" style="border:none;" readonly></td>
                        <td width="15%"></td>
                    </tr>
                    <tr>
                        <td width="55%">Total Deductions</td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="total_80_deductions" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Taxable Income as per Old Regime</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_taxable_salary" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income Tax As Per Old Regime</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_basic_tax" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Tax Rebate</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_tax_rebate" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Net Income Tax</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_net_income_tax" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Surcharge</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_surcharge" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_net_income_tax_surcharge" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Cess</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_cess" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>Tax Including Cess (Old Regime)</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><strong><input type="text" id="or_yearly_tds" value="" style="border:none; color:red;" readonly></strong></td>
                    </tr>
                    <tr>
                        <td width="55%">TDS Deducted from Other Employer</td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><input type="text" id="or_other_employer_tds" value="" style="border:none;" readonly></td>
                    </tr>
                    <tr>
                        <td width="55%"><strong>TDS to be Deducted (Old Regime)</strong></td>
                        <td width="15%"></td>
                        <td width="15%"></td>
                        <td width="15%"><strong><input type="text" id="or_final_tds" value="" style="border:none; color:red;" readonly></strong></td>
                    </tr>
                </tbody>
            </table> 
        </div>
        <div class="modal-footer bg-body-secondary rounded-bottom">
            <button type="button" class="btn btn-danger" data-dismiss="modal" id="stafftaxDetailsCloseBtn2">Close</button>
            <button type="button" class="btn btn-secondary" style="margin-bottom:4px;" id="reOpenDeclaration">Re-Open</button>
            <button type="button" class="btn btn-success" style="margin-bottom:4px;" id="approveDeclaration">Approve</button>
        </div>
        </div>
    </div>
</div>

<div class="modal fade" id="verify_not_approved" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important;">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header bg-body-secondary rounded-top">
                <h5 class="modal-title" id="staticBackdropLabel">Review Tax Declarations Before Closing</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="cancel_all_approve">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body bg-body-secondary">
                <div class="col-md-12">
                    <p class="sub_header_note">
                        <strong>Note:</strong> These are all the <span style="font-weight:700;font-size:16px;color:blue">Not Approved</span> tax declarations. You can <span style="font-weight:700;font-size:16px;color:blue">approve all</span> by clicking on "Approve All" button.
                    </p>
                </div>
                <div class="col-md-12">
                    <div class="table-resposinve">
                        <table id="filtered_list_tab" class="table table-bordered"> </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-body-secondary rounded-bottom">
                <button type="button" class="btn btn-danger" data-dismiss="modal" id="close_all_approve">Close</button>
                <button type="button" class="btn btn-success" style="margin-bottom:4px;" id="approveAllDeclaration" onclick="validate_approve_all()">Approve All</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addStaffsToTds" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog" style="margin-top: 5%;">
        <div class="modal-content" style="width: 50%; margin: auto;">
            <div class="modal-header bg-body-secondary rounded-top">
                <h5 class="modal-title" id="staticBackdropLabel">Add Missing Staff</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="reset_add_staffs_modal()">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="add_btn_msg">
                    <p class="sub_header_note">
                        <strong>Note:</strong><span style="font-weight:600;font-size:15px;color:blue">Selected Staff</span> will be <span style="font-weight:600;font-size:15px;color:blue">Added</span> to the selected <span style="font-weight:600;font-size:15px;color:blue">Financial Year Tax Declarations</span>
                    </p>
                </div>
                <div class="row col-md-12 pr-0">
                    <div class="mr-2">
                        <label for="">Select Staff: </label>
                    </div>
                    <div id="staff_details" class="ml-2 col-md-10 pr-0"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="reset_add_staffs_modal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="add_staffs_to_tds()" id="add_staffs_to_tds">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="staffInfoModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="width: 80%; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel">Staff Information</h5>
                <button type="button" class="close" id="email_close_btn" data-dismiss="modal" aria-label="Close" onclick="resetStaffInfoModal()">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="staffDetailsMessage">
                    <p class="sub_header_note">
                        <strong>Note:</strong> The selected staff will be processed accordingly.
                        <span id="proofSubmissionReopenMessage" style="display: none;">
                            Where
                            <span style="font-weight:600;font-size:15px;color:blue">The tax declaration will be reopened for investment proof document submission.</span>
                        </span>
                    </p>
                </div>
                <div id="regimeCount" class="col-md-12 pl-0 mb-2 d-none justify-content-start align-items-center">
                    <div class="">Total: <b><span id="totalRegimeCount"></span></b></div>
                    <div class="ml-2">New Regime: <b><span id="newRegimeCount"></span></b></div>
                    <div class="ml-2">Old Regime: <b><span id="oldRegimeCount"></span></b></div>
                </div>
                <div id="selectRegimeDiv" class="col-md-12 pl-0 d-none justify-content-start align-items-center mb-2">
                    <label>Select Regime:</label>
                    <select class="form-control col-md-2 ml-2" id="regimeForReopenProofSubmission" class="form-control" onchange="filterTableByRegime()">
                        <!-- <option value="0">All</option> -->
                        <!-- <option value="1">New Regime</option> -->
                        <option value="2" selected>Old Regime</option>
                    </select>
                </div>
                <div class="table-resposinve col-md-12 pl-0" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-bordered" id="staffInfoTable" style="width: 100%; border-collapse: collapse; border-top: 2px solid black;">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Staff Name</th>
                                <th id="staffSelectedRegime" style="display: none;">Regime</th>
                                <th id="staffdeclarationStatus" style="display: none;">Declaration Status</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Rows will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" id="email_cancel_btn" data-dismiss="modal" onclick="resetStaffInfoModal()">Close</button>
                <button type="button" class="btn btn-primary mb-1" id="confirmBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="attachmentsModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
    <div class="modal-dialog modal-dialog-scrollable" role="document" style="margin-top: 5% !important; margin: auto; width: 85%;">
        <div class="modal-content" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.5)">
        <div class="modal-header">
            <h5 class="modal-title" id="attachmentsModalLabel"><b><span id="staffName"></span> Investment Proof Attachments</b></h5>
            <button type="button" id="closeAttachmentModal" class="close" data-bs-dismiss="modal" aria-label="Close" onclick="closeProofAttachmentsModal()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="col-md-12 mb-2 d-flex justify-content-end align-items-center">
                <button class="btn btn-info" id="viewInvestmentChangesBtn">View Investment Changes</button>
                <button class="btn btn-primary ml-2" id="viewTaxCalculationBtn">View Tax Calculations</button>
            </div>
            <table class="table table-bordered">
            <thead>
                <tr>
                <th>#</th>
                <th>Investment</th>
                <th>Amount</th>
                <th>File Name</th>
                <th>Status</th>
                <th>Approved / Rejected By</th>
                <th>Approved / Rejected On</th>
                <th>Actions</th>
                </tr>
            </thead>
            <tbody id="attachmentsTableBody">
                <!-- Dynamic content goes here -->
            </tbody>
            </table>
        </div>
        </div>
    </div>
</div>

<div class="modal fade" id="viewStaffPayrollEditHistory" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
    <div class="modal-dialog modal-dialog-scrollable" role="document" style="margin-top: 5% !important; margin: auto; width: 85%;">
        <div class="modal-content" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.5)">
            <div class="modal-header">
                <h5 class="modal-title" id="viewStaffPayrollEditHistoryLabel"><b><span id="staffName"></span> Payroll Edit History</b></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" onclick="closeStaffPayrollHistoryModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table id="viewStaffHistoryTable" style="" class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="white-space: nowrap;">#</th>
                            <th style="width: 40%">Old Data</th>
                            <th style="width: 40%">New Data</th>
                            <th style="white-space: nowrap;">Edited By</th>
                            <th style="white-space: nowrap;">Edited On</th>
                        </tr>
                    </thead>
                    <tbody id="viewStaffHistoryTableBody">
                        <!-- Dynamic content goes here -->
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeStaffPayrollHistoryModal()">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="viewProofModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="margin-top: 5%;">
        <div class="modal-content" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.5)">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProofModalLabel"><b><span id="columnName"></span> <span id="fileName"></span> Proof</b></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" onclick="closeViewProodAttachment()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <iframe id="proofFileIframe" src="" style="width: 100%; height: 500px; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelDeclarationProof" onclick="closeViewProodAttachment()">Close</button>
                <button type="button" class="btn btn-danger mb-1" id="rejectDeclarationProof">Reject</button>
                <button type="button" class="btn btn-primary mb-1" id="approveDeclarationProof">Approve</button>
            </div>
        </div>
    </div>
</div>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.16/jspdf.plugin.autotable.min.js"></script> -->
<style type="text/css">
    .loadingIconMID {
        border: 8px solid #eee;
        border-top: 8px solid #7193be;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        position: fixed;
        z-index: 1;
        animation: spin 2s linear infinite;
        margin-top: 8%;
        /* margin-left: 43%; */
        position: absolute;
        z-index: 99999;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script> -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
    function refresh_page(){
        $('#refresh_page_btn').html('Please Wait...');
        $('#refresh_page_btn').prop('disabled', true);
        get_income_declaration_details();
    }

    function closeProofAttachmentsModal(){
        $('#attachmentsModal').hide();
    }

    function closeViewProodAttachment(){
        $('#viewProofModal').hide();
        const iframe = document.getElementById('proofFileIframe');
        iframe.src = '';
        $('#attachmentsModal').show();
    }

    function closeStaffPayrollHistoryModal(){
        $('#viewStaffPayrollEditHistory').hide();
        $('#attachmentsModal').show();
    }

var isSuperAdmin = '<?php echo $this->authorization->isSuperAdmin() ?>';
var school = '<?php echo CONFIG_ENV['school_sub_domain'];?>';
let globalStaffData;
    function filterTableByRegime() {
        let selectedRegime = $('#regimeForReopenProofSubmission').val();
        let tableBody = $('#staffInfoTable tbody');
        let rows = tableBody.find('tr');
        let matchFound = false;

        tableBody.find('.no-match-row').remove();

        rows.each(function () {
            let regimeCell = $(this).find('td:eq(2)');
            let regimeValue = regimeCell.text().trim();

            if (selectedRegime === '0' || regimeValue === (selectedRegime === '1' ? 'New Regime' : 'Old Regime')) {
                $(this).show();
                matchFound = true;
            } else {
                $(this).hide();
            }
        });

        if (!matchFound) {
            $('#confirmBtn').prop('disabled', true);
            tableBody.append(`
                <tr class="no-match-row">
                    <td colspan="4" style="text-align: center;">No match found</td>
                </tr>
            `);
        } else {
            $('#confirmBtn').prop('disabled', false);
        }
    }

    function check_all_income_tax(check){
        if($(check).is(':checked')) {
            $('.DTFC_Cloned tbody td div input.income_tax_staff_id').prop('checked', true);
        } else {
            $('.DTFC_Cloned tbody td div input.income_tax_staff_id').prop('checked', false);
        }
    }

    function reset_add_staffs_modal(){
        // $('#staffSelect').selectpicker('refresh');
        $('#your-select').select2('destroy');
        $('#staff_details').html('');
        $('#addStaffsToTds').hide();
    }

    function get_staffs_to_be_added_to_tds(){
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_staffs_to_be_added_to_tds'); ?>',
            data: {'financial_year': selected_financial_year_id},
            type: 'post',
            success: function(data) {
                parsed_data = $.parseJSON(data);
                // console.log(parsed_data);
                
                if (parsed_data.length > 0) {
                    // console.log(parsed_data);
                    let selectElement = $('<select>', {
                        id: 'staffSelect',
                        class: 'form-control col-md-12',
                        name: 'staff_ids[]',
                        multiple: true,
                    });

                    $('#staff_details').append(selectElement);

                    $('<option>', {
                        value: '',
                        text: 'Select Staff',
                        disabled: true,
                        selected: true
                    }).appendTo(selectElement);

                    parsed_data.forEach(function(item) {
                        $('<option>', {
                            value: item.staff_id,
                            text: item.staff_name
                        }).appendTo(selectElement);
                    });

                    $('#staffSelect').select2();

                    $('#addStaffsToTds').show();
                } else {
                    Swal.fire({
                        icon: "info",
                        title: "No Staffs Found",
                        showConfirmButton: true,
                        confirmButtonText: "OK"
                    });
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function add_staffs_to_tds(){
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        var staff_ids = $('#staffSelect').val();
        if(staff_ids != ''){
            $('#add_staffs_to_tds').html('Please Wait...').attr('disabled', 'disabled');
            $.ajax({
                url: '<?php echo site_url('management/payroll/add_staffs_to_tds'); ?>',
                type: 'post',
                data: {'staff_ids': staff_ids, 'financial_year': selected_financial_year_id},
                success: function(data) {
                    parsed_data = $.parseJSON(data);
                    if (parsed_data) {
                        Swal.fire({
                            icon: "success",
                            title: "Successfully Added Staff/s To Tax Declarations",
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function() {
                            $('#your-select').select2('destroy');
                            $('#staff_details').html('');
                            $('#add_staffs_to_tds').html('Add').removeAttr('disabled');
                            $('#addStaffsToTds').hide();
                            get_income_declaration_details();
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Enable To Add Staff To Tax Declarations",
                            showConfirmButton: true,
                            confirmButtonText: "OK"
                        });
                    }
                },
                error: function(err) {
                    console.log(err);
                }
            });
        }else{
            Swal.fire({
                icon: "error",
                title: "Please Select At Least One Staff",
                showConfirmButton: false,
                timer: 1500
            });
        }
    }

    // document.getElementById('exportButton').addEventListener('click', function () {
    //     const { jsPDF } = window.jspdf;
    //     const doc = new jsPDF();

    //     // Function to export a table
    //     function exportTableToPDF(tableId, title, startY) {
    //         doc.text(title, 14, startY);
    //         doc.autoTable({ html: tableId, startY: startY + 10 });
    //         return doc.autoTable.previous.finalY + 10;
    //     }

    //     let finalY = 10;
    //     finalY = exportTableToPDF('#taxSummaryDetails', 'Tax Summary Details', finalY);
    //     finalY = exportTableToPDF('#taxDetailsTable', 'Tax Details Table', finalY);

    //     if (document.getElementById('taxCalculationNewRegime').style.display != 'none') {
    //         finalY = exportTableToPDF('#taxCalculationNewRegime', 'Tax Calculation New Regime', finalY);
    //     }

    //     if (document.getElementById('taxCalculationOldRegime').style.display != 'none') {
    //         finalY = exportTableToPDF('#taxCalculationOldRegime', 'Tax Calculation Old Regime', finalY);
    //     }

    //     doc.save('tax_details.pdf');
    // });

    function change_income_status_payroll(){
        
        var selectedStatus = $('#staff_status_income').val();
        var table = $('#list_tab').DataTable();

        if (selectedStatus) {
            let escapedStatus = selectedStatus.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
            table.column(4).search('^' + escapedStatus + '$', true, false).draw();
        } else {
            table.column(4).search('').draw();
        }
        
        $('.dataTables_empty').attr('colspan', '12');

        var timestamp = new Date().getTime();
        localStorage.setItem('staffStatusIncome', JSON.stringify({status: selectedStatus, timestamp: timestamp}));
    }

    $(document).ready(function() {
        var selected_year = _get_cookie('selected_schedule_year');
        if (selected_year) {
            $(`#schedule_year option[value='${selected_year}']`).prop('selected', true);
        }
        adjust_declaration_window_status ();
        // change_income_status_payroll();
        // get_income_open_data();

        $('#cancel_all_approve, #close_all_approve').on('click', function(){
            $('#staying_rented_house_checkbox').prop('checked', false);
        })
    });

    function income_declaration_cal(staff_id, resSubmit) {
        var selected_financial_year_id = $('#schedule_year').val();
        if(resSubmit){
            var get_url = '<?php echo site_url('management/payroll/re_income_tax_staff_declartion'); ?>';
        }else{
            var get_url = '<?php echo site_url('management/payroll/income_declaration_cal'); ?>';
        }

        $.ajax({
            url: get_url,
            type: 'post',
            data: {'staffid': staff_id, 'financial_year_id': selected_financial_year_id},
            success: function(data) {
                var echr_data = JSON.parse(data);
                let final_deductions = Number(echr_data.total_80_deductions) + Number(echr_data.or_lta);
                //Fill the Old Regime Table
                $("#or_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#or_sd").val(echr_data.or_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_hra_exemption").val(echr_data.hra_exemption.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_sd_hra").val(`(${(parseFloat(echr_data.hra_exemption) + parseFloat(echr_data.or_sd)).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);

                $("#or_income_from_salary").val(echr_data.income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#or_pt").val(echr_data.pt_paid.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_income_from_salary_pt").val(echr_data.taxable_income_from_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#or_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_gross_salary_income").val(echr_data.gross_salary_old.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php if($collectPerkTaxMode == 'employee') { ?>
                    $("#or_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php } ?>
                $("#or_80c").val(echr_data.c_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ccd").val('₹' + echr_data.ccd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80d").val(echr_data.d_80 != null ? echr_data.d_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}) : '₹0.00');
                $("#or_80dd").val(echr_data.dd_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ddb").val(echr_data.ddb_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80g").val(echr_data.g_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80u").val(echr_data.u_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80e").val('₹' + echr_data.e_80.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                // $("#total_80_deductions").val(echr_data.total_80_deductions.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#total_80_deductions").val(final_deductions.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_tax_rebate").val('('+echr_data.or_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})+')');
                $("#or_net_income_tax").val(echr_data.or_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_taxable_salary").val(echr_data.or_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_basic_tax").val(echr_data.or_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_surcharge").val(echr_data.or_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_net_income_tax_surcharge").val(echr_data.or_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_cess").val(parseFloat(echr_data.or_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_yearly_tds").val(echr_data.or_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_final_tds").val(echr_data.or_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_80ttab").val(Number(echr_data.ttab_80).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#or_sec24").val(`(${parseFloat(echr_data.sec_24).toLocaleString('en-IN', {style: 'currency', currency: 'INR'})})`);
                $("#or_lta").val(Number(echr_data.or_lta).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                //Fill the New Regime Table
                $("#nr_basic_salary").val(echr_data.basic_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_hra").val(echr_data.hra.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_other_allowance").val(echr_data.other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_hra_other_allowance").val(echr_data.hra_other_allowance.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_sd").val(echr_data.nr_sd.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));

                $("#nr_income_from_salary_pt").val(echr_data.taxable_income_from_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php if($collectPerkTaxMode == 'employee') { ?>
                    $("#nr_perquisite_income").val(echr_data.perquisite_income.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                <?php } ?>
                $("#nr_other_employer_income").val(parseFloat(echr_data.other_employer_income).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_gross_salary_income").val(echr_data.gross_salary_new.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_tax_rebate").val('('+echr_data.nr_tax_rebate.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})+')');
                $("#nr_net_income_tax").val(echr_data.nr_net_income_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_basic_tax").val(echr_data.nr_basic_tax.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_surcharge").val(echr_data.nr_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_net_income_tax_surcharge").val(echr_data.nr_net_income_tax_surcharge.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_cess").val(parseFloat(echr_data.nr_cess).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_yearly_tds").val(echr_data.nr_tax_amt.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_other_employer_tds").val(parseFloat(echr_data.other_employer_tds).toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_final_tds").val(echr_data.nr_tax_amt_remaining.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_taxable_salary").val(echr_data.nr_taxable_salary.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
                $("#nr_additional_allowance").val(echr_data.outside_ctc_allowances.toLocaleString('en-IN', {style: 'currency', currency: 'INR'}));
            }
        });
    }

    function _set_cookie(name, value) {
        var d = new Date();
        d.setFullYear(d.getFullYear() + 1);
        var expires = "expires=" + d.toUTCString();
        document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }
    
    function adjust_declaration_window_status() {
        var schedule_year = $('#schedule_year').val();
        _set_cookie('selected_schedule_year', schedule_year);
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_payroll_declaration_status') ?>',
            type: 'post',
            data: {'schedule_year_id': schedule_year},
            success: function(data) {
                var data = $.parseJSON(data);
                if (data == null) {
                    //Window is not opened yet
                    $('#income_lock').show();
                    $('#income_open').hide();
                    $('#add_staffs_to_tds_btn_div').hide();
                    $('#refresh_page_btn_div').hide();
                    $('#stop_income_declaration_text').hide();
                    $('#stop_income_declaration_switch').hide();
                    $('#close_income_declaration_text').hide();
                } else if (data === 'open') {
                    $('#income_open').show();
                    $('#add_staffs_to_tds_btn_div').show();
                    $('#refresh_page_btn_div').show();
                    $('#income_lock').hide();
                    $('#stop_income_declaration_text').show();
                    $('#stop_income_declaration_switch').show();
                    $('#close_income_declaration_text').hide();
                    get_income_declaration_details();
                } else if (data === 'closed') {
                    $('#income_open').show();
                    $('#add_staffs_to_tds_btn_div').hide();
                    $('#refresh_page_btn_div').hide();
                    $('#income_lock').hide();
                    $('#stop_income_declaration_text').hide();
                    $('#stop_income_declaration_switch').hide();
                    $('#close_income_declaration_text').show();
                    get_income_declaration_details();
                }
            },
            error: function(error) {
            }
        });
    }
    
    function get_income_declaration_details() {
        $('.loadingIconMID').show();
        var staff_status_income = $('#staff_status_income').val();
        var schedule_year = $('#schedule_year').val();
        //var staff_status = $('#staff_status').val();
        $('#staff_status_income').prop('disabled', true);
        var year = $('#schedule_year option:selected').text();
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_income_declaration_details') ?>',
            type: 'post',
            data: {
                'schedule_year': schedule_year,
                'staff_status_income': staff_status_income,
                //'staff_status': staff_status
            },
            success: function(data) {
                var staff = $.parseJSON(data);
                globalStaffData = staff;
                $('.loadingIconMID').hide();
                $('#refresh_page_btn').html('Refresh');
                if (staff.length > 0 ) {
                    $('#list_staff').html(construct_table_header());
                    $('#list_tab_body').html(construct_table(staff, schedule_year));
                    $('#declartion_summary').html(construct_table_summary(staff, schedule_year));
                    var currentDateTime = new Date();
                    var day = String(currentDateTime.getDate()).padStart(2, '0');
                    var month = currentDateTime.toLocaleString('default', { month: 'short' });
                    var year = currentDateTime.getFullYear();
                    var hours = String(currentDateTime.getHours()).padStart(2, '0');
                    var minutes = String(currentDateTime.getMinutes()).padStart(2, '0');
                    var seconds = String(currentDateTime.getSeconds()).padStart(2, '0');
                    var formattedDateTime = `${day}-${month}-${year} ${hours}-${minutes}-${seconds}`;
                    var table = $('#list_tab').DataTable({
                        stateSave: true,
                        stateDuration: 3600, // 1 hour in seconds
                        ordering: false,
                        language: {
                            search: "",
                            searchPlaceholder: "Enter Search..."
                        },
                        scrollX: true,
                        scrollY: '60vh',
                        scrollCollapse: true,
                        paging: false, // Remove pagination
                        fixedHeader: {
                            header: true,
                            headerOffset: 0
                        },
                        fixedColumns: {
                            leftColumns: 5
                        },
                        dom: 'lBfrtip',
                        buttons: [                        
                            {
                                extend: 'print',
                                text: 'Print',
                                filename: `Income Declarations ${year}`,
                                className: 'btn btn-info',
                                exportOptions: {
                                    columns: ':not(:first-child)'
                                },
                            },
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                filename: `Income Declarations ${year}_${formattedDateTime}`,
                                className: 'btn btn-info',
                                exportOptions: {
                                    columns: ':not(:first-child)'
                                },
                            }
                        ]
                    });
    
                    var savedStatusInfo = localStorage.getItem('staffStatusIncome');
                    if (savedStatusInfo !== null) {
                        var savedData = JSON.parse(savedStatusInfo);
                        var currentTime = new Date().getTime();
                        var timeDifferenceStatus = (currentTime - savedData.timestamp) / (1000 * 60 * 60);
    
                        if (timeDifferenceStatus <= 1) {
                            $('#staff_status_income').val(savedData.status);
                            change_income_status_payroll();
                        } else {
                            $('#staff_status_income').val('');
                            change_income_status_payroll();
                            localStorage.removeItem('staffStatusIncome');
                        }
                    }
    
                    setTimeout(function() {
                        var savedPageInfo = localStorage.getItem('selectedPage');
                        var initialPage = 0;
                        if (savedPageInfo !== null) {
                            var savedData = JSON.parse(savedPageInfo);
                            var currentTime = new Date().getTime();
                            var timeDifferencePage = (currentTime - savedData.timestamp) / (1000 * 60 * 60);
    
                            if (timeDifferencePage <= 1) {
                                initialPage = parseInt(savedData.page);
                            } else {
                                initialPage = 0;
                                localStorage.removeItem('selectedPage');
                            }
                        }
    
                        var pageCount = table.page.info().pages;
                        if (initialPage < pageCount) {
                            table.page(initialPage).draw(false);
                        }
                    }, 100);
    
                    table.on('page.dt', function () {
                        var info = table.page.info();
                        var timestamp = new Date().getTime();
                        localStorage.setItem('selectedPage', JSON.stringify({ page: info.page, timestamp: timestamp }));
                    });
                }else{
                    $("#list_staff").html('<div class="no-data-display">Result not found</div>');
                }
                $('#staying_rented_house_checkbox').removeAttr('disabled');
                $('#staff_status_income').prop('disabled', false);
            },
            error: function(error) {
                console.log(error);
            }
        });
    }
    
    function construct_table_summary(staff, schedule_year) {
        var summary = '';
        summary += '<table id="investmentDeclarationStatusCount" class="table table-bordered" style="width:30%; white-space: nowrap;">';
        summary += '<thead>';
        summary += '<tr>';
        summary += '<th>Open</th>';
        summary += '<th>Submitted</th>';
        summary += '<th>Approved</th>';
        summary += '<th>Reopened (Tax Declaration)</th>';
        summary += '<th style="color: blue;">Reopened (Proof Submission)</th>';
        summary += '<th style="color: blue;">Submitted (Proof Submission)</th>';
        summary += '<th style="color: blue;">Approved (Proof Submission)</th>';
        summary += '</tr>';
        summary += '</thead>';
        let open = 0;
        let submitted = 0;
        let approved = 0;
        let reopenForDeclaration = 0;
        let reopenForProofSubmission = 0;
        let submittedForProofSubmission = 0;
        let approvedForProofSubmission = 0;
        for (var i = 0; i < staff.length; i++) {
            switch (staff[i].status) {
                case 'Open':
                    open++;    
                    break;
                case 'Submitted':
                    submitted++;
                    break;
                case 'Approved':
                    approved++;    
                    break;
                case 'Reopen':
                    reopenForDeclaration++;
                    break;
                default:
                break;
            }
            if(staff[i].proof_submission_status == 'Reopened'){
                reopenForProofSubmission++;
            } else if(staff[i].proof_submission_status == 'Submitted') {
                submittedForProofSubmission++;
            } else if(staff[i].proof_submission_status == 'Approved'){
                approvedForProofSubmission++;
            }
        }
        summary += '<tbody>';
        summary += '<tr>';
        summary += '<td>'+open+'</td>';
        summary += '<td>'+submitted+'</td>';
        summary += '<td>'+approved+'</td>';
        summary += '<td>'+reopenForDeclaration+'</td>';
        summary += '<td>'+reopenForProofSubmission+'</td>';
        summary += '<td>'+submittedForProofSubmission+'</td>';
        summary += '<td>'+approvedForProofSubmission+'</td>';
        summary += '</tr>';
        summary += '</tbody>';
        summary += '</table>';
        // console.log(submitted);
        
        return summary;
    }

    function displayRejectDocumentData(rejectedDocumentsCounts, staffName){
        Swal.fire({
            icon: "info",
            title: `${staffName} has about <span style="color: red;">${rejectedDocumentsCounts}</span> Rejected,<br>Please Go To ( Actions <i class="fa fa-long-arrow-right"></i> View Proof Attachments ) for More Information`,
            showConfirmButton: true,
            confirmButtonText: "OK",
            width: 600,
            // timer: 1500
        });
    }

    function displayRegimeChangeDetails(staff_name, remarks, regimeChangedBy, regimeChangedOn){
        Swal.fire({
            icon: "info",
            html: `
                <div style="font-size: 16px; text-align: left;">
                    <p>
                        <strong>Regime Change Details for ${staff_name}:</strong><br><br>
                        Changed By: <strong>${regimeChangedBy || '-'}</strong><br>
                        Changed On: <strong>${regimeChangedOn || '-'}</strong><br>
                        Remarks: "${remarks || '-'}"
                    </p>
                </div>
            `,
            showConfirmButton: true,
            confirmButtonText: "OK",
            width: 600
        });
    }

    function construct_table_header(){
        let html = '';
        html += '<table class="table table-striped table-bordered nowrap" id="list_tab" style="white-space: nowrap;">';
        html += '<thead>';
        html += '<tr>';
        html += '<th class="d-flex justify-content-between align-items-center" style="height: 34px !important;">';
        html += `<div><button class="btn btn-success dropdown-toggle actions_btn" type="button" id="actionButton" onclick="showMainActionsMenu(event)">Actions</button></div>`;
        html += '<div><input type="checkbox" name="selectAll" onclick="check_all_income_tax(this)" id="selectAll" style="margin-left:10px" class="check"></div>';
        html += '</th>';
        html += '<th style="width:5%">#</th>';
        html += '<th style="width:15%">Staff Name</th>';
        html += '<th style="width:15%">Employee Code</th>';
        html += '<th style="width:7%">Declaration Status</th>';
        html += '<th style="width:15%">Joining Date</th>';
        html += '<th style="width:15%">Staff Status</th>';
        html += '<th style="width:7%">Staff TDS Agreed / Reopen</th>';
        html += '<th style="width:10%">Yearly CTC</th>';
        html += '<th style="width:10%">Selected Regime</th>';
        html += '<th style="width:10%"><span class="d-flex flex-column align-items-center">Total Income <span>(Including Other Income)</span></span></th>';
        html += '<th style="display: hidden;">Availing Company Accommodation</th>';
        <?php if($collectPerkTaxMode == 'employee') { ?>
            html += '<th style="width:12%">Perquisite Income</th>';
        <?php } ?>
        html += '<th style="width:10%"><span class="d-flex flex-column align-items-center">Taxable Income <span>(With Deduction And As Per Regime)</span></span></th>';
        html += '<th style="width:12%;background: #e04b4a;color: white;">Total TDS</th>';

        // Rent Details
        html += '<th style="width:12%;background: #d3d3d3;">Total Rent Paid</th>';
        
        // 80C Investments && 80C Other Investments & Exemptions
        html += '<th style="width:12%;background: #d3d3d3;">EPF & VPF Contribution</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Public Provident Fund (PPF)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">N.S.C (Investment + accrued Interest before Maturity Year)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Tax Saving Fixed Deposit (5 Years and above)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">E.L.S.S (Tax Saving Mutual Fund)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Life Insurance Premiums Paid</th>';
        html += '<th style="width:12%;background: #d3d3d3;">New Pension Scheme (NPS) (U/S 80CCC)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Pension Plan from Insurance Co./Mutual Funds (u/s 80CCC)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Principal Repayment on House Building Loan</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Sukanya Samriddhi Yojana</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Stamp Duty & Registration Fees on House Buying</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Tuition Fees for Children(max 2 Children)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Other 80C Investments</th>';
        html += '<th style="width:12%;background: #e04b4a;color: white;">Total 80C Deduction</th>';
        html += '<th style="width:12%">Additional deduction for NPS U/S 80CCD(1B) - Max 50000</th>';
        
        // 80D Exemptions
        html += '<th style="width:12%;background: #d3d3d3;">Self Age</th>';
        html += '<th style="width:12%;background: #d3d3d3;">Parents Age</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Insurance Premium (for Self, Spouse & Children)</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Insurance Premium (for Parents )</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Preventive Health Checkup</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Preventive Health Checkup for Parents</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Bills for Parents - Senior Citizen</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Bills for Self, Spouse, Children - Senior Citizen</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen</th>';
        html += '<th style="width:12%;background: #d3d3d3;">80D Medical Insurance Premium (for Parents) - Senior Citizen</th>';
        html += '<th style="width:12%;background: #e04b4a;color: white;">Total 80D Deduction</th>';
        
        // Other Investments & Exemptions
        html += '<th style="width:12%">80E Interest Paid on Education Loan</th>';
        html += '<th style="width:12%">80DD Medical Treatment for Dependent Handicapped</th>';
        html += '<th style="width:12%">80DDB Expenditure on Medical Treatment for Self/ Dependent</th>';
        html += '<th style="width:12%">80GG Rent paid in case of no HRA received</th>';
        html += '<th style="width:12%">80U For Physically Disabled Person - Severe</th>';
        html += '<th style="width:12%">80U For Physically Disabled Person</th>';
        html += '<th style="width:12%">80G Donations To Approved Funds (100% Exemption)/th>';
        html += '<th style="width:12%">80G Donation To Approved Funds (50% Exemption)</th>';
        html += '<th style="width:12%">80DD Medical Treatment for Dependent Handicapped - Severe</th>';
        html += '<th style="width:12%">80DDB Expenditure on Medical Treatment for Self/ Dependent - Senior</th>';
        html += '<th style="width:12%">80TTA/B (10,000 for Others & 50,000 for Senior Citizens)</th>';
        // html += '<th style="width:12%">Others</th>';
        
        // Others
        html += '<th style="width:12%">Other Employer Income</th>';
        html += '<th style="width:12%">Other Employer TDS</th>';
        html += '<th style="width:12%">Interest Paid On Home Loan</th>';
        html += '<th style="width:12%">LTA / LTC</th>';

        html += '</tr>';
        html += '</thead>';
        html += '<tbody id="list_tab_body">';
        html += '</tbody>';
        html += '</table>';
        return html;
    }

    function construct_table(staff, schedule_year) {
        let html = '';
        var m = 1;
        for (var i = 0; i < staff.length; i++) {
            let declarationStatusTdId = ` id="declarationStatus_${staff[i].staff_id}" `;
            let actionsTdId = ` id="actionsId_${staff[i].staff_id}" `;
            let resigned = staff[i].staff_profile_status == 4 ? 'class="text-muted"' : '';
            let taxDeclarationStatus = staff[i].status == 'Reopen' ? 'Reopened' : staff[i].status ;
            let reopenProofStatus = staff[i].proof_submission_status != 0 ? `${staff[i].proof_submission_status} (Proof Submission)` : `${staff[i].proof_submission_status}` ;
            html += '<tr id="row_' + staff[i].staff_id + '" ' + resigned + '>';
            html += '<td'+ actionsTdId +'class="d-flex justify-content-between align-items-center" style=""><div>';
            html += `<button class="btn btn-secondary dropdown-toggle actions_btn" type="button" id="actionButton_${staff[i].staff_id}" onclick="showMenu(event, ${staff[i].staff_id}, ${i}, '${schedule_year}', ${(staff[i].proof_submission_status != 0 ? `'${reopenProofStatus}'` : `'${taxDeclarationStatus}'`)})">
                        Actions
                    </button>`;
            let staff_profile_status;
            switch (staff[i].staff_profile_status) {
                case '1':
                case 1:
                    staff_profile_status = 'Pending';
                    break;
                case '2':
                case 2:
                    staff_profile_status = 'Approved';
                    break;
                case '3':
                case 3:
                    staff_profile_status = 'Rejected';
                    break;
                case '4':
                case 4:
                    staff_profile_status = 'Resigned';
                    break;
                case '5':
                case 5:
                    staff_profile_status = 'Retired';
                    break;
                default:
                    staff_profile_status = 'N/A';
                    break;
            }
            // let taxRegimeChangedTooltip = staff[i].has_tax_regime_changed == 1 ? '<span class="" title="Regime has been changed" style="color: red;cursor: help;font-size: 20px;">*</span> ' : '';
            let taxRegimeChangedTooltip = staff[i].has_tax_regime_changed == 1 ? ' <span style="color: red;">(Changed)</span>' : '';
            let taxRegimeChangedPopup = staff[i].has_tax_regime_changed == 1 ? `onclick="displayRegimeChangeDetails('${staff[i].staff_name}', '${staff[i].regime_change_remarks}','${staff[i].regime_changed_by}', '${staff[i].regime_changed_on}')" style="cursor: pointer;"` : '';
            let resignedStyle = (staff[i].staff_profile_status == 4 ? 'color:red' : '');
            let hasRejectedDocumentStyle = (staff[i].hasRejectedDocument > 0 ? `color: red; cursor: pointer;` : '');
            let hasRejectedDocumentClick = (staff[i].hasRejectedDocument > 0 ? `onclick="displayRejectDocumentData(${staff[i].hasRejectedDocument}, '${staff[i].staff_name}')"` : '');
            let agreed_reopen_style = (staff[i].tds_agreed_reopen_status == 1 ? 'color: green' : (staff[i].tds_agreed_reopen_status == 2 ? 'color: red' : ''))
            let agreed_reopen = (staff[i].tds_agreed_reopen_status == 1 ? "Agreed" : (staff[i].tds_agreed_reopen_status == 2 ? "Requested Reopen" : '-'))
            html += `</div><div><input style="margin-left:10px" type='checkbox' name='income_tax_staff[]' id="incomeTaxStaffId_${staff[i].staff_id}" class='income_tax_staff_id' value='${staff[i].staff_id}' data-name='${staff[i].staff_name}' data-regime='${staff[i].tax_regime}' data-declarationstatus='${taxDeclarationStatus}' data-proofsubmissionstatus='${staff[i].proof_submission_status}' data-employeecode='${staff[i].employee_code}' data-staffStatus='${staff[i].staff_profile_status}'></div>`;
            html += '</td>';
            html += '<td style="vertical-align: middle;">' + m + '</td>';
            html += '<td style="vertical-align: middle;">' + staff[i].staff_name + '</td>';
            html += '<td style="vertical-align: middle;">' + (staff[i].employee_code != '' ? staff[i].employee_code  : '-' ) + '</td>';
            html += '<td' + declarationStatusTdId + 'style="vertical-align: middle;' + hasRejectedDocumentStyle + '" ' + hasRejectedDocumentClick + '>' + (staff[i].proof_submission_status != 0 ? `${reopenProofStatus}` : taxDeclarationStatus) + '</td>';
            html += '<td style="vertical-align: middle;">' + staff[i].joining_date + '</td>';
            html += '<td style="vertical-align: middle; ' + resignedStyle + '">' + staff_profile_status + '</td>';
            html += '<td style="vertical-align: middle; ' + agreed_reopen_style + '">' + (agreed_reopen) + '</span></td>';
            html += '<td style="vertical-align: middle;">' + (staff[i].yearly_ctc != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].yearly_ctc) : '-' )+ '</td>';
            html += '<td '+ taxRegimeChangedPopup +'>' + ((['Submitted', 'Approved', 'Reopen'].includes(staff[i].status)) ? (staff[i].tax_regime == 1 ? `New Regime${taxRegimeChangedTooltip}` : `Old Regime${taxRegimeChangedTooltip}`) : '-') + '</td>';
            html += '<td style="vertical-align: middle;">' + (staff[i].total_income != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].total_income) : '-' ) + '</td>';
            html += '<td style="display: hidden;">' + (staff[i].availing_company_accommodation != 0 ? 'Yes' : 'No' ) + '</td>';
            <?php if($collectPerkTaxMode == 'employee') { ?>
                html += '<td>' + (staff[i].perquisite_income != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].perquisite_income) : '-' ) + '</td>';
            <?php } ?>
            html += '<td style="vertical-align: middle;">' + (staff[i].taxable_salary != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].taxable_salary) : '-' )+ '</td>';
            html += '<td>' + (staff[i].total_declared_tds != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format((staff[i].total_declared_tds)) : '₹0.00' ) + '</td>';
            
            // Rent Details
            html += '<td>' + (staff[i].grand_total_rent != null ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].grand_total_rent) : '-' ) + '</td>';

            // 80C Investments && 80C Other Investments & Exemptions
            html += '<td>' + (staff[i].epf_and_pf_contribution != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].epf_and_pf_contribution) : '-' ) + '</td>';
            html += '<td>' + (staff[i].public_provident_fund != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].public_provident_fund) : '-' ) + '</td>';
            html += '<td>' + (staff[i].nsc_investment != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].nsc_investment) : '-' ) + '</td>';
            html += '<td>' + (staff[i].tax_saving_fixed_deposite != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].tax_saving_fixed_deposite) : '-' ) + '</td>';
            html += '<td>' + (staff[i].elss_mutual_fund != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].elss_mutual_fund) : '-' ) + '</td>';
            html += '<td>' + (staff[i].life_insurance != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].life_insurance) : '-' ) + '</td>';
            html += '<td>' + (staff[i].new_pension_scheme != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].new_pension_scheme) : '-' ) + '</td>';
            html += '<td>' + (staff[i].pension_plan_for_insurance != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].pension_plan_for_insurance) : '-' ) + '</td>';
            html += '<td>' + (staff[i].principal_repayment_house_loan != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].principal_repayment_house_loan) : '-' ) + '</td>';
            html += '<td>' + (staff[i].sukanya_samriddhi_yojana != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].sukanya_samriddhi_yojana) : '-' ) + '</td>';
            html += '<td>' + (staff[i].stamp_duty_registration_fees != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].stamp_duty_registration_fees) : '-' ) + '</td>';
            html += '<td>' + (staff[i].tution_fees_for_children != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].tution_fees_for_children) : '-' ) + '</td>';
            html += '<td>' + (staff[i].other_80c_investments != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].other_80c_investments) : '-' ) + '</td>';
            html += '<td>' + ((staff[i].total_80c_deduction == null || staff[i].total_80c_deduction == '0.00') ? '-' : new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].total_80c_deduction)) + '</td>';
            html += '<td>' + (staff[i].additional_deducation_for_nps != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].additional_deducation_for_nps) : '-' ) + '</td>';
            
            // 80D Exemptions
            html += '<td>' + (staff[i].self_age == "above_60" ? "Above 60" : "Below 60") + '</td>';
            html += '<td>' + (staff[i].parents_age == "above_60" ? "Above 60" : "Below 60" ) + '</td>';
            html += '<td>' + (staff[i].medical_insurance_premium_self_80d != 0.00 && staff[i].self_age == "below_60" ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_insurance_premium_self_80d) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_insurance_premium_parent_80d != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_insurance_premium_parent_80d) : '-' ) + '</td>';
            html += '<td>' + (staff[i].preventive_health_checkup_80d != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].preventive_health_checkup_80d) : '-' ) + '</td>';
            html += '<td>' + (staff[i].preventive_health_checkup_parents_80d != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].preventive_health_checkup_parents_80d) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_bills_for_parents_senior != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_bills_for_parents_senior) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_bills_for_self_senior != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_bills_for_self_senior) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_insurance_premium_self_80d_senior != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_insurance_premium_self_80d_senior) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_insurance_premium_parent_80d_senior != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_insurance_premium_parent_80d_senior) : '-' ) + '</td>';
            html += '<td>' + ((staff[i].total_80d_deduction == null || staff[i].total_80d_deduction == '0.00') ? '-' : new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].total_80d_deduction)) + '</td>';
            
            // Other Investments & Exemptions
            html += '<td>' + (staff[i].interest_paid_education_80e != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].interest_paid_education_80e) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_treatment_dependent_handicapped_80dd != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_treatment_dependent_handicapped_80dd) : '-' ) + '</td>';
            html += '<td>' + (staff[i].expenditure_medical_tretment_self_dependent_80ddb != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].expenditure_medical_tretment_self_dependent_80ddb) : '-' ) + '</td>';
            html += '<td>' + (staff[i].rent_paid_no_hra_recived_80gg != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].rent_paid_no_hra_recived_80gg) : '-' ) + '</td>';
            html += '<td>' + (staff[i].physically_disabled_person_80u_severe != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].physically_disabled_person_80u_severe) : '-' ) + '</td>';
            html += '<td>' + (staff[i].physically_disabled_person_80u != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].physically_disabled_person_80u) : '-' ) + '</td>';
            html += '<td>' + (staff[i].donation_approved_funds_80ggc != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].donation_approved_funds_80ggc) : '-' ) + '</td>';
            html += '<td>' + (staff[i].donation_approved_funds_80ggc_fifty != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].donation_approved_funds_80ggc_fifty) : '-' ) + '</td>';
            html += '<td>' + (staff[i].medical_treatment_dependent_handicapped_servere_80dd != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].medical_treatment_dependent_handicapped_servere_80dd) : '-' ) + '</td>';
            html += '<td>' + (staff[i].expenditure_medical_tretment_self_dependent_80ddb_senior != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].expenditure_medical_tretment_self_dependent_80ddb_senior) : '-' ) + '</td>';
            html += '<td>' + (staff[i].b_senior_citizens_80tta != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].b_senior_citizens_80tta) : '-' ) + '</td>';
            // html += '<td>' + new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].others_funds_80d) + '</td>';
            
            // Others
            html += '<td>' + (staff[i].other_employer_income != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].other_employer_income) : '-' ) + '</td>';
            html += '<td>' + (staff[i].other_employer_tds != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].other_employer_tds) : '-' ) + '</td>';
            html += '<td>' + (staff[i].interest_paid_on_home_loan != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].interest_paid_on_home_loan) : '-' ) + '</td>';
            html += '<td>' + (staff[i].lta != 0.00 ? new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(staff[i].lta) : '-' ) + '</td>';
            html += '</tr>';
            m++;
        }
        return html;
    }

    function showMainActionsMenu(event){
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            if (!staff_info.some(staff => staff.id === staff_id)) {
                staff_info.push({
                    id: staff_id,
                    name: staff_name
                });
            }
        });
        
        if (staff_info.length <= 0) {
            Swal.fire({
                icon: "info",
                title: "Please Select At least One Staff.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        closeMenu();

        let staffInvestmentStatus = $('#staff_status_income').val();
        const menu = document.createElement('div');
        menu.classList.add('absolute-mainmenu');
        let actions = '<ul class="action-list">';
        actions += `<li><a class="" title="Send Tax Declaration Details Email" onclick="send_tds_email()">
                        <i class="fa fa-envelope"></i> Send Emails
                    </a></li>`;
        // if(isSuperAdmin){
        // }
        if(staffInvestmentStatus == 'Submitted'){
            actions += `<li><a class="" onclick="reopenDeclarationForStaff()">
                            <i class="fa fa-undo"></i> Reopen Declarations
                        </a></li>`;
        }
        if(staffInvestmentStatus == 'Approved'){
            actions += `<li><a class="" onclick="reopenForProofSubmission()">
                            <i class="fa fa-undo"></i> Reopen Declarations For Proof Submission
                        </a></li>`;
        }
        if(staffInvestmentStatus == 'Submitted'){
            actions += `<li><a class="" onclick="massApproveDeclarations()">
                            <i class="fa fa-check"></i> Mass Approve
                        </a></li>`;
        }
        if(staffInvestmentStatus == 'Open' || staffInvestmentStatus == 'Reopened' || staffInvestmentStatus == 'Reopened (Proof Submission)'){
            actions += `<li><a class="" onclick="massApplyNewRegime()">
                            <i class="fa fa-plus"></i> Mass Apply New Regime
                        </a></li>`;
        }
        if(staffInvestmentStatus == 'Reopened (Proof Submission)'){
            actions += `<li><a class="" onclick="sendProffSubmissionRemainder()">
                            <i class="fa fa-paperclip"></i> Send Proof Submission Remainder Email
                        </a></li>`;
        }
        actions += '</ul>';

        menu.innerHTML = actions;
        document.body.appendChild(menu);

        let rect;
        if (event) {
            rect = event.target.getBoundingClientRect();
        } else {
            const fallbackElement = document.getElementById(`actionButton_${staffId}`);
            if (!fallbackElement) {
                console.error('Fallback element not found');
                return;
            }
            rect = fallbackElement.getBoundingClientRect();
        }
        const menuHeight = menu.offsetHeight;

        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        menu.style.position = 'absolute';
        menu.style.left = `${rect.left + window.scrollX}px`;

        if (spaceBelow < menuHeight && spaceAbove >= menuHeight) {
            menu.style.top = `${rect.top + window.scrollY - menuHeight}px`;
        } else {
            menu.style.top = `${rect.bottom + window.scrollY}px`;
        }
    }

    function showMenu(event, staffId, index, schedule_year, declaration_status) {
        // console.log(globalStaffData);
        const staff = globalStaffData.find(item => item.staff_id == staffId);
        // console.log(staff);
        // return false;
        
        // Close any previously opened menus
        closeMenu();

        // Create the menu dynamically
        const menu = document.createElement('div');
        menu.classList.add('absolute-menu');
        menu.setAttribute('staff_id', 'absoluteMenu' + staffId);
        let actions = '<ul class="action-list">';  // Start of the list
        if (staff.staff_profile_status == 4 && staff.status == 'Open') {
            actions += `<li><a class="" title="Delete Tax Declaration" onclick="delete_tds_regime(${staff.staff_id}, '${staff.staff_name}', '${staff.status}', '${staff.joining_date}')">
                            <i class="fa fa-trash-o"></i> Delete Tax Declaration
                        </a></li>`;
        } else if (staff.status == 'Open' || staff.status == 'Reopen') {
            if(isSuperAdmin || staff.status == 'Open'){
                actions += `<li><a class="" title="Delete Tax Declaration" onclick="delete_tds_regime(${staff.staff_id}, '${staff.staff_name}', '${staff.status}', '${staff.joining_date}')">
                                <i class="fa fa-trash-o"></i> Delete Tax Declaration
                            </a></li>`;
            }
            actions += `<li><a class="" title="Apply New Regime" onclick="apply_new_regime(${staff.staff_id}, '${staff.staff_name}', '${staff.status}', '${staff.joining_date}')">
                            <i class="fa fa-plus"></i> Apply New Regime
                        </a></li>`;
        } else if (staff.status == 'Approved') {
            if (staff.tds_agreed_reopen_status == 2) {
                actions += `<li><a class="" onclick="incomeTaxReopenDeclaration(${staff.staff_id}, '${staff.staff_name}')">
                                <i class="fa fa-undo"></i> Re-Open
                            </a></li>`;
                actions += `<li><a class="" disbaled onclick="openModal(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent}, ${0}, 'View Only', 'Main Table')">
                                <i class="fa fa-eye"></i> View Declaration
                            </a></li>`;
            } else if (staff.proof_submission_status == 0 && staff.tax_regime == 2) {
                actions += `<li><a class="" onclick="income_tax_staff_reopen_after_approve(${staff.staff_id}, '${staff.staff_name}')">
                                <i class="fa fa-undo"></i> Re-Open
                            </a></li>`;
            } else if(staff.proof_submission_status == 'Submitted') {
                actions += `<li><a class="" onclick="viewProofAttachments(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent})">
                                <i class="fa fa-paperclip"></i> View Proof Attachments
                            </a></li>`;
            } else if(staff.proof_submission_status == 'Approved') {
                actions += `<li><a class="" onclick="viewProofAttachments(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent})">
                                <i class="fa fa-paperclip"></i> View Proof Attachments
                            </a></li>`;
                actions += `<li><a class="" disbaled onclick="openModal(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent}, ${0}, 'View Only', 'Main Table')">
                                <i class="fa fa-eye"></i> View Declaration
                            </a></li>`;
            } else if(staff.proof_submission_status == 'Reopened'){
                actions += `<li><a class="" title="Apply New Regime" onclick="apply_new_regime(${staff.staff_id}, '${staff.staff_name}', '${staff.proof_submission_status}', '${staff.joining_date}')">
                                <i class="fa fa-plus"></i> Apply New Regime
                            </a></li>`;
                if(staff.hasRejectedDocument > 0){
                    actions += `<li><a class="" onclick="viewProofAttachments(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent})">
                                    <i class="fa fa-paperclip"></i> View Proof Attachments
                                </a></li>`;
                }
            } else {
                actions += `<li><a class="" disbaled onclick="openModal(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent}, ${0}, 'View Only', 'Main Table')">
                                <i class="fa fa-eye"></i> View Declaration
                            </a></li>`;
            }
            if(declaration_status == 'Reopened (Proof Submission)'){
                actions += `<li><a class="" onclick="sendProffSubmissionRemainderIndividual(${staff.staff_id}, '${schedule_year}')">
                                <i class="fa fa-paperclip"></i> Send Proof Submission Remainder Email
                            </a></li>`;
            }
        } else {
            actions += `<li><a class="" disbaled onclick="openModal(${staff.staff_id}, '${staff.staff_name}', ${staff.grand_total_rent}, ${0}, 'Actions', 'Main Table')">
                            <i class="fa fa-eye"></i> View Declaration
                        </a></li>`;
        }

        if(staff.status != 'Open'){
            actions += `<li><a class="" onclick="getTaxCalculationsComparison(${staff.staff_id}, '${staff.staff_name}', '${staff.employee_code}', ${staff.tax_regime})">
                                <i class="fa fa-columns"></i> Tax Regime Comparison
                            </a></li>`;
        }

        if (isSuperAdmin) {
            actions += `<li><a class="" onclick="income_tax_declaration_by_admin(${staff.staff_id}, '${schedule_year}')">
                            <i class="fa fa-pencil"></i> Re-Open Approved Declaration
                        </a></li>`;
        }
        actions += '</ul>';

        // Set the menu content and append it to the body
        menu.innerHTML = actions;
        document.body.appendChild(menu);

        // Get the button's bounding rectangle for positioning
        let rect;
        if (event) {
            rect = event.target.getBoundingClientRect();
        } else {
            const fallbackElement = document.getElementById(`actionButton_${staffId}`);
            if (!fallbackElement) {
                console.error('Fallback element not found');
                return;
            }
            rect = fallbackElement.getBoundingClientRect();
        }
        const menuHeight = menu.offsetHeight;

        // Calculate available space and position the menu
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        menu.style.position = 'absolute';
        menu.style.left = `${rect.left + window.scrollX}px`;

        if (spaceBelow < menuHeight && spaceAbove >= menuHeight) {
            menu.style.top = `${rect.top + window.scrollY - menuHeight}px`;
        } else {
            menu.style.top = `${rect.bottom + window.scrollY}px`;
        }
    }

    document.addEventListener('click', function(event) {
        const isButtonClick = event.target.classList.contains('dropdown-toggle');
        if (!isButtonClick) {
            closeMenu();
        }
    });

    function closeMenu() {
        const openMenu = document.querySelector('.absolute-menu');
        const openMainMenu = document.querySelector('.absolute-mainmenu');
        if (openMenu) {
            openMenu.remove();
        } else if(openMainMenu){
            openMainMenu.remove();
        }
    }

    function delete_tds_regime(staff_id, staff_name, staff_status, staff_doj){        
        if(staff_status == 'Reopen'){
            bootbox.confirm({
                title: `Confirm - <b>${staff_name}</b>`,
                message: `<h5><center>The current status of the tax declaration is <span style="font-weight:600;font-size:15px;color:blue">Reopen</span>.<br> Are you sure you want to <span style="font-weight:600;font-size:15px;color:red">Delete - ${staff_name}</span> Tax Declaration.?</center></h5>`,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        delete_tds_regime_ajax(staff_id)
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        }else{
            bootbox.confirm({
                title: `Confirm - <b>${staff_name}</b>`,
                message: `<h5><center><span style="font-weight:600;font-size:15px;color:red">${staff_name}'s Tax Declaration Will Be Deleted</span>. Are you sure?</center></h5>`,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        delete_tds_regime_ajax(staff_id)
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        }
    }

    function delete_tds_regime_ajax(staff_id){
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/delete_tds_staff_by_id'); ?>',
            type: 'post',
            data: {'staff_id': staff_id, 'financial_year': selected_financial_year_id},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if (parsed_data) {
                    Swal.fire({
                        icon: "success",
                        title: "Income-Tax Declaration Successfully Deleted",
                        showConfirmButton: false,
                        // confirmButtonText: "OK"
                        timer: 1500
                    }).then(function() {
                        $('#staffTaxDetails').modal('hide');
                        var table = $('#list_tab').DataTable();
                        ['declarationStatus_' + staff_id, 'actionsId_' + staff_id].forEach(function(id) {
                            refreshCell(table, id, id.includes('declarationStatus') ? 'Deleted (Refresh Pending)' : 'Refresh Pending');
                        });
                        table.draw(false);
                        $('#refresh_page_btn').prop('disabled', false);
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Failed To Delete Income-Tax Declaration",
                        showConfirmButton: true,
                        confirmButtonText: "OK"
                    });
                }
            },
            error: function(err) {
                console.log(err);
                
            }
        });
    }

    function apply_new_regime(staff_id, staff_name, staff_status, staff_doj){
        if(staff_doj == '-'){
            Swal.fire({
                icon: "error",
                title: "Joining Date is not entered in the ERP.",
                showConfirmButton: false,
                // confirmButtonText: "OK",
                timer: 1500
            });
            return;
        }
        if(staff_status == 'Reopen'){
            let bootboxMessage = `<div style="text-align: center;">
                                    <h5>
                                        The current status of the tax declaration is 
                                        <span style="font-weight:600; font-size:15px; color:blue;">Reopen</span>.<br>
                                        Are you sure you want to apply the 
                                        <span style="font-weight:600; font-size:15px; color:blue;">New Regime</span>?
                                    </h5>

                                    <div style="margin-top: 15px; text-align: left;">
                                        <label for="regimeRemarks" style="font-weight:600;">
                                            Remarks <!-- <span style="color:red;">*</span> -->
                                        </label>
                                        <textarea id="regimeRemarks" class="form-control" rows="3" style="resize: none;" placeholder="Enter your remarks here..." maxlength="200"></textarea>
                                        <span id="remarkError" style="color:red; display:none;">Remarks are required.</span>
                                    </div>
                                </div>`;
            bootbox.confirm({
                title: `Confirm - <b>${staff_name}</b>`,
                // message: `<h5><center>The current status of the tax declaration is <span style="font-weight:600;font-size:15px;color:blue">Reopen</span>.<br> Are you sure you want to apply the <span style="font-weight:600;font-size:15px;color:blue">New Regime.</span>?</center></h5>`,
                message: bootboxMessage,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        let remarks = document.getElementById("regimeRemarks").value.trim();
                        // if (remarks == "") {
                        //     document.getElementById("remarkError").style.display = "block";
                        //     return false; // prevent closing
                        // } else {
                            // document.getElementById("remarkError").style.display = "none";
                            apply_new_regime_ajax(staff_id, 'Reopen', remarks);
                        // }
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        } else if(staff_status == 'Reopened') {
            let bootboxMessage = `
                                <div style="font-family: Segoe UI, sans-serif;">
                                    <div style="padding: 10px; background-color: #e9f7ef; border-left: 4px solid #28a745; margin-bottom: 10px; border-radius: 4px;">
                                        <strong>Status:</strong><br>
                                        <span style="color:green; font-weight:600;">Approved (Old Regime)</span> &nbsp;|&nbsp;
                                        <span style="color:blue; font-weight:600;">Reopened For Proof Submission</span>
                                    </div>
                                    <p style="font-size: 15px; margin-bottom: 15px;">
                                        You are about to apply the <span style="color:blue; font-weight:600;">New Tax Regime</span>. Please confirm below.
                                    </p>
                                    <div style="background: #fff; border: 1px dashed #ff0000; padding: 12px; border-radius: 6px;">
                                        <strong style="color:#dc3545;">Important Notes:</strong>
                                        <ul style="margin: 8px 0 0 18px; padding: 0; font-size: 14px;">
                                            <li style="color:#b94a48;">Old Regime investments will be reset to 0.</li>
                                            <li style="color:red; font-weight: 600;">This action is final and cannot be reversed.</li>
                                        </ul>
                                    </div>
                                    <div style="margin-top: 15px;">
                                        <label for="regimeRemarks" style="font-weight:600;">Remarks <!-- <span style="color:red;">*</span> --></label>
                                        <textarea id="regimeRemarks" class="form-control" rows="3" style="resize: none;" placeholder="Enter your remarks here..." maxlength="200"></textarea>
                                        <span id="remarkError" style="color:red; display:none;">Remarks are required.</span>
                                    </div>
                                </div>`;
            bootbox.confirm({
                title: `Confirm - <b>${staff_name}</b>`,
                // message: `<h5><center>The current status of the tax declaration is <br><span style="font-weight:600;font-size:15px;color:green">Approved (Old Regime)</span> and has been <span style="font-weight:600;font-size:15px;color:blue">Reopened For Proof Submission</span>.<br><br> Are you sure you want to apply the <span style="font-weight:600;font-size:15px;color:blue">New Regime.</span>?<br><br><span style="color:#b94a48; font-weight:600; font-size:15px;">Note: If the New Regime is applied, all Old Regime investment declarations will be removed (set to 0).</span><br><span style="color:red !important;font-weight:600;font-size:15px;">This change is final and cannot be undone.</span></center></h5>`,
                message: bootboxMessage,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        if (result) {
                            let remarks = document.getElementById("regimeRemarks").value.trim();
                            // if (remarks === "") {
                            //     document.getElementById("remarkError").style.display = "block";
                            //     return false; // prevent closing
                            // } else {
                                // document.getElementById("remarkError").style.display = "none";
                                apply_new_regime_ajax(staff_id, 'Reopen Proof Submission', remarks);
                            // }
                        }
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        } else{
            let bootboxMessage = `
                                <div style="text-align: center;">
                                    <h5>
                                        The <span style="font-weight:600; font-size:15px; color:blue;">New Regime</span> will be applied. 
                                        Are you sure?
                                    </h5>
                                    <div style="margin-top: 15px; text-align: left;">
                                        <label for="regimeRemarks" style="font-weight:600;">
                                            Remarks <!-- <span style="color:red;">*</span> -->
                                        </label>
                                        <textarea id="regimeRemarks" class="form-control" rows="3" style="resize: none;" placeholder="Enter your remarks here..." maxlength="200"></textarea>
                                        <span id="remarkError" style="color:red; display:none;">Remarks are required.</span>
                                    </div>
                                </div>
                            `;
            bootbox.confirm({
                title: `Confirm - <b>${staff_name}</b>`,
                // message: `<h5><center>The <span style="font-weight:600;font-size:15px;color:blue">New Regime will be applied</span>. Are you sure?</center></h5>`,
                message: bootboxMessage,
                size: 'small',
                centerVertical: true,
                className: 'widthadjust',
                width: '50%',
                backdrop: true,
                buttons: {
                    confirm: {
                        label: "<i class='fa fa-check'></i> Yes",
                        className: 'btn-success'
                    },
                    cancel: {
                        label: "<i class='fa fa-times'></i> No",
                        className: 'btn-danger'
                    }
                },
                callback: function(result) {
                    if (result) {
                        if (result) {
                            let remarks = document.getElementById("regimeRemarks").value.trim();
                            // if (remarks === "") {
                            //     document.getElementById("remarkError").style.display = "block";
                            //     return false; // prevent closing
                            // } else {
                            //     document.getElementById("remarkError").style.display = "none";
                                apply_new_regime_ajax(staff_id, 'Open', remarks);
                            // }
                        }
                    }
                },
            }).find("div.modal-content").addClass("confirmWidth");
        }
    }

    function apply_new_regime_ajax(staff_id, status, remarks){
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/apply_new_regime_to_staff_by_id'); ?>',
            type: 'post',
            data: {'staff_id': staff_id, 'financial_year': selected_financial_year_id, 'status': status, 'remarks': remarks},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if (parsed_data) {
                    Swal.fire({
                        icon: "success",
                        title: "New Regime Income-Tax Declaration Successfully Added",
                        showConfirmButton: false,
                        // confirmButtonText: "OK"
                        timer: 1500
                    }).then(function() {
                        $('#staffTaxDetails').modal('hide');
                        var table = $('#list_tab').DataTable();
                        ['declarationStatus_' + staff_id, 'actionsId_' + staff_id].forEach(function(id) {
                            refreshCell(table, id, id.includes('declarationStatus') ? 'Submitted (Refresh Pending)' : 'Refresh Pending');
                        });
                        table.draw(false);
                        $('#refresh_page_btn').prop('disabled', false);
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Failed To Add Income-Tax Declaration",
                        showConfirmButton: true,
                        confirmButtonText: "OK"
                    });
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function format_month_year(date) {
        var date_parts = date.split('-');
        var month = date_parts[0];
        var year = date_parts[1];

        var month_names = {
            '01': 'JAN', '02': 'FEB', '03': 'MAR',
            '04': 'APR', '05': 'MAY', '06': 'JUN',
            '07': 'JUL', '08': 'AUG', '09': 'SEP',
            '10': 'OCT', '11': 'NOV', '12': 'DEC'
        };

        return month_names[month] + ' ' + year;
    }

    function get_staff_rent_details(staffId, callback){
        var schedule_year = $('#schedule_year').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_staff_rent_details') ?>',
            type: 'post',
            data: {
                'schedule_year': schedule_year,
                'staff_id': staffId
            },
            success: function(data) {
                var house_rent_details = $.parseJSON(data);
                callback(house_rent_details);
            },
            error: function(error) {
                console.log(error);
                callback(null);
            }
        });
    }

    function openModal(staffId, staffName, grand_total_rent, resSubmit = 0, type, callFrom) {
        if(callFrom == 'Attachments Table'){
            $('#attachmentsModal').hide();
        }
        var selected_financial_year_id = $('#schedule_year').val();
        // $('#verify_not_approved').modal('hide');
        get_staff_rent_details(staffId, function(house_details) {
            income_declaration_cal(staffId, resSubmit);
            var table = $('#list_tab').DataTable();
            var columns = table.columns().header().toArray().map(function(header) {
                return $(header).text().trim();
            });
            var rowData = table.row('#row_' + staffId).data();
            
            var rowDataWithColumnNames = [];
            for (var i = 0; i < columns.length; i++) {
                rowDataWithColumnNames.push({ column: columns[i], data: rowData[i] });
            }
            // console.log(rowDataWithColumnNames);
            let collectPerkTaxMode = '<?php echo $collectPerkTaxMode;?>';
            let perquisiteIncome = ' style="display: none;"';
            if(collectPerkTaxMode == 'employee'){
                perquisiteIncome = '';
            } else {
                rowData.splice(12, 0, 0);
            }
            $('#staffTaxDetails #staff_name').html(`<b>${rowData[2]} ( ${rowData[3]} )</b>`);
            var tableContentSummary = `
                <thead>
                    <tr>
                        <th colspan="7" style="text-align:center;">Summary</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center"><b>Staff TDS Agreed / Reopen</b></td>
                        <td class="text-center"><b>Yearly CTC</b></td>
                        <td class="text-center"><b>Selected Regime</b></td>
                        <td><b><span class="d-flex flex-column align-items-center">Total Income <span>(Including Other Income)</span></span></b></td>
                        <td class="text-center"${perquisiteIncome}><b>Perquisite Income</b></td>
                        <td><b><span class="d-flex flex-column align-items-center">Taxable Income <span>(With Deduction And As Per Regime)</span></span></b></td>
                        <td class="text-center"><b>Total TDS</b></td>
                    </tr>
                    <tr>
                        <td style="${rowData[7] == 'Agreed' ? 'color: green;' : rowData[7] == 'Requested Reopen' ? 'color: red;' : ''}"><input type="text"  style="border:none;" readonly value="${(rowData[7])}"></td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[8])}"></td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[9])}"></td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[10])}"></td>
                        <td${perquisiteIncome}><input type="text" style="border:none;" readonly value="${(rowData[12])}"></td$>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[13])}"></td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[14])}"></td>
                    </tr>
                </tbody>
            `;
            var tableContentDetails = `
                <thead>
                    <tr>
                        <th colspan="4" style="text-align:center;">Tax Details</th>
                    </tr>
                </thead>
                <tbody>
                <tr>
                    <td colspan="4"><b>Perquisite Income</b></td>
                </tr>
                <tr>
                    <td width="35%">Availing Company Accommodation?</td>
                    <td width="15%"><input type="text" style="border:none;" readonly value="${rowData[11]}"></td>
                </tr>`;
                if (house_details && house_details.length > 0) {
                    tableContentDetails += `
                        <tr>
                            <td colspan="5"><b>DECLARATION FOR HOUSE RENT ALLOWANCE RELIEF</b></td>
                        </tr>
                        <tr>
                            <td colspan="3">
                                <table style="margin: 0 auto;">
                                    <tr>
                                        <th style="width: 10%">#</th>
                                        <th style="width: 30%">From Date</th>
                                        <th style="width: 30%">To Date</th>
                                        <th style="width: 22%">Rent Per Month</th>
                                        <th style="width: 20%">Total Rent</th>
                                    </tr>`;
                    house_details.forEach(function(rent, index) {
                        tableContentDetails += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${format_month_year(rent.from_date)}</td>
                                <td>${format_month_year(rent.to_date)}</td>
                                <td>${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(rent.monthly_amount)}</td>
                                <td>${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(rent.rent_amount_cal)}</td>
                            </tr>`;
                    });
                    tableContentDetails += `
                                </table>
                            </td>
                            <td colspan="2">
                                <table style="margin: 0 auto;">
                                    <tr>
                                        <th>Grand Total Rent</th>
                                    </tr>
                                    <tr>
                                        <td>${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(grand_total_rent)}</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>`;
                }
                tableContentDetails += `
                    <tr>
                        <td colspan="4"><b>80C Investments ${rowData[29] != '-' ? `<span>(Total : ${rowData[29]}</span>)` : '' }</b></td>
                    </tr>
                    <tr>
                        <td width="35%">EPF & VPF Contribution </td>
                        <td width="15%"><input type="text" style="border:none;" id="epf_vpf_contribution" readonly value="${(rowData[16])}"></td>
                        <td width="35%">Public Provident Fund (PPF) </td>
                        <td width="15%"><input type="text"  style="border:none;" readonly value="${(rowData[17])}"></td>
                    </tr>
                    <tr>
                        <td>N.S.C (Investment + accrued Interest before Maturity Year)</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[18])}"></td>
                        <td>Tax Saving Fixed Deposit (5 Years and above) </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[19])}"></td>
                    </tr>
                    <tr>
                        <td>E.L.S.S (Tax Saving Mutual Fund)</td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[20])}"></td>
                        <td>Life Insurance Premiums paid </td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[21])}"></td>
                    </tr>
                    <tr>
                        <td>New Pension Scheme (NPS) (U/S 80CCC) </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[22])}"></td>
                        <td>Pension Plan from Insurance Co./Mutual Funds (u/s 80CCC) </td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[23])}"></td>
                    </tr>
                    <tr>
                        <td>Principal Repayment on House Building Loan </td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[24])}"></td>
                        <td>Sukanya Samriddhi Yojana </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[25])}"></td>
                    </tr>
                    <tr>
                        <td>Stamp Duty & Registration Fees on House Buying</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[26])}"></td>
                        <td>Tuition Fees for Children(max 2 Children) </td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[27])}"></td>
                    </tr>
                    <tr>
                        <td>Other 80C Investments</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[28])}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>80C Other Investments & Exemptions</b></td>
                    </tr>
                    <tr>
                        <td>Additional Deduction for NPS U/S 80CCD(1B) - Max 50000</td>
                        <td colspan="3"><input type="text" style="border:none;" readonly value="${(rowData[30])}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>80D Exemptions ${rowData[41] != '-' ? `<span>(Total: ${rowData[41]})</span>` : ''}</b></td>
                    </tr>
                    <tr>
                        <td>Age of Self</td>
                        <td><input type="text" style="border:none;" readonly value="${rowData[31] != 'Below 60' ? 'Above 60' : 'Below 60'}"></td>
                        <td>Age of Parents</td>
                        <td><input type="text" style="border:none;" readonly value="${rowData[32] != 'Below 60' ? 'Above 60' : 'Below 60'}"></td>
                    </tr>
                    <tr>
                        <td>80D Preventive Health Checkup</td>
                        <td><input type="text" style="border:none;" readonly value="${rowData[35]}"></td>
                        <td>80D Preventive Health Checkup for Parents</td>
                        <td><input type="text" style="border:none;" readonly value="${rowData[36]}"></td>
                    </tr>
                    <tr ${rowData[31] == 'Above 60' && rowData[32] == 'Above 60' ? 'class="d-none"' : ''}>
                        <td ${rowData[31] == 'Above 60' ? 'class="textMuted"' : ''}>80D Medical Insurance Premium (for Self, Spouse & Children)</td>
                        <td ${rowData[31] == 'Above 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[33]}"></td>
                        <td ${rowData[32] == 'Above 60' ? 'class="textMuted"' : ''}>80D Medical Insurance Premium (for Parents)</td>
                        <td ${rowData[32] == 'Above 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[34]}"></td>
                    </tr>
                    <tr ${rowData[31] == 'Below 60' && rowData[32] == 'Below 60' ? 'class="d-none"' : ''}>
                        <td ${rowData[31] == 'Below 60' ? 'class="textMuted"' : ''}>80D Medical Bills for Self, Spouse, Children - Senior Citizen</td>
                        <td ${rowData[31] == 'Below 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[38]}"></td>
                        <td ${rowData[32] == 'Below 60' ? 'class="textMuted"' : ''}>80D Medical Bills for Parents - Senior Citizen</td>
                        <td ${rowData[32] == 'Below 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[37]}"></td>
                    </tr>
                    <tr ${rowData[31] == 'Below 60' && rowData[32] == 'Below 60' ? 'class="d-none"' : ''}>
                        <td ${rowData[31] == 'Below 60' ? 'class="textMuted"' : ''}>80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen</td>
                        <td ${rowData[31] == 'Below 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[39]}"></td>
                        <td ${rowData[32] == 'Below 60' ? 'class="textMuted"' : ''}>80D Medical Insurance Premium (for Parents) - Senior Citizen</td>
                        <td ${rowData[32] == 'Below 60' ? 'class="textMuted"' : ''}><input type="text" style="border:none;" readonly value="${rowData[40]}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><strong>Other Investments & Exemptions</strong> </td>
                    </tr>
                    <tr>
                        <td>80E Interest Paid on Education Loan</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[42])}"></td>
                        <td>80DD Medical Treatment for Dependent Handicapped</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[43])}"></td>
                    </tr>
                    <tr>
                        <td>80DDB Expenditure on Medical Treatment for Self/ Dependent </td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[44])}"></td>
                        <td>80U For Physically Disabled Person - Severe </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[46])}"></td>
                    </tr>
                    <tr>
                        <td>80GG Rent paid in case of no HRA Received</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[45])}"></td>
                        <td>80U For Physically Disabled Person</td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[47])}"></td>
                    </tr>
                    <tr>
                        <td>80G Donations to Approved Funds (100% Exemption) </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[48])}"></td>
                        <td>80DD Medical Treatment for Dependent Handicapped - Severe </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[50])}"></td>
                    </tr>
                    <tr>
                        <td>80G Donation to Approved Funds (50% Exemption) </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[49])}"></td>
                        <td>80DDB Expenditure on Medical Treatment for Self/ Dependent - Senior </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[51])}"></td>
                    </tr>
                    <tr>
                        <td>80TTA/B Interest from Savings Account (10,000 for Others & 50,000 for Senior Citizens) </td>
                        <td><input type="text"  style="border:none;" readonly value="${(rowData[52])}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><strong>Others</strong> </td>
                    </tr>
                    <tr>
                        <td>Other Employer Income</td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[53])}"></td>
                        <td>Other Employer TDS</td>
                        <td><input type="text"   style="border:none;" readonly value="${(rowData[54])}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><strong>24 Home Loan</strong> </td>
                    </tr>
                    <tr>
                        <td>Interest Paid On Home Loan</td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[55])}"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><strong>Section 10(5) - LTA / LTC</strong> </td>
                    </tr>
                    <tr>
                        <td>Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)</td>
                        <td><input type="text" style="border:none;" readonly value="${(rowData[56])}"></td>
                    </tr>
                </tbody>`;
            if(rowData[9] == 'Old Regime'){
                $('#taxCalculationOldRegime').css('display', '')
                $('#taxCalculationNewRegime').css('display', 'none')
                $('#view_additional_allowance_break_down_old').off().on('click', function(){view_additional_allowance_break_down(staffId, selected_financial_year_id, rowData[9])});
            }else{
                $('#taxCalculationNewRegime').css('display', '')
                $('#taxCalculationOldRegime').css('display', 'none')
                $('#view_additional_allowance_break_down_new').off().on('click', function(){view_additional_allowance_break_down(staffId, selected_financial_year_id, rowData[9])});
            }

            $('#taxSummaryDetails').html(tableContentSummary);
            $('#taxDetailsTable').html(tableContentDetails);
            if(type != 'View Only'){
                $('#reOpenDeclaration').show().off().on('click', function(){income_tax_staff_reopen(staffId, staffName)});
                $('#approveDeclaration').show().off().on('click', function(){income_tax_staff_approve(staffId, staffName)});
            } else {
                $('#reOpenDeclaration').hide();
                $('#approveDeclaration').hide();
            }
            if(callFrom == 'Attachments Table'){
                $('#staffTaxDetails #stafftaxDetailsCloseBtn1').off().on('click', function(){
                    $('#staffTaxDetails').modal('hide');
                    $('#attachmentsModal').show();
                    if(collectPerkTaxMode != 'employee'){
                        rowData.splice(12, 1);
                    }
                })
                $('#staffTaxDetails #stafftaxDetailsCloseBtn2').off().on('click', function(){
                    $('#staffTaxDetails').modal('hide');
                    $('#attachmentsModal').show();
                    if(collectPerkTaxMode != 'employee'){
                        rowData.splice(12, 1);
                    }
                })
            } else {
                $('#staffTaxDetails #stafftaxDetailsCloseBtn1').off().on('click', function(){
                    $('#staffTaxDetails').modal('hide');
                    if(collectPerkTaxMode != 'employee'){
                        rowData.splice(12, 1);
                    }
                })
                $('#staffTaxDetails #stafftaxDetailsCloseBtn2').off().on('click', function(){
                    $('#staffTaxDetails').modal('hide');
                    if(collectPerkTaxMode != 'employee'){
                        rowData.splice(12, 1);
                    }
                })
            }
            $('#staffTaxDetails').modal('show');
        })
    }

    function validate_approve_all(){
        bootbox.confirm({
            title: `Confirm - Approve All`,
            message: `<center>Do You Want To Approve All Tax Declarations</center>`,
            size: 'small',
            centerVertical: true,
            className: 'widthadjust',
            width: '50%',
            backdrop: true,
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    income_tax_all_staff_approve();
                }else{
                    $('#massGenerateButton').removeAttr('disabled','disabled').html('Generate');
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }

    async function income_tax_all_staff_approve() {
        $('#approveAllDeclaration').attr('disabled', 'disabled').html('Please wait...');
        $('#verify_not_approved #close_all_approve').attr('disabled', 'disabled');
        $('#verify_not_approved #cancel_all_approve').attr('disabled', 'disabled');
        var table = $('#filtered_list_tab').DataTable();
        var rowIDs = [];

        table.rows().every(function(rowIdx, tableLoop, rowLoop) {
            var data = this.data();
            var rowId = this.node().id;
            if (rowId.startsWith('row_')) {
                var idNumber = rowId.replace('row_', '');
                if (data[5] == "Submitted") {
                    rowIDs.push(idNumber);
                }
            }
        });

        var financial_year = $('#schedule_year').val();
        // console.log(rowIDs);
        for (let rowId of rowIDs) {
            // console.log(document.getElementById(`row_${rowId}`));
            try {
                await approve_staffs_by_id(rowId, financial_year);
            } catch (error) {
                console.error(error);
            }
        }

        // alert('All Tax Declarations are Approved');
        $('#approveAllDeclaration').removeAttr('disabled').html('Approve All');
        $('#verify_not_approved #close_all_approve').removeAttr('disabled');
        $('#verify_not_approved #cancel_all_approve').removeAttr('disabled');
        Swal.fire({
            icon: "success",
            title: "All Investment Declaration Are Successfully Approved",
            showConfirmButton: false,
            timer: 1500
        }).then(function() {
            window.location.reload();
        });
    }

    async function approve_staffs_by_id(staff_id, financial_year) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                $.ajax({
                    url: '<?php echo site_url('management/payroll/income_tax_staff_approve'); ?>',
                    method: 'POST',
                    data: { 'staff_id': staff_id, 'financial_year': financial_year },
                    success: function(response) {
                        var resData = $.parseJSON(response);
                        var row = $(`#row_${staff_id}`);
                        row.addClass('disabled-row');
                        if(resData){
                            row.find('td').eq(5).html('Approved').css('color', 'green');
                            resolve();
                        }else{
                            row.find('td').eq(5).html('Error').css('color', 'red');
                            reject();
                        }
                    },
                    error: function(error) {
                        var row = $(`#row_${staff_id}`);
                        row.addClass('disabled-row');
                        row.find('td').eq(4).html('Error').css('color', 'red');
                        reject(error);
                    }
                });
            }, 250);
        });
    }
    
    function incometax_unlock_btn() {
        var schedule_year = $('#schedule_year').val();
        
        bootbox.confirm({
            title: "Confirm",
            message: "Would you like to open the Investment Declaration Window?",
            className: 'widthadjust',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/incometax_declaration_unlock') ?>',
                        type: 'post',
                        data: {'schedule_year': schedule_year},
                        success: function(data) {
                            if (data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Opened",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    icon: "error",
                                    title: "Error",
                                    text: "Failed to deactivate income tax."
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: "Failed to send request: " + error
                            });
                        }
                    });
                } else {
                    // If the user clicks "No"
                    $('#income_lock_btn').prop('checked', false); // Turn off the slider
                }
            }
        });
    }

    function refreshCell(table, elementId, newValue) {
        var tds = document.querySelectorAll('#' + elementId);
        tds.forEach(function(td) {
            if (td.hasAttribute('data-dt-row')) {
                if (td.children.length == 0) {
                    td.innerHTML = newValue;
                } else {
                    td.innerHTML = newValue;
                }
            } else {
                td.innerHTML = newValue;
            }
            // td.style.border = 'none';
            td.style.height = '28px';
            td.style.color = '#495057';
            td.style.fontWeight = '500';
        });
        $('#investmentDeclarationStatusCount tbody').html('<tr><td colspan="7" style="text-align: center; font-weight: 500; color: #495057;">Refresh pending. Please refresh to view updated data.</td></tr>');
    }
    
    function income_tax_staff_approve(staff_id, staff_name) {
        $('#staffTaxDetails').modal('hide');
        bootbox.confirm({
            title: `Confirm - ${staff_name}`,
            message: `<h5><center>Are you sure you want to Approve Declaration?</center></h5>`,
            size: 'small',
            centerVertical: true,
            className: 'widthadjust',
            width: '50%',
            backdrop: true,
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                var selected_financial_year_id = $('#schedule_year option:selected').val();
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/income_tax_staff_approve'); ?>',
                        type: 'post',
                        data: {'staff_id': staff_id, 'financial_year': selected_financial_year_id},
                        success: function(data) {
                            parsed_data = $.parseJSON(data);
                            if (parsed_data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Approved",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    $('#staffTaxDetails').modal('hide');

                                    if ($('#verify_not_approved').is(':visible')) {
                                        $('#filtered_list_tab').html('');
                                        income_tax_declaration_close();
                                    }else{
                                        var table = $('#list_tab').DataTable();
                                        ['declarationStatus_' + staff_id, 'actionsId_' + staff_id].forEach(function(id) {
                                            refreshCell(table, id, id.includes('declarationStatus') ? 'Approved (Refresh Pending)' : 'Refresh Pending');
                                        });
                                        table.draw(false);
                                        $('#refresh_page_btn').prop('disabled', false);
                                    }
                                });
                            } else {
                                console.log(err);
                            }
                        },
                        error: function(err) {
                            console.log(err);
                        }
                    });
                }else{
                    $('#staffTaxDetails').modal('show');
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }
    
    function income_tax_staff_reopen(staff_id, staff_name) {
        $('#staffTaxDetails').modal('hide');
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        bootbox.confirm({
            title: `Confirm - ${staff_name}`,
            message: `<h5><center>Are you sure you want to Re-Open Declaration? </center></h5>`,
            size: 'small',
            centerVertical: true,
            className: 'widthadjust',
            width: '50%',
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/income_tax_staff_reopen'); ?>',
                        type: 'post',
                        data: {'staff_id': staff_id, 'financial_year': selected_financial_year_id},
                        success: function(data) {
                            parsed_data = $.parseJSON(data);
                            if (parsed_data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Re-Opened",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    $('#staffTaxDetails').modal('hide');
                                    var table = $('#list_tab').DataTable();
                                    ['declarationStatus_' + staff_id, 'actionsId_' + staff_id].forEach(function(id) {
                                        refreshCell(table, id, id.includes('declarationStatus') ? 'Reopened (Refresh Pending)' : 'Refresh Pending');
                                    });
                                    table.draw(false);
                                    $('#refresh_page_btn').prop('disabled', false);
                                });
                            } else {
                                console.log(err);
                            }
                        },
                        error: function(err) {
                            console.log(err);
                        }
                    });
                }else{
                    $('#staffTaxDetails').modal('show');
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }

    function income_tax_staff_reopen_after_approve(staffId, staff_name) {
        var financialYear = $('#schedule_year option:selected').val();
        Swal.fire({
            title: `Confirm - ${staff_name}`,
            html: `
                <div style="text-align: left; margin-top: 10px;">
                    <label class="d-flex align-items-center">
                        <input type="checkbox" class="swal-checkbox" id="reopenDeclaration" value="reopenDeclaration" style="margin-right: 10px;" />
                        <span>Reopen Declaration</span>
                    </label><br />
                    <label class="d-flex align-items-center">
                        <input type="checkbox" class="swal-checkbox" id="reopenDeclaraitonForProof" value="reopenDeclaraitonForProof" style="margin-right: 10px;" />
                        <span>Reopen Declaration for Proof Submission</span>
                    </label>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '<i class="fa fa-check"></i> Confirm',
            cancelButtonText: '<i class="fa fa-times"></i> Cancel',
            reverseButtons: true,
            didOpen: () => {
                const checkboxes = document.querySelectorAll('.swal-checkbox');
                const confirmButton = Swal.getConfirmButton();
                confirmButton.disabled = true;
                
                checkboxes.forEach((checkbox) => {
                    checkbox.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            // Uncheck the other checkbox
                            checkboxes.forEach((cb) => {
                                if (cb !== e.target) {
                                    cb.checked = false;
                                }
                            });
                            confirmButton.disabled = false;
                        } else {
                            // Disable confirm button if no checkbox is checked
                            const isAnyChecked = Array.from(checkboxes).some((cb) => cb.checked);
                            if (!isAnyChecked) {
                                confirmButton.disabled = true;
                            }
                        }
                    });
                });
            },
            preConfirm: () => {
                const selectedAction = document.querySelector('.swal-checkbox:checked').value;
                return selectedAction;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const action = result.value;
                Swal.fire({
                    title: 'Processing...',
                    html: 'Please wait while your request is being processed.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    allowEnterKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                if (action === "reopenDeclaration") {
                    // Perform functionality for Reopen Declaration
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/income_tax_staff_reopen'); ?>',
                        type: 'post',
                        data: { 'staff_id': staffId, 'financial_year': financialYear },
                        success: function(data) {
                            Swal.close();
                            const parsed_data = $.parseJSON(data);
                            if (parsed_data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Re-Opened",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    $('#staffTaxDetails').modal('hide');
                                    $('#list_staff').html('');
                                    get_income_declaration_details();
                                });
                            } else {
                                console.error("Error: Could not process the request.");
                            }
                        },
                        error: function(err) {
                            console.error(err);
                        }
                    });
                } else if (action === "reopenDeclaraitonForProof") {
                    // Perform functionality for Reopen Declaration for Proof Submission
                    incomeTaxReopenForProofSubmission(staffId, financialYear);
                }
            }
        });
    }

    function incomeTaxReopenDeclaration(staffId, staffName){
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        bootbox.confirm({
            title: `Confirm - ${staffName}`,
            message: `<h5><center>Are you sure you want to Re-Open Declaration? </center></h5>`,
            size: 'small',
            centerVertical: true,
            className: 'widthadjust',
            width: '50%',
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/income_tax_staff_reopen'); ?>',
                        type: 'post',
                        data: {'staff_id': staffId, 'financial_year': selected_financial_year_id},
                        success: function(data) {
                            parsed_data = $.parseJSON(data);
                            if (parsed_data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Re-Opened",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    var table = $('#list_tab').DataTable();
                                    ['declarationStatus_' + staffId, 'actionsId_' + staffId].forEach(function(id) {
                                        refreshCell(table, id, id.includes('declarationStatus') ? 'Reopened (Refresh Pending)' : 'Refresh Pending');
                                    });
                                    table.draw(false);
                                    $('#refresh_page_btn').prop('disabled', false);
                                });
                            } else {
                                console.log(err);
                            }
                        },
                        error: function(err) {
                            console.log(err);
                        }
                    });
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }

    function incomeTaxReopenForProofSubmission(staffId, financialYear){
        Swal.fire({
            title: 'Processing...',
            html: 'Please wait while your request is being processed.',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        $.ajax({
            url: '<?php echo site_url('management/payroll/incomeTaxReopenProofSubmission'); ?>',
            type: 'post',
            data: { 'staffId': staffId, 'financialYear': financialYear },
            success: function(data) {
                Swal.close();
                const parsed_data = $.parseJSON(data);
                if (parsed_data) {
                    let title = '';
                    let icon = 'success';
                    switch (parsed_data) {
                        case 1:
                            title = 'Income-Tax Declaration Re-Opened For Proof Submission Successfully.';
                            icon = 'success';
                            break;
                        case 2:
                            title = 'Income-Tax Declaration Re-Opened For Proof Submission Already Done.';
                            icon = 'info';
                            break;
                        case 3:
                            title = 'Income-Tax Declaration Not Approved.';
                            icon = 'error';
                            break;
                        default:
                            title = 'Something Went Wrong.';
                            icon = 'warning';
                            break;
                    }
                    Swal.fire({
                        icon: icon,
                        title: title,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(function() {
                        get_income_declaration_details();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: `Something Went Wrong. Please Try Again Later.`,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(function() {
                        get_income_declaration_details();
                    });
                    // console.error("Error: Could not process the request.");
                }
            },
            error: function(err) {
                Swal.fire({
                    icon: "error",
                    title: `Something Went Wrong. Please Try Again Later.`,
                    showConfirmButton: false,
                    timer: 1500
                }).then(function() {
                    get_income_declaration_details();
                });
            }
        });
    }
    
    function income_tax_declaration_close(argument) {
        var schedule_year = $('#schedule_year').val();

        var table = $('#list_tab').DataTable();

        var allSubmitted = true;
        var allApproved = true;
        var allProofSubmitted = true;
        var allProofApproved = true;
        var rowsNotApproved = [];
        var rowIDs = [];

        table.rows().every(function(rowIdx, tableLoop, rowLoop) {
            var data = this.data();
            var rowId = this.node().id;
            var status = String(data[4]).trim();

            if (rowId.startsWith('row_')) {
                var idNumber = rowId.replace('row_', '');
                if (status == "Submitted") {
                    rowIDs.push(idNumber);
                }
            }

            if(status == "Submitted (Proof Submission)"){
                allProofApproved = false;
            }

            if (status == "Submitted") {
                rowsNotApproved.push(data);
                allApproved = false;
            }

            if (status == "Open" || status == "Reopen") {
                allSubmitted = false;
            }

            if(status == 'Reopened (Proof Submission)'){
                allProofSubmitted = false;
            }
        });

        if (!allSubmitted) {
            $('#staying_rented_house_checkbox').prop('checked', false);
            // get_details_and_verify(rowIDs, rowsNotApproved);
            Swal.fire({
                icon: "info",
                title: "Few Income-Tax Declarations Are Not Submitted.",
                text: "Please verify before closing income declaration.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return;
        }

        if(!allProofSubmitted){
            $('#staying_rented_house_checkbox').prop('checked', false);
            // get_details_and_verify(rowIDs, rowsNotApproved);
            Swal.fire({
                icon: "info",
                title: "Few Income-Tax Declarations are not Submitted.",
                text: "Please verify before closing income declaration.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return;
        }

        if(!allProofApproved){
            $('#staying_rented_house_checkbox').prop('checked', false);
            // get_details_and_verify(rowIDs, rowsNotApproved);
            Swal.fire({
                icon: "info",
                title: "Few Income-Tax Declarations Proofs are not Approved.",
                text: "Please verify before closing income declaration.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return;
        }

        if(!allApproved){
            get_details_and_verify(rowIDs, rowsNotApproved);
            return;
        }

        bootbox.confirm({
            title: "Do you want to Close Income-Tax Declaration?",
            message: `<h5><center>Are you sure you want to Close Income-Tax Declaration Window?</center></h5>`,
            size: 'small',
            centerVertical: true,
            className: 'widthadjust',
            width: '50%',
            buttons: {
                confirm: {
                    label: "<i class='fa fa-check'></i> Yes",
                    className: 'btn-success'
                },
                cancel: {
                    label: "<i class='fa fa-times'></i> No",
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('management/payroll/income_tax_declaration_close'); ?>',
                        type: 'post',
                        data: {'schedule_year': schedule_year},
                        success: function(data) {
                            parsed_data = $.parseJSON(data);
                            if (parsed_data) {
                                Swal.fire({
                                    icon: "success",
                                    title: "Income-Tax Declaration Successfully Closed",
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function() {
                                    window.location.reload();
                                });
                            } else {
                                console.log(err);
                            }
                        },
                        error: function(err) {
                            console.log(err);
                        }
                    });
                }else{
                    $('#staying_rented_house_checkbox').prop('checked', false);
                }
            },
        }).find("div.modal-content").addClass("confirmWidth");
    }

    function get_details_and_verify(rowIDs, rowsNotApproved) {
        var i = 1;
        var truncatedData = rowsNotApproved.map((row, index) => {
            var slicedRow = row.filter((_, i) => i !== 7).slice(1, 14);
            slicedRow[0] = i++;
            return slicedRow;
        });

        $('#filtered_list_tab').empty();

        var tableHeaders = `
            <thead>
                <tr>
                    <th>#</th>
                    <th>Staff Name</th>
                    <th>Employee Code</th>
                    <th>Declaration Status</th>
                    <th>Joining Date</th>
                    <th>Staff Status</th>
                    <th>Yearly CTC</th>
                    <th>Selected Regime</th>
                    <th>Total Income</th>
                    <th>Availing Company Accommodation</th>
                    <th>Perquisite Income</th>
                    <th>Taxable Income</th>
                    <th>Total TDS</th>
                </tr>
            </thead>
            <tbody id="filtered_list_tab_tbody">
            </tbody>
        `;

        $('#filtered_list_tab').append(tableHeaders);

        truncatedData.forEach((row, index) => {
            var rowClass = index % 2 === 0 ? 'even' : 'odd';
            var tableRow = `<tr id="row_${rowIDs[index]}" class="${rowClass}">`;
            row.forEach(cell => {
                tableRow += `<td>${cell}</td>`;
            });
            tableRow += '</tr>';
            $('#filtered_list_tab_tbody').append(tableRow);
        });

        setTimeout(function(){
            $('#filtered_list_tab').DataTable({
                destroy: true,
                responsive: true,
                ordering: false,
                paging: false,
                scrollX: true
            })
        }, 500)

        $('#filtered_list_tab').css('white-space', 'nowrap');

        $('#verify_not_approved').modal('show');
    }

    // function get_re_cal_income_tax_staff_declartion(staff_id, selected_financial_year_id){
    //     $.ajax({
    //         url: '<?php //echo site_url('management/payroll/re_income_tax_staff_declartion'); ?>',
    //         type: 'post',
    //         data: {'staff_id': staff_id, 'financial_year': selected_financial_year_id},
    //         success: function(data) {
    //             parsed_data = $.parseJSON(data);

    //             console.log(parsed_data);
    //             return false;
                
    //         },
    //         error: function(err) {
    //             console.log(err);
    //         }
    //     });
    // }

    function sendProffSubmissionRemainder(){
        closeMenu();
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var employeecode = $(this).data('employeecode');
            if (!staff_info.some(staff => staff.id === staff_id)) {
                staff_info.push({
                    id: staff_id,
                    name: staff_name,
                    employeecode: employeecode
                });
            }
        });
        
        if (staff_info.length == 0) {
            Swal.fire({
                icon: "info",
                title: "Please Select At least One Staff.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();
        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        $('#staffInfoModal #staffSelectedRegime').hide();
        $('#staffInfoModal #staffdeclarationStatus').hide();
        $('#staffInfoModal').modal('show');
        $('#confirmBtn').prop('disabled', false);
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            var rows = $('#staffInfoTable tbody tr');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Sending Emails',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processNext(index) {
                if (index >= totalRows) {
                    Swal.fire({
                        title: 'Done!',
                        text: 'All Sending Remainder Email/s Has Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500,
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event); // calls resetStaffInfoModal
                            }
                            get_income_declaration_details(); // call after modal reset
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const statusCell = row.find('.status-cell');

                sendRemainderEmailToStaff(staffId, statusCell, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    setTimeout(() => processNext(index + 1), 300); // Delay of 300ms per request
                });
            }

            processNext(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var statusCell = $(this).find('.status-cell');
                
            //     sendRemainderEmailToStaff(staffId, statusCell, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'All emails have been processed.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //         }
            //     });
            // });
        });
    }

    function sendRemainderEmailToStaff(staffId, statusCell, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/sendTaxProofSubmissionRemainderEmailToStaff'); ?>',
            type: 'POST',
            data: { staff_id: staffId, 'financial_year': selected_financial_year_id },
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.text('Sent');
                    statusCell.css('color', 'green');
                } else {
                    statusCell.text('Not Sent');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.text('Not Sent');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function sendProffSubmissionRemainderIndividual(staffId, financial_year) {
        $.ajax({
            url: '<?php echo site_url('management/payroll/sendTaxProofSubmissionRemainderEmailToStaff'); ?>',
            type: 'POST',
            data: { staff_id: staffId, 'financial_year': financial_year },
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    Swal.fire({
                        title: 'Done',
                        text: 'Tax Proof Submission Remainder Email Sent Successfully!',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500,
                    });
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Something Went Wrong Please Try Again Later!',
                        icon: 'error',
                        showConfirmButton: false,
                        timer: 1500,
                    });
                }
                callback();
            },
            error: function (err) {
                Swal.fire({
                    title: 'Error',
                    text: 'Something Went Wrong Please Try Again Later!',
                    icon: 'error',
                    showConfirmButton: false,
                    timer: 1500,
                });
            }
        });
    }

    function send_tds_email(){
        closeMenu();
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var employeecode = $(this).data('employeecode');
            if (!staff_info.some(staff => staff.id === staff_id)) {
                staff_info.push({
                    id: staff_id,
                    name: staff_name,
                    employeecode: employeecode
                });
            }
        });
        
        if (staff_info.length == 0) {
            Swal.fire({
                icon: "info",
                title: "Please Select At least One Staff.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        // console.log(staff_info);
        // console.log(staff_ids);
        // console.log(staff_names);
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();

        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        $('#confirmBtn').prop('disabled', false);
        $('#staffInfoModal #staffSelectedRegime').hide();
        $('#staffInfoModal #staffdeclarationStatus').hide();
        $('#staffInfoModal').modal('show');
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            var rows = $('#staffInfoTable tbody tr');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Sending Emails',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processNext(index) {
                if (index >= totalRows) {
                    Swal.fire({
                        title: 'Done!',
                        text: 'All Sending Email/s Have Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event);
                            }
                            get_income_declaration_details(); // refresh data
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const statusCell = row.find('.status-cell');

                sendEmailToStaff(staffId, statusCell, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    setTimeout(() => processNext(index + 1), 300); // Delay between requests
                });
            }
            processNext(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var statusCell = $(this).find('.status-cell');
                
            //     sendEmailToStaff(staffId, statusCell, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'All emails have been processed.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //         }
            //     });
            // });
        });
    }

    function sendEmailToStaff(staffId, statusCell, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var selected_financial_year_id = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/send_email_staff_wise'); ?>',
            type: 'POST',
            data: { staff_id: staffId, 'financial_year': selected_financial_year_id },
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.text('Sent');
                    statusCell.css('color', 'green');
                } else {
                    statusCell.text('Not Sent');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.text('Not Sent');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function resetStaffInfoModal() {
        // get_income_declaration_details();
        $('#staffInfoTable tbody').empty();
        $('#staffInfoModal #proofSubmissionReopenMessage').hide();
        $('#staffInfoModal #staffSelectedRegime').hide();
        $('#staffInfoModal #selectRegimeDiv').removeClass('d-flex');
        $('#staffInfoModal #selectRegimeDiv').addClass('d-none');
        $('#staffInfoModal #regimeCount').removeClass('d-flex');
        $('#staffInfoModal #regimeCount').addClass('d-none');
    }

    async function income_tax_assinged_staff_wise(){
        var staff_ids = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            staff_ids.push($(this).val());
        });

        for(var i= 0; i<staff_ids.length; i++) {
            try {
                const result = await default_approve_income_tax_assinged_staff_wise(staff_ids[i]);
                if(i == staff_ids.length - 1) {
                    alert('Approval Sent Successfully');
                    // window.location.reload();
                }
            } catch (error) {
                console.error(error);
            }
        }
    }

    function default_approve_income_tax_assinged_staff_wise(staff_id){
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                $.ajax({
                    url: '<?php echo site_url('management/payroll/default_approve_income_tax_assinged_staff_wise'); ?>',
                    type: 'post',
                    data: {
                        'staff_id': staff_id
                    },
                    success: function(data) {
                        if(data) {
                            $("#incomeTaxStaffId_"+staff_id).attr('disabled','disabled');
                        } else {
                            $("#incomeTaxStaffId_"+staff_id).attr('disabled','disabled');
                        }
                        resolve();
                    },
                    error: function(err) {
                        console.log(err);
                        $("#incomeTaxStaffId_"+staff_id).attr('disabled','disabled');
                        reject(err);
                    }
                });
            }, 250);
        });
    }

    function income_tax_declaration_by_admin(staff_id, schedule_year){
        // console.log(staff_id, schedule_year);
        $.ajax({
            url: '<?php echo site_url('staff/payroll_controller/redirect_income_declaration_admin'); ?>',
            type: 'post',
            data: {
                'staff_id': staff_id,
                'schedule_year': schedule_year
            },
            success: function(response) {
                var data = JSON.parse(response);
                if (data.url) {
                    window.open(data.url, '_blank');
                } else {
                    console.log(data.error);
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function view_additional_allowance_break_down(staff_id, financial_year, selected_regime){
        $.ajax({
            url: "<?php echo site_url('staff/Payroll_controller/view_additional_allowance_break_down')?>",
            type: "POST",
            data: {
                'staff_id': staff_id,
                'financial_year': financial_year,
            },
            success: function (response) {
                $('#staffTaxDetails').modal('hide');
                let parsed_allowance_data = JSON.parse(response);
                if(Object.keys(parsed_allowance_data).length > 0 > 0){
                    showAllowanceBreakdown(parsed_allowance_data);
                } else {
                    Swal.fire({
                        title: "No Data Found",
                        icon: "warning",
                        timer: 1500,
                        showConfirmButton: false,
                    }).then(() => {
                        $('#staffTaxDetails').modal('show');
                    });
                }
            }
        });
    }

    function formatIndianCurrency(amount) {
        const formattedAmount = amount.toFixed(2);
        return `₹${parseFloat(formattedAmount).toLocaleString('en-IN')}`;
    }

    function showAllowanceBreakdown(data) {
        let tableHtml = `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 1px solid #ddd;">
                        <th style="padding: 8px; text-align: left;">Allowance</th>
                        <th style="padding: 8px; text-align: right;">Amount</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Variables to calculate total
        let totalAmount = 0;

        // Loop through data to build rows
        Object.values(data).forEach((item) => {
            tableHtml += `
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;text-align: left;">${item.display_name}</td>
                    <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd;">${item.amount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
                </tr>
            `;
            totalAmount += item.amount;
        });

        // Add total row
        tableHtml += `
            </tbody>
            <tfoot>
                <tr>
                    <td style="padding: 8px; font-weight: bold;text-align: left;">Total</td>
                    <td style="padding: 8px; text-align: right; font-weight: bold;">${totalAmount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
                </tr>
            </tfoot>
        </table>
        `;
        
        Swal.fire({
            title: "Allowance Breakdown",
            html: tableHtml,
            confirmButtonText: "Close",
        }).then(() => {
            $('#staffTaxDetails').modal('show');
        });
    }

    function openPerformSwal() {
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            if (!staff_info.some(staff => staff.id === staff_id)) {
                staff_info.push({
                    id: staff_id,
                    name: staff_name
                });
            }
        });
        
        if (staff_info.length <= 0) {
            Swal.fire({
                icon: "info",
                title: "Please Select At least One Staff.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        Swal.fire({
            title: 'Select Action',
            html: `
                <div style="text-align: left;">
                    <label class="d-flex align-items-center">
                        <input type="checkbox" id="sendEmail" value="sendEmail" class="swal-checkbox" style="margin-right: 10px;">
                        <span>Send Email</span>
                    </label>
                    <br>
                    <label class="d-flex align-items-center">
                        <input type="checkbox" id="reopenDeclaration" value="reopenDeclaration" class="swal-checkbox" style="margin-right: 10px;">
                        <span>Reopen Declaration</span>
                    </label>
                    <br>
                    <label class="d-flex align-items-center">
                        <input type="checkbox" id="reopenDeclarationForProof" value="reopenDeclarationForProof" class="swal-checkbox" style="margin-right: 10px;">
                        <span">Reopen Declaration for Proof Submission</span>
                    </label>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Submit',
            reverseButtons: true,
            didOpen: () => {
                const checkboxes = document.querySelectorAll('.swal-checkbox');
                const confirmButton = Swal.getConfirmButton();
                confirmButton.disabled = true;
                checkboxes.forEach((checkbox) => {
                    checkbox.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            checkboxes.forEach((cb) => {
                                if (cb !== e.target) {
                                    cb.checked  = false;
                                }
                            });
                            confirmButton.disabled = false;
                        } else {
                            const isAnyChecked = Array.from(checkboxes).some((cb) => cb.checked);
                            if (!isAnyChecked) {
                                confirmButton.disabled = true;
                            }
                        }
                    });
                });
            },
            preConfirm: () => {
                const selectedAction = Array.from(document.querySelectorAll('.swal-checkbox'))
                    .filter((cb) => cb.checked)
                    .map((cb) => cb.value)[0];

                if (!selectedAction) {
                    Swal.showValidationMessage('You need to select an action!');
                }
                return selectedAction;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                switch (result.value) {
                    case 'sendEmail':
                        send_tds_email();
                        break;
                    case 'reopenDeclaration':
                        reopenDeclarationForStaff();
                        break;
                    case 'reopenDeclarationForProof':
                        reopenForProofSubmission();
                        break;
                }
            }
        });
    }

    function reopenForProofSubmission(){
        closeMenu();
        var staff_info = [];
        let totalRegimeCount = 0;
        let newRegimeCount = 0;
        let oldRegimeCount = 0;
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var staff_status = $(this).data('staffstatus');
            var staff_tax_regime = $(this).data('regime');
            var proofsubmissionstatus = $(this).data('proofsubmissionstatus');
            var declarationstatus = $(this).data('declarationstatus');
            var employeecode = $(this).data('employeecode');
            if(proofsubmissionstatus == 0 && staff_tax_regime == 2 && staff_status == 2){
                if (!staff_info.some(staff => staff.id === staff_id)) {
                    totalRegimeCount++;
                    if(staff_tax_regime == 1){
                        newRegimeCount++;
                    } else {
                        oldRegimeCount++;
                    }
                    staff_info.push({
                        id: staff_id,
                        name: staff_name,
                        regime: staff_tax_regime,
                        employeecode: employeecode,
                        declarationStatus: declarationstatus
                    });
                }
            }
        });
        if (staff_info.length == 0) {
            Swal.fire({
                icon: "info",
                title: "Please select a staff member who has<br>opted for the Old Regime and whose<br>declaration status is 'Approved'.",
                showConfirmButton: true,
                confirmButtonText: "OK",
                width: 600,
            });
            return false;
        }
        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        // var staffSelectedRegime = staff_info.map(function(staff) {
        //     return staff.
        // })
        // console.log(staff_info);
        // console.log(staff_ids);
        // console.log(staff_names);
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();

        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}" data-regime="${staff.regime}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td>${staff.regime == 1 ? 'New Regime' : 'Old Regime' } </td>
                    <td>${staff.declarationStatus}</td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        // $('#staffInfoTable').DataTable({
        //     'destroy': true,
        //     'ordering': false,
        //     'info': false,
        //     'paging': false,
        // });
        $('#confirmBtn').prop('disabled', false);
        // $('#staffInfoModal #proofSubmissionReopenMessage').show();
        $('#staffInfoModal #staffSelectedRegime').show();
        $('#staffInfoModal #staffdeclarationStatus').show();
        // $('#staffInfoModal #selectRegimeDiv').removeClass('d-none');
        // $('#staffInfoModal #selectRegimeDiv').addClass('d-flex');
        // $('#staffInfoModal #regimeCount').removeClass('d-none');
        // $('#staffInfoModal #regimeCount').addClass('d-flex');
        // $('#staffInfoModal #regimeCount #totalRegimeCount').html(totalRegimeCount);
        // $('#staffInfoModal #regimeCount #newRegimeCount').html(newRegimeCount);
        // $('#staffInfoModal #regimeCount #oldRegimeCount').html(oldRegimeCount);
        $('#staffInfoModal').modal('show');
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            let selectedRegimeForProofSubmission = $('#staffInfoModal #regimeForReopenProofSubmission').val();
            var rows = $('#staffInfoTable tbody tr:visible');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Reopening Tax Declarations For Proof Submission',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processNext(index) {
                if (index >= totalRows) {
                    Swal.fire({
                        title: 'Done!',
                        text: 'The Tax Declarations Reopen For Proof Submission Has Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event);
                            }
                            get_income_declaration_details(); // refresh data
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const statusCell = row.find('.status-cell');

                reopenForProofSubmissionForStaff(staffId, statusCell, selectedRegimeForProofSubmission, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    setTimeout(() => processNext(index + 1), 300); // adjust delay if needed
                });
            }
            processNext(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var statusCell = $(this).find('.status-cell');
            //     reopenForProofSubmissionForStaff(staffId, statusCell, selectedRegimeForProofSubmission, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'The Tax Declarations Have Been Reopened For The Selected Staff/s.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             // get_income_declaration_details();
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //         }
            //     });
            // });
        });
    }

    function reopenForProofSubmissionForStaff(staffId, statusCell, selectedRegimeForProofSubmission, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var financialYear = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/incomeTaxReopenProofSubmission'); ?>',
            type: 'POST',
            data: { 'staffId': staffId, 'financialYear': financialYear, 'selectedRegimeForProofSubmission': selectedRegimeForProofSubmission},
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.html('Tax Declararion Reopened');
                    statusCell.css('color', 'green');
                } else if(data == 2){
                    statusCell.html('Already Done');
                    statusCell.css('color', 'blue');
                } else if(data == 3){
                    statusCell.html('Declaration Not Approved');
                    statusCell.css('color', 'red');
                } else {
                    statusCell.html('Error');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.html('Error');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function massApplyNewRegime(){
        closeMenu();
        let staff_status_income = $('#staff_status_income').val();
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var staff_tax_regime = $(this).data('regime');
            var employeecode = $(this).data('employeecode');
            var declarationstatus = $(this).data('declarationstatus');
            var proofsubmissionstatus = $(this).data('proofsubmissionstatus');

            if(declarationstatus == 'Reopened' || proofsubmissionstatus == 'Reopened' || declarationstatus == 'Open'){
                if (!staff_info.some(staff => staff.id === staff_id)) {
                    staff_info.push({
                        id: staff_id,
                        name: staff_name,
                        regime: staff_tax_regime,
                        employeecode: employeecode,
                        declarationstatus: declarationstatus,
                    });
                }
            }
        });
        if (staff_info.length == 0) {
            Swal.fire({
                icon: "info",
                title: "Please select at least one staff member whose tax regime is not 'New Regime'.",
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        // var staffSelectedRegime = staff_info.map(function(staff) {
        //     return staff.
        // })
        // console.log(staff_info);
        // console.log(staff_ids);
        // console.log(staff_names);
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();
        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}" data-regime="${staff.regime}" data-staff-declarationstatus="${staff.declarationstatus}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td>${staff.regime == 1 ? 'New Regime' : 'Old Regime' } </td>
                    <td>${staff.declarationstatus} </td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        $('#confirmBtn').prop('disabled', false);
        $('#staffInfoModal #staffSelectedRegime').show();
        $('#staffInfoModal #staffdeclarationStatus').show();
        $('#staffInfoModal').modal('show');
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            var rows = $('#staffInfoTable tbody tr:visible');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Applying New Regime For Selected Staff/s',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processNext(index) {
                if (index >= totalRows) {
                    Swal.fire({
                        title: 'Done!',
                        text: 'The Apply New Regime Has Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event); // reset modal
                            }
                            get_income_declaration_details(); // reload table
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const declarationstatus = row.data('staff-declarationstatus');
                const statusCell = row.find('.status-cell');

                massApplyNewRegimeForStaff(staffId, declarationstatus, statusCell, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    setTimeout(() => processNext(index + 1), 300); // adjust delay as needed
                });
            }
            processNext(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var declarationstatus = $(this).data('staff-declarationstatus');
            //     var statusCell = $(this).find('.status-cell');
            //     massApplyNewRegimeForStaff(staffId, declarationstatus, statusCell, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'The apply new regime process has been completed for the selected staff. Please verify once.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             // get_income_declaration_details();
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //         }
            //     });
            // });
        });
    }

    function massApplyNewRegimeForStaff(staffId, declarationstatus, statusCell, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var financialYear = $('#schedule_year option:selected').val();
        let remarks = 'Mass Apply New Regime';
        $.ajax({
            url: '<?php echo site_url('management/payroll/apply_new_regime_to_staff_by_id'); ?>',
            type: 'POST',
            data: {'staff_id': staffId, 'financial_year': financialYear, 'status': declarationstatus, 'remarks': remarks},
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.text('New Regime Applied');
                    statusCell.css('color', 'green');
                } else {
                    statusCell.text('Failed');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.html('Error');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function massApproveDeclarations(){
        closeMenu();
        let staff_status_income = $('#staff_status_income').val();
        var staff_info = [];

        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var staff_tax_regime = $(this).data('regime');
            var employeecode = $(this).data('employeecode');
            var declarationstatus = $(this).data('declarationstatus');
            var proofsubmissionstatus = $(this).data('proofsubmissionstatus');
            
            if(proofsubmissionstatus == 0 && declarationstatus == 'Submitted') {
                if (!staff_info.some(staff => staff.id === staff_id)) {
                    staff_info.push({
                        id: staff_id,
                        name: staff_name,
                        regime: staff_tax_regime,
                        employeecode: employeecode,
                        declarationstatus: declarationstatus,
                    });
                }
            }
        });

        if (staff_info.length == 0) {
            Swal.fire({
                icon: "info",
                title: 'No eligible staff members selected for approval',
                text: 'Please select staff members with Submitted status and no proof submission',
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }

        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        // var staffSelectedRegime = staff_info.map(function(staff) {
        //     return staff.
        // })
        // console.log(staff_info);
        // console.log(staff_ids);
        // console.log(staff_names);
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();

        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}" data-regime="${staff.regime}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td>${staff.regime == 1 ? 'New Regime' : 'Old Regime' } </td>
                    <td>${staff.declarationstatus}</td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        $('#confirmBtn').prop('disabled', false);
        $('#staffInfoModal #staffSelectedRegime').show();
        $('#staffInfoModal #staffdeclarationStatus').show();
        $('#staffInfoModal').modal('show');
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            var rows = $('#staffInfoTable tbody tr:visible');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Approving Tax Declarations For Selected Staff/s',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processEmail(index) {
                if (index >= totalRows) {
                    // All emails sent
                    Swal.fire({
                        title: 'Done!',
                        text: 'The Tax Declarations Approve Has Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event);
                            }
                            get_income_declaration_details();
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const statusCell = row.find('.status-cell');

                approveDeclarationForStaffAjax(staffId, statusCell, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    // Process next row after 300ms delay
                    setTimeout(() => processEmail(index + 1), 300);
                });
            }
            processEmail(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var statusCell = $(this).find('.status-cell');
                
            //     approveDeclarationForStaffAjax(staffId, statusCell, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'The Tax Declarations Have Been Approved For The Selected Staff/s.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //             // get_income_declaration_details();
            //         }
            //     });
            // });
        });
    }

    function approveDeclarationForStaffAjax(staffId, statusCell, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var financialYear = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/income_tax_staff_approve'); ?>',
            type: 'POST',
            data: { 'staff_id': staffId, 'financial_year': financialYear},
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.text('Approved');
                    statusCell.css('color', 'green');
                } else {
                    statusCell.text('Failed');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.text('Failed');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function reopenDeclarationForStaff(){
        closeMenu();
        let staff_status_income = $('#staff_status_income').val();
        var staff_info = [];
        $('.DTFC_Cloned tbody td div input.income_tax_staff_id:checked').each(function () {
            var staff_id = $(this).val();
            var staff_name = $(this).data('name');
            var staff_tax_regime = $(this).data('regime');
            var employeecode = $(this).data('employeecode');
            var proofsubmissionstatus = $(this).data('proofsubmissionstatus');
            var declarationstatus = $(this).data('declarationstatus');
            if(proofsubmissionstatus == 0 && declarationstatus != 'Open' && declarationstatus != 'Reopened'){
                if (!staff_info.some(staff => staff.id === staff_id)) {
                    staff_info.push({
                        id: staff_id,
                        name: staff_name,
                        regime: staff_tax_regime,
                        employeecode: employeecode,
                        declarationstatus: declarationstatus
                    });
                }
            }
        });
        if (staff_info.length == 0) {
            let title = (staff_status_income == 'Reopened (Proof Submission)') ? 'For the selected staff members, the tax declarations have been reopened for proof submission.' : (staff_status_income == 'Reopened') ? 'For the selected staff members, the tax declarations have been reopened.' : 'Please select at least one staff member whose declaration status is not "Open" or "Reopened".';
            Swal.fire({
                icon: "info",
                title: title,
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
            return false;
        }
        var staff_ids = staff_info.map(function(staff) {
            return staff.id;
        });
        var staff_names = staff_info.map(function(staff) {
            return staff.name;
        });
        // var staffSelectedRegime = staff_info.map(function(staff) {
        //     return staff.
        // })
        // console.log(staff_info);
        // console.log(staff_ids);
        // console.log(staff_names);
        var tableBody = $('#staffInfoTable tbody');
        tableBody.empty();

        staff_info.forEach((staff, index) => {
            tableBody.append(`
                <tr data-staff-id="${staff.id}" data-regime="${staff.regime}">
                    <td>${index + 1}</td>
                    <td>${staff.name} ${staff.employeecode != '' ? `(${staff.employeecode})` : '' } </td>
                    <td>${staff.regime == 1 ? 'New Regime' : 'Old Regime' } </td>
                    <td>${staff.declarationstatus}</td>
                    <td class="status-cell">-</td>
                </tr>
            `);
        });
        $('#confirmBtn').prop('disabled', false);
        $('#staffInfoModal #staffSelectedRegime').show();
        $('#staffInfoModal #staffdeclarationStatus').show();
        $('#staffInfoModal').modal('show');
        $('#email_cancel_btn, #email_close_btn').each(function () {
            if (!this.originalOnClick) {
                this.originalOnClick = this.onclick; // save original (resetStaffInfoModal)
            }
            this.onclick = null; // clear any previous combo handlers
        });
        $('#confirmBtn').off('click').on('click', function() {
            var rows = $('#staffInfoTable tbody tr:visible');
            var totalRows = rows.length;
            $('#email_cancel_btn').prop('disabled', true);
            $('#email_close_btn').prop('disabled', true);
            $('#confirmBtn').prop('disabled', true);
            var emailsSent = 0;
            Swal.fire({
                title: 'Reopening Tax Declarations For Proof Submission',
                html: '<progress id="emailProgressBar" value="0" max="100" style="width: 100%;"></progress>' + '<div id="emailPercentage">0%</div>',
                allowOutsideClick: false,
                showConfirmButton: false
            });
            function processReopen(index) {
                if (index >= totalRows) {
                    Swal.fire({
                        title: 'Done!',
                        text: 'The Tax Declarations Reopen Has Been Processed For The Selected Staff/s.',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });

                    $('#email_cancel_btn, #email_close_btn').prop('disabled', false);

                    $('#email_cancel_btn, #email_close_btn').each(function () {
                        const self = this;
                        this.onclick = function (event) {
                            if (typeof self.originalOnClick === 'function') {
                                self.originalOnClick.call(this, event);
                            }
                            get_income_declaration_details();
                        };
                    });

                    return;
                }

                const row = $(rows[index]);
                const staffId = row.data('staff-id');
                const statusCell = row.find('.status-cell');

                reopenDeclarationForStaffAjax(staffId, statusCell, function () {
                    emailsSent++;
                    const progress = (emailsSent / totalRows) * 100;
                    $('#emailProgressBar').val(progress);
                    $('#emailPercentage').text(Math.round(progress) + '%');

                    setTimeout(() => processReopen(index + 1), 300); // delay before next request
                });
            }
            processReopen(0);
            // rows.each(function () {
            //     var staffId = $(this).data('staff-id');
            //     var statusCell = $(this).find('.status-cell');
                
            //     reopenDeclarationForStaffAjax(staffId, statusCell, function() {
            //         emailsSent++;
            //         var progress = (emailsSent / totalRows) * 100;
            //         $('#emailProgressBar').val(progress);
            //         $('#emailPercentage').text(Math.round(progress) + '%');

            //         if (emailsSent === totalRows) {
            //             Swal.fire({
            //                 title: 'Done!',
            //                 text: 'The Tax Declarations Have Been Reopened For The Selected Staff/s.',
            //                 icon: 'success',
            //                 showConfirmButton: false,
            //                 timer: 1500,
            //             });
            //             $('#email_cancel_btn').prop('disabled', false);
            //             $('#email_close_btn').prop('disabled', false);
            //             // get_income_declaration_details();
            //             $('#email_cancel_btn, #email_close_btn').each(function () {
            //                 const self = this;
            //                 this.onclick = function (event) {
            //                     if (typeof self.originalOnClick === 'function') {
            //                         self.originalOnClick.call(this, event); // calls resetStaffInfoModal
            //                     }
            //                     get_income_declaration_details(); // call after modal reset
            //                 };
            //             });
            //         }
            //     });
            // });
        });
    }

    function reopenDeclarationForStaffAjax(staffId, statusCell, callback) {
        statusCell.html('<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>');
        var financialYear = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/income_tax_staff_reopen'); ?>',
            type: 'POST',
            data: { 'staff_id': staffId, 'financial_year': financialYear},
            success: function (response) {
                let data = JSON.parse(response);
                if (data == 1) {
                    statusCell.text('Reopened');
                    statusCell.css('color', 'green');
                } else {
                    statusCell.text('Failed');
                    statusCell.css('color', 'red');
                }
                callback();
            },
            error: function () {
                statusCell.text('Failed');
                statusCell.css('color', 'red');
                callback();
            }
        });
    }

    function viewProofAttachments(staffId, staffName, grandTotalRent){
        let financialYear = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/viewProofAttachments'); ?>',
            type: 'POST',
            data: { 'staffId': staffId, 'financialYear': financialYear},
            success: function (response) {
                let data = JSON.parse(response);
                if(data.length > 0){
                    if(data.length == 1 && data[0].hasOwnProperty("proof_submission_status")){
                        approveRejectStaffFinalAttachments(staffId, data[0], staffName, financialYear, grandTotalRent);
                    } else {
                        displayAttachments(staffId, data, staffName, grandTotalRent);
                    }
                } else {
                    Swal.fire({
                        icon: "info",
                        title: `No Proof Attachments Found For ${staffName}.`,
                        showConfirmButton: false,
                        timer: 1500,
                    });
                }
            },
            error: function (error) {
                console.log(error);
            }
        });
    }

    function approveRejectStaffFinalAttachments(staffId, data, staffName, financialYear, grandTotalRent){
        let rowID = data.id;
        let proofSubmissionStatus = data.proof_submission_status;
        if(proofSubmissionStatus == 'Submitted'){
            Swal.fire({
                title: `${staffName} has no investments`,
                text: "Please choose an action:",
                icon: "warning",
                showCancelButton: true,
                showDenyButton: true,
                confirmButtonText: "Approve",
                denyButtonText: "Reject",
                cancelButtonText: "Close",
                reverseButtons: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            }).then((result) => {
                if (result.isConfirmed) {
                    sendApproveRejectRequest(rowID, staffId, "Approved", financialYear);
                } else if (result.isDenied) {
                    sendApproveRejectRequest(rowID, staffId, "Rejected", financialYear);
                }
            });
        } else {
            Swal.fire({
                title: `${staffName} Tax Declaration For No Investment Proof has already been ${proofSubmissionStatus}`,
                // text: "Please choose an action:",
                icon: "info",
                showCancelButton: false,
                showDenyButton: false,
                confirmButtonText: "Close",
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            });
        }
    }

    function sendApproveRejectRequest(rowID, staffId, status, financialYear) {
        Swal.fire({
            title: 'Processing...',
            html: 'Please wait while we process your request.',
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        $.ajax({
            url: '<?php echo site_url('management/payroll/sendApproveRejectRequest'); ?>',
            type: "POST",
            data: {
                rowID: rowID,
                staffId: staffId,
                status: status,
                financialYear: financialYear
            },
            success: function(response) {
                Swal.close();
                let parsedData = JSON.parse(response);
                if(parsedData){
                    Swal.fire({
                        title: `Proof Submission ${status}`,
                        text: `The Declaration has been ${status.toLowerCase()} successfully and email is sent`,
                        icon: "success",
                        timer: 1500,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        title: "Error",
                        text: "Failed to send request. Please try again.",
                        icon: "error",
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
                setTimeout(() => {
                    get_income_declaration_details();
                }, 1500);
            },
            error: function() {
                Swal.fire({
                    title: "Error",
                    text: "Something went wrong. Please try again.",
                    icon: "error"
                });
            }
        });
    }

    function displayAttachments(staffId, attachmentsData, staffName, grandTotalRent){
        const tableBody = document.getElementById('attachmentsTableBody');
        tableBody.innerHTML = '';

        attachmentsData.forEach((attachment, index) => {
            let approvalStatus = '-';
            let approvalClass = '';
            let timeApprovedRejected = '-';
            if (attachment.approved_by !== '-') {
                approvalStatus = attachment.approved_by;
                timeApprovedRejected = attachment.approved_on;
                approvalClass = 'text-success';
            } else if (attachment.rejected_by !== '-') {
                approvalStatus = attachment.rejected_by;
                timeApprovedRejected = attachment.rejected_on;
                approvalClass = 'text-danger';
            }
            let investmentAmount = attachment.amount;
            let formatter = new Intl.NumberFormat('en-IN', {style: 'currency', currency: 'INR', minimumFractionDigits: 2});
            let formattedInvestmentAmount = formatter.format(investmentAmount);

            // Create table row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${getColumnName(attachment.column_name)}</td>
                <td>${formattedInvestmentAmount}</td>
                <td>${attachment.file_name}</td>
                <td class="${approvalClass}">${attachment.status}</td>
                <td class="">${approvalStatus}</td>
                <td class="">${timeApprovedRejected}</td>
                <td>
                    <button class="btn btn-secondary" onclick="openViewModal('${attachment.proof_file_url}', '${attachment.file_name}', '${attachment.column_name}', ${attachment.id}, ${staffId}, '${staffName}', '${attachment.status}', '${formattedInvestmentAmount}', ${grandTotalRent})">View</button>
                </td>
            `;
            tableBody.appendChild(row);
        });
        $('#attachmentsModal #staffName').html(staffName);
        // Show the modal
        // const attachmentsModal = new bootstrap.Modal(document.getElementById('attachmentsModal'));
        $('#attachmentsModal #viewTaxCalculationBtn').off('click').on('click', function(){
            openModal(staffId, staffName, grandTotalRent, 0, 'View Only', 'Attachments Table');
        })
        // if(isSuperAdmin){
            $('#attachmentsModal #viewInvestmentChangesBtn').off('click').on('click', function(){
                // $('#attachmentsModal').hide();
                openHistoryModal(staffId, staffName);
            })
        // }

        $('#attachmentsModal').show();
    }

    function openHistoryModal(staffId, staffName){
        $('#attachmentsModal').hide();
        let financialYear = $('#schedule_year option:selected').val();
        $.ajax({
            url: '<?php echo site_url('management/payroll/getStaffEditHistoryPayroll'); ?>',
            type: 'POST',
            data: { 'staffId': staffId, 'financialYear': financialYear},
            success: function (response) {
                let parsedData = JSON.parse(response);
                if(parsedData.length > 0){
                    displayEditHistory(parsedData, staffId, staffName);
                } else {
                    Swal.fire({
                        icon: "info",
                        title: `No Edit History Found For ${staffName}.`,
                        showConfirmButton: false,
                        timer: 1500,
                    }).then(() => {
                            $('#attachmentsModal').show();
                    });
                }
            },
            error: function (error) {
                console.log(error);
            }
        });
    }

    function displayEditHistory(editHistoryData, staffId, staffName){
        $('#viewStaffPayrollEditHistory #viewStaffPayrollEditHistoryLabel #staffName').html(staffName);
        const tableBody = document.getElementById('viewStaffHistoryTableBody');
        tableBody.innerHTML = '';
        editHistoryData.forEach((edit, index) => {
            // Create table row
            const row = document.createElement('tr');
            row.innerHTML = `
            <td style="white-space: nowrap;">${index + 1}</td>
            <td style="width: 40%; word-break: break-all">${formatKeyValue(edit.old_data)}</td>
            <td style="width: 40%; word-break: break-all">${formatKeyValue(edit.new_data)}</td>
            <td style="white-space: nowrap;">${edit.edited_by}</td>
            <td style="white-space: nowrap;">${edit.edited_on}</td>
            `;
            tableBody.appendChild(row);
        });
        $('#viewStaffPayrollEditHistory').show();
    }

    function formatKeyValue(data) {
        if (!data) return "<span class='text-muted'>N/A</span>";

        try {
            let parsedData = JSON.parse(data);
            let listItems = Object.entries(parsedData)
                .map(([key, value]) => `<li><strong>${key}:</strong> ${value}</li>`)
                .join("");
            return `<ul class="list-unstyled">${listItems}</ul>`;
        } catch (error) {
            return `<span class="text-danger">Invalid Data</span>`; // If JSON parsing fails
        }
    }

    function openViewModal(fileUrl, fileName, columnName, attachmentId, staffId, staffName, status, formattedInvestmentAmount, grandTotalRent) {
        document.getElementById('proofFileIframe').src = fileUrl;
        $('#viewProofModal #columnName').html(formattedInvestmentAmount + ' ' + getColumnName(columnName));
        // $('#viewProofModal #fileName').html(fileName);
        if(status == 'Approved'){
            $('#approveDeclarationProof').prop('disabled',true);
            $('#rejectDeclarationProof').prop('disabled',true);
        } else {
            $('#approveDeclarationProof').prop('disabled',false);
            $('#rejectDeclarationProof').prop('disabled',false);
            $('#approveDeclarationProof').off().on('click', function(){
                approveRejectStaffProofAttachment(columnName, attachmentId, staffId, staffName, grandTotalRent, 'Approved');
            })
            $('#rejectDeclarationProof').off().on('click', function(){
                approveRejectStaffProofAttachment(columnName, attachmentId, staffId, staffName, grandTotalRent, 'Rejected');
            })
        }
        $('#attachmentsModal').hide();
        $('#viewProofModal').show();
    }

    function approveRejectStaffProofAttachment(columnName, attachmentId, staffId, staffName, grandTotalRent, status) {
        $('#closeAttachmentModal').each(function() {
            let existingOnClick = this.onclick;

            this.onclick = function(event) {
                if (existingOnClick) existingOnClick.call(this, event);
                get_income_declaration_details();
            };
        });
        // $('#attachmentsModal').hide();
        $('#viewProofModal').hide();
        let financialYear = $('#schedule_year option:selected').val();
        // Implement approval logic here
        titleStatus = status == 'Approved' ? 'Approve' : 'Reject' ;
        let displayColumnName = getColumnName(columnName);
        Swal.fire({
            title: `${titleStatus} ${displayColumnName} Proof Attachment for ${staffName}?`,
            // text: `Are you sure you want to ${titleStatus} this attachment?`,
            html: `<p>Are you sure you want to ${titleStatus} this attachment?</p>
                    ${status === 'Rejected' ? '<textarea style="width: 80% !important;" id="remarks" class="swal2-textarea" placeholder="Enter remarks (Required)"></textarea>' : ''}`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes, ${titleStatus} it!`,
            cancelButtonText: "No, cancel",
            reverseButtons: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            preConfirm: () => {
                if (status == 'Rejected') {
                    let remarks = document.getElementById('remarks').value.trim();
                    if (!remarks) {
                        Swal.showValidationMessage('Remarks are required when rejecting.');
                        return false;
                    }
                    return remarks;
                }
                return '';
            },
            width: '600px',
            didOpen: () => {
                // Clear remarks text area when modal is opened
                if (status === 'Rejected') {
                    document.getElementById('remarks').value = '';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                let remarks = result.value;
                Swal.fire({
                    title: 'Processing...',
                    html: 'Please wait while we process your request.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    allowEnterKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                $.ajax({
                    url: '<?php echo site_url('management/payroll/approveRejectStaffProofAttachment'); ?>',
                    type: 'POST',
                    data: { 
                        'staffId': staffId,
                        'financialYear': financialYear,
                        'attachmentId': attachmentId,
                        'status': status,
                        'columnName': displayColumnName,
                        'remarks': remarks
                    },
                    success: function (response) {
                        Swal.close();
                        const iframe = document.getElementById('proofFileIframe');
                        iframe.src = '';
                        let data = JSON.parse(response);
                        if(data.length > 0){
                            if(data[0].approveRejectedStatus == true && data[0].reopenStatus == false && data[0].approvedStatus == false){
                                let icon = status == 'Approved' ? 'success' : 'info' ;
                                Swal.fire({
                                    icon: icon,
                                    title: `The Proof Document has been ${status}`,
                                    showConfirmButton: false,
                                    timer: 1500,
                                }).then((result)=>{
                                    viewProofAttachments(staffId, staffName, grandTotalRent);
                                });
                            } else if(data[0].approveRejectedStatus == true && data[0].reopenStatus == true && data[0].approvedStatus == false){
                                Swal.fire({
                                    icon: 'info',
                                    title: `Staff investment declaration reopened, email sent for resubmission.`,
                                    showConfirmButton: false,
                                    timer: 1500,
                                }).then((result)=>{
                                    viewProofAttachments(staffId, staffName, grandTotalRent);
                                });
                            } else if(data[0].approveRejectedStatus == true && data[0].approvedStatus == true && data[0].reopenStatus == false){
                                Swal.fire({
                                    icon: 'info',
                                    title: `Staff investment declaration proof approved, approval email sent.`,
                                    showConfirmButton: false,
                                    timer: 1500,
                                }).then((result)=>{
                                    viewProofAttachments(staffId, staffName, grandTotalRent);
                                });
                            }
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: `Somethiing Went Wrong. Please Try Again Later.`,
                                showConfirmButton: false,
                                timer: 1500,
                            }).then((result)=>{
                                viewProofAttachments(staffId, staffName, grandTotalRent);
                            });
                        }
                    },
                    error: function (error) {
                        Swal.close();
                        const iframe = document.getElementById('proofFileIframe');
                        iframe.src = '';
                        console.log(error);
                        Swal.fire({
                            icon: "error",
                            title: "An error occurred. Please try again.",
                            showConfirmButton: false,
                            timer: 1500,
                        }).then(() => {
                            viewProofAttachments(staffId, staffName, grandTotalRent);
                        });
                    }
                });
            } else {
                // viewProofAttachments(staffId, staffName);
                $('#viewProofModal').show();
            }
        });
    }

    function getColumnName(columnName) {
        const categories = {
            'availing_company_accommodation': 'Availing Company Accommodation',
            'grand_total_rent': 'Rent',
            'landlord_pan_card': 'PAN of the landlord',
            'public_provident_fund': 'Public Provident Fund (PPF)',
            'nsc_investment': 'National Savings Certicate (Investment + Accrued Interest)',
            'tax_saving_fixed_deposit': 'Tax Saving Fixed Deposit (5 Years and above)',
            'elss_mutual_fund': 'ELSS Tax Saving Mutual Fund',
            'life_insurance': 'Life Insurance Premium',
            'other_80c_investments': 'Other 80C Investments',
            'new_pension_scheme': 'New Pension Scheme (NPS) (U/S 80CCC)',
            'pension_plan_for_insurance': 'Pension Plan from Insurance Co./Mutual Funds (u/s 80CCC)',
            'principal_repayment_house_loan': 'Principal Repayment on House Building Loan',
            'sukanya_samriddhi_yojana': 'Sukanya Samriddhi Yojana / Deposit Scheme',
            'stamp_duty_registration_fees': 'Stamp Duty & Registration Fees on House Buying',
            'tution_fees_for_children': 'Tuition Fees for Children(max 2 Children)',
            'additional_deducation_for_nps': 'Additional deduction for National Pension Scheme U/S 80CCD(1B)',
            'eightyd_medical_insurance_premium_self': '80D Medical Insurance Premium (for Self, Spouse & Children)',
            'medical_insurance_premium_self_80d_senior': '80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen',
            'preventive_health_checkup_80d': '80D - Preventive Health Checkup',
            'medical_bills_for_self_senior': '80D - Medical Bills for Self, Spouse, Children - Senior Citizen',
            'eightyd_medical_insurance_premium_parent': '80D Medical Insurance Premium (for Parents)',
            'medical_insurance_premium_parent_80d_senior': '80D Medical Insurance Premium (for Parents) - Senior Citizen',
            'preventive_health_checkup_parents_80d': '80D - Preventive Health Checkup for Parents',
            'medical_bills_for_parents_senior': '80D - Medical Bills for Parents - Senior Citizen',
            'eightydd_medical_treatment_dependent_handicapped': '80DD Medical Treatment for Dependent Handicapped',
            'medical_treatment_dependent_handicapped_servere_80dd': '80DD Medical Treatment for Dependent Handicapped - Severe',
            'eightyddb_expenditure_medical_tretment_self_dependent': '80DDB Expenditure on Medical Treatment for Self/ Dependentn',
            'expenditure_medical_tretment_self_dependent_80ddb_senior': '80DDB Expenditure on Medical Treatment for Self/ Dependent - Senior',
            'eightye_interest_paid_education': '80E Interest Paid on Education Loan',
            'eightytta_b_senior_citizens': '80TTA/B Interest from Savings Account',
            'eightyggc_donation_approved_funds': '80G - Donations to Approved Funds (100% Exemption)',
            'donation_approved_funds_80ggc_fifty': '80G - Donation to Approved Funds (50% Exemption)',
            'eightygg_rent_paid_no_hra_recived': '80GG  Rent paid in case of no HRA received',
            'eightyu_physically_disabled_person': '80GG  Rent paid in case of no HRA received',
            'physically_disabled_person_80u_severe': '80U For Physically Disabled Person - Severe',
            'other_employer_income': 'Other Employer Income',
            'other_employer_tds': 'Other Employer TDS',
            'interest_paid_on_home_loan': 'Interest Paid On Home loan',
            'leave_travel_allowance': 'Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)'
        };

        return categories[columnName] || columnName;
    }

    function viewHistory(staffId, staffName, grandTotalRent){
        console.log('Declaration History');
    }

    function getTaxCalculationsComparison(staffId, staffName, staffEmployeeCode, staffTaxRegime){
        var selected_financial_year_id = $('#schedule_year').val();
        var baseUrl = '<?php echo site_url('staff/Payroll_controller/income_tax_calculation'); ?>';
        var fullUrl = baseUrl + '/' + selected_financial_year_id + '/' + staffId + '/viewOnlyFromMID';
        window.open(fullUrl, '_blank');
        
    }
</script>
<style>
    .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
    }

    /* .dataTables_wrapper {
        width: 100%;
        overflow: auto;
    }

    table.dataTable {
        width: 100%;
        margin: 0 auto;
    }

    table.dataTable th, table.dataTable td {
        white-space: nowrap;
    } */

    /* th:nth-child(1){
        position: sticky;
        left: 0;
        z-index: 1;
    }

    th:nth-child(2){
        position: sticky;
        left: 5%;
        z-index: 1;
    }

    th:nth-child(3){
        position: sticky;
        left: 20%;
        z-index: 1;
    }

    th:nth-child(4){
        position: sticky;
        left: 27%;
        z-index: 1;
    } */

    .widthadjust {
    width: 50% !important;
    margin-left: 25%;
    }
</style>
<!-- FixedColumns CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedcolumns/3.3.5/css/fixedColumns.dataTables.min.css"/>
<!-- FixedColumns JavaScript -->
<script type="text/javascript" src="https://cdn.datatables.net/fixedcolumns/3.3.5/js/dataTables.fixedColumns.min.js"></script>

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<!-- DataTables FixedHeader CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.2.0/css/fixedHeader.dataTables.min.css">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<!-- DataTables FixedHeader JS -->
<script src="https://cdn.datatables.net/fixedheader/3.2.0/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.11/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
<style type="text/css">

    input[type="checkbox"]{
      width: 20px; 
      height: 20px;
    }
    /* .DTFC_LeftWrapper table,
    .DTFC_LeftWrapper table tbody {
        display: block;
    }
    .DTFC_LeftWrapper table tbody tr {
        display: table;
        width: 100%;
    } */
    /* #list_staff{
        height: 70vh;
        overflow-y: scroll;
    } */

    /* table.dataTable thead th,
    table.dataTable tbody td {
        padding: 8px 10px;
        box-sizing: border-box;
    } */
    
    .loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 20%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
    }
    @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
    }
    .active{
    background: #6893ca;
    }
    .list-group-item.active{
    background-color: #ebf3f9;
    border-color: #ebf3f9;
    color: #737373;
    }
    .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
    background: #ebf3f9;
    color: #737373;
    }
    .list-group-item{
    border:none;
    }
    .widthadjust{
    width: 48%;
    margin: auto;
    }
    .widthadjust-alert{
    width: 30%; 
    margin: auto;/* Adjust the width as needed */
    }
    .scroller {
    scrollbar-width: thin;
    }
    .dataTables_wrapper .dt-buttons {
    float: right;
    }
    .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
    }
    #DataTables_Table_0_length{
    text-align: left;
    }
    ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
    }
    ::-webkit-scrollbar {
    background-color: #F5F5F5;
    height: 18px;
    }
    ::-webkit-scrollbar-thumb {
    background-color: #c6c6c7;
    border-radius: 5px;
    }
    .dataTables_scrollBody{
    height: auto !important; /* Allow vertical scrolling */
    overflow-x: auto !important; /* Enable horizontal scrolling */
    overflow-y: auto !important;
    display: block !important; /* Ensure proper rendering */
    max-width: 100% !important; /* Ensure flexibility based on content width */
    }
    ::-webkit-scrollbar {
    width: 18px; /* Set the width of the scrollbar */
    scrollbar-width: auto; /* Let the browser determine the width */
    }
    ::-webkit-scrollbar-thumb {
    background-color: #c6c6c7;
    border-radius: 5px;
    }
    ::-webkit-scrollbar-track {
    background-color: #F5F5F5;
    }
    .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
    }

    #list_tab_length{
        border: none;
    }

    .dt-buttons{
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 38px;
        width: 11%;
    }

    .ellipsis{
        display: none;
    }

    .buttons-excel{
        margin-left: 5px;
    }

    .sub_header_note {
        color: #8f7f7f !important;
        font-size: 14px;
    }

    #filtered_list_tab_filter, #filtered_list_tab_length, #list_tab_filter{
        border: none;
    }

    .disabled-row {
        background-color: #f2f2f2;
        color: #999;
        pointer-events: none;
        opacity: 0.5;
    }

    .disabled-element {
        pointer-events: none;
        opacity: 0.5;
    }

    .dataTables_scrollBody{
        border-bottom: none !important;
    }

    .bootbox  > .modal-dialog > .confirmWidth > .modal-footer > button {
		margin: 0px .25rem;
	}

    /* table.dataTable tbody th, table.dataTable tbody td {
        padding: 0px !important;
    } */

    .textMuted{
        text-decoration: line-through;
    }

    .select2-selection__rendered{
        display: none !important;
    }

    #staffSelect > option{
        height: 20px !important;
        display: flex;
        align-items: center;
        padding: 12px 6px;
        margin: 5px 0px;
        border-radius: 5px;
    }

    .absolute-mainmenu{
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* padding: 10px; */
        z-index: 1000;
    }

    .absolute-menu {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* padding: 10px; */
        z-index: 1000;
    }

    .action-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    /* Individual list items */
    .action-list li {
        margin: 4px 0;
    }

    /* Styling for action links */
    .action-list li a {
        display: block;
        padding: 8px 15px; /* Smaller padding for compact look */
        color: #333;
        text-decoration: none;
        border-bottom: 1px solid #e9e9e9;
        border-radius: 3px; /* Slightly rounded corners */
        font-size: 12px; /* Smaller font size */
        transition: all 0.3s ease;
    }

    /* Hover effect for links */
    .action-list li a:hover {
        background-color: #f7f7f7; /* Light background on hover */
        border-color: #ccc;
        color: #000;
    }

    /* Active state for focused links */
    .action-list li a:focus {
        outline: none;
        background-color: #ececec;
        border-color: #bbb;
    }

    /* Special styling for buttons (if used instead of links) */
    .action-list li a {
        display: block;
        width: 100%;
        padding: 8px 15px; /* Same compact padding */
        color: #333;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        border-radius: 3px;
        font-size: 12px;
        cursor: pointer;
        text-align: left;
        transition: all 0.3s ease;
    }

    /* Hover effect for buttons */
    .action-list li button:hover {
        background-color: #f7f7f7;
        border-color: #ccc;
        color: #000;
    }

    .actions_btn{
        border-radius: 4px !important;
    }

    .dtfc-fixed-left {
        position: sticky !important;
        background-color: #fff;
        z-index: 1;
    }

    .DTFC_LeftWrapper {
        z-index: 2;
    }

    .dataTables_scroll {
        overflow: visible !important;
    }
</style>