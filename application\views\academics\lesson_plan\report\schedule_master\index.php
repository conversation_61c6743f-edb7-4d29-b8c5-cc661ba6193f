<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index') ?>">Academics</a></li>
    <li class="active">Manage Schedule</li>
</ul>
<div class="col-md-12 col_new_padding">
    <div class="card cd_border" style="border: none;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-4 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Schedule
                    </h3>
                </div>
                <!-- add button here -->
                <div class="col-md-8 pull-right">
                    <button id="createCalenderBtn" data-toggle="modal" class="new_circleShape_res" style="background-color: #fe970a; float: right;" onclick="checkLPProgramAlreadyExists()" title="Add New Schedule">
                        <span class="fa fa-plus " style="font-size: 19px;"></span>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="weeks-container" style="">
                <div id="scheduleNote" class="col-md-12 pl-0" style="display: none;">
                    <p class="sub_header_note">
                        <strong>Note</strong>: <span style="font-weight:700;font-size:16px;color:blue">To activate</span> the <span style="font-weight:700;font-size:16px;color:blue">schedule(s)</span>, please <span style="font-weight:700;font-size:16px;color:blue">add class(s)</span>. Then Proceed to <span style="font-weight:700;font-size:16px;color:blue">activate</span> the schedule in <span style="font-weight:700;font-size:16px;color:blue">Actions Menu</span>.
                    </p>
                    </div>
                <div class="weeks">
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="add_calender_modal" tabindex="-1" role="dialog" aria-labelledby="calenderLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="calenderLabel" style="font-size: 1.5rem;">Create New Schedule</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 2rem;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body container">
                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label" for="schedule-name">Schedule Name <font style="color: red;"> *
                        </font></label>
                    <div class="col-md-9 col-xs-12">
                        <input type="text" name="schedule-name" id="schedule-name" value="<?php echo date("Y");?>" placeholder="Schedule Name" class="form-control" required maxlength="45">
                        <span id="year_error" class="help-block">Add Schedule Name</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label" for="from-date">From Date <font style="color: red;">*</font></label>
                    <div class="col-md-9 col-xs-12">
                        <input type="date" name="from-date" id="from-date" class="form-control" required placeholder="From Date" autocomplete="off">
                        <span id="from_date_error" class="help-block">Add From Date</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label" for="to-date">To Date <font style="color: red;">*</font></label>
                    <div class="col-md-9 col-xs-12">
                        <input type="date" name="to-date" id="to-date" class="form-control" required placeholder="To Date" autocomplete="off">
                        <span id="to_date_error" class="help-block">Add To Date</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label" for="description">Description</label>
                    <div class="col-md-9 col-xs-12">
                        <textarea name="description" type="text" id="description" class="form-control input-md" placeholder="Description"></textarea>
                        <span id="desc_error" class="help-block">Add Description</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="generateNumOfDays()">Save</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="add_classes-modal" tabindex="-1" role="dialog" aria-labelledby="calenderLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 2% !important; amrgin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="calenderLabel" style="font-size: 1.5rem;">Add Classes To Schedule</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 2rem;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body container" id="add_class_container">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="addClassTolpProgram()">Add Classes</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="remove_classes-modal" tabindex="-1" role="dialog" aria-labelledby="calenderLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 2% !important; amrgin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="calenderLabel" style="font-size: 1.5rem;">Remove Classes From Schedule</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 2rem;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body container" id="remove_class_container">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="removeClassTolpProgram()">Remove Classes</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="weeksModal" tabindex="-1" role="dialog" aria-labelledby="weeksModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="weeksModalLabel">Edit Schedule Week Names</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body edit-weeks-container">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="updateWeeksName()">Save</button>
            </div>
        </div>
    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $("document").ready(e => {
        getSchedules();
        let today = new Date().toISOString().split('T')[0];
        // Set min date on load
        $('#from-date').attr('min', today);
        $('#to-date').attr('min', today);

        $('#from-date').on('click', function () {
            this.showPicker && this.showPicker();
        });

        $('#to-date').on('click', function () {
            this.showPicker && this.showPicker();
        });

        $('#from-date').on('change', function () {
            const fromDate = $(this).val();
            $('#to-date').attr('min', fromDate);
        });

        $('#from-date, #to-date').on('blur', function () {
            const inputVal = $(this).val();
            const minDate = $(this).attr('min');
            if (inputVal && inputVal < minDate) {
                const parts = minDate.split('-');
                const formattedMin = `${parts[2]}-${parts[1]}-${parts[0]}`;
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Date cannot be before ${formattedMin}`,
                });
                $(this).val('');
            }
        });
    });

    async function isActiveProgramExists(){
        try {
            let status;
            await $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/check_LP_program_Already_exists') ?>",
                type: "POST",
                data: {},
                success: (res) => {
                    status=res;
                }
            })
            return status;
        } catch (err) {
            console.log(err.message);
        }
    }

    async function checkLPProgramAlreadyExists(){
        $("#add_calender_modal").modal("show");
    }

    function generateNumOfDays() {
        let schedule = $("#schedule-name").val();
        if(schedule.length > 45){
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Schedule name is too long!",
            });
        }
        const pattern = /^[a-zA-Z0-9 _-]+$/;
        if (!pattern.test(schedule)) {
            return Swal.fire({
                icon: "error",
                title: "Invalid Input",
                text: "Schedule name can only contain letters, numbers, spaces, underscores, or hyphens.",
            });
        }
        let fromDate = $("#from-date").val();
        let toDate = $("#to-date").val();
        let description = $("#description").val();
        if (!schedule || !fromDate || !toDate) {
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please fill all the fields!",
            });
        }

        let difference = (new Date(toDate).getTime() - new Date(fromDate).getTime());
        let days = Math.ceil(difference / (1000 * 3600 * 24)) + 1;

        let totalWeeks = Math.trunc(days / 7);

        let daysRemaining = days - totalWeeks * 7;

        const dataObj = {
            schedule,
            fromDate,
            toDate,
            totalWeeks,
            daysRemaining,
            description
        }

        try {
            $.ajax({
                url: "<?php echo site_url('academics/Lesson_plan/store_lp_weeks') ?>",
                type: "POST",
                data: dataObj,
                success: (data) => {
                    let parsedData = JSON.parse(data);
                    if(parsedData){
                        Swal.fire({
                            icon: "success",
                            title: "Added",
                            text: "Schedule Added Successfully!",
                        }).then(e=>{
                            $("#add_calender_modal").modal("hide");
                            getSchedules();
                        })
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        })
                    }
                },
                error: (err) => {
                    console.log(err);
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    })
                }
            })
        } catch (err) {
            console.log(err.message);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            })
        }
    }

    async function isActiveProgramExists(){
        try {
                let status;
                await $.ajax({
                    url: "<?php echo site_url('academics/Lesson_plan/check_LP_program_Already_exists') ?>",
                    type: "POST",
                    data: {},
                    success: (res) => {
                        status = res;
                    }
                })
                return status;
            } catch (err) {
                console.log(err.message);
            }
        }

    async function checkIsAlreadyExistsAddedClassesInlpPrograms(lpProgramsId){
        // 1. get the list of existing added classes to this current lp programs
        // 2. then, find the 'Active lp programs' which has these classes in them
         try {
            let status;
            await $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/check_is_already_exists_added_classes_inlp_programs') ?>",
                type: "POST",
                data: {"lpProgramId":lpProgramsId},
                success: (res) => {
                    status = res;
                }
            })
            return status;
        } catch (err) {
            // console.log(err.message);
            throw err;
        }
    }

    async function changeProgramStatus(lpProgramsId, name, status) {
        try {
            if(status==0){
                // check for any classes which is active already
                const isAlreadyExistsAddedClasses= await checkIsAlreadyExistsAddedClassesInlpPrograms(lpProgramsId);
                if(isAlreadyExistsAddedClasses>=1){
                    $("#add_calender_modal").modal("hide");
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text:"The class / grade is already active in other Schedule, Please remove that class / grade to active this Schedule!"
                    });
                }else if(isAlreadyExistsAddedClasses==-1){
                    // check for empty classes for the current schedule
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text:"Please add class(s) to activate this schedule!"
                    });
                }
            }
            // display confirm then -> change status
            const acticationStatus = `${status == 1 && "Deactivate" || "Activate"}`;
            Swal.fire({
                title: "Are you sure?",
                text: `Do you want to ${acticationStatus} ${name} !`,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: `Yes, ${acticationStatus} it !`,
                reverseButtons: true,
            }).then(result => {
                if (result.isConfirmed) {
                    $(`#active_btn-${lpProgramsId}`).text(`${acticationStatus}`);
                    const newStatus = status == 1 && "0" || 1;

                    // const isUpdated = await updateLpPragramStatus(lpProgramsId, newStatus);
                    $.ajax({
                        url: "<?php echo site_url("academics/Lesson_plan/update_lp_programs_status") ?>",
                        type: "POST",
                        data: {
                            lpProgramsId, newStatus
                        },
                        success: function (response) {
                            let parsedData = JSON.parse(response);
                            if (!parsedData) {
                                Swal.fire({
                                    icon: "error",
                                    title: "Oops...",
                                    text: "Something went wrong!",
                                });
                                return;
                            }
                            Swal.fire({
                                icon: "success",
                                title: "Updated",
                                text: "You status has been updated!",
                            }).then(()=>{
                                getSchedules();
                            });
                        },
                        error: function (err) {
                            console.log(err);
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Something went wrong!",
                            });
                        }
                    });
                }
            });
        }
        catch (err) {
            console.log(err.message);
        }
    }

    // get added session weeks
    function getSchedules() {
        try {
            $('#scheduleNote').hide();
            $(".weeks").html("<div class='no-data-display'>Loading...</div>");

            $.ajax({
                url: "<?php echo site_url('academics/Lesson_plan/get_lp_programs') ?>",
                type: "POST",
                data: {},
                success: (data) => {
                    const response = JSON.parse(data);

                    if (!response?.length) {
                        return $(".weeks").html("<div class='no-data-display'>No Schedule(s) Found For The Current Academic Year.</div>");
                    }
                    let html = `<table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th style="width: 8%;">Actions</th>
                                            <th>#</th>
                                            <th>Schedule Name</th>
                                            <th>From Date</th>
                                            <th>To Date</th>
                                            <th>Schedule Status</th>
                                            <th>Description</th>
                                            <th>Class(s)</th>
                                            <th>Created On</th>
                                            <th>Created By</th>
                                        </tr>
                                    </thead>
                                    <tbody> `;
                        response?.forEach((w, i) => {
                            const safeData = encodeURIComponent(JSON.stringify(w));
                            
                            html += `<tr>
                                        <td style="width: 8%;">
                                            <button class="btn btn-secondary dropdown-toggle actions_btn" type="button" id="actionButton_${i++}" data-schedule='${safeData}' data-classes="${w.appliedClasses}" onclick="showMainActionsMenu(event, this)">
                                                Actions
                                            </button>
                                        </td>
                                        <td>${i++}</td>
                                        <td>${w.program_name}</td>
                                        <td>${w.from_date}</td>
                                        <td>${w.to_date}</td>
                                        <td>${w.status == 1 && "Active" || "Deactive"}</td>
                                        <td>${w.description || "-"}</td>
                                        <td>${w.appliedClasses || "-"}</td>
                                        <td>${w.created_on || "-"}</td>
                                        <td>${w.created_by_name || "-"}</td>
                                    </tr> `;
                        });
                    html += `</tbody>
                            </table> `;
                    $('#scheduleNote').show();
                    $(".weeks").html(html);
                },
                error: (err) => {
                    console.log(err);
                    $(".weeks").html("<div class='no-data-display'>Something Went Wrong!. Please try again later.</div>");
                }
            });
        }catch (err) {
            console.log(err);
            $(".weeks").html("<div class='no-data-display'>Something Went Wrong!. Please try again later.</div>");
        }
    }

    document.addEventListener('click', function(event) {
        const isButtonClick = event.target.classList.contains('dropdown-toggle');
        if (!isButtonClick) {
            closeMenu();
        }
    });

    function closeMenu() {
        const openMenu = document.querySelector('.absolute-menu');
        const openMainMenu = document.querySelector('.absolute-mainmenu');
        if (openMenu) {
            openMenu.remove();
        } else if(openMainMenu){
            openMainMenu.remove();
        }
    }

    function showMainActionsMenu(event, button){
        closeMenu();
        const dataStr = button.getAttribute('data-schedule');
        const classes = button.getAttribute('data-classes');
        const data = JSON.parse(decodeURIComponent(dataStr));
        const menu = document.createElement('div');
        menu.classList.add('absolute-mainmenu');
        let actions = '<ul class="action-list">';
        if(classes != "null"){
            actions += `<li><a id="active_btn-${data.lp_programs_id}" onClick="changeProgramStatus('${data.lp_programs_id}','${data.program_name}','${data.status}')" style="color: ${data.status == 1 && "red" || "green"}">
                            <i class="fa ${data.status == 1 && "fa-ban" || "fa-check-circle"}"></i> ${data.status == 1 && "De-Activate" || "Activate"}
                        </a></li>`;
        }
        actions += `<li><a class="" id="add_classes-${data.lp_programs_id}" data-lp-schedule-id="${data.lp_programs_id}" data-toggle="modal" data-target="#" onclick="addClassForPrograms(${data.lp_programs_id})">
                        <i class="fa fa-plus"></i> Add Classes
                    </a></li>`;
        actions += `<li><a class="" id="remove_classes-${data.lp_programs_id}" data-lp-schedule-id="${data.lp_programs_id}" data-toggle="modal" data-target="#" onclick="removeClassForPrograms(${data.lp_programs_id})">
                        <i class="fa fa-times"></i> Remove Classes
                    </a></li>`;
        actions += `<li><a class="" onclick="editWeekNames(${data.lp_programs_id})">
                        <i class="fa fa-edit"></i> Edit Weeks
                    </a></li>`;
        actions += '</ul>';

        menu.innerHTML = actions;
        document.body.appendChild(menu);

        let rect;
        if (event) {
            rect = event.target.getBoundingClientRect();
        } else {
            const fallbackElement = document.getElementById(`actionButton_${staffId}`);
            if (!fallbackElement) {
                console.error('Fallback element not found');
                return;
            }
            rect = fallbackElement.getBoundingClientRect();
        }
        const menuHeight = menu.offsetHeight;

        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        menu.style.position = 'absolute';
        menu.style.left = `${rect.left + window.scrollX}px`;

        if (spaceBelow < menuHeight && spaceAbove >= menuHeight) {
            menu.style.top = `${rect.top + window.scrollY - menuHeight}px`;
        } else {
            menu.style.top = `${rect.bottom + window.scrollY}px`;
        }
    }

    async function getLPWeeksUsingProgramId(lpProgramId){
        try {
            let res;
            await $.ajax({
                url: "<?php echo site_url('academics/Lesson_plan/get_lp_weeks_using_programs_id') ?>",
                type: "POST",
                data: {"lpProgramId":lpProgramId},
                success: (data) => {
                    res = JSON.parse(data);
                }
            });
            return res;
        } catch (err) {
            throw err;
        }
    }

    async function editWeekNames(lpProgramId){
        try {
            // bring all the weeks under lp schedule
            // iterate each html element -> if oldName!=newName then add into the API Object along with lp schedule id
            // construct html of weeks-> which will include -> data-weekId, data-oldName, data-newName
            // Make an saveWeeksNames API to save week names : it'll take an object and returns Boolean value True / False
            // Notify user using SWAL notification

            const weeks=await getLPWeeksUsingProgramId(lpProgramId);

            let html=`<table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Duration</th>
                                    <th>Week Name</th>
                                </tr>
                            </thead>
                            <tbody>`;
            weeks.forEach((w,i)=>{
            html+=`             <tr>
                                    <td>${++i}</td>
                                    <td>${w.duration}</td>
                                    <td>
                                        <input class="form-control edit-lp_weeks" type="text" name="week-${w.week_id}" data data-week-id="${w.week_id}" data-old-name="${w.week_name}" value="${w.week_name}" />
                                    </td>
                                </tr>
                        `;
            });

            html+=`</tbody>
                        </table>`;

            $(".edit-weeks-container").html(html);
            $("#weeksModal").modal("show");

            window.localStorage.setItem("editProgramId",lpProgramId);
        } catch (err) {
            console.log(err.message);
        }
    }

    function updateWeeksName(){
        const allEditLPWeeks=document.querySelectorAll(".edit-lp_weeks");
        const lpProgramId=window.localStorage.getItem("editProgramId");
        const updateWeeks={
            // lpProgramId:lpProgramId,
            editWeeks:[]
        }
        
        allEditLPWeeks.forEach(w=>{
            const {weekId,oldName}=w.dataset;
            if(w.value!==oldName){
                updateWeeks.editWeeks.push({"weekId":weekId,"newName":w.value});
            }
        });

        if(!updateWeeks?.editWeeks?.length){
            // handle empty check here
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "No changes found to save!",
            });
        }
        
        try {
            $.ajax({
                url: "<?php echo site_url('academics/Lesson_plan/update_lp_weeks') ?>",
                type: "POST",
                data: { updateWeeks },
                success: (res) => {
                    // console.log(res);
                    let parsedData = JSON.parse(res);
                    if(parsedData){
                        Swal.fire({
                            icon: "success",
                            title: "Saved",
                            text: "Changes saved successfully!!",
                        });
                    }else{
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }
                    $("#weeksModal").modal("hide");
                },
                error: (err) => {
                    console.log(err);
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            });
        } catch (err) {
            console.log(err.message);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    }

    function removeClassForPrograms(lpProgramId){
        $("#remove_class_container").html("Loading...");
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_added_classes_of_lp_programs') ?>",
            type: "POST",
            data: {lpProgramId},
            success: function (res) {
                const classes=JSON.parse(res);
                if(!classes?.length) {
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "No classes to show!",
                    });
                }

                $("#remove_classes-modal").modal("show")

                let lpClass='';
                classes.forEach((c,i)=>{
                    lpClass+=`<div class="addLPClass" data-lp-programs-id="${lpProgramId}" data-class-id="${c.class_id}" id="class-id-${c.class_id}-${lpProgramId}" onclick="selectDeselectClass(${c.class_id},'remove',${lpProgramId})">${c.class_name}</div>`;
                });
                $("#remove_class_container").html(lpClass);
            }
        });
    }

    function addClassForPrograms(lpProgramId){
        $("#add_class_container").html("Loading...");
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_classes_for_lp_programs') ?>",
            type: "POST",
            data: {lpProgramId},
            success:function(res){
                const classes=JSON.parse(res);
                if(!classes?.length) {
                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "No classes to show!",
                    });
                }
                $("#add_classes-modal").modal("show");

                let lpClass='';
                classes.forEach((c,i)=>{
                    lpClass+=`<div class="addLPClass" data-lp-programs-id="${lpProgramId}" data-class-id="${c.class_id}" id="class-id-${c.class_id}-${lpProgramId}" onclick="selectDeselectClass(${c.class_id},'add',${lpProgramId})">${c.class_name}</div>`;
                });

                $("#add_class_container").html(lpClass);
            }
        });
    }

    function selectDeselectClass(classId,type,lpProgramId){
        if(type=="add"){
            $(`#class-id-${classId}-${lpProgramId}`).toggleClass("selectedClass");
        }else{
            $(`#class-id-${classId}-${lpProgramId}`).toggleClass("removeClass");
        }
    }

    function removeClassTolpProgram(){
        const allSelectedClasses=document.querySelectorAll(".removeClass");

        if(!allSelectedClasses?.length){
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Select Class(s) Before Removing!",
            });
        }

        const dataObj={
            classIds:[]
        }

        allSelectedClasses.forEach((c,i)=>{
            if(i==0){
                dataObj.lpProgramsId=c.dataset.lpProgramsId;
            }
            dataObj.classIds.push(c.dataset.classId);
        });

        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/remove_classes_of_lp_programs') ?>",
            type: "POST",
            data: dataObj,
            success: function (res) {
                let parsedData = JSON.parse(res);
                if (parsedData) {
                    Swal.fire({
                        icon: "success",
                        title: "Class(s) removed successfully",
                        text: "Class(s) removed from the Schedule successfully!",
                    }).then(()=>{
                        getSchedules();
                        $("#remove_classes-modal").modal("hide");
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            },
            error: function (err) {
                console.log(err);
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Something went wrong!",
                });
            }
        });
    }

    function addClassTolpProgram(){
        const allSelectedClasses=document.querySelectorAll(".selectedClass");

        if(!allSelectedClasses?.length){
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Select Class(s) Before Adding!",
            });
        }

        const dataObj={
            classIds:[]
        }

        allSelectedClasses.forEach((c,i)=>{
            if(i==0){
                dataObj.lpProgramsId=c.dataset.lpProgramsId;
            }
            dataObj.classIds.push(c.dataset.classId);
        });

        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/add_classes_for_lp_programs') ?>",
            type: "POST",
            data: dataObj,
            success: function (res) {
                let parsedData = JSON.parse(res);
                if(parsedData){
                    Swal.fire({
                        icon: "success",
                        title: "Class(s) added successfully",
                        text: "Class(s) addeed to the Schedule successfully!",
                    }).then(()=>{
                        getSchedules();
                        $("#add_classes-modal").modal("hide");
                    });
                }else{
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            },
            error: function (err) {
                console.log(err);
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Something went wrong!",
                });
            }
        });
    }

</script>

<style>
    .new_circleShape_res {
        padding: 8px;
        float: right;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .modal-dialog {
        width: 50%;
        margin: auto;
    }

    .addLPClass{
        background: #ebedef;
        padding: 9px 15px;
        margin-bottom: 7px;
    }

    .selectedClass{
        background: #71ca71c7;
    }

    .removeClass{
        background: #dc9d9d;
    }

    .addLPClass {
        cursor: pointer;
    }

    .absolute-mainmenu{
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* padding: 10px; */
        z-index: 1000;
    }

    .absolute-menu {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* padding: 10px; */
        z-index: 1000;
    }

    .action-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    /* Individual list items */
    .action-list li {
        margin: 4px 0;
    }

    /* Styling for action links */
    .action-list li a {
        display: block;
        padding: 8px 15px; /* Smaller padding for compact look */
        color: #333;
        text-decoration: none;
        border-bottom: 1px solid #e9e9e9;
        border-radius: 3px; /* Slightly rounded corners */
        font-size: 12px; /* Smaller font size */
        transition: all 0.3s ease;
    }

    /* Hover effect for links */
    .action-list li a:hover {
        background-color: #f7f7f7; /* Light background on hover */
        border-color: #ccc;
        color: #000;
    }

    /* Active state for focused links */
    .action-list li a:focus {
        outline: none;
        background-color: #ececec;
        border-color: #bbb;
    }

    /* Special styling for buttons (if used instead of links) */
    .action-list li a {
        display: block;
        width: 100%;
        padding: 8px 15px; /* Same compact padding */
        color: #333;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        border-radius: 3px;
        font-size: 12px;
        cursor: pointer;
        text-align: left;
        transition: all 0.3s ease;
    }

    /* Hover effect for buttons */
    .action-list li button:hover {
        background-color: #f7f7f7;
        border-color: #ccc;
        color: #000;
    }

    .actions_btn{
        border-radius: 4px !important;
    }

    .sub_header_note {
        color: #8f7f7f !important;
        font-size: 14px;
    }
</style>