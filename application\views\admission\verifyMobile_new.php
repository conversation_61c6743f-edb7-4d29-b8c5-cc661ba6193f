
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <link rel="icon" href="<?php echo base_url() . $this->settings->getSetting('favicon'); ?>" type="image/x-icon" />

    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url()?>assets/css/login/fonts/iconic/css/material-design-iconic-font.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url()?>assets/css/login/css/util.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url()?>assets/css/login/css/main.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url();?>assets/css/theme-default.css" />
    <!--===============================================================================================-->

</head>

    <div>
        <div class="row">
            <div class="col-md-9" style="padding: 0px !important;">
                <?php 
            $var = $this->settings->getSetting('login_background');

            if (strpos($var, "assets") !== false) {
                echo "<div class='container-login100' style='background-image: url(" . base_url() . $this->settings->getSetting('login_background') . "); background-position: top;'>";
            } else {
                $get_url = $this->filemanager->getFilePath($var);
                echo "<div class='container-login100' style='background-image: url(" . $get_url . "); background-position: top; background-size: 100% 100%'>";
            }
            ?>
            </div>
        </div>

        <div class="col-md-4 d-flex align-items-center justify-content-center" style="height:100%">
            <form enctype="multipart/form-data" id="Verify-Mobile-Number" class="form-horizontal" data-parsley-validate
                method="post">
                <?php 
                    $email = $this->settings->getSetting('admission_email_based_otp');
                    $admission_form_word = $this->settings->getSetting('admission_form_word'); 
                ?>
                <div style="display: flex; flex-direction: column; align-items: flex-start; text-align: left; padding: 20px;"
                    id="logo_content">
                    <div class="logo" style="margin-bottom: 40px; display: flex; align-items: center;">
                        <img style="width: 34px; height: 40px; margin-right: 10px;"
                            src="<?php echo base_url() . $this->settings->getSetting('school_logo'); ?>">
                        <span class="school_name"
                            style="font-size: 20px;
                                    font-weight: 700;
                                    font-style: normal;
                                    letter-spacing: 0.5px;
                                    line-height: 1.2;
                                    color: #333;
                                    white-space: nowrap;"><?php echo $this->settings->getSetting('school_name'); ?></span>
                    </div>

                    <h3 style="font-size: 30px;">Welcome back!</h3>

                    <span
                        style="color:#333 !important; font-size: 14px; max-width: 100%; font-style: normal; text-align: left;">
                        <?php 
                        if ($this->settings->getSetting('school_short_name') == 'nhis') { ?>
                        <?php echo $this->settings->getSetting('school_name'); ?>
                        Welcome to the
                        <?php if($admission_form_word) echo $admission_form_word.' '; else echo 'admissions '; ?>
                        portal.
                        <br> Enter a mobile number <?php if($email) echo '/ Email'; ?> to continue
                        <p>(Some Schools/colleges require a registered mobile number)</p>
                        <?php } else if ($this->settings->getSetting('school_short_name') == 'aimit') { ?>
                        Welcome to <?php echo $this->settings->getSetting('school_name'); ?>.
                        <br> Enter an Indian mobile number <?php if($email) echo '/ Email'; ?> to continue
                        <?php }else if ($this->settings->getSetting('admission_login_text')) { ?>
                        Welcome to <?php echo $this->settings->getSetting('school_name'); ?>.
                        <br> Enter an Indian mobile number <?php if($email) echo '/ Email'; ?> to continue
                        <?php } else { ?>
                        Welcome to the
                        <?php if($admission_form_word) echo $admission_form_word.' '; else echo 'admissions '; ?> portal
                        of <?php echo $this->settings->getSetting('school_name'); ?>.
                        <br><br> Enter an Indian mobile number <?php if($email) echo '/ Email'; ?> to continue
                        <?php } ?>
                    </span>
                </div>

                <input type="hidden" id="input_val_type" value="mobile">
                <div style="max-width: 100%; margin:0 20px; font-family: sans-serif;">
                    <label for="mobile"
                        style="font-weight: 500; font-size: 14px; font-style: normal; display: block; margin-bottom: 8px; text-align: left;">Mobile
                        No.</label>
                    <div
                        style="display: flex; align-items: center; border: 1px solid #ddd; border-radius: 8px; padding: 8px 10px; margin-bottom: 20px;">
                        <span style="margin-right: 8px; color: #333;font-size:14px" class="country-code">+91</span>
                        <input type="text" required class="form-control"
                            data-parsley-error-message="Enter valid phone number" autocomplete="off"
                            placeholder="Enter Mobile No." id="mobileNumber" name="mobileNumber"
                            style="border: none; outline: none; flex: 1; font-size:14px; " />
                    </div>
                    <div class="verifyOTP"
                        style="display: none; align-items: center; border: 1px solid #ddd; border-radius: 8px; padding: 8px 10px; margin-bottom: 20px;">
                        <input class="form-control" id="otpCode" name="otpCode" placeholder="Enter OTP Number"
                            type="text" minlength="6" maxlength="6" autocomplete="off"
                            style="border: none; outline: none; flex: 1; font-size: 12px;">
                    </div>

                    <div class="helper-text" id="resend_section" style="display: none; text-align: left;">
                        <span style="font-size: 13px;font-style: normal;font-weight: 500;">Din't receive the OTP?</span>
                        <a type="button" id="show_hide_resend" onclick="resend_otp()" class="resend-btn"
                            style="display:none;font-weight:600; color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?> !important; background-color: transparent !important;">Resend
                            OTP</a>
                        <span id="show_hide_timer" style="font-size: 14px;">Resend Code in <span
                                id="timer"></span></span>
                    </div>

                    <button id="sendOTP" onclick="send_otp()" type="button" class="btn verify-btn"
                        style="margin-top: 30px;">
                        Send OTP
                    </button>

                    <button type="button" id="verify" class="btn verify-btn verify"
                        style="display: none; margin-top: 15px;background-color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;">
                        Login
                    </button>

                    <?php  if($email == 1) { ?>
                    <div class="divider" style="margin: 30px 0; text-align: left;">Or, Login with</div>

                    <div class="email-login" onclick="change_login_mode('email')"
                        style="margin-bottom: 10px; display: flex; align-items: center;">
                        <span
                            style="width: 24px; height: 20px; margin-right: 8px;"><?php $this->load->view('svg_icons/email_icon.svg') ?></span>
                        <button style="font-size: 16px;">Email Address</button>
                    </div>

                    <div class="mobile-login" onclick="change_login_mode('mobile')"
                        style="display:none; margin-bottom: 10px;">
                        <img src="https://img.icons8.com/material-outlined/24/000000/phone.png" alt="Mobile Icon">
                        <button style="font-size: 16px;">Mobile Number</button>
                    </div>
                    <?php } ?>
                </div>
            </form>
        </div>
    </div>

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap.min.js"></script>
    <!-- END PLUGINS -->

    <!-- START THIS PAGE PLUGINS-->
    <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/icheck/icheck.min.js'></script>
    <script type="text/javascript"
        src="<?php echo base_url();?>assets/js/plugins/mcustomscrollbar/jquery.mCustomScrollbar.min.js"></script>

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootbox.min.js"></script>
    <!-- END THIS PAGE PLUGINS-->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins.js"></script>
    <script type="text/javascript" src="<?php echo base_url();?>assets/js/actions.js"></script>
    <!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/demo_dashboard.js"></script> -->
    <!-- END TEMPLATE -->

    <script type="text/javascript" src="<?php echo base_url();?>assets/js/parsley.js"></script>
    <!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/chung-timepicker.js'></script>  -->
    <style type="text/css">
    #parsley-id-4 {
        display: none;
    }
    </style>
    <script type="text/javascript">
    var timerOn = true;
    var email = '<?php echo $email ?>';

    function change_login_mode(login_type) {
        $('#input_val_type').val(login_type);
        $('#mobileNumber').removeAttr('readonly')
        $('.verifyOTP').hide();
        $('#resend_section').hide();
        $('#verify').hide();
        $('#sendOTP').show();
        $('#mobileNumber').val('')
        if (login_type == 'email') {
            $('.country-code').hide();
            $('.email-login').hide();
            $('.mobile-login').show();
            $('#mobileNumber').attr('placeholder', 'Enter Email Id');
            $('label[for="mobile"]').text('Email Id');
        } else {
            $('.country-code').show();
            $('.email-login').show();
            $('.mobile-login').hide();
            $('#mobileNumber').attr('placeholder', 'Enter Mobile No.');
            $('label[for="mobile"]').text('Mobile No.');
        }
        $('#mobileNumber').blur(); // Ensure the input field loses focus
    }

    function send_otp() {
        $('#mobileNumber').parsley();
        let mobileNumber = $('#mobileNumber').val();
        let mobilePattern = /^[6-9]\d{9}$/; // Indian mobile numbers (starting 6-9 and 10 digits)
        let emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/; // Email format
        let input_val_type = $('#input_val_type').val();
        if (mobilePattern.test(mobileNumber) || emailPattern.test(mobileNumber)) {

            document.querySelector(".verifyOTP").style.display = "block"; // Show OTP input
            document.querySelector("#sendOTP").style.display = "none"; // Hide send button
            document.querySelector("#verify").style.display = "inline-block"; // Show Verify button
            document.querySelector("#resend_section").style.display = "flex"; // Show resend section
        } else if (input_val_type == 'email') {
            Swal.fire({
                icon: "error",
                title: "Enter Valid Email",
                showConfirmButton: false,
                timer: 1500
            });
            return false;
        } else if (input_val_type == 'mobile') {
            Swal.fire({
                icon: "error",
                title: "Enter 10 Digit Mobile Number",
                showConfirmButton: false,
                timer: 1500
            });
            return false;
        }
        ajaxcallotp_number(mobileNumber);
    }

    function resend_otp() {
        // var mobileNumber = $("#mobileNumber").val();
        var mobileNumber = $("#mobileNumber").val().replace(/^0/, '');
        $("#otpCode").removeAttr('required');
        var $form = $('#Verify-Mobile-Number');
        if ($form.parsley().validate()) {
            ajaxcallotp_number(mobileNumber);
        } else {
            Swal.fire({
                icon: "error",
                title: "Enter 10 Digit Mobile Number",
                showConfirmButton: false,
                timer: 1500
            });
        }
    }

    function ajaxcallotp_number(mobileNumber) {
        $.ajax({
            url: '<?php echo site_url('Admission_user/sendOTP/'); ?>',
            type: 'post',
            data: {
                'mobileNumber': mobileNumber
            },
            success: function(data) {
                var retData = $.parseJSON(data);
                console.log(retData);

                if (retData.status == 'ok') {
                    $("#sendOTP").hide(); // Hide "Send OTP"
                    $('#otpCode').val('')
                    $(".verifyOTP").show(); // Show OTP input
                    $("#verify").show(); // Show "Verify & Proceed"
                    // $("#show_hide_resend").show(); // Show "Resend OTP"

                    $('#mobileNumber').attr('readonly', 'readonly'); // Disable input after sending OTP
                    $("#otpCode").prop('required', true);
                    $('#show_hide_timer').show();
                    $('#show_hide_resend').hide();
                    resend_timer(59);
                } else {
                    Swal.fire({
                        icon: "error",
                        title: retData.msg,
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            }
        });
    }

    $(".verify").click(function(e) {
        var otp = $('#otpCode').val();
        if (otp == '') {
            Swal.fire({
                icon: "error",
                title: "Enter The OTP",
                showConfirmButton: true,
            });
        }
        var $form = $('#Verify-Mobile-Number');
        if ($form.parsley().validate()) {
            var form = $('#Verify-Mobile-Number')[0];
            var formData = new FormData(form);
            // console.log(formData);
            $.ajax({
                url: '<?php echo site_url('admissions/home');?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    if (data == 0) {
                        Swal.fire({
                            icon: "error",
                            title: "Invalid OTP Entered",
                            showConfirmButton: false,
                        });
                        $('#errorPopUp').show();
                        $('#error_otp').html('Incorrect OTP. Please try again');
                    } else {
                        window.location.href = '<?php echo site_url('admissions/home');?>';
                        return true;
                    }
                }
            });
        }
    });

    function resend_timer(remaining) {
        // $('#resend-success').hide();
        // $('#resend-timer').show();
        var m = Math.floor(remaining / 60);
        var s = remaining % 60;
        m = m < 10 ? '0' + m : m;
        s = s < 10 ? '0' + s : s;
        document.getElementById('timer').innerHTML = m + ':' + s;
        remaining -= 1;

        if (remaining >= 0 && timerOn) {
            setTimeout(function() {
                resend_timer(remaining);
            }, 1000);
            return;
        }
        if (!timerOn) {
            // Do validate stuff here
            return;
        }
        // $('.initial').addClass('active');

        $('#show_hide_resend').show();
        $('#show_hide_timer').hide();

    }

    function detectBrowser() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        let browserName = "Unknown";

        if (/Edg/i.test(userAgent)) {
            browserName = "Microsoft Edge";
        } else if (/CriOS/i.test(userAgent)) {
            browserName = "Google Chrome (iOS)";
        } else if (/Chrome/i.test(userAgent)) {
            browserName = "Google Chrome";
        } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
            browserName = "Safari";
        } else if (/Firefox/i.test(userAgent)) {
            browserName = "Mozilla Firefox";
        } else if (/MSIE|Trident/i.test(userAgent)) {
            browserName = "Internet Explorer";
        }

        console.log("Detected browser:", browserName);

        if (!['Google Chrome', 'Google Chrome (iOS)', 'Microsoft Edge'].includes(browserName)) {
            $('#mobileNumber').attr('readonly', 'readonly');
            Swal.fire({
                icon: "error",
                title: "Use only Google Chrome or Microsoft Edge to fill the application.",
                showConfirmButton: true,
                timer: 5000
            });
        }
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <?php
if ($this->mobile_detect->isTablet()) { ?>
    <style>
    .container-login100 {
        background-size: cover;
        background-position: center;
        height: 5vh;
        min-height: 42vh;
        /* background-size: 100% 50%; */
    }

    .col-md-4.d-flex {
        background: #f9fafc;
        padding: 50px 0 20px 0;
    }

    .row.align-items-center {
        margin: 0;
    }

    #logo_content {
        margin-top: 0px !important;
    }

    /* Form Container */
    form #Verify-Mobile-Number {
        width: 100%;
        background: #ffffff;
        padding: 30px 20px;
        border-radius: 20px;
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.08);
        max-width: 400px;
    }

    .logo img {
        width: 120px;
        height: auto;
    }


    /* Input Groups */
    .input-group {
        position: relative;
        margin-bottom: 20px;
    }

    .input-group .country-code {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        color: #555;
    }

    .input-group input {
        padding: 10px 10px 10px 50px;
        width: 100%;
        border: 1px solid #ccc;
        border-radius: 12px;
        font-size: 16px;
        background: #fafafa;
        outline: none;
    }

    /* OTP Field */
    .verifyOTP input {
        padding: 10px 15px;
    }

    /* Buttons */
    .btn.verify-btn {
        width: 100%;
        background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        ;
        color: white;
        padding: 14px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        margin-top: 10px;
        transition: 0.3s;
    }

    .btn.verify-btn:hover {
        background: #5a52d4;
    }

    /* Resend OTP Section */
    .helper-text {
        display: flex;
        font-size: 14px;
        margin: 15px 25px;
        color: #555;
        text-align: left;
        justify-content: space-between;
    }

    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #fff;
    opacity: 1;
    }

    /* Timer */
    </style>
    <?php }else if($this->mobile_detect->isMobile()){ ?>
    <style>
    /* Background Container */
    .container-login100 {
        background-size: cover;
        background-position: center;
        height: 5vh;
        min-height: 30vh;
        /* background-size: 100% 50%; */
    }

    /* Main Layout */
    .row.align-items-center {
        margin: 0;
    }

    #logo_content {
        margin-top: 0px !important;
    }

    /* Form Container */
    form #Verify-Mobile-Number {
        width: 100%;
        background: #ffffff;
        padding: 30px 20px;
        border-radius: 20px;
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.08);
        max-width: 400px;
    }

    /* Center Content */
    .col-md-4.d-flex {
        background: #f9fafc;
        /* min-height: 100vh; */
        padding: 35px 0 20px 0;
    }

    /* Logo */
    .logo img {
        width: 120px;
        height: auto;
    }

    /* Input Groups */
    .input-group {
        position: relative;
        margin-bottom: 20px;
    }

    .input-group .country-code {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        color: #555;
    }

    .input-group input {
        padding: 10px 10px 10px 50px;
        width: 100%;
        border: 1px solid #ccc;
        border-radius: 12px;
        font-size: 16px;
        background: #fafafa;
        outline: none;
    }

    /* OTP Field */
    .verifyOTP input {
        padding: 10px 15px;
    }

    /* Buttons */
    .btn.verify-btn {
        width: 100%;
        background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        color: white;
        padding: 14px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        margin-top: 10px;
        transition: 0.3s;
    }

    .btn.verify-btn:hover {
        background: #5a52d4;
    }

    /* Resend OTP Section */
    .helper-text {
        display: flex;
        font-size: 14px;
        margin: 15px 25px;
        color: #555;
        text-align: left;
        justify-content: space-between;
    }

    /* Responsive */
    @media screen and (max-width: 768px) {

        .col-md-4 {
            width: 100%;
        }

        form#Verify-Mobile-Number {
            margin: auto;
            box-shadow: none;
            background: transparent;
        }
    }

    .container-login100 {}
    </style>
    <?php  }else{ ?>
    <style>
    .glass {
        width: 400px;
        background: inherit;
        position: relative;
        z-index: 1;
        overflow: hidden;
        margin: 0 auto;
        padding: 2rem;
        box-sizing: border-box;
        box-shadow: 0 .5em 1em rgba(0, 0, 0, .3);
        /* border-radius : 16px; */
    }

    .glass::before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: inherit;
        box-shadow: inset 0 0 3000px rgba(255, 255, 255, .5);

        filter: blur(5px);
    }

    .input100 {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        color: #000000;
        line-height: 1.2;
        display: block;
        width: 100%;
        height: 56px;
        background-color: #FFFFFF;
        padding: 0 5px 0 38px;
        border-radius: 8px;
        border: none;
    }

    

    .wrap-input100 {
        width: 100%;
        position: relative;
        border: none;
        margin-bottom: 20px;
    }

    .login100-form-btn::before {
        content: "";
        display: block;
        position: absolute;
        z-index: -1;
        width: 100%;
        height: 100%;
        border-radius: 8px;
        background-color: #43a047;
        top: 0;
        left: 0;
        opacity: 1;
        -webkit-transition: all 0.4s;
        -o-transition: all 0.4s;
        -moz-transition: all 0.4s;
        transition: all 0.4s;
    }

    .login100-form-btn {
        font-family: 'Roboto', sans-serif;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 1.2;
        display: -webkit-box;
        display: -webkit-flex;
        display: -moz-box;
        display: -ms-flexbox;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 8px;
        width: 100%;
        height: 48px;
        border-radius: 8px;
        background: #43a047;
        position: relative;
        z-index: 1;
        -webkit-transition: all 0.4s;
        -o-transition: all 0.4s;
        -moz-transition: all 0.4s;
        transition: all 0.4s;
    }

    .txt1 {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        color: #e5e5e5;
        line-height: 1.5;
    }

    .contact100-form-checkbox {
        padding-left: 5px;
        padding-top: 5px;
        padding-bottom: 24px;
    }

    #Verify-Mobile-Number .input-group {
        margin-top: 3%;
        margin-bottom: 3%;
        height: 42px !important;
    }

    #Verify-Mobile-Number .btn {
        /* font-size: 16px;
  width: 100% !important;
  max-width: 350px !important;  */
        display: flex;
        /* padding: 17px 24px; */
        justify-content: center;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        border-radius: 8px;
    }

    .col-md-3 {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
    }

    .row {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
    }

    .logo img,
    span img {
        width: 161px;
        height: 117px;
        flex-shrink: 0;
        display: inline-block;
        /* or block */
    }

    .logo {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .input-group {
        /* border: 1px solid #04327F; */
        border-radius: 0px;
        overflow: hidden;
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 350px;
        margin: 20px auto;
    }

    .country-code {
        /* background-color: #04327F; */
        color: #212121;
        /* padding: 10px 15px; */
        font-size: 16px;
        font-weight: 400;
        font-style: normal;
        /* font-weight: bold; */
    }

    .input-group input {
        border: none;
        padding: 10px;
        flex: 1;
        font-size: 16px;
        outline: none;
    }

    /* Main button styling */

    /* Resend OTP button hover effect */
    .otp-container {
        text-align: center;
        max-width: 320px;
        margin: auto;
    }

    .input-group {
        display: flex;
        border: 1px solid #ccc;
        border-radius: 5px;
        overflow: hidden;
    }

    .input-group-text {
        background-color: #002f6c;
        color: white;
        padding: 10px 15px;
        font-weight: bold;
    }

    .otp-input {
        margin-top: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    .verify-btn {

        background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        ;
        color: white !important;
        border: none;
        padding: 10px 20px;
        width: 100%;
        /* Make it full width */
        text-align: center;
        display: block;
        border-radius: 3px;
        /* Optional for smooth edges */
        box-shadow: 0px 0px 3px 1px #ccc;
        /* Optional shadow for effect */
        height: 42px !important;
    }

    .verify-btn:hover {
        color: white !important;
        /* Ensures text color stays white */
        background-color: #002766;
        /* Slightly darker shade for hover effect */
    }

    .helper-text {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-top: 8px;
    }

    .resend-btn {
        background: none;
        border: none;
        color: #003366;
        font-weight: bold;
        text-decoration: none;
        cursor: pointer;
        font-size: 14px;
    }

    .resend-btn:hover {
        text-decoration: underline;
    }

    #resend_section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        max-width: 350px;
        /* Ensures same width as input fields */
        margin: auto;
        margin-top: -8px;
        font-size: 12px;
        /* Adjust font size if needed */
    }

    .container-login100 {
        background-size: 140% 110%
    }

    .btn.verify-btn {
        width: 100%;
        background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
        ;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        margin-top: 10px;
        transition: 0.3s;
        font-weight: 400px;

    }

    #sendOTP,
    #show_hide_resend {
        font-weight: 500;
        font-size: 14px;
        background-color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    }
    </style>
    <?php } ?>


    <style type="text/css">
    #error_otp {
        color: red;
    }

    .parsley-errors-list {
        display: none;
    }

    #sendOTP:disabled {
        opacity: 0.6;
    }
    </style>

    <style>
    .container {
        text-align: center;
        width: 383px;
    }

    .otp-button {
        width: 100%;
        padding: 12px;
        background-color: #6a38f5;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
    }

    .divider {
        display: flex;
        align-items: center;
        text-align: center;
        margin: 20px 0;
        color: #aaa;
        font-size: 14px;
    }

    .divider::before,
    .divider::after {
        content: "";
        flex: 1;
        border-bottom: 1px solid #ddd;
    }

    .divider::before {
        margin-right: 10px;
    }

    .divider::after {
        margin-left: 10px;
    }

    .email-login,
    .mobile-login {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: background 0.3s;
    }

    .email-login:hover,
    .mobile-login:hover {
        background-color: #f1f1f1;
    }

    .email-login img,
    .mobile-login img {
        width: 16px;
        margin-right: 8px;
    }

    .school_name {
        color: #212121;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 120%;
    }

    @media screen and (min-width: 992px) {
        .col-md-4.d-flex {
            padding: 0 40px 0 20px;
            margin-top: -50px;
        }

        input[readonly] {
            background-color: #fff;
            /* Match the background color of editable inputs */
            color: #333;
            /* Ensure text color is consistent */
            border: none;
            /* Remove any border changes */
            outline: none;
            /* Remove focus outline */
            cursor: not-allowed;
            /* Indicate the field is not editable */
        }
    }
    input::placeholder {
        color: #9EA2AE;
        opacity: 1;
    }
    </style>