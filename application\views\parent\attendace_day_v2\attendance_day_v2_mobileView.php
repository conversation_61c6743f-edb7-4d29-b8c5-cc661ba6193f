<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Calendar</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            background: #fafbfc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 430px;
            margin: 0 auto;
            background: #fafbfc;
            min-height: 100vh;
            padding: 0 0 32px 0;
        }
        .attendance-header {
            font-size: 1.35rem;
            font-weight: 700;
            margin: 0;
            text-align: center;
            padding: 32px 0 18px 0;
            letter-spacing: 0.01em;
        }
        .alert {
            background: #fff7e0;
            color: #b68400;
            border-radius: 12px;
            padding: 18px 24px 14px 24px;
            margin: 0 16px 22px 16px;
            font-size: 1.08rem;
            display: flex;
            flex-direction: column;
            gap: 6px;
            font-family: inherit;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        .alert span:first-child {
            color: #e1a100;
            font-size: 1.2em;
            margin-bottom: 2px;
            font-weight: 700;
        }
        .alert span b {
            color: #b68400;
            font-weight: 700;
        }
        .summary-card-wrap {
            display: flex;
            gap: 0;
            margin: 0 16px 22px 16px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.03);
            border: 1px solid #f0f0f0;
            overflow: hidden;
        }
        .summary-card {
            flex: 1;
            background: #fff;
            border-right: 1px solid #f0f0f0;
            border-radius: 0;
            padding: 18px 0 12px 0;
            text-align: center;
        }
        .summary-card:last-child {
            border-right: none;
        }
        .summary-card .label {
            font-size: 1.01rem;
            color: #888;
            margin-bottom: 8px;
        }
        .summary-card .value {
            font-size: 1.45rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }
        .summary-card .value.attendance { color: #5b6dfa; }
        .summary-card .value.present { color: #2ca66f; }
        .summary-card .value.absent { color: #e14c4c; }
        .summary-card .value.holiday { color: #e1a100; }
        .calendar-card {
            background: #fff;
            border-radius: 12px;
            margin: 0 16px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.03);
            border: 1px solid #f0f0f0;
            padding: 0 0 18px 0;
        }
        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 18px 0 18px;
        }
        .calendar-header .month {
            font-size: 1.13rem;
            font-weight: 600;
            color: #222;
            letter-spacing: 0.01em;
        }
        .calendar-header button {
            background: #f6f8fa;
            border: none;
            border-radius: 6px;
            font-size: 1.2rem;
            width: 36px;
            height: 36px;
            color: #5b6dfa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .calendar-table th, .calendar-table td {
            width: 14.28%;
            height: 54px;
            text-align: center;
            vertical-align: top;
            border: none;
            background: #fff;
            font-size: 1.01rem;
            position: relative;
        }
        .calendar-table th {
            color: #888;
            font-weight: 500;
            font-size: 1.01rem;
            background: #fff;
            padding-bottom: 6px;
        }
        .calendar-table .day-number {
            font-size: 1.01rem;
            font-weight: 600;
            margin-bottom: 2px;
            display: block;
            color: #222;
        }
        .calendar-table .today {
            background: #f5f7ff !important;
            border-radius: 50%;
            color: #4c6ef5 !important;
            border: 1.5px solid #4c6ef5;
            padding: 2px 7px;
            display: inline-block;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 0.93rem;
            font-weight: 500;
            margin-top: 4px;
            margin-bottom: 2px;
        }
        .present { background: #eafaf1; color: #2ca66f; }
        .absent { background: #fdeaea; color: #e14c4c; }
        .holiday { background: #fff7e0; color: #e1a100; }
        .weekoff { background: #f0f0f0; color: #aaa; }
        .leave { background: #fffbe6; color: #b68400; border: 1px solid #ffe58f; }
        .calendar-table td {
            padding: 0;
        }
        .fc-event {
            border: none !important;
            padding: 0 !important;
            border-radius: 7px !important;
            font-size: 15px !important;
            font-weight: 600 !important;
            margin: 2px 0 !important;
            display: flex !important;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            min-width: 28px;
            min-height: 28px;
            max-width: 28px;
            max-height: 28px;
            box-sizing: border-box;
        }
        .fc-event .att-icon {
            font-size: 17px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff !important; /* White icon for all event types */
        }
        .fc-event.present {
            background:#308242 !important;
            color: #fff !important;
            border: 1.5px solid #2ca66f !important;
        }
        .fc-event.absent {
            background:#e14c4c !important;
            color: #fff !important;
            border: 1.5px solid #e14c4c !important;
        }
        .fc-event.holiday {
            background:#e1a100 !important;
            color: #fff !important;
            border: 1.5px solid #e1a100 !important;
        }
        .fc-event.weekoff {
            background:#756969 !important;
            color: #fff !important;
            border: 1.5px solid #aaa !important;
        }
        .fc-event.halfday {
            background: #fffbe6 !important;
            color: #fff !important;
            border: 1.5px solid #b68400 !important;
        }
    </style>
</head>
<body>
<?php 
$present_days= isset($getOverallDetails['present_days']) ? $getOverallDetails['present_days'] : '--';
$absent_days= isset($getOverallDetails['absent_days']) ? $getOverallDetails['absent_days'] : '--';
$holidays= isset($getOverallDetails['holidays']) ? $getOverallDetails['holidays'] : '--';
$total_days= isset($getOverallDetails['total_days']) ? $getOverallDetails['total_days'] : '--';
if (is_numeric($present_days) && is_numeric($total_days) && is_numeric($holidays) && ($total_days - $holidays) > 0) {
    $percentage = round(($present_days / ($total_days - $holidays)) * 100, 2);
} else {
    $percentage = '--';
}
?>
<div class="container">
    <div class="attendance-header">Attendance</div>
    <?php if(is_numeric($percentage) && $percentage < 75): ?>
    <div class="alert">
        <span>⚠️  Your attendance is below 75%</span>
        <span>Please improve your attendance</span>
    </div>
    <?php endif; ?>
    <div class="summary-card-wrap">
        <div class="summary-card">
            <div class="label">Overall Attendance</div>
            <div class="value attendance"><?php echo is_numeric($percentage) ? $percentage.'%' : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Present Days</div>
            <div class="value present"><?php echo is_numeric($present_days) ? $present_days : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Absent Days</div>
            <div class="value absent"><?php echo is_numeric($absent_days) ? $absent_days : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Holidays</div>
            <div class="value holiday"><?php echo is_numeric($holidays) ? $holidays : '--'; ?></div>
        </div>
    </div>
    <div class="calendar-legend" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px 12px; margin: 14px 0 0 0; max-width: 360px;">
        <div style="display: flex; align-items: center; gap: 4px; min-width: 80px;">
            <span class="fc-event present" style="width:18px; height:18px; display:flex; align-items:center; justify-content:center;"><span class="att-icon">✔️</span></span>
            <span style="font-size:0.93rem; color:#308242;">Present</span>
        </div>
        <div style="display: flex; align-items: center; gap: 4px; min-width: 80px;">
            <span class="fc-event absent" style="width:18px; height:18px; display:flex; align-items:center; justify-content:center;"><span class="att-icon">❌</span></span>
            <span style="font-size:0.93rem; color:#e14c4c;">Absent</span>
        </div>
        <div style="display: flex; align-items: center; gap: 4px; min-width: 80px;">
            <span class="fc-event holiday" style="width:18px; height:18px; display:flex; align-items:center; justify-content:center;"><span class="att-icon">⭐</span></span>
            <span style="font-size:0.93rem; color:#e1a100;">Holiday</span>
        </div>
        <div style="display: flex; align-items: center; gap: 4px; min-width: 80px;">
            <span class="fc-event weekoff" style="width:18px; height:18px; display:flex; align-items:center; justify-content:center;"><span class="att-icon">🛌</span></span>
            <span style="font-size:0.93rem; color:#756969;">Weekoff</span>
        </div>
        <div style="display: flex; align-items: center; gap: 4px; min-width: 80px;">
            <span class="fc-event halfday" style="width:18px; height:18px; display:flex; align-items:center; justify-content:center;"><span class="att-icon">H</span></span>
            <span style="font-size:0.93rem; color:#b68400;">Halfday</span>
        </div>
    </div>
    <div class="calendar-card">
        <div id="calendar"></div>
    </div>
</div>
<!-- FullCalendar CSS and JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>
<script>
function fetchAttendanceEvents(startDate, endDate) {
    var student_id = <?php echo isset($studentId) ? json_encode($studentId) : 'null'; ?>;
    return fetch('<?php echo base_url("parent_controller/get_attendance_events"); ?>', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            start: startDate,
            end: endDate,
            student_id: student_id
        })
    }).then(response => response.json());
}
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: ''
        },
        events: function(fetchInfo, successCallback, failureCallback) {
            fetchAttendanceEvents(fetchInfo.startStr, fetchInfo.endStr)
                .then(data => successCallback(data))
                .catch(() => failureCallback([]));
        },
        eventDidMount: function(info) {
            // Show only icon, no text, using classNames array for event type
            let icon = '';
            let classList = info.event.classNames || [];
            if (classList.includes('present')) {
                icon = '<span class="att-icon">✔️</span>';
            } else if (classList.includes('absent')) {
                icon = '<span class="att-icon">❌</span>';
            } else if (classList.includes('holiday')) {
                icon = '<span class="att-icon">⭐</span>';
            } else if (classList.includes('weekoff')) {
                icon = '<span class="att-icon">🛌</span>';
            } else if (classList.includes('halfday')) {
                icon = '<span class="att-icon">H</span>';
            }
            if (icon) {
                info.el.innerHTML = icon;
            } else {
                info.el.innerHTML = '';
            }
            // Remove any text nodes that may remain
            Array.from(info.el.childNodes).forEach(function(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    node.textContent = '';
                }
            });
            info.el.title = info.event.title;
        }
    });
    calendar.render();
});
</script>
</body>
</html>
