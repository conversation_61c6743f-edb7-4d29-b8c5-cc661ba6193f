<!-- Add these script references at the top of your file or in the head section -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<style>
    /* Mobile-specific styles */
    .mobile-container {
        padding: 10px;
    }
    
    .mobile-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    
    .mobile-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
    
    .mobile-filters {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    .mobile-filter-group {
        margin-bottom: 15px;
    }
    
    .mobile-filter-group:last-child {
        margin-bottom: 0;
    }
    
    .mobile-filter-label {
        font-weight: 600;
        margin-bottom: 5px;
        display: block;
    }
    
    .mobile-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .mobile-table th,
    .mobile-table td {
        padding: 10px;
        border: 1px solid #dee2e6;
    }
    
    .mobile-table th {
        background: #f8f9fa;
        font-weight: 600;
    }
    
    .mobile-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        overflow: hidden;
    }
    
    .mobile-card-header {
        padding: 15px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .mobile-card-body {
        padding: 15px;
    }
    
    .mobile-btn {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 4px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .mobile-btn-primary {
        background: #007bff;
        color: white;
    }
    
    .mobile-btn-info {
        background: #17a2b8;
        color: white;
    }
    
    /* Circular create button */
    .create-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transition: transform 0.2s;
    }
    
    .create-btn:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    
    .create-btn i {
        font-size: 24px;
    }
    
    /* Responsive table styles */
    @media screen and (max-width: 768px) {
        .mobile-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .mobile-table th,
        .mobile-table td {
            min-width: 120px;
        }
    }
</style>

<div class="mobile-container">
    <div class="mobile-header">
        <h3 class="mobile-title">Emergency Exit</h3>
        <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/create_emergency_exit');?>" class="create-btn">
            <i class="fa fa-plus"></i>
        </a>
    </div>
    
    <div class="mobile-filters">
        <div class="mobile-filter-group">
            <label class="mobile-filter-label">Date Range:</label>
            <div id="reportrange" class="dtrange" style="width: 100%">                                            
                <span></span>
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
            </div>
        </div>
        
        <div class="mobile-filter-group">
            <label class="mobile-filter-label">Section:</label>
            <select id="class_section_id" class="form-control">
                <option value="">All Sections</option>
                <?php foreach ($class_section as $section): ?>
                <option value="<?= $section->sectionID ?>"><?= $section->class_name.$section->section_name ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <button type="button" class="mobile-btn mobile-btn-info" id="search_btn">
            <i class="fa fa-search"></i> Search
        </button>
    </div>
    
    <div class="mobile-card">
        <div class="mobile-card-body">
            <div class="table-responsive">
                <table id="emergency_exit_table" class="mobile-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Student Name</th>
                            <th>Class/Section</th>
                            <th>Pick-up By</th>
                            <th>Pick-up DateTime</th>
                            <th>Remarks</th>
                            <th>Allowed By</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded here by DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize date range picker
    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment()
    }, function(start, end) {
        $('#reportrange span').html(start.format('DD-MM-YYYY') + ' to ' + end.format('DD-MM-YYYY'));
        $('#from_date').val(start.format('YYYY-MM-DD'));
        $('#to_date').val(end.format('YYYY-MM-DD'));
    });
    
    // Set initial values
    $('#reportrange span').html(moment().subtract(29, 'days').format('DD-MM-YYYY') + ' to ' + moment().format('DD-MM-YYYY'));
    $('#from_date').val(moment().subtract(29, 'days').format('YYYY-MM-DD'));
    $('#to_date').val(moment().format('YYYY-MM-DD'));
    
    // Initialize DataTable with AJAX source
    var dataTable = $('#emergency_exit_table').DataTable({
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('attendance_day_v2/attendance_day_v2/get_emergency_exit_records'); ?>",
            "type": "POST",
            "data": function(d) {
                d.from_date = $('#from_date').val();
                d.to_date = $('#to_date').val();
                d.class_section_id = $('#class_section_id').val();
            },
            "dataSrc": function(json) {
                if (!json || typeof json !== 'object') {
                    console.error('Invalid JSON response:', json);
                    return [];
                }
                
                if (Array.isArray(json)) {
                    return json;
                }
                
                if (json.data && Array.isArray(json.data)) {
                    return json.data;
                }
                
                console.error('Unexpected JSON structure:', json);
                return [];
            }
        },
        "columns": [
            { "data": null, "render": function(data, type, row, meta) { return meta.row + 1; } },
            { "data": "student_name", "defaultContent": "" },
            { "data": "class_section", "defaultContent": "" },
            { "data": null, "render": function(data, type, row) { 
                if (!row.pickup_name && !row.emergency_exit_pickup_by) return "";
                return (row.pickup_name || "") + ' (' + (row.emergency_exit_pickup_by || "") + ')'; 
            }},
            { "data": "emergency_exit_time", "defaultContent": "" },
            { "data": "emergency_exit_remarks", "defaultContent": "" },
            { "data": "allowed_by_name", "defaultContent": "" }
        ],
        "paging": true,
        "searching": false,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "language": {
            "emptyTable": "No records found"
        },
        "dom": 'rt<"bottom"ip>',
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
    
    // Search button click handler
    $('#search_btn').on('click', function() {
        dataTable.ajax.reload();
    });
    
    // Error handling for DataTables
    $.fn.dataTable.ext.errMode = 'throw';
    
    // Add responsive behavior
    $(window).on('resize', function() {
        dataTable.columns.adjust();
    });
});
</script> 