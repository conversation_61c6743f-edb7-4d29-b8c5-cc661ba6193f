    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="student_firstname">First Name <font color="red">*</font></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input placeholder="Enter Firstname" id="student_firstname" name="student_firstname" type="text"  class="form-control input-md" data-parsley-error-message="Cannot be empty, only alphabets" data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-minlength="2" required="">
        </div>
    </div>

    <?php if (in_array('student_middle_name', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="student_middle_name">Middle Name <?php echo in_array('student_middle_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('student_middle_name', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Middle Name" id="student_middle_name" name="student_middle_name" type="text"  class="form-control input-md" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
        </div>
    </div>
    <?php } ?>

    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="student_lastname">Last Name <?php echo in_array('last_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('last_name', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Lastname" id="student_lastname" name="student_lastname" type="text"  class="form-control input-md" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
        </div>
    </div>

    <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="gender"> Gender <?php echo in_array('gender', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group"> 
            <label class="radio-inline" for="gender-0">
                <input <?php echo in_array('gender', $display_required_fields['personal_info']) ? "required" : "" ?>   type="radio" data-parsley-group="block1" name="gender" id="gender-0" value="M" checked>
                Male
            </label>
            <label class="radio-inline" for="gender-1">
                <input <?php echo in_array('gender', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio" data-parsley-group="block1"  name="gender" id="gender-1" value="F">
                Female
            </label>
            <label class="radio-inline" for="gender-2">
                <input <?php echo in_array('gender', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio" data-parsley-group="block1"  name="gender" id="gender-2" value="O">
                Other
            </label>
        </div>
    </div>

    <?php if (in_array('nick_name', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="student_nickname">Nick Name <?php echo in_array('nick_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>  
            <input <?php echo in_array('nick_name', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Nick name" id="student_nickname" name="student_nickname" type="text"  class="form-control input-md" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('email', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="email">Email <?php echo in_array('email', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('email', $display_required_fields['personal_info']) ? "required" : "" ?>  type="email" placeholder="Enter Email" id="email" data-type="student" name="s_email" class="form-control input-md emailtype">      
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('preferred_contact_no', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="contact_no">Preferred Contact Number <?php echo in_array('preferred_contact_no', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-2 pr-1 pl-0">
            <select <?php echo in_array('preferred_contact_no', $display_required_fields['personal_info']) ? "required" : "" ?> name="s_country_code" id="s_country_code" class="form-control">
                <?php foreach ($this->config->item('country_codes') as $key => $code) { ?>
                    <option value="<?= $code?>"><?= $code?></option>
                <?php } ?>
            </select>
        </div>  
        <div class="col-md-6 input-group">  
            <input <?php echo in_array('preferred_contact_no', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Enter Peferred Contact Number" id="contact_no" data-type="student" data-parsley-pattern="^[0-9 -()+]+$" name="contact_no" class="form-control input-md emailtype">
        </div>
    </div>
    <?php } ?>
   
    <?php if (in_array('student_whatsapp_num', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="whatsapp_number">Student Whatsapp Number <?php echo in_array('student_whatsapp_num', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('student_whatsapp_num', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Enter Whatsapp Number" id="whatsapp_number" data-type="student" data-parsley-pattern="^[0-9 -()+]+$" name="whatsapp_number" class="form-control input-md">
        </div>
    </div>
    <?php } ?>
   
    <?php if (in_array('dob', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="student_dob"> Date of Birth <?php echo in_array('dob', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <div class="input-group date" id="">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span> 
                <input <?php echo in_array('dob', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" class="form-control dob_dtpicker" id="student_dob" name="student_dob"  placeholder="Enter Date of Brith" autocomplete="off" onkeydown="return false" >
            </div>
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('birth_district', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="birth_taluk">Birth Place <?php echo in_array('birth_taluk', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-4 pl-0">
            <input <?php echo in_array('birth_taluk', $display_required_fields['personal_info']) ? "required" : "" ?>  id="birth_taluk" name="birth_taluk" type="text"  placeholder="Taluk/District" class="form-control input-md" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$" >             
        </div>
        <div class="col-md-4 pr-0">
            <input <?php echo in_array('birth_district', $display_required_fields['personal_info']) ? "required" : "" ?>  id="birth_district" name="birth_district" type="text"  placeholder="State" class="form-control input-md ml-1" data-parsley-error-message="Only alphabets and spaces allowed" data-parsley-pattern="^[a-zA-Z ]+$" >             
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('passport_number', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="passport_number">PassPort Number <?php echo in_array('passport_number', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('passport_number', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Enter PassPort Number" id="passport_number"  name="passport_number" class="form-control">
        </div>
    </div>
    <?php }?>
    
    <?php if (in_array('passport_issued_place', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="passport_issued_place">PassPort Issued Place <?php echo in_array('passport_issued_place', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('passport_issued_place', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Enter PassPort Issued place" id="passport_issued_place"  name="passport_issued_place" class="form-control">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('passport_validity', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="passport_validity">PassPort Validity <?php echo in_array('passport_validity', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <div class="input-group date" id="passport_date">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span> 
                <input <?php echo in_array('passport_validity', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Select Passport expiry date" id="passport_validity"  name="passport_validity" class="form-control">
            </div>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('student_indian_visa_number', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="visa_number">Indian Visa Number <?php echo in_array('student_indian_visa_number', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('student_indian_visa_number', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" placeholder="Enter Indian Visa Number" id="visa_number"  name="visa_number" class="form-control">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('student_indian_visa_expiry_date', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="visa_expiry_date">Visa Expiry Date <?php echo in_array('student_indian_visa_expiry_date', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <div class="input-group date" id="visa_date">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span> 
                <input <?php echo in_array('student_indian_visa_expiry_date', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="visa_expiry_date" placeholder="Select Visa Expiry date"  name="visa_expiry_date" class="form-control">
            </div>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('identification_mark1', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">
        <label class="col-md-4 control-label" for="identification_mark1">Identification Mark1 <?php echo in_array('identification_mark1', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('identification_mark1', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="identification_mark1"  name="identification_mark1" class="form-control" placeholder="Enter Identification mark1">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('identification_mark2', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="identification_mark2">Identification Mark2 <?php echo in_array('identification_mark2', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('identification_mark2', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="identification_mark2"  name="identification_mark2" class="form-control"  placeholder="Enter Identification mark2">
        </div>
    </div>
    <?php } ?>
    
    <?php if (in_array('religion', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="religion"> Religion <?php echo in_array('religion', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('religion', $display_required_fields['personal_info']) ? "required" : "" ?> id="religion" name="religion" class="form-control input-md">
                <option value="-">Select Religion</option>
                <?php foreach ($this->config->item('religions') as $religion) {
                    echo '<option>'.$religion.'</option>';
                } ?>
            </select>
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('current_nearest_location', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="current_nearest_location">Current Nearest Location <?php echo in_array('current_nearest_location', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('current_nearest_location', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="current_nearest_location"  name="current_nearest_location" class="form-control"  placeholder="Enter Current Nearest Location">
        </div>
    </div>
    <?php } ?> 

    <?php if (in_array('category', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="category"> Category <?php echo in_array('category', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('category', $display_required_fields['personal_info']) ? "required" : "" ?> id="casteid" name="category" class="form-control input-md"  data-parsley-error-message="Category cannot be empty">
                <option value=""><?php echo "Select Category" ?></option>
                <?php foreach ($category as $key => $value) { ?>
                <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                <?php } ?>

            </select>
        </div>
    </div>
    <?php } ?> 

    <?php if (in_array('caste', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="casteName"> Caste <?php echo in_array('caste', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-6 pl-0">
            <select <?php echo in_array('caste', $display_required_fields['personal_info']) ? "required" : "" ?> id="casteName" name="caste" class="form-control input-md">
                <option value=""><?php echo "Select Caste " ?></option>
                <?php foreach ($casteList as $casteObj) { ?>
                    <option value="<?php echo $casteObj->caste; ?>"><?php echo ucwords($casteObj->caste); ?></option>
                <?php } ?>
            </select>
        </div>
        <div class="col-md-1 pl-0">
            <input type="button" class="btn btn-info " data-toggle="modal" data-target="#newCasteDialog" value="Add New" style="margin-left:20px"/>
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('caste_income_certificate_number', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="casteName"> Caste & Income Certificate Number <?php echo in_array('caste_income_certificate_number', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
        <input <?php echo in_array('caste_income_certificate_number', $display_required_fields['personal_info']) ? "required" : "" ?>   placeholder="Enter  Caste & Income Certificate Number" type="text" name="caste_income_certificate_number" class="form-control" placeholder="Enter Caste & Income Certificate Number">
    </div>
</div>
    <?php } ?>

    <?php if (in_array('aadhar_no', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label">Aadhaar Number <?php echo in_array('aadhar_no', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('aadhar_no', $display_required_fields['personal_info']) ? "required" : "" ?> autocomplete="off"  placeholder="Enter Aadhaar Number" pattern="^\d{12}$" data-parsley-error-message="Enter valid Aadhar number" oninput="this.value = this.value.replace(/[^0-9]/g, '');"  maxlength="12" data-parsley-maxlength="12" data-parsley-type="number" type="text" name="std_aadhar" class="form-control" placeholder="Enter Aadhar Number">
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('nationality', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">
        <label class="col-md-4 control-label" for="nationality">Nationality <?php echo in_array('nationality', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('nationality', $display_required_fields['personal_info']) ? "required" : "" ?> id="nationality" name="nationality" class="form-control input-md ">
                <option value="-">Select Nationality</option>
                <?php foreach ($this->config->item('nationality') as $nation) {
                    echo '<option>'.$nation.'</option>';
                } ?>
            </select>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('point_of_contact', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="point_of_contact">Point of Contact <?php echo in_array('point_of_contact', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('point_of_contact', $display_required_fields['personal_info']) ? "required" : "" ?> id="point_of_contact"  name="point_of_contact" class="form-control">
                <option value="">Select</option>
                <option value="Father">Father</option>
                <option value="Mother">Mother</option>
                <option value="Guardian">Guardian</option>
            </select>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('distance_from_school_to_home_in_km', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="distance_from_school_to_home_in_km">School to Home Distance in Km <?php echo in_array('distance_from_school_to_home_in_km', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('distance_from_school_to_home_in_km', $display_required_fields['personal_info']) ? "required" : "" ?>  id="distance_from_school_to_home_in_km"  name="distance_from_school_to_home_in_km" class="form-control" placeholder="Enter the schhool to home distance">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('blood_group', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="blood_group"> Blood Group <?php echo in_array('blood_group', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('blood_group', $display_required_fields['personal_info']) ? "required" : "" ?> id="bld_group" name="blood_group" type="text"  class="form-control">
                <option value=""><?php echo "Select Blood Group" ?></option>
                <?php foreach ($this->config->item('blood_groups') as $blood_group) {
                    echo '<option value="'.$blood_group.'" >'.$blood_group.'</option>';
                } ?>
            </select>
        </div>
    </div>
    <?php } ?>
    
    <?php if (in_array('student_living_with', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 mt-3 mb-0 form-group">  
        <label class="col-md-4 control-label" for="student_living_with">Student Living With <?php echo in_array('student_living_with', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <select <?php echo in_array('student_living_with', $display_required_fields['personal_info']) ? "required" : "" ?> id="student_living_with"  name="student_living_with" class="form-control">
                <option value="">Select</option>
                <option value="Father">Father</option>
                <option value="Mother">Mother</option>
                <option value="Guardian">Guardian</option>
            </select>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('is_minority', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="minority"> Is Minor <?php echo in_array('is_minority', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group"> 
            <label class="radio-inline" for="minority-1">
                <input <?php echo in_array('is_minority', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio" name="minority" id="minority-1" value="1" checked>
                Yes
            </label>
            <label class="radio-inline" for="minority-2">
                <input <?php echo in_array('is_minority', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio"  name="minority" id="minority-2" value="0">
                No
            </label>
        </div>
    </div>
    <?php } ?>

    <?php if (in_array('is_single_child', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="single_child"> Is Single Child <?php echo in_array('is_single_child', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group"> 
            <label class="radio-inline" for="single_child-1">
                <input <?php echo in_array('is_single_child', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio" name="single_child" id="single_child-1" value="1" checked>
                Yes
            </label>
            <label class="radio-inline" for="single_child-2">
                <input <?php echo in_array('is_single_child', $display_required_fields['personal_info']) ? "required" : "" ?>  type="radio"  name="single_child" id="single_child-2" value="0">
                No
            </label>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling1_name', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling1_name">Sibling1 Name <?php echo in_array('sibling1_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling1_name', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling1_name"  name="sibling1_name" class="form-control" placeholder="Enter sibling1 name">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling1_occupation', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling1_occupation">Sibling1 Occupation <?php echo in_array('sibling1_occupation', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling1_occupation', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling1_occupation"  name="sibling1_occupation" class="form-control" placeholder="Enter sibling1 occupation">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling1_mobile_num', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling1_mobile_number">Sibling1 Mobile Number <?php echo in_array('sibling1_mobile_num', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling1_mobile_num', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling1_mobile_number"  name="sibling1_mobile_number" class="form-control" data-parsley-pattern="^[0-9 -()+]+$"  placeholder="Enter sibling1 Mobile Number">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling2_name', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling2_name">Sibling2 Name <?php echo in_array('sibling2_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling2_name', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling2_name"  name="sibling2_name" class="form-control"  placeholder="Enter sibling2 name">
        </div>
    </div>
    <?php }?>
    
    <?php if (in_array('sibling2_occupation', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling2_occupation">Sibling2 Occupation <?php echo in_array('sibling2_occupation', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling2_occupation', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling2_occupation"  name="sibling2_occupation" class="form-control"  placeholder="Enter sibling2 occupation">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling2_mobile_num', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling2_mobile_number">Sibling2 Mobile Number <?php echo in_array('sibling2_mobile_num', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling2_mobile_num', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling2_mobile_number"  name="sibling2_mobile_number" class="form-control" data-parsley-pattern="^[0-9 -()+]+$"  placeholder="Enter sibling2 Mobile Number">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling3_name', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling3_name">Sibling3 Name <?php echo in_array('sibling3_name', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling3_name', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling3_name"  name="sibling3_name" class="form-control"  placeholder="Enter sibling3 name">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling3_occupation', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling3_occupation">Sibling3 Occupation <?php echo in_array('sibling3_occupation', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling3_occupation', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling3_occupation"  name="sibling3_occupation" class="form-control"  placeholder="Enter sibling3 occupation">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('sibling3_mobile_num', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="sibling3_mobile_number">Sibling3 Mobile Number <?php echo in_array('sibling3_mobile_num', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('sibling3_mobile_num', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text"  id="sibling3_mobile_number"  name="sibling3_mobile_number" class="form-control" data-parsley-pattern="^[0-9 -()+]+$"  placeholder="Enter sibling3 Mobile Number">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('parents_marriage_anniversary', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label" for="marriage_anniversary">Parent Marriage Anniversary <?php echo in_array('parents_marriage_anniversary', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <div class="input-group date" id="anniversary_date">
                <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span> 
                <input <?php echo in_array('parents_marriage_anniversary', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" id="marriage_anniversary" name="marriage_anniversary" class="form-control"  placeholder="Parent Marriage Anniversary">
            </div>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('extracurricular_activities', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label">Extra Curricular Activities <?php echo in_array('extracurricular_activities', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
            <input <?php echo in_array('extracurricular_activities', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Extra Curricular Activities" type="text" name="extracurricular_activities" class="form-control">
        </div>
    </div>
    <?php }?>

    <?php if (in_array('student_hobbies', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label">Student Hobbies <?php echo in_array('student_hobbies', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <textarea <?php echo in_array('student_hobbies', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Student Hobbies" name="student_hobbies" class="form-control" rows="3"></textarea>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('student_area_of_strength', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label">Student’s 3 Areas of Strength in order of biggest strength first <?php echo in_array('student_area_of_strength', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <textarea <?php echo in_array('student_area_of_strength', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Student areas of srength" name="student_area_of_strength" class="form-control" rows="3"></textarea>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('student_area_of_improvement', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label">Student’s 3 Areas of Improvement in order of most concerning area first <?php echo in_array('student_area_of_improvement', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <textarea <?php echo in_array('student_area_of_improvement', $display_required_fields['personal_info']) ? "required" : "" ?>  placeholder="Enter Student areas of improvement" name="student_area_of_improvement" class="form-control" rows="3"></textarea>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('did_they_enrolled_in_different_institute_earlier', $display_enabled_fields['personal_info'])) { ?>
     <div class="col-md-6 my-3 form-group">  
        <label class="col-md-4 control-label">Had you enrolled for 1st PU / 11th std in a different college earlier?<?php echo in_array('did_they_enrolled_in_different_institute_earlier', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>
        <div class="col-md-8 input-group">
            <select class="form-control" name="did_they_enrolled_in_different_institute_earlier" id="" <?php echo in_array('did_they_enrolled_in_different_institute_earlier', $display_required_fields['personal_info']) ? "required" : "" ?>>
                <option value="">Select</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>
        </div>
    </div>
    <?php }?>

    <?php if (in_array('picture_url', $display_enabled_fields['school_info'])) { ?>
    <div class="col-md-6 my-3 form-group"> 
        <div class="form-group">  
            <label class="col-md-4"></label>
            <div class="col-md-8 input-group">
                <div id="dvPreview">
                    <img id="previewing"  name="photograph" style="width: 100px;height: 70px;" src="<?php echo $this->config->item('s3_base_url')."/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png" ?>" />
                    <span id="percentage_student_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span>
                </div>
            </div>
        </div>

        <div id="wrapper" class="form-group">
            <label class="col-md-4 control-label" for="netAmount">Upload Student Image <?php echo in_array('picture_url', $display_required_fields['school_info']) ? '<font color="red">*</font>' : "" ?></label>  
            <div class="col-md-8 input-group">
                <input <?php echo in_array('picture_url', $display_required_fields['school_info']) ? "required" : "" ?>  class="form-control" id="fileupload" name="student_photo" type="file"  accept="image/*"/>
                <input type="hidden" name="high_quality_url" id="student_high_quality_url">
                <p style="color:red;" id="fileuploadError"></p>
            </div>
        </div>
     </div>
     <?php }?>

     <?php if (in_array('student_signature', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <div class="form-group">  
            <label class="col-md-4"></label>
            <div class="col-md-8 input-group">
                <div id="dvPreview">
                    <img id="previewing_signature"  name="" style="width: 100px;height: 70px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/contract_signature.png" />
                    <span id="percentage_sign_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span>
                </div>
            </div>
        </div>

        <div id="wrapper" class="form-group">
            <label class="col-md-4 control-label" for="netAmount">Upload Student Signature</label>  
            <div class="col-md-8 input-group">
                <input class="form-control" id="fileuploadsign" name="sign_photo" type="file"  accept="image/*"/>
                <input type="hidden" name="student_signature" id="student_signature">
                <p style="color:red;" id="fileuploadsignError"></p>
            </div>
        </div>
    </div>
    <?php }?>

     <?php if (in_array('family_picture_url', $display_enabled_fields['personal_info'])) { ?>
    <div class="col-md-6 my-3 form-group">
        <div class="form-group">  
            <label class="col-md-4"></label>
            <div class="col-md-8 input-group">
                <div id="dvPreview">
                    <img id="previewing_family"  name="family_photograph" style="width: 100px;height: 70px;" src="<?php echo $this->config->item('s3_base_url')."/nextelement-common/Parent App Icons 64px/family_placeholder_icon.png" ?>" />
                    <span id="percentage_family_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span>
                </div>
            </div>
        </div>

        <div id="wrapper" class="form-group">
            <label class="col-md-4 control-label" for="netAmount">Upload Family Image <?php echo in_array('family_picture_url', $display_required_fields['personal_info']) ? '<font color="red">*</font>' : "" ?></label>  
            <div class="col-md-8 input-group">
                <input <?php echo in_array('family_picture_url', $display_required_fields['personal_info']) ? "required" : "" ?>  class="form-control" id="fileuploadfamily" name="family_photo" type="file"  accept="image/*"/>
                <input type="hidden" name="high_quality_url_family" id="student_family_high_quality_url">
                <p style="color:red;" id="fileuploadfamilyError"></p>
            </div>
        </div>
    </div>
    <?php }?>
   
<div class="modal fade" id="newCasteDialog" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width: 48%;" >
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Caste</h4>
            </div>
            <div class="modal-body">
                <input <?php echo in_array('caste', $display_required_fields['personal_info']) ? "required" : "" ?>  type="text" name="newCasteItem" id="newCasteItem" class="form-control" placeholder="Enter new Caste" value="">
                <button type="submit" class="btn btn-primary" data-dismiss="modal" id="newCasteButton" name="newCasteButton">Add</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">


// Family Profile Image Upload
$('#fileuploadfamily').change(function() {
    var src = $(this).val();
    if (src && validateStudentPhoto(this.files[0],'fileuploadfamily')) {
        completed_promises=0;
        current_percentage=0;
        total_promises =1;
        in_progress_promises = total_promises;
        saveFileToStorage1(this.files[0]);
        // $('#previewing_family').css('opacity','0.3');
        // $("#fileuploadfamilyError").html("");
         readURLfamily(this,'previewing_family');
    } else{
        this.value = null;
    }
});

$('#fileuploadsign').change(function() {
    var src = $(this).val();
    if (src && validateStudentPhoto(this.files[0],'fileuploadsign')) {
        completed_promises=0;
        current_percentage=0;
        total_promises =1;
        in_progress_promises = total_promises;
        saveFileToStorage2(this.files[0]);
         readURLfamily(this,'previewing_signature');
    } else{
        this.value = null;
    }
});

function readURLfamily(input,preview_id) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $('#'+preview_id).attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}
function saveFileToStorage1(file) {
    $('#percentage_family_completed').show();
    $('#fileuploadfamily').attr('disabled','disabled');
    $("#btnSubmit").prop('disabled',true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'profilefamily'},
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress1(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type, 
                    "x-amz-acl":"public-read" 
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress1(e.loaded / e.total *100|0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    $('#student_family_high_quality_url').val(path);
                    // savePhoto(path, usertype, id, file);
                    $('#percentage_family_completed').hide();
                    $('#fileuploadfamily').removeAttr('disabled');
                    $('#previewing_family').css('opacity','1');
                    $("#btnSubmit").prop('disabled',false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function (err) {
            reject(err);
        }
    });
}
function single_file_progress1(percentage) {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }
  $("#percentage_family_completed").html(`${current_percentage} %`);
  return false;
}

function saveFileToStorage2(file) {
    $('#percentage_sign_completed').html('0%');
    $('#percentage_sign_completed').show();
    $('#fileuploadsign').attr('disabled','disabled');
    $("#btnSubmit").prop('disabled',true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'signPhoto'},
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress2(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type, 
                    "x-amz-acl":"public-read" 
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress2(e.loaded / e.total *100|0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // savePhoto(path, usertype, id, file);
                    $('#student_signature').val(path);
                    $('#percentage_sign_completed').hide();
                    $('#fileuploadsign').removeAttr('disabled');
                    $('#previewing_signature').css('opacity','1');
                    $("#btnSubmit").prop('disabled',false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function (err) {
            reject(err);
        }
    });
}
function single_file_progress2(percentage) {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }
  $("#percentage_sign_completed").html(`${current_percentage} %`);
  return false;
}
// Student Profile Image Upload
$('#fileupload').change(function(){
    var src = $(this).val();

    if(src && validateStudentPhoto(this.files[0], 'fileupload')){

        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0]);
        $('#previewing').css('opacity','0.3');
        $("#fileuploadError").html("");
        readURL(this);
    } else{
        this.value = null;
    }
});

function validateStudentPhoto(file,errorId){
    if (file.size > 10000000 || file.fileSize > 10000000)
    {
       $("#"+errorId+"Error").html("Allowed file size exceeded. (Max. 10 MB)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $('#previewing').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

 function saveFileToStorage(file) {
    $('#percentage_student_completed').show();
    $('#fileupload').attr('disabled','disabled');
    $("#btnSubmit").prop('disabled',true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'profile'},
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type, 
                    "x-amz-acl":"public-read" 
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total *100|0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    $('#student_high_quality_url').val(path);
                    // savePhoto(path, usertype, id, file);
                    $('#percentage_student_completed').hide();
                    $('#fileupload').removeAttr('disabled');
                    $('#previewing').css('opacity','1');
                    $("#btnSubmit").prop('disabled',false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function (err) {
            reject(err);
        }
    });
}

function single_file_progress(percentage) {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }
  $("#percentage_student_completed").html(`${current_percentage} %`);
  return false;
}

</script>