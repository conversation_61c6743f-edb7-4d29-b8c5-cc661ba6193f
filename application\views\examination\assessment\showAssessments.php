<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('examination/assessments/index');?>">Examination</a></li>
    <li>Assessments</li>
</ul>

<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row">
                <div class="col-md-6">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('examination/assessments/index') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Assessments
                    </h3>
                </div>
                <div class="float-right col-md-5">
                    <ul class="list-group list-group-horizontal-md float-right"
                        style="margin-left:20px;margin-right:15px;">
                        <li class="list-group-item">Short-cuts</li>
                        <?php if($this->authorization->isAuthorized('EXAMINATION.MODULE')) { ?>
                        <li class="list-group-item" style="background-color: aliceblue">
                            <a id="marks_entry_sc" href="<?php echo site_url() . "/examination/assessment_marks_v2/index/$classSelected"; ?>">Goto Marks Entry</a>
                        </li>  
                        <?php } if($this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')) { ?>
                        <li class="list-group-item" style="background-color: aliceblue">
                            <a id="derived_ass_sc" href="<?php echo site_url() . "/examination/Assessments/consolidationIndex/$classSelected"; ?>">Goto Derived Assessments</a>
                        </li> 
                        <?php } if($this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')) { ?>
                        <li class="list-group-item" style="background-color: aliceblue">
                            <a id="report_card_sc" href="<?php echo site_url() . "/examination/assessment_marks/marksCards/$classSelected"; ?>">Goto Report Cards</a>
                        </li>
                        <?php } ?>
                        <li>
                            <!--- Leaving this empty to workaround a bug --->
                        </li>
                    </ul>
                </div>
                <div class="col-md-1">
                </div>
            </div>
        </div>
        


        <style>
                .radio-container {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                    font-family: Arial, sans-serif;
                }
                .radio-option {
                    display: flex;
                    align-items: center;
                }
                input[type="radio"] {
                    margin-right: 8px;
                    position: relative;
                }
            </style>
        <?php if (!empty($classList)) { ?>
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="form-group col-md-3">
                <form id="selectStd" enctype="multipart/form-data" method="post" id="students"
                action="<?php echo site_url('examination/assessments/showAssessments') ?>" class="form-horizontal">
                    <div class="col-md-12">
                        <select id="classId" name="classId" class="form-control input-md" onchange="getAssessments()">
                            <!-- <option value="-1" selected>Select Class</option> -->
                            <?php
                                foreach ($classList as $cl) {
                                    $option = '<option value="' . $cl->id . '"';
                                    if($cl->id == $classSelected)
                                        $option .= ' selected';
                                    $option .= '>' . $cl->class_name .'</option>';
                                    $name= $cl->class_name;
                                    echo $option;
                                }
                                $selected = '';
                                if($classSelected == 0)
                                $selected = ' Selected';
                                ?>
                            <option value="0" <?= $selected ?>>All</option>
                        </select>
                        <p id="info" class="text-info"></p>
                    </div>
            </form>
            </div>
            <div class="radio-container col-md-9">
                <div class="radio-option">
                    <input checked type="radio" id="manual" name="assessment_type" value="manual" style="margin: -5px 5px 0 10px;" onclick="getAssessments()">
                    <label for="manual">Manual Only</label>
                </div>
                <div class="radio-option">
                    <input type="radio" id="auto" name="assessment_type" value="auto" style="margin: -5px 5px 0 10px;" onclick="getAssessments()">
                    <label for="auto">Include Auto with Manual</label>
                </div>

                <ul class="panel-controls">
                    <li>
                        <a href="" class="new_circleShape_res circleButton_noBackColor"
                            onclick="create_assessemnt_class_modal()" style="background: #fe790a" data-toggle="modal"
                            data-target="#show-assessment_uploader">
                            <span class="fa fa-plus backgroundColor_orange" style="font-size: 19px;"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <?php } ?>
        <div class="card-body overflow-auto" id="assTable">
            <h6>Select Class/Grade to view Assessments</h6>
        </div>
    </div>
</div>
</div>
</div>

<div class="modal fade" id="show-assessment_uploader" tabindex="-1" role="dialog" style="width:70%; margin:auto;top:0%"
    data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
        <div class="modal-header rounded" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Create Assessment for <span id="display_clasS_name"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                type="button" class="close" data-dismiss="modal">&times;</button>
        </div>

        <form enctype="multipart/form-data" data-parsley-validate method="post" id="add_assessement_class_wise"
            class="form-horizontal">

            <div class="modal-body" style="height: 70vh; overflow: auto;">

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class">Assessment type</label>
                    <div class="col-md-9 col-xs-12">
                        <select class="form-control" name="ass_type" id="ass_type">
                            <option value="Internal">Internal</option>
                            <option value="External">External</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class">Short name <font color="red">*</font>
                    </label>
                    <div class="col-md-4 col-xs-12">
                        <input maxlength="8" placeholder="Short name" type="text" name="short_name" id="short_name"
                            class="form-control" required="" onkeypress="return checkEntry(event)"
                            onpaste="return checkEntry(event)" onchange="return checkEntry(event)" />
                        <p class="help-block">Spaces and special characters not allowed</p>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class">Long name <font color="red">*</font>
                    </label>
                    <div class="col-md-9 col-xs-12">
                        <input placeholder="Long name" type="text" name="long_name" id="long_name" class="form-control"
                            required="" />
                            <p class="help-block">Spaces and special characters not allowed</p>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class">Display name <font color="red">*</font>
                    </label>
                    <div class="col-md-9 col-xs-12">
                        <input placeholder="Display name" type="text" name="display_name" id="display_name" class="form-control"
                            required="" />
                            <p class="help-block">Spaces and special characters not allowed</p>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class"></label>
                    <div class="col-md-9 col-xs-12">
                        <label class="control-label"><input class="control-label" type="checkbox"
                                name="show_marks_to_parents" id="show_marks_to_parents">&nbsp;&nbsp;&nbsp;Show marks to
                            parents</label>
                        <span class="help-block">Marks will be shown in the parent application.</span>
                    </div>
                </div>
                <div class="form-group" style=" width: 48%; padding-left: 11.1vh;" id="enability">
                    <label class="col-md-3 col-xs-12 control-label label-class" style=""></label>
                    <div class="col-md-9 col-xs-12">
                        <label class="control-label"><input class="control-label" onchange="addRemarksGroup()"
                                type="checkbox" name="enable_subject_remarks"
                                id="enable_subject_remarks">&nbsp;&nbsp;&nbsp;Enable Subject Remarks</label>
                    </div>
                </div>
                <div class="form-group" id="subjectGroup" style="display: none; width: 50%;">
                    <div class="col-md-6 col-xs-4 lbl-div">
                        <select class="form-control" name="remarks_group" id="remarks_group">
                            <option value="">Select Remarks Group</option>
                            <?php foreach ($remarksGroup as $key => $group) {
                                echo '<option value="'.$group->id.'">'.$group->group_name.'</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 col-xs-12 control-label label-class">Description</label>
                    <div class="col-md-9 col-xs-9">
                        <textarea placeholder="Add Description" rows="4" class="form-control" name="description"
                            id="description"></textarea>
                    </div>

                </div>
                <div class="form-group">

                    <label class="col-md-3 col-xs-12 control-label label-class">Remarks </label>
                    <div class="col-md-9 col-xs-9"><textarea placeholder=" Add Remarks" class="form-control"
                            name="remarks" rows="4" id="remarks"></textarea></div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label label-class">Marks Release Type</label>
                    <div class="col-md-9">
                         <label style="" for="marks_release_type_manual"><input onclick="show_hide_dates()" style="display: relative;" class="radio1" checked value="manual" type="radio" id="marks_release_type_manual" name="marks_release_type"> Manual</label>
                        <label style="" for="marks_release_type_auto"><input onclick="show_hide_dates()" style="display: relative;" class="radio1" value="auto" type="radio" id="marks_release_type_auto" name="marks_release_type">  Auto</label>
                    </div>
                </div>
                <div class="form-group" id="open_div" style="display: none;">
                    <label class="col-md-3 control-label label-class">Marks Entry Open Date <font color="red">*</font></label>
                    <div class="col-md-9">
                         <input type="text" id="open_date" name="open_date" class="form-control col-md-6">
                         <input type="time" id="open_date_time" name="open_date_time" class="form-control col-md-6">
                    </div>
                </div>
                <div class="form-group" id="close_div" style="display: none;">
                    <label class="col-md-3 control-label label-class">Marks Entry Cloas Date <font color="red">*</font></label>
                    <div class="col-md-9">
                         <input type="text" name="close_date" id="close_date" class="form-control col-md-6">
                         <input type="time" name="close_date_time" id="close_date_time" class="form-control col-md-6">
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="">
                <center>
                    <div class="">
                        <input style="width: 12vw;" type="button" value="Create" id="createAssesmentButton" class="btn btn-primary"
                            onclick="create_assessment()">
                    </div>
                </center>
            </div>
        </form>

    </div>
</div>


<style type="text/css">

    input.radio1 {
        height: 20px;
        width: 20px;
        margin: 10px 0 0 20px;
    }
#subjectGroup,
#enability {
    display: inline-block;
}

.label-class {

    width: 18%;

}

.publish {
    cursor: pointer;
}

ul.panel-controls>li>a {
    border-radius: 50%;
}

.widthadjust {
    width: 600px;
    margin: auto;
}
</style>

<script type="text/javascript">

    function show_hide_dates() {
        var selectedValue = $('input[name="marks_release_type"]:checked').val();
        if(selectedValue == 'manual') {
            $("#open_div, #close_div").hide();
        } else {
            $("#open_div, #close_div").show();
        }
    }

    $(document).ready(() => {
        

        $('#open_date, #close_date').datepicker({
            todayBtn: "linked",
            language: "it",
            autoclose: true,
            todayHighlight: true,
            format: 'dd-mm-yyyy',
            orientation: "top"
            // endDate: "today"
        });
    });

function create_assessemnt_class_modal() {
    $('#add_assessement_class_wise').trigger("reset");
    var class_name = $('#classId').find(":selected").text();
    $('#display_clasS_name').html(class_name);

}

function convertTo24HourFormat(timeString) { 
        const [time, period] = timeString.split(' '); 
        const [hour, minute] = time.split(':'); 
        let formattedHour = parseInt(hour); 
    
        if (period === 'PM') { 
            formattedHour += 12; 
        } 
    
        return `${formattedHour}:${minute}`; 
    } 

function create_assessment() {
    var classId = $('#classId').val();
    var $form = $('#add_assessement_class_wise');
    var selectedValue = $('input[name="marks_release_type"]:checked').val();

    var open_date_time= $("#open_date_time").val().toString();
    let time_open= '';
    if(open_date_time) {
        time_open= convertTo24HourFormat(open_date_time);
    }
    var close_date_time= $("#close_date_time").val().toString();
    let time_close= '';
    if(close_date_time) {
        time_close= convertTo24HourFormat(close_date_time);
    }
    // $("#create_hospital_visit_modal #open_date_time").parent().after(`<input id="time_str" name="time_str" value="${time}" type="hidden" />`);

    var valid= true;
    if(selectedValue == 'auto') {
        var open= $("#open_date").val();
        var close= $("#close_date").val();
        if(!open || !close) {
            valid= false;
        }
    }
    if ($form.parsley().validate() && valid) {
        var form = $('#add_assessement_class_wise')[0];
        var formData = new FormData(form);
        formData.append('class_id', classId);
        formData.append('time_open', time_open);
        formData.append('time_close', time_close);
        // $('#createAssesmentButton').val('Please wait ...').attr('disabled','disabled');
        $.ajax({
            url: '<?php echo site_url('examination/Assessments/submitAssessment') ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                switch (data.trim()) {
                    case -1:
                    case '-1':
                        $(function() {
                            new PNotify({
                                title: 'error',
                                text: `Adding new Assessment failed<br>Short Name or Long Name Already exist`,
                                type: 'error',
                            })
                        });
                        break;
                    case 1:
                    case '1':
                        new PNotify({
                            title: 'success',
                            text: 'Assessment added successfully',
                            type: 'success',
                        })
                        break;
                    default:
                        $(function() {
                            new PNotify({
                                title: 'error',
                                text: `Adding new Assessment failed<br>Something went wrong`,
                                type: 'error',
                            })
                        });
                        break;
                }
            },
            error: function(err) {
                console.log(err);
            },
            complete: function() {
                $('#show-assessment_uploader').modal('hide');
                $('#createAssesmentButton').val('Create').removeAttr('disabled');
                // getAssessments();
            }
        });
    } else {
        alert('Please enter all the necessary field');
    }


}

function addRemarksGroup() {
    if ($("#enable_subject_remarks").is(':checked')) {
        $("#subjectGroup").show();
        $("#remarks_group").prop('required', false);
    } else {
        $("#subjectGroup").hide();
        $("#remarks_group").prop('required', false);
    }
}

function checkEntry(e) {
    var k;
    document.all ? k = e.keyCode : k = e.which;

    return ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || (k >= 48 && k <= 57));
}










function getAssessments() {
    var site_url = "<?php echo site_url(); ?>";

    var classId = $('#classId').val();
    let selectedRadio = document.querySelector('input[name="assessment_type"]:checked');
    // console.log('selectedRadio', selectedRadio.value);
    // return;

    //Update Shortcuts
    $('#marks_entry_sc').attr("href", site_url + "/examination/assessment_marks_v2/index/" + classId);
    $('#derived_ass_sc').attr("href", site_url + "examination/Assessments/consolidationIndex/" + classId);
    $('#report_card_sc').attr("href", site_url + "/examination/assessment_marks/marksCards/" + classId);

    $.ajax({
        url: '<?php echo site_url('examination/Assessments/getAssessmentData'); ?>',
        data: {
            'classId': classId,
            'selectedRadio': selectedRadio.value
        },
        type: "post",
        success: function(data) {
            var pData = $.parseJSON(data);
            var assessments = pData.assessments;
            console.log('pData', pData);

            if(assessments.length > 0){

            var output = '<table class="table table-bordered">';

            output += '<thead>';
            output += '<th style="width:5%">#</th>';
            output += '<th style="width:10%">Short Name</th>';
            output += '<th style="width:10%">Long Name</th>';
            output += '<th stle="width:10%">Type</th>';
            output += '<th style="width:25%">Subjects Added</th>';
            output += '<th style="width:10%">Released For Marks Entry?</th>';
            output += '<th style="width:10%">Portions Published?</th>';
            output += '<th style="width:15%">Actions</th>';
            output += '</thead>';
            output += '<tbody>';

            if(assessments.length > 0) {
                for (i = 0; i < assessments.length; i++) {
                    var is_release= assessments[i].marks_release_type == 'manual' ? (assessments[i].release_marks == '1' ? 'Released' : 'Not Released') : (assessments[i].add_marks_enability == '1' ? 'Released' : 'Not Released');
                    output += '<tr>';
                    output += '<td>' + (i + 1) + '</td>';
                    output += '<td>' + assessments[i].short_name + '</td>';
                    output += '<td>' + assessments[i].long_name + '</td>';
                    output += `<td>${assessments[i].ass_type} (${assessments[i].generation_type})</td>`;
                    output += '<td>' + assessments[i].subAdded + '</td>';
                    output += '<td>' + is_release +
                        '</td>';
                    // output += '<td>' + (assessments[i].publish_status == 'Published' ? 'Published' :
                    //     'Not Published') + '</td>';
                    output += '<td>' + assessments[i].isPortionsPublished + '</td>';
                    site_url = '<?php echo site_url("examination/Assessments/showAssessmentsMorePage/") ?>' +
                        assessments[i].id + "/" + classId;

                        var disable= 'disabled';
                        var display= 'display: none;';
                        var superAdmin= '<?php echo $this->authorization->isSuperAdmin(); ?>';
                        if((assessments[i].release_marks ==0 && assessments[i].is_assessment_deletable == '1') && superAdmin == '1') {
                            disable= 'enabled';
                            display= '';
                        }
                    output += `<td>
                                <a class="btn btn-info" href="${site_url}">Details</a>
                                <button ${disable} class="btn btn-danger" style="${display }" onclick="onclick_delete_assessment('${assessments[i].long_name}', ${classId}, ${assessments[i].id}, '${assessments[i].subAdded}', '${site_url}')"><i class="fa fa-trash-o"></i></button>
                            </td>`;
                    output += '</tr>';
                }
            }else {
                output += '<tr>';
                output += '<td colspan="8" class="text-center">No Assessments Found</td>';
                output += '</tr>';
            }
            output += '</tbody>';
            output += '</table>';
            $('#assTable').html(output);
        }else{
            $('#assTable').html('<div class="no-data-display">No Assessments Found</div>');

        }
        }
    });
}

function onclick_delete_assessment(long_name, class_id, assessment_id, is_sub_added, subject_url) {
    if(is_sub_added == '' || is_sub_added == null || is_sub_added == undefined || is_sub_added == '-')
    bootbox.confirm({
        title: 'Confirm',
        message: `Are you sure you want to delete assessment- <strong>${long_name}</strong>?`,
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
            if(result) {
                $.ajax({
                    url: "<?php echo site_url('examination/assessments/delete_assessment_having_no_subjects');?>",
                    data: {
                        'class_id': class_id,
                        'assessment_id': assessment_id
                    },
                    type: 'post',
                    success: function(data) {
                        var data = JSON.parse(data);
                        console.log(data);
                        getAssessments();
                        if(data == 1 || data == true){
                            $(function(){
                                new PNotify({
                                    title:'Success',
                                    text: 'Successfully Deleted',
                                    type:'success',
                                });
                            });
                        }else if(data == 'not super admin'){
                            $(function(){
                                new PNotify({
                                    title:'Error',
                                    text: 'Only <b>SuperAdmin</b> can delete',
                                    type:'error',
                                });
                            });
                        } else {
                            $(function(){
                                new PNotify({
                                    title:'Error',
                                    text: 'Something went wrong',
                                    type:'error',
                                });
                            });
                        }
                    },
                    error: function(err) {
                        console.log(err);
                    }
                });
            }
        }
    }).find("div.modal-dialog").addClass("largeWidth");
    else {
        bootbox.confirm({
            title: `<span style="color: red;">Can't delete!</span>`,
            message: `For deleting <strong>${long_name}</strong> assessment, first <b>delete all the subjects</b> added to this class assessment...`,
            buttons: {
                confirm: {
                    label: 'Go to Subjects',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'Ok, Got it',
                    className: 'btn-success'
                }
            },
            callback: function (result) {
                if(result) {
                    window.location='<?php echo site_url('examination/Assessments/addSubjects/') ?>'+assessment_id+'/'+class_id;
                }
            }
            }).find("div.modal-dialog").addClass("largeWidth");

    }
}

$("#classSectionId").change(function() {
    $("#selectStd").submit();
});



function checkIsFirst(sub, assId, classId) {
    if (sub == '') {
        bootbox.confirm({
            title: "Info",
            message: "Edit assessment will be locked once you add subjects. Do you want to continue?",
            className: 'widthadjust',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    window.location = '<?php echo site_url('examination/assessments/addSubjects/') ?>' +
                        assId + '/' + classId;
                }
            }
        });
    } else {
        window.location = '<?php echo site_url('examination/assessments/addSubjects/') ?>' + assId + '/' + classId;
    }
}

$(document).ready(function() {
    getAssessments();

    $(document).on('click', '#publishBtn', function(e) {
        e.preventDefault();
        var uid = $(this).data('id'); // get id of clicked row
        var arr = uid.split("_");
        $("#publishId").val(arr[0] + '_' + arr[2]);
        $('#dynamic-content').html(''); // leave this div blank
        $('#modal-loader').show(); // load ajax loader on button click

        $.ajax({
                url: "<?php echo site_url('examination/assessments/publishAssessment');?>",
                data: {
                    'assId': arr[0],
                    'classId': arr[1]
                },
                type: 'post',
            }).done(function(data) {
                var data = JSON.parse(data);
                var empty = data.empty;
                var data = data.html;
                $('#dynamic-content').html(''); // blank before load.
                $('#dynamic-content').html(data); // load here
                $('#modal-loader').hide(); // hide loader
                if (empty == 1) $("#confirmP").hide();
            })
            .fail(function() {
                $('#dynamic-content').html('Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    });
});

function publish() {
    var ids = $("#publishId").val();
    var idStat = ids.split("_");
    $.ajax({
        url: "<?php echo site_url('examination/assessments/changePublishStatus');?>",
        data: {
            'id': idStat[0],
            'status': idStat[1]
        },
        type: 'post',
        success: function(data) {
            if (data == -1) {
                bootbox.alert({
                    title: "Publishing Assessment",
                    message: "Date or Portions not added to one or more subjects. Please add before continuing.",
                    buttons: {
                        ok: {
                            label: 'Got it',
                            className: 'btn-success'
                        }
                    }
                });
            } else if (data) {
                location.reload();
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function releaseMarks(changeTo, assId) {
    $msg = "You cannot make changes to subjects after releasing. Are you Sure?";
    if (changeTo == 0)
        $msg = "You are closing the marks entry. Are you Sure?";
    bootbox.confirm({
        title: 'Release Assessment Marks Entry',
        message: $msg,
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: "<?php echo site_url('examination/assessments/changeMarksReleaseStatus'); ?>",
                    data: {
                        'assId': assId,
                        'status': changeTo
                    },
                    type: 'post',
                    success: function(data) {
                        location.reload();
                    },
                    error: function(err) {
                        console.log(err);
                    }
                });
            }
        }
    });
}
</script>
<style>
    .largeWidth {
        width: 40%;
        margin: auto;
    }
</style>