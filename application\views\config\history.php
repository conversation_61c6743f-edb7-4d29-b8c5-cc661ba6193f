<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('Master_dashboard');?>">Master Dashboard</a></li>
	<li class="active">Config History</li>
</ul>

<hr>

<div class="col-md-12 col_new_padding">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-9 pl-0">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('Master_dashboard') ?>" class="control-primary">
						<span class="fa fa-arrow-left"></span>
						</a> 
						Config History
					</h3>
				</div>
			</div>
		</div>
		<div class="card-body pt-1">
			<div class="row" style="margin-left: 6px;">
				<div class="col-md-3">
					<label class="control-label">Date Range</label>
					<div id="reportrange" class="dtrange" style="width: 100%">
						<span></span>
						<input type="hidden" id="from_date">
						<input type="hidden" id="to_date">
					</div>
				</div>
				<div class="col-md-3">
					<label class="col-md-12" for="selectConfig" style="font-size: 14px;">Config</label>
					<div class="col-md-12">
						<select  class="form-control" id="selectConfig" name="selectConfig">
						<option value='all'>All</option>
						<?php
							foreach($config as $cs => $cl){ ?>
								<option value ="<?= $cl->id?>"><?= $cl->name ?></option>
							<?php } 
						?>
						</select>
					</div>
				</div>
				<div class="col-md-1 d-flex align-items-end">
					<button type="submit" id="get" class="btn btn-primary" style="border-radius: .45rem;" onclick="viewRoleHistory()" >Get Report</button>
				</div>
			</div>

			<div class="card-body mt-3" id="displayPanel" style="margin-bottom: 18px; display: none">
				<table class="table table-bordered" id="config-status-table">
					<thead>
						<tr>
							<th>No.</th>
							<th>Date</th>
							<th>Action Taken</th>
							<th>Action By</th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script>
	var startDate = moment().subtract(6, 'days');
	var endDate = moment();
	$("#reportrange").daterangepicker({
		ranges: {
			'Today': [moment(), moment()],
			'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
			'Last 7 Days': [moment().subtract(6, 'days'), moment()],
			'Last 30 Days': [moment().subtract(29, 'days'), moment()],
			'This Month': [moment().startOf('month'), moment().endOf('month')],
			'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
		},
		opens: 'right',
		buttonClasses: ['btn btn-default'],
		applyClass: 'btn-small btn-primary',
		cancelClass: 'btn-small',
		format: 'DD-MM-YYYY',
		separator: ' to ',
		startDate: startDate,
		endDate: endDate          
	},function(start, end) {
		$('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
		$('#from_date').val(start.format('DD-MM-YYYY'));
		$('#to_date').val(end.format('DD-MM-YYYY'));
	});
	

    $('#reportrange span').html(startDate.format('MMM D, YYYY') + ' - ' + endDate.format('MMM D, YYYY'));
    $('#from_date').val(startDate.format('DD-MM-YYYY'));
    $('#to_date').val(endDate.format('DD-MM-YYYY'));
	
	$(document).ready(function() {
        $('#selectConfig').select2();
    });

	function viewRoleHistory(){
		var from_date = $("#from_date").val();
        var to_date = $("#to_date").val();
		var config_id = $('#selectConfig').val()
		$.ajax({
			url: '<?php echo site_url('config/get_config_history') ?>',
			type: 'post',
			data: {
				'config_id': config_id, 'from_date': from_date, 'to_date': to_date
			},
			success: function(data) {
				data = $.parseJSON(data);
				if (data.length === 0) {
					const tableBody = document.querySelector('#config-status-table tbody');
					tableBody.innerHTML = '';
					const row = `<td colspan='4'>No Data</td>`;
					tableBody.insertAdjacentHTML('beforeend', row);
				} else {
					renderTable(data);
				}
				$("#displayPanel").css('display', 'block');
			}
		});
	}

	function formatDate(dateStr) {
		const date = new Date(dateStr);

		const day = String(date.getDate()).padStart(2, '0');
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const year = date.getFullYear();

		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		const seconds = String(date.getSeconds()).padStart(2, '0');

		const datePart = `${day}-${month}-${year}`;
		const timePart = `${hours}:${minutes}:${seconds}`;

		return { datePart, timePart };
	}

	function renderTable(data) {
		const tableBody = document.querySelector('#config-status-table tbody');
		tableBody.innerHTML = '';
		let i = 1;

		data.forEach(item => {
			const fullText = `'${item.name}' ${item.modified_status}`;
			const { datePart, timePart } = formatDate(item.modified_date_time);
			const row = `
				<tr>
					<td>${i}</td>
					<td style="min-width: 120px;">
						<div>${datePart}</div>
						<div>${timePart}</div>
					</td>
					<td class="action-cell" style="padding: 0;">
						<div style="
							max-height: 80px; 
							overflow: auto; 
							white-space: normal; 
							word-break: break-word; 
							padding: 5px;" 
							title="${fullText}">
							<b>${item.name}</b> ${item.modified_status}
						</div>
					</td>
					<td>${item.modified_by_name} (${item.modified_by_email_id})</td>
				</tr>
			`;
			tableBody.insertAdjacentHTML('beforeend', row);
			i++;
		});
	}
</script>