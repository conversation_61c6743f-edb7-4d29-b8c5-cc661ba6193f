<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/Payment_voucher_controller/payment_voucher_dashboard'); ?>">Payment Voucher Dashboard</a></li>
    <li>Manage Payment Vouchers</li>
</ul>

<div>
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/Payment_voucher_controller/payment_voucher_dashboard') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Manage Payment Vouchers
                    </h3>


                    <a href="<?php echo site_url("procurement/Payment_voucher_controller/add_payment_advice") ?>">
                        <div class="pull-right"
                            style="background: #676363;padding: 5px;border-radius: 9px;color: #fff;font-size: 1rem;padding-right: 1.4rem;cursor: pointer;height: 3rem;display: flex;justify-content: center;align-items: center;">
                            <div style="display: flex;justify-content: center;align-items: center;">
                                <svg style="height: 1.3rem;width: 3rem;" xmlns="http://www.w3.org/2000/svg" width="16"
                                    height="16" fill="currentColor" class="bi bi-plus-lg" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd"
                                        d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2" />
                                </svg>Create New
                            </div>
                        </div>
                    </a>


                </div>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-12" id="filter-div">
                <div class="col-md-3 form-group">
                    <label class="control-label">Date Range</label>
                    <div id="reportrange" class="dtrange" style="width: 100%; height: 30px;">
                        <span></span>
                        <input type="hidden" id="from_date">
                        <input type="hidden" id="to_date">
                    </div>
                </div>
                 <div class="form-group col-md-3">
                    <label for="vouchert_status">Status</label>
                    <div class="input-group">
                        <select name="vouchert_status" id="vouchert_status" class="form-control">
                            <option value="all">All</option>
                            <option value="Draft">Draft</option>
                            <option value="Unpaid">Unpaid</option>
                            <option value="Paid">Paid</option>
                            <option value="Rejected">Rejected</option>
                            <option value="Pending">Pending</option>
                            <option value="Aapproved">Aapproved</option>
                        </select>
                        <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    <label for="vendor_id" style="">Vendor</label>
                    <div class="input-group">
                        <select name="vendor_id" id="vendor_id" class="form-control">
                            <option value="all">All vendor</option>
                            <?php
                                if(!empty($vendors)) {
                                    foreach($vendors as $key => $val) {
                                        echo "<option value='$val->id'>$val->vendor_name</option>";
                                    }
                                }
                            ?>
                        </select>
                         <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                <div class="form-group col-md-2">
                    <label for="contrac-type" style="opacity: 0;">C</label>
                    <div class="input-group">
                        <button class="btn btn-dark col-md-12" id="get-report-btn" onclick="__get_payment_vouchers()">Get</button>
                    </div>
                </div>
            </div>

            <div class="col-md-12" style="height: 25px;"></div>

            <div class="col-md-12" id="table-div">      </div>
        </div>
    </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js'); ?>"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // __get_payment_vouchers();
    });

    // Date range picker
    $("#reportrange").daterangepicker({
        maxDate: moment(),
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment(),
        endDate: moment()
    }, function (start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));
// End date range picker

    function __get_payment_vouchers() {
        $("#get-report-btn").prop('disabled', true).html('Please Wait...');
         $("#table-div").html(`
         <center>
         <div class="" style="max-width: 90px; max-height: 90px   ;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin:auto;background:#fff;display:block;" width="50px" height="50px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
                <g><circle cx="73.801" cy="68.263" fill="#e15b64" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="0s"></animateTransform>
                </circle><circle cx="68.263" cy="73.801" fill="#f47e60" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.062s"></animateTransform>
                </circle><circle cx="61.481" cy="77.716" fill="#f8b26a" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.125s"></animateTransform>
                </circle><circle cx="53.916" cy="79.743" fill="#abbd81" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.187s"></animateTransform>
                </circle><circle cx="46.084" cy="79.743" fill="#849b87" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.25s"></animateTransform>
                </circle><circle cx="38.519" cy="77.716" fill="#6492ac" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.312s"></animateTransform>
                </circle><circle cx="31.737" cy="73.801" fill="#637cb5" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.375s"></animateTransform>
                </circle><circle cx="26.199" cy="68.263" fill="#6a63b6" r="3">
                <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.437s"></animateTransform>
                </circle><animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;0 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s"></animateTransform></g>
            </svg>
        </div>
        </center>


         `);
        let vouchert_status= $("#vouchert_status").val();
        let from_date= $("#from_date").val();
        let to_date= $("#to_date").val();
        let vendor_id= $("#vendor_id").val();
        $.ajax({
            url: '<?php echo site_url('procurement/Payment_voucher_controller/get_payment_vouchers'); ?>',
            type: 'post',
            data: {vouchert_status, from_date, to_date, vendor_id},
            success: function(data) {
                let vouchers= JSON.parse(data);
                console.log(vouchers);
                if(Object.keys(vouchers)?.length) {
                    __construct_vouchers_table(vouchers);
                } else {
                    $("#table-div").html(`<div class="no-data-display">No data</div>`);
                }
                $("#get-report-btn").prop('disabled', false).html('Get');
            }
        });
    }

    function __construct_vouchers_table(vouchers) {
        let html= ` <table class="table table-bordered" id="table-id">
                        <thead class="thead-dark">
                            <tr>
                                <th>Vendor</th>
                                <th>Voucher Number</th>
                                <th>Type</th>
                                <th>Date</th>
                                <th>Remarks</th>
                                <th>Net Payble</th>
                                <th>Voucher Status</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>`;
        for(var v of vouchers) {
            html += `<tr>
                        <td>${v.vendor_name}</td>
                        <td>${v.voucher_number}</td>
                        <td>${v.voucher_type}</td>
                        <td>${v.voucher_date}</td>
                        <td>${v.remarks}</td>
                        <td>${v.net_payable}</td>
                        <td>${v.voucher_status}</td>
                        <td>${v.staff}</td>
                        <td>
                            <a href="<?php echo site_url('procurement/Payment_voucher_controller/voucher_details/'); ?>${v.pa_id}" class="btn btn-outline-dark">View Details</a>
                        </td>
                    </tr>`;
        }
        html += `</tbody>
        </table>`;
         $("#table-div").html(html);

         __construct_data_table();
    }

    function __construct_data_table() {
        $('#table-id').DataTable( {
            "language": {
                    "search": "",
                    "searchPlaceholder": "Enter Search..."
                },
                "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                "pageLength": 10,
                dom: 'lBfrtip',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        text: 'Excel',
                        filename: 'visitor_not_checked_out_report',
                        className: 'btn btn-dark'
                    },
                    {
                        extend: 'print',
                        text: 'Print',
                        filename: 'visitor_not_checked_out_report',
                        className: 'btn btn-dark'
                    },
                    {
                        extend: 'pdfHtml5',
                        text: 'PDF',
                        filename: 'visitor_not_checked_out_report',
                        className: 'btn btn-dark'
                    }
                ]
            } );
    }
</script>



<style>
    .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>