<div class="modal fade" id="learning_objective" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Learning Objective</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="objective_session_id" id="objective_session_id">
                <div class="form-group">
                    <label for="objective_type" class="control-label">Type <font style="color: red;">*</font></label>
                    <select class="form-control" name="objective_type" id="objective_type">
                        <option value="">Select Objective</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 19%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <span id="objectiveTypeError" style="display: none;"></span>
                </div>
                <div class="form-group">
                    <label for="obj_type_desc" class="control-label">Description</label>
                    <textarea class="form-control" id="obj_type_desc" name="obj_type_desc" style="height: 11rem;" placeholder="Wanna Describe?"></textarea>
                    <span id="objectiveTypeDescError" style="display: none;"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_objectives_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_objectives_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="addObjectives()">Update</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // $("#learning_objective").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $.ajax({
        url: '<?php echo site_url("academics/Lesson_plan/get_lp_objectives"); ?>',
        type: "POST",
        data: {},
        success(data) {
            data = $.parseJSON(data);
            // console.log(data);
            let options = `<option value="">Select Objective</option>`
            data.forEach((item, i) => {
                options += `<option value="${item.id}">${item.objective_name}</option>`
            })
            $("#objective_type").html(options);
        }
    })

    $("#objective_type,#obj_type_desc").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#objectiveTypeError, #objectiveTypeDescError").hide();
            if($("#objective_type").val() == ""){
                $("#objectiveTypeError").html("Please Select Objective Type").css("color", "red").show();
                return false;
            }
            // if($("#obj_type_desc").val() == ""){
            //     $("#objectiveTypeDescError").html("Please Enter Objective Description").css("color", "red").show();
            //     return false;
            // }
            addLearningObjective();
            loadObjective();
        }
    })

    function addLearningObjective() {
        // const session_id = $("#objective_session_id").val();
        const learning_objective_description = $("#obj_type_desc").val();
        const objective_id = $("#objective_type").val();
        const visible_to_students = $("#visible_objectives_to_students").is(":checked") && 1 || 0;

        // console.log();


        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/add_new_learning_objective"); ?>',
            type: "POST",
            data: { session_id, learning_objective_description, objective_id, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#learning_objective").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        $("#objective_session_id").val("");
                        $("#objective_type").val("");
                        $("#obj_type_desc").val("");
                        getSessionData(session_id);
                        loadObjective();
                        showMainModal();
                    });
                } else {
                    $("#learning_objective").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Something went wrong!",
                    }).then(() => {
                        $("#learning_objective").modal('show');
                    });
                }
            },
            error(err){
                console.log(err);
                $("#learning_objective").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Something went wrong!",
                }).then(() => {
                    $("#learning_objective").modal('show');
                });
            }
        })
    }

    function addObjectives() {
        $("#objectiveTypeError, #objectiveTypeDescError").hide();
        if($("#objective_type").val() == ""){
            $("#objectiveTypeError").html("Please Select Objective Type").css("color", "red").show();
            return false;
        }
        // if($("#obj_type_desc").val() == ""){
        //     $("#objectiveTypeDescError").html("Please Enter Objective Description").css("color", "red").show();
        //     return false;
        // }
        addLearningObjective();
        loadObjective();
    }
</script>

<style>

</style>