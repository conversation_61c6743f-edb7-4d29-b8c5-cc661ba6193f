<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Assessments extends CI_Controller {
  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
    $this->load->model('examination/Assessment_model','assessment_model');
  }

  //Examination dashboard
  public function index(){
    $staffId = $this->authorization->getAvatarStakeHolderId();
    
    // $staff = $this->staffcache->getStaffCache();
    // $staffId = 0;
    // if(!empty($staffId)) $staffId = $staff->staffId;
    $staffId= $this->authorization->getAvatarStakeHolderId();
    $data['status'] = $this->assessment_model->getMarksEntryStatus($staffId);

    // echo '<pre>'; print_r($data['status']); die();

    $marks_entry_version = strtolower($this->settings->getSetting('marks_entry_version'));
    if($marks_entry_version == '') $marks_entry_version = 'v1';
    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Assessments',
        'sub_title' => 'Add/View Assessments',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'examination/Assessments/showAssessments',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CREATE')
      ],
      [
        'title' => 'Marks Entry',
        'sub_title' => ($data['status'] == 1)?'Marks entry is pending':'Marks Entry',
        'sub_title' => 'Marks Entry',
        'icon' => 'svg_icons/marksentry.svg',
        'url' => $site_url.'examination/assessment_marks',
        'permission' => ($marks_entry_version=='v1')
      ],
      [
        'title' => 'Marks Entry V2',
        'sub_title' => 'Marks Entry V2',
        'icon' => 'svg_icons/marksentry.svg',
        'url' => $site_url.'examination/assessment_marks_v2',
        'permission' => ($marks_entry_version=='v2')
      ],
      [
        'title' => 'Derived Assessments',
        'sub_title' => 'Create derived assessments',
        'icon' => 'svg_icons/derivedassessments.svg',
        'url' => $site_url.'examination/Assessments/consolidationIndex',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')
      ],
      [
        'title' => 'Report Card',
        'sub_title' => 'Generate report cards',
        'icon' => 'svg_icons/reportcardnew.svg',
        'url' => $site_url.'examination/assessment_marks/marksCards',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')
      ],
      [ 
        'title' => 'Subject Remarks',
        'sub_title' => 'Add Subject Remarks',
        'icon' => 'svg_icons/exam.svg',
        'url' => $site_url.'examination/assessment_marks/subject_remarks',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.SUBJECT_REMARKS')
      ],
      [ 
        'title' => 'Computed Fields (Super Admin)',
        'sub_title' => 'Create / Update Derived Fields',
        'icon' => 'svg_icons/derivedassessments.svg',
        'url' => $site_url.'examination/assessment_marks/derived_fields',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [ 
        'title' => 'Marks Entry Status',
        'sub_title' => 'Check marks entry status',
        'icon' => 'svg_icons/attendance.svg',
        'url' => $site_url.'examination/assessment_marks/marks_entry_status',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.VIEW_MARKS_STATUS')
      ],
      
      /*[
        'title' => 'Assessment wise Analysis',
        'sub_title' => 'Assessment wise Analysis',
        'icon' => 'svg_icons/sectionwiseanalysis.svg',
        'url' => $site_url.'examination/assessment_reports/summaryReport',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CLASS_REPORT')
      ],*/
      
      
      
      [
        'title' => 'Electives Report',
        'sub_title' => 'Electives Report',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_electives/report',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CLASS_REPORT')
      ]
      
      
      
      
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);






    $data['grade_level'] = array(
      [
        'title' => 'Section Wise Analysis',
        'sub_title' => 'Section Wise Analysis',
        'icon' => 'svg_icons/sectionwiseanalysis.svg',
        'url' => $site_url.'examination/assessment_reports/performanceAnalysis',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.PERFORMANCE_ANALYSIS')
      ],
      [
        'title' => 'Multi Year Result Analysis',
        'sub_title' => 'Grade Wise Result Analysis',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_reports/grade_wise_result_analysis',
        // need to authorize ?
        'permission' => $this->authorization->isAuthorized('EXAMINATION.RESULT_ANALYSIS_REPORT')
      ],
      [
        'title' => 'Assessment Wise Subject Analysis',
        'sub_title' => 'Assessment Wise Subject Analysis',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_reports/assessment_wise_subject_analysis_filter',
        // need to authorize ?
        'permission' => $this->authorization->isAuthorized('EXAMINATION.RESULT_ANALYSIS_REPORT')
      ]
    );
    $data['grade_level'] = checkTilePermissions($data['grade_level']);

    $data['student_level'] = array(
      [
        'title' => 'Assessment wise Analysis V2',
        'sub_title' => 'Assessment wise Analysis V2',
        'icon' => 'svg_icons/assessmentwiseanalysis.svg',
        'url' => $site_url.'examination/assessment_reports/assessment_wise_analysis',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CLASS_REPORT')
      ],
      [
        'title' => 'Student wise Analysis',
        'sub_title' => 'Student wise Analysis',
        'icon' => 'svg_icons/studentwiseanalysisdata.svg',
        'url' => $site_url.'examination/assessment_reports/studentWiseReprt',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.STUDENT_REPORT')
      ],
      [
        'title' => 'Ranking Report',
        'sub_title' => 'Ranking Report',
        'icon' => 'svg_icons/rankingreport.svg',
        'url' => $site_url.'examination/assessment_reports/rankingReport',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.RANKING_REPORT')
      ],
      [
        'title' => 'Student Wise Multi-Year Analysis',
        'sub_title' => 'Student Wise Multi-Year Analysis',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_reports/student_wise_multi_year_analysis_filter',
        // need to authorize ?
        'permission' => $this->authorization->isAuthorized('EXAMINATION.RESULT_ANALYSIS_REPORT')
      ]
    );
    $data['student_level'] = checkTilePermissions($data['student_level']);


    $data['subject_staff_level'] = array(
      [
        'title' => 'Subject-wise Result Analysis',
        'sub_title' => 'Subject Wise Result Analysis',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_reports/subject_wise_analysis',
        // need to authorize ?
        'permission' => $this->authorization->isAuthorized('EXAMINATION.RESULT_ANALYSIS_REPORT')
      ],
      [
        'title' => 'Staff Performance Index',
        'sub_title' => 'Staff Performance Index',
        'icon' => 'svg_icons/electivereports.svg',
        'url' => $site_url.'examination/assessment_reports/staff_performance_index',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.STAFF_PERFORMANCE_INDEX')
      ]
    );
    $data['subject_staff_level'] = checkTilePermissions($data['subject_staff_level']);


    $data['other_tiles'] = array(
      [
        'title' => 'Order Subjects',
        'sub_title' => 'Order Subjects',
        'icon' => 'svg_icons/ordersubjects.svg',
        // 'url' => $site_url.'examination/assessment_subjects/ordering',
        'url' => $site_url.'examination/assessment_subjects/sorting',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN')
      ],
      // [
      //   'title' => 'Manage Subjects',
      //   'sub_title' => 'Add/Edit Class wise subjects',
      //   'icon' => 'svg_icons/managesubjects.svg',
      //   'url' => $site_url.'examination/assessment_subjects',
      //   'permission' => $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN')
      // ],
      [
        'title' => 'Manage Subjects',
        'sub_title' => 'Add/Edit Class wise subjects',
        'icon' => 'svg_icons/managesubjects.svg',
        'url' => $site_url.'examination/assessment_subjects/manage_subjects',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN')
    ],
      [
        'title' => 'Manage Electives',
        'sub_title' => 'Add/Edit subject electives',
        'icon' => 'svg_icons/class.svg',
        'url' => $site_url.'examination/assessment_electives',
        'permission' => $this->authorization->isAuthorized('EXAMINATION.CREATE_ELECTIVES')
      ],
      [
        'title' => 'Grading System',
        'sub_title' => 'Add/Edit grading system',
        'icon' => 'svg_icons/createelectivegroups.svg',
        'url' => $site_url.'examination/assessment_grades',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Created Subject',
        'sub_title' => 'Add/Edit Subject',
        'icon' => 'svg_icons/freshentry.svg',
        'url' => $site_url.'examination/assessment_subjects/create_subjects',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Template',
        'sub_title' => 'Template Creation Requirement',
        'icon' => 'svg_icons/freshentry.svg',
        'url' => $site_url.'examination/assessment_marks/template_creation',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [ 
       'title' => 'Generate Marks & Averages',
       'sub_title' => 'Generate marks, grades and ranks',
       'icon' => 'svg_icons/reportcardnew.svg',
       'url' => $site_url.'examination/assessment_marks/generate_marks',
       'permission' => $this->authorization->isAuthorized('EXAMINATION.GENERATE_MARKS_AVERAGE')
     ],
     [ 
      'title' => 'Map Subjects Over the Exam-Subjects',
      'sub_title' => 'Map Subjects Over the Exam-Subjects',
      'icon' => 'svg_icons/reportcardnew.svg',
      'url' => $site_url.'examination/assessment_reports/map_subject_master_over_the_examination_subjects',
      'permission' => $this->authorization->isAuthorized('EXAMINATION.GENERATE_MARKS_AVERAGE')
    ]
    );
    $data['other_tiles'] = checkTilePermissions($data['other_tiles']);



// Under Development
    $data['under_development'] = array(
      [
        'title' => 'Assessment Wise Report Filter',
        'sub_title' => 'Assessment Wise Report Filter',
        'icon' => 'svg_icons/ordersubjects.svg',
        'url' => $site_url.'super_admin_folder/super_admin_controller/assessment_wise_report_filter',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['under_development'] = checkTilePermissions($data['under_development']);





    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/index_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/index';   	
    }

    

    // $this->load->helper('chatdata_helper');
    // $this->load->library('filemanager');
    // $data['chatData'] = getChatData('Examination');
    // $data['path_prefix'] = $this->filemanager->getFilePath('');
    $data['type'] = 'Examination';
    $data['back_url'] = site_url('examination/assessments/');    

    $this->load->view('inc/template', $data);
  }

  //Assessments List Page
  public function showAssessments($class_id=1){
    $class = $this->input->post('classId');
    if(!empty($class)) {
      $class_id = $class;
    }
    if($class == 0 && $class != '') {
      $class_id = $class;
    }
    $data['classSelected'] = $class_id;
    $data['classList'] = $this->assessment_model->getClassess();
    $data['remarksGroup'] = $this->assessment_model->getRemarksGroup();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/showAssessments_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/showAssessments_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/showAssessments';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function deleteAccess(){
    $assId = $_POST['assId'];
    $staffId = $_POST['staffId'];
    echo $this->assessment_model->deleteAccess($assId, $staffId);
  }

  //Assessments Detail Page
  public function showAssessmentsMorePage($assId, $class_id) {
    $data['classId'] = $class_id;
    $data['className'] = $this->assessment_model->getClassName($class_id)->className;
    $data['permitAccessControl'] = $this->authorization->isAuthorized('EXAMINATION.ACCESS_CONTROL');
    $data['permitReleaseToMarks'] = $this->authorization->isAuthorized('EXAMINATION.UNLOCK_MARKS_ENTRY');
    $data['permitPublishMarksToParents'] = $this->authorization->isAuthorized('EXAMINATION.PUBLISH_MARKS_TO_PARENT');

    $assObj = $this->assessment_model->getAssessmentName($assId);
    // echo "<pre>"; print_r($assObj); die();
    if(empty($assObj)) {
      redirect('examination/Assessments/showAssessments');
    }
    // $assObj->marksAdded = $this->assessment_model->checkOverallStatus($assId); // This is not used in the view page

    $assObj->subAdded = $this->assessment_model->getsubAdded($assId);
    if($assObj->subAdded == '')
      $assObj->showAccessControl = 0;
    else 
      $assObj->showAccessControl = 1;

    $data['assObj'] = $assObj;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/more_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/more_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/more';   	
    }

    $this->load->view('inc/template', $data);
  }

  //Ajax call
  public function getAssessmentData() {
    $classId = $_POST['classId'];
    // echo '<pre>'; print_r($_POST); die();
    $assessment_type = isset($_POST['assessment_type']) ? $_POST['assessment_type'] : 'auto';
    $show_derived = $this->authorization->isSuperAdmin(); //Always show derived assessments for Admin!
    // $data['assessments'] = $this->assessment_model->getAssessments_v2($classId, $show_derived);
    $data['assessments'] = $this->assessment_model->getAssessmentsGenerationTypeWise($classId, $assessment_type);
    echo json_encode($data);
  }

  //Ajax call
  public function getDerivedAssessmentData() {
    $classId = $_POST['classId'];
    $data['assessments'] = $this->assessment_model->getDerivedAssessments($classId);
    echo json_encode($data);
  }

  public function get_derived_entities_for_assessment() {
        $assessment_id = $_POST['assessment_id'];
        $data['entities']= $this->assessment_model->get_derived_entities_for_assessment($assessment_id);
        echo json_encode($data);
  }

  //Ajax call for each assessment
  public function getAssData() {
    $classId = $_POST['classId'];
    $data['assessments'] = $this->assessment_model->getAss($classId);
    echo json_encode($data);
  }

  private function _prepareAttendanceData($details) {

    $preresult = [];
    $day = [];

    foreach ($details as $k => $v) {


      $day[$v['day']] = $v['day'];

      $data = ['id' => $v['id'],
                    'attendance_session_id' => $v['attendance_session_id'],
                    'attendance_master_id' => $v['attendance_master_id'],
                    'attendance_master_group_id' => $v['attendance_master_group_id'],
                    'reference_type' => $v['reference_type'],
                    'reference_id' => $v['reference_id'],
                    'reference_status' => $v['reference_status'],
                    'status' => $v['status'],
                    'day' => $v['day']
                    ];

      $preresult[$v['student_admission_id']][$data['day']][] = $data;
    }


    foreach ($preresult as $key => $value) {

      $temp_arr = $day; 
      $only_keys = array_keys($value);

      foreach ($temp_arr as $key1 => $value1) {
        if(in_array($key1, $only_keys)) {
          unset($temp_arr[$key1]);
        }
      }

      if(!empty($temp_arr)) {
        foreach ($temp_arr as $key3 => $value3) {
          $preresult[$key][$key3] = [];
        }

        foreach ($day as $key8 => $value) {
          $new_arr[$key8] = $preresult[$key][$key8];
          unset($preresult[$key][$key8]);
        }

        $preresult[$key] = array_merge($new_arr, $preresult[$key]);
      }   

    }

    return $preresult;

  }

  public function getAttendance(){
    // $this->load->model('report/Student_attendance_model');
    $this->load->model('attendance/Attendance_model','attendanceModel');
    $this->load->helper('attendance_helper');
    $periods = $this->attendanceModel->getTimetablePeriodsByClass($_POST['classId'], $_POST['sectionId']);
    $attendance = $this->assessment_model->getAttendanceSessionID();
    $final = $this->_prepareAttendanceData($attendance);
    $data = array();
    foreach ($final as $stdId => $day) {
      foreach ($day as $date => $sessions) {
        $state = prepare_attendance_count($sessions, $periods);
        if(!array_key_exists($stdId, $data)) {
          $data[$stdId] = ['id'=>$stdId, 'count'=>0.0];
        }
        $att = $state['day']['status'] + $state['competation']['status'];
        if($att >= 1) {
          $data[$stdId]['count'] += 1;
        } else if($att == 0.5) {
          $data[$stdId]['count'] += 0.5;
        }
      }
    }
    $finalData = array();
    foreach ($data as $key => $value) {
      $finalData[] = $value;
    }
    echo json_encode($finalData);
    // echo "<pre>"; print_r($data); die();
  }

  //create new assessment
  public function createAssessment($class_id){
    $data['classSelected'] = $class_id;
    $data['classSections'] = $this->assessment_model->getClassSections($class_id);
    $data['remarksGroup'] = $this->assessment_model->getRemarksGroup();
    // $data['staffList'] = $this->assessment_model->getStaff();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/createAssessment_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/createAssessment_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/createAssessment';	
    }
    $this->load->view('inc/template', $data);
  }

  public function submitAssessment(){
    $class_id = $this->input->post('class_id');

    if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
      $status = (int)$this->assessment_model->addNewAssessment($class_id);
      if($status == -1)
        $this->session->set_flashdata('flashError', 'Assessment Name Already Exists.');
      else if($status)
        $this->session->set_flashdata('flashSuccess', 'Assessment Added Successfully.');
      else 
        $this->session->set_flashdata('flashError', 'Something Went Wrong.');
      redirect('examination/Assessments/showAssessments/'.$class_id);
    } else {
      echo $this->assessment_model->addNewAssessment($class_id);
    }
  }

  //loading edit assessment page
  public function editAssessment($assId, $class_id){
    $data['assId'] = $assId;
    $data['classId'] = $class_id;
    $data['classSelected'] = $class_id;
    $data['assessment'] = $this->assessment_model->getAssessmentById($assId);
    if(empty($data['assessment'])){
      redirect('examination/Assessments/showAssessments');
    }
    $data['remarksGroup'] = $this->assessment_model->getRemarksGroup();
    $sections = $this->assessment_model->getSectionsOfAssessment($assId);
    $data['sections'] = array();
    foreach ($sections as $key => $value) {
      array_push($data['sections'], $value->sectionId);
    }
    $data['classSections'] = $this->assessment_model->getClassSections($class_id);
    // echo "<pre>"; print_r($data['classSections']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/editAssessment_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/editAssessment_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/editAssessment';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function get_entities_for_assessment() {
    $assessment_id = $_POST['assessment_id'];
    $data['entities'] = $this->assessment_model->get_entities_for_assessment($assessment_id);
    echo json_encode($data);
  }
  public function getSectionData() {
    $classId = $_POST['classId'];
    $sections = $this->assessment_model->getSectionData($classId);
    echo json_encode($sections);
  }
  public function get_assessment_subjectData() {
    $assessment_id = $_POST['assessment_id'];
    $subjects = $this->assessment_model->getSubjectDatas($assessment_id);
    echo json_encode($subjects);
  }

  //update assessment data
  public function updateAssessment($assId, $class_id){
    $status = (int)$this->assessment_model->editAssessment($assId, $class_id);
    if($status == -1)
      $this->session->set_flashdata('flashInfo', 'Assessment Name Already Exists.');
    else if($status)
      $this->session->set_flashdata('flashSuccess', 'Assessment Updated Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect('examination/Assessments/showAssessments/'.$class_id);
  }

  //Add|Edit Subjects page
  public function addSubjects($assId, $class_id){
    $data['assId'] = $assId;
    $data['class_id'] = $class_id;
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    if(empty($data['assessment'])){
      redirect('examination/Assessments/showAssessments');
    }
    $data['assessments'] = $this->assessment_model->getAssessments($class_id);
    $data['groups'] = $this->assessment_model->getAddedGroups($assId, $class_id);
    $class = $this->assessment_model->getClassName($class_id);
    $data['className'] = $class->className;
    
    foreach ($data['groups'] as $key => $value) {
      $arr = $this->assessment_model->getCompo($value->id);
      $value->components = implode(", ", $arr['arrNames']);
      $value->compIds = $arr['arrIds'];
      $value->tMarks = $arr['tMarks'];
      $value->assEntIds = $arr['assEntIds'];
    }
    //echo "<pre>"; print_r($data['groups']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/subjects/assessmentSubjects_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/subjects/assessmentSubjects_mobile';
    }else{
      $data['main_content'] = 'examination/subjects/assessmentSubjects';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function generateClassAverage() {
    $input = $this->input->post();
    $status = $this->assessment_model->generateAverage($input['class_id'], $input['ass_id'], explode(",", $input['ass_entity_ids']));
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Generated Successfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Generation of Average Failed!');
    }
    redirect('examination/assessments/addSubjects/'.$input['ass_id'].'/'.$input['class_id']);
  }

  public function getSubComponents(){
    echo json_encode($this->assessment_model->getComponents($_POST['subId']));
  }

  public function addNewSubjects($assId, $classId){
    if(!isset($classId) || $classId <= 0 || $classId == '') {
      $this->session->set_flashdata('flashError', 'Something went wrong, Go  to Manage Subject and try again.');
      redirect('examination/assessments');
    }
    if(!isset($assId) || $assId <= 0 || $assId == '') {
      $this->session->set_flashdata('flashError', 'Something went wrong, Go  to Manage Subject and try again.');
      redirect('examination/assessments');
    }
    $data['assId'] = $assId;
    $data['class_id'] = $classId;
    $data['subjects'] = $this->assessment_model->getClassSubjects($assId, $classId);
    $data['groups'] = $this->assessment_model->getSubGroups($assId, $classId);
    // echo "<pre>"; print_r($data['groups']);die();
    // $data['entities'] = $this->assessment_model->getAssEntities($assId);
    
    $class = $this->assessment_model->getClassName($classId);
    if(empty($class)) {
      $this->session->set_flashdata('flashError', 'Something went wrong, Go  to Manage Subject and try again.');
      redirect('examination/assessments');
    }
    if(!isset($class->className)) {
      $this->session->set_flashdata('flashError', 'Something went wrong, Go  to Manage Subject and try again.');
      redirect('examination/assessments');
    }
    $data['className'] = $class->className;
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    if(empty($data['assessment'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong, Go  to Manage Subject and try again.');
      redirect('examination/assessments');
    }
    $data['main_content'] = 'examination/subjects/addAssessmentSubjects';
    $this->load->view('inc/template', $data);
  }

  public function addAssessmentSubject($assId, $class_id){
    // echo "<pre>"; print_r($this->input->post());die();
    if($this->input->post('total_marks') === null) {
      $this->session->set_flashdata('flashError', 'Component not added.');
      redirect('examination/Assessments/addNewSubjects/'.$assId.'/'.$class_id);
    }
    $data['assId'] = $assId;
    $data['class_id'] = $class_id;
    $status = (int)$this->assessment_model->addAssessmentSubject($assId);
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Subject Added Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect('examination/Assessments/addSubjects/'.$assId.'/'.$class_id);
  }

  public function editSubjects($subGid, $assEGid, $assId, $classId){
    $allEntities = $this->assessment_model->getAllEntities($assEGid, $classId);
    $data['added'] = $this->assessment_model->getAddedEntities($subGid);
    $data['remaining'] = $allEntities;
    foreach ($allEntities as $k => $v) {
      foreach ($data['added'] as $key => $value) {
        if($v->id == $value->id) {
          unset($data['remaining'][$k]);
        }
      }
    }
    $data['assId'] = $assId;
    $data['class_id'] = $classId;
    $class = $this->assessment_model->getClassName($classId);
    $data['className'] = $class->className;
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    $data['subject'] = $this->assessment_model->getSubjectData($subGid);
    $data['entities'] = $this->assessment_model->getEntitiesData($subGid);
    foreach ($data['entities'] as $key => $value) {
      $value->marksAdded = $this->assessment_model->checkMarks($value->id);
    }
    $data['main_content'] = 'examination/subjects/editAssessmentSubjects';
    $this->load->view('inc/template', $data);
  }

  public function submitPortions(){
    $status = $this->assessment_model->addPortions();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Portions Added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessments/showAssessmentsMorePage/'.$_POST['assessment'].'/'.$_POST['classId']);
  }

  public function UpdateAssessmentSubject($assId, $classId){
    // echo "<pre>"; print_r($this->input->post());die();
    $data['assId'] = $assId;
    $data['class_id'] = $classId;
    $status = (int)$this->assessment_model->updateAssessmentSubject($assId);
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Subject Updated Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect('examination/Assessments/addSubjects/'.$assId.'/'.$classId);
  }

  public function deleteSubjects(){
    $eId = $_POST['eId'];
    $compId = $_POST['compId'];
    // echo $eId."<br>".$aGid."<br>".$compId;die();
    echo $this->assessment_model->deleteSubjects($eId, $compId);
  }


  /* Assessment Portions publish */
  public function publishAssessment(){
    $assId = $_POST['assId'];
    $classId = $_POST['classId'];
    $assessment = $this->assessment_model->getAssessmentName($assId);
    $groups = $this->assessment_model->getGroupNames($classId);
    $entities = $this->assessment_model->getEntityNames($classId);
    $result = $this->assessment_model->getAssessmentTT($assId);
    // echo "<pre>"; print_r($result);die();
    $empty = 0;
    if(empty($result)){
      $htmlStr = 'Date or Portions not added to one or more subjects. Please add before continuing.';
      $empty = 1;
    }
    else {
      $empty = 0;
      if($assessment->publish_status == 'Published'){
        $htmlStr = 'Do you want to unpublish?';
      } else {
        $htmlStr = '<h5 style="border-bottom: 1px solid #ccc;"><strong>' . $assessment->long_name . '- Timetable and portions</strong></h5>';
        $gIdArr = array();
        foreach ($result as $key => $sub) {
          $date = 'No Date';
          $day = '';
          if($sub->portions_at == 0){
            if(($sub->gDate) != NULL) { 
              $date = date('d-M-Y', strtotime($sub->gDate));
              $day = strtoupper(date('l', strtotime($sub->gDate)));
            }
            if(in_array($sub->assEGid, $gIdArr)) {
              continue;
            }
            array_push($gIdArr, $sub->assEGid);
            $portion = $sub->gPortions;
            if($sub->gPortions == '') $portion = 'Portions not added';
            $htmlStr .= "<p><strong>". $date .' ('.$day.') - '.$groups[$sub->assEGid]. "</strong></p><p>". $portion ."</p><hr>";
          } else {
             $portion = $sub->portions;
            if($sub->portions == '') $portion = 'Portions not added';
            if(($sub->date) != NULL) { 
              $date = date('d-M-Y', strtotime($sub->date));
              $day = strtoupper(date('l', strtotime($sub->date)));
            }
            $htmlStr .= "<p><strong>". $date .' ('.$day.') - '.$entities[$sub->entity_id]."</strong></p><p>". $sub->portions ."</p><hr>";
          }
        }
      }
    }

    print_r (json_encode(array('html'=>$htmlStr, 'empty'=>$empty)));
  }

  public function changePublishStatus(){
    $assId = $_POST['id'];
    $status = $_POST['status'];
    echo $this->assessment_model->changeStatus($assId, $status);
  }

  public function changeParentMarksPublishStatus($assId, $classId, $status){
   $status = $this->assessment_model->changeParentMarksPublishStatus($assId, $status);
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Status updated Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect("examination/Assessments/showAssessmentsMorePage/$assId/$classId");
  }

  public function getPortions($assId, $classId){
    $data['assId'] = $assId;
    $data['classId'] = $classId;
    $portion = $this->assessment_model->getPortion($assId);
    $str = '';
    $publish = 0;
    if(empty($portion)) {
      $groups = $this->assessment_model->getGroupNames($classId);
      $entities = $this->assessment_model->getEntityNames($classId);
      $assData = $this->assessment_model->getAssessmentName($assId);
      $assessments = $this->assessment_model->getAssessmentTT($assId);
      $dates = array();
      $portions = array();
      foreach ($assessments as $key => $value) {
        if($value->portions_at == 0){
          $portions['group_'.$value->assEGid] = array('portions' => $value->gPortions, 'date' => $value->gDate, 'start_time' => $value->gStart, 'end_time' => $value->gEnd);
        } else {
          $portions['entity_'.$value->entity_id] = array('portions' => $value->gPortions, 'date' => $value->gDate, 'start_time' => $value->gStart, 'end_time' => $value->gEnd);
        }
      }

      foreach ($entities as $id => $name) {
        if(array_key_exists('entity_'.$id, $portions)) {
          $portions['entity_'.$id]['subName'] = $name;
        }
      }
      foreach ($groups as $id => $name) {
        if(array_key_exists('group_'.$id, $portions)) {
          $portions['group_'.$id]['subName'] = $name;
        }
      }

      usort($portions, function($a, $b) { return strtotime($a['date']) - strtotime($b['date']); });

      $str .= '<h2>'. $assData->long_name.'</h2><br>';
      foreach ($portions as $key => $value) {
        $date = ($value['date'] != '')?date('d-m-Y', strtotime($value['date'])):'';
        $time = ($value['start_time'] != '')?(date('h:i a', strtotime($value['start_time'])).' to '.date('h:i a', strtotime($value['end_time']))):'';
        $str .= '<b>Subject : '.$value['subName'].'</b><br>';
        $str .= '<b>Date : '.$date.' '.$time.' </b><br>';
        $str .= '<b>Portions : </b>'.$value['portions'].'<br><hr>';
      }
    } else {
      $str .= $portion->portions;
      $publish = $portion->publish_status;
    }

    $data['portion'] = $str;
    $data['publish_status'] = $publish;

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/add_portions_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/add_portions_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/add_portions';    	
    }

    
    $this->load->view('inc/template', $data);
    
    // echo json_encode(array('portion' => $str, 'publish' => $publish));
  }

  /*Access control functions */
  public function addPermissions($assId, $class_id, $staffType=-1){
    $data['assId'] = $assId;
    $data['class_id'] = $class_id;
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    if(empty($data['assessment'])){
      redirect('examination/Assessments/showAssessments');
    }
    $data['staffList'] = $this->assessment_model->getStaffWithNoPermission($assId, $staffType);

    $data["assId"]=$assId;
    $data["class_id"]=$class_id;
    $data["staffType"] = $staffType;

    $data["allStaffTypes"]=$this->settings->getSetting('staff_type');

    $data['sections'] = $this->assessment_model->getSectionsByClass($class_id);
    // $data['sections'] = $this->assessment_model->getAssessmentSections($assId,$class_id);
    // $data['subjectList'] = $this->assessment_model->getSubjectSections($assId);
    $data['subjectList'] = $this->assessment_model->getSectionSubject($assId);
    $data['permissionsAdded'] = $this->assessment_model->getPermissionsAdded($assId);
    $data['clsAssessments'] = $this->assessment_model->getAssessments($class_id);
    $class = $this->assessment_model->getClassName($class_id);
    $data['className'] = $class->className;
    // echo "<pre>"; print_r($data['subjectList']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/assessment/acces_controls_redirect_msg';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/assessment/acces_controls_mobile';
    }else{
      $data['main_content'] = 'examination/assessment/acces_controls';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function cloneClassAccess(){
    $currentId = $_POST['currentId'];
    $copyId = $_POST['copyId'];
    echo $this->assessment_model->cloneComplete($currentId, $copyId);
  }

  public function submitAccess($class_id){
    $assId = $this->input->post('assId');
    $status = (int) $this->assessment_model->submitAccessControl();
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Permissions Added Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect('examination/Assessments/addPermissions/'.$assId.'/'.$class_id);
  }

  public function updateAccess($class_id){
    $assId = $this->input->post('assId');
    $status = (int) $this->assessment_model->updateAccessControl();
    if($status)
      $this->session->set_flashdata('flashSuccess', 'Permissions Updated Successfully.');
    else 
      $this->session->set_flashdata('flashError', 'Something Went Wrong.');
    redirect('examination/Assessments/addPermissions/'.$assId.'/'.$class_id);
  }


  /* Release for marks entry */
  public function changeMarksReleaseStatus(){
    $assId = $_POST['assId'];
    $changeTo = $_POST['status'];
    $this->db->where('id', $assId);
    $status = $this->db->update('assessments', array('release_marks'=> $changeTo));
    echo $status;
  }



  /* Derived Assessments */

  public function consolidationIndex($classId = 1){
    $data['classSelected'] = $classId;
    $data['classList'] = $this->assessment_model->getClassess();
    $data['assessments'] = $this->assessment_model->getConsolAss($classId);
    // echo '<pre>';print_r($data['assessments']);die();
    foreach ($data['assessments'] as $key => $value) {
      // $class = $this->assessment_model->getClassDetails($value->id);
      $sections = $this->assessment_model->getSectionsOfAssessment($value->id);
      // echo "<pre>";print_r($sections);die();
      $value->sections = $sections;
      $value->className = $sections[0]->className;
      $value->classId = $sections[0]->classId;
      $value->derived_formula = json_decode($value->formula)->merg_algorithm->name;
      $value->marks_generated = $this->assessment_model->isMarksGenerated($value->id);
      // echo '<pre>';print_r($obj);
    }
    // echo "<pre>";print_r($data['assessments']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/consolidation/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/consolidation/index_mobile';
    }else{
      $data['main_content'] = 'examination/consolidation/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function deleteDerivedAssessment() {
    $assessment_id = $_POST['assessment_id'];
    echo $this->assessment_model->deleteDerivedAssessment($assessment_id);
  }

  public function createConsolidation($classId){
    $data['classId'] = $classId;
    $data['assessments'] = $this->assessment_model->getClsAssessments($classId);
    // echo "<pre>"; print_r($data['assessments']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/consolidation/create_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/consolidation/create_mobile';
    }else{
      $data['main_content'] = 'examination/consolidation/create'; 	
    }

    
    $this->load->view('inc/template', $data);
  }

  public function getDerivedMarks(){
    $data = $this->assessment_model->getDerivedMarks($_POST['assId'], $_POST['sectionId'], $_POST['entityId'], $_POST['assEid']);
    echo json_encode($data);
  }

  public function createConsolidatedAssessment(){
    $classId = $this->input->post('con_class');
    // $status = $this->assessment_model->addConsolidatedAssessment();
    $status = $this->assessment_model->saveConsolidatedAssessment();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/Assessments/consolidationIndex/'.$classId);
  }

  public function getSubjectUnion(){
    $assessments = $this->assessment_model->getAssDetails($_POST['assessment']);
    $subjects = $this->assessment_model->getSubjectsUnion($_POST['assessment']);
    echo json_encode(array('assessments' => $assessments, 'subjects'=>$subjects));
  }

  public function getClassWiseAssessment(){
    echo json_encode($this->assessment_model->getClsAssessments($_POST['classId']));
  }

  public function getSectionWiseAssessment(){
    echo json_encode($this->assessment_model->getSecAssessments($_POST['sectionId']));
  }

  public function generateMarks($assId, $classId, $sectionId){
    $data['sections'] = $this->assessment_model->getSectionsOfAssessment($assId);
    $data['classId'] = $classId;
    // $data['assessments'] = $this->assessment_model->getAssessmentName($assId);
    $data['assessments'] = $this->assessment_model->getAssessmentName($assId);
    $assIds = json_decode($data['assessments']->formula);
    $assArr = array();
    $assNames = array();
    foreach ($assIds->assessments as $key => $value) {
      array_push($assArr, $value->id);
      array_push($assNames, $value->name);
    }

    $data['assNames'] = implode(",", $assNames);
    $data['assIds'] = $assArr;
    
    // $data['subjects'] = $this->assessment_model->getSubjectsUnion($assArr);
    $data['subjects'] = $this->assessment_model->getDerivedAssessmentSubjects($assId);
    // echo '<pre>';print_r($data['subjects']);die();
    foreach ($data['subjects'] as $key => $value) {
      $value->status = $this->assessment_model->getGenStatus($value->id, $data['assessments']->id, $sectionId);
    }
    // echo "<pre>"; print_r($data['subjects']);die();
    $data['sectionId'] = $sectionId;
    $data['csName'] = $this->assessment_model->getSectionName($sectionId);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/consolidation/generate_marks_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/consolidation/generate_marks_mobile';
    }else{
      $data['main_content'] = 'examination/consolidation/generate_marks';
    }
    // echo '<pre>';print_r($data);die();
    $this->load->view('inc/template', $data);
  }

  public function generateConsolidation(){
    // $status = (int)$this->assessment_model->generateConsolidation();
    $status = $this->assessment_model->generatederivedMarks();
    echo json_encode($status);
  }

  public function get_student_derived_assessments_marks() {
    $status = $this->assessment_model->get_student_derived_assessments_marks($_POST['section_id'], $_POST['assessment_id'], $_POST['entity_id']);
    echo json_encode($status);
  }

  public function get_student_marks() {
    $res = $this->assessment_model->get_student_marks($_POST['section_id'], $_POST['assessment_id'], $_POST['entity_id'], $_POST['class_id']);
    // echo '<pre>';print_r($res);die();
    echo json_encode($res);
  }

  public function editDerivedAssessment($assId){
    $data['assId'] = $assId;
    $derivedData = $this->assessment_model->getAssessmentName($assId);
    $json = json_decode($derivedData->formula);
    $assIds = array($assId);
    $data['assessments'] = $json->assessments;
    $data['formula'] = ($json->merg_algorithm)->name;
    $data['entities'] = $json->merg_values;
    $data['assNames'] = '';
    foreach ($json->assessments as $key => $value) {
      $data['assNames'] .= $value->name.', ';
      array_push($assIds, $value->id);
    }
    $subjects = $this->assessment_model->getSubjectsUnion($assIds);
    $entityArr = array();

    foreach ($data['entities'] as $key => $value) {
      $entityArr[$value->entity_id] = array();
      foreach ($value->value_set as $key => $valSet) {
        $entityArr[$value->entity_id]['ass'][$valSet->assId] = $valSet->value;
      }
      unset($value->value_set);
    }

    foreach ($subjects as $k => $val) {
      if(array_key_exists($val->id, $entityArr)) {
        $entityArr[$val->id]['entName'] = $val->name;
      } else {
        $entityArr[$val->id] = array();
        $entityArr[$val->id]['entName'] = $val->name;
        $entityArr[$val->id]['ass'] = array();
      }
    }
    $data['entities'] = $entityArr;
    // echo "<pre>"; print_r($data['entities']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/consolidation/edit_derived_assessment_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/consolidation/edit_derived_assessment_mobile';
    }else{
      $data['main_content'] = 'examination/consolidation/edit_derived_assessment';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function updateConsolidatedAssessment(){
    $status = $this->assessment_model->updateConsolidatedAssessmentNew();
    // $status = $this->assessment_model->updateConsolidatedAssessment();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/Assessments/consolidationIndex');
  }

  public function getCloningSubjects() {
    $clone_from = $_POST['clone_from'];
    $subjects = $this->assessment_model->getCloningSubjects($clone_from);
    echo json_encode($subjects);
  }

  public function addClonedSubjects() {
    $input = $this->input->post();
    $status = $this->assessment_model->saveClonedSubjects();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Subjects cloned successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/Assessments/addSubjects/'.$input['assessment_id'].'/'.$input['class_id']);
  }

  public function hallticket($assessment_id, $classId) {
    $data['assessment_id'] = $assessment_id;
    $data['classId'] = $classId;
    $data['hallticket'] = $this->assessment_model->getHallticketId($assessment_id);
    $data['main_content'] = 'examination/hallticket/template';
    $this->load->view('inc/template', $data);
  }

  public function saveHallticket() {
    $input = $this->input->post();
    $status = $this->assessment_model->saveHallticket();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Template added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/Assessments/showAssessmentsMorePage/'.$input['assessment_id'].'/'.$input['class_id']);
  }

  public function halltickets($assessment_id, $classId) {
    $data['assessment_id'] = $assessment_id;
    $data['classId'] = $classId;
    $data['sections'] = $this->assessment_model->getClassSections($classId);
    $data['hallticket'] = $this->assessment_model->getHallticketId($assessment_id);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/hallticket/print_halltickets_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/hallticket/print_halltickets_mobile';
    }else{
      $data['main_content'] = 'examination/hallticket/print_halltickets';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function showHalltickets() {
    $input = $this->input->post();
    $data['assessment_id'] = $input['assessment_id'];
    $data['classId'] = $input['class_id'];
    if(!isset($input['assessment_id']) || !isset($input['class_id']) || !isset($input['section_id']) || !isset($input['hallticket_id'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please try again!');
      redirect('examination/assessments/index');
    }
    $data['students'] = $this->assessment_model->getSectionStudents($input['section_id']);
    $data['class_teacher'] = $this->assessment_model->getClassTeacherDetails($input['section_id']);
   
    if(empty($data['students'])) {
      $this->session->set_flashdata('flashError', 'Students are not their in this section, try again with another section.');
      if(isset($input['assessment_id']) && isset($input['class_id'])) {
        redirect('examination/Assessments/halltickets/'. $input['assessment_id']. '/'. $input['class_id']);
      } else {
        redirect('examination/assessments/index');
      }
    }
    $data['assessment'] = $this->assessment_model->getAssessmentName($input['assessment_id']);
    $data['template'] = $this->assessment_model->getHallticketTemplate($input['hallticket_id']);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/hallticket/halltickets_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/hallticket/halltickets_mobile';
    }else{
      $data['main_content'] = 'examination/hallticket/halltickets';     	
    }
    $this->load->view('inc/template', $data);
  }

  public function viewMarksData(){
    $ass_entity_gid=$_POST["ass_entity_gid"];
    $result=$this->assessment_model->viewMarksData($ass_entity_gid);
    echo json_encode($result);
  }

  public function delete_assessment_having_no_subjects() {
    if($this->authorization->isSuperAdmin()){
      echo $this->assessment_model->delete_assessment_having_no_subjects($_POST["class_id"], $_POST["assessment_id"]);
    } else {
      echo json_encode('not super admin');
    }
  }

  public function get_subjects_assessment_wise(){
    $ass_id=$_POST["ass_id"];
    $class_id=$_POST["class_id"];
    $result=$this->assessment_model->get_subjects_assessment_wise($ass_id, $class_id);
    $computed= $this->assessment_model->get_computed_class_wise($class_id, $ass_id);
    echo json_encode(array('result' => $result, 'computed' => $computed));
  }

  public function changeParentMarksPublishStatusSubjectWise(){
    $ass_id = $_POST['ass_id'];
    $class_id = $_POST['class_id'];
    $sub_ids_arr_status0 = isset($_POST['sub_ids_arr_status0']) ? $_POST['sub_ids_arr_status0'] : [];
    $sub_ids_arr_status1 = isset($_POST['sub_ids_arr_status1']) ? $_POST['sub_ids_arr_status1'] : [];

    $computed_ids_arr_status0 = isset($_POST['computed_ids_arr_status0']) ? $_POST['computed_ids_arr_status0'] : [];
    $computed_ids_arr_status1 = isset($_POST['computed_ids_arr_status1']) ? $_POST['computed_ids_arr_status1'] : [];

    $change_mark_status = $_POST['change_mark_status'];
    $status = $this->assessment_model->changeParentMarksPublishStatusSubjectWise($ass_id, $class_id, $sub_ids_arr_status0, $sub_ids_arr_status1, $change_mark_status, $computed_ids_arr_status0, $computed_ids_arr_status1);

    echo $status;
    
  }

  public function get_subjects_ass_wise(){
    $subjects = $this->assessment_model->get_subjects_ass_wise($_POST['ass_id']);
    echo json_encode($subjects);
  }

  public function edit_derived_assessment_formula() {
    $subjects = $this->assessment_model->edit_derived_assessment_formula();
    echo $subjects;
  }

  public function update_time_boundation() {
    $status = $this->assessment_model->update_time_boundation();
    echo $status;
  }

  public function edit_display_name() {
    $status = $this->assessment_model->edit_display_name();
    echo $status;
  }

  public function edit_rounding_parameter() {
    $status = $this->assessment_model->edit_rounding_parameter();
    echo $status;
  }

}
?>