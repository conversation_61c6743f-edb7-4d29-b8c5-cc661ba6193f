<div class="modal" tabindex="-1" role="dialog" id="view_learning_context">
    <div class="modal-dialog" role="document" id="">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Learning Context</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_learning_context_data">
                <textarea class="data-here form-control" id="l_context" name="l_context" style="height: 11rem;" placeholder="wanna describe ?" disabled></textarea>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('add_learning_context', 'view_learning_context')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Edit</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $("#view_learning_context").on("shown.bs.modal", e => {
        const dataSet = e.relatedTarget.dataset;
        const content = dataSet.value;
        $(".data-here").val(content);
    })

    function loadLearningContext() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ session_details } = data);

                // console.log(session_details);

                $(".data-here").val(session_details.learning_context);
            }
        })
    }
</script>