<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller');?>"><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></li>
  <li>Mass Students</li>
</ul>

<form enctype="multipart/form-data" id="addChronical" class="form-horizontal" data-parsley-validate method="post">
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Mass Students
          </h3>

          <div class="col-md-3">
                <div class="form-group">
                  <div class="input-group date" id="datePicker"> 
                    <input type="text" class="form-control" id="date" name="date" placeholder="Select Date" required="" value="<?php echo date('d-m-Y'); ?>" >
                    <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
                  </div> 
                </div>
              </div>

        </div>
      </div>
    </div>
    <div class="modal-body">
      <div class="col-md-12">

        <table class="table table-bordered">
          <tbody>
            <tr>
            <td>
              <div class="mt-2" id="mul_stu_type">
               <button name="student_id" id="chronicalStudentId" style="font-weight: bold;" data-toggle="modal" data-target="#contact" type="button" class="btn btn-outline-secondary">Add Students &nbsp;&nbsp;<i class="fa fa-group"></i></button>
               <span class="student-box" id="mulitple_student_selection" name="mulitple_student_selection[]"></span>
              </div>
            </td>
            </tr>
            <tr>
            <td>
              <button type="button" id="copy-from" class="btn btn-primary" style="float:right;">Clone From...</button>

              <div class="col-md-4" style="float: right;">
            <select class="form-control input-md" id="template_id" onchange="applyTemplate()" name="template_id">
              <?php foreach ($templates as $template) {
                  echo '<option value="' . $template['name'] .'">' . $template['name'] . '</option>';
                } ?>
            </select>
          </div>
            </td>
            </tr>
          </tbody>
        </table>

        <div class="form-group">
          <textarea name="chronicle_content" id="chronicle_content" data-parsley-required-message="Please enter content" placeholder="Body" class="summernote form-control" style="height: 100px;"></textarea><br>
        </div>

        <div class="form-group">
          <div class="col-md-12">
            <input type="hidden" name="clone_atthaced_file" id="clone_atthaced_file">
            <div id="cloneAttachedView"></div>
              <input type="file" class="form-control verify-size" data-parsley-required-message="Please select a document to upload" name="certificate_name" id="cerificate_img_Id" type="file" accept="application/pdf">
              <span class="help-block">Allowed file types - pdf; Allowed size - upto 5Mb</span>
              <span id="file-size-error" style="color: red;"></span>
              <span id="resource-file_error" style="color: red;"></span>
              <p id="error1" style="color:red;"></p>
            </div>
          </div>
        </div>

        <div class='card-footer panel_footer_new mb-2'>
            <center>
              <button type="button" onclick="save_chronicles()" id="confirmBtn"  class="btn btn-success">Send <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></button>
              <a class="btn btn-danger" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">Back</a>
            </center>
        </div>   

    </div>
  </div>
</div>
</form>



<div class="col-md-12 form-horizontal">
    <div id="contact" class="modal fade" role="dialog">
        <div class="modal-dialog" style="width: 60%;margin:auto;">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                  <h4 class="modal-title">Select the Students</h4>
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;max-height:500px;">

                  <div id="individual">
                    <div class="form-group">
                      <div class="col-md-4">
                        <!-- <label for="class" class="col-md-2 control-label">Selct Class</label> -->
                        <select id="class_id" class="form-control input-md">
                          <option value="">Select Class</option>
                          <?php foreach ($classes as $key => $cls) { ?>
                            <option value="<?php echo $cls->id; ?>"><?php echo $cls->class_name; ?></option>
                           <?php } ?>
                        </select>
                      </div>
                      <div class="col-md-4">
                        <select disabled="true" id="section_dtails" class="form-control" onchange="getSectionStudents()" >
                          <option value="">Select Section</option>
                        </select>
                      </div>
                    </div>

                    <div class="form-group">
                      <!-- <label class="control-label col-md-2" for="BookType">Student</label> -->
                      <div class="col-md-12">
                        <button type="button" onclick="selectAll()" class="btn  btn-secondary">Select All</button>
                        <select multiple title="Select Student" disabled="true" size="10" id="students" class="form-control ">
                        </select>
                      </div>

                      <div class="form-group">
                    <div class="col-md-12" style="max-height: 150px;overflow-y: scroll;">
                      <span class="student-box"></span>
                      <span class="class-box"></span>
                      <span class="staff-box"></span>
                    </div>
                  </div>

                    </div>
                  </div>

                <div class="modal-footer" style="justify-content: flex-start;">
                  <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="addMembers()" style="margin-bottom: 3px">Add</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="copy-from-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">

        <div class="modal-dialog" style="width: 60%;margin:2% auto;">

          <!-- Modal content-->
          <div class="modal-content">
              <div class="modal-header">
                <h4 class="modal-title">Clone data from</h4>
              </div>
              <div class="modal-body">
                <label class="control-label">Select <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> to be copied</label>
                <select class="form-control" onchange="select_clone_chronicles()" name="selected_chronicles_id" id="selected_chronicles_id">
                  <option value="">Select <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></option>
                </select>
              </div>
              <div class="modal-footer" style="justify-content: center;">
                <button type="button" id="cancelModal" style="width: 120px;" class="btn btn-danger my-0" data-dismiss="modal">Close</button>
                <button type="button" disabled style="width: 120px;" onclick="copy_chronicles()" id="confirmCloneBtn"  class="btn btn-primary my-0">Confirm</button>
              </div>
          </div>
      </div>
</div>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>

<script type="text/javascript">

  var addedStudents = [];
  var addedSections = [];
  var addedStaff = [];
  var fileCount = 0;


$("#class_id").change(function(){
      var className=$("#class_id").val();
      var acad_year=$("#acad_year").val();
      var batch = $("#batch").val();
      $.post("<?php echo site_url('Class_students/get_section');?>",{className:className, acad_year:acad_year, batch:batch},function(data){
        var resultData=$.parseJSON(data);
        var output='';
        var output1='';
        var section = resultData.section;
        var stdName = resultData.stdname;
         for (var i=0,j=section.length; i < j; i++) {
          output+='<option value="'+section[i].id+'">'+section[i].section_name+'</option>';
         }
          $("#section_dtails").html(output);
          $("#section_dtails").prop('disabled',false);
          getSectionStudents();
      });
  });

  function getSectionStudents(){
      var className=$("#class_id").val(); 
      var section=$("#section_dtails").val();
      $.post("<?php echo site_url('Class_students/get_student_counselling');?>",{className:className,section:section},function(data){
          var studentname=$.parseJSON(data);
          var output='';
          var len=studentname.length;
          populateStudents(studentname);
          $("#students").prop('disabled',false);
      });
  };


    $(document).on('click', '#previewBtn', function(e){
      var ajax_data = {};
        $('#dynamic-content').html(''); // blank before load.
      var text_content = $(".summernote").code();
      var title = $("#title").val();
      var category = $("#category").val();
      ajax_data['communication_type'] = $("#communication_type").val();
      ajax_data['batch_class'] = $("#batch_class").val();
      ajax_data['sendTo_class'] = $('#send_to_class').val();
      var send_to_type = $('#send_to_type').val();
      ajax_data['sendTo'] = 'Both';
      if(send_to_type == 'parents_and_students') {
        ajax_data['sendTo'] = 'parents_and_students';
      } else if(send_to_type == 'students_only') {
        ajax_data['sendTo'] = 'preferred';
      }
      ajax_data['sendTo_class'] = ajax_data['sendTo'];
      ajax_data['student_ids'] = [];
      ajax_data['section_ids'] = [];
      ajax_data['staff_ids'] = [];
      ajax_data['custom_ids'] = [];
      $("input[class='stdIds']").each(function() {
       ajax_data['student_ids'].push($(this).val());
    });
    $("input[class='secIds']").each(function() {
       ajax_data['section_ids'].push($(this).val());
    });
    $("input[class='stfIds']").each(function() {
       ajax_data['staff_ids'].push($(this).val());
    });
    $("input[class='cusIds']").each(function() {
       ajax_data['custom_ids'].push($(this).val());
    });

    $('#modalTable').hide();
    var counts = $("#counts").html();
        $("#countBody").html('');
      if(ajax_data['student_ids'].length == 0 && ajax_data['section_ids'].length == 0 && ajax_data['staff_ids'].length == 0 && ajax_data['custom_ids'].length == 0) {
        $("#confirmBtn").attr('disabled',true);
            $('#msgBody').html('<span style="color:red;">Select Students/Class/Staff/Custome Numbers</span>');
            return false;
      }
      if(text_content == '<p><br></p>') {
          $("#confirmBtn").attr('disabled',true);
            $('#msgBody').html('<span style="color:red;">Text content cannot be empty.</span>');
            return false;
        }
        if(title == '') {
          $("#confirmBtn").attr('disabled',true);
            $('#msgBody').html('<span style="color:red;">Title cannot be empty.</span>');
            return false;
        }
        if(category == '') {
          $("#confirmBtn").attr('disabled',true);
            $('#msgBody').html('<span style="color:red;">Choose category</span>');
            return false;
        }
    $("#countBody").html(counts);
        $('#modal-loader').show();
        $.ajax({
            url: '<?php echo site_url('communication/texting/getPreviewData'); ?>',
            type: 'POST',
            data: ajax_data
        })
        .done(function(data){
            var data = JSON.parse(data);
            $('#modalTable').show();
            var previewData = callConstruct(data, ajax_data['communication_type']);
            $('#dynamic-content').html(previewData);
            $('#modal-loader').hide(); // hide loader 
        })
        .fail(function(){
            $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
            $('#modal-loader').hide();
        });
    });

    function populateStudents(stdData) {
    var output1 = '';
    for (var i=0,j=stdData.length; i < j; i++) {
          output1+='<option value="'+stdData[i].id+'">'+stdData[i].std_name+' </option>';
        }
      
        $("#students").html(output1);
  }

  function addMembers() {
      var userType = $("#selection_type").val();
      var html = '';
            var cName = $("#class_id option:selected").text();
            var csName = $("#section_dtails option:selected").text();
            $("#students option:selected").each(function () {
            var name = $(this).text();
            var id = $(this).val();
              if(addedStudents.indexOf(id) == -1) {
              html += '<div onclick="removeName(\'std_'+id+'\')" class="contact-names std_'+id+'"><input id="studentIds" class="stdIds" type="hidden" name="student_ids[]" value="'+id+'"><b>'+cName+csName+':</b> '+name+'&nbsp;&nbsp;<span class="fa fa-times remove" ></span></div>';
              addedStudents.push(id);
              }
        });
        $(".student-box").append(html);
             
      countMembers();
    }

    function clearAll() {
      addedStudents = [];
      addedSections = [];
      addedStaff = [];
      $(".contact-names").remove();
      countMembers();
    }

     function countMembers() {
      var std = addedStudents.length;
      var sec = addedSections.length;
      var stf = addedStaff.length;
      var html = '<strong>Students : </strong>'+std+'&nbsp;&nbsp;&nbsp;&nbsp;<strong>Sections : </strong>'+sec+'&nbsp;&nbsp;&nbsp;&nbsp;<strong>Staff:</strong> '+stf;
      $("#counts").html(html);
      if(std>0||sec>0||stf>0) {
        $("#contact-details").show();
      } else {
        $("#contact-details").hide();
      }
    }

    function removeName(id) {
      var ids = id.split("_");
      var type = ids[0];
      var idNum = ids[1];
      switch (type) {
          case 'std':
            var index = addedStudents.indexOf(idNum);
            addedStudents.splice(index, 1);
              break;
          case 'sec':
            var index = addedSections.indexOf(idNum);
            addedSections.splice(index, 1);
              break;
          case 'stf':
            var index = addedStaff.indexOf(idNum);
            addedStaff.splice(index, 1);
              break;
          case 'cus':
            var index = customNumbers.indexOf(idNum);
            customNumbers.splice(index, 1);
              break;
          default:
            break;
      }
      $("."+id).remove();
      countMembers();
    }

</script>

<script type="text/javascript">
  $(document).ready(function(){
    select_clone_chronicles();
  });

  function select_clone_chronicles() {
    var cloneSelect = $('#selected_chronicles_id').val();
    if(cloneSelect !=''){
      $('#confirmCloneBtn').removeAttr('disabled');
    }else{
      $('#confirmCloneBtn').attr('disabled','disabled');
    }
  }
  function applyTemplate() {
      var templateName = $("#template_id option:selected").val();
      $.ajax({
        url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getTemplate'); ?>',
        data: {'templateName': templateName},
        type: "post",
        success: function (data) {
          templateData = $.parseJSON(data);
          console.log(templateData);
          $('.summernote').code(templateData);
        },
        error: function (err) {
          console.log(err);
        }
    });
  }
  $(document).ready(function() {
        get_class_section();
        checkvalueDisabled();
    });

    function get_class_section(){
        $('#student_name').hide();
    $('#search').hide();
        $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_class_section'); ?>',
          type: "post",
          success: function (data) {
          var clsSection = $.parseJSON(data);
          option = '<option value="">Class Section</option>';
          for (i = 0; i < clsSection.length; i++) {
          option +='<option value="'+clsSection[i].csId+'">'+clsSection[i].classSection+'</option>'
          }
          $('#class_section').html(option);
          },

          error: function (err) {
            console.log(err);
          }
    });
    }


   function classSection_wise_search_std() {
        var sectionId = $("#class_section").val();
        if(sectionId ==''){
          $("#student_data").html('');  
        }
        var selected_date = $('#date').val();
        if(sectionId) {
            $.ajax({
                url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'sectionId':sectionId,'selected_date':selected_date},
                success: function(data) {
                  var std = JSON.parse(data);
                  console.log(std);
                  $("#student_data").html(prepare_student_table(std, selected_date));  
                  console.log(std); 
                }
            });
        }
    }
    function prepare_student_table (std, selected_date) {
    var html = '';
    if(!std)
        html += "<h4>Student Not Found</h4>";
    else {
        html += `
            <table id="customers2" class="table table-bordered datatable" style="width:70%; margin:auto;">
                <thead>
                    <tr>
                        <th style="width:5%">#</th>
                        <th style="width:20%">Student Name</th>
                        <th style="width:15%">Actions</th>
                    </tr>
                </thead>
                <tbody>
            `;
        for (i=0;i < std.length; i++) {
          var checked = '';
          var disabled = '';
          var enable = '';
          if (std[i].sent_check == 1) {
            checked = 'checked';
            disabled = 'disabled';
          }else if (std[i].sent_check == 0) {
            checked = 'unchecked';
            enable = 'disabled';
          }
            html += "<tr><td>" + (i+1) + "</td>";
            html += "<td>"+std[i].student_name + "</td>";
            html += '<td><a onclick="add_chronicles('+i+','+std[i].id+', \''+std[i].student_name+'\')" id="add_button'+i+'" data-target="#add_chronicles" '+disabled+' data-toggle="modal" class="btn btn-success" >Add Chronicles</a> <a href="#" onclick="view_chronicles('+std[i].id+',\''+selected_date+'\', \''+std[i].student_name+'\' )" data-target="#view_chronicles" '+enable+' data-toggle="modal" id="view_button'+i+'" class="btn btn-primary">View Chronicles</a></td>';

        }
        html += '</tbody></table>';
    }
    return html;
}

function view_chronicles(student_id, selected_date, std_name) {
  $('#chronicalStudentId1').val(student_id);
    $('#stdnamepopup1').html(std_name);
  $.ajax({
    url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_chronicles_by_student_id'); ?>',
    type: 'post',
    data: {
      'student_id': student_id,
      'selected_date': selected_date,
    },
    success: function(data) {
      var resData = $.parseJSON(data);
      console.log(resData);
      $('#chronicles-detail').html(construct_view_chronicles(resData));
    }
  });
}

function construct_view_chronicles(resData) {
  var viewhtml = '';
  viewhtml += '<div class="content-div" style="height:350px; overflow-y: auto;""> <b> Contant : </b> <br>' +resData.chronicle_content+ '</div><br>';
  viewhtml += '<br>';
  if(resData.file_path != ''){
    viewhtml += '<b>Attachment: <b>';
    viewhtml += '<br>';
    viewhtml += '<a onclick="download_chronicles('+resData.id+')" class="btn btn-primary"  ><i class="fa fa-download"> </i> Download </a>';
  }
 

  return viewhtml;
}

function download_chronicles(rowid) {
  window.location.href = '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/download_chronicles_by_rowid/'); ?>' + rowid;
}

function checkvalueDisabled(i) {
  var checkvalue = $('#check'+i).val();
  const checkBox=$('#check'+i);

  if ($(checkBox).is(':checked')) {
    // $(checkBox).attr('value', 'true');
    $('#add_button'+i).prop('disabled',true).addClass('btn btn-success');
    $('#view_button'+i).prop('disabled',false);
  } else {
    // $(checkBox).attr('value', 'false');
    $('#add_button'+i).prop('disabled',false).addClass('btn btn-danger');
    $('#view_button'+i).prop('disabled',true);
  }

 }

 $("#fileupload").change(function (){
    var files = $(this)[0].files;
    fileCount = files.length;
    $("#fileCount").html(fileCount+' Attachments');
  });

 function save_chronicles() {
  var selectedDate = $('#date').val();
    var $form = $('#addChronical');
    if ($form.parsley().validate()){
      var form = $('#addChronical')[0];
      var form_data = new FormData(form);
      form_data.append('chronicle_content', $(".summernote").code());
      var file_data = $('#fileupload').prop('files');
      for(var i in file_data) {
            form_data.append('attachment[]', file_data[i]);
        }
      form_data.append('created_on', selectedDate);
      $('#addChronical').trigger("reset");
      $('#clone_atthaced_file').trigger("reset");
      $('#cloneAttachedView').trigger("reset");

      $("#confirmBtn").html('Please wait..').attr('disabled', 'disabled');
      $.ajax({
        url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/save_mass_student_chronicles') ?>',
        type: 'post',
        data: form_data,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
          // console.log(data);
          location.reload()
          $('#add_chronicles').modal('hide');
          classSection_wise_search_std();
          $("#confirmBtn").html('Submit').removeAttr('disabled', 'disabled');
      form_data.append('chronicle_content', $(".summernote").code(''));


        }
      });
     }
    }
</script>

<script type="text/javascript">
  $(document).ready(function() {

    $('#datePicker').datetimepicker({
     viewMode: 'days',
      format: 'DD-MM-YYYY',
      minDate:new Date()
    }).change(dateChanged)
    .on('dp.change',dateChanged);
  });
  function dateChanged(ev) {
    $('#class_section').val('');
    classSection_wise_search_std();
  }

  $("#cerificate_img_Id").change(function() {
    var file = document.getElementById('cerificate_img_Id');
    if (!file.files[0]) {
      document.getElementById('resource-file_error').innerHTML = 'Resource is required';
      return false;
    }
    document.getElementById('resource-file_error').innerHTML = '';
    var file_size = parseFloat(file.files[0].size / 1024 / 1024);
    var max_size_string = '5MB';
    var max_file_size = parseInt(max_size_string.replace('MB', ''));
    if (file_size > max_file_size) {
      $("#file-size-error").html('File size exceeded.');
      $("#cerificate_img_Id").val('');
    } else {
      $("#file-size-error").html('');
    }
  });
</script>
<script type="text/javascript">
  $("#copy-from").click(function() {
  var selectedDate = $('#date').val();
  $("#copy-from-modal").modal('show');
  $('#confirmCloneBtn').prop('disabled',true);
 $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getchronicles_clone'); ?>',
          data: {'selectedDate':selectedDate},
          type: "post",
          success: function (data) {
            var chronicles = $.parseJSON(data);
            var options = '<option value="">Select</option>';
            for (var i = 0; i < chronicles.length; i++) {
              options += `<option value="${chronicles[i].id}">${chronicles[i].student_name}</option>`;
            }
            $("#selected_chronicles_id").html(options);

          },
          error: function (err) {
            console.log(err);
          }
      });
  });

  function copy_chronicles() {
    var chronicles_id = $("#selected_chronicles_id").val();
      $("#copy-from-modal").modal('hide');
    $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/getchroniclesById'); ?>',
          data: {chronicles_id: chronicles_id},
          type: "post",
          success: function (data) {
            var chrdata = $.parseJSON(data);
            $('.summernote').code(chrdata.chronicle_content); 

            var viewhtml = '';
            if(chrdata.file_path != ''){
              viewhtml += '<b>Clone Attachment: <b>';
              viewhtml += '<br>';
              viewhtml += '<a onclick="download_chronicles('+chrdata.id+')" class="btn btn-primary" id="clone_download_button"><i class="fa fa-download"> </i> Download </a>';
            }
            $('#cloneAttachedView').html(viewhtml);
            $('#clone_atthaced_file').val(chrdata.file_path);
          },
          error: function (err) {
            console.log(err);
          }
      });
  }
  
      function selectAll() {
        var selType = $("#selection_type").val();
        var id = 'students';
        if(selType == 'Class') {
          id = 'multi_class_sections';
        }
        $("#"+id+" option").prop('selected', true);
      }
  
</script>

<style type="text/css">
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
    max-height: 30%;
    overflow-y: scroll;
}
</style>

<style type="text/css">
  .btn-group-sm>.btn {
      padding: 4px 10px;
      font-size: 14px;
      line-height: 2 !important;
      border-radius: 3px !important;
  }
  .pType>.btn {
    padding: 4px 8px;
    font-size: 14px !important;
    line-height: 1.3 !important;
    border-radius: 3px !important;
  }
  .selectNumbers {
    font-size: 16px !important;
    background: #329030 !important;
    color: white !important;
  }
  .names {
    padding-bottom: 2% !important;
      padding-top: 2% !important;
      border-bottom: 1px solid #ccc !important;
  }
  .rowHead {
    padding-bottom: 2% !important;
      border-bottom: 2px solid #675f5f !important;
  }
  .clsBtn {
    padding: 2% 4% !important;
    font-size: 16px !important;
  } 
  .btn-group>.btn:focus,
  .open>.dropdown-toggle.btn-secondary {
    background-color: #329030 !important;
    color: #fff !important;
  }
  .default-active {
    background-color: #329030 !important;
    color: #fff !important;
  }
  .btn-green {
    background: #329030 !important;
    color:white;
  }
  .customCheck, .staffCheck {
    display: block;
  }
  .cCheck {
    font-size: 18px;
    margin-left:9%;
  }

  #clsType {
    display: none;
  }

  .customCheck>input[type="checkbox"], .staffCheck>input[type="checkbox"]{
    /*display: none;*/
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: #ccc;
    border-radius: 50%;
    box-sizing: border-box;
    position: relative;
    top:1px;
    bottom: 4px;
    box-sizing: content-box ;
    width: 17px;
    height: 17px;
    border-width: 0;
    transition: all .3s linear;
  }
  .customCheck>input[type="checkbox"]:checked,.staffCheck>input[type="checkbox"]:checked{
    background: #329030;
  }
  .csSelect {
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: none;
    border-radius: 1px;
    box-sizing: border-box;
    position: relative;
    box-sizing: content-box ;
    width: 25px;
    height: 25px;
    border-width: 0;
    transition: all .3s linear;
  }
  #unicode {
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    background: #ccc;
    border-radius: 1px;
    box-sizing: border-box;
    position: relative;
    box-sizing: content-box ;
    width: 17px;
    height: 17px;
    border: 1px solid;
    transition: all .3s linear;
  }
  #unicode:checked {
    background: #329030;
  }

  #classType {
    display: none;
  }
  .form-group:last-child {
    margin-bottom: 15px;
  }
  .mrg {
    width:100%;
  }
  .contact-box{
    max-height:300px;
    overflow-y: scroll;
  }
  .contact-box>button{
    position: relative;
    top: 0px;
    left: 0px;
  }
  .student-box,#staff-box,#class-box, #custom-box {
    padding-top:10px;
  }
  .contact-names{
    padding:5px;
    border-radius: 5px;
    margin:3px;
    display: inline-block;
    border:1px solid #fff;
    cursor: pointer;
  }
  .student-box>.contact-names{
    background: #ccf5e6;
    color: #0f6944;
  }
  .class-box>.contact-names{
    background: #f7daa3;
    color: #826116;
  }
  .staff-box>.contact-names{
    background: #fdbdbd;
    color: #7b1e1e;
  }
  .contact-names:hover {
    border:1px solid #000;
  }
  .class-box>.contact-names:hover{
    border:1px solid #000;
  }
  .staff-box>.contact-names:hover{
    border:1px solid #000;
  }
  .remove{
    cursor: pointer;
  }

  #student_id {
    position: -webkit-sticky; /* Safari */
    position: sticky;
    top: 0;
  }
  #contact-container {
    border: 1px solid #ccc;
    border-radius: 5px;
    max-height: 150px;
    overflow-y: scroll;
  }
  
</style>