<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Transportation extends CI_Controller {

	public function __construct() {
        parent::__construct();
		if (!$this->ion_auth->logged_in()) {
     		redirect('auth/login', 'refresh');
    	}
        $this->load->model('transportation_model');
        // $this->load->model('student/Student_Model');
  	}

  	public function index() {
      // echo $this->acad_year->getAcadYearId(); die();
      $data['username'] = $this->settings->getSetting('transport_admin_username');//'<EMAIL>';
      $data['password'] = $this->settings->getSetting('transport_admin_password');//'<EMAIL>';
  		$data['permit_admin_console'] = $this->authorization->isAuthorized('TRANSPORTATION.ADMIN_CONSOLE');
  		$data['permit_buses'] = $this->authorization->isAuthorized('TRANSPORTATION.BUSES');
  		$data['permit_routes'] = $this->authorization->isAuthorized('TRANSPORTATION.ROUTES');
  		$data['permit_stops'] = $this->authorization->isAuthorized('TRANSPORTATION.STOPS');
      $data['permit_allocate_journeys'] = $this->authorization->isAuthorized('TRANSPORTATION.ENTITIES');
      $data['permit_add_journeys'] = $this->authorization->isAuthorized('TRANSPORTATION.JOURNEYS');
      $data['permit_add_driver'] = $this->authorization->isAuthorized('TRANSPORTATION.DRIVERS');
      $data['permit_take_attendance'] = $this->authorization->isAuthorized('TRANSPORTATION.TAKE_ATTENDANCE');
      $data['permit_student_route_report'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_REPORT');
      $data['permit_mass_journey_changes'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_JOURNEY_CHANGES');
      $data['permit_mismatch_report'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_MISMATCH_REPORT');
      $data['permit_daily_track'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_DAILY_TRACK');
      $data['permit_daily_track_summary'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_DAILY_TRACK_SUMMARY');
      $data['permit_student_wise_report'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_STUDENT_WISE_REPORT');
      $data['permit_avg_journey_time'] = $this->authorization->isAuthorized('TRANSPORTATION.TX_AVG_JOURNEY_TIME');
      $data['permit_notification_logs'] = $this->authorization->isAuthorized('TRANSPORTATION.NOTIFICATION_LOGS');
      $data['permit_export_student_data'] = $this->authorization->isAuthorized('TRANSPORTATION.EXPORT_STUDENT_DATA');
      $data['permit_export_staff_data'] = $this->authorization->isAuthorized('TRANSPORTATION.EXPORT_STAFF_DATA');

      $site_url = site_url();
      $data['tiles'] = array(
          [
            'title' => 'Admin Console',
            'sub_title' => $data['username'] . '/' . $data['password'],
            'icon' => 'svg_icons/management.svg',
            'url' => 'https://next-web.dhundhoo.com/map',
            'permission' => $data['permit_admin_console']
          ],
          [
            'title' => 'Manage Drivers',
            'sub_title' => 'Add / Edit driver details',
            'icon' => 'svg_icons/drivers.svg',
            'url' => $site_url.'transportation/drivers',
            'permission' => $data['permit_add_driver']
          ],
          [
            'title' => 'Manage Buses',
            'sub_title' => 'Add / Edit bus details',
            'icon' => 'svg_icons/buses.svg',
            'url' => $site_url.'transportation/buses',
            'permission' => $data['permit_buses']
          ],
          [
            'title' => 'Manage Stops',
            'sub_title' => 'Add / Edit stop data',
            'icon' => 'svg_icons/stop.svg',
            'url' => $site_url.'transportation/stops',
            'permission' => $data['permit_stops']
          ],
          [
            'title' => 'Manage Journeys',
            'sub_title' => 'Add / Edit journey data',
            'icon' => 'svg_icons/journeys.svg',
            'url' => $site_url.'transportation/journeys',
            'permission' => $data['permit_add_journeys']
          ],
          [
            'title' => 'Allocate Journeys to Students',
            'sub_title' => 'Add / Edit student journeys',
            'icon' => 'svg_icons/provisionstaff.svg',
            'url' => $site_url.'transportation/student_journeys',
            'permission' => $data['permit_allocate_journeys']
          ],
          [
            'title' => 'Mass Allocate Students - 1',
            'sub_title' => 'Mass Assign Student Journeys',
            'icon' => 'svg_icons/massupdate.svg',
            'url' => $site_url.'transportation/mass_allocate_journeys',
            'permission' => $data['permit_allocate_journeys']
          ],
          [
            'title' => 'Allocate Journeys to Staffs',
            'sub_title' => 'Staff Journeys',
            'icon' => 'svg_icons/journeys.svg',
            'url' => $site_url.'transportation/staff_journeys',
            'permission' => $data['permit_allocate_journeys']
          ],
          [
            'title' => 'Re-Allocate Journeys to Students',
            'sub_title' => 'Update student journeys',
            'icon' => 'svg_icons/massupdate.svg',
            'url' => $site_url.'transportation/mass_journeys_changes',
            'permission' => $data['permit_allocate_journeys']
          ],
          [
            'title' => 'Mass Allocate Students - 2',
            'sub_title' => 'Assign Student Journeys',
            'icon' => 'svg_icons/provisionstaff.svg',
            'url' => $site_url.'transportation/assign_student_journeys',
            'permission' => $data['permit_allocate_journeys']
          ]
      );
      $data['tiles'] = checkTilePermissions($data['tiles']);

      $data['report_tiles'] = array(
          [
            'title' => 'Bus-wise Report (SMS) Format - 1',
            'sub_title' => 'Shows bus wise student details format 1',
            'icon' => 'svg_icons/student.svg',
            'url' => $site_url.'transportation/buswise_report_format_one',
            'permission' => $data['permit_student_route_report']
          ],
          [
            'title' => 'Bus-wise Report (SMS) Format - 2',
            'sub_title' => 'Shows bus wise student details format 2',
            'icon' => 'svg_icons/student.svg',
            'url' => $site_url.'transportation/buswise_report_format_two',
            'permission' => $data['permit_student_route_report']
          ],
          [
            'title' => 'Journey Mismatch',
            'sub_title' => 'View student journey mismatches',
            'icon' => 'svg_icons/journeys.svg',
            'url' => $site_url.'transportation/journey_mismatch',
            'permission' => $data['permit_mismatch_report']
          ],
          [
            'title' => 'Notification Logs',
            'sub_title' => 'View transport notification logs',
            'icon' => 'svg_icons/logs.svg',
            'url' => $site_url.'transportation/logs',
            'permission' =>  $data['permit_notification_logs']
          ],
          [
            'title' => 'Daily Tracking',
            'sub_title' => 'View Daily journey reports',
            'icon' => 'svg_icons/journeys.svg',
            'url' => $site_url.'transportation/daily_track',
            'permission' => $data['permit_daily_track']
          ],
          [
            'title' => 'Daily Tracking Summary',
            'sub_title' => 'View Daily journey summary',
            'icon' => 'svg_icons/journeys.svg',
            'url' => $site_url.'transportation/daily_track_summary',
            'permission' => $data['permit_daily_track_summary']
          ],
          [
            'title' => 'Student Wise Report',
            'sub_title' => 'View Student Wise Report',
            'icon' => 'svg_icons/studentwisehomeworkreport.svg',
            'url' => $site_url.'transportation/student_wise_report',
            'permission' => $data['permit_student_wise_report']
          ],
          [
            'title' => 'Travel Time Report',
            'sub_title' => 'View journey wise travel time',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'transportation/journey_time',
            'permission' => $data['permit_avg_journey_time']
          ],
          [
            'title' => 'Export Student Data',
            'sub_title' => 'View journey wise travel time',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'transportation/export_student_data',
            'permission' => $data['permit_export_student_data']
          ],
          [
            'title' => 'Export Staff Data',
            'sub_title' => 'View journey wise travel time',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'transportation/export_staff_data',
            'permission' => $data['permit_export_staff_data']
          ]
      );
      $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/dashboard/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/dashboard/index_mobile';
      }else{
        $data['main_content'] = 'transportation/dashboard/index';    	
      }

  		
    	$this->load->view('inc/template', $data);
  	}

  	public function buses() {
  		$data['buses'] = $this->transportation_model->getBuses();
      foreach($data['buses'] as $bus){
        if(isset($bus->permit_number_expiry_date))
          $bus->permit_number_expiry_date = date('d-m-Y', strtotime($bus->permit_number_expiry_date));

        if(isset($bus->rc_expiry_date))
          $bus->rc_expiry_date = date('d-m-Y', strtotime($bus->rc_expiry_date));

        if(isset($bus->insurance_number_expiry_date))
          $bus->insurance_number_expiry_date = date('d-m-Y', strtotime($bus->insurance_number_expiry_date));
        // if(!empty($bus->tracking_url)){
        //   $randomNumber = rand(1, 10000);
        //   $trackingUrlWithRandom = $bus->tracking_url . '&rand=' . $randomNumber;
        //   $bus->tracking_url = $trackingUrlWithRandom;
        // }
      }
      // echo "<pre>";print_r($data['buses']);die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/buses/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/buses/index_mobile';
      }else{
        $data['main_content'] = 'transportation/buses/index';     	
      }
    	$this->load->view('inc/template', $data);
  	}

  	public function new_bus() {
      $data['isEdit'] = 0;
      $data['drivers'] = $this->transportation_model->getDrivers('new_bus');
  		// $data['main_content'] = 'transportation/buses/add_bus';

      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/buses/add_bus_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/buses/add_bus_mobile';
      }else{
        $data['main_content'] = 'transportation/buses/add_bus';    	
      }

    	$this->load->view('inc/template', $data);
  	}

  	public function add_bus() {
  		$status = $this->transportation_model->addBus();
  		if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully added.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to add.');
  		}
  		redirect('transportation/buses');
  	}

  	public function delete_bus($id) {
  		$status = $this->transportation_model->deleteBus($id);
  		if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to delete.');
  		}
  		redirect('transportation/buses');
  	}

  	public function routes() {
      $data['routes'] = $this->transportation_model->getRoutesAndStops();
  		$data['uniqueStops'] = $this->transportation_model->getUniqueStops();
  		$data['main_content'] = 'transportation/routes/index';
    	$this->load->view('inc/template', $data);
  	}

  	public function new_route() {
  		$data['main_content'] = 'transportation/routes/add_route';
    	$this->load->view('inc/template', $data);
  	}

  	public function add_route() {
  		$status = $this->transportation_model->addRoute();
  		if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully added.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to add.');
  		}
  		redirect('transportation/routes');
  	}

  	public function delete_route($id) {
  		$status = $this->transportation_model->deleteRoute($id);
  		if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to delete.');
  		}
  		redirect('transportation/routes');
  	}

  	public function stops() {
      $data['isEdit'] = 0;
      $data['stop'] = array();
      if(isset($_POST['stop_id'])) {
        $data['isEdit'] = 1;
        $data['stop'] = $this->transportation_model->getStop($_POST['stop_id']);
      }
      // echo "<pre>"; print_r($data); die();
  		$data['stops'] = $this->transportation_model->getStops();
      // $data['main_content'] = 'transportation/stops/stops';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/stops/stops_page_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/stops/stops_page_mobile';
      }else{
        $data['main_content'] = 'transportation/stops/stops_page';    	
      }
    	$this->load->view('inc/template', $data);
  	}

    public function get_transport_stop_data_ajax(){
      $result = $this->transportation_model->getStops();
      echo json_encode($result);
    }
  	public function new_stop() {
  		// $data['routes'] = $this->transportation_model->getRouteNames();
  		$data['main_content'] = 'transportation/stops/add_stop';
    	$this->load->view('inc/template', $data);
  	}

    public function add_transport_stop_data_ajax(){
      echo $this->transportation_model->addStop();
    }

  	public function add_stop() {
  		$status = $this->transportation_model->addStop();
  		if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully added.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to add.');
  		}
  		redirect('transportation/stops');
  	}

    public function edit_stop($stop_id) {
      $data['routes'] = $this->transportation_model->getRouteNames();
      $data['stop'] = $this->transportation_model->getStop($stop_id);
      $data['main_content'] = 'transportation/stops/edit_stop';
      $this->load->view('inc/template', $data);
    }

    public function update_stop() {
      $status = $this->transportation_model->updateRouteStop();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/stops');
    }

    public function edit_transport_stop_get_data_ajax(){
      $stop_id = $_POST['stop_id'];
      $result = $this->transportation_model->getStop($stop_id);
      echo json_encode($result);
    }
  	public function delete_stop() {
      $id = $_POST['stop_id'];
  		$status = $this->transportation_model->removeStop($id);
      echo $status;
  		/*if($status) {
  			$this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
  		} else {
  			$this->session->set_flashdata('flashError', 'Failed to delete.');
  		}
  		redirect('transportation/stops');*/
  	}

    public function entities() {
      $data['class_sections'] = $this->transportation_model->getClassSections();
      // $data['stops'] = $this->transportation_model->getStops();
      $data['routes'] = $this->transportation_model->getRouteNames();
      $data['main_content'] = 'transportation/entities/index';
      $this->load->view('inc/template', $data);
    }

    public function new_entity() {
      $data['routes'] = $this->transportation_model->getRouteNames();
      $data['class_sections'] = $this->transportation_model->getClassSections();
      $data['main_content'] = 'transportation/entities/add_entities';
      $this->load->view('inc/template', $data);
    }

    public function getStopsByRoute() {
      $route_id = $_POST['route_id'];
      echo json_encode($this->transportation_model->getStopsByRoute($route_id));
    }

    /* JOURNEYS */
    public function journeys() {
      $data['buses'] = $this->transportation_model->getBusNames();
      $data['stops'] = $this->transportation_model->getStopNames();
      $data['super_admin'] = $this->authorization->isSuperAdmin();
      
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/journeys/index_new_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/journeys/index_new_mobile';
      }else{
        $data['main_content'] = 'transportation/journeys/index_new';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function checkStudentsInJourney() {
      $journey_id = $_POST['journey_id'];
      $stop_id = $_POST['stop_id'];
      $status = $this->transportation_model->checkStudentsInJourney($journey_id, $stop_id);
      echo $status;
    }

    public function deleteJourney() {
      $journey_id = $_POST['journey_id'];
      $status = $this->transportation_model->delete_journey($journey_id);
      echo $status;
    }

    public function deleteJourneyStop() {
      $status = $_POST['status'];
      $journey_id = $_POST['journey_id'];
      $stop_id = $_POST['stop_id'];
      $status = $this->transportation_model->deleteJourneyStop($status, $journey_id, $stop_id);
      echo $status;
    }

    public function add_journey_stops() {
      $journey_ids = $_POST['journey_ids'];
      $stops = $_POST['stops'];
      $status = $this->transportation_model->add_journey_stops($journey_ids, $stops);
      echo $status;
    }

    public function new_journey() {
      $data['buses'] = $this->transportation_model->getBusNames();
      $data['stops'] = $this->transportation_model->getStopNames();
      // $data['routes'] = $this->transportation_model->getRouteNames();
      $data['weekdays'] = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/journeys/add_journey_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/journeys/add_journey_mobile';
      }else{
        $data['main_content'] = 'transportation/journeys/add_journey';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function add_journey() {
      $status = $this->transportation_model->addJourneys();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/journeys');
    }

     public function clone_journey() {
      $input = $this->input->post();
      $input['stop_ids'] = explode(',', $input['stop-names']);
      $status = $this->transportation_model->clone_journey($input);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/journeys');
    }

    public function updateJourney() {
      $input = $this->input->post();
      echo $this->transportation_model->update_journey($input);
    }

    public function getJourneys() {
      $thing_id = $_POST['thing_id'];
      $data['journeys'] = $this->transportation_model->getJourneysByThing($thing_id);
      echo json_encode($data['journeys']);
    }

    public function get_unique_journeys_by_thing_id() {
      $thing_id = $_POST['thing_id'];
      $data['journeys'] = $this->transportation_model->get_unique_journeys_by_thing_id($thing_id);
      echo json_encode($data['journeys']);
    }

    private function _updateJourneyRefresh($journeys_json, $thing_id, $response) {
        $journeys = json_decode($journeys_json);
        $journey_ids = array();
        foreach ($journeys->journeys as $key => $journey) {
          $journey_ids[] = $journey->journeyId;
        }

        $this->db->trans_start();
        //update journeys as refreshed
        $this->transportation_model->update_journeys_refreshed($journey_ids);
        //update tracking url in things 
        $this->transportation_model->updateTrackingUrl($thing_id, $response);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    private function _refreshJourneys($journeys_json, $thing_id) {
      // $curlUrl = 'https://v1.dhundhoo.com/vendor/journeys/refresh?apiKey=f4334ca8-cc4f-458a-a7c4-760e9cd960ca';
      // 
      $curlUrl = $this->settings->getSetting('refresh_journey_url');
      $curl = curl_init();

      // trigger_error($journeys_json);

      curl_setopt_array($curl, array(
          CURLOPT_URL => $curlUrl,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => $journeys_json,
          CURLOPT_HTTPHEADER => array(
              "Content-Type:application/json",
              "Accept: application/json"
          ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);
      if($err) {
        // trigger_error('..........................................................');
        trigger_error($err);
        return 0;
      } else {
        // trigger_error($response);
        return $this->_updateJourneyRefresh($journeys_json, $thing_id, $response);
        
        // return $this->transportation_model->updateTrackingUrl($thing_id, $response);
      }
      // echo "<pre>"; print_r($response); die();
      
    }

    public function refresh_journeys() {
      $thing_id = $_POST['thing_id'];
      if (ENVIRONMENT !== 'production') {
        //Do not allow journey refresh from localhost
        return 0;
      }
      // $journeyData = $this->transportation_model->getNotRefreshedJourneys($thing_id);
      $journeyData = $this->transportation_model->getJourneysByThingId($thing_id);
      $thing = $this->transportation_model->getThingById($thing_id);
      $journeys['thingId'] = $thing->thing_reg_number;
      $journeys['thingName'] = $thing->thing_name;
      $journeys['journeys'] = array();
      foreach ($journeyData as $key => $journey) {
          // $stops[$journey->route_id] = $this->transportation_model->getStopsByRouteId($journey->route_id);
        $stops = $this->transportation_model->getStopsByJourney($journey->id);
        if(empty($stops))
          continue;
        $journeys['journeys'][] = array(
          'journeyId' => $journey->id,
          'journeyName' => $journey->journey_name,
          'journeyType' => $journey->journey_type,
          'startTime' => date('H:i', strtotime($journey->tentative_start_time)),
          'endTime' => date('H:i', strtotime($journey->tentative_end_time)),
          'workingDays' => json_decode($journey->days),
          'disabled' => false,
          'stops' => $stops
        );
      }
      // echo "<pre>"; print_r($journeys); die();
      $journeys_json = json_encode($journeys);

      $status = $this->_refreshJourneys($journeys_json, $thing_id);
      echo $status;
    }

    public function refresh_single_journey() {
      $journey_id = $_POST['journeyId'];
      $journeyData = $this->transportation_model->getJourney($journey_id);
      $stops = $this->transportation_model->getStopsByRouteId($journeyData->id);
      $thing = $this->transportation_model->getThingById($journeyData->thing_id);
      $journeys['thingId'] = $thing->thing_reg_number;
      $journeys['thingName'] = $thing->thing_name;
      $journeys['journeys'] = array();
      $journeys['journeys'][] = array(
        'journeyId' => $journeyData->id,
        'journeyName' => $journeyData->journey_name,
        'journeyType' => $journeyData->journey_type,
        'startTime' => date('H:i', strtotime($journeyData->tentative_start_time)),
        'endTime' => date('H:i', strtotime($journeyData->tentative_end_time)),
        'workingDays' => json_decode($journeyData->days),
        'disabled' => false,
        'stops' => $stops
      );

      $journeys_json = json_encode($journeys);
      $status = $this->_refreshJourneys($journeys_json, $journeyData->thing_id);
      echo $status;
    }

    public function student_stops() {
      $sectionId = $_POST['section_id'];
      $students = $this->transportation_model->getStudentsBySection($sectionId);
      echo json_encode($students);
    }

    public function assign_student_stops() {
      $status = $this->transportation_model->assignStudentStops();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully assigned.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/entities');
    }

    public function drivers() {
      $data['drivers'] = $this->transportation_model->getDrivers('manage_drivers');
      foreach($data['drivers'] as &$driver){
        if (isset($driver->driver_licence_expiry_date)) {
          $timestamp = strtotime($driver->driver_licence_expiry_date);
          if ($timestamp !== false) {
              $driver->driver_licence_expiry_date = date('d-m-y', $timestamp);
          }
        }
      }
      // echo "<pre>";print_r($data['drivers']);die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/drivers/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/drivers/index_mobile';
      }else{
        $data['main_content'] = 'transportation/drivers/index';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function new_driver() {
      $data['isEdit'] = 0;
      // $data['main_content'] = 'transportation/drivers/add_driver';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/drivers/add_driver_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/drivers/add_driver_mobile';
      }else{
        $data['main_content'] = 'transportation/drivers/add_driver';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function add_driver() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->transportation_model->addDriver($input);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/drivers');
    }

    public function edit_driver($id) {
      $data['isEdit'] = 1;
      $data['driver'] = $this->transportation_model->getDriver($id);
      if (isset($data['driver']->driver_licence_expiry_date)) {
        $data['driver']->driver_licence_expiry_date = substr($data['driver']->driver_licence_expiry_date, 0, 10);
      }
      // echo "<pre>";print_r($data['driver']);die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/drivers/add_driver_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/drivers/add_driver_mobile';
      }else{
        $data['main_content'] = 'transportation/drivers/add_driver';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function check_driver_status(){
      $id = $this->input->post('driver_id');
      echo $this->transportation_model->check_driver_status($id);
      // echo $status;
    }

    public function update_driver($id) {
      $status = $this->transportation_model->updateDriver($id);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully updated.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to update.');
      }
      redirect('transportation/drivers');
    }

    public function remove_bus_driver($id) {
      $status = $this->transportation_model->removeBusDriver($id);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully removed.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to remove.');
      }
      redirect('transportation/buses');
    }

    public function edit_bus($id) {
      $data['isEdit'] = 1;
      $data['drivers'] = $this->transportation_model->getUnallocatedDrivers();
      // echo "<pre>"; print_r($data);die();
      $data['bus'] = $this->transportation_model->getBus($id);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/buses/add_bus_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/buses/add_bus_mobile';
      }else{
        $data['main_content'] = 'transportation/buses/add_bus';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function update_bus($id) {
      $status = $this->transportation_model->updateBus($id);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully updated.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to update.');
      }
      redirect('transportation/buses');
    }

    public function take_attendance() {
      $data['drivers'] = $this->transportation_model->getDrivers('task_attendance');
      $data['class_sections'] = $this->transportation_model->getClassSections();
      $data['main_content'] = 'transportation/drivers/attendance';
      $this->load->view('inc/template', $data);
    }

    public function getDriverBuses() {
      $driver_id = $_POST['driver_id'];
      $things = $this->transportation_model->getDriverBuses($driver_id);
      echo json_encode($things);
    }

    public function getDriverJourneys() {
      $thing_id = $_POST['thing_id'];
      $date_sel = isset($_POST['date_sel'])?$_POST['date_sel']:0;
      $journeys = $this->transportation_model->getDriverJourneys($thing_id, $date_sel);

      echo json_encode($journeys);
    }

    public function getBusJourneys() {
      $thing_id = $_POST['thing_id'];
      $journeys = $this->transportation_model->getBusJourneys($thing_id);
      echo json_encode($journeys);
    }

    public function getJourneyStudents() {
      $journeyId = $_POST['journey_id'];
      $students = $this->transportation_model->getJourneyStudents($journeyId);
      $stops = $this->transportation_model->getJourneyStops($journeyId);
      echo json_encode(array("students" => $students, "stops" => $stops));
    }

    public function getStudentsInJourney() {
      $journeyId = $_POST['journey_id'];
      $thingId = $_POST['thing_id'];
      $date = $_POST['date'];
      $students = $this->transportation_model->getJourneyStudents($journeyId, $date);
      $driver = $this->transportation_model->getDriverByBus($thingId);
      $bus = $this->transportation_model->getThingById($thingId);
      echo json_encode(array("students" => $students, "driver" => $driver, 'bus' => $bus));
    }

    public function getStudentsInStops() {
      $journeyId = $_POST['journey_id'];
      $thingId = $_POST['thing_id'];
      $stopId = $_POST['stop_id'];
      $date = $_POST['date'];
      $students = $this->transportation_model->getJourneyStopsStudents($journeyId, $stopId, $date);
      $staff = $this->transportation_model->getJourneyStopsStaff($journeyId, $stopId, $date);
      $merageArry = array_merge($students, $staff);
      $driver = $this->transportation_model->getDriverByBus($thingId);
      $bus = $this->transportation_model->getThingById($thingId);
      echo json_encode(array("students" => $merageArry, "driver" => $driver, 'bus' => $bus));
    }

    public function addAttendance() {
      $status = $this->transportation_model->addAttendance();
      $journeyId = $_POST['journey_id'];
      $students = $this->transportation_model->getJourneyStudents($journeyId);
      echo json_encode($students);
    }

    public function getSectionStudents() {
      $section_id = $_POST['section_id'];
      $students = $this->transportation_model->getSectionStudents($section_id);
      echo json_encode($students);
    }

    public function change_bus() {
      $thing_id = $_POST['thing_id'];
      $new_thing_id = $_POST['new_thing_id'];
      $journey_id = $_POST['journey_id'];
      $status = $this->transportation_model->changeBus($thing_id, $new_thing_id, $journey_id);
      echo $status;
    }

    public function getStopById() {
      $stop_id = $_POST['stop_id'];
      $stopData = $this->transportation_model->getStopById($stop_id);
      echo json_encode($stopData);
    }

    public function attachStop() {
      $status = $this->transportation_model->addStop();
      echo $status;
    }

    public function override_student_routes($stdId) {
      $stdData = $this->transportation_model->getStudentData([$stdId]);
      $data['stdData'] = $stdData[0];
      $data['routes'] = $this->transportation_model->getRouteNames();
      $data['weekdays'] = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      $data['actualRoutes'] = $this->transportation_model->getStudentActualRoutes($stdId);
      $data['overrides'] = $this->transportation_model->getStudentOverrides($stdId);
      // echo "<pre>"; print_r($data); die();
      $data['main_content'] = 'transportation/entities/override';
      $this->load->view('inc/template', $data);
    }

    public function add_student_overrides() {
      $stdId = $input = $this->input->post('student_id');
      $status = $this->transportation_model->addStudentOverride();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add.');
      }
      redirect('transportation/override_student_routes/'.$stdId);
    }

    public function deleteOverride($overrideId, $stdId) {
      $status = $this->transportation_model->deleteOverride($overrideId);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to delete.');
      }
      redirect('transportation/student_journey_override/'.$stdId);
    }

    public function buswise_report_format_one() {
      $data['buses'] = $this->transportation_model->getBusNames();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/reports/bus_wise_students_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/reports/bus_wise_students_mobile';
      }else{
        $data['main_content'] = 'transportation/reports/bus_wise_students';    	
      }
      $this->load->view('inc/template', $data);
    }
    
    public function buswise_report_format_two() {
      $days_of_week = array(
        array('id' => 1, 'value' => 'MONDAY'),
        array('id' => 2, 'value' => 'TUESDAY'),
        array('id' => 3, 'value' => 'WEDNESDAY'),
        array('id' => 4, 'value' => 'THURSDAY'),
        array('id' => 5, 'value' => 'FRIDAY'),
        array('id' => 6, 'value' => 'SATURDAY'),
        // array('id' => 7, 'value' => 'SUNDAY')
      );
      $data['days'] = $days_of_week;
      $data['buses'] = $this->transportation_model->getBusNames();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/reports/bus_wise_report_format_two_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/reports/bus_wise_report_format_two_mobile';
      }else{
        $data['main_content'] = 'transportation/reports/bus_wise_report_format_two';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function driverActivation() {
      $userId = $_POST['userId'];
      $active = $_POST['active'];
      $status = $this->transportation_model->driverActivation($userId, $active);
      echo $status;
    }

    public function student_journeys($section_id=0) {
      $data['section_id'] = $section_id;
      // $data['classes'] = $this->transportation_model->getClasses();
      $data['class_section'] = $this->transportation_model->getClassSections();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/index_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/index';  	
      }
      $this->load->view('inc/template', $data);
    }

    public function assign_student_journeys() {
      $data['class_section'] = $this->transportation_model->getClassSections();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/student_journeys_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/student_journeys_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/student_journeys';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function mass_allocate_journeys($section_id=0) {
      $data['section_id'] = $section_id;
      $data['selectJourneys'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['class_section'] = $this->transportation_model->getClassSections();
      $data['classList'] = $this->transportation_model->getClassList();
      $data['picks'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['drops'] = $this->transportation_model->getJourneysByType('DROPPING');
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/assign_journeys_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/assign_journeys_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/assign_journeys';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function getStudentsAndJourneys() {
      $section_id = $_POST['section_id'];
      $std_name = $_POST['std_name'];
      $students = $this->transportation_model->getStduentsAndJourneys($section_id, $std_name);
      // echo "<pre>"; print_r($students); die();
      $stdData = array();
      $overrides = array();
      $stdJourneys = array();
      foreach ($students as $key => $std) {
        $stdId = $std->stdId;
        if(!array_key_exists($stdId, $stdData)) {
          $stdData[$stdId] = array();
          $stdData[$stdId]['stdName'] = $std->stdName;
          $stdData[$stdId]['admission_no'] = $std->admission_no;
          $stdData[$stdId]['roll_no'] = $std->roll_no;
          $stdData[$stdId]['class_section'] = $std->class_section;
          $stdData[$stdId]['journey'] = array();
          $stdData[$stdId]['override'] = '';
        }
        $journeyStr = '';
        $override = '';
        $jType = '<i style="color:#1bea1b;" class="fa fa-arrow-up"></i>&nbsp;&nbsp;PICK';
        if($std->journey_type == 'DROPPING') {
          $jType = '<i style="color:yellow;" class="fa fa-arrow-down"></i>&nbsp;&nbsp;DROP';
        }
        if($std->journey_type != '' && !in_array($std->stdJourneyId, $stdJourneys)) {
          array_push($stdJourneys, $std->stdJourneyId);
          $stdData[$stdId]['journey'][$std->day][] = array('id' => $std->stdJourneyId, 'name' => $jType.' '.$std->journey_name. ' - Stop: '.$std->stop_name);
        } 
        if($std->override_journey != '' && !in_array($std->sroId, $overrides)) {
          array_push($overrides, $std->sroId);
          $day = '';
          if($std->sroType == 'single') {
            $day = ' ['.$std->sroDay.']';
          }
          $stdData[$stdId]['override'] .= '<b>'.$std->override_journey.'</b> ('.$std->override_stop.') '.$std->from_date.' - '.$std->to_date.''.$day.' ,<br>';
        }
      }
      $weekdays = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
      foreach ($stdData as $stdId => $stdVal) {
        foreach ($weekdays as $day) {
          if(array_key_exists($day, $stdVal['journey']))
            $stdData[$stdId]['journeys'][$day] = $stdVal['journey'][$day];
            unset($stdData[$stdId]['journey']);
        }
      }
      // echo "<pre>"; print_r($stdData); die();
      echo json_encode($stdData);
    }

    public function deleteStdJourney() {
      $stdJourneyId = $_POST['stdJourneyId'];
      echo $this->transportation_model->deletestdJourney($stdJourneyId);
    }

    public function student_journey_override($stdId) {
      $stdData = $this->transportation_model->getStudentData([$stdId]);
      $data['overrides'] = $this->transportation_model->getStudentOverrides($stdId);
      $data['journeys'] = $this->transportation_model->getStudentJourneys($stdId);
      $data['stdData'] = $stdData[0];
      $data['section_id'] = $data['stdData']->section_id;
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/overrides_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/overrides_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/overrides';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function assign_journeys($section_id=0) {
      $data['section_id'] = $section_id;
      $data['selectJourneys'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['class_section'] = $this->transportation_model->getClassSections();
      $data['picks'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['drops'] = $this->transportation_model->getJourneysByType('DROPPING');
      $data['main_content'] = 'transportation/student_journeys/assign_journeys';
      $this->load->view('inc/template', $data);
    }

    public function submitStudentJourneys() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $status = $this->transportation_model->addStudentJourneys($input);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully assigned the journey.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to assign journey.');
      }
      redirect('transportation/mass_allocate_journeys');
    }

    public function submitStudentEditJourneys() {
      $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();
      $result = $this->transportation_model->submitStudentEditJourneys($input);
      echo $result ? '1' : '0';
      // if($status) {
      //   $this->session->set_flashdata('flashSuccess', 'Successfully assigned the journey.');
      // } else {
      //   $this->session->set_flashdata('flashError', 'Failed to assign journey.');
      // }
      // redirect('transportation/edit_student_journeys/'.$input['student_id']);
    }

    public function getStopsByJourney() {
      $journey_id = $_POST['journey_id'];
      $stops = $this->transportation_model->getStopsByJourneyId($journey_id);
      echo json_encode($stops);
    }

    public function getJourneysByType() {
      $type = $_POST['type'];
      $journeys = $this->transportation_model->getJourneysByType($type);
      echo json_encode($journeys);
    }

    public function getJourneyStops() {
      $journey_id = $_POST['journey_id'];
      $journeys = $this->transportation_model->getJourneyStops($journey_id);
      echo json_encode($journeys);
    }

    public function getDaysAndStops() {
      $journey_id = $_POST['journey_id'];
      $data['stops'] = $this->transportation_model->getStopsByJourneyId($journey_id);
      $data['days'] = $this->transportation_model->getJourneyDays($journey_id);
      // $data['busCapacity'] = $this->transportation_model->getBusCapacity($journey_id);
      echo json_encode($data);
    }

    public function deleteStudentJourney() {
      $std_journey_id = $_POST['std_journey_id'];
      $status = $this->transportation_model->removeStudentJourney($std_journey_id);
      echo $status;
    }

    public function deleteStaffJourney() {
      $staff_journey_id = $_POST['staff_journey_id'];
      $status = $this->transportation_model->removeStaffJourney($staff_journey_id);
      echo $status;
    }

    public function getClassStudents() {
      // $filter = $_POST['filter'];
      $classId = isset($_POST['classId']) ? $_POST['classId'] : 0;
      $classSectionId = isset($_POST['classSectionId']) ? $_POST['classSectionId'] : 0;
      $stdName = isset($_POST['stdName']) ? $_POST['stdName'] : 0;
      $admission_no = isset($_POST['admission_no']) ? $_POST['admission_no'] : 0;
      $students = $this->transportation_model->getStudentsByClassSection($classId, $classSectionId, $stdName, $admission_no);
      echo json_encode($students);
    }

    public function edit_student_journeys($stdId) {
      $stdData = $this->transportation_model->getStudentData([$stdId]);
      $data['stdData'] = $stdData[0];
      $data['section_id'] = $data['stdData']->section_id;
      $data['selectJourneys'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['journeys'] = $this->transportation_model->getStudentJourneys($stdId);
      $data['pick_days'] = array();
      $data['drop_days'] = array();
      foreach ($data['journeys'] as $day => $journeys) {
        foreach ($journeys as $key => $value) {
          if($value->journey_type == 'PICKING') {
            $data['pick_days'][] = $day;
          } else {
            $data['drop_days'][] = $day;
          }
        }
      }
      // echo "<pre>"; print_r($data['journeys']); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/edit_student_journeys_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/edit_student_journeys_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/edit_student_journeys';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function addStudentOverride() {
      $input = $this->input->post();
      $status = $this->transportation_model->addStudentOverride($input);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully added override.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to add override.');
      }
      redirect('transportation/student_journey_override/'.$input['student_id']);
    }
    
    public function get_bus_names(){
      
    }

    public function logs() {
      $data['date_ini'] = date('d-m-Y');
      $data['bus_ini'] = '1';
      if(isset($_POST['date'])) {
        $data['date_ini'] = $_POST['date'];
        $data['bus_ini'] = $_POST['bus'];
      }
      // echo $data['date'];die();
      $data['logs'] = $this->transportation_model->getTxLogs($data['date_ini'], $data['bus_ini']);
      $data['buses'] = $this->transportation_model->getBusNames();
      // echo "<pre>"; print_r($data); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/logs/tx_logs_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/logs/tx_logs_mobile';
      }else{
        $data['main_content'] = 'transportation/logs/tx_logs';	
      }
      // echo'<pre>';print_r($data);die();
      $this->load->view('inc/template', $data);
    }

    public function log_details($logId) {
      $data['log'] = $this->transportation_model->getLogData($logId);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/logs/tx_log_detail_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/logs/tx_log_detail_mobile';
      }else{
        $data['main_content'] = 'transportation/logs/tx_log_detail';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function staff_journeys() {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/staff_journeys/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/staff_journeys/index_mobile';
      }else{
        $data['main_content'] = 'transportation/staff_journeys/index';  	
      }
      $this->load->view('inc/template', $data);
    }

    public function get_staff_journeys(){
      $staff_name = $this->input->post('staff_name');
      $staffs = $this->transportation_model->getStaffJourneys($staff_name);

      $staffData = array();
      foreach ($staffs as $staff) {
          $staffId = $staff->staff_id;

          if (!isset($staffData[$staffId])) {
              $staffData[$staffId] = array(
                  'staffName' => $staff->staffName,
                  'staff_id' => $staff->staff_id,
                  'journeys' => array()
              );
          }

          $jType = '<i style="color:#1bea1b;" class="fa fa-arrow-up"></i>&nbsp;&nbsp;PICK';
          if ($staff->journey_type == 'DROPPING') {
              $jType = '<i style="color:yellow;" class="fa fa-arrow-down"></i>&nbsp;&nbsp;DROP';
          }

          if ($staff->journey_type != '') {
              $staffData[$staffId]['journeys'][$staff->day][] = $jType . ' ' . $staff->journey_name . ' - Stop: ' . $staff->stop_name;
          }
      }

      $staffData = array_values($staffData);
      echo json_encode($staffData);
    }

    public function assign_staff_journeys($staffId=0) {
      if($staffId == 0)
        $staffId = $this->input->post('staff_id');
      // echo $staffId;die();
      $data['staff_id'] = $staffId;
      $this->load->model('staff/Staff_Model');
      $data['selectJourneys'] = $this->transportation_model->getJourneysByType('PICKING');
      $data['journeys'] = $this->transportation_model->getStaffJourney($staffId);
      $data['staffName'] = $this->Staff_Model->getStaffName($staffId);
      $data['pick_days'] = array();
      $data['drop_days'] = array();
      foreach ($data['journeys'] as $day => $journeys) {
        foreach ($journeys as $key => $value) {
          if($value->journey_type == 'PICKING') {
            $data['pick_days'][] = $day;
          } else {
            $data['drop_days'][] = $day;
          }
        }
      }
      // echo "<pre>"; print_r($data); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/staff_journeys/assign_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/staff_journeys/assign_mobile';
      }else{
        $data['main_content'] = 'transportation/staff_journeys/assign';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function save_staff_journeys() {
      $input = $this->input->post();
      $status = $this->transportation_model->saveStaffJourneys();
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully assigned the journey.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to assign journey.');
      }
      redirect('transportation/assign_staff_journeys/'.$input['staff_id']);
      // echo '<pre>'; print_r($input); die();
    }

    public function communicateTransportInfo() {
      $input = $this->input->post();
      $input = $this->input->post();
      $school_name = $this->settings->getSetting('school_name');
      $input_array = array(
        'mode' => $input['communication_type'], 
        'title' => $school_name, 
        'message' => $input['test_message'], 
        'source' => 'Transportation',
        'student_url' => site_url('dashboard'),
        'student_ids' => explode(',', $input['student_ids']),
        'staff_ids' => explode(',', $input['staff_ids'])
      );
      
      $this->load->helper('texting_helper');
      $this->load->helper('sms_v2_helper');
      $response = sendText($input_array);

      if($response['success'] != '') {
        $this->session->set_flashdata('flashSuccess', $response['success']);
      }
      if($response['error'] != '') {
        $this->session->set_flashdata('flashError', $response['error']);
      }
      redirect('transportation/buswise_report_format_one');
    }

    public function mass_journeys_changes() {
      $data['buses'] = $this->transportation_model->getBusNames();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/journey_changes_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/journey_changes_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/journey_changes';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function change_journeys() {
      $input = $this->input->post();
      $status = $this->transportation_model->changeJourney($input);
      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Successfully changed journeys.');
      } else {
        $this->session->set_flashdata('flashError', 'Failed to change journeys.');
      }
      redirect('transportation/mass_journeys_changes');
      // echo "<pre>"; print_r($input); die();
    }

    public function journey_mismatch() {
      $data['classes'] = $this->transportation_model->getClasses();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/student_journeys/journey_mismatch_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/student_journeys/journey_mismatch_mobile';
      }else{
        $data['main_content'] = 'transportation/student_journeys/journey_mismatch';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function getMismatchedJourneys() {
      $class_id = $_POST['class_id'];
      $data = $this->transportation_model->getMismatchJourneys($class_id);
      echo json_encode($data);
    }

    public function resolveMismatch() {
      $mismatchId = $_POST['mismatchId'];
      echo $this->transportation_model->resolveMismatch($mismatchId);
    }

    public function daily_track() {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/daily_tracking/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/daily_tracking/index_mobile';
      }else{
        $data['main_content'] = 'transportation/daily_tracking/index';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function daily_track_summary() {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/daily_tracking_summary/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/daily_tracking_summary/index_mobile';
      }else{
        $data['main_content'] = 'transportation/daily_tracking_summary/index';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function get_journey_data() {
      $journey_type = 'All';
      if (!empty($_POST['filter_pick_drop'])) {
        $journey_type = $_POST['filter_pick_drop'];
      }

      $data['journeys'] = $this->transportation_model->getJourneysByType($journey_type);
      echo json_encode($data);
    }

    public function get_student_attendance_summary_across_days() {
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $journey_ids = $_POST['journey_ids'];
      $filter_pick_drop = $_POST['filter_pick_drop'];
      // echo '<pre>';print_r($_POST);die();
      $data = $this->transportation_model->get_student_attendance_summary_across_days($from_date, $to_date, $filter_pick_drop, $journey_ids);

      echo json_encode($data);
    }

    public function getDailyJourneys() {
      $journey_type = $_POST['journey_type'];
      $date = $_POST['date'];
      $journeys = $this->transportation_model->getDailyJourneys($journey_type, $date);
      echo json_encode($journeys);
    }

    public function dailyTrackDetails($journey_id, $date) {
      $data['date'] = $date;
      $details = $this->transportation_model->getDailyJourneyDetails($journey_id, $date);
      $data['journey'] = $details['journey'];
      $data['eta'] = $details['eta'];
      $data['attendance'] = $details['attendance'];
      $data['geofence'] = $details['geofence'];
      $data['number_of_students'] = $details['number_of_students'];
      $data['present_students'] = $details['present_students'];
      $data['number_of_other_students'] = $details['number_of_other_students'];
      // echo '<pre>'; print_r($data); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/daily_tracking/tracking_details_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/daily_tracking/tracking_details_mobile';
      }else{
        $data['main_content'] = 'transportation/daily_tracking/tracking_details';     	
      }
      $this->load->view('inc/template', $data);
    }

    public function student_wise_report() {
      $this->load->model('student/Student_Model');
      $data['studentNames'] = $this->transportation_model->getstudentNames();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/reports/student_wise_report_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/reports/student_wise_report_mobile';
      }else{
        $data['main_content'] = 'transportation/reports/student_wise_report';   	
      }
      $this->load->view('inc/template', $data);
    }

    public function studentJourneyDetails($student_id) {
      $student = $this->transportation_model->getStudentData([$student_id]);
      $data['student'] = $student[0];
      $journeys = $this->transportation_model->getStudentJourneys($student_id);
      $rfid = $this->transportation_model->getRFID($student_id);
      $today = date('Y-m-d');
      $data['attendance_present'] = 0;
      $tapData = array();
      $todays_attendance = array();
      if($rfid) {
        $todays_attendance = $this->transportation_model->rfidJourneys($rfid, $today);
        $date = $today;
        for ($i=1; $i <= 7; $i++) { 
          $date = date('Y-m-d', strtotime($date.' -1 day'));
          $day = strtoupper(date('l', strtotime($date)));
          if($day == 'SUNDAY') {
            continue;
          }
          $prev = $this->transportation_model->rfidJourneys($rfid, $date);
          if(!empty($prev)) {
            $tapData[$day] = array('date' => $date, 'journeys' => $prev);
            $data['attendance_present'] = 1;
          }
        }
      }

      $dailyJourneys = [];
      foreach ($journeys as $day => $journeys) {
        $dailyJourneys[$day] = array();
        $dailyJourneys[$day]['date'] = '';
        $dailyJourneys[$day]['journeys'] = array();
        foreach ($journeys as $key => $journey) {
          if(!array_key_exists($journey->journey_type, $dailyJourneys[$day])) {
            $dailyJourneys[$day]['journeys'][$journey->journey_type] = array();
          }
          $journey->is_tapped = 0;
          $journey->taps = array();
          $journey->tapped_journey = $journey->journey_name;
          $dailyJourneys[$day]['journeys'][$journey->journey_type][] = $journey;
        }
      }

      $data['todays_journey'] = $todays_attendance;
      if(!isset($todays_attendance['PICKING'])) {
          $day = strtoupper(date('l', strtotime($today)));
          if(isset($dailyJourneys[$day]['journeys']['PICKING'])) {
              $data['todays_journey']['PICKING'] = $dailyJourneys[$day]['journeys']['PICKING'];
          }
      }
      if(!isset($todays_attendance['DROPPING'])) {
          $day = strtoupper(date('l', strtotime($today)));
          if(isset($dailyJourneys[$day]['journeys']['DROPPING'])) {
              $data['todays_journey']['DROPPING'] = $dailyJourneys[$day]['journeys']['DROPPING'];
          }
      }

      foreach ($dailyJourneys as $day => $journeys) {
        if(isset($tapData[$day])) {
          $dailyJourneys[$day]['date'] = date('d-M', strtotime($tapData[$day]['date']));
          foreach ($journeys['journeys'] as $type => $journey) {
            if(array_key_exists($type, $tapData[$day]['journeys'])) {
              foreach ($journey as $key => $jval) {
                if(array_key_exists($key, $tapData[$day]['journeys'][$type])) {
                  $dailyJourneys[$day]['journeys'][$type][$key]->is_tapped = 1;
                  if($tapData[$day]['journeys'][$type][$key]->journeyId != $jval->journeyId) {
                    $dailyJourneys[$day]['journeys'][$type][$key]->tapped_journey = $tapData[$day]['journeys'][$type][$key]->journey_name;
                  }
                  $dailyJourneys[$day]['journeys'][$type][$key]->taps = $tapData[$day]['journeys'][$type][$key]->taps;
                }
              }
            }
          }
        }
      }

      $data['all_journeys'] = $dailyJourneys;
      // echo '<pre>'; print_r($dailyJourneys); 
      // echo '<pre>'; print_r($tapData);
      // echo '<pre>'; print_r($data); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/reports/student_journey_details_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/reports/student_journey_details_mobile';
      }else{
        $data['main_content'] = 'transportation/reports/student_journey_details';	
      }
      $this->load->view('inc/template', $data);
    }

    public function getStudentsOfJourneys() {
      $journeyId = $_POST['journey_id'];
      $students = $this->transportation_model->getStudentsOfJourneys($journeyId);
      echo json_encode($students);
    }

    public function allocateStudentJourneys() {
      echo $this->transportation_model->allocateStudentJourneys();
    }

    public function saveStudentStops() {
      echo $this->transportation_model->saveStudentStops();
    }

    public function journey_time() {
      $data['buses'] = $this->transportation_model->getBusNames();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'transportation/reports/journey_times_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'transportation/reports/journey_times_mobile';
      }else{
        $data['main_content'] = 'transportation/reports/journey_times';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function getJourneyTimes() {
      $journeyId = $_POST['journey_id'];
      $students = $this->transportation_model->getJourneyTimes($journeyId);
      echo json_encode($students);
      // echo '<pre>'; print_r($students); die();
    }

    public function deleteStudentStops(){
      $journey_id = $_POST['journey_id'];
      $entity_source_id = $_POST['entity_source_id'];
      // echo "<pre>"; print_r($journey_id); 
      // echo "<pre>"; print_r($entity_source_id); die();
      echo $this->transportation_model->deletestdmassJourney($journey_id, $entity_source_id);
    }

    public function getaddClassStudents(){
      $section_id = $_POST['section_id'];
      $result = $this->transportation_model->get_class_section_student_data($section_id);
      echo json_encode($result);
    }

    public function export_student_data() {
      $data['buses'] = $this->transportation_model->getBusNames();
      $data['stops'] = $this->transportation_model->getexport_Stops();
      $data['main_content'] = 'transportation/reports/export_student_data';     
      $this->load->view('inc/template', $data);
    }

    public function generate_export_student() {
      $days = $_POST['days'];
      $status = $_POST['status'];
      $_routes = $_POST['_routes'];
      $result = $this->transportation_model->get_generate_export_student($days, $status, $_routes);
      // echo "<pre>"; print_r($result); die();
      echo json_encode($result);
    }

    public function export_staff_data() {
      $data['buses'] = $this->transportation_model->getBusNames();
      $data['stops'] = $this->transportation_model->getexport_Stops();
      $data['main_content'] = 'transportation/reports/export_staff_data';     
      $this->load->view('inc/template', $data);
    }

    public function generate_export_staff(){
      $days = $_POST['days'];
      $status = $_POST['status'];
      $_routes = $_POST['_routes'];
      $result = $this->transportation_model->get_generate_export_staff($days, $status, $_routes);
      echo json_encode($result);
    }

    public function addStopTime(){
      $input = $this->input->post();
      $status = $this->transportation_model->addStopTime($input);
      echo json_encode($status);
    }

    public function get_stops_sorted_by_timings(){
      $journey_id = $_POST['journey_id'];
      $result = $this->transportation_model->get_stops_sorted_by_timings($journey_id);
      echo json_encode($result);
    }

    public function removeStopTime(){
      $input = $this->input->post();
      $status = $this->transportation_model->removeStopTime($input);
      echo json_encode($status);
    }

    public function delete_all_journeys_by_std_id() {
      $std_id = $_POST['std_id'];
      echo $this->transportation_model->delete_all_journeys_by_std_id($std_id);
    }

    public function mass_upload_stops() {
      echo $this->transportation_model->mass_upload_stops();
    }
    
    public function mass_add_journey_stops() {
      echo $this->transportation_model->mass_add_journey_stops();
    }

    public function getPassengersInJourneys(){
      $thingId = $_POST['thing_id'];
      $journeyId = $_POST['journey_id'];
      $days = $_POST['days'];
      // echo "<pre>";print_r($journeyId);die();
      $students = [];
      $staff = [];
      if($journeyId == 'all'){
        $students = $this->transportation_model->getPassengersInBothJourneysStudents($thingId, $days);
        $staff = $this->transportation_model->getPassengersInBothJourneysStaffs($thingId, $days);
      }else{
        $students = $this->transportation_model->getPassengersInJourneysStudents($journeyId, $days);
        $staff = $this->transportation_model->getPassengersInJourneysStaffs($journeyId, $days);
      }
      $merageArry = array_merge($students, $staff);
      $driver = $this->transportation_model->getDriverByBus($thingId);
      $bus = $this->transportation_model->getThingById($thingId);
      echo json_encode(array("passengers" => $merageArry, "driver" => $driver, 'bus' => $bus));
    }

    public function mass_assign_student_journey(){
      $input = $this->input->post();
      echo  json_encode($this->transportation_model->mass_assign_student_journey($input));
    }

    public function validate_student_pickup_journeys(){
      $input = $this->input->post();
      $status = $this->transportation_model->validate_student_pickup_journeys($input);
      echo json_encode($status);
    }

    public function validate_student_drop_journeys(){
      $input = $this->input->post();
      $status = $this->transportation_model->validate_student_drop_journeys($input);
      echo json_encode($status);
    }

    public function get_all_journeys_of_the_bus(){
      $thingId = $_POST['thing_id'];
      $journey_id = $_POST['journey_id'];
      $journeys = $this->transportation_model->get_all_journeys_of_the_bus($thingId, $journey_id);
      echo json_encode($journeys);
    }

    public function get_bus_tracking_url(){
      if(!isset($_POST['bus_id']) || empty($_POST['bus_id'])){
        echo json_encode(false);
        return;
      }
      $thingId = $_POST['bus_id'];
      $url = $this->transportation_model->get_bus_tracking_url($thingId);
      echo json_encode($url);
    }
}