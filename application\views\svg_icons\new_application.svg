<svg width="151" height="151" viewBox="0 0 151 151" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="0" y="0" width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(22.9px);clip-path:url(#bgblur_0_1011_32_clip_path);height:100%;width:100%"></div></foreignObject><circle data-figma-bg-blur-radius="45.8" cx="69" cy="69" r="58" fill="#FAF8FF"/>
<g opacity="0.4" filter="url(#filter0_f_1011_32)">
<circle cx="75.1052" cy="75.1052" r="30.9624" fill="#623CE7"/>
</g>
<g filter="url(#filter1_i_1011_32)">
<circle cx="69" cy="69" r="29" fill="#FFFEFF"/>
</g>
<rect x="58" y="59.75" width="22" height="22" rx="3" fill="white" stroke="#623CE7" stroke-width="2"/>
<mask id="path-5-inside-1_1011_32" fill="white">
<path d="M69 55.25C69.8539 55.25 70.5948 55.7264 70.9755 56.4274C71.0766 56.6136 71.2607 56.75 71.4726 56.75H72.25C73.3546 56.75 74.25 57.6454 74.25 58.75V60.75C74.25 61.8546 73.3546 62.75 72.25 62.75H65.75C64.6454 62.75 63.75 61.8546 63.75 60.75V58.75C63.75 57.6454 64.6454 56.75 65.75 56.75H66.5274C66.7393 56.75 66.9234 56.6136 67.0245 56.4274C67.4051 55.7264 68.1461 55.25 69 55.25Z"/>
</mask>
<path d="M69 55.25C69.8539 55.25 70.5948 55.7264 70.9755 56.4274C71.0766 56.6136 71.2607 56.75 71.4726 56.75H72.25C73.3546 56.75 74.25 57.6454 74.25 58.75V60.75C74.25 61.8546 73.3546 62.75 72.25 62.75H65.75C64.6454 62.75 63.75 61.8546 63.75 60.75V58.75C63.75 57.6454 64.6454 56.75 65.75 56.75H66.5274C66.7393 56.75 66.9234 56.6136 67.0245 56.4274C67.4051 55.7264 68.1461 55.25 69 55.25Z" fill="white"/>
<path d="M69 55.25L69 53.25L69 53.25L69 55.25ZM74.25 58.75L76.25 58.75L76.25 58.75L74.25 58.75ZM65.75 62.75L65.75 64.75H65.75V62.75ZM63.75 58.75L61.75 58.75V58.75H63.75ZM65.75 56.75L65.75 54.75L65.75 54.75L65.75 56.75ZM67.0245 56.4274L65.2669 55.473L67.0245 56.4274ZM70.9755 56.4274L72.7331 55.473L70.9755 56.4274ZM69 55.25V57.25C69.0893 57.25 69.1719 57.2971 69.2179 57.3818L70.9755 56.4274L72.7331 55.473C72.0178 54.1557 70.6186 53.25 69 53.25V55.25ZM71.4726 56.75V58.75H72.25V56.75V54.75H71.4726V56.75ZM72.25 56.75V58.75L72.25 58.75L74.25 58.75L76.25 58.75C76.25 56.5409 74.4591 54.75 72.25 54.75V56.75ZM74.25 58.75H72.25V60.75H74.25H76.25V58.75H74.25ZM74.25 60.75H72.25V62.75V64.75C74.4591 64.75 76.25 62.9591 76.25 60.75H74.25ZM72.25 62.75V60.75H65.75V62.75V64.75H72.25V62.75ZM65.75 62.75L65.75 60.75L65.75 60.75H63.75H61.75C61.75 62.9591 63.5409 64.75 65.75 64.75L65.75 62.75ZM63.75 60.75H65.75V58.75H63.75H61.75V60.75H63.75ZM63.75 58.75L65.75 58.75L65.75 58.75L65.75 56.75L65.75 54.75C63.5409 54.75 61.75 56.5409 61.75 58.75L63.75 58.75ZM65.75 56.75V58.75H66.5274V56.75V54.75H65.75V56.75ZM67.0245 56.4274L68.7821 57.3818C68.8281 57.2971 68.9107 57.25 69 57.25L69 55.25L69 53.25C67.3814 53.25 65.9822 54.1557 65.2669 55.473L67.0245 56.4274ZM66.5274 56.75V58.75C67.6387 58.75 68.4183 58.0517 68.7821 57.3818L67.0245 56.4274L65.2669 55.473C65.4285 55.1754 65.8398 54.75 66.5274 54.75V56.75ZM70.9755 56.4274L69.2179 57.3818C69.5817 58.0517 70.3613 58.75 71.4726 58.75V56.75V54.75C72.1602 54.75 72.5715 55.1754 72.7331 55.473L70.9755 56.4274Z" fill="#623CE7" mask="url(#path-5-inside-1_1011_32)"/>
<path d="M63.4974 67.25H74.4974" stroke="#623CE7" stroke-width="2" stroke-linecap="round"/>
<path d="M63.4974 73.25H74.4974" stroke="#623CE7" stroke-width="2" stroke-linecap="round"/>
<defs>
<clipPath id="bgblur_0_1011_32_clip_path" transform="translate(0 0)"><circle cx="69" cy="69" r="58"/>
</clipPath><filter id="filter0_f_1011_32" x="0.142822" y="0.142822" width="149.925" height="149.925" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="22" result="effect1_foregroundBlur_1011_32"/>
</filter>
<filter id="filter1_i_1011_32" x="40" y="40" width="58" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="17.95"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.384314 0 0 0 0 0.235294 0 0 0 0 0.905882 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1011_32"/>
</filter>
</defs>
</svg>
