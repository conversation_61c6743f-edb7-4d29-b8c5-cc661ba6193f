<ul class="breadcrumb">
  <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
  <li class="active">Manage Skills</li>
</ul>


<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Skills
                    </h3>
                </div>
                <div class="col-md-3">
                    <a href="" class="new_circleShape_res" style="background-color: #fe970a;float: right;" data-toggle="modal" data-target="#addNewSkillModal" title="Add New Skill">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <div id="skillsDiv">
                <div class='no-data-display'>Loading...</div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addNewSkillModal" tabindex="-1" role="dialog" aria-labelledby="addNewSkillModal" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 52%;margin-top: 2% !important;margin: auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="addNewSkillModal">Add New Skill</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetForm()">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="card-body">
                    <div class="col-md-12 pb-3">
                        <form action="" data-parsley-validate="true" class="form-horizontal mb-2" id="skills">
                            <div class="form-group">
                                <label for="skill_name" class="col-md-2">Skill Name <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input type="text" name="skill_name" id="skill_name" class="form-control" placeholder="Enter Skill Name" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2">Description</label>
                                <div class="col-md-10">
                                    <textarea name="skill_description" id="skill_description" class="form-control" placeholder="Enter Description"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetForm()">Close</button>
              <button type="button" id="add_skill" onclick="submitSkill()" class="btn btn-primary mt-0">Submit</button> 
            </div>
        </div>
    </div>
</div>

<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }

  .new_circleShape_res1 {
    padding: 5px 8px;
    border-radius: 50% !important;
    font-size: 16px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
  }

  .loader-background {
    width: 100%;
    height: 100%;
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #fff;
    border-radius: 8px;
  }

  ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
  }

  /* Create a custom scrollbar */
  ::-webkit-scrollbar-track {
    background-color: #f2f2f2;
    border-radius: 10px;
  }

  /* Create a thumb for the scrollbar */
  ::-webkit-scrollbar-thumb {
    /* background-color: #007bff; */
    background-color: #777;
    border-radius: 0px;
  }

  /* Make the scrollbar visible when hovering over the track */
  ::-webkit-scrollbar-track-piece-over:hover {
    background-color: #F5F5F5;
  }

  /* Make the scrollbar thumb visible when hovering over it */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #777;
  }

  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
  }

  ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
  }

  .ellipsis{
    display:none;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function () {
  getAllSkills();
});

function resetForm(){
  var form = $('#skills');
  form[0].reset();
  form.parsley().reset();
}

function submitSkill() {
  const form = $("#skills");
  if (form.parsley().validate()) {
    $("#add_skill").prop('disabled', true).html('Please wait...');
    var skill_name = $("#skill_name").val();
    var skill_description = $("#skill_description").val();

    $.ajax({
      url: '<?php echo site_url('academics/skills/submitSkill') ?>',
      type: 'post',
      data: { 'skill_name': skill_name, 'skill_description': skill_description },
      beforeSend: function () {
        $('#opacity').css('opacity', '0.5');
        $('#loader').show();
      },
      success: function (data) {
        data = JSON.parse(data);
        if(data == -1){
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Skill already exists!",
          }).then(e => {
            $("#add_skill").prop('disabled', false).html('Submit');
            resetForm();
            $('#loader').hide();
            $('#opacity').css('opacity', '');
          });
          return;
        }
        if (data == 1) {
          Swal.fire({
            icon: "success",
            title: "Skill Added",
            text: "New skill added successfully!",
          }).then(e => {
            $("#add_skill").prop('disabled', false).html('Submit');
            $('#addNewSkillModal').modal('hide');
            resetForm();
            getAllSkills();
          });
        } else {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong!",
          }).then(e => {
            $("#add_skill").prop('disabled', false).html('Submit');
            $('#addNewSkillModal').modal('hide');
            resetForm();
            getAllSkills();
          });
        }
      },
      complete: function () {
        $('#loader').hide();
        $('#opacity').css('opacity', '');
      },
    });
  }
}

function getAllSkills() {
  $.ajax({
    url: '<?php echo site_url('academics/skills/getAllSkills') ?>',
    type: 'post',
    success: function (data) {
      let parsedData = JSON.parse(data);
      if (parsedData.length == 0) {
        $("#skillsDiv").html('<div class="no-data-display">No Skills Found</div>');
        return;
      } else {
        $("#skillsDiv").html(constructSkillsTable(parsedData));
        $('#skillsTable').DataTable({
          "ordering": false,
          "pageLength": 10,
          "lengthMenu": [10, 20, 50, 100, 200, 500],
          "dom": '<"top"fl>rt<"bottom"ip><"clear">',
          "language": {
            "search": "Search:",
            "paginate": {
              "previous": "Previous",
              "next": "Next",
              "first": "First",
              "last": "Last"
            }
          }
        });

      }
    },
    error: function (err) {
      console.log(err);
      $("#skillsDiv").html('<div class="no-data-display">Something went wrong!, Please try again later.</div>');
    }
  });
}

function constructSkillsTable(data) {
  let html = '';
  html += '<table class="table table-bordered table-striped table-hover" id="skillsTable">';
  html += '<thead>';
  html += '<tr>';
  html += '<th>Skill Name</th>';
  html += '<th>Description</th>';
  html += '<th>Created By</th>';
  // html += '<th>Action</th>';
  html += '</tr>';
  html += '</thead>';
  html += '<tbody>';
  for (let i = 0; i < data.length; i++) {
    html += '<tr>';
    html += '<td>' + data[i].skill_name + '</td>';
    html += '<td>' + (data[i].skill_description.trim() == '' ? '-' : data[i].skill_description.trim()) + '</td>';
    html += '<td>' + data[i].friendly_name + '</td>';
    // html += `<td><button onclick="deleteSkill(${data[i].id}, '${data[i],skill_name}')" class="btn btn-danger"><span class="fa fa-trash-o mr-0"></span></button></td>`;
    html += '</tr>';
  }
  html += '</tbody>';
  html += '</table>';
  return html;
}

// function deleteSkill(id) {
//   Swal.fire({
//     title: 'Are you sure?',
//     text: "You won't be able to revert this!",
//     icon: 'warning',
//     showCancelButton: true,
//     confirmButtonColor: '#3085d6',
//     cancelButtonColor: '#d33',
//     confirmButtonText: 'Yes, delete it!'
//   }).then((result) => {
//     if (result.isConfirmed) {
//       $.ajax({
//         url: '<?php //echo site_url('academics/skills/deleteSkill') ?>',
//         type: 'post',
//         data: { 'id': id },
//         success: function (data) {
//           let parsedData = JSON.parse(data);
//           if (data == 1) {
//             Swal.fire({
//               icon: "success",
//               title: "Skill Deleted",
//               text: "Skill deleted successfully!",
//             }).then(e => {
//               getAllSkills();
//             });
//           } else {
//             Swal.fire({
//               icon: "error",
//               title: "Oops...",
//               text: "Something went wrong!",
//             }).then(e => {
//               getAllSkills();
//             });
//           }
//         },
//         error: function (data) {
//           console.log(data);
//         }
//       });
//     }
//   });
// }
</script>