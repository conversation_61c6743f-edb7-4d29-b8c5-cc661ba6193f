<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Skill_model extends CI_Model {
	private $yearId;
	public function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
  }

  public function Kolkata_datetime() {
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function getSkills(){
    $stakeholder_id = $this->authorization->getAvatarStakeHolderId();
    $created_by = $this->db->select("concat(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as name")
    ->from('staff_master s')
    ->where('s.id', $stakeholder_id)
    ->get()->row();
    $result = $this->db->select('s.*, a.friendly_name, ifnull(s.skill_description, "") as skill_description')
    ->from('lp_skills s')
    ->join('avatar a', 'a.id = s.created_by', 'left')
    ->order_by("a.friendly_name, s.skill_name")
    ->get()->result();
    return $result;
  }

  public function submitSkill(){
    $skillName = trim($_POST['skill_name']);
    $skillNameValidation = $this->db->select('skill_name')
    ->from('lp_skills')
    ->where('skill_name', $skillName)
    ->get()->row();
    if($skillNameValidation){
      return -1;
    }
    $data = array(
      'skill_name' => trim($_POST['skill_name']),
      'skill_description' => trim($_POST['skill_description']) == '' ? null : $_POST['skill_description'],
      'created_by' => $this->authorization->getAvatarId(),
      'created_on' => $this->Kolkata_datetime()
    );
    return $this->db->insert('lp_skills',$data);
  }
}