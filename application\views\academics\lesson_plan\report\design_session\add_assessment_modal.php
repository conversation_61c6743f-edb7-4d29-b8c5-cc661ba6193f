<div class="modal fade" id="add_assessment" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 50%; margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Assessment</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="assessment_session_id" id="assessment_session_id">
                <div class="form-group">
                    <label for="assessment_type" class="control-label">Type <font style="color:red;">*</font></label>
                    <select name="assessment_type" class="form-control" id="assessment_type">
                        <option value="">Assessment Type</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 18%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <span id="assessmentTypeError" style="display: none;"></span>
                </div>

                <div class="form-group">
                    <label for="remarks" class="control-label">Remarks</label>
                    <textarea class="form-control" name="remarks" id="remarks" style="height: 11rem;" placeholder="Enter Remarks"></textarea>
                    <span id="remarksError" style="display: none;"></span>
                </div>

                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_assessment_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_assessment_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="assessment()">Update</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // $("#add_assessment").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    get_assessment_details();

    function get_assessment_details() {
        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/get_assessment_details'); ?>',
            type: 'post',
            data: {},
            async: false,
            success: function (data) {
                let assessment_types = $.parseJSON(data);
                if(assessment_types.length <= 0){
                    $("#assessment_type").html(`<option disabled selected>No Assessment Type Found</option>`);
                    return;
                }
                let options = `<option value="">Assessment Type</option>`
                assessment_types.forEach(i => {
                    options += `<option value="${i.id}">${i.name}</option>`
                })
                $("#assessment_type").html(options);
            }
        });
    }

    $("#assessment_type,#remarks").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#assessmentTypeError,#remarksError").html("").hide();
            if(!$("#assessment_type").val()){
                $("#assessmentTypeError").html("Please Select Assessment Type").css("color","red").show();
                return;
            }
            // if(!$("#remarks").val()){
            //     $("#remarksError").html("Please Enter Remarks").css("color","red").show();
            //     return;
            // }
            addAssessment();
            loadAssessments();
        }
    })

    function addAssessment() {
        const session_id = $("#assessment_session_id").val();
        const assessment_type_id = $("#assessment_type").val();
        const new_assessment_remarks = $("#remarks").val();
        const visible_to_students = $("#visible_assessment_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/add_new_assessment"); ?>',
            type: "POST",
            data: { session_id, assessment_type_id, new_assessment_remarks,visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_assessment").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        getSessionData(session_id);
                        $("#assessment_type").val("");
                        $("#remarks").val("");
                        loadAssessments();
                        showMainModal();
                    });
                } else {
                    $("#add_assessment").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed to save activity",
                        text: "Please try again later.",
                    }).then(() => {
                        $("#add_assessment").modal('show');
                    });
                }
            },
            error: function (error) {
                console.log(error);
                $("#add_assessment").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed to save activity",
                    text: "Please try again later.",
                }).then(() => {
                    $("#add_assessment").modal('show');
                });
            }
        })
    }

    function assessment() {
        $("#assessmentTypeError,#remarksError").html("").hide();
        if(!$("#assessment_type").val()){
            $("#assessmentTypeError").html("Please Select Assessment Type").css("color","red").show();
            return;
        }
        // if(!$("#remarks").val()){
        //     $("#remarksError").html("Please Enter Remarks").css("color","red").show();
        //     return;
        // }
        addAssessment();
        loadAssessments();
    }
</script>