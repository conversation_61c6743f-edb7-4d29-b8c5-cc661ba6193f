<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('feesv2/fees_dashboard'); ?>">Fees Master</a></li>
    <li>Email Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header" style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Invoice Statement Email Report
                </h3>
            </div>
        </div>
        <div class="panel-body">
            
            <div class="col-md-3 form-group">
                <label for="reportrange">Date Range</label>
                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                    <span></span>
                    <input type="hidden" id="from_date">
                    <input type="hidden" id="to_date">
                </div>
            </div>

            <div class="col-md-2">
                <label for="selected_inovice_type">Invoice Type</label>
                <select name="invoice_type" id="selected_inovice_type" class="form-control">
                    <option value="Invoice">Invoice</option>
                    <option value="Statement">Statement</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="filter">Filter Report</label>
                <select name="filter" id="filter" class="form-control" onchange="show_filter_div();">
                    <option value="0">Show All</option>
                    <option value="class">Class</option>
                    <!-- <option value="section">Class / Section</option> -->
                    <option value="student">Student Name</option>
                    <option value="admission">Admission Number</option>
                </select>
            </div>
            <div class="col-md-3 class section hide_class" style="display: none;">
                <label for="selected_class_id">Select Class</label>
                <select name="filter" id="selected_class_id" class="form-control" onchange="get_sections();">
                    <option value="0">Select...</option>
                </select>
            </div>
            <div class="col-md-3 section hide_class" style="display: none;">
                <label for="selected_class_section_id">Select Section</label>
                <select name="filter" id="selected_class_section_id" class="form-control">
                    
                </select>
            </div>
            <div class="col-md-3 student hide_class" style="display: none;">
                <label for="student_name_id">Student Name</label>
                <input type="text" class="form-control" id="student_name_id">
            </div>
            <div class="col-md-3 admission hide_class" style="display: none;">
                <label for="admission_number_id">Admission Number</label>
                <input type="text" class="form-control" id="admission_number_id">
            </div>
            <div class="col-md-2" id="filter_button_div" style="">
                <label for="" style="opacity: 0;">F</label>
                <button  class="btn btn-primary form-control" onclick="get_email_report()">Get Report</button>
                <input type="hidden" id="hidden_filter_type" value="0">
            </div>
        </div>
        <div class="col-md-12" id="report_div"></div>
    </div>
</div>

<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}
</style>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script>
    $("#reportrange").daterangepicker({
        ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'MM.DD.YYYY',
        separator: ' to ',
        startDate: moment(),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });
    $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

    $('#from_date').val(moment().format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));


    function show_filter_div() {
        var filter= $("#filter").val();
        $("#hidden_filter_type").val(filter);
        if(filter !== '0') {
            $(".hide_class").hide();
            $(`.${filter}`).show();
        } else {
            $(".hide_class").hide();
        }

        get_all_class();

    }

    function get_sections() {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student/get_sections'); ?>',
            type: "post",
            data: {'class_id': $("#selected_class_id").val()},
            success(data) {
                var p_data = JSON.parse(data);
                console.log(p_data);
                var sections= `<option value="0">Select...</option>`;
                for(var v of p_data) {
                    sections +=`<option value="${v.id}">${v.section_name}</option>`;
                }
                $("#selected_class_section_id").html(sections);
                
            }
        });
    }

    function get_all_class() {
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student/get_all_class'); ?>',
            type: "post",
            success(data) {
                var p_data = JSON.parse(data);
                console.log(p_data);
                var classess= `<option value="0">Select...</option>`;
                for(var v of p_data) {
                    classess +=`<option value="${v.id}">${v.class_name}</option>`;
                }
                $("#selected_class_id").html(classess);
            }
        });

    }

    function get_email_report() {
        $("#report_div").html('<br><br>Loading...');
        var filter_type= $("#hidden_filter_type").val();
        var selected_inovice_type= $("#selected_inovice_type").val();

        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();

        let id;
        switch(filter_type) {
            case 'class': {
                id= $("#selected_class_id").val();
                break;
            }
            case 'section': {
                id= $("#selected_class_section_id").val();
                break;
            }
            case 'student': {
                id= $("#student_name_id").val();
                break;
            }
            case 'admission': {
                id= $("#admission_number_id").val();
                break;
            }          
            default: {
                id= 0;
                break;
            }
        }
        $.ajax({
            url: '<?php echo site_url('feesv2/fees_student/get_email_report'); ?>',
            type: "post",
            data: {filter_type, id,selected_inovice_type,'from_date':from_date,'to_date':to_date},
            success(data) {
                var p_data = JSON.parse(data);
                console.log(p_data);
                let display_download_all_btn = p_data.length > 0 ? '' : 'd-none' ;
                let report= `<table class="table table-bordered table-hover" id="email_info_data_table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Admission No.</th>
                                        <th>Student(Class Section)</th>
                                        <th>PDF <button class="btn btn-success pull-right mr-2 ${display_download_all_btn}" style="padding: 2px 10px;" onclick="download_all_statement()" id="download_all_invoice_statement_btn">Zip All</button></th>
                                        <th>Email Details</th>
                                    </tr>
                                </thead>
                                <tbody>`;

                var srno= 1;
                for(var i in p_data) {
                    report += ` <tr>
                            <td>${srno++}</td>
                            <td>${p_data[i].admission_no}</td>
                            <td>${p_data[i].student} ${p_data[i].section_name}</td>
                            <td><a id="downloadInvoice_${p_data[i].invoice_id}" onclick="download_invoice_statement(${p_data[i].invoice_id})" style="float: right;'+downloadShow+'" data-invoice_student_name="${p_data[i].student}" data-invoice_class_sectoon="${p_data[i].section_name}" data-invoice_statement_path="${p_data[i].invoice_path}" class="btn btn-info btn-sm show_invoice">Download <span class="fa fa-cloud-download"></span></a></td>`;
                        details = p_data[i].parent_details;
                        report += '<td>';
                        if (details.length > 0) {
                            report += '<b>Sent on: </b> ' + p_data[i].created_on + '<br>';
                            for (let k = 0; k < details.length; k++) {
                                report += '<b>Name: </b> ' + details[k].parent + '(' + details[k].relation_type + ')<br>';
                                report += '<b>Email:</b> ' + details[k].p_email + '(' + details[k].email_sent_status + ')<br>';
                            }
                        }
                        report += '</td>';
                         report += '</tr>';
                }
                report += `</tbody>
                        </table>`;
                $("#report_div").html(report);

			const reportName=`email_invoice_statement_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;
                //data table here
                $("#email_info_data_table").DataTable({
                    "language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
                    },
                    "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                            "pageLength": 10,
                    dom: 'lBfrtip',
                    buttons: [
                        {
                        extend: 'excelHtml5',
                        text: 'Excel',
                        filename: reportName,
                        className: 'btn btn-info'
                        },
                        {
                        extend: 'csvHtml5',
                        text: 'CSV',
                        filename: reportName,
                        className: 'btn btn-info'
                        },
                        {
                        extend: 'pdfHtml5',
                        text: 'PDF',
                        filename: reportName,
                        className: 'btn btn-info'
                        }
                    ]
                })
                
            }
        });

    }

function download_invoice_statement(invoice_statement_id) {
    // Get file details
    const downloadBtn = event.target.closest('a');
    const student_name = downloadBtn.dataset.invoice_student_name;
    const class_section = downloadBtn.dataset.invoice_class_sectoon;
    const filename = `${student_name} ${class_section}.pdf`;
    const path = downloadBtn.dataset.invoice_statement_path;
    const baseUrl = "<?php echo $this->filemanager->getFilePath(''); ?>";
    const downloadUrl = baseUrl + path;

    var xhr = new XMLHttpRequest();
    xhr.open('GET', downloadUrl, true);
    xhr.responseType = 'blob';
    xhr.onload = function() {
        if (xhr.status === 200) {
            var blob = new Blob([xhr.response], { type: 'application/octet-stream' });
            var a = document.createElement('a');
            a.href = window.URL.createObjectURL(blob);
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(a.href);  // Clean up the URL object
        } 
    };
    xhr.onerror = function() {
        reject('Network error');
    };
    xhr.send();
}

function download_all_statement() {
    // Disable button and show loading state
    $('#download_all_invoice_statement_btn').html('Please Wait...').prop('disabled', true);
    // Get DataTable instance
    var table = $('#email_info_data_table').DataTable();
    // Get all rows data using DataTable API
    var files = [];
    // Collect all file data
    table.$('.show_invoice').each(function() {
        files.push({
            path: $(this).data('invoice_statement_path'),
            studentName: $(this).data('invoice_student_name'),
            classSection: $(this).data('invoice_class_sectoon')
        });
    });
    if (files.length > 0) {
        var zip = new JSZip();
        var totalFiles = files.length;
        var loadedFiles = 0;
        var baseUrl = "<?php echo $this->filemanager->getFilePath(''); ?>";

        // Process each file
        files.forEach(function(file) {
            var fullPath = baseUrl + file.path;
            // Create unique file name for each document
            var fileName = `${file.studentName} ${file.classSection}.pdf`;

            fetch(fullPath)
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.blob();
                })
                .then(blob => {
                    zip.file(fileName, blob);
                    loadedFiles++;

                    // When all files are processed, create and download zip
                    if (loadedFiles === totalFiles) {
                        zip.generateAsync({
                            type: 'blob',
                            compression: 'DEFLATE',
                            compressionOptions: {
                                level: 6
                            }
                        }).then(function(content) {
                            const zipName = `invoices_statements_${new Date().toISOString().slice(0,10)}.zip`;
                            const downloadUrl = URL.createObjectURL(content);
                            const link = document.createElement('a');
                            link.href = downloadUrl;
                            link.download = zipName;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(downloadUrl);
                            
                            // Reset button state
                            $('#download_all_invoice_statement_btn')
                                .html('Zip All')
                                .prop('disabled', false);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error processing file:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Download Error',
                        text: 'Failed to download one or more files',
                        timer: 2000
                    });
                    $('#download_all_invoice_statement_btn')
                        .html('Zip All')
                        .prop('disabled', false);
                });
        });
    } else {
        Swal.fire({
            icon: 'error',
            title: 'No Data Found',
            showConfirmButton: false,
            timer: 1500
        });
        $('#download_all_invoice_statement_btn')
            .html('Zip All')
            .prop('disabled', false);
    }
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>