<?php

class ManageSubjects extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    $this->load->library('filemanager');
    $this->load->model('academics/ManageSubject_model');
		$this->yearId = $this->acad_year->getAcadYearId();
  }

  public function index() {
    $data['loggedIn_staff_id']= $this->authorization->getAvatarStakeHolderId();
    
    // $data['classes'] = $this->ManageSubject_model->getClasses();
    $data['classes'] = $this->ManageSubject_model->getClassesForLms();

    $data['period_templates'] = $this->ManageSubject_model->getPeriodTemplates();
    $data["currentYearId"]=$this->yearId;
    $data["acad_year"] = $this->ManageSubject_model->get_academic_years();
    $data["acad_years_for_subject_clone"] = $this->ManageSubject_model->acad_years_for_subject_clone();

    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
    $data['is_semester_scheme'] = 0;
    if($is_semester_scheme == '1') {
      $data['is_semester_scheme'] = 1;
    }

    $data["is_add_subjects_permission_enabled"]=$this->authorization->isAuthorized("LESSON_PLAN.ADD_SUBJECTS");
    $data["is_clone_option_permission_enabled"] = $this->authorization->isAuthorized("LESSON_PLAN.CLONE_OPTIONS");
    $data["is_delete_subjects_permission_enabled"] = $this->authorization->isAuthorized("LESSON_PLAN.DELETE_ADDED_SUBJECTS");

    $data["staff_types"]=$this->settings->getSetting("staff_type");

    // if ($this->mobile_detect->isTablet()) {
    //   $data['main_content'] = 'academics/manage_subjects/index_tablet';
    // }else if($this->mobile_detect->isMobile()){
    //   $data['main_content'] = 'academics/manage_subjects/index_mobile';
    // }else{
      $data['main_content'] = 'academics/manage_subjects/index';	
    // }
    $this->load->view('inc/template', $data);
    //echo json_encode($data);
  }

  
  public function clone_previous_syllabus(){
    echo $this->ManageSubject_model->clone_previous_syllabus($_POST);
  }
  
  public function getClassesForClone(){
    $result=$this->ManageSubject_model->getClassesForClone($_POST);
    echo json_encode($result);
  }
  public function clone_previous_class(){
    echo json_encode($this->ManageSubject_model->clone_previous_class($_POST));
  }

  public function getLpSubjectsForClone(){
    $result=$this->ManageSubject_model->getLpSubjectsForClone($_POST);
    echo json_encode($result);
  }

  public function clone_individual_subject(){
    echo $this->ManageSubject_model->clone_individual_subject($_POST);
  }

  public function get_subjects(){
      $data['viewSubjects'] = $this->ManageSubject_model->callGetSubjects($_POST);
      echo json_encode($data);
  }

  public function delete_subject() {
    $subject_id = $_POST['subject_id'];
    echo json_encode($this->ManageSubject_model->delete_subject($subject_id));
  }

  public function add_edit_lesson(){
    $lesson_id = $_POST['lesson_id'];
    $lesson_name = $_POST['lesson_name'];
    $subject_id = $_POST['subject_id'];
    $mode = $_POST['mode'];

    if ($mode == 'add')
      $lesson_id = $this->ManageSubject_model->add_lesson($subject_id, $lesson_name);
    else
      $lesson_id = $this->ManageSubject_model->edit_lesson($lesson_id, $lesson_name);

    echo $lesson_id;
  }
  public function add_edit_topic() {
    $lesson_id = $_POST['lesson_id'];
    $topic_name = $_POST['topic_name'];
    $topic_id = $_POST['topic_id'];
    $mode = $_POST['mode'];

    if ($mode == 'add')
      $topic_id = $this->ManageSubject_model->add_topic($lesson_id, $topic_name);
    else
      $topic_id = $this->ManageSubject_model->edit_topic($topic_id, $topic_name);

    echo $topic_id;
  }
  public function get_lessons() {
    $subject_id = $_POST['subject_id'];
    $data = $this->ManageSubject_model->get_lessons($subject_id);
    echo json_encode($data);
  }

  public function get_sub_topics() {
    $lesson_id = $_POST['lesson_id'];
    $data = $this->questions_model->get_sub_topics($lesson_id);
    echo json_encode($data);
  }
  public function view_lesson(){
    $data['viewLessons'] = $this->ManageSubject_model->get_lesson();
    echo json_encode($data);
  }
  public function view_subtopic(){
   // $lesson_id = $_POST['lesson_id'];
    $data['viewSubTopics'] = $this->ManageSubject_model->get_subtopic();
    echo json_encode($data);
  }
  

  public function submitSubjects(){
    $data = $this->ManageSubject_model->submitSubjects();
    echo json_encode($data);
  }

  public function getNotAddedSubjects(){
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
    $class_master_id = $_POST['class_master_id'];
    $semester_main_screen_id = $_POST['semester_main_screen_id'];
    $data['subjects'] = $this->ManageSubject_model->get_not_added_subjects($class_master_id, $semester_main_screen_id, $is_semester_scheme);
    if($is_semester_scheme) {
      $data['semesters'] = $this->ManageSubject_model->get_class_semesters($class_master_id);
    }
    echo json_encode($data);
  }

  public function delete_sub_topic_by_id(){
    $sub_topic_id=$_POST['sub_topic_id'];
    echo json_encode($this->ManageSubject_model->delete_subtopic_by_id($sub_topic_id));
  }

  public function delete_lesson_by_id(){
    $lesson_id=$_POST['lesson_id'];
    echo json_encode($this->ManageSubject_model->delete_lesson_by_id($lesson_id));
  }

  public function get_class_semesters() {
    $class_master_id = $_POST['class_master_id'];
    $data['semesters'] = $this->ManageSubject_model->get_class_semesters($class_master_id);
    echo json_encode($data);
  }

  public function update_subject_semester() {
    $subject_id = $_POST['subject_id'];
    $semester_id = ($_POST['semester_id'])?$_POST['semester_id']:NULL;
    $status = $this->ManageSubject_model->update_subject_semester($subject_id, $semester_id);
    echo json_encode($status);
  }

  public function get_sections_and_subject_teachers() {
    $class_master_id = $_POST['class_master_id'];
    $lp_subject_id = $_POST['lp_subject_id'];
    $status = $this->ManageSubject_model->get_sections_and_subject_teachers($class_master_id,$lp_subject_id);
    echo json_encode($status);
  }

  
  public function remove_teacher_assigned_to_section(){
    echo json_encode($this->ManageSubject_model->remove_teacher_assigned_to_section($_POST));
  }

  public function get_subject_staffs() {
    $status = $this->ManageSubject_model->get_subject_staffs($_POST);
    echo json_encode($status);
  }

  public function assign_teacher_to_the_subject() {
    $loggedIn_staff_id = $_POST['loggedIn_staff_id'];
    $sub_id = $_POST['sub_id'];
    $type = $_POST['type'];
    $sec_id = $_POST['sec_id'];
    $staff_id = $_POST['staff_id'];
    $accessibility = $_POST['accessibility'];
    $status = $this->ManageSubject_model->assign_teacher_to_the_subject($loggedIn_staff_id, $sub_id, $type, $sec_id, $staff_id, $accessibility);
    echo json_encode($status);
  }

  public function get_details_if_exist() {
    $subject_id = $_POST['subject_id'];
    $status = $this->ManageSubject_model->get_details_if_exist($subject_id);
    echo json_encode($status);
  }
  public function assign_period_template_to_class($data){
    echo $this->ManageSubject_model->assign_period_template_to_class($data);
  }

  public function getClassesForLmsClone(){
    $classes=$this->ManageSubject_model->getClassesForLmsClone($_POST);
    echo json_encode($classes);
  }

  public function callGetSubjectsForClone(){
    $data["viewSubjects"]=$this->ManageSubject_model->callGetSubjectsForClone($_POST);
    echo json_encode($data);
  }

}
?>