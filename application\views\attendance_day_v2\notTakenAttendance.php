<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
  <li>Not Taken Attendance</li>
</ul>

<div class="container-fluid">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border d-flex align-items-center justify-content-between">
      <h3 class="card-title panel_title_new_style_staff mb-0">
        <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>" aria-label="Go back">
          <span class="fa fa-arrow-left"></span>
        </a> 
        Not Taken Attendance Report
      </h3>
    </div>
    <div class="card-body"> 
      <form id="notTakenForm" method="post">
        <div class="row g-3">
          <div class="col-md-2">
            <div class="form-group">
              <label for="sectionid">Section</label>
              <select name="classsecID[]" id="sectionID" class="form-control select2" required multiple>
                <option value="all_all">All Sections</option>
                <?php foreach ($class_section as $cls_section){ ?>
                  <option value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>">
                    <?= $cls_section->class_name . ' ' . $cls_section->section_name ?>
                  </option>
                <?php } ?>
              </select>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label for="reportrange">Date Range</label>
              <div id="reportrange" class="dtrange">
                <span></span>
                <input type="hidden" name="from_date" id="from_date">
                <input type="hidden" name="to_date" id="to_date">
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="col-md-2 my-5">
            <button type="button" class="btn btn-primary w-100" id="submitBtn" onclick="nottakenAttendance()" style="margin-top: -4px;" aria-label="Get Attendance Report">Get</button>
          </div>
        </div>
      </form>
      <div id="notTakenReport" class="mt-4"></div>
    </div>
  </div>
</div>

<!-- Date Range Picker JS and CSS -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    $('#sectionID').on('change', function() {
        var selectedValues = $(this).val();
        var allSectionsSelected = selectedValues && selectedValues.includes('all_all');
        
        // Enable all options first
        $('#sectionID option').prop('disabled', false);
        
        if (allSectionsSelected) {
            // If "All Sections" is selected, disable other options
            $('#sectionID option:not([value="all_all"])').prop('disabled', true);
            $(this).val(['all_all']).trigger('change.select2');
        } else if (selectedValues && selectedValues.length > 0) {
            // If any other option is selected, disable "All Sections"
            $('#sectionID option[value="all_all"]').prop('disabled', true);
        } else {
            // If nothing is selected, enable all options
            $('#sectionID option').prop('disabled', false);
        }
    });

    // Initialize select2
    $('#sectionID').select2();
});
</script>
<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment()],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment(),
    maxDate: moment(),
  }, function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });

  // Set initial date range (today)
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  const loading = `<div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="text-align: center;">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                  </div>`;

  function nottakenAttendance() {
    $("#submitBtn").prop("disabled", true).text("Please wait...");
    $('#notTakenReport').html(loading);

    var formData = $('#notTakenForm').serialize();

    $.ajax({
      url: '<?= site_url('attendance_day_v2/Attendance_day_v2/ajaxNotTakenAttendanceReport') ?>',
      type: 'POST',
      data: formData,
      success: function(response) {
        try {
          response = JSON.parse(response);
          if (Object.keys(response).length) {
            const { dateRange, report } = response;
            let html = `<div class="scrolable-table-container">
                          <table class="table table-bordered table-striped table-hover scrollable-table" id="attendanceTable">
                            <thead>
                              <tr>
                                <th>#</th>
                                <th>Class & Section</th>
                                <th>Class Teacher</th>`;
                                dateRange.forEach(date => {
              const formattedDate = moment(date, 'YYYY-MM-DD').format('D MMM');
              const weekday = moment(date, 'YYYY-MM-DD').format('ddd');
              html += `<th>${formattedDate}<br><small>(${weekday})</small></th>`;
            });
            html += `</tr></thead><tbody>`;
            report.forEach((row, index) => {
              html += `<tr>
                        <td>${index + 1}</td>
                        <td style="text-align: left;">${row.class} ${row.section}</td>
                        <td style="text-align: left; white-space: nowrap; max-width: 150px; overflow: hidden; text-overflow: ellipsis;">${row.classTeacher.trim() || 'NA'}</td>`;
                        dateRange.forEach(date => {
                const status = row.status[date] || '';
                if (status === 1) {
                  html += `<td><span style="color: green;">✔</span></td>`;
                } else if (status === 2) {
                  html += `<td><span style="color: red;">✖</span></td>`;
                } else if (status === 'WE') {
                  html += `<td style="color:#FFA500; font-weight:bold;">WE</td>`;
                } else if (status === 'H') {
                  html += `<td style="color:#FFA500; font-weight:bold;">H</td>`;
                }else {
                  html += `<td style="color: #808080; font-weight: bold;">-</td>`;
                }
              });
              html += `</tr>`;
            });
            html += `</tbody></table></div>`;
            $('#notTakenReport').html(html);

            $('#attendanceTable').DataTable({
              ordering: false,
              paging: true, // Enable pagination
              info: true, // Enable "Showing X of Y entries" text
              lengthChange: true, // Enable "Show entries" dropdown
              scrollY: '40vh',
              scrollX: true,
              language: {
                search: "",
                searchPlaceholder: "Enter Search...",
                lengthMenu: "Show _MENU_ entries", // Customize "Show entries" text
                paginate: {
                  previous: "Previous", // Customize "Previous" button text
                  next: "Next" // Customize "Next" button text
                }
              },
              lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
              pageLength: 10,
              dom: 'lBfrtip',
              buttons: [
                {
                  extend: 'excelHtml5',
                  text: 'Excel',
                  filename: 'monthWiseSummaryReport',
                  className: 'btn btn-info'
                },
                {
                  extend: 'print',
                  text: 'Print',
                  filename: 'monthWiseSummaryReport',
                  className: 'btn btn-info'
                }
              ]
            });
          } else {
            $('#notTakenReport').html('<div class="alert alert-warning text-center">No data found for the selected date range.</div>');
          }
        } catch (err) {
          console.error(err);
          $('#notTakenReport').html('<div class="alert alert-danger text-center">Something went wrong while fetching the data.</div>');
        } finally {
          $("#submitBtn").prop("disabled", false).text("Get");
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX Error:', status, error);
        $('#notTakenReport').html('<div class="alert alert-danger text-center">AJAX request failed. Please try again.</div>');
        $("#submitBtn").prop("disabled", false).text("Get");
      }
    });
  }

  $(document).ready(function() {
    $('.select2').select2({
        placeholder: "Select Section",
        allowClear: true,
        width: '100%'
    });
});
</script>

<style>
.form-group {
  margin: 8px 0px;
}

.form-group:last-child {
  margin-bottom: 12px;
}

 div.dt-buttons,
  .dataTables_wrapper .dt-buttons {
    float: right;
    margin-bottom: 10px;
  }

  .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5vh;
  }

  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
  }

  .dataTables_filter {
    position: absolute;
    right: 20%;
  }

  @media only screen and (min-width: 1404px) {
    .dataTables_filter {
      right: 15%;
    }
  }

  @media only screen and (min-width: 1734px) {
    .dataTables_filter {
      right: 5vh;
    }
  }

  .dataTables_scrollBody {
    margin-top: -13px;
    overflow-x: auto;
  }

  tr:hover {
    background: #F1EFEF;
  }

  .row_background_color {
    background: #7f848780;
  }

  .dt-buttons {
    font-size: 14px;
    background: none;
  }

  .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
  }

  td > a > i {
    text-decoration: none;
    font-size: 16px;
    color: #191818;
    padding: 2px 5px;
  }

  .dataTables_filter label input {
    margin-right: -63px;
  }

  #attendanceTable{
    width: 100%;
  }

  .scrollable-table-container {
    position: relative;
    overflow-x: auto;
    width: 100%;
  }

  .scrollable-table {
    border-collapse: collapse;
    width: max-content;
    min-width: 100%;
    table-layout: auto;
  }

  .scrollable-table th,
  .scrollable-table td {
    border: 1px solid #ccc;
    padding: 8px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    text-align: center;
    min-width: 120px;
  }

  /* === Sticky Header === */
  .scrollable-table thead th {
    position: sticky;
    top: 0;
    background: #f1f1f1;
    z-index: 4;
  }

  /* === Fixed First 3 Columns === */
  .scrollable-table th:nth-child(1),
  .scrollable-table td:nth-child(1) {
    position: sticky;
    left: 0;
    background: #fff;
    z-index: 3;
    min-width: 100px;
    width: 100px;
  }

  .scrollable-table th:nth-child(2),
  .scrollable-table td:nth-child(2) {
    position: sticky;
    left: 100px;
    background: #fff;
    z-index: 3;
    min-width: 150px;
    width: 150px;
  }

  .scrollable-table th:nth-child(3),
  .scrollable-table td:nth-child(3) {
    position: sticky;
    left: 250px;
    background: #fff;
    z-index: 3;
    min-width: 180px;
    width: 180px;
  }

  /* Ensure header cells in fixed columns appear above all */
  .scrollable-table thead th:nth-child(1),
  .scrollable-table thead th:nth-child(2),
  .scrollable-table thead th:nth-child(3) {
    z-index: 5;
  }

  /* Extra horizontal scroll styling for DataTables container */
  .dataTables_scrollBody {
    overflow-x: auto;
  }

</style>
