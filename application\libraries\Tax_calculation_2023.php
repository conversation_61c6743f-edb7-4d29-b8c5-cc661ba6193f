<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Tax_calculation_2023 {
    protected $CI;
    protected $collectPrekTaxMode;

	public function __construct() {
		$this->CI =& get_instance();
        $this->CI->load->library('settings', true);
        $this->collectPrekTaxMode = $this->CI->settings->getSetting('payroll_collect_perk_tax_mode');
	}

    public function tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances, $yearly_professional_tax, $gender, $age, $vpf_contribution, $previous_payslip_income=[], $dataFor = '') {
        $fromDate = $tax_declaration_data->from_date;
        $toDate = $tax_declaration_data->to_date;
        //Default to 30-Male in case gender or age is not available
        if (empty($gender)) $gender = 'M';
        if (empty($age)) $age = '30';

        //Step 1:
        $yearly_ctc_without_employee_pf = $yearly_ctc_with_employee_pf - $yearly_employee_pf;
        $yearly_inctc_allowances = $yearly_ctc_without_employee_pf - $yearly_basic_salary_with_da - $yearly_hra;
        $total_income = $yearly_ctc_without_employee_pf + $tax_declaration_data->other_employer_income;

        //Step 2:
        $hra_other_allowances = $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances;
        //Manjukiran 19th June 2024: This line of code is added for Indus. They take a perquisite income of 10% of gross income for tax purpose in case the employee is availing school-provided accommodation.
        //For others it is not applicable.
        $perquisite_income = 0;
        if ($tax_declaration_data->availing_company_accommodation == '1') {
            $perquisite_income =  ($yearly_basic_salary_with_da + $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances) * 0.1;
        }

        //Step 3: Less exempted
        $hra_exemption = min($yearly_hra, max(0, $tax_declaration_data->rent_amount_cal - ($yearly_basic_salary_with_da * 0.10)));

        //Standard Deduction
        $standard_deduction_old = 50000;
        $standard_deduction_new = 75000;
        $income_from_salary_old = 0;
        $income_from_salary_new = 0;
        if($this->collectPrekTaxMode == 'employee' || $dataFor == 'Tax Summary Report'){
            $income_from_salary_old = $yearly_basic_salary_with_da + $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances + $perquisite_income - ($hra_exemption + $standard_deduction_old);
            $income_from_salary_new = $yearly_basic_salary_with_da + $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances + $perquisite_income - $standard_deduction_new;
        } else {
            $income_from_salary_old = $yearly_basic_salary_with_da + $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances - ($hra_exemption + $standard_deduction_old);
            $income_from_salary_new = $yearly_basic_salary_with_da + $yearly_hra + $yearly_inctc_allowances + $yearly_outctc_allowances - $standard_deduction_new;
        }

        $income_from_salary_old = max(0, $income_from_salary_old);
        $income_from_salary_new = max(0, $income_from_salary_new);

        //Step 4: PT Deduction
        $taxable_income_from_salary_old = $income_from_salary_old - $yearly_professional_tax;
        $taxable_income_from_salary_new = $income_from_salary_new; //No PT Deducation for new regime

        //Step 4.1: Home Loan interest
        $home_loan_interest = $this->_24_deductions($tax_declaration_data);

        //Step 5: Income from other sources
        $gross_salary_old = $taxable_income_from_salary_old + $tax_declaration_data->other_employer_income - $home_loan_interest;
        $gross_salary_new = $taxable_income_from_salary_new + $tax_declaration_data->other_employer_income;
        $gross_salary_old = max(0, $gross_salary_old);
        $gross_salary_new = max(0, $gross_salary_new);

        //Step 6: Deductions
        $deduction_80c = $this->_80c_calculation($tax_declaration_data, $vpf_contribution);
        $deduction_80ccd = $this->_80ccd_calculation($tax_declaration_data);
        $deduction_80d = $this->_80d_calculation($tax_declaration_data);
        $deduction_80dd = $this->_80dd_deductions($tax_declaration_data);
        $deduction_80ddb = $this->_80ddb_deductions($tax_declaration_data);
        $deduction_80e = $tax_declaration_data->eightye_interest_paid_education;
        $deduction_80g = $this->_80g_deductions($tax_declaration_data);
        $deduction_80u = $this->_80u_deductions($tax_declaration_data);
        $deduction_80ttab = $this->_80ttab_deductions($tax_declaration_data);

        $total_80_deductions = $deduction_80c + $deduction_80ccd + $deduction_80d + $deduction_80dd + $deduction_80ddb + $deduction_80e + $deduction_80g + $deduction_80u + $deduction_80ttab;

        //Step 7: Old Tax Calculation
        $lta_limit = 0;
        if(!empty($previous_payslip_income) && isset($previous_payslip_income['lta_limit']) && $previous_payslip_income['lta_limit'] > 0){
            $lta_limit = $previous_payslip_income['lta_limit'];
        } else if(isset($tax_declaration_data->lta_limit) && $tax_declaration_data->lta_limit > 0){
            $lta_limit = $tax_declaration_data->lta_limit;
        }
        $lta_claimed = $tax_declaration_data->leave_travel_allowance;
        $lta_deduction = $lta_claimed > $lta_limit ? $lta_limit : $lta_claimed;
        
        $taxable_salary_old = $gross_salary_old - $total_80_deductions - $lta_deduction;
        $taxable_salary_old = max (0, $taxable_salary_old);
        $basic_tax_old = round($this->_basic_tax_old_regime_calculation($taxable_salary_old, $gender, $age), 2);
        $tax_rebate_old = $this->_tax_rebate_old($taxable_salary_old, $basic_tax_old);
        $net_income_tax_old = $basic_tax_old - $tax_rebate_old;
        $surcharge_old = $this->_surcharge_old($total_income, $net_income_tax_old);
        $net_income_tax_surcharge_old = $net_income_tax_old + $surcharge_old;
        $cess_old = $this->_cess($net_income_tax_surcharge_old);
        $total_tax_old = round($net_income_tax_surcharge_old + $cess_old, 2);
        $total_remaining_tds_old = max(0,$total_tax_old - $tax_declaration_data->other_employer_tds);

        //Step 8: New Tax Calculation
        $taxable_salary_new = $gross_salary_new;
        $taxable_salary_new = max (0, $taxable_salary_new);
        if($fromDate >= '2025-04-01' && $toDate <= '2026-03-31'){
            $basic_tax_new = round($this->_basic_tax_new_regime_calculation_2025($taxable_salary_new), 2);
        }else {
            $basic_tax_new = round($this->_basic_tax_new_regime_calculation_2024($taxable_salary_new), 2);
        }
        $tax_rebate_new = $this->_tax_rebate_new($taxable_salary_new, $basic_tax_new, $fromDate, $toDate);
        $net_income_tax_new = $basic_tax_new - $tax_rebate_new;
        $surcharge_new = $this->_surcharge_new($total_income, $net_income_tax_new, $fromDate, $toDate);
        $net_income_tax_surchage_new = $net_income_tax_new + $surcharge_new;
        $cess_new = $this->_cess($net_income_tax_surchage_new);
        $total_tax_new = round($net_income_tax_surchage_new + $cess_new, 2);
        $total_remaining_tds_new = max(0, $total_tax_new - $tax_declaration_data->other_employer_tds);

        $taxData = array(
            'ctc' => $yearly_ctc_without_employee_pf,
            'yearly_ctc_with_pf' => $yearly_ctc_with_employee_pf,
            'basic_salary' => $yearly_basic_salary_with_da,
            'hra' => $yearly_hra,
            'other_allowance' => $yearly_inctc_allowances,
            'outside_ctc_allowances' => $yearly_outctc_allowances,
            'hra_other_allowance' => $hra_other_allowances,
            'hra_exemption' => $hra_exemption,
            'income_from_salary_new' => $income_from_salary_new,
            'income_from_salary_old' => $income_from_salary_old,
            'pt_paid' => $yearly_professional_tax,
            'vpf' => $vpf_contribution,
            'taxable_income_from_salary_new' => $taxable_income_from_salary_new,
            'taxable_income_from_salary_old' => $taxable_income_from_salary_old,
            'perquisite_income' => $perquisite_income,
            'other_employer_income' => $tax_declaration_data->other_employer_income,
            'sec_24' => $home_loan_interest,
            'gross_salary_old' => $gross_salary_old,
            'gross_salary_new' => $gross_salary_new,

            'or_sd' => $standard_deduction_old,
            'c_80' => $deduction_80c,
            'ccd_80' => $deduction_80ccd,
            'd_80' => $deduction_80d,
            'dd_80' => $deduction_80dd,
            'ddb_80' => $deduction_80ddb,
            'e_80' => $deduction_80e,
            'g_80' => $deduction_80g,
            'u_80' => $deduction_80u,
            'ttab_80' => $deduction_80ttab,
            
            'total_80_deductions' => $total_80_deductions,
            'total_80c_deduction' => $deduction_80c,
            'total_80ccd_deduction' => $deduction_80ccd,
            'total_80d_deduction' => $deduction_80d,
            'interest_paid_on_home_loan' => $tax_declaration_data->interest_paid_on_home_loan,

            'or_lta' => round($lta_deduction),
            'or_taxable_salary' => $taxable_salary_old,
            'or_basic_tax' => $basic_tax_old,
            'or_tax_rebate' => $tax_rebate_old,
            'or_net_income_tax' => $net_income_tax_old,
            'or_surcharge' => $surcharge_old,
            'or_net_income_tax_surcharge' => $net_income_tax_surcharge_old,
            'or_cess' => $cess_old,
            'or_tax_amt' => $total_tax_old,
            'or_tax_amt_remaining' => $total_remaining_tds_old,

            'nr_sd' => $standard_deduction_new,
            'nr_taxable_salary' => $taxable_salary_new,
            'nr_basic_tax' => $basic_tax_new,
            'nr_tax_rebate' => $tax_rebate_new,
            'nr_net_income_tax' => $net_income_tax_new,
            'nr_surcharge' => $surcharge_new,
            'nr_basic_tax_surcharge' => $net_income_tax_surchage_new,
            'nr_net_income_tax_surcharge' => $net_income_tax_surchage_new,
            'nr_cess' => $cess_new,
            'nr_tax_amt' => $total_tax_new,
            'nr_tax_amt_remaining' => $total_remaining_tds_new,

            'other_employer_tds' => $tax_declaration_data->other_employer_tds,
            'total_income' => $total_income,

            'previous_basic_salary_with_da' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_basic_salary_with_da'])) ? $previous_payslip_income['previous_basic_salary_with_da'] : '',
            'previous_hra' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_hra'])) ? $previous_payslip_income['previous_hra'] : '',
            'previous_employee_pf_contribution' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_employee_pf_contribution'])) ? $previous_payslip_income['previous_employee_pf_contribution'] : '',
            'previous_professional_tax' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_professional_tax'])) ? $previous_payslip_income['previous_professional_tax'] : '',
            'previous_vpf' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_vpf'])) ? $previous_payslip_income['previous_vpf'] : '',
            'previous_outside_ctc_allowance' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_outside_ctc_allowance'])) ? $previous_payslip_income['previous_outside_ctc_allowance'] : '',
            'previous_ctc_with_employee_pf' => (!empty($previous_payslip_income) && isset($previous_payslip_income['previous_ctc_with_employee_pf'])) ? $previous_payslip_income['previous_ctc_with_employee_pf'] : '',
            'staff_months_in_year' => (!empty($previous_payslip_income) && isset($previous_payslip_income['staff_months_in_year'])) ? $previous_payslip_income['staff_months_in_year'] : ''
        );
        return $taxData;
    }

    public function tax_calculation_yearly($tax_declaration_data, $staff_obj) {
        $staff_months_in_year = $this->_get_staff_months($staff_obj->joining_date, $tax_declaration_data->from_date, $tax_declaration_data->to_date);
        $yearly_outctc_allowances = $this->_get_outside_ctc_allowance($tax_declaration_data, $staff_months_in_year);

        //Step 1:
        $yearly_ctc_with_employee_pf = $tax_declaration_data->yearly_ctc * $staff_months_in_year / 12;
        $yearly_basic_salary_with_da = ($tax_declaration_data->monthly_basic_salary + $tax_declaration_data->staff_da) * $staff_months_in_year; //We are considering monthly basic salary
        $yearly_hra = $tax_declaration_data->staff_hra * $staff_months_in_year; //staff_hra is stored monthly
        $yearly_employee_pf = $tax_declaration_data->epf_and_pf_contribution * $staff_months_in_year / 12; //stored yearly
        $yearly_employee_vpf = $tax_declaration_data->vpf * $staff_months_in_year; //stored monthly

        //Step 2:
        $yearly_professional_tax = $tax_declaration_data->professional_tax * $staff_months_in_year;

        return $this->tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances, $yearly_professional_tax, $staff_obj->gender, $staff_obj->age, $yearly_employee_vpf);
    }

    public function tax_calculation_yearly_v2($tax_declaration_data, $staff_obj, $payslip_yearly_income, $dataFor = '') {
        
        $yearly_ctc_with_employee_pf = $payslip_yearly_income['ctc_with_employee_pf'] ;
        $yearly_basic_salary_with_da = $payslip_yearly_income['basic_salary_with_da'];
        $yearly_outctc_allowances = $payslip_yearly_income['outside_ctc_allowance'] + $payslip_yearly_income['additional_month_allowance'];
        $yearly_hra = $payslip_yearly_income['hra'];
        $yearly_employee_pf = $payslip_yearly_income['employee_pf_contribution'];
        $yearly_employee_vpf = $payslip_yearly_income['vpf'];
        $yearly_professional_tax = $payslip_yearly_income['professional_tax'];
        return $this->tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances, $yearly_professional_tax, $staff_obj->gender, $staff_obj->age, $yearly_employee_vpf, $payslip_yearly_income, $dataFor);
    }

    /*****
     * yearly_income_params[
     *  'ctc_with_employee_pf' => XXXX,
     *  'basic_salary_with_da' => XXXX,
     *  'hra' => XXXX,
     *  'employee_pf_contribution' => XXXX,
     *  'outside_ctc_allowance' => XXXX,
     *  'professional_tax' => XXX
     * ]
     * current_month_income_params[
     *  'ctc_with_employee_pf' => XXXX,
     *  'basic_salary_with_da' => XXXX,
     *  'hra' => XXXX,
     *  'employee_pf_contribution' => XXXX,
     *  'outside_ctc_allowance' => XXXX,
     *  'additional_month_allowance' => XXXX,
     *  'professional_tax' => XXX
     * ]
     */
    public function tax_calculation_with_current_month_changes($tax_declaration_data, $current_month_income_params, $staff_obj) {

        $staff_months_in_year = $this->_get_staff_months($staff_obj->joining_date, $tax_declaration_data->from_date, $tax_declaration_data->to_date);
        $yearly_outctc_allowances = $this->_get_outside_ctc_allowance($tax_declaration_data, $staff_months_in_year);
        //Step 1:
        $staff_months_in_year_rectified = $staff_months_in_year - 1; //exclude current month
        $yearly_ctc_with_employee_pf = $tax_declaration_data->yearly_ctc * $staff_months_in_year_rectified / 12 + $current_month_income_params['ctc_with_employee_pf'];
        $yearly_basic_salary_with_da = ($tax_declaration_data->monthly_basic_salary + $tax_declaration_data->staff_da) * $staff_months_in_year_rectified + $current_month_income_params['basic_salary_with_da'];
        $yearly_hra = $tax_declaration_data->staff_hra * $staff_months_in_year_rectified  + $current_month_income_params['hra'];
        $yearly_employee_pf = $tax_declaration_data->epf_and_pf_contribution * $staff_months_in_year_rectified / 12 + $current_month_income_params['employee_pf_contribution'];
        //Step 2:
        $yearly_outctc_allowances = $yearly_outctc_allowances * $staff_months_in_year_rectified / 12 + $current_month_income_params['outside_ctc_allowance'];
        $yearly_professional_tax = $tax_declaration_data->professional_tax * $staff_months_in_year_rectified  + $current_month_income_params['professional_tax'];
        $yearly_vpf = $tax_declaration_data->vpf * $staff_months_in_year_rectified + $current_month_income_params['vpf'];
        
        $monthly_staff_cals = $this->tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances + $current_month_income_params['additional_month_allowance'], $yearly_professional_tax, $staff_obj->gender, $staff_obj->age, $yearly_vpf);
        $yearly_staff_cals = $this->tax_calculation_yearly($tax_declaration_data, $staff_obj);
        $selected_regime = $tax_declaration_data->tax_regime;
        if ($selected_regime === '1') {
            //1 is new_regime
            $difference_tds = $monthly_staff_cals['nr_tax_amt'] - $tax_declaration_data->total_declared_tds;
        } else {
            $difference_tds = $monthly_staff_cals['or_tax_amt'] - $tax_declaration_data->total_declared_tds;
        }
        return $difference_tds;
    }

    public function tax_calculation_with_current_month_changes_v2($tax_declaration_data, $payslip_previous_collected_income, $current_month_income_params, $staff_obj) {

       // $staff_months_in_year = $this->_get_staff_months($staff_obj->joining_date, $tax_declaration_data->from_date, $tax_declaration_data->to_date);
        $staff_months_in_year = $payslip_previous_collected_income['staff_months_in_year'];       
        $staff_months_in_year_rectified = $staff_months_in_year - 1;
        $yearly_outctc_allowances = $this->_get_outside_ctc_allowance_v2($tax_declaration_data, $staff_months_in_year_rectified);
        // Yearly CTC
        $previous_yearly_ctc_with_pf = $payslip_previous_collected_income['ctc_with_employee_pf'];
        $new_yearly_ctc_with_pf = $tax_declaration_data->monthly_gross * $staff_months_in_year_rectified + $current_month_income_params['ctc_with_employee_pf'];
        $yearly_ctc_with_employee_pf= $previous_yearly_ctc_with_pf + $new_yearly_ctc_with_pf;
     
        // Basic Salary
        $previous_basic_salary = $payslip_previous_collected_income['basic_salary_with_da'];
        $new_basic_salaray = $tax_declaration_data->monthly_basic_salary * $staff_months_in_year_rectified + $current_month_income_params['basic_salary_with_da'];
        $yearly_basic_salary_with_da = $previous_basic_salary + $new_basic_salaray;
        
        // HRA
        $previous_hra = $payslip_previous_collected_income['hra'];
        $new_basic_salaray = $tax_declaration_data->staff_hra * $staff_months_in_year_rectified + $current_month_income_params['hra'];
        $yearly_hra = $previous_hra + $new_basic_salaray;

        // PF
        $previous_pf = $payslip_previous_collected_income['employee_pf_contribution'];
        $new_basic_epf_pf = $tax_declaration_data->pf_for_employer * $staff_months_in_year_rectified + $current_month_income_params['employee_pf_contribution'];
        $yearly_employee_pf = $previous_pf + $new_basic_epf_pf;

        // outside ctc
        $previous_outside_ctc = $payslip_previous_collected_income['outside_ctc_allowance'] + $payslip_previous_collected_income['additional_month_allowance'];
        $new_out_ctc = $yearly_outctc_allowances + $current_month_income_params['outside_ctc_allowance'];       
        $yearly_outctc_allowances = $previous_outside_ctc + $new_out_ctc;

        // outside PT
        $previous_professional_tax = $payslip_previous_collected_income['professional_tax'];
        $new_professional_tax = $tax_declaration_data->professional_tax * $staff_months_in_year_rectified + $current_month_income_params['professional_tax'];       
        $yearly_professional_tax = $previous_professional_tax + $new_professional_tax;
     
        // outside VPF
        $previous_vpf = $payslip_previous_collected_income['vpf'];
        $new_vpf = $tax_declaration_data->vpf * $staff_months_in_year_rectified + $current_month_income_params['vpf'];       
        $yearly_vpf = $previous_vpf + $new_vpf;

        $monthly_staff_cals = $this->tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances + $current_month_income_params['additional_month_allowance'], $yearly_professional_tax, $staff_obj->gender, $staff_obj->age, $yearly_vpf);
        $selected_regime = $tax_declaration_data->tax_regime;
        
        if ($selected_regime === '1') {
            //1 is new_regime
            $difference_tds = $monthly_staff_cals['nr_tax_amt'] - $tax_declaration_data->total_declared_tds;
        } else {
            $difference_tds = $monthly_staff_cals['or_tax_amt'] - $tax_declaration_data->total_declared_tds;
        }
        $data = array(
            'total_declared_tds' => $tax_declaration_data->total_declared_tds,
            'tds_as_per_calc' => $selected_regime == '1' ? $monthly_staff_cals['nr_tax_amt'] : $monthly_staff_cals['or_tax_amt'],
            'difference_tds' => $difference_tds,
        );
        return $data;
    }

    private function _80c_calculation($tax_declaration_data, $vpf_contribution) {
        $epf_and_pf_contribution = isset($tax_declaration_data->epf_and_pf_contribution) ? $tax_declaration_data->epf_and_pf_contribution : 0;
        $public_provident_fund = isset($tax_declaration_data->public_provident_fund) ? $tax_declaration_data->public_provident_fund : 0;
        $nsc_investment = isset($tax_declaration_data->nsc_investment) ? $tax_declaration_data->nsc_investment : 0;
        $tax_saving_fixed_deposit = isset($tax_declaration_data->tax_saving_fixed_deposit) ? $tax_declaration_data->tax_saving_fixed_deposit : 0;
        $elss_mutual_fund = isset($tax_declaration_data->elss_mutual_fund) ? $tax_declaration_data->elss_mutual_fund : 0;
        $life_insurance = isset($tax_declaration_data->life_insurance) ? $tax_declaration_data->life_insurance : 0;
        $new_pension_scheme = isset($tax_declaration_data->new_pension_scheme) ? $tax_declaration_data->new_pension_scheme : 0;
        $pension_plan_for_insurance = isset($tax_declaration_data->pension_plan_for_insurance) ? $tax_declaration_data->pension_plan_for_insurance : 0;
        $principal_repayment_house_loan = isset($tax_declaration_data->principal_repayment_house_loan) ? $tax_declaration_data->principal_repayment_house_loan : 0;
        $sukanya_samriddhi_yojana = isset($tax_declaration_data->sukanya_samriddhi_yojana) ? $tax_declaration_data->sukanya_samriddhi_yojana : 0;
        $stamp_duty_registration_fees = isset($tax_declaration_data->stamp_duty_registration_fees) ? $tax_declaration_data->stamp_duty_registration_fees : 0;
        $tution_fees_for_children = isset($tax_declaration_data->tution_fees_for_children) ? $tax_declaration_data->tution_fees_for_children : 0;
        $other_80c_investments = isset($tax_declaration_data->other_80c_investments) ? $tax_declaration_data->other_80c_investments : 0;

        $deduction_80c = $epf_and_pf_contribution + $public_provident_fund + $nsc_investment + $tax_saving_fixed_deposit + $elss_mutual_fund + $life_insurance + $new_pension_scheme + $pension_plan_for_insurance + $principal_repayment_house_loan + $sukanya_samriddhi_yojana + $stamp_duty_registration_fees + $tution_fees_for_children + $other_80c_investments + $vpf_contribution;

        $total_deduction_80c = $deduction_80c >= 150000 ? 150000 : $deduction_80c;

        return $total_deduction_80c;
    }

    private function _80ccd_calculation($tax_declaration_data) {
        $additional_deducation_for_nps = isset($tax_declaration_data->additional_deducation_for_nps) ? $tax_declaration_data->additional_deducation_for_nps : 0;

        $total_additional_deduction_80c = $additional_deducation_for_nps >= 50000 ? 50000 : $additional_deducation_for_nps;

        return $total_additional_deduction_80c;
    }

    private function _80d_calculation($tax_declaration_data) {
        $medical_insurance_premium_self_80d = isset($tax_declaration_data->medical_insurance_premium_self_80d) ? ($tax_declaration_data->medical_insurance_premium_self_80d >= 25000 ? 25000 : $tax_declaration_data->medical_insurance_premium_self_80d) : 0;
        $medical_insurance_premium_self_80d_senior = isset($tax_declaration_data->medical_insurance_premium_self_80d_senior) ? ($tax_declaration_data->medical_insurance_premium_self_80d_senior >= 50000 ? 50000 : $tax_declaration_data->medical_insurance_premium_self_80d_senior) : 0;
        $medical_insurance_premium_parent_80d = isset($tax_declaration_data->medical_insurance_premium_parent_80d) ? ($tax_declaration_data->medical_insurance_premium_parent_80d >= 25000 ? 25000 : $tax_declaration_data->medical_insurance_premium_parent_80d) : 0;
        $medical_insurance_premium_parent_80d_senior = isset($tax_declaration_data->medical_insurance_premium_parent_80d_senior) ? ($tax_declaration_data->medical_insurance_premium_parent_80d_senior >= 50000 ? 50000 : $tax_declaration_data->medical_insurance_premium_parent_80d_senior) : 0;
        $preventive_health_checkup_80d = isset($tax_declaration_data->preventive_health_checkup_80d) ? ($tax_declaration_data->preventive_health_checkup_80d >= 5000 ? 5000 : $tax_declaration_data->preventive_health_checkup_80d) : 0;
        $preventive_health_checkup_parents_80d = isset($tax_declaration_data->preventive_health_checkup_parents_80d) ? ($tax_declaration_data->preventive_health_checkup_parents_80d >= 5000 ? 5000 : $tax_declaration_data->preventive_health_checkup_parents_80d) : 0;
        $medical_bills_for_parents_senior = isset($tax_declaration_data->medical_bills_for_parents_senior) ? ($tax_declaration_data->medical_bills_for_parents_senior >= 50000 ? 50000 : $tax_declaration_data->medical_bills_for_parents_senior) : 0;
        $medical_bills_for_self_senior = isset($tax_declaration_data->medical_bills_for_self_senior) ? ($tax_declaration_data->medical_bills_for_self_senior >= 50000 ? 50000 : $tax_declaration_data->medical_bills_for_self_senior) : 0;

        $is_parent_insurance = 0;
        if ($medical_insurance_premium_parent_80d > 0 || $medical_insurance_premium_parent_80d_senior > 0) {
            $is_parent_insurance = 1;
        }
        $is_self_insurance = 0;
        if ($medical_insurance_premium_self_80d > 0 || $medical_insurance_premium_self_80d_senior > 0) {
            $is_self_insurance = 1;
        }
        if ($tax_declaration_data->self_age == 'below_60' && $tax_declaration_data->parents_age == 'below_60') {
            if ($is_parent_insurance == 1) {
                $deduction_80d = $medical_insurance_premium_self_80d + $medical_insurance_premium_parent_80d + $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d;
                $Deduction2 = $deduction_80d >= 50000 ? 50000 : $deduction_80d;
                return $Deduction2;
            } else {
                $deduction_80d = $medical_insurance_premium_self_80d + $preventive_health_checkup_80d;
                $Deduction2 = $deduction_80d >= 25000 ? 25000 : $deduction_80d;
                return $Deduction2;
            }
        }
        if ($tax_declaration_data->self_age == 'below_60' && $tax_declaration_data->parents_age == 'above_60') {
            if ($is_parent_insurance == 1) {
                $deduction_80d = $medical_insurance_premium_self_80d + $medical_insurance_premium_parent_80d_senior + $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d;
                $Deduction2 = $deduction_80d >= 75000 ? 75000 : $deduction_80d;            
                return $Deduction2;
            } else {
                $deduction_80d = ($medical_insurance_premium_self_80d + $preventive_health_checkup_80d) > 25000 ? 25000 :  ($medical_insurance_premium_self_80d + $preventive_health_checkup_80d);
                $deduction_80d = $deduction_80d + $medical_bills_for_parents_senior;
                $Deduction2 = $deduction_80d >= 75000 ? 75000 : $deduction_80d;
                return $Deduction2;    
            }
        }
        if ($tax_declaration_data->self_age == 'above_60' && $tax_declaration_data->parents_age == 'above_60') {
            if ($is_parent_insurance == 1 && $is_self_insurance == 1) {
                $deduction_80d = $medical_insurance_premium_self_80d_senior + $medical_insurance_premium_parent_80d_senior + $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d;
                $Deduction2 = $deduction_80d >= 100000 ? 100000 : $deduction_80d;
                return $Deduction2;
            }
            if ($is_parent_insurance == 1 && $is_self_insurance == 0) {
                $deduction_80d = $medical_insurance_premium_parent_80d_senior + $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d + $medical_bills_for_self_senior;
                
                $Deduction2 = $deduction_80d >= 100000 ? 100000 : $deduction_80d;
                return $Deduction2;
            }
            if ($is_parent_insurance == 0 && $is_self_insurance == 1) {
                $deduction_80d = $medical_insurance_premium_self_80d_senior + $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d + $medical_bills_for_parents_senior;
                
                $Deduction2 = $deduction_80d >= 100000 ? 100000 : $deduction_80d;
                return $Deduction2;
            }
            if ($is_parent_insurance == 0 && $is_self_insurance == 0) {
                $deduction_80d = $preventive_health_checkup_80d + $preventive_health_checkup_parents_80d + $medical_bills_for_parents_senior + $medical_bills_for_self_senior;
                
                $Deduction2 = $deduction_80d >= 100000 ? 100000 : $deduction_80d;
                return $Deduction2;
            }
        }
    }

    private function _80dd_deductions ($tax_declaration_data) {
        $medical_treatment_dependent_handicapped_80dd = isset($tax_declaration_data->medical_treatment_dependent_handicapped_80dd) ? ($tax_declaration_data->medical_treatment_dependent_handicapped_80dd >= 75000 ? 75000 : $tax_declaration_data->medical_treatment_dependent_handicapped_80dd) : 0;

        $medical_treatment_dependent_handicapped_servere_80dd = isset($tax_declaration_data->medical_treatment_dependent_handicapped_servere_80dd) ? ($tax_declaration_data->medical_treatment_dependent_handicapped_servere_80dd >= 125000 ? 125000 : $tax_declaration_data->medical_treatment_dependent_handicapped_servere_80dd) : 0;

        $total_80dd_deduction = $medical_treatment_dependent_handicapped_80dd + $medical_treatment_dependent_handicapped_servere_80dd;

        return $total_80dd_deduction;
    }

    private function _80ddb_deductions ($tax_declaration_data) {
        $expenditure_medical_tretment_self_dependent_80ddb = isset($tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb) ? ($tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb >= 40000 ? 40000 : $tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb) : 0;

        $expenditure_medical_tretment_self_dependent_80ddb_senior = isset($tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb_senior) ? ($tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb_senior >= 100000 ? 100000 : $tax_declaration_data->expenditure_medical_tretment_self_dependent_80ddb_senior) : 0;

        $total_80ddb_deduction = $expenditure_medical_tretment_self_dependent_80ddb + $expenditure_medical_tretment_self_dependent_80ddb_senior;
        
        return $total_80ddb_deduction;
    }

    private function _80g_deductions($tax_declaration_data) {
        $donation_approved_funds_80ggc_fifty = isset($tax_declaration_data->donation_approved_funds_80ggc_fifty) ? $tax_declaration_data->donation_approved_funds_80ggc_fifty * 0.5 : 0;

        $eightyggc_donation_approved_funds = isset($tax_declaration_data->eightyggc_donation_approved_funds) ? $tax_declaration_data->eightyggc_donation_approved_funds * 1 : 0;

        $total_80g_deduction = $donation_approved_funds_80ggc_fifty + $eightyggc_donation_approved_funds;
        
        return $total_80g_deduction;
    }

    private function _80u_deductions($tax_declaration_data) {
        $eightyu_physically_disabled_person = isset($tax_declaration_data->eightyu_physically_disabled_person) ? ($tax_declaration_data->eightyu_physically_disabled_person >= 75000 ? 75000 : $tax_declaration_data->eightyu_physically_disabled_person) : 0;

        $physically_disabled_person_80u_severe = isset($tax_declaration_data->physically_disabled_person_80u_severe) ? ($tax_declaration_data->physically_disabled_person_80u_severe >= 125000 ? 125000 : $tax_declaration_data->physically_disabled_person_80u_severe) : 0;

        $total_80u_deduction = $eightyu_physically_disabled_person + $physically_disabled_person_80u_severe;

        return $total_80u_deduction;
    }

    private function _80ttab_deductions($tax_declaration_data) {
        if ($tax_declaration_data->self_age == 'below_60') {
            $eighty_ttab = isset($tax_declaration_data->eightytta_b_senior_citizens) ? ($tax_declaration_data->eightytta_b_senior_citizens >= 10000 ? 10000 : $tax_declaration_data->eightytta_b_senior_citizens) : 0;
        } else {
            $eighty_ttab = isset($tax_declaration_data->eightytta_b_senior_citizens) ? ($tax_declaration_data->eightytta_b_senior_citizens >= 50000 ? 50000 : $tax_declaration_data->eightytta_b_senior_citizens) : 0;
        }
        return $eighty_ttab;
    }

    private function _24_deductions($tax_declaration_data) {
        $twenty_four_deduction = isset($tax_declaration_data->interest_paid_on_home_loan) ? ($tax_declaration_data->interest_paid_on_home_loan >= 200000 ? 200000 : $tax_declaration_data->interest_paid_on_home_loan) : 0;

        return $twenty_four_deduction;
    }

    private function _basic_tax_old_regime_calculation($taxable_salary_old, $gender, $age) {
        $basicTax = 0;

        if ($gender == 'M') {
            if ($age < 60) {
                //Regular Slab
                return $this->_regular_male_slab_old($taxable_salary_old);
            } else if ($age <80) {
                //Senior Slab
                return $this->_senior_male_slab_old($taxable_salary_old);
            } else {
                //Super Senior Slab
                return $this->_super_senior_male_slab_old($taxable_salary_old);
            }
        } else if ($gender == 'F') {
            if ($age < 60) {
                //Regular Slab
                return $this->_regular_male_slab_old($taxable_salary_old);
            } else if ($age <80) {
                //Senior Slab
                return $this->_senior_male_slab_old($taxable_salary_old);
            } else {
                //Super Senior Slab
                return $this->_super_senior_male_slab_old($taxable_salary_old);
            }
        }
    }

    private function _regular_male_slab_old($taxable_salary_old) {
        if ($taxable_salary_old <= 250000) {
            $basicTax = 0;
        } else if ($taxable_salary_old <= 500000) {
            $basicTax = ($taxable_salary_old - 250000) * 0.05;
        } else if ($taxable_salary_old <= 1000000) {
            $basicTax = 12500 + ($taxable_salary_old - 500000) * 0.2;
        } else {
            $basicTax = 112500 + ($taxable_salary_old - 1000000) * 0.3;
        }

        return $basicTax;
    }

    private function _senior_male_slab_old($taxable_salary_old) {
        if ($taxable_salary_old <= 300000) {
            $basicTax = 0;
        } else if ($taxable_salary_old <= 500000) {
            $basicTax = ($taxable_salary_old - 300000) * 0.05;
        } else if ($taxable_salary_old <= 1000000) {
            $basicTax = 10000 + ($taxable_salary_old - 500000) * 0.2;
        } else {
            $basicTax = 110000 + ($taxable_salary_old - 1000000) * 0.3;
        }

        return $basicTax;
    }

    private function _super_senior_male_slab_old($taxable_salary_old) {
        if ($taxable_salary_old <= 500000) {
            $basicTax = 0;
        } else if ($taxable_salary_old <= 1000000) {
            $basicTax = ($taxable_salary_old - 500000) * 0.2;
        } else {
            $basicTax = 100000 + ($taxable_salary_old - 1000000) * 0.3;
        }

        return $basicTax;
    }

    private function _basic_tax_new_regime_calculation($taxable_salary_new) {
        $basicTax = 0;
        if ($taxable_salary_new <= 300000) {
            $basicTax = 0;
        } else if ($taxable_salary_new <= 600000) {
            $basicTax = ($taxable_salary_new - 300000) * 0.05;
        } else if ($taxable_salary_new <= 900000) {
            $basicTax = 15000 + ($taxable_salary_new - 600000) * 0.1;
        } else if ($taxable_salary_new <= 1200000) {
            $basicTax = 45000 + ($taxable_salary_new - 900000) * 0.15;
        } else if ($taxable_salary_new <= 1500000) {
            $basicTax = 90000 + ($taxable_salary_new - 1200000) * 0.2;
        } else {
            $basicTax = 150000 + ($taxable_salary_new - 1500000) * 0.3;
        }

        return $basicTax;
    }

    private function _basic_tax_new_regime_calculation_2025($taxable_salary_new) {
        $basicTax = 0;
        if ($taxable_salary_new <= 400000) {
            $basicTax = 0;
        } else if ($taxable_salary_new <= 800000) {
            $basicTax = ($taxable_salary_new - 400000) * 0.05;
        } else if ($taxable_salary_new <= 1200000) {
            $basicTax = 20000 + ($taxable_salary_new - 800000) * 0.10;
        } else if ($taxable_salary_new <= 1600000) {
            $basicTax = 60000 + ($taxable_salary_new - 1200000) * 0.15;
        } else if ($taxable_salary_new <= 2000000) {
            $basicTax = 120000 + ($taxable_salary_new - 1600000) * 0.20;
        } else if ($taxable_salary_new <= 2400000) {
            $basicTax = 200000 + ($taxable_salary_new - 2000000) * 0.25;
        } else {
            $basicTax = 300000 + ($taxable_salary_new - 2400000) * 0.30;
        }
        return $basicTax;
    }

    private function _basic_tax_new_regime_calculation_2024($taxable_salary_new) {
        $basicTax = 0;
        if ($taxable_salary_new <= 300000) {
            $basicTax = 0;
        } else if ($taxable_salary_new <= 700000) {
            $basicTax = ($taxable_salary_new - 300000) * 0.05;
        } else if ($taxable_salary_new <= 1000000) {
            $basicTax = 20000 + ($taxable_salary_new - 700000) * 0.1;
        } else if ($taxable_salary_new <= 1200000) {
            $basicTax = 50000 + ($taxable_salary_new - 1000000) * 0.15;
        } else if ($taxable_salary_new <= 1500000) {
            $basicTax = 80000 + ($taxable_salary_new - 1200000) * 0.2;
        } else {
            $basicTax = 140000 + ($taxable_salary_new - 1500000) * 0.3;
        }

        return $basicTax;
    }

    private function _cess($basicTax_surcharge){
        $cess = round($basicTax_surcharge * 0.04, 2);
        return $cess;
    }

    private function _surcharge_old($total_income, $basicTax) {
        if ($total_income >= 50000000) {
            $surcharge = round($basicTax * 0.37, 2);
        } else if ($total_income >= 20000000) {
            $surcharge = round($basicTax * 0.25, 2);
        } else if ($total_income >= 10000000) {
            $surcharge = round($basicTax * 0.15, 2);
        } else if ($total_income >= 5000000) {
            $surcharge = round($basicTax * 0.1, 2);
        } else {
            $surcharge = 0;
        }
        return $surcharge;
    }

    private function _surcharge_new($total_income, $basicTax, $fromDate, $toDate) {
        $surcharge = 0;
        if($fromDate >= '2025-04-01' && $toDate <= '2026-03-31'){
            if ($total_income >= 50000000) {
                $surcharge = round($basicTax * 0.25, 2);
            } else if ($total_income >= 20000000) {
                $surcharge = round($basicTax * 0.25, 2);
            } else if ($total_income >= 10000000) {
                $surcharge = round($basicTax * 0.15, 2);
            } else if ($total_income >= 5000000) {
                $surcharge = round($basicTax * 0.1, 2);
            } else {
                $surcharge = 0;
            }
            // $threshold_income = ($total_income >= 50000000) ? 50000000 : (($total_income >= 20000000) ? 20000000 : (($total_income >= 10000000) ? 10000000 : 5000000));
            // $excess_income = $total_income - $threshold_income;
            // $tax_without_surcharge = $basicTax;
            // $tax_with_surcharge = $basicTax + $surcharge;

            // if (($tax_with_surcharge - $tax_without_surcharge) > $excess_income) {
            //     $surcharge = $excess_income;
            // }
            // echo "<pre>";print_r($surcharge);die();
            // return round($surcharge, 2);
            return $surcharge;
        } else {
            if ($total_income >= 20000000) {
                $surcharge = round($basicTax * 0.25, 2);
            } else if ($total_income >= 10000000) {
                $surcharge = round($basicTax * 0.15, 2);
            } else if ($total_income >= 5000000) {
                $surcharge = round($basicTax * 0.1, 2);
            } else {
                $surcharge = 0;
            }
            return $surcharge;
        }
    }

    private function _get_staff_months ($staff_joining_date, $fy_start_date, $fy_end_date) {
        $staff_joining_date = new DateTime($staff_joining_date);
        $fy_start_date = new DateTime($fy_start_date);
        $fy_end_date = new DateTime($fy_end_date);

        if ($staff_joining_date < $fy_start_date) {
            return 12;
        }

        if ($staff_joining_date > $fy_end_date) {
            return 0;
        }

        $fy_end_date_year = $fy_end_date->format('Y');
        $staff_joining_date_year = $staff_joining_date->format('Y');
        $fy_end_date_month = $fy_end_date->format('m');
        $staff_joining_date_month = $staff_joining_date->format('m');

        // Calculate the number of months between the two dates
        $staff_months_in_year = ($fy_end_date_year - $staff_joining_date_year) * 12 + ($fy_end_date_month - $staff_joining_date_month);
        $staff_months_in_year = min(12, $staff_months_in_year);

        if ($staff_months_in_year < 12) {
            //Get number of days staff is available in the month
            $total_days = $staff_joining_date->format('t');
            $current_days = $staff_joining_date->format('j');
            $remainder = (float)($total_days - $current_days + 1) / $total_days;
            $staff_months_in_year += number_format($remainder, 5);
        }

        // echo '<pre>staff_months_in_year: ';print_r($staff_months_in_year);die();

        return $staff_months_in_year;
    }

    private function _get_outside_ctc_allowance($tax_declaration_data, $staff_months_in_year) {
        $columns = $this->CI->payroll_model->get_payroll_column_table();
        $taxDecleratedOutsideCTC = (array) $tax_declaration_data;
        $outsidectcolumnsValues = 0;
        foreach ($columns->payroll_column as  $colmn) {
            if ($colmn->outside_ctc_salary_strucutre == 1) {
                $key = $colmn->column_name;
                if (isset($taxDecleratedOutsideCTC[$key])) {
                    $outsidectcolumnsValues += floatval($taxDecleratedOutsideCTC[$key]) * $staff_months_in_year;
                }
            }
        }
        $yearly_outside_ctc_allowance = $outsidectcolumnsValues;

        //Use calculations to bring the correct value for all schools
        // $yearly_outside_ctc_allowance = ($tax_declaration_data->extra_allowance*$staff_months_in_year) + ($tax_declaration_data->hra_fixed*$staff_months_in_year) + ($tax_declaration_data->transport_allowance*$staff_months_in_year) + ($tax_declaration_data->co_ordinator_allowance*$staff_months_in_year) + ($tax_declaration_data->ib_retention_allowance*$staff_months_in_year) + ($tax_declaration_data->house_master_allowance*$staff_months_in_year);
        return $yearly_outside_ctc_allowance;
    }

    private function _tax_rebate_old($taxable_salary_old, $basic_tax_old) {
        if ($taxable_salary_old <= 500000) {
            return min(12500, $basic_tax_old);
        }
        // $margin_amount = $taxable_salary_old - 500000;
        // if ($margin_amount <= $basic_tax_old) {
        //     return $basic_tax_old - $margin_amount;
        // }
        return 0;
    }

    private function _tax_rebate_new($taxable_salary_new, $basic_tax_new, $fromDate, $toDate) {
        if($fromDate >= '2025-04-01' && $toDate <= '2026-03-31'){
            if ($taxable_salary_new <= 1200000) {
                return min(60000, $basic_tax_new);
            }
            $margin_amount = $taxable_salary_new - 1200000;
            if ($margin_amount <= $basic_tax_new) {
                return $basic_tax_new - $margin_amount;
            }
            return 0;
        } else {
            if ($taxable_salary_new <= 700000) {
                return min(25000, $basic_tax_new);
            }
            $margin_amount = $taxable_salary_new - 700000;
            if ($margin_amount <= $basic_tax_new) {
                return $basic_tax_new - $margin_amount;
            }
            return 0;
        }
    }

    private function _get_outside_ctc_allowance_v2($tax_declaration_data, $staff_months_in_year) {
        $columns = $this->CI->payroll_model->get_payroll_column_table();
        $taxDecleratedOutsideCTC = (array) $tax_declaration_data;
        $outsidectcolumnsValues = 0;

        foreach ($columns->payroll_column as  $colmn) {
            if ($colmn->outside_ctc_salary_strucutre == 1) {
                $key = $colmn->column_name;
                if (isset($taxDecleratedOutsideCTC[$key]) && $key !='reimbursement' && $key !='rent_reimbursment') {
                    $outsidectcolumnsValues += floatval($taxDecleratedOutsideCTC[$key]) * $staff_months_in_year;
                }
            }
        }        
        $yearly_outside_ctc_allowance = $outsidectcolumnsValues;

        //Use calculations to bring the correct value for all schools
        // $yearly_outside_ctc_allowance = ($tax_declaration_data->extra_allowance*$staff_months_in_year) + ($tax_declaration_data->hra_fixed*$staff_months_in_year) + ($tax_declaration_data->transport_allowance*$staff_months_in_year) + ($tax_declaration_data->co_ordinator_allowance*$staff_months_in_year) + ($tax_declaration_data->ib_retention_allowance*$staff_months_in_year) + ($tax_declaration_data->house_master_allowance*$staff_months_in_year);
        return $yearly_outside_ctc_allowance;
    }

    public function new_tax_calculation_with_changes($tax_declaration_data, $payslip_total_income, $staff_obj) {

        $yearly_ctc_with_employee_pf = $payslip_total_income['ctc_with_employee_pf'];
        $yearly_basic_salary_with_da = $payslip_total_income['basic_salary_with_da'];
        $yearly_hra = $payslip_total_income['hra'];
        $yearly_employee_pf = $payslip_total_income['employee_pf_contribution'];
        $yearly_outctc_allowances = $payslip_total_income['outside_ctc_allowance'] +  $payslip_total_income['additional_month_allowance'];
        $yearly_professional_tax = $payslip_total_income['professional_tax'];
        $yearly_vpf = $payslip_total_income['vpf'];

        $new_tds_cals = $this->tax_calculation($tax_declaration_data, $yearly_ctc_with_employee_pf, $yearly_basic_salary_with_da, $yearly_hra, $yearly_employee_pf, $yearly_outctc_allowances, $yearly_professional_tax, $staff_obj->gender, $staff_obj->age, $yearly_vpf);
        $selected_regime = $tax_declaration_data->tax_regime;

        $data = array(
            'tds_as_per_calc' => $selected_regime == '1' ? $new_tds_cals['nr_tax_amt'] : $new_tds_cals['or_tax_amt'],
        );
        return $data;
    }
}

?>
