<?php $institute = $this->settings->getSetting('your_word_for_institute'); ?>
<?php if(!empty($prev_eduction_info)) : ?>
<div class="row">
    <div class="panel panel-default">
        <div class="panel-heading">
           
            <h3 class="panel-title">Details of Previous Education</h3>
        </div>
        <div class="panel-body">
            <span class="help-block">
            Enter the previous education details.  Fill the details the year, add <?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> name, address and all click on 'Fill the details button'.</span>
            <form id="demo-form" action="" class="form-horizontal"  data-parsley-validate method="post" >
               <div class="card cd_border col-md-10">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 10%;"><?php if(in_array('year',$disabled_fields)) { echo 'Class/Course';}else{ echo 'Select Year';} ?></th>
                                <th style="width: 50%;"><?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> Name</th>
                                <?php if(in_array('year',$disabled_fields)) { ?> 
                                    <th style="width: 15%;">Year of Passing</th>
                                <?php }else{ ?>
                                    <th style="width: 15%;">Grade</th>
                                <?php } ?>
                                <th style="width: 25%;">Fill Previous Education Details</th>
                            </tr>
                        </thead>        
                        <tbody id="pre_school_table">
                            <?php
                            foreach ($previous_year_settings as $key => $year) { ?>
                            <tr>
                                <td id="<?php echo $year ?>" ><?php echo $year; ?> <?php if($required_fields['year']['required']=='required') echo '<font color="red">*</font>' ?></td>
                                <td class="required_checkYear" id="displayschoolName<?php echo $year ?>"></td>
                                <?php if(in_array('year',$disabled_fields)) { ?> 
                                    <td id="displayYear<?php echo $year ?>">-</td>
                                <?php }else{ ?>
                                    <td id="displayclassName<?php echo $year ?>">-</td>
                                <?php } ?>
                                <td>
                                    <button type="button" class="btn btn-primary btn_style2" style="margin: 0 0 0 4vh;"  data-toggle="modal" data-target="#previous_school_details_popup" onclick="fill_school_details('<?php echo $year ?>')" id="detail_popup"><span id="span_fill<?php echo $year; ?>">Fill Details</span><span id="span_edit<?php echo $year; ?>" style="color: yellow;">Edit Details</span><br><span id="successErrorMessage<?php echo $year ?>'"></span></button>
                                    <button type="button" class="btn btn-primary btn_style1 subject_btn" style="margin: 0 0 0 4vh;"  data-toggle="modal" data-target="#previous_school_subject_popup" onclick="fill_subject_details('<?php echo $year ?>')" id="subject_popup<?php echo $year ?>">Add Subjects</button>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif ?>

<div class="row" id="language_selection">
</div>

<?php $document_size = $this->settings->getSetting("documents_size_in_admissions"); if(empty($document_size)) { $document_size = 2 ;} ?>
<?php if(!empty($documents)){ ?>
<div class="row" id="newDocTabV1">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">Upload Documents</h3>
        </div>
        <div class="panel-body">
            <div class="col-md-9">
            <p><b>Note:</b> Only PDF, JPG, JPEG, PNG documents can be uploaded and File Size should be <?= $document_size ?> Mb.</p>
                <table class="table table-bordered" id="doc_table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="30%">Document Type</th>
                            <th colspan="2" width="20%">Upload</th>
                        </tr>
                    </thead>
                    <tbody id="doc_id">
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4">
                                <a href="javascript:;" class="btn btn-danger" id="addmore"><i class="fa fa-fw fa-plus-circle"></i> Add More</a>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
                <!-- <div><button class="btn btn-primary btn-sm" onclick="add_to_document_type()">Add more documents</button></div> -->
             <script>

$('#addmore').on('click', function () {
    var sl1 = $('#doc_id tr').length + 1;
    var trTag = `<tr id="hiderow${sl1}">
        <td style="width: 2%;">${sl1}</td>
        <td style="width: 30%;">
            <input type="text" name="document_for" id="document_for${sl1}" class="form-control" placeholder="Enter document name" value="">
        </td>
        <td style="border-right: none; width: 10%;">
            <div style="display: flex; align-items: center; gap: 5px;">
                <input type="file" onchange="upload_document_file_path(${sl1}, this)" name="document_file_path" class="documentUpload" id="doc-upload${sl1}" style="flex: 1;" />
                <i id="remove${sl1}" onclick="remove_but($(this))" class="fa fa-times-circle-o" style="font-size: 1.8rem; color: red; cursor: pointer;"></i>
            </div>
            <div id="afterSuccessUploadShow${sl1}"></div>
        </td>
        <td style="border-left: none;">
            <span id="percentage_doc_completed${sl1}" style="font-size: 20px; display: none;">0 %</span>
        </td>
    </tr>`;
    $("#doc_id").append(trTag);
});

                // var totalCount = '<?php // echo count($documents);  ?>';
                // var continueSl =  totalCount+1    
                // function add_to_document_type() {
                    
                //     $("#document_modals").modal('hide');
                //     var tr_tag= `<tr>
                //                     <td style="width: 2%;">${continueSl}</td>
                //                     <td style="width: 30%;" >${docu_for} <input onkeyup="disabled_fn(${continueSl})" class="form-control" type="text" name="document_for" id="document_for${continueSl}" value="${docu_for}" required=""></td>
                //                     <td style="border-right: none; width:10%">
                //                         <input type="file" onchange="upload_document_file_path(${continueSl})" name="document_file_path" class="documentUpload" disabled id="doc-upload${continueSl}" accept="application/pdf"/>
                //                         <button style="margin-top: 1rem;" id="remove_button${continueSl}" type="button" class="btn btn-primary btn-sm" onclick="delete_new_added_row(${continueSl})">Remove <span class="fa fa-times"></span></button>
                //                         <div id="afterSuccessUploadShow${continueSl}">
                //                         </div>
                //                     </td>
                //                     <td style="border-left: none;" ><span id="percentage_doc_completed${continueSl++}" style="font-size: 20px; display: none;">0 %</span></td>
                //                 </tr>`;
                               
                //                     // $("#doc-upload"+(sl-1)).attr('disabled', 'enabled');

                //     $("#doc_id").append(tr_tag);

                // }

                // function disabled_fn(continueSl) {
                //     $("#doc-upload"+continueSl).removeAttr('disabled');
                // }


                function remove_but(row) {
                    row.closest('tr').remove();
                    // Reorder serial numbers
                    $('#doc_id tr').each(function(index) {
                        $(this).find('td:first').text(index + 1); // Update serial number
                    });
                }

                function deletedocument_row_new(d_id, sl,required = '') {
                    var afid = $('#afid').val();
                    $.ajax({
                        url: '<?php echo site_url('admission_controller/delete_documentbyId'); ?>',
                        type: 'post',
                        data:{'d_id': d_id},
                        success: function(data) {
                            if(data !='') {
                                $('#removeButtonId'+sl).hide();
                                $('#successmessageId'+sl).hide();
                                $('#doc-upload'+sl).removeAttr('disabled');
                                $('#doc-upload'+sl).val('');
                                if(required){
                                    $('#doc-upload'+sl).attr('required','required');
                                }
                                // if(sl == sl1) {
                                    $("#hiderow"+sl).hide();
                                    sl1--;
                                // }
                                $("#remove"+sl).show();
                            } else {
                                alert("Something went wrong in Student data, try again.");
                                return 0;
                            }
                        }
                    });
                }
                function upload_document_file_path(sl,document) {
                    var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
                    var src = $(document).val();
                    if(src && validate_documents_config_based(document.files[0])){
                        $("#doc_error"+sl).html("");
                    } else{
                        $("#doc_error"+sl).html(`Allowed file size exceeded. (Max.  ${max_size_string} Mb) / Allowed file types are jpeg, jpg and pdf`);
                        $('#doc-upload'+sl).val('');
                        return;
                    }
                    var emptyCheck = $('#document_for'+sl).val();
                    if (emptyCheck =='') {
                        $('#doc-upload'+sl).val('');
                        $('#afterSuccessUploadShow'+sl).html('<p style="color:#ff4c13">Please enter document name</p>');
                        return false;
                    }
                    var file = event.target.files[0];
                    completed_promises = 0;
                    current_percentage = 0;
                    total_promises = 1;
                    in_progress_promises = total_promises;
                    var document_for = $('#document_for'+sl).val();
                    // console.log(document_for);
                    var af_id = '<?php echo $insert_id ?>';
                    saveFileToStorage_document(file,document_for,af_id, sl);
                    $('.file-preview').css('opacity', '0.3');
                }
                function saveFileToStorage_document(file, document_for,af_id, sl) {
                    $('#percentage_doc_completed'+sl).show();
                    $('#doc-upload'+sl).attr('disabled', 'disabled');
                    $("#document_submit").prop('disabled', true);
                    $.ajax({
                      url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
                      type: 'post',
                      data: {
                        'filename': file.name,
                        'file_type': file.type,
                        'folder': 'profile'
                      },
                      success: function(response) {
                        // console.log('Response: ',response)
                        single_file_progress_doc(0);
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;
                        $.ajax({
                          url: signedUrl,
                          type: 'PUT',
                          headers: {
                            "Content-Type": file.type,
                            "x-amz-acl": "public-read"
                          },
                          processData: false,
                          data: file,
                          xhr: function() {
                            var xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function(e) {
                              // For uploads
                              if (e.lengthComputable) {
                                single_file_progress_doc(e.loaded / e.total * 100 | 0,sl);
                              }
                            };
                            return xhr;
                          },
                          success: function(response) {
                            // return false;
                            $('#percentage_doc_completed'+sl).hide();
                            update_admission_student_documents(path, document_for,af_id, sl);
                            $('#doc-upload'+sl).removeAttr('disabled');
                            $('.file-preview').css('opacity', '1');
                            $("#document_submit").prop('disabled', false);
                            // resolve({path:path, name:file.name, type:file.type});
                            // increaseLoading();
                          },
                          error: function(err) {
                            // console.log(err);
                            reject(err);
                          }
                        });
                      },
                      error: function(err) {
                        reject(err);
                      }
                    });

                }
                function single_file_progress_doc(percentage, sl) {
                    if (percentage == 100) {
                      in_progress_promises--;
                      if (in_progress_promises == 0) {
                        current_percentage = percentage;
                      }
                    } else {
                      if (current_percentage < percentage) {
                        current_percentage = percentage;
                      }
                    }
                    $("#percentage_doc_completed"+sl).html(`${current_percentage} %`);
                    return false;
                }

                function update_admission_student_documents(path, document_for,af_id,sl) {
                    // console.log(document_for);
                    $.ajax({
                        url: '<?php echo site_url('admission_controller/update_documents_new'); ?>',
                        type: 'post',
                        data: {'path':path,'document_for':document_for,'af_id':af_id},
                        success: function(data) {
                            var docrowId = data.trim();
                            if (docrowId !=0) {
                                $('#doc-upload'+sl).attr('disabled','disabled');
                                var html ='';
                                html += '<a style="margin-top: 1rem;" id="successmessageId'+sl+'" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>'
                                html += '<a style="margin-top: 1rem;" onclick="deletedocument_row_new('+docrowId+','+sl+')" id="removeButtonId'+sl+'" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                                $('#afterSuccessUploadShow'+sl).html(html);
                                // $("#remove"+sl).attr('disabled', 'disabled');
                                $("#remove"+sl).hide();
                                // sl1++;
                            }

                        }
                    });
                }
            </script>
        </div>
    </div>
</div>

<?php } ?>


<style type="text/css">
    .fileinput-remove-button{
        display: none !important;
    }
    .fileinput-remove{
        display: none !important;
    }
</style>
<style type="text/css">
    .bootbox-close-button{
        display: none;
    }
</style>


<!-- Modal Pre-School -->
<div class="modal fade" id="previous_school_details_popup" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:2%; height: 105%;" data-backdrop="static" aria-labelledby="previous_school_details_popup-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title inline-block inline_class" id="modalHeader">Add Previous Education Details <span id="yearName"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1; " type="button" class="close inline_class" data-dismiss="modal">&times;</button>
        </div>

        <form enctype="multipart/form-data" id="school_previous_form" class="form-horizontal" data-parsley-validate method="post">
            <input type="hidden" id="schoolYear" name="schooling_year">
            <input type="hidden" id="schoolYearAfId">
            <input type="hidden" id="admission_prev_school_primary_id" name="admission_prev_school_primary_id">
            <input type="hidden" value='<?php echo $au_id ?>' name="au_id">
            <div class="modal-body" style="height: 45rem;overflow:scroll">

                <?php if(in_array('year',$disabled_fields)) { ?> 
                <div class="form-group">
                    <label class="control-label col-md-4">Year of Passing</label>
                    <div class="col-md-7 col-xs-12">
                        <select name="prev_school_year" id="prev_school_year" class="form-control select">
                            <option value="">Select Year</option>
                            <?php foreach($academic_years as $key =>$val) { ?>
                                <option value="<?= $val->acad_year ?>"><?= $val->acad_year ?></option>
                            <?php }?>
                        </select>
                    </div>
                </div>
                <?php }?>

                <div class="form-group">
                    <label class="control-label col-md-4"><?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> name <?php if($required_fields['school_name']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text"  <?php echo $required_fields['school_name']['required'] ?> name="schooling_school" id="schooling_school_new" class="form-control" placeholder="Enter the School Name">
                        </div>
                        <div class="help-block">Enter <?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> Name here as per the school records - Full name
                        </div>
                    </div>
                </div>
        
                <?php if(!in_array('school_address',$disabled_fields)) { ?> 
                <div class="form-group">
                    <label class="control-label col-md-4"><?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> Address <?php if($required_fields['school_address']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text" <?php echo $required_fields['school_address']['required'] ?> name="school_address"  id="school_address_new" class="form-control" placeholder="Enter the School Address">
                        </div>
                        <div class="help-block">Write <?php if($institute) { echo $institute ;}else{ echo 'School' ;} ?> Address here as per the school records - Full address with pincode.
                        </div>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('type_of_school',$disabled_fields)) { ?>  `

                <div class="form-group">
                    <label class="control-label col-md-4">Type of School<?php if($required_fields['type_of_school']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7">
                        <select class="form-control select" <?php echo $required_fields['type_of_school']['required'] ?> name="type_of_school" id="type_of_school">
                            <option value="">Select Type of School</option>
                            <option value="Public school"> Public school</option>
                            <option value="Private school">Private school</option>
                            <option value="Home School">Home School</option>
                        </select>
                        <div class="help-block">Choose Type of School here</div>
                    </div>
                </div>
                <?php } ?> 
                 
                <?php if(!in_array('medium_of_instruction',$disabled_fields)) { ?>  
                <div class="form-group">
                    <label class="control-label col-md-4">Medium of instruction <?php if($required_fields['medium_of_instruction']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7">
                        <select class="form-control select" <?php echo $required_fields['medium_of_instruction']['required'] ?> name="medium_of_instruction" id="medium_of_instruction_new">
                            <option value="">Select Medium of Instruction</option>
                            <option value="English">English</option>
                            <option value="Kannada">Kannada</option>
                        </select>
                        <div class="help-block">Choose Medium of Instruction here</div>
                    </div>
                </div>
                <?php } ?> 

                <?php $label = $this->settings->getSetting('your_word_for_class') ?>

                <?php if(admission_is_enabled($disabled_fields, 'class')) :  ?>
                <div class="form-group">
                    <label class="control-label col-md-4"><?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?>  <?php if($required_fields['class']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text" <?php echo $required_fields['class']['required'] ?> name="schooling_class" id="schooling_class_new" class="form-control" placeholder="Enter the Grade">
                        </div>
                        <div class="help-block">Enter <?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?> here</div>
                    </div>
                </div>
                <?php endif ?>
                    
                <?php if(admission_is_enabled($disabled_fields, 'registration_no')) :  ?>
                <div class="form-group">
                    <label class="control-label col-md-4">REGISTER NO/UNIQUE NO/ROLL NO <?php if($required_fields['registration_no']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text" <?php echo $required_fields['registration_no']['required'] ?> name="registration_no" id="registration_no_new" class="form-control" placeholder="Enter REGISTER NO/UNIQUE NO/ROLL NO ">
                        </div>
                        <div class="help-block">Add REGISTER NO/UNIQUE NO/ROLL NO</div>
                    </div>
                </div>
                <?php endif ?>
                    
                <div class="form-group">
                    <label class="control-label col-md-4">Board <?php if($required_fields['board']['required']=='required') echo '<font color="red">*</font>' ?></label>
                    <div class="col-md-7">
                        <select class="form-control select" <?php echo $required_fields['board']['required'] ?> name="schooling_board" id="schooling_board_new">
                            <option value="">Select Board</option>
                            <?php if(!empty($boards)) {
                                    foreach ($boards as $key => $value) { ?>
                                        <option value="<?php echo $value; ?>"><?php echo $value; ?></option>
                                    <?php }
                            } else { ?>
                                        <option value="CBSE">CBSE</option>
                                        <option value="ICSE">ICSE</option>
                                        <option value="STATE">STATE</option>
                                        <option value="Home School">Home School</option>
                                        <option value="IGCSE">IGCSE</option>
                                        <option value="IB">IB</option>
                                        <option value="NIOS">NIOS</option>
                                        <option value="PU Board">PU Board</option>
                                        <option value="ITI Board">ITI Board</option>
                                        <option value="KSEEB">KSEEB</option>
                                        <option value="Other">Other</option>
                            <?php } ?>   
                        </select>
                    </div>
                </div>
                <div id="board_other_show">
                    <div id="board_other" class="form-group">
                        <label class="col-md-4 control-label" for="board_other">Others</label>  
                        <div class="col-md-7">
                            <input placeholder="Enter Please Specify"  id="b_other" name="board_other" type="text"  class="form-control input-md"  data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-maxlength="6">
                            <span class="help-block">Please Specify</span>
                        </div>
                    </div>
                </div>


                <?php if(admission_is_enabled($disabled_fields, 'transfer_reason')) :  ?>
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="passport_issued_place">Reason for transfer/withdrawal from school  <?php if($required_fields['transfer_reason']['required']=='required') echo'<font color="red">*</font>' ?></label>  
                        <div class="col-md-7">
                            <textarea class="form-control" name="transfer_reason_id" id="transfer_reason_id_new" placeholder="Enter the reason for transfer/withdrawl from school"></textarea>
                        </div>
                    </div>
                <?php endif ?>

                <?php if(admission_is_enabled($disabled_fields, 'expelled_or_suspended')) :  ?>
                   
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="learning">Has student ever been expelled or suspended from school?</label>
                            <div class="col-md-7"> 
                                <label class="radio-inline" for="expelled_or_suspended-0">
                                    <input  type="radio" name="expelled_or_suspended" id="expelled_or_suspended-0" value="Yes" onclick="show1()">
                                    Yes
                                </label>
                                <label class="radio-inline" for="expelled_or_suspended-1">
                                    <input type="radio" name="expelled_or_suspended"  id="expelled_or_suspended-1" value="No" checked onclick="hide0()">
                                    No
                                </label>
                            </div>
                        </div>
                       <div id="expelled_or_suspended_id">
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="expelled_or_suspended_description">Add Reason</label>
                                <div class="col-md-7"> 
                                    <textarea class="form-control" name="expelled_or_suspended_description" id="expelled_or_suspended_description_new"></textarea>
                                </div>
                            </div>
                       </div>
                <?php endif ?>
                <?php if(admission_is_enabled($disabled_fields, 'report_card')) :  ?>
                    <br>
                   
                <div class="form-group" id="attachmentReport" style="display:none;">
                    <label class="col-md-4 control-label" for="fileupload">Report card Attachment</label>
                    <div class="col-md-7" id="download_link">
                    </div>
                </div>
                <div id="wrapper" class="form-group">
                    <label class="col-md-4 control-label" for="fileupload">Report card <?php if($required_fields['report_card']['required']=='required') echo'<font color="red">*</font>' ?></label>  
                    <div class="col-md-7">
                        
                        <input class="form-control removeAttrFileUpload" id="report_card_fileupload"  name="report_card" <?php echo $required_fields['report_card']['required'] ?> type="file" accept="image/jpeg, application/pdf"/>
                        
                        <span class="help-block">Allowed file types - jpeg, jpg,png,pdf; Allowed size - upto 2MB</span>
                        <span id="fileuploadError" style="color:red;"></span>
                    </div>
                </div>
                <?php endif ?>

                <?php if(admission_is_enabled($disabled_fields, 'previous_school_ratings')) :  ?>
                <div class="form-group">
                        <label class="col-md-4 control-label" for="learning">Rating of <?php if($institute) { echo $institute ;}else{ echo 'School'; }?></label>
                        <div class="col-md-7"> 
                            <label class="radio-inline" for="previous_school_ratings-1">
                                <input  type="radio" name="previous_school_ratings" id="previous_school_ratings-1" value="1">
                                1
                            </label>
                            &nbsp;
                            <label class="radio-inline" for="previous_school_ratings-2">
                                <input type="radio" name="previous_school_ratings"  id="previous_school_ratings-2" value="2"  >
                                2
                            </label>
                            &nbsp;
                            <label class="radio-inline" for="previous_school_ratings-3">
                                <input type="radio" name="previous_school_ratings"  id="previous_school_ratings-3" value="3"  >
                                3
                            </label>
                            &nbsp;
                            <label class="radio-inline" for="previous_school_ratings-4">
                                <input type="radio" name="previous_school_ratings"  id="previous_school_ratings-4" value="4"  >
                                4
                            </label>
                            &nbsp;
                            <label class="radio-inline" for="previous_school_ratings-5">
                                <input type="radio" name="previous_school_ratings"  id="previous_school_ratings-5" value="5"  >
                                5
                            </label>
                            <div class="col-md-12 help-block">On a scale of 1 to 5, with 1 Being the least and 5 being the best
                        </div>
                        </div>
                        
                    </div>
                    
                <?php endif ?>
            </div>
            <div class="modal-footer" style="width: 100%;">
                <div class="inline_class2"> <input class=" btn btn-primary btn_style4" id="ClearFormButton" type="text" onclick="clearform_schooldetails_alert()" value="Clear Form" ></div>
                <input type="button" id="save_school_previous_school" onclick="submit_previous_school_details()" class="btn btn-primary btn_style3" value="Submit">
            </div>
        </form>

       
    </div>
</div>

<script>
    function clearform_schooldetails_alert() {
         bootbox.confirm({
            message: 'Do you want clear this form. Are you sure ?',
            title:'Confirm',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if (result) {
                    $('#previous_school_details_popup form').trigger("reset");
                }
                
            }
        });
    }
   
</script>

<?php $label = $this->settings->getSetting('your_word_for_class') ?>
<!-- Modal Pre-Subject -->
<div class="modal fade" id="previous_school_subject_popup" tabindex="-1" role="dialog" style="width:100%;margin:auto;top:2%;" data-backdrop="static" aria-labelledby="previous_school_details_popup-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;width:50%">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <?php if (!$show_previous_schooling_subjects) { ?>
            <h4 class="modal-title inline-block inline_class" id=""><?php if($label) { echo $label;}else{ echo 'Grades' ;} ?> and marks obtained in Final Exam</h4>
            <?php } else { ?>
            <h4 class="modal-title inline-block inline_class" id="">Add Previous School Subjects</h4>
            <?php } ?>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1; padding-top: .5rem;" type="button" class="close inline_class" data-dismiss="modal">&times;</button>
        </div>
        <form enctype="multipart/form-data" id="school_subjects_form" class="form-horizontal" data-parsley-validate method="post">
            <input type="hidden" id="subject_schoolYear" name="schooling_sub_year">
            <input type="hidden"  value="<?php echo $insert_id ?>" name="sub_lastId">
            <input type="hidden" value='<?php echo $au_id ?>' name="sub_au_id">
            <div class="modal-body" style="">

                <div><div class="subject_details">
                    
                <hr>
                <!-- <?php //if ($show_previous_schooling_overall_total_marks) { ?>
                        <div class="form-group">
                            <label class="control-label col-md-3">Total Marks Scored </label>
                            <div class="col-md-8">
                                <input type="number" min="0" max="1000" readonly data-parsley-error-message="Invalid number" name="total_marks_scored" id="total_marks_scored" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-md-3">Total Marks </label>
                            <div class="col-md-8">
                                <input type="number" data-parsley-error-message="Invalid number" readonly min="0" max="1000" name="total_max_marks_entry" id="total_max_marks_entry" class="form-control">
                            </div>
                        </div>

                    <?php //} ?>
                    <?php //if ($show_previous_schooling_overall_grade) { ?>
                        <div class="form-group">
                            <label class="control-label col-md-3">Grade </label>
                            <div class="col-md-8">
                                <input type="text" name="total_grade" id="total_grade" class="form-control">
                            </div>
                        </div>
                    <?php //} ?>

                    

                    <?php //if ($show_previous_schooling_overall_percentage) { ?>
                        <div class="form-group">
                            <label class="control-label col-md-3">Total Percentage </label>
                            <div class="col-md-8">
                                <input type="text" data-parsley-error-message="Invalid number" readonly name="total_percentage" id="total_percentage" class="form-control">
                            </div>
                        </div>
                    <?php //} ?> -->
                    </div></div>
            </div>

            <div class="modal-footer" style="">
                <div class="inline_class2"> <input class="btn btn-primary btn_style4" type="reset" value="Clear Form" ></div>
                <button type="button" class="btn btn-primary inline_class2 btn_style3" onclick="submit_previous_school_subjects()" id="save_school_subjects">Add Subjects</button>
            </div>

        </form>
    </div>
</div>






  <style>
    .btn_style1 {
        border: 1px solid white;
        border-radius: 12px;
        background-color: #6893ca;
        text-align: center;
    }

     .btn_style2 {
        border-radius: 12px;
        border: 1px solid white;
        background-color: #17a2b8;
        text-align: center;
    }

     .btn_style3 {
        border-radius: 12px;
        border: 1px solid white;
        background-color: green;
        text-align: center;
    }

     .btn_style4 {
        border-radius: 12px;
        border: 1px solid white;
        background-color: gray;
        text-align: center;
    }

    .inline_class {
        display: inline;
    }

    .inline_class2 {
        display: inline;
    }
  </style>

<script type="text/javascript">
  function download_report_card_attachment() {
    var staffId = $('#workshop_staff_id').val();
    var workshop_staff_certificate_id = $('#workshop_staff_certificate_id').val();
    window.location.href = '<?php echo site_url('staff/Staff_controller/stafftraning_workshop_documents_download/'); ?>' + staffId + '/' + workshop_staff_certificate_id;
  }
</script>
  <script type="text/javascript">
    var schoolYear = '<?php echo json_encode($previous_year_settings) ?>';
    var schoolYears = $.parseJSON(schoolYear);
    var afId = '<?php echo $insert_id ?>';
    var gYearsSetting = '<?= json_encode($previous_year_settings)?>';
     var gYearDispaly = $.parseJSON(gYearsSetting);
     // console.log(gYearDispaly);
     // var yearLength = gYear.length;
    $(document).ready(function(){
         $("#previous_school_details_popup").find("#expelled_or_suspended_id").css({"display": "none"});
         $("#previous_school_details_popup").find("#board_other_show").css({"display": "none"});

        for(var k in schoolYears){
            get_if_exit_school_detailsperyear(afId, schoolYears[k]);
        }
    });
    
        if($('#schooling_board_new').find(":selected").text() == '') {
        $("#board_other_show").show();
    }

    //  $("input[name=learning]:radio").click(function () {
    //         if ($('input[name=learning]:checked').val() == "Y") {
    //             $('#expelled_or_suspended_id').show();
    //         } else {
    //         $('#expelled_or_suspended_id').hide();
    //         }
    //     });

        function show1() {
            $('#expelled_or_suspended_id').show();
        }

        function hide0() {
            $('#expelled_or_suspended_id').hide();
        }

    function fill_school_details(year) {
        $('#previous_school_details_popup form').trigger("reset");
        $("#schooling_board_new").val('').trigger("change");
        $('#schooling_board_new').selectpicker('refresh');
        $("#medium_of_instruction_new").val('').trigger("change");
        $('.medium_of_instruction_new').selectpicker('refresh');
        $('#prev_school_year').val('').trigger("change");
        $('.prev_school_year').selectpicker('refresh');
        $('#type_of_school').val('').trigger("change");
        $('.type_of_school').selectpicker('refresh');
        $('#yearName').html(year);
        $('#schoolYear').val(year);
        var afId = '<?php echo $insert_id ?>';
        $('#schoolYearAfId').val(afId);
        get_if_exit_school_detailsperyear(afId, year);

    }

   function fill_subject_details(year){
        $('#total_marks_scored').val('');
        $('#total_max_marks_entry').val('');
        $('#total_grade').val('');
        $('#total_percentage').val('');

        var admission_setting_id = '<?php echo $admission_setting_id ?>';
        var afId = '<?php echo $insert_id ?>';
        var au_id = '<?php echo $au_id ?>';
        $('#subject_schoolYear').val(year);
        get_admission_prev_school_id(afId, year).then(apsId => {
            $.ajax({
                url: '<?php echo site_url('admission_controller/get_subject_details_from_admission_settings'); ?>',
                type: 'post',
                data:{'admission_setting_id':admission_setting_id,'afId':afId,'au_id':au_id, 'pre_school_year': year,'apsId':apsId},
                success: function(data) {
                    $('.subject_details').html(data);
                }
            });
        });
    }
    function get_admission_prev_school_id(afid, year){
        return new Promise((resolve, reject) => {
            $.ajax({
                type: "POST",
                url: '<?php echo site_url('admission_controller/get_previous_school_detail_for_the_year'); ?>',
                data:{'afid':afid,'year':year},
                success: response => {
                    res = JSON.parse(response);
                    resolve(res.id)
                },
                error: error => reject(error)
            });
        });
    }

    function get_if_exit_school_detailsperyear(afid, year){
        
        $.ajax({
            url:'<?php echo site_url('admission_controller/get_previous_school_detail_for_the_year');?>',
            type:'post',
            data:{'afid':afid,'year':year},
            success:function(data){
                var schoolDetails = $.parseJSON(data);
                 $('#subject_popup'+year).prop('disabled',true);
                 $('#admission_prev_school_primary_id').val('');
                if(schoolDetails !=null){
                    $("#span_fill"+year).hide();
                    $("#span_edit"+year).show();
                    $('#admission_prev_school_primary_id').val(schoolDetails.id);
                    $('#displayschoolName'+year).html(schoolDetails.school_name);
                    $('#displayclassName'+year).html(schoolDetails.class);
                    $('#displayYear'+year).html(schoolDetails.year);
                    $('#schooling_school_new').val(schoolDetails.school_name);
                    $('#school_address_new').val(schoolDetails.school_address);
                    $('#schooling_class_new').val(schoolDetails.class);
                    $('#registration_no_new').val(schoolDetails.registration_no);
                    if (schoolDetails.medium_of_instruction !='') {
                        $("#medium_of_instruction_new").find(`option[value="${schoolDetails.medium_of_instruction}"]`).prop("selected", true)
                        $('#medium_of_instruction_new').selectpicker('refresh');
                    }
                    if (schoolDetails.type_of_school !='') {
                        $("#type_of_school").find(`option[value="${schoolDetails.type_of_school}"]`).prop("selected", true)
                        $('#type_of_school').selectpicker('refresh');
                    }
                    if (schoolDetails.year !='') {
                        $("#prev_school_year").find(`option[value="${schoolDetails.year}"]`).prop("selected", true)
                        $('#prev_school_year').selectpicker('refresh');
                    }
                    
                    $('#attachmentReport').show();
                    var downloadurl = '<?php echo site_url('admission_controller/admission_previous_school_report_card/') ?>' + schoolDetails.id;
                    let download = `<a target="_blank" class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`;

                    if (schoolDetails.report_card != null && schoolDetails.report_card != '') {
                      $("#download_link").html(download);
                    } else {
                      $("#download_link").html("No Document Present");
                    }
                    if (schoolDetails.board !='') {
                        $("#schooling_board_new").find(`option[value="${schoolDetails.board}"]`).prop("selected", true)
                        $('#schooling_board_new').selectpicker('refresh');
                    }
                    if (schoolDetails.expelled_or_suspended == 'Yes') {
                        $('#expelled_or_suspended-1').removeAttr('checked');
                        $('#expelled_or_suspended-0').attr('checked','checked');
                        $('#expelled_or_suspended_id').show();
                    }
                    if (schoolDetails.expelled_or_suspended == 'No') {
                        $('#expelled_or_suspended-0').removeAttr('checked');
                        $('#expelled_or_suspended-1').attr('checked','checked');
                        $('#expelled_or_suspended_id').hide();
                    }
                    if (schoolDetails.previous_school_ratings == '1') {
                        $('#previous_school_ratings-2').removeAttr('checked');
                        $('#previous_school_ratings-3').removeAttr('checked');
                        $('#previous_school_ratings-4').removeAttr('checked');
                        $('#previous_school_ratings-5').removeAttr('checked');
                        $('#previous_school_ratings-1').attr('checked','checked');
                    }
                    if (schoolDetails.previous_school_ratings == '2') {
                        $('#previous_school_ratings-1').removeAttr('checked');
                        $('#previous_school_ratings-3').removeAttr('checked');
                        $('#previous_school_ratings-4').removeAttr('checked');
                        $('#previous_school_ratings-5').removeAttr('checked');
                        $('#previous_school_ratings-2').attr('checked','checked');
                    }
                    if (schoolDetails.previous_school_ratings == '3') {
                        $('#previous_school_ratings-2').removeAttr('checked');
                        $('#previous_school_ratings-1').removeAttr('checked');
                        $('#previous_school_ratings-4').removeAttr('checked');
                        $('#previous_school_ratings-5').removeAttr('checked');
                        $('#previous_school_ratings-3').attr('checked','checked');
                    }
                    if (schoolDetails.previous_school_ratings == '4') {
                        $('#previous_school_ratings-2').removeAttr('checked');
                        $('#previous_school_ratings-1').removeAttr('checked');
                        $('#previous_school_ratings-3').removeAttr('checked');
                        $('#previous_school_ratings-5').removeAttr('checked');
                        $('#previous_school_ratings-4').attr('checked','checked');
                    }
                    if (schoolDetails.previous_school_ratings == '5') {
                        $('#previous_school_ratings-2').removeAttr('checked');
                        $('#previous_school_ratings-1').removeAttr('checked');
                        $('#previous_school_ratings-3').removeAttr('checked');
                        $('#previous_school_ratings-4').removeAttr('checked');
                        $('#previous_school_ratings-5').attr('checked','checked');
                    }
                    $('#transfer_reason_id_new').val(schoolDetails.transfer_reason);
                    $('#expelled_or_suspended_description_new').val(schoolDetails.expelled_or_suspended_description);
                    $('#subject_popup'+year).prop('disabled',false);
                }

                else {
                    $('#attachmentReport').hide();
                    $("#span_edit"+year).hide();
                    $("#span_fill"+year).show();
                    $('#expelled_or_suspended-0').removeAttr('checked');
                    $('#expelled_or_suspended-1').attr('checked','checked');
                    $('#expelled_or_suspended_id').hide();
                    $('input[name="previous_school_ratings"]').prop('checked', false);
                }
            }
        });

    }

    function submit_previous_school_details(){
        // var prev_school_year = $('#prev_school_year').val();
        var schoolYear = $('#schoolYear').val();
         var afId = '<?php echo $insert_id ?>';
        var $form = $('#school_previous_form');
        if ($form.parsley().validate()){
            var form = $('#school_previous_form')[0];
            var formData = new FormData(form);
            formData.append('lastId',afId);
            $('#save_school_previous_school').val('Please wait ...').attr('disabled','disabled');
            $('#ClearFormButton').attr('disabled','disabled');
            // Update and insert both 
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_previous_school_details_year_wise'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // $('#school_previous_form').trigger('reset');
                    $("#previous_school_details_popup").modal('hide');
                    
                     $('#save_school_previous_school').val('Submit').removeAttr('disabled');
                    if(data !=0){
                        $('#successErrorMessage'+schoolYear).html('Successfully added.');
                    }else{
                        $('#successErrorMessage'+schoolYear).html('Not added.');
                    }
                    // console.log(afId, schoolYear);
                    get_if_exit_school_detailsperyear(afId, schoolYear);
                }
            });
        }
    }

     function submit_previous_school_subjects(){

            var form = $('#school_subjects_form')[0];
            var formData = new FormData(form);
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_previous_school_subjects_year_wise'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $("#previous_school_subject_popup").modal('hide');
                }
            });
    }

function max_marks_total(m) {
    var maxmarks = $('#maxMarks_'+m).val();
    if(maxmarks == ''){
        $('#maxMarks_'+m).val('0');
    }
    var tmaxMarks = 0;
    $('.maxMarks').each(function() {
      tmaxMarks += parseFloat($(this).val());
    });

    $('#total_max_marks_entry').val(tmaxMarks);
    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}
function max_marks_scored_total(m) {
    var maxMarkScored = $('#maxMarkScored_'+m).val();
    if(maxMarkScored == ''){
        $('#maxMarkScored_'+m).val('0');
    }
    var tmaxMakrsScored = 0;
    $('.maxMakrsScored').each(function() {
      tmaxMakrsScored += parseFloat($(this).val());
    });
    $('#total_marks_scored').val(tmaxMakrsScored);

    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function check_is_value_not_empty(e, sl) {
    if(e.value !=''){
        $('#maxMarks_'+sl).attr({
           "max" : 125,       
           "min" : 100 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 125,       
           "min" : 0
        });
    }else{
        $('#maxMarks_'+sl).attr({
           "max" : 0,       
           "min" : 0 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 0,       
           "min" : 0
        });
    }
}


$("#btn_go").on('click', function(){
    validateUserDetails().done(function(data){
         if(data == "someValue")
            return "whatever you want";
    });
});

function documentSubmit_new() {
    var document_input_version = '<?php echo $config_val['document_input_version'] ?>';
    if(document_input_version == 'V2'){
        check_document_uploaded()
    }else if(document_input_version == 'V1'){
        check_previous_school_details()
    }
}

function check_document_uploaded(){
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    $.ajax({
            url: '<?php echo site_url('admission_controller/check_document_uploaded'); ?>',
            type: 'post',
            data: {'af_id':af_id,'adm_setting_id':adm_setting_id},
            success: function(data) {
                console.log(data);
                var resData = data.trim();
                if (resData == 0 ) {
                    alert('Upload the documents');
                    return false;
                }else{
                    check_previous_school_details()
                }
            }
        });
}

function check_previous_school_details(){
    var required = '<?php echo $required_fields['year']['required'] ?>';
    if (required == 'required' && gYearDispaly !=null) {
        var resData = [];
        for(var k in schoolYears){
            var valueCheck = $('#displayschoolName'+schoolYears[k]).html();
            resData.push(valueCheck);
        }
        var found = 0;
        $.each(resData, function (index, value) {
            if (value =='') {
                found = 0;
                return false;
            }else{
                found = 1;
            }
        });
        if (found) {
            $('.removeAttrFileUpload').removeAttr('required');

            var url = '<?php  echo site_url('admissions/preview') ?>';
            $('#document-form').attr('action',url);
            $('#document-form').submit();
            return true; 
        }else{
            alert("Enter previous details, then you can 'Save and Preview'.");
        }
    }else{
         $('.removeAttrFileUpload').removeAttr('required');
        $('#report_card_fileupload').removeAttr('required');
        $('#schooling_school_new').removeAttr('required');
        $('#school_address_new').removeAttr('required');
        $('#medium_of_instruction_new').removeAttr('required');
        $('#schooling_class_new').removeAttr('required');
        $('#registration_no').removeAttr('required');
        $('#schooling_board_new').removeAttr('required');
        var url = '<?php  echo site_url('admissions/preview') ?>';
        $('#document-form').attr('action',url);
        $('#document-form').submit();
        return true;
    }
}
</script>