<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('staff/leaves/dashboard'); ?>">Leaves Master</a></li>
	<li>Month-wise Leave Balance Report</li>
</ul>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px;">
				<div class="d-flex justify-content-between" style="width:100%;">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('staff/leaves/dashboard'); ?>">
							<span class="fa fa-arrow-left"></span>
						</a>
						Month-wise Leave Balance Report
					</h3>
					<div>
						<button id="stu_print" class="new_circleShape_res" style="margin-left: 8px; background-color: #fe970a;" data-placement="top" data-toggle="tooltip" title="Print" data-original-title="Print" onClick="printProfile();"><span class="fa fa-print" aria-hidden="true"></span></button>

						<button id="exportBtn" onclick="exportToExcel()" class="new_circleShape_res mr-2" style="margin-left: 8px; background-color: #fe970a;cursor: pointer;" data-placement="top" data-toggle="tooltip" title="" data-original-title="Export"><i class="fa fa-file-excel-o"></i></button>

					</div>
				</div>
			</div>
		</div>
		<div class="card-body pt-1">

			<div class="row mb-5">
				<?php
				$from = (isset($_POST["from"]) ? $_POST['from'] : "From Month");
				$to = (isset($_POST["to"]) ? $_POST['to'] : "To Month");
				?>
				<div class="col-md-12">
					<form action="" data-parsley-validate="" class="form-horizontal" id="form" style="">
						<div class="form-group d-flex" style="justify-content: space-between;align-items: self-end;">

							<!-- Make type of date range filter over here -->
							<div class="" style="width: 100%;margin-right:8px;">
								<label for="date_range_type">Select Date Range Type</label>
								<select name="" class="form-control" id="date_range_type" onchange="getSelectiveFilters()" data-parsley-required-message="required date range type" required>
									<!-- <option value="">Select Date Range Type</option> -->
									<option value="monthly" selected>Monthly Date Range</option>
									<option value="payroll_monthly">Payroll Schedule</option>
								</select>
							</div>

							<!-- Select financial year -->
							<div class="payroll_monthly" style="width: 100%;margin-right:8px;display: none;">
								<label for="financial_leave_year">Select Payroll Year</label>
								<select name="financial_leave_year" id="financial_leave_year" class="form-control" style="" data-parsley-required-message="required financial year" onchange="getFinancialMonths()">
									<option value="">Select Financial Year</option>
									<?php
										if(!empty($financial_leave_years)){
											foreach ($financial_leave_years as $key => $f_leave_year) { ?>
											<option value="<?php echo $f_leave_year->financial_year_id ?>">
												<?php echo $f_leave_year->f_year_name ?>
											</option>
									<?php } }else{
										echo '<option value="0">Not found financial year</option>';
									} ?>
								</select>
							</div>
							<!-- Select financial month -->
							<div class="payroll_monthly" style="width: 100%;margin-right:8px;display: none;">
								<label for="financial_leave_month">Select Payroll Month</label>
								<select name="financial_leave_month" id="financial_leave_month" data-parsley-required-message="required payroll month" class="form-control" style="">
									<option value="">Select Month</option>
								</select>
							</div>

							<div class="monthly" style="width: 100%;margin-right:8px;">
								<label for="year">Select Leave Year</label>
								<select name="year" id="year" class="form-control" style="" data-parsley-required-message="required leave year">
									<option value="">Select Year</option>
									<?php foreach ($leave_years as $key => $leave_year) { ?>
										<option name="<?php echo $leave_year->id ?>" value="<?php echo $leave_year->id ?>">
											<?php echo $leave_year->name ?>
										</option>
									<?php } ?>
								</select>
							</div>

							<div class="monthly" style="width: 100%;margin-right:8px;">
								<input type="hidden" name="yearId" id="yearId">
								<label for="month">Select Leave Month</label>
								<select name="month" id="month" data-parsley-required-message="required leave month" class="form-control" style="">
									<option value="">Select Month</option>
								</select>
							</div>

							<div style="width: 100%;margin-right:8px;">
								<label for="staff_status">Staff Status Type</label>
								<select class="form-control" name="staff_status" id="staff_status" data-parsley-required-message="" required>
									<option value="all">All</option>
									<?php foreach ($staff_status as $key => $val) {
										$selected="";
										if($val=="Approved"){
											$selected="Selected";
										}
										echo "<option $selected value='$key'>$val</option>";
									}?>
								</select>
							</div>


							<div class="col-md-3 form-group">
								<button type="button" class="btn btn-primary" onclick="leaveBalanceDetails()">Get Report</button>
							</div>
						</div>
					</form>
				</div>
			</div>
			<div id="loader-icon" style="display:none;">
				<div class="d-flex justify-content-center align-items-center" style="">
					<i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
				</div>
			</div>

			<div id="leave-no-data-msg">
				<div style="color:red;text-align:center;
								color: black;
								border: 2px solid #fffafa;
								text-align: center;
								border-radius: 6px;
								position: relative;
								padding: 10px;
								font-size: 14px;
								margin-top: 14px;
								background: #ebf3ff;">
								Select filters to get report!
								</div>
			</div>
			<div id="leave-data" class="tableFixHead" style="opacity: 0;overflow:auto;">
				<table class="table table-bordered" id="staff_leave_bal_rep_dt" style="width: 100%;white-space: nowrap;">
					<thead>
						<tr class="first">
							<th rowspan="2" style="width: 40px;">#</th>
							<th rowspan="2" style="min-width: 80px;">Employee Code</th>
							<th rowspan="2" style="min-width: 80px;">Staff Name</th>
							<th rowspan="2" style="min-width: 80px;">Date Of Joining</th>
							<th rowspan="2" style="min-width: 80px;">Date Of Exit</th>
							<?php
							foreach ($categories as $cat) {
								echo "<th colspan='3' class='text-center'>" . $cat['name'] . " (" . $cat['short_name'] . ")</th>";
							}
							?>
						</tr>
						<tr class="second">
							<?php
							foreach ($categories as $cat) {
								echo "<th class='text-center'>OB</th>";
								echo "<th class='text-center'>Used</th>";
								echo "<th class='text-center'>Balance</th>";
							}
							?>
						</tr>
					</thead>
					<tbody id="balance-data">
						
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<style>
      table {
        border-collapse: collapse; /* make the table borders collapse to each other */
        width: 100%;
      }
      th,
      td {
        padding: 8px 16px;
        border: 1px solid #ccc;
      }
      th {
        background: #eee;
      }
    </style>


<style type="text/css">
	.table>thead>tr>th,
	.table>tbody>tr>td {
		border: 1px solid #000 !important;
	}

	.new_circleShape_res {
		padding: 8px;
		border-radius: 50% !important;
		color: white !important;
		font-size: 22px;
		height: 3.2rem !important;
		width: 3.2rem !important;
		text-align: center;
		vertical-align: middle;
		float: left;
		border: none !important;
		box-shadow: 0px 3px 7px #ccc;
		line-height: 1.7rem !important;
	}
</style>

<script>
	function printProfile() {
		var restorepage = document.body.innerHTML;
		var printcontent = document.getElementById('leave-data').innerHTML;
		document.body.innerHTML = printcontent;
		window.print();
		document.body.innerHTML = restorepage;
	}
	$(document).ready(function() {

		var maxAttendenceDate = new Date();
		maxAttendenceDate.setDate(maxAttendenceDate.getDate());
		var minAttendenceDate = new Date();
		minAttendenceDate.setFullYear(minAttendenceDate.getFullYear() - 2);

		$('#datetimepicker6,#datetimepicker7').datetimepicker({
			viewMode: 'months',
			format: 'MM-YYYY',
			maxDate: maxAttendenceDate,
			minDate: minAttendenceDate
		});

		getSelectiveFilters();
	});
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
	function exportToExcel() {
		var wb = XLSX.utils.book_new();
		wb.Props = {
			Title: "Staff Leave Balance",
			Subject: "Staff Leave Balance Report",
			Author: "NextElement",
			CreatedDate: new Date()
		};

		wb.SheetNames.push('Leaves');
		// var ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
		var ws_school = XLSX.utils.table_to_sheet(document.getElementById('staff_leave_bal_rep_dt'));
		wb.Sheets['Leaves'] = ws_school;

		var wbout = XLSX.write(wb, {
			bookType: 'xlsx',
			type: 'binary'
		});
		downloadSample(wbout, "Staff Leave Balance");
	}

	function s2ab(s) {
		var buf = new ArrayBuffer(s.length);
		var view = new Uint8Array(buf);
		for (var i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
		return buf;
	}

	function downloadSample(wbout, file_name) {
		file_name = file_name + '.xlsx';
		saveAs(new Blob([s2ab(wbout)], {
			type: "application/octet-stream"
		}), file_name);
	};
</script>

<script type="text/javascript">
	// $(document).ready(function() {
	// 	// $("#leave-data").css("opacity", "0");
	// });

	const monthArray = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]

	$("#year").change(function() {
		let yearId = $("#year").val();

		$.ajax({
			url: "<?php echo site_url('staff/leaves/getLeaveYearById') ?>",
			type: "POST",
			data: {
				"yearId": yearId
			},
			success: function(data) {
				data = $.parseJSON(data)

				const year = Number(data.start_date.split('-').at(0))
				const year2 = Number(data.end_date.split('-').at(0))

				const start_month = Number(data.start_date.split('-').at(1));
				const end_month = Number(data.end_date.split('-').at(1));
				
				const monthsAdded = []
				let options = ``
				let isEndYearPresent = false;

				monthArray.forEach((m, i) => {
					i++;
					if (!isEndYearPresent && i >= start_month) {
						monthsAdded.push(i)
						options += `<option value="${year}-${i.toString().length==1 && '0'+i || i}">${m}</option>`;

						if(+year===+year2){
							if (i == end_month) isEndYearPresent = true
						}
					}
				})

				if (!isEndYearPresent) {
					monthArray.forEach((m, i) => {
						i++;
						if (i <= end_month) {
							options += `<option value="${year2}-${i.toString().length==1 && '0'+i || i}">${m}</option>`;
						}
					});
				}

				$("#yearId").val(data.id)
				$("#month").html(options);
			}
		})
	})

	function leaveBalanceDetails() {
		const selectedDateRangeType = $("#date_range_type").val()
		const new_payroll_schedules_id=$("#financial_leave_month").val();

		if(selectedDateRangeType==="payroll_monthly"){
			if(new_payroll_schedules_id=="0"){
				return alert("Financial month cannot be empty")
			}
		}

		const form = $("#form");
		if (form.parsley().validate()) {
			var month = $("#month").val();
			var year = $("#year").val();
			year = String(year).split("-")[0]

			var staffStatus = $("#staff_status").val();
			const yearId = $("#yearId").val()
			
			var categories = JSON.parse('<?php echo json_encode($categories) ?>');
			// console.log(categories);

			$.ajax({
				url: '<?php echo site_url('staff/leaves/getStaffLeaveMonthBalance'); ?>',
				type: "POST",
				data: {
					'month': month,
					"yearId": yearId,
					"staffStatus": staffStatus,
					'selectedDateRangeType':selectedDateRangeType,
					'new_payroll_schedules_id':new_payroll_schedules_id
				},
				beforeSend: function() {
					$("#leave-no-data-msg").html("");
					$('#loader-icon').show();
					$("#leave-data").css("opacity", "0");
					$('#staff_leave_bal_rep_dt').DataTable().destroy();
				},
				type: "post",
				success: function(data) {
					$("#loader-icon").hide();

					var balance = JSON.parse(data);
					if(!balance?.length){

						let NoDataMSG = `
							<div style="color:red;text-align:center;
								color: black;
								border: 2px solid #fffafa;
								text-align: center;
								border-radius: 6px;
								position: relative;
								padding: 10px;
								font-size: 14px;
								margin-top: 14px;
								background: #ebf3ff;">
								No Data to show
								</div>
							`;

						return $("#leave-no-data-msg").html(NoDataMSG);
					}else{
						$("#leave-data").css("opacity", "1");
						$("#leave-no-data-msg").html("");
					}

					var html = '';
					var sl = 0;
					balance.sort(function(a, b) {
						if (a.name > b.name) {
							return 1
						}
						if (a.name < b.name) {
							return -1
						}
						return 0;
					});

					for (var i in balance) {

						var total = 0;
						var totalQuota = 0;
						var used = 0;
						var remaining = 0;
						html += `
            		<tr>
            			<td>${sl+1}</td>
            			<td>${balance[i].employee_code}</td>;
            			<td>${balance[i].name}</td>;
            			<td>${balance[i].joining_date}</td>;
            			<td>${balance[i].date_of_exit}</td>`;
						for (var j = 0; j < categories.length; j++) {
							const leaveCatId = Number(categories[j].id);
							
							if(leaveCatId in balance[i]['categories']){
								// const totalQuota=balance[i]['categories'][leaveCatId]["total_quota"];
								const OB=balance[i]['categories'][leaveCatId]["ob"];
								const USEDQUOTA=balance[i]['categories'][leaveCatId]["used_quota"];
								const BALANCEQUOTA=balance[i]['categories'][leaveCatId]["balance_quota"];
								
								html += `<td class="text-center">${OB}</td>`; // opening balance
								html += `<td class="text-center">${USEDQUOTA}</td>`;
								html += `<td class="text-center">${BALANCEQUOTA}</td>`;
							}else{
								html += `<td class="text-center">-</td>`;
								html += `<td class="text-center">-</td>`;
								html += `<td class="text-center">-</td>`;
							}
						}
						html += `</tr>`;
						sl++;
					}
					$("#balance-data").html(html);

					// staff_leave_bal_rep_dt
					const reportName=`month_wise_staff_leave_balalance_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

               	 	var table = $('#staff_leave_bal_rep_dt').DataTable( {
					"language": {
						"search": "",
						"searchPlaceholder": "Enter Search..."
					},
					"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
					"pageLength": -1,
					// dom: 'lBfrtip',
					buttons: [
						{
						extend: 'excelHtml5',
						text: 'Excel',
						filename: reportName,
						className: 'btn btn-info'
						},
						{
						extend: 'print',
						text: 'Print',
						filename: reportName,
						autoPrint: true,
						className: 'btn btn-info'
						},
						// {
						// extend: 'csvHtml5',
						// text: 'CSV',
						// filename: reportName,
						// className: 'btn btn-info'
						// },
						{
						extend: 'pdfHtml5',
						text: 'PDF',
						filename: reportName,
						className: 'btn btn-info'
						}
					],
					scrollY:  '350px',
					fixedHeader: {
						header: true,
						footer: true
					},
					ordering:false
        	    });
				},
				error: function(err) {
					console.log(err);
				}
			});
		}
	}

	function getSelectiveFilters(){
		const selectedDateRangeType=$("#date_range_type").val();
		const el=document.querySelector("#financial_leave_year");

		// get all monthly elements
		const m_elmts=document.querySelectorAll(".monthly");
		m_elmts.forEach(el=>{
			const select=el.querySelector("select");
			select.removeAttribute("required","required")
		});

		const payroll_elmts=document.querySelectorAll(".payroll_monthly");
		payroll_elmts.forEach(el=>{
			const select=el.querySelector("select");
			select.removeAttribute("required","required")
		});
		
		if(selectedDateRangeType=="monthly"){
			m_elmts.forEach(el=>{
			const select=el.querySelector("select");
			select.setAttribute("required","required")

			$(".monthly").show();
			$(".payroll_monthly").hide();

		});
		}else if(selectedDateRangeType=="payroll_monthly"){
			payroll_elmts.forEach(el=>{
			const select=el.querySelector("select");
			select.setAttribute("required","required")

			$(".monthly").hide();
			$(".payroll_monthly").show();

		});
		}
	}

	function getFinancialMonths(){
		const selectedFinancialYear=$("#financial_leave_year").val();
		$.ajax({
			url:"<?php echo site_url('staff/leaves/getPayrollMonthsByFinancialYearId') ?>",
			type:"POST",
			data:{selectedFinancialYear},
			success:function(res){
				const data=JSON.parse(res);

				// let options=`<option>Select payroll month</option>`;
				let options=``;
				if(data?.length){
					data.forEach(p=>{
						options+=`<option value="${p.new_payroll_schedules_id}">${p.schedule_name}</option>`;
					});
				}else{
					options=`<option value="0">Found No payroll month</option>`;
				}
0
				$("#financial_leave_month").html(options);
			}
		})
	}
</script>

<style>
    .fixedStaffName{
        position: sticky;
        left: 0;
        background: #fff;
    }

    .paging_simple_numbers{
        bottom:25px;
    }

    .btn_align {
        margin-bottom: 4px;
        width: 32px;
    }

    ul.panel-controls>li>a {
        border-radius: 50%;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .modal{
        z-index: 1061;
    }
    @media (max-width: 576px) {
    /* Adjust the width for smaller screens (e.g., mobile devices) */
        .custom-swal {
            width: 200% !important;
        }
    }

    @media (min-width: 577px) and (max-width: 992px) {
        /* Adjust the width for medium-sized screens (e.g., tablets) */
        .custom-swal {
            width: 70% !important;
        }
    }

    @media (min-width: 993px) {
        /* Reset the width for larger screens (e.g., desktops) */
        .custom-swal {
            width: 40% !important; /* Same as the initial width */
        }
    }
     @media (max-width: 576px) {
    /* Adjust the width for smaller screens (e.g., mobile devices) */
        .view-custom-swal {
            width: 200% !important;
        }
    }

    @media (min-width: 577px) and (max-width: 992px) {
        /* Adjust the width for medium-sized screens (e.g., tablets) */
        .view-custom-swal {
            width: 70% !important;
        }
    }

    @media (min-width: 993px) {
        /* Reset the width for larger screens (e.g., desktops) */
        .view-custom-swal {
            width: 40% !important; /* Same as the initial width */
        }
    }

    .dataTables_wrapper{
        width:200%;
    }

    .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		/* float: right; */
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		/* float: right; */
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 2%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

    #formControlRange{
        margin-bottom:10px;
    }

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 2%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}

    .remark-show-hide{
        display: none;
    }

    /* new css */
    .dataTables_paginate{
        position: absolute;
        right: 0;
    }

    ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
    }

    /* Create a custom scrollbar */
    ::-webkit-scrollbar-track {
    background-color: #f2f2f2;
    border-radius: 10px;
    }

    /* Create a thumb for the scrollbar */
    ::-webkit-scrollbar-thumb {
    /* background-color: #007bff; */
    background-color: #777;
    border-radius: 0px;
    }

    /* Make the scrollbar visible when hovering over the track */
    ::-webkit-scrollbar-track-piece-over:hover {
    background-color: #F5F5F5;
    }

    /* Make the scrollbar thumb visible when hovering over it */
    ::-webkit-scrollbar-thumb:hover {
    background-color: #777;
    }

    ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #F5F5F5;
    border-radius: 0px;
    }

    ::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
    height: 8px;
    }

    ::-webkit-scrollbar-thumb {
    background-color: #777;
    border-radius: 0px;
    }

    .swal2-popup{
        width:50%;
    }

    .dt-buttons, .dataTables_filter{
        position: absolute;
        /* top: 7rem; */
    }
	
	.monthly{
		background: none;
		color: black;
	}
</style>