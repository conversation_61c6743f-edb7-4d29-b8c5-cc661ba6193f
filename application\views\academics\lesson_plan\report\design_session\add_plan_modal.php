<div class="modal fade" id="add_plan" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto; position: relative;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title" id="plan-name-here">Add Plan</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="plan_session_id" id="plan_session_id">
                <div class="form-group">
                    <label for="minute" class="control-label">Minute <font style="color: red;">*</font></label>
                    <input type="number" value="0" placeholder="minutes" min="0" class="form-control" name="minute" id="minute">
                    <span id="minuteError" style="display:none;"></span>
                </div>
                <div class="form-group">
                    <label for="plan" class="control-label">Plan <font style="color: red;">*</font></label>
                    <textarea class="form-control" name="plan" id="plan" placeholder="Enter Plan" style="height: 11rem;"></textarea>
                    <span id="planError" style="display:none;"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_plan_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_plan_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="plan()">Update</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // $("#add_plan").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    let planType;
    let URL = '<?php echo site_url("academics/Lesson_plan/"); ?>';

    $("#minute,#plan").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#minuteError, #planError").hide();
            if($("#minute").val() <= 0){
                $("#minuteError").html("This field is required.").css("color", "red").show();
                return false;
            }
            if($("#plan").val() == ""){
                $("#planError").html("This field is required.").css("color", "red").show();
                return false;
            }
            updatePlan();
            if (planType == "Beginning") {
                loadBeginningPlan();
            } else if (planType == "Middle") {
                loadMiddlePlan();
            } else if (planType == "End") {
                loadEndPlan();
            }
        }
    })

    function updatePlan() {
        const id = $("#plan_session_id").val();
        const value = $("#plan").val();
        const visible_to_students = $("#visible_plan_to_students").is(":checked") && 1 || 0;
        updateMinute(id);
        const planURL = URL.concat(`Update${planType}Plan`);
        $.ajax({
            url: planURL,
            type: "POST",
            data: { id, value, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_plan").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        $('#minute').val(0);
                        $('#plan').val('');
                        getSessionData(id);
                        if (planType == "Beginning") {
                            loadBeginningPlan();
                        } else if (planType == "Middle") {
                            loadMiddlePlan();
                        } else if (planType == "End") {
                            loadEndPlan();
                        }
                        showMainModal();
                    });
                } else {
                    $("#add_plan").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Activity not saved",
                        text: "Activity not saved successfully!",
                    }).then(() => {
                        $("#add_plan").modal('show');
                    });
                }
            },
            error(err){
                console.log(err);
                $("#add_plan").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Activity not saved",
                    text: "Activity not saved successfully!",
                }).then(() => {
                    $("#add_plan").modal('show');
                });
            }
        })
    }

    function updateMinute(id) {
        const value = $("#minute").val();
        if(value <= 0){
            return false;
        }
        const minuteURL = URL.concat(`Update${planType}Minute`);
        $.ajax({
            url: minuteURL,
            type: "POST",
            data: { id, value },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_plan").modal('hide');
                } else {
                    $("#add_plan").modal('hide');
                }
            },
            error(err){
                console.log(err);
                $("#add_plan").modal('hide');
            }
        })
    }

    function plan() {
        $("#minuteError, #planError").hide();
        if($("#minute").val() <= 0){
            $("#minuteError").html("This field is required.").css("color", "red").show();
            return false;
        }
        if($("#plan").val() == ""){
            $("#planError").html("This field is required.").css("color", "red").show();
            return false;
        }
        updatePlan();
        if (planType == "Beginning") {
            loadBeginningPlan();
        } else if (planType == "Middle") {
            loadMiddlePlan();
        } else if (planType == "End") {
            loadEndPlan();
        }
    }
</script>