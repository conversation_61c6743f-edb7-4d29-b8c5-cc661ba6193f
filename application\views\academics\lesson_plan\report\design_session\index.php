<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index') ?>">Academics</a></li>
    <li class="">Design Session</li>
</ul>
<div class="col-md-12 col_new_padding">
    <div class="card cd_border" style="border: none;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index') ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Design Session
                </h3>
            </div>
        </div>
        <div class="card-body body">
            <div id="session-desc-path" class="col-md-12 px-2" style="position: relative;display: none;">
                <div class="col-md-10 p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <th>Grade</th>
                                <th>Subject</th>
                                <th>Lesson</th>
                                <th>Topic</th>
                                <th>Session</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span id="grade_name">Grade</span>
                                    </td>
                                    <td>
                                        <span id="subject_name">Subject</span>
                                    </td>
                                    <td>
                                        <span id="lesson_name">Lesson</span>
                                    </td>
                                    <td>
                                        <span id="topic_name">Topic</span>
                                    </td>
                                    <td>
                                        <span id="session_name">Session</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col-md-2 <?php echo $is_semester_scheme == 1 ? 'addStyle' : '' ?>" style="">
                    <div class="new_circleShape_res tooltipNew eye-btn" style="display: none; margin-left: 8px; background-color:#6693ca !important;cursor:pointer" id="" data-toggle="modal" data-target="#full_session_detail" onClick="show_session_detail()">
                        <span class="fa fa-eye mt-1" aria-hidden="true"></span>
                        <span class="tooltiptextNew">View</span>
                    </div>

                    <div class="btn-div" style="display:none;">
                        <div class="new_circleShape_res tooltipNew" style="margin-left: 8px; background-color:#fe970a !important;cursor:pointer;display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;" data-action="open-chooser" data-sectionid="0" data-sectionreturnid="0" id="" onclick="" data-toggle="modal" data-target="#resources_modal">
                            <span class="fa fa-plus" aria-hidden="true" style="position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);"></span>
                            <span class="tooltiptextNew">Add an Activity or Resources</span>
                        </div>
                    </div>
                </div>

            </div>

            <!-- manual options for filter -->
            <div class="mannual-filters" style="position: relative;display: none;">
                <form action="" class="form-group" style="margin-bottom: 10px;">
                    <div class="row m-0">
                        <div class="col-md-2">
                            <label> Select Grade <font color="red">*</font></label>
                            <select class="form-control" name="select_grade" id="select_grade" onchange="<?php echo $is_semester_scheme == 1 ? 'getSemesters()' : 'getSubjetsList()'; ?>">
                                <option value="">Select Grade</option>
                            </select>
                        </div>
                        <div class="col-md-2" id="semester_container" style="display: <?php echo $is_semester_scheme == 1 ? 'block' : 'none'; ?>;">
                            <label> Select Semester <font color="red">*</font></label>
                            <select class="form-control" name="select_grade" id="select_semester" onchange="getSubjetsList()">
                                <option value="">Select Semester</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label> Select Subject <font color="red">*</font></label>
                            <select class="form-control" name="select_subject" id="select_subject" onchange="getCurrentSubjectLessons()">
                                <option value="">Select Subject</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label> Select Lesson <font color="red">*</font></label>
                            <select class="form-control" name="select_lesson" id="select_lesson" onchange="getTopicsList()">
                                <option value="">Select Lesson</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label> Select Topic <font color="red">*</font></label>
                            <select class="form-control" name="select_topic" id="select_topic" onchange="getSessionList()">
                                <option value="">Select Topic</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label> Select Session <font color="red">*</font></label>
                            <select class="form-control" name="select_session" id="select_session" onchange="getSession()">
                                <option value="">Select Session</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-center <?php echo $is_semester_scheme == 1 ? 'addStyle' : '' ?>" style="">
                            <div class="new_circleShape_res tooltipNew eye-btn" style="display: none; background-color: #6693ca !important;cursor:pointer" id="" data-toggle="modal" data-target="#full_session_detail" onClick="show_session_detail()">
                                <span class="fa fa-eye mt-1" aria-hidden="true"></span>
                                <span class="tooltiptextNew">View</span>
                            </div>

                            <div class="btn-div" style="display:none;">
                                <div class="new_circleShape_res tooltipNew" style="margin-left: 8px; background-color: #fe970a !important;cursor:pointer;display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;" data-action="open-chooser" data-sectionid="0" data-sectionreturnid="0" id="" onclick="" data-toggle="modal" data-target="#resources_modal">
                                    <span class="fa fa-plus" aria-hidden="true" style="position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);"></span>
                                    <span class="tooltiptextNew">Add an activity or resource</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="heading" style="display: none;">
                    <div class="heading-content">
                        <h2>Design Session for Grade <span class="grade-subject"><span id="heading_grade">Loading...</span> - <span id="heading_subject">Loading...</span></span></h2>
                    </div>
                </div>
            </div>
            <div id="card-container" class="resources-container">
            </div>
        </div>
    </div>
</div>

<?php
// Add New Activity or Learning Resource 
$this->load->view('academics/lesson_plan/report/design_session/resources_modal');

// View Session Detail
$this->load->view('academics/lesson_plan/report/view_resources/view_session_detail/view_session_detail_modal');

// View Activity or Learning Resource
$this->load->view('academics/lesson_plan/report/view_resources/learning_context/view_learning_context_modal');
$this->load->view('academics/lesson_plan/report/view_resources/learning_objective/view_learning_objective_modal');
$this->load->view('academics/lesson_plan/report/view_resources/learning_intention/view_learning_intention_modal');
$this->load->view('academics/lesson_plan/report/view_resources/skills/view_skills_modal');
$this->load->view('academics/lesson_plan/report/view_resources/success_criteria/view_success_criteria_modal');
$this->load->view('academics/lesson_plan/report/view_resources/beginning_plan/view_beginning_plan_modal');
$this->load->view('academics/lesson_plan/report/view_resources/middle_plan/view_middle_plan_modal');
$this->load->view('academics/lesson_plan/report/view_resources/end_plan/view_end_plan_modal');
$this->load->view('academics/lesson_plan/report/view_resources/extended_learning/view_extended_learning_modal');
$this->load->view('academics/lesson_plan/report/view_resources/contingency_plan/view_contingency_plan_modal');
$this->load->view('academics/lesson_plan/report/view_resources/resources/view_resources_modal');
$this->load->view('academics/lesson_plan/report/view_resources/book_resources/view_book_resources_modal');
$this->load->view('academics/lesson_plan/report/view_resources/additional_information/view_additional_information_modal');
$this->load->view('academics/lesson_plan/report/view_resources/assessments/view_assessments_modal');
?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    var grade;
    var subject_id;
    var lesson_name;
    var topic_name;
    var session_name;
    var session_id;
    var lesson_id;
    var topic_id;
    var assessmentType, book_resourceType, learningObjectiveType, resourceType, session_details, skillType;
    let parentModalToReturn;
    const displayFormat = {
        "Meta Data": {
            learning_context: "Learning Context",
            learningObjectiveType: "Learning Objectives",
            learning_intention: "Learning Intention",
            skillType: "Skill",
            success_criteria: "Success Criteria"
        },
        "Execution Plan": {
            beginning_plan: "Beginning Plan",
            middle_plan: "Middle Plan",
            end_plan: "End Plan",
            extended_learning: "Extended Learning",
            contingency_plan: "Contingency Plan"
        },
        "Resources": {
            resourceType: "Digital Resources",
            book_resourceType: "Book References",
            additional_information: "Additional Information",
            assessmentType: "Assessments",
            session_details: "Session Details"
        }
    };

    const resourceNameFormat = {
        learning_context: "Learning Context",
        learningObjectiveType: "Learning Objectives",
        learning_intention: "Learning Intention",
        skillType: "Skill",
        success_criteria: "Success Criteria",
        beginning_plan: "Beginning Plan",
        middle_plan: "Middle Plan",
        end_plan: "End Plan",
        extended_learning: "Extended Learning",
        contingency_plan: "Contiengency Plan",
        resourceType: "Digital Resources",
        book_resourceType: "Book References",
        additional_information: "Additional Information",
        assessmentType: "Assessments",
        session_details: "Session Details",
    }

    function loadClasses() {
        let classArray = <?php echo json_encode($classes); ?>;

        if (classArray.length) {
            let options = `<option value="">Select Grade</option>`;

            let selected;
            // let isGradeSessionPresent = false;
            classArray.forEach(c => {
                options += `<option ${window.localStorage.getItem("design_class_master_id") == c.class_master_id && "Selected"} value="${c.class_master_id}">${c.class_name}</option>`
                // if (window.localStorage.getItem("design_class_master_id") == c.class_master_id) {
                //     isGradeSessionPresent = true;
                // }
            })
            $("#select_grade").html(options);

            // if (isGradeSessionPresent) {
                const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
                if (is_semester_scheme == 1) {
                    getSemesters();
                } else {
                    getSubjetsList();
                }
            // }
        } else {
            options += `<option value="">Classes not found</option>`;
            $("#select_grade").html(options);
        }
    }

    $("document").ready(function () {
        grade = "<?php echo $grade; ?>";
        subject_id = "<?php echo $subject_id; ?>";
        lesson_id = "<?php echo $lesson_id; ?>";

        topic_id = "<?php echo $topic_id; ?>";
        session_id = "<?php echo $session_id; ?>";

        lesson_name = "<?php echo $lesson_name; ?>";
        topic_name = "<?php echo $topic_name; ?>";
        session_name = "<?php echo $session_name; ?>";

        if (session_id) {
            $("#session-desc-path").hide();
            $(".mannual-filters").show();
            $(".btn-div").show();

            getSubjectNameAndGrade(subject_id, grade);
            $("#lesson_name").text(lesson_name);
            $("#topic_name").text(topic_name);
            $("#session_name").text(session_name);
            $(".session_id").val(session_id);
            // getSessionData(session_id);

            window.localStorage.setItem("design_class_master_id", grade);
            window.localStorage.setItem("design_subject_id", subject_id);
            window.localStorage.setItem("design_lesson_id", lesson_id);

            window.localStorage.setItem("design_topic_id", topic_id);
            window.localStorage.setItem("design_session_id", session_id);

            // loadClasses();
        } else if (window.location.pathname.split("/").splice(-3)[0] >= 0) {
            const URL = window.location.pathname.split("/").splice(-3);

            const design_class_master_id = URL[0];
            const design_subject_id = URL[1];
            const design_lesson_id = URL[2];

            const urlObtainedData = {
                design_class_master_id,
                design_subject_id,
                design_lesson_id,
            }

            $("#session-desc-path").hide();
            $(".mannual-filters").show();

            window.localStorage.setItem("design_class_master_id", design_class_master_id);
            window.localStorage.setItem("design_subject_id", design_subject_id);
            window.localStorage.setItem("design_lesson_id", design_lesson_id);

            // loadClasses();
        } else {
            $("#session-desc-path").hide();
            $(".mannual-filters").show();

            // loadClasses();
        }
        loadClasses();
    })

    function getSession() {
        session_id = $("#select_session").val();
        $(".session_id").val(session_id);
        window.localStorage.setItem("design_session_id", session_id);
        $(".heading").hide();
        $(".eye-btn").show();
        $(".btn-div").show();
        $("#card-container").show();
        getSessionData(session_id);
    }

    function getSemesters() {
        const class_id_main = $("#select_grade").val();
        $.ajax({
            url: "<?php echo site_url('academics/lesson_plan/get_semesters') ?>",
            type: "POST",
            data: { "class_master_id": class_id_main },
            success(data) {
                const semesters = JSON.parse(data);
                if (!semesters?.length) {
                    $("#select_semester").html("<option value='' disabled selected>No semesters</option>");
                    return;
                }

                let options = ``;
                semesters.forEach(s => {
                    options += `
                        <option ${window.localStorage.getItem("manage_semester_id") == s.id && "Selected"} value='${s.id}'>${s.sem_name}</option>
                    `;
                });

                $("#select_semester").html(options);
                getSubjetsList();
            }
        });
    }

    function getSubjetsList() {
        const class_master_id = $("#select_grade").val();
        
        window.localStorage.setItem("design_class_master_id", class_master_id);

        const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
        const semester_id = $("#select_semester").val() || 0;
        if (semester_id) {
            window.localStorage.setItem("design_semester_id", semester_id);
        }
        if(!semester_id && is_semester_scheme == 1) {
            $('#card-container').html(`<div class="no-data-display">Please Select A Semester.</div>`);
            return;
        };
        if(!class_master_id) {
            $('#card-container').html(`<div class="no-data-display">Please Select A Grade.</div>`);
            return;
        };
        $(".heading").hide();
        $(".eye-btn").hide();
        $(".btn-div").hide();
        // $("#card-container").hide();
        $("#select_subject").empty();
        $("#select_subject").html("<option value=''>Select Subject</option>");
        $("#select_lesson").empty();
        $("#select_lesson").html("<option value=''>Select Lesson</option>");
        $("#select_topic").empty();
        $("#select_topic").html("<option value=''>Select Topic</option>");
        $("#select_session").empty();
        $("#select_session").html("<option value=''>Select Session</option>");
        $("#card-container").html(`<div class="no-data-display">Loading...</div>`);
        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/get_subjects_list') ?>',
            type: 'post',
            data: {
                class_master_id,
                is_semester_scheme,
                semester_id
            },
            success: function (data) {
                let isSectionSessionPresent = false;
                var resData = $.parseJSON(data);
                output = '<option value="">Select Subject</option>';

                let isGradeSubjectPresent = false;
                for (var k = 0; k < resData.length; k++) {
                    output +=
                        `<option ${window.localStorage.getItem("design_subject_id") == resData[k].id && "Selected"} value='${resData[k].id}'>${resData[k].subject_name}</option>`;
                    if (window.localStorage.getItem("design_subject_id") == resData[k].id) {
                        isGradeSubjectPresent = true;
                    }
                }
                $('#select_subject').html(output);

                if (isGradeSubjectPresent) getCurrentSubjectLessons();
                else $("#card-container").html(`<div class="no-data-display">Please Select A Subject.</div>`);
            },
        });
    }

    function getCurrentSubjectLessons() {
        const subjectId = $("#select_subject").val();
        $("#select_lesson").empty();
        $("#select_lesson").html("<option value=''>Select Lesson</option>");
        $("#select_topic").empty();
        $("#select_topic").html("<option value=''>Select Topic</option>");
        $("#select_session").empty();
        $("#select_session").html("<option value=''>Select Session</option>");
        window.localStorage.setItem("design_subject_id", subjectId);
        $(".heading").hide();
        $(".eye-btn").hide();
        $(".btn-div").hide();
        // $("#card-container").hide();
        $("#card-container").html(`<div class="no-data-display">Loading...</div>`);

        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_subject_lessons') ?>",
            type: "POST",
            data: {
                subjectId
            },
            success(data) {
                data = $.parseJSON(data);
                let isLessionSessionPresent = false;
                let options = `<option value="0">Select Lesson</option>`
                if (data.length) {

                    let isGradeLessonPresent = false;
                    data.forEach(l => {
                        options +=
                            `<option ${window.localStorage.getItem("design_lesson_id") == l.id && "Selected"} value="${l.id}">${l.lesson_name}</option>`
                        if (window.localStorage.getItem("design_lesson_id") == l.id) {
                            isGradeLessonPresent = true;
                        }
                    });
                    $("#select_lesson").html(options);

                    if (isGradeLessonPresent) getTopicsList();
                    else $("#card-container").html(`<div class="no-data-display">Please Select A Topic.</div>`);
                }
            }
        })
    }

    function getTopicsList() {
        const subject_id = $("#select_subject").val();
        const lesson_id = $("#select_lesson").val();
        $("#select_topic").empty();
        $("#select_topic").html("<option value=''>Select Topic</option>");
        $("#select_session").empty();
        $("#select_session").html("<option value=''>Select Session</option>");
        window.localStorage.setItem("design_lesson_id", lesson_id);
        $(".heading").hide();
        $(".eye-btn").hide();
        $(".btn-div").hide();
        // $("#card-container").hide();
        $("#card-container").html(`<div class="no-data-display">Loading...</div>`);

        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/view_lesson') ?>',
            type: 'POST',
            data: {
                subject_id
            },
            success: function (data) {
                const topicData = $.parseJSON(data).viewLessons;
                topicData.forEach(l => {
                    if (subject_id == l.lp_subject_id && lesson_id == l.lesson_id) {
                        let html = `<option value="">Select Topic</option>`;

                        let selected, isGradeTopicPresent = false;
                        l.sub_topic_arr.forEach(t => {
                            html +=
                                `<option ${window.localStorage.getItem("design_topic_id") == t.sub_topic_id && "Selected"} value="${t.sub_topic_id}">${t.sub_topic_name}</option>`;
                            if (window.localStorage.getItem("design_topic_id") == t
                                .sub_topic_id) {
                                isGradeTopicPresent = true;
                            }
                        })
                        $("#select_topic").html(html);

                        if (isGradeTopicPresent) getSessionList();
                        else $("#card-container").html(`<div class="no-data-display">Please Select A Session.</div>`);
                    }
                })
            }
        });
    }

    function getSessionList() {
        const class_master_id = $("#select_grade").val();
        const subject_id = $("#select_subject").val();
        const lesson_id = $("#select_lesson").val();
        const lp_topic_id = $("#select_topic").val();

        window.localStorage.setItem("design_topic_id", lp_topic_id);
        $("#select_session").empty();
        $("#select_session").html("<option value=''>Select Session</option>");
        $(".heading").hide();
        $(".eye-btn").hide();
        $(".btn-div").hide();
        // $("#card-container").hide();
        $("#card-container").html(`<div class="no-data-display">Loading...</div>`);

        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_sessionList') ?>",
            type: "POST",
            data: {
                class_master_id,
                subject_id,
                lesson_id,
                lp_topic_id
            },
            success(data) {
                const sessionList = $.parseJSON(data).sessionList;

                if (sessionList.length) {
                    let isSessionPresent = false;
                    let options = `<option value="">Select Session</option>`;
                    sessionList.forEach(s => {
                        options +=
                            `<option ${window.localStorage.getItem("design_session_id") == s.id && "Selected"} value="${s.id}">${s.session_code}</option>`;
                        if (window.localStorage.getItem("design_session_id") == s.id) {
                            isSessionPresent = true;
                        }
                    })
                    $("#select_session").html(options);
                    if (isSessionPresent) getSession();
                    else $("#card-container").html(`<div class="no-data-display">Please Select A Session.</div>`);
                } else {
                    options = `<option value="">Session not found</option>`;
                    $("#select_session").html(options);
                    $("#card-container").show();
                    $("#card-container").html(`<div class="no-data-display">No Session Found, Please Create One.</div>`);
                }
            }
        })
    }

    function getSubjectNameAndGrade(subjectId, gradeId) {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_LP_Subject_name') ?>",
            type: "POST",
            data: {
                subjectId,
                gradeId
            },
            success(data) {
                data = $.parseJSON(data)

                const gradeName = data.grade_name;
                $("#grade_name").text(`G${gradeName.class_name}`);

                const subjectName = data.subject_name;
                $("#subject_name").text(subjectName.subject_name);
            }
        })
    }

    function setHeading() {
        const gradeName = $("#select_grade option:selected").text();
        const subjectName = $("#select_subject option:selected").text();

        $("#heading_grade").text(gradeName);
        $("#heading_subject").text(subjectName);
    }

    var enabled_activities_for_lms = <?php echo json_encode($this->settings->getSetting("enable_lms_activities")) ?>;

    if(enabled_activities_for_lms==false){
        enabled_activities_for_lms=totalActivities;
    }
    
    var enabledActiviesObj = {};
    if (enabled_activities_for_lms?.length) {
        $("#message").hide();
        enabled_activities_for_lms.forEach(enabledActivity => {
            enabledActiviesObj[enabledActivity] = enabledActivity;
        })
    } else {
        $("#message").show();
    }

    function getSessionData(session_id) {
        window.localStorage.setItem("design_session_id", session_id);
        $("#card-container").html(`<div class="no-data-display">Loading...</div>`);
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = JSON.parse(data);
                // Check if data is empty or null
                if (!data || Object.keys(data).length === 0) {
                    $(".heading").hide();
                    $("#card-container").html(`<div class="no-data-display">No Session Data Available</div>`);
                    return;
                }
                
                if (data.session_details && Object.keys(data.session_details).length > 0) {
                    session_details = data.session_details;
                }
                if (data.learningObjectiveType && Object.keys(data.learningObjectiveType).length > 0) {
                    learningObjectiveType = data.learningObjectiveType;
                }
                if (data.skillType && Object.keys(data.skillType).length > 0) {
                    skillType = data.skillType;
                }
                if (data.assessmentType && Object.keys(data.assessmentType).length > 0) {
                    assessmentType = data.assessmentType;
                }
                if (data.resourceType && Object.keys(data.resourceType).length > 0) {
                    resourceType = data.resourceType;
                }
                if (data.book_resourceType && Object.keys(data.book_resourceType).length > 0) {
                    book_resourceType = data.book_resourceType;
                }

                let outerCards = ``;
                const displayCategories = Object.keys(displayFormat);
                let hasAnyContent = false;

                for (let category of displayCategories) {
                    let hasSectionContent = false;
                    let sectionCards = ``;

                    for (let key in displayFormat[category]) {
                        const label = displayFormat[category][key];
                        // Check session_details keys
                        if (data.session_details.hasOwnProperty(key)) {
                            const value = data.session_details[key];
                            if (value == null) {
                                continue;
                            }
                        } else if (Array.isArray(data[key])) {
                            if (data[key].length === 0) continue;
                        }

                        // Use session_details if available, otherwise fallback to data[key]
                        const val = session_details && key in session_details ? session_details[key] : (data[key] || '');

                        // Check if value exists and is enabled
                        if (!val || !(typeof val.length === "number") || !(key in enabledActiviesObj)) continue;

                        hasSectionContent = true;
                        hasAnyContent = true;

                        const minuteData = session_details ? (
                            key === "beginning_plan" ? session_details.beginning_minute :
                            key === "middle_plan" ? session_details.middle_minute :
                            key === "end_plan" ? session_details.end_minute :
                            "no minute"
                        ) : "no minute";

                        const cardHtml = `
                            <div class="card" style="border: none;">
                                <div class="content_box">
                                    <div class="row m-0 d-flex align-items-center">
                                        <div class="col-md-11 py-3">
                                            <div data-toggle="modal" data-target="#view_${key}" data-session_id="${session_id}" ${category === "Execution Plan" ? `data-minute="${minuteData}"` : ''} data-value="${val}" style="cursor:pointer;margin-left: 1rem;">
                                                <h4 class="mb-2"><i>${label}</i></h4>
                                                <h5 class="m-0">View resources</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-1 d-flex justify-content-end">
                                            <button type="button" class="btn btn-danger btn-lg" onClick="${category === "Meta Data" || category === "Execution Plan" ? `deleteUpdateActivity('${key}','${session_id}')` : `deleteActivity('${key}','${session_id}')`}" style="border-radius:1.1rem; display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">
                                                <i class="fa fa-trash-o m-0" aria-hidden="true" style="font-size:1.4rem"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        sectionCards += cardHtml;
                    }

                    if (hasSectionContent) {
                        outerCards += `
                            <div class="section-row">
                                <h5 class="section-title">${category}</h5>
                                <div class="section-content">
                                    ${sectionCards}
                                </div>
                            </div>
                        `;
                    }
                }

                if (!hasAnyContent) {
                    $(".heading").hide();
                    $("#card-container").html(`<div class="no-data-display">No Resources To Show, Please Add One!</div>`);
                } else {
                    setHeading();
                    $(".heading").show();
                    $("#card-container").html(outerCards);
                }
            },
            error: function(xhr, status, error) {
                console.error('Ajax error:', error);
                $(".heading").hide();
                $("#card-container").html(`<div class="no-data-display">Error loading resources. Please try again.</div>`);
            }
        });
    }

    function deleteActivity(activityName, session_id) {
        Swal.fire({
            title: `Are you sure?`,
            html: `<h5>Remove <b>${resourceNameFormat[activityName]}</b></h5>`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes",
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                const url = `<?php echo site_url('academics/Lesson_plan/') ?>delete_${activityName}`;
                $.ajax({
                    url: url,
                    type: "POST",
                    data: {
                        session_id
                    },
                    success(data) {
                        getSessionData(session_id);
                    }
                })
            }
        });
    }

    function deleteUpdateActivity(activityName, session_id) {
        let updateFormatNames = {
            learning_context: "LearningContext",
            learning_intention: "LearningIntention",
            success_criteria: "SuccessCriteria",
            extended_learning: "Extendedlearning",
            contingency_plan: "ContengencyPlan",
            beginning_plan: "BeginningPlan",
            middle_plan: "MiddlePlan",
            end_plan: "EndPlan",
            additional_information: "AdditionalInformation"
        }

        Swal.fire({
            title: `Are you sure?`,
            html: `<h5>Remove <b>${resourceNameFormat[activityName]}</b></h5>`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes",
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                const url = `<?php echo site_url('academics/Lesson_plan/') ?>update${updateFormatNames[activityName]}`;
                $.ajax({
                    url: url,
                    type: "POST",
                    data: {
                        "id": session_id,
                        "value": ""
                    },
                    success(data) {
                        getSessionData(session_id);
                    }
                })
            }
        });
    }
</script>

<style>
    .heading {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .heading-content {
        text-align: center;
    }

    .heading h2 {
        color: #2c3e50;
        font-size: 1.8rem;
        margin: 0;
        font-weight: 600;
    }

    .grade-subject {
        color: #3498db;
        font-weight: 700;
    }

    .resources-container {
        padding: 1.5rem 0;
    }

    .section-row {
        margin-bottom: 2.5rem;
        background: #ffffff;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #eef2f7;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background: #3498db;
    }

    .section-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .content_box {
        background: #ffffff;
        border: 1px solid #e1e8ed;
        border-radius: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .content_box:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #3498db;
    }

    .content_box .row {
        padding: 1.25rem;
        margin: 0;
    }

    .content_box h4 {
        color: #2c3e50;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .content_box h5 {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    .btn-danger {
        background-color: #e74c3c;
        border: none;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
        opacity: 0.9;
    }

    .btn-danger:hover {
        background-color: #c0392b;
        transform: scale(1.05);
        opacity: 1;
    }

    /* .no-data-display {
        text-align: center;
        padding: 2.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        color: #6c757d;
        font-size: 1.1rem;
        grid-column: 1 / -1;
        border: 1px dashed #dee2e6;
    } */

    @media screen and (max-width: 1200px) {
        .section-content {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media screen and (max-width: 768px) {
        .section-content {
            grid-template-columns: 1fr;
        }

        .section-title {
            font-size: 1.2rem;
        }
    }

    .body {
        width: 85%;
        margin: auto;
    }

    .addBtn {
        border-radius: 0.5rem;
        color: #0f6cbf;
        background-color: #f5f9fc;
        border: 1px solid #3584c9;
        width: 100%;
        font-size: 15px;
        padding: 21px;
        transition: all 200ms ease-in;
    }

    .addBtn:hover {
        border: 1px solid #3584c9;
        background: #CFE2F2;
        text-decoration: underline;
    }

    .pluscontainer {
        border: 1px solid;
    }

    .fa-plus {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }

    .outer-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        border: 1px solid #DEE2E6;
        padding: 5px;
        margin-bottom: 10px;
        transition: all 300ms ease-out;
    }

    .outer-card:hover {
        background-color: #f5f9fc;
        border: 1px solid #3584c9;
    }

    .left-part {
        display: flex;
        justify-content: center;
    }

    .icon-here {
        padding-right: 10px;
        font-size: 39px;
    }

    .title-desc {
        font-size: 15px;
        font-weight: 400;
    }

    .title {
        color: #336DB5;
    }

    .desc {
        color: #399BE2;
    }

    .menu-action:hover {
        color: #fff;
        background: #0f6cbf;
    }

    #session-desc-path {
        font-size: 19px;
        color: #598dcd;
    }

    .right-arrow {
        position: relative;
        bottom: 2px;
    }

    .new_circleShape_res {
        padding: 10px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 4.2rem !important;
        width: 4.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .content_box {
        border: solid 1px #eee;
        border-radius: 1rem;
        margin-bottom: 1rem;
    }

    /* Tooltip */
    .tooltipNew {
        position: relative;
        display: inline-block;
        border-bottom: 1px dotted black;
        /* If you want dots under the hoverable text */
    }

    /* Tooltip text */
    .tooltipNew .tooltiptextNew {
        visibility: hidden;
        width: auto;
        bottom: 75%;
        left: 50%;
        background-color: #484848;
        color: #fff;
        text-align: center;
        padding: 5px 20px;
        border-radius: 6px;
        font-size: 12px;
        /* Position the tooltip text - see examples below! */
        position: absolute;
        transform: translate(-50%, -50%);
        z-index: 1;
        white-space: nowrap;
    }

    /* Show the tooltip text when you mouse over the tooltip container */
    .tooltipNew:hover .tooltiptextNew {
        visibility: visible;
    }

    .addStyle {
        position: absolute;
        right: -15rem;
        top: 1rem;
        margin-top: -8px;
    }

    @media screen and (max-width:1290px) {
        .body {
            width: 98%;
            margin: auto;
        }

    }

    @media screen and (max-width:1600px) {
        .addStyle {
            right: -11rem;
        }
    }

    .new_circleShape_res {
        margin-top: 8px;
    }
</style>