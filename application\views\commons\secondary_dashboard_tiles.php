<div class="row" style="margin: 0px;">
	<?php if(!empty($tile_list) && isset($heading) && $heading != '') { ?>
	    <div class="col-md-12">
	        <div class="m-0 d-flex">
	            <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a"><?php echo $heading ?></p></div>
	            <div class="mt-1 flex-fill"><hr></div>
	        </div>
	    </div>
	<?php } ?>
    <div class="col-md-12">
        <div class="row m-0">
			<?php 
				foreach ($tile_list as $key => $tile) { 
					$click_function = '';
					if(isset($tile['click_function'])) {
						$click_function = 'onclick="'.$tile['click_function'].'"';
					}
					$disabled = '';
					$message = '';
					$url = $tile['url'];
					if($this->mobile_detect->isMobile() && isset($tile['mobile_enabled']) && $tile['mobile_enabled'] == 0) {
						$url = '#';
						$disabled = 'disabled';
						$message = 'Not available for mobile devices';
					} else if($this->mobile_detect->isTablet() && isset($tile['tablet_enabled']) && $tile['tablet_enabled'] == 0) {
						$url = '#';
						$disabled = 'disabled';
						$message = 'Not available for tablet devices';
					}
					?>
					<div class="<?php echo $this->mobile_detect->isTablet() ? 'col-md-4' : ' col-lg-3 col-md-4 col-sm-4 col-xs-6' ?>">
			          	<a class="<?php echo $disabled; ?>" href="<?php echo $url; ?>" target = <?= $tile['title'] == 'Student 360 Degree'? '_blank' : ''?> <?php echo $click_function; ?>>
				            <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
				              	<div class="widget-item-left" style="width:52px;">
				              		<?php if ($tile['icon']!=''){ ?>
				                	
									<span class="animate__animated animate__fadeIn">
										<?php $this->load->view($tile['icon']) ?>
									</span>
									<!-- <img class="img-responsive" src="<?php //echo $tile['icon'] ?>"> -->
									
				                <?php } else { ?>
				                	<span class="<?php echo $tile['fa_icon'] ?>" style="font-size:48px;"></span>
				                <?php } ?>
				              	</div>
				              	<div class="widget-data" style="padding-left:78px;">
				                	<div class="widget-title" style="<?php echo $this->mobile_detect->isTablet() ? 'padding-top: 12px;' : 'padding-top:20px;' ?>"><?php echo $tile['title']; ?></div>
				                	<!-- <div class="widget-subtitle" style="color:#929292;"><?php //echo $tile['sub_title']; ?></div> -->
				                	<!-- <div class="widget-subtitle" style="color:#929292;"><?php// echo $message; ?></div> -->
				              	</div>
				            </div>
			          	</a>
			        </div>
			<?php } ?>
		</div>
	</div>
</div>
  

  <!-- Use this website for more animation styles https://animate.style/#documentation -->

<style type="text/css">
	.widget .widget-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 4px;
        line-height: 1.6rem;
        text-transform: none;
    }

.disabled {
	opacity: 0.6;
}
</style>