<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.15.0"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/morris.js') ?>"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<?php $this->load->view("msm_v3/scripts/color_theme") ?>
<script type="text/javascript">
    var json_school_list = '<?php echo $json_school_list ?>';
    var school_list = JSON.parse(json_school_list);
    var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';

    function waitOneSecondSync() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve();
            }, 400);
        });
    }

    $(document).ready(async function () {
        google.charts.load("current", {packages: ["corechart", "bar"]});
        $(".student_tb_btn").css("display", "block");
        $(".overview_tb_dropdown").css("display", "block");
        $(".overview_tb_btn").css("display", "none");

        load_theme();

        display_student_count(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_non_compliance_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_observation_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_counselling_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_stu(school_list, default_acad_year);
        display_student_counselling_statuswise_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        // display_schools_dropdown_student(school_list, default_acad_year);
        display_student_nationalitywise_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_schools_dropdown_stu_birthday(school_list, default_acad_year);
        display_student_birthday_widget(school_list, default_acad_year);
    });

    const selectElement = document.getElementById('acad_select');
    selectElement.value = default_acad_year;
    selectElement.addEventListener('change', async function() {
        default_acad_year = selectElement.value;
        sessionStorage.setItem("msm_acad_year", selectElement.value);

        display_student_count(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_non_compliance_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_observation_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_counselling_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_counselling_statuswise_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_nationalitywise_widget(school_list, default_acad_year);
        await waitOneSecondSync();
        display_student_birthday_widget(school_list, default_acad_year);
    });

    /* Student Count */
    function display_student_count(school_list, acad_year, type="Bar") {
        $('#student_count_statistics').html('<center>Loading Student Statistics...</center>');
        $('#student_management_table').html("<tr><td colspan='11'>Loading Student Statistics...</td></tr>");
        const overall_student_statistics = [];
        var boys_count = 0;
        var girls_count = 0;
        var unsigned_count = 0;
        //Call each school's data
        const mapLoop = async () => {
            const promises = await school_list.map(async school => {
            const num_promise = await get_student_data(school.school_code, school.school_domain, acad_year)
            .then(response => {
                if (!response) return true;
                overall_student_statistics[overall_student_statistics.length] = [school.school_code.toUpperCase(),parseInt(response.total_boys), parseInt(response.total_girls), parseInt(response.total_unassigned), parseInt(response.total_rte), parseInt(response.nonRTE_count), parseInt(response.scholarship_count), parseInt(response.staff_kids),  parseInt(response.total_students)];
                return true;
            })
            .catch(err => {
                console.log(err);
                return false;
            });
            });
            await Promise.all(promises)

            //Arrange in a sequence
            var temp_array = [];
            school_list.forEach(sl => {
            overall_student_statistics.forEach(ola => {
                if (sl.school_code.toUpperCase() == ola[0]) {
                temp_array.push(ola);
                return false;
                }
            });
            });

            var total_student_count = 0;
            temp_array.forEach(obj => {
            total_student_count += parseInt(obj[1]);
            boys_count += parseInt(obj[2]);
            girls_count += parseInt(obj[3]);
            unsigned_count += parseInt(obj[4]);
            })

            //Add to top bar
            // $('#top_bar_student_count').html(`${total_student_count}`);
            // $('#student_count_stat').html(`${total_student_count}`);
            // $("#studstat_this_month").html(`Boys: ${boys_count} Girls: ${girls_count} Unsigned: ${unsigned_count}`);
            _construct_student_count_view(temp_array.slice(), type);
            _construct_student_counts_table(temp_array);
        }
        mapLoop();
    }

    function get_student_data(school_code, school_domain, acad_year) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'acad_year': acad_year,
                    'api': 'get_student_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_student_count_view(input_array, type) {
        const display_array = [];

        input_array.forEach(obj => {
            display_array.push([obj[0].toUpperCase(), parseInt(obj[2]), parseInt(obj[3]), parseInt(obj[4]), parseInt(obj[1])]);
            });

        google.charts.setOnLoadCallback(function()  {
            const data = new google.visualization.DataTable();
            data.addColumn('string', 'School Code');
            data.addColumn('number', 'Boys');
            data.addColumn('number', 'Girls');
            data.addColumn('number', 'Unassigned');
            data.addColumn('number', 'Total');

            data.addRows(display_array);

            var options;
            var view;
            var formatPercent = new google.visualization.NumberFormat({
                pattern: '#,##0%'
            });

            options = {
                isStacked: 'percent',
                height: 450,
                // width: 1400,
                chartArea: {left: '15%', top: "15%", width: '70%', bottom: '10%', height: '100%'},
                hAxis: {
                    title: "Count",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                    bar: { groupWidth: "10%" },
                    axisTitlesPosition: "out",
                },
                vAxis: {
                    title: "Instituition",
                    titleTextStyle: {
                        color: "#1450A3",
                        fontName: "Arial",
                        fontSize: "16",
                        bold: true,
                        italic: false,
                    },
                },
                annotations: {
                    alwaysOutside: false,
                    textStyle: {
                    fontSize: 10,
                    bold: true
                    },
                },
                series: {
                    0: {
                    color: boys_color,
                    areaOpacity: 0.85,
                    visibleInLegend: true,
                    annotations: {
                        stem: {
                        color: 'transparent',
                        length: 16
                        }
                    },
                    },
                    1: {
                    color: girls_color,
                    areaOpacity: 0.85,
                    visibleInLegend: true,
                    annotations: {
                        stem: {
                        color: 'transparent',
                        length: 16
                        }
                    },
                    },
                    2: {
                    color: "none",
                    areaOpacity: 0.25,
                    visibleInLegend: true,
                    annotations: {
                        alwaysOutside: true,
                        stem: {
                        color: 'transparent',
                        length: 40
                        }
                    },
                    },
                },
                legend: {
                    position: 'top', alignment: 'center'
                }
                };

                var view = new google.visualization.DataView(data);
                view.setColumns([
                                0,
                                1,{ sourceColumn: 1,
                                    type: "number",
                                    role: "annotation" },
                                2, { sourceColumn: 2,
                                    type: "string",
                                    calc: function (dt, row) {
                                    return dt.getValue(row, 2) + " (" + dt.getValue(row, 4) + ")";
                                    },
                                    role: "annotation" },
                                ]);

        var chartType = type+"Chart";
        var chartConstructor = google.visualization[chartType];
        var chart = new chartConstructor(document.getElementById('student_count_statistics'));
        chart.draw(view, options);
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${boys_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Male</span>
                    <div style="width: 15px; height: 15px; background-color: ${girls_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Female</span>
                    </div>`;
        $("#student_count_legend_div").html(html);
        });
    }

    function _construct_student_counts_table(input_array) {
        enableTableButtons();
        var html = '';
        var i = 1;
        let total_boys = 0;
        let total_girls = 0;
        let total_unassigned = 0;
        let total_rte = 0;
        let total_nonrte = 0;
        let total_scholarship = 0;
        let total_staff_kids = 0;
        let fee_paying_students = 0;
        let total_students = 0;

        input_array.forEach((obj, i) => {
            html += `
                <tr>
                    <td class="align-middle text-center text-sm">${i + 1}</td>
                    <td class="align-middle text-center text-sm">${obj[0]}</td>
                    <td class="align-middle text-center text-sm">${obj[1]}</td>
                    <td class="align-middle text-center text-sm">${obj[2]}</td>
                    <td class="align-middle text-center text-sm">${obj[3]}</td>
                    <td class="align-middle text-center text-sm">${obj[4]}</td>
                    <td class="align-middle text-center text-sm">${obj[5]}</td>
                    <td class="align-middle text-center text-sm">${obj[6]}</td>
                    <td class="align-middle text-center text-sm">${obj[7]}</td>
                    <td class="align-middle text-center text-sm">${obj[8] - obj[7] - obj[4] - obj[6]}</td>
                    <td class="align-middle text-center text-sm">${obj[8]}</td>
                </tr>
            `;

            // Accumulate totals for columns 3-8 (obj[2] - obj[7])
            total_boys += obj[1];
            total_girls += obj[2];
            total_unassigned += obj[3];
            total_rte += obj[4];
            total_nonrte += obj[5];
            total_scholarship += obj[6];
            total_staff_kids += obj[7];
            fee_paying_students += obj[8] - obj[7] - obj[4] - obj[6];
            total_students += obj[8];
        });

        // Add a row for the totals
        html += `
            <tr>
                <td class="align-middle text-center text-sm">${input_array.length + 1}</td>
                <td class="align-middle text-center text-sm"><strong>Total</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_boys}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_girls}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_unassigned}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_rte}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_nonrte}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_scholarship}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_staff_kids}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${fee_paying_students}</strong></td>
                <td class="align-middle text-center text-sm"><strong>${total_students}</strong></td>
            </tr>
        `;

        $('#student_management_table').html(html);
    }

    /* Student Non Compliance */
    function display_student_non_compliance_widget(school_list, acad_year) {
        disableTableButtons();
        $("#student_nc_statistics_graph").html("<center>Loading Student Non Compliance Statistics...</center>");
        const monthCounts = {};
        var total_count = 0;

        const mapLoop = async () => {
        const promises = school_list.map(async (school) => {
            try {
            const response = await get_student_non_compliance_data(school.school_code, school.school_domain, acad_year);
            if (response) {
                response.forEach((item) => {
                const month = item.month;
                const count = parseInt(item.count);
                total_count += count;
                if (monthCounts[month]) {
                    monthCounts[month] += count;
                } else {
                    monthCounts[month] = count;
                }
                });
            }
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);
        const dataArray = Object.entries(monthCounts).map(([month, count]) => ["", count, month]);
        
        _construct_student_non_compliance_view(dataArray);
        _construct_student_non_compliance_table(dataArray);
        };

        google.charts.setOnLoadCallback(mapLoop);
    }

    function get_student_non_compliance_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_student_non_compliance_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_student_non_compliance_view(input_array) {
        // console.log(input_array);
        if(input_array.length > 0){
        // Prepare data structure
        const dataMap = new Map(); // Map to store data for each month
        // Fill dataMap
        input_array.forEach(function (item) {
            const month = getMonthName(item[2]);
            const count = parseInt(item[1]) || 0;

            if (!dataMap.has(month)) {
            dataMap.set(month, 0);
            }

            dataMap.set(month, dataMap.get(month) + count);
        });

        // Prepare dataArray for the chart
        const dataArray = [["Month", "Count"]];

        // Populate dataArray
        dataMap.forEach((count, month) => {
            dataArray.push([month, count]);
        });

        // Load Google Charts and draw the chart
        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback(function () {
            const data = google.visualization.arrayToDataTable(dataArray);

            const options = {
            height: 400,
            curveType: 'function',
            chartArea: {left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%"},
            hAxis: {
                title: "Month",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
            },
            vAxis: {
                title: "Count",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                minValue: 0, // Set the minimum value for the y-axis (count)
            },
            annotations: {
                alwaysOutside: false,
                textStyle: {
                fontSize: 12,
                color: "black",
                bold: true,
                },
            },
            legend: {
                position: "top",
                alignment: "center",
            },
            };
            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);
            const chart = new google.visualization.LineChart(document.getElementById("student_nc_statistics_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                        <div style="width: 15px; height: 15px; background-color: #1450A3; display: inline-block; border-radius: 50%;"></div>
                        <span style="margin-left: 5px;">Non-non_Compliance Count</span>
                    </div>`;
        $("#stu_legend_div").html(html);
        }
        else{
        $("#student_nc_statistics_graph").html("<center>No Data Available</center>");
        }
    }

    function _construct_student_non_compliance_table(input_array) {
        enableTableButtons();
        if(input_array.length > 0){
            // Prepare data structure
            const dataMap = new Map(); // Map to store data for each month
            var html = "";
            input_array.forEach(function (item) {
                const month = getMonthName(item[2]);
                const count = parseInt(item[1]) || 0;

                html += `
                    <tr>
                        <td>${month}</td>
                        <td>${count}</td>
                    </tr>
                `;
            });
            $("#student_nc_statistics_table").html(html);
        }
        else{
            var html = `
                <tr>
                    <td colspan=2>No Data</td>
                </tr>
            `;
            $("#student_nc_statistics_table").html(html);
        }
    }

    /* Student Observation */
    function display_student_observation_widget(school_list, acad_year) {
        disableTableButtons();
        $("#student_observation_statistics_graph").html("<center>Loading Student Observation Statistics...</center>");
        const monthCounts = {};
        var total_count = 0;

        const mapLoop = async () => {
        const promises = school_list.map(async (school) => {
            try {
            const response = await get_student_observation_data(school.school_code, school.school_domain, acad_year);
            if (response) {
                response.forEach((item) => {
                const month = item.month;
                const count = parseInt(item.count);
                total_count += count;

                if (monthCounts[month]) {
                    monthCounts[month] += count;
                } else {
                    monthCounts[month] = count;
                }
                });
            }
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);
        const dataArray = Object.entries(monthCounts).map(([month, count]) => ["", count, month]);
        $("#student_ob").html(total_count);

        _construct_student_observation_view(dataArray);
        _construct_student_observation_table(dataArray);
        };

        google.charts.setOnLoadCallback(mapLoop);
    }

    function get_student_observation_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_student_observation_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_student_observation_view(input_array) {
        if (input_array.length === 0) {
        $("#student_observation_statistics_graph").html('<div class="no-data-display"><center>No Data Available</center></div>');
        } else {
        const dataMap = new Map();
        input_array.forEach(function (item) {
            const month = getMonthName(item[2]);
            const count = parseInt(item[1]) || 0;

            if (!dataMap.has(month)) {
            dataMap.set(month, 0);
            }

            dataMap.set(month, dataMap.get(month) + count);
        });
        const dataArray = [["Month", "Count"]];
        dataMap.forEach((count, month) => {
            dataArray.push([month, count]);
        });
        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback(function () {
            const data = google.visualization.arrayToDataTable(dataArray);

            const options = {
            height: 350,
            curveType: 'function',
            chartArea: { left: "10%", top: "15%", height: "80%", width: "80%", bottom: "15%" },
            hAxis: {
                title: "Month",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
            },
            vAxis: {
                title: "Count",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                minValue: 0,
            },
            annotations: {
                alwaysOutside: false,
                textStyle: {
                fontSize: 12,
                color: "black",
                bold: true,
                },
            },
            legend: {
                position: "top",
                alignment: "center",
            },
            };
            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);
            const chart = new google.visualization.LineChart(document.getElementById("student_observation_statistics_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: #1450A3; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Observation Count</span>
                    </div>`;
        $("#student_observation_legend_div").html(html);
        }
    }

    function _construct_student_observation_table(input_array) {
        enableTableButtons();
        if(input_array.length > 0){
            // Prepare data structure
            const dataMap = new Map(); // Map to store data for each month
            var html = "";
            input_array.forEach(function (item) {
                const month = getMonthName(item[2]);
                const count = parseInt(item[1]) || 0;

                html += `
                    <tr>
                        <td>${month}</td>
                        <td>${count}</td>
                    </tr>
                `;
            });
            $("#student_observation_table").html(html);
        }
    }

    /* Student Counselling */
    function display_student_counselling_widget(school_list, acad_year) {
        disableTableButtons();
        $("#student_counselling_graph").html("<center>Loading Student Counselling Statistics...</center>");
        const monthCounts = {};
        var total_count = 0;

        const mapLoop = async () => {
        const promises = school_list.map(async (school) => {
            try {
            const response = await get_student_counselling_data(school.school_code, school.school_domain, acad_year);
            if (response) {
                response.forEach((item) => {
                const month = item.month;
                const count = parseInt(item.count);
                total_count += count;

                if (monthCounts[month]) {
                    monthCounts[month] += count;
                } else {
                    monthCounts[month] = count;
                }
                });
            }
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);
        const dataArray = Object.entries(monthCounts).map(([month, count]) => ["", count, month]);
        $("#student_co").html(total_count);

        _construct_student_counselling_view(dataArray);
        _construct_student_counselling_table(dataArray);
        };

        google.charts.setOnLoadCallback(mapLoop);
    }

    function get_student_counselling_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_student_counselling_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_student_counselling_view(input_array) {
        if (input_array.length === 0) {
        $("#student_counselling_graph").html('<div class="no-data-display"><center>No Data Available</center></div>');
        } else {
        const dataMap = new Map();
        input_array.forEach(function (item) {
            const month = getMonthName(item[2]);
            const count = parseInt(item[1]) || 0;

            if (!dataMap.has(month)) {
            dataMap.set(month, 0);
            }

            dataMap.set(month, dataMap.get(month) + count);
        });
        const dataArray = [["Month", "Count"]];
        dataMap.forEach((count, month) => {
            dataArray.push([month, count]);
        });
        google.charts.load("current", { packages: ["corechart"] });
        google.charts.setOnLoadCallback(function () {
            const data = google.visualization.arrayToDataTable(dataArray);

            const options = {
            height: 350,
            curveType: 'function',
            chartArea: { left: "10%", top: "15%", height: "80%", width: "80%", bottom: "15%" },
            hAxis: {
                title: "Month",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
            },
            vAxis: {
                title: "Count",
                titleTextStyle: {
                color: "#1450A3",
                fontName: "Arial",
                fontSize: "16",
                bold: true,
                italic: false,
                },
                minValue: 0,
            },
            annotations: {
                alwaysOutside: false,
                textStyle: {
                fontSize: 12,
                color: "black",
                bold: true,
                },
            },
            legend: {
                position: "top",
                alignment: "center",
            },
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([0, 1, { sourceColumn: 1, type: "number", role: "annotation" }]);
            const chart = new google.visualization.LineChart(document.getElementById("student_counselling_graph"));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                        <div style="width: 15px; height: 15px; background-color: #1450A3; display: inline-block; border-radius: 50%;"></div>
                        <span style="margin-left: 5px;">Counselling Count</span>
                    </div>`;
        $("#stu_legend_div").html(html);
        }
    }

    function _construct_student_counselling_table(input_array) {
        enableTableButtons();
        if(input_array.length > 0){
            // Prepare data structure
            const dataMap = new Map(); // Map to store data for each month
            var html = "";
            input_array.forEach(function (item) {
                const month = getMonthName(item[2]);
                const count = parseInt(item[1]) || 0;

                html += `
                    <tr>
                        <td>${month}</td>
                        <td>${count}</td>
                    </tr>
                `;
            });
            $("#student_counselling_table").html(html);
        }
    }

    /* Student Counselling Statuswise */
    function display_schools_dropdown_stu(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        
        const dropdowns = ['dropdownSchool_student'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            // If relevant permission is not there, then the dropdown object is not available. Hence adding a check.
            if (dropdown) {
                dropdown.innerHTML = html;
                // Use an anonymous function to pass acad_year to change_graph_stu
                dropdown.addEventListener('change', (event) => change_graph_stu(event, acad_year));
            }
        });
    }

    function change_graph_stu(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain] = option.value.split('-');
            return { school_code: schoolCode, school_domain: schoolDomain };
        });

        // Pass selectedSchools and acad_year to the display function
        display_student_counselling_statuswise_widget(selectedSchools, acad_year);
    }

    function display_student_counselling_statuswise_widget(school_list, acad_year){
        $("#student_counselling_statuswise_statistics_graph").html("<center>Loading Student Counselling Statuswise Statistics...</center>");
        var dataArray;
        const maploop = async () => {
        const promises = [school_list[0]].map(async school => {
            try {
            const data = await get_student_counselling_statuswise_data(school.school_code, school.school_domain, acad_year);
            // console.log(data);
            const result = data.map(item => [item.name, parseInt(item.count), item.color]);
            dataArray = [["Name", "Count", "Color"]];
            result.forEach(item => dataArray.push(item));
            } catch (err) {
            console.error(err);
            }
        });

        await Promise.all(promises);
        _construct_student_counselling_statuswise_view(dataArray);
        };
        maploop();
    }
    
    function get_student_counselling_statuswise_data(school_code, school_domain, acad_year){
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year:  acad_year,
            api: "get_student_counselling_statuswise_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_student_counselling_statuswise_view(input_array){
        // console.log(input_array);
        const array1 = input_array.map(item => [item[0], item[1]]);
        const array2 = input_array.map(item => [item[0], item[2]]);
        google.charts.load("current", { packages: ["corechart"] });
        const colors = [];
        // Loop through array2 starting from index 1
        for (let i = 1; i < array2.length; i++) {
            // Push color to colors array
            colors.push(array2[i][1]);
        }

        // Initialize slices object
        const slices = {};

        // Construct slices object based on colors array
        colors.forEach((color, index) => {
            slices[index] = { 'color': color };
        });
        google.charts.setOnLoadCallback(function () {
        const data = google.visualization.arrayToDataTable(array1);

        var options = {
            annotations: {
                style: "point", // or 'point' for point annotations
                pieSliceText: "value",
            },
            height: 400,
            chartArea: { left: "10%", top: "15%", width: "80%", height: "80%", bottom: "15%" },
            pieHole: 0.4,
            slices: slices,
            legend: {position: 'top', alignment: "center"}
            };
            // Create the chart, passing in the data and options.
            var chart = new google.visualization.PieChart(document.getElementById("student_counselling_statuswise_statistics_graph"));
            chart.draw(data, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">`;
        for (let i = 1; i < array2.length; i++) {
        html += `<div style="margin-left: 10px;width: 15px; height: 15px; background-color: ${array2[i][1]}; display: inline-block; border-radius: 50%;"></div>
                <span style="margin-left: 5px;">${array2[i][0]}</span>
                `;
        }
        // console.log(html);
        html += `</div>`;
        $("#student_counselling_statuswise_statistics_legend_div").html(html);
    }

    /* Student Nationality Wise Data */
    // function display_schools_dropdown_student(school_list, acad_year) {
    //     let html = '';
    //     school_list.forEach((school) => {
    //         html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
    //     });

    //     const dropdowns = ['dropdownSchool_student_nationality'];
    //     dropdowns.forEach((dropdownId) => {
    //         const dropdown = document.getElementById(dropdownId);
    //         // If relevant permission is not there, then the dropdown object is not available. Hence adding a check.
    //         if (dropdown) {
    //             dropdown.innerHTML = html;
    //             // Use an anonymous function to pass acad_year to change_graph_student_nationalitywise
    //             dropdown.addEventListener('change', (event) => change_graph_student_nationalitywise(event, acad_year));
    //         }
    //     });
    // }

    // function change_graph_student_nationalitywise(event, acad_year) {
    //     const dropdown = event.target;
    //     const selectedOptions = Array.from(dropdown.selectedOptions);
    //     const selectedSchools = selectedOptions.map(option => {
    //         const [schoolCode, schoolDomain] = option.value.split('-');
    //         return { school_code: schoolCode, school_domain: schoolDomain };
    //     });

    //     // Pass selectedSchools and acad_year to the display function
    //     display_student_nationalitywise_widget(selectedSchools, acad_year);
    // }

    function display_student_nationalitywise_widget(school_list, acad_year) {
        $("#student_nationalitywise_table").html("<center>Loading student Nationalitywise Statistics...</center>");
        
        var nationality_data = {};

        const mapLoop = async () => {
            const promises = school_list.map(async (school) => {
                try {
                    const response = await get_student_nationalitywise_data(school.school_code, school.school_domain, acad_year);
                    if (response) {
                        response.forEach((obj) => {
                            const nationality = (obj.nationality === '0') ? '-' : obj.nationality;
                            const studentCount = parseInt(obj.student_count);

                            // Accumulate the count for each nationality
                            // if (nationality_data[nationality]) {
                            //     nationality_data[nationality] += studentCount;
                            // } else {
                            //     nationality_data[nationality] = studentCount;
                            // }

                            if (!nationality_data[nationality]) {
                                nationality_data[nationality] = {};
                            }
                            nationality_data[nationality][school.school_name] = studentCount;
                        });
                    }
                } catch (err) {
                    console.error(err);
                }
            });

            // Wait for all promises to resolve
            await Promise.all(promises);

            // Convert the nationality_data object into an array for the table
            // var final_array = Object.entries(nationality_data).map(([nationality, student_count]) => {
            //     return [student_count, nationality];
            // });

            // Call the function to display the table with final combined data
            // _construct_student_nationalitywise_table(final_array);
            _construct_student_nationalitywise_table_new(nationality_data, school_list);
        };
        
        mapLoop();
    }

    function get_student_nationalitywise_data(school_code, school_domain, acad_year) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'acad_year': acad_year,
                    'api': 'get_student_nationalitywise_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_student_nationalitywise_overall_view(input_array, no_of_schools) {
        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(input_array);
            var options = {
                height: 330,
                curveType: 'function',
                // width: 1400,
                chartArea: {left: '15%', top: '15%', width: '75%', height: '80%', bottom: '10%'},
                hAxis: {
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                    bar: { groupWidth: "90%" },
                    axisTitlesPosition: 'out',
                    curveType: 'function',
                    legend: {
                    position: 'labeled'
                    }
                },
                vAxis: {
                    title: '# TICKETS',
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                },
                legend: {
                    position: 'top', alignment: 'center'
                }
            };

            var view = new google.visualization.DataView(data);
            var columns = [0, 1];
            for (var i = 0; i < no_of_schools; i++) {
                columns.push({
                    sourceColumn: i + 1,
                    type: "number",
                    role: "annotation",
                    color: "#000000"
                });
                if (i < (no_of_schools-1)) {
                    columns.push(i + 2);
                }
            }
            view.setColumns(columns);
            var chart = new google.visualization.LineChart(document.getElementById('pt_column_chart'));
            chart.draw(view, options);
        });
    }

    function _construct_student_nationalitywise_table(input_array) {
        var html = `<table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">`;
        html += `<thead style="color: #0F256E">`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">#</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Nationality</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Student Count</th>`;
        html += `</thead>`;
        html += `<tbody>`;
        
        let total_student = 0;
        input_array.forEach((obj, i) => {
            // console.log(obj);
            html += `<tr>`;
            html += `<td>${i + 1}</td>`;
            html += `<td>${obj[1]}</td>`;
            html += `<td>${obj[0]}</td>`;
            total_student += obj[0];
        });
        html += `</tr>`;
        html += `<tr>`;
        html += `<td><strong>${input_array.length + 1}</strong></td>`;
        html += `<td><strong>Total</strong></td>`;
        html += `<td><strong>${total_student}</strong></td>`;
        html += `</tr>`;
        html += `</tbody>`;
        html += `</table>`;

        $('#student_nationalitywise_table').html(html);
    }

    function _construct_student_nationalitywise_table_new(nationality_data, school_list) {
        var html = `<table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">`;
        html += `<thead style="color: #0F256E">`;
        html += `<tr>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">#</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Nationality</th>`;

        school_list.forEach((school) => {
            html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">${school.school_code.toUpperCase()}</th>`;
        });

        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Total</th>`;
        html += `</tr>`;
        html += `</thead>`;

        let schoolTotals = Array(school_list.length).fill(0);
        let grandTotal = 0;

        html += `<tbody>`;
        
        let rowIndex = 0;
        for (const [nationality, countsBySchool] of Object.entries(nationality_data)) {
            html += `<tr>`;
            html += `<td>${++rowIndex}</td>`;
            html += `<td>${nationality == '-' ? 'Unassigned' : nationality }</td>`;

            let rowTotal = 0;

            school_list.forEach((school, schoolIndex) => {
                const studentCount = countsBySchool[school.school_name] || 0;
                html += `<td>${studentCount}</td>`;
                rowTotal += studentCount;
                schoolTotals[schoolIndex] += studentCount;
            });

            html += `<td><strong>${rowTotal}</strong></td>`;
            html += `</tr>`;
            grandTotal += rowTotal;
        }

        html += `<tr>`;
        html += `<td><strong></strong></td>`;
        html += `<td colspan="1" style="text-align: right;"><strong>Total</strong></td>`;

        let overallTotal = 0;
        schoolTotals.forEach((schoolTotal) => {
            html += `<td><strong>${schoolTotal}</strong></td>`;
            overallTotal += schoolTotal;
        });

        html += `<td><strong>${grandTotal}</strong></td>`;
        html += `</tr>`;
        html += `</tbody>`;
        html += `</table>`;

        $('#student_nationalitywise_table').html(html);
    }

    /*Student Birthday*/
    function display_schools_dropdown_stu_birthday(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
            html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        
        const dropdowns = ['dropdownSchool_student_birthday'];
        dropdowns.forEach((dropdownId) => {
            const dropdown = document.getElementById(dropdownId);
            // If relevant permission is not there, then the dropdown object is not available. Hence adding a check.
            if (dropdown) {
                dropdown.innerHTML = html;
                // Use an anonymous function to pass acad_year to change_graph_stu
                dropdown.addEventListener('change', (event) => change_graph_stu_birthday(event, acad_year));
            }
        });
    }

    function change_graph_stu_birthday(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
            const [schoolCode, schoolDomain] = option.value.split('-');
            return { school_code: schoolCode, school_domain: schoolDomain };
        });

        // Pass selectedSchools and acad_year to the display function
        display_student_birthday_widget(selectedSchools, acad_year);
    }

    function display_student_birthday_widget(school_list, acad_year) {
        $("#student_birthday_pie_card").html("<center>Loading student Birthday Statistics...</center>");
        var birthday_data = [];

        // Function to process each school's data
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const response = await get_student_birthday_list(school.school_code, school.school_domain, acad_year);
                    if (response) {
                        // console.log(response);
                        birthday_data.push({
                            school_code: school.school_code,
                            student_just_celebrated: response.student_just_celebrated,
                            student_today_celebrated: response.student_today_celebrated,
                            student_tomorrow_celebrating: response.student_tomorrow_celebrating,
                            student_upcoming_celebrating: response.student_upcoming_celebrating
                        });
                    }
                } catch (err) {
                    console.error(err);
                }
            });

            await Promise.all(promises);
            var reordered_birthday_data = [];
            school_list.forEach(sl => {
                birthday_data.forEach(bd => {                    
                    if (sl.school_code.toUpperCase() === bd.school_code.toUpperCase()) {
                        reordered_birthday_data.push(bd);
                    }
                });
            });
            displaystudentBirthdayList(reordered_birthday_data);
        };
        
        mapLoop();
    }

    function get_student_birthday_list(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "student_birthday_list",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function displaystudentBirthdayList(birthdayData) {
        const container = document.getElementById("student_birthday_pie_card");
        container.innerHTML = "";

        function createStudentTableRows(studentList, label, bgColor) {
            if (!studentList.length) {
                return `
                    <tr>
                        <td><b>${label}</b></td>
                        <td colspan="3" style="background: lightgrey; text-align: center;">No Data</td>
                    </tr>
                `;
            }

            let rows = '';
            studentList.forEach((student, index) => {
                rows += `
                    <tr>
                        ${index === 0 ? `<td rowspan="${studentList.length}" style="vertical-align: middle;"><b>${label}</b></td>` : ''}
                        <td>${student.std_name}</td>
                        <td>${student.class_name} ${student.section_name}</td>
                        <td>${student.dobDisplay}</td>
                    </tr>
                `;
            });
            return rows;
        }

        birthdayData.forEach(school => {
            let tableHTML = `
                <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                    <thead style="color: #0F256E">
                        <tr>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Category</th>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Student Name</th>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Class & Section</th>
                            <th class="text-left font-weight-bolder text-m text-uppercase opacity-7">DOB</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${createStudentTableRows(school.student_just_celebrated, "Just Celebrated", pie_color1)}
                        ${createStudentTableRows(school.student_today_celebrated, "Today", pie_color2)}
                        ${createStudentTableRows(school.student_tomorrow_celebrating, "Tomorrow", pie_color3)}
                        ${createStudentTableRows(school.student_upcoming_celebrating, "Upcoming", pie_color4)}
                    </tbody>
                </table>
            `;
            container.innerHTML += tableHTML;
        });
    }

    /* Utility */
    function getMonthName(monthNumber) {
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        return months[monthNumber - 1] || "";
    }

    function flip_student_view(type, id, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');
        switch(id){
            case 'student_management_card':
                switch (type) {
                    case 'column_chart':
                        $('#student_management_pie_card').css('display', 'block');
                        $('#student_management_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#student_management_table_card').css('display', 'block');
                        $('#student_management_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'student_nc_statistics_card':
                switch (type) {
                    case 'column_chart':
                        $('#student_nc_statistics_pie_card').css('display', 'block');
                        $('#student_nc_statistics_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#student_nc_statistics_table_card').css('display', 'block');
                        $('#student_nc_statistics_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'student_observation_card':
                switch (type) {
                    case 'column_chart':
                        $('#student_observation_pie_card').css('display', 'block');
                        $('#student_observation_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#student_observation_table_card').css('display', 'block');
                        $('#student_observation_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'student_counselling_card':
                switch (type) {
                    case 'column_chart':
                        $('#student_counselling_pie_card').css('display', 'block');
                        $('#student_counselling_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#student_counselling_table_card').css('display', 'block');
                        $('#student_counselling_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    function disableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.setAttribute('disabled', true);
        }
    }

    // Function to enable table buttons
    function enableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.removeAttribute('disabled');
        }
    }
</script>
