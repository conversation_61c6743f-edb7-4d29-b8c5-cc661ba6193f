<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li>Student Promotion</li>
</ul>

<hr>
<div class="col-md-12">
	<div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-10 d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/student_menu'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        Student Promotion
                    </h3>   
                </div>
				<?php if($this->authorization->isSuperAdmin()){ ?>
				<div class="col-md-2">
					<button class="btn-danger pull-right" onclick="move_temp_to_studying()" >Temp Added to Studying</button>        
				</div>
				<?php } ?>
            </div>    
        </div> 
		<div class="col-md-12">
			<div class="card-header panel_heading_new_style_padding">
				<h3 class="card-title panel_title_new_style"><strong>Select Class</strong></h3>
			</div>
			<div class="card-body p-0">
				<form id="classForm" method="post" action="<?php echo site_url('student/student_promotion');?>">
					<input type="hidden" name="class_id" id="class_id">
				</form>
				<div class="form-group">
					<div class="col-md-12">
							<?php 
								$array = array();
								$array[0] = 'Select Class';
								foreach ($classes as $key => $cl) {
									$array[$cl->classId] = $cl->className;
								}
								echo form_dropdown("classId", $array, set_value("classId",$classSelected), "id='classId'  class='form-control'");
							?>
					</div>
				</div>
			</div>
			<br>
			<br>
			<form id="clsForm" method="post" action="">
				<div class="card cd_border mt-4">
					<div class="card-header panel_heading_new_style_padding">
						<h3 class="card-title panel_title_new_style"><strong>Promote Students</strong></h3>
					</div>
					<div class="card-body" id="promote-students" style="max-height: 700px;overflow-y: scroll;">
						<?php if(empty($students)) { 
								echo '<h4>No data</h4>';
							} else { 
								if($promotionClass) { ?>
									<!-- <button type="button" class="btn btn-primary" id="promoteBtn">Promote</button> -->
									<button type="button" class="btn btn-primary" id="tempPromoteBtn">Temp Promote</button>
								<?php } ?>
								<input type="hidden" name="currentClass" value="<?php echo $classSelected; ?>">
								<div class="row">
									<label class="form-control-label col-md-2">Current Class</label>
									<div class="col-md-6 text-left">
										<?php echo '<p>'.$className.'</p>'; ?>
									</div>
								</div>
								<div class="row">
									<label class="form-control-label col-md-2">Current Academic Year</label>
									<div class="col-md-10 text-left">
										<?php echo '<p>'.$current_academic_year.'</p>'; ?>
									</div>
								</div>
								<div class="row">
									<label class="form-control-label col-md-2">Promotion Academic Year</label>
									<div class="col-md-10 text-left">
										<?php echo '<p>'.$promotion_academic_year.'</p>'; ?>
									</div>
								</div>
								<div class="row">
									<label class="form-control-label col-md-2">Promote to</label>
									<div class="col-md-8 text-left">
										<?php if(empty($promotionClass)) {
											echo 'Promotion class is not added, please contact school admin.';
										} else {
											echo $promotionClass;
											echo '<input type="hidden" name="promotion_class_id" value="'.$promotionClassId.'">';
										} ?>
									</div>
									<div class="col-md-8"></div>
								</div><br>
								<div class="row">
									<p class="form-control-label col-md-12"><b>Note : </b>Student Status indicated in red are fees pending students.
									</p>
								</div>
								<br>
								<div class="col-md-12" style="padding: 0px;max-height: 400px; overflow-y: scroll;">
									<table class="table table-striped table-bordered">
										<thead>
											<tr>
												<th>#</th>
												<th>Adm. No</th>
												<th>Name</th>
												<th>Status (<?php echo $current_academic_year ?>)</th>
												<th>Status (<?php echo $promotion_academic_year ?>)</th>
												<th style="vertical-align: middle;">Action <input style="float:right;" type="checkbox" id="selectAll"></th>
											</tr>
										</thead>
										<tbody>
											<?php $i=1; foreach ($students as $std) {
											 	$disabled = ''; 
											 	$bgColor = '';
												if ($std->promotion_pStatus == 4 || $std->promotion_pStatus == 5) {
													$disabled = 'disabled';
													$bgColor = 'red';
												}
												$background_color = ($std->fee_pending == 1) ? 'red' : '';
												;
											 ?>
												<tr style="color: <?php echo $bgColor ?>;" >
													<td><?php echo $i++ ?></td>
													<td><?php echo $std->admission_no ?></td>
													<td><?php echo ($name_to_caps?strtoupper($std->stdName):($std->stdName)) .' ('. $std->section_name .')' ?></td>
													<td style="color: <?= $background_color ?>;"><?php echo strtoupper($std->current_pStatus) ?></td>
													<td><?php echo strtoupper($std->promotion_pStatus) ?></td>
													<td>
														<?php if(strtoupper($std->current_pStatus) == 'STUDYING' || strtoupper($std->promotion_pStatus) == 'TEMP_ADDED') { ?>
															<input class="stdCheck" type="checkbox"  name="students[]" <?php echo $disabled ?> value="<?php echo $std->currentStdYearId.'_'.$std->promotionStdYearId ?>">
														<?php } else {
															echo 'DONE';
														} ?>
													</td>
												</tr>
											<?php  } ?>
										</tbody>
									</table>
								</div>
						<?php } ?>
					</div>
				</div>
			</form>		
		</div>	

	</div>
</div>
<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>


<script type="text/javascript">
	$("#classId").change(function(){
		var classId = $(this).val();
		if(classId == 0) {
			$("#promote-students").html('');
		} else {
			$("#class_id").val(classId);
			$("#classForm").submit();
		}
	});

	$("#selectAll").click(function(){
		if($(this).is(':checked')) {
			$(".stdCheck").not(':disabled').prop('checked', true);
		} else {
			$(".stdCheck").prop('checked', false);
		}
	});

	function checkValidPromotions(type) {
		var current = [];
		var promoted = [];
        $('.stdCheck:checked').each(function(i){
        	var vals = $(this).val().split("_");
        	current.push(vals[0]);
        	if(vals[1]!='')
        		promoted.push(vals[1]);
        });
        if(current.length == 0) {
        	return 0;
        }
        if(type == 'temp' && promoted.length > 0) {
        	return 0;
        } else if(type == 'promote' && promoted.length != current.length) {
        	return 0;
        }
        return 1;
	}

	$("#tempPromoteBtn").click(function(){
		$('#tempPromoteBtn').prop('disabled', true);
		$('#tempPromoteBtn').html('Please wait');
		var valid = checkValidPromotions('temp');
		if(!valid) {
			$(function(){
		        new PNotify({
		            title: 'Warning',
		            text: 'Nothing selected OR Selected students who are temporarily promoted already.',
		            type: 'warning',
		        });
		    });
			$('#tempPromoteBtn').removeAttr("disabled");
			$('#tempPromoteBtn').html('Temp promote');
			return false;
		}
		$("#clsForm").attr('action', "<?php echo site_url('student/student_promotion/promoteTemporarily');?>");
		$("#clsForm").submit();
		$('#tempPromoteBtn').removeAttr("disabled");
		$('#tempPromoteBtn').html('Temp promote');
	});

	$("#promoteBtn").click(function(){
		$('#promoteBtn').prop('disabled', true);
		$('#promoteBtn').html('Please wait');
		var valid = checkValidPromotions('promote');
		if(!valid) {
			$(function(){
		        new PNotify({
		            title: 'Warning',
		            text: 'Nothing selected OR Selected students who are not temporarily promoted.',
		            type: 'warning',
		        });
		    });
			$('#promoteBtn').removeAttr("disabled");
			$('#promoteBtn').html('Promote');
			return false;
		}
		$("#clsForm").attr('action', "<?php echo site_url('student/student_promotion/promoteCompletely');?>");
		$("#clsForm").submit();
		$('#promoteBtn').removeAttr("disabled");
		$('#promoteBtn').html('Promote');
	});

	function move_temp_to_studying(){
		var acad_year ='<?php echo $this->acad_year->getAcadYearID() ?>';
		bootbox.confirm({
            title: "Change Promotion Status",
            message: "Do you want to change all students Promotion status TEMP_ADDED to STUDYING in Current Academic Year...?",
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
             },
            callback: function (result) {
                if(result) {
                    $.ajax({
                        url: '<?php echo site_url('student/student_promotion/change_promotion_status'); ?>',
                        type: 'post',
                        data: {'acad_year':acad_year},
                        success: function(data) {
                            if(data){
                                $(function(){
                                    new PNotify({
                                        title: 'Success',
                                        text:  'Successfully changed the status',
                                        type: 'success',
                                    });
                                });
                            }else{
                                $(function(){
                                    new PNotify({
                                        title: 'Error',
                                        text:  'Something went wrong',
                                        type: 'error',
                                    });
                                });
                            }
                            location.reload();
                        }
                     
                    });
                }
            }
        });
	}
</script>

<style type="text/css">
	input[type="checkbox"]{
	  width: 1.2em; /*Desired width*/
	  height: 1.2em; /*Desired height*/
	  cursor: pointer;
	}
	#promoteBtn {
		float: right;
		margin-left: 1%;
	}
	#tempPromoteBtn {
		float: right;
	}
	@media only screen and (max-width: 768px){
input[type=radio], input[type=checkbox] {
    margin: 0px 11px 1px;
}}

.medium{
	width:50%;
	margin: auto;
}
</style>
