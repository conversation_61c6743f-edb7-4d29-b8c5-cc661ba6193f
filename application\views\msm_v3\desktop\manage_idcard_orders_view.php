<?php $this->load->view('msm_v3/styles/_staff_leave_approve_css'); ?>
<?php $this->load->view('msm_v3/styles/idcard_dashboard_css'); ?>
<?php $this->load->view('msm_v3/scripts/idcard_helper_functions'); ?>

<div class="card landing_card">
    <div class="container-fluid py-2 pt-3">
        <div class="row mx-0">
            <?php $this->load->view('msm_v3/desktop/_top_bar'); ?>
        </div>
    </div>

    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12 d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0" style="color: #0F256E; font-weight: 600;">ID Card Order Management</h4>
                <button id="refreshBtn" class="btn btn-outline-dark" onclick="refresh_idcard_orders()">Refresh</button>
            </div>
        </div>


        <!-- Summary Cards Section -->
        <div class="row overview_cards_wrapper">
            <div class="col-md-3 mb-3">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1" style="font-size: 14px;">Total ID Cards</h6>
                                <h3 class="mb-0" style="font-weight: 600; color: #0F256E;" id="totalCardsCount"><?php echo isset($summary->total_cards) ? $summary->total_cards : 0; ?></h3>
                            </div>
                            <div class="rounded-circle p-2" style="background-color: rgba(0, 204, 131, 0.1);">
                                <i class="fa fa-id-card" style="color: #00CC83; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1" style="font-size: 14px;">In Review</h6>
                                <h3 class="mb-0" style="font-weight: 600; color: #0F256E;" id="inReviewCount"><?php echo isset($summary->in_review) ? $summary->in_review : 0; ?></h3>
                            </div>
                            <div class="rounded-circle p-2" style="background-color: rgba(15, 37, 110, 0.1);">
                                <i class="fa fa-search" style="color: #0F256E; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1" style="font-size: 14px;">In Process</h6>
                                <h3 class="mb-0" style="font-weight: 600; color: #0F256E;" id="inPrintingCount"><?php echo isset($summary->in_printing) ? $summary->in_printing : 0; ?></h3>
                            </div>
                            <div class="rounded-circle p-2" style="background-color: rgba(255, 152, 0, 0.1);">
                                <i class="fa fa-print" style="color: #FF9800; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1" style="font-size: 14px;">Delivered</h6>
                                <h3 class="mb-0" style="font-weight: 600; color: #0F256E;" id="deliveredCount"><?php echo isset($summary->delivered) ? $summary->delivered : 0; ?></h3>
                            </div>
                            <div class="rounded-circle p-2" style="background-color: rgba(0, 204, 131, 0.1);">
                                <i class="fa fa-check-circle" style="color: #00CC83; font-size: 20px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search Section -->
        <div class="row mb-4 filter-section">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-white border-right-0" style="height: 40px;">
                            <i class="fa fa-search text-muted"></i>
                        </span>
                    </div>
                    <input type="text" class="form-control border-left-0" id="searchInput" placeholder="Search order" value="<?php echo isset($filters['search']) ? $filters['search'] : ''; ?>" style="height: 40px;">
                </div>
            </div>
            <div class="col-lg-8 col-md-6">
                <div class="filter-wrapper">
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="orderStatusButton" data-bs-toggle="dropdown" aria-expanded="false" style="height: 40px;">
                            <?php echo isset($filters['order_status']) && $filters['order_status'] !== 'all' ? $filters['order_status'] : 'Order Status'; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="orderStatusButton">
                            <li><a class="dropdown-item" href="#">All Order Status</a></li>
                            <li><a class="dropdown-item" href="#">In Review</a></li>
                            <li><a class="dropdown-item" href="#">Approved for Printing</a></li>
                            <li><a class="dropdown-item" href="#">In-Printing</a></li>
                            <li><a class="dropdown-item" href="#">In-Delivery</a></li>
                            <li><a class="dropdown-item" href="#">Delivered</a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="paymentStatusButton" data-bs-toggle="dropdown" aria-expanded="false" style="height: 40px;">
                            <?php echo isset($filters['payment_status']) && $filters['payment_status'] !== 'all' ? $filters['payment_status'] : 'Payment Status'; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="paymentStatusButton">
                            <li><a class="dropdown-item" href="#">All Payment Status</a></li>
                            <li><a class="dropdown-item" href="#">not_started</a></li>
                            <li><a class="dropdown-item" href="#">invoice_sent</a></li>
                            <li><a class="dropdown-item" href="#">payment_complete</a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="idCardTypeButton" data-bs-toggle="dropdown" aria-expanded="false" style="height: 40px;">
                            <?php echo isset($filters['id_card_type']) && $filters['id_card_type'] !== 'all' ? $filters['id_card_type'] : 'ID Card Type'; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="idCardTypeButton">
                            <li><a class="dropdown-item" href="#">All ID Card Types</a></li>
                            <li><a class="dropdown-item" href="#">Staff</a></li>
                            <li><a class="dropdown-item" href="#">Student</a></li>
                            <li><a class="dropdown-item" href="#">Parent</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-primary" type="button" id="searchButton" style="height: 40px;">
                        <i class="fa fa-search"></i> Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Table Section -->
        <div class="row">
            <div class="col-12">
                <div class="table-container">
                    <div class="table-responsive custom-scrollbar">
                        <table id="ordersTable" class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th> <!-- Changed from Order ID to Serial Number -->
                                    <th>Order Name</th>
                                    <th>School</th>
                                    <th>Type</th>
                                    <th>Cards</th>
                                    <th>Order Status</th>
                                    <th>Payment Status</th>
                                    <th>Created Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <!-- Table rows will be populated by JavaScript -->
                                <tr>
                                    <td colspan="9" class="text-center">Loading...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="row mt-3">
            <div class="col-md-6 mb-3">
                <div class="showing-entries">
                    Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="totalOrders">0</span> orders
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <nav aria-label="Page navigation" class="float-md-end">
                    <ul class="pagination mb-0" id="paginationContainer">
                        <!-- Pagination removed -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
<script>
    var json_school_list = '<?php echo $json_school_list ?>';
    var school_list = JSON.parse(json_school_list);
    var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';
    $(document).ready(function() {
        // Fetch orders on page load
        fetchOrders();

        // Search button click
        $('#searchButton').on('click', function() {
            fetchOrders();
        });

        // Search on Enter key
        $('#searchInput').on('keypress', function(e) {
            if (e.which === 13) {
                fetchOrders();
            }
        });

        // Filter dropdown items click
        $('.dropdown-item').on('click', function(e) {
            e.preventDefault();
            $(this).closest('.dropdown').find('.dropdown-toggle').text($(this).text());
            fetchOrders();
        });

        // Function to fetch orders
        function fetchOrders() {
            // Get filter values
            const orderStatus = $('#orderStatusButton').text().trim() === 'Order Status' || $('#orderStatusButton').text().trim() === 'All Order Status' ? 'all' : $('#orderStatusButton').text().trim();
            const paymentStatus = $('#paymentStatusButton').text().trim() === 'Payment Status' || $('#paymentStatusButton').text().trim() === 'All Payment Status' ? 'all' : $('#paymentStatusButton').text().trim();
            const idCardType = $('#idCardTypeButton').text().trim() === 'ID Card Type' || $('#idCardTypeButton').text().trim() === 'All ID Card Types' ? 'all' : $('#idCardTypeButton').text().trim();
            const search = $('#searchInput').val().trim();

            // Show loading indicator
            $('#ordersTableBody').html('<tr><td colspan="9" class="text-center">Loading...</td></tr>');

            // Fetch orders via AJAX
            $.ajax({
                url: '<?php echo site_url("msm_v3/Dashboard/get_idcard_orders"); ?>',
                type: 'GET',
                data: {
                    order_status: orderStatus,
                    payment_status: paymentStatus,
                    id_card_type: idCardType,
                    search: search
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Update summary cards
                        updateSummaryCards(response.summary);

                        // Render orders table
                        renderOrdersTable(response.orders);

                        // Update total orders count
                        $('#totalOrders').text(response.orders.length);
                    } else {
                        $('#ordersTableBody').html('<tr><td colspan="9" class="text-center text-danger">Error loading orders</td></tr>');
                    }
                },
                error: function() {
                    $('#ordersTableBody').html('<tr><td colspan="9" class="text-center text-danger">Error loading orders</td></tr>');
                }
            });
        }

        // Function to update summary cards
        function updateSummaryCards(summary) {
            $('#totalCardsCount').text(summary.total_cards);
            $('#inReviewCount').text(summary.in_review);
            $('#inPrintingCount').text(summary.in_printing);
            $('#deliveredCount').text(summary.delivered);
        }

        // Function to render orders table
        function renderOrdersTable(orders) {
            const tableBody = $('#ordersTableBody');
            tableBody.empty();

            if (orders.length === 0) {
                tableBody.html('<tr><td colspan="9" class="text-center">No orders found</td></tr>');
                return;
            }

            orders.forEach(order => {
                // Format the date
                const createdDate = new Date(order.created_date);
                const formattedDate = createdDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                // Create status badge
                const orderStatusBadge = getStatusBadge(order.order_status);
                const paymentStatusBadge = getPaymentStatusBadge(order.payment_status);

                // Create action buttons
                const actionButtons = createActionButtons(order);

                // Create table row
                const row = `
                    <tr>
                        <td>${order.order_id}</td>
                        <td>${order.order_name}</td>
                        <td>${order.school_name}</td>
                        <td>${order.id_card_type}</td>
                        <td>${order.quantity}</td>
                        <td>${orderStatusBadge}</td>
                        <td>${paymentStatusBadge}</td>
                        <td>${formattedDate}</td>
                        <td>${actionButtons}</td>
                    </tr>
                `;

                tableBody.append(row);
            });

            // Initialize dropdown menus for action buttons
            initializeDropdowns();
        }

        // Function to create status badge
        function getStatusBadge(status) {
            let badgeClass = '';
            let badgeText = status;

            switch(status.toLowerCase()) {
                case 'in review':
                    badgeClass = 'badge-info';
                    break;
                case 'approved for printing':
                    badgeClass = 'badge-primary';
                    break;
                case 'in-printing':
                    badgeClass = 'badge-warning';
                    break;
                case 'in-delivery':
                    badgeClass = 'badge-secondary';
                    break;
                case 'delivered':
                    badgeClass = 'badge-success';
                    break;
                default:
                    badgeClass = 'badge-light';
            }

            return `<span class="badge ${badgeClass}">${badgeText}</span>`;
        }

        // Function to create payment status badge
        function getPaymentStatusBadge(status) {
            let badgeClass = '';
            let badgeText = status;

            switch(status.toLowerCase()) {
                case 'not_started':
                    badgeClass = 'badge-light';
                    badgeText = 'Not Started';
                    break;
                case 'invoice_sent':
                    badgeClass = 'badge-warning';
                    badgeText = 'Invoice Sent';
                    break;
                case 'payment_complete':
                    badgeClass = 'badge-success';
                    badgeText = 'Payment Complete';
                    break;
                default:
                    badgeClass = 'badge-light';
            }

            return `<span class="badge ${badgeClass}">${badgeText}</span>`;
        }

        // Function to create action buttons
        function createActionButtons(order) {
            // Create dropdown menu with actions based on order status
            let actions = `
                <div class="dropdown action-dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionDropdown-${order.id}" data-bs-toggle="dropdown" aria-expanded="false">
                        Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown-${order.id}">
                        <li><a class="dropdown-item view-order" href="#" data-order-id="${order.id}"><i class="fa fa-eye"></i> View Details</a></li>
            `;

            // Add status-specific actions
            switch(order.order_status.toLowerCase()) {
                case 'in review':
                    actions += `
                        <li><a class="dropdown-item approve-order" href="#" data-order-id="${order.id}"><i class="fa fa-check"></i> Approve for Printing</a></li>
                    `;
                    break;
                case 'approved for printing':
                    if (order.payment_status.toLowerCase() === 'payment_complete') {
                        actions += `
                            <li><a class="dropdown-item send-to-printing" href="#" data-order-id="${order.id}"><i class="fa fa-print"></i> Send to Printing</a></li>
                        `;
                    } else {
                        actions += `
                            <li><a class="dropdown-item send-invoice" href="#" data-order-id="${order.id}"><i class="fa fa-file-invoice"></i> Send Invoice</a></li>
                            <li><a class="dropdown-item mark-payment" href="#" data-order-id="${order.id}"><i class="fa fa-money-bill"></i> Mark as Paid</a></li>
                        `;
                    }
                    break;
                case 'in-printing':
                    actions += `
                        <li><a class="dropdown-item mark-delivery" href="#" data-order-id="${order.id}"><i class="fa fa-truck"></i> Mark as In-Delivery</a></li>
                    `;
                    break;
                case 'in-delivery':
                    actions += `
                        <li><a class="dropdown-item mark-delivered" href="#" data-order-id="${order.id}"><i class="fa fa-check-circle"></i> Mark as Delivered</a></li>
                    `;
                    break;
            }

            // Close dropdown
            actions += `
                    </ul>
                </div>
            `;

            return actions;
        }

        // Function to initialize dropdowns
        function initializeDropdowns() {
            // Initialize Bootstrap dropdowns if needed
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                dropdownElementList.map(function (dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
            }

            // Add event listeners for action buttons
            $('.view-order').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order-id');
                window.location.href = '<?php echo site_url("msm_v3/Dashboard/view_idcard_order/"); ?>' + orderId;
            });

            $('.approve-order, .send-to-printing, .send-invoice, .mark-payment, .mark-delivery, .mark-delivered').on('click', function(e) {
                e.preventDefault();
                const orderId = $(this).data('order-id');
                const action = $(this).attr('class').split(' ')[1];

                // Handle different actions
                handleOrderAction(orderId, action);
            });
        }

        // Function to handle order actions
        function handleOrderAction(orderId, action) {
            let url = '';
            let actionText = '';

            switch(action) {
                case 'approve-order':
                    url = '<?php echo site_url("msm_v3/Dashboard/approve_idcard_order"); ?>';
                    actionText = 'approve this order for printing';
                    break;
                case 'send-to-printing':
                    url = '<?php echo site_url("msm_v3/Dashboard/send_to_printing"); ?>';
                    actionText = 'send this order to printing';
                    break;
                case 'send-invoice':
                    url = '<?php echo site_url("msm_v3/Dashboard/send_invoice"); ?>';
                    actionText = 'send an invoice for this order';
                    break;
                case 'mark-payment':
                    url = '<?php echo site_url("msm_v3/Dashboard/mark_payment_complete"); ?>';
                    actionText = 'mark payment as complete for this order';
                    break;
                case 'mark-delivery':
                    url = '<?php echo site_url("msm_v3/Dashboard/mark_in_delivery"); ?>';
                    actionText = 'mark this order as in delivery';
                    break;
                case 'mark-delivered':
                    url = '<?php echo site_url("msm_v3/Dashboard/mark_delivered"); ?>';
                    actionText = 'mark this order as delivered';
                    break;
                default:
                    return;
            }

            // Confirm action
            if (confirm(`Are you sure you want to ${actionText}?`)) {
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: { order_id: orderId },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert(response.message || 'Action completed successfully');
                            // Refresh orders
                            fetchOrders();
                        } else {
                            alert(response.message || 'Error performing action');
                        }
                    },
                    error: function() {
                        alert('Error performing action. Please try again.');
                    }
                });
            }
        }
    });

    async function refresh_idcard_orders() {
        const btn = document.getElementById('refreshBtn');
        btn.disabled = true;
        
        // Create progress bar container if it doesn't exist
        let progressContainer = document.getElementById('refreshProgressContainer');
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'refreshProgressContainer';
            progressContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                width: 300px;
                text-align: center;
            `;
            
            const progressBarOuter = document.createElement('div');
            progressBarOuter.style.cssText = `
                width: 100%;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                margin: 10px 0;
            `;
            
            const progressBarInner = document.createElement('div');
            progressBarInner.id = 'refreshProgressBar';
            progressBarInner.style.cssText = `
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #45a049);
                border-radius: 4px;
                transition: width 0.3s ease;
            `;
            
            const progressText = document.createElement('div');
            progressText.id = 'refreshProgressText';
            progressText.style.cssText = `
                margin-top: 10px;
                color: #666;
                font-size: 14px;
            `;
            progressText.innerText = 'Refreshing data...';
            
            progressBarOuter.appendChild(progressBarInner);
            progressContainer.appendChild(progressBarOuter);
            progressContainer.appendChild(progressText);
            document.body.appendChild(progressContainer);
        }

        // Async fetch logic
        const overall_idcard_order_array = [];
        const totalSchools = school_list.length;
        let completedSchools = 0;
        
        const mapLoop = async () => {
            const promises = school_list.map(async (school) => {
                try {
                    const response = await get_idcard_orders_pipeline(school.school_code, school.school_domain, default_acad_year, school.id);
                    overall_idcard_order_array.push([school.school_code, response]);
                    
                    // Update progress for fetching data (80% of total progress)
                    completedSchools++;
                    const progress = (completedSchools / totalSchools) * 80;
                    document.getElementById('refreshProgressBar').style.width = `${progress}%`;
                    document.getElementById('refreshProgressText').innerText = `Fetching school data... ${Math.round(progress)}%`;
                } catch (err) {
                    console.log(err);
                }
            });

            await Promise.all(promises);

            // Arrange in sequence
            const temp_array = [], table_array = [];
            school_list.forEach((sl) => {
                overall_idcard_order_array.forEach((ola) => {
                    if (sl.school_code === ola[0]) {
                        temp_array.push(ola);
                        table_array.push(ola);
                    }
                });
            });

            // Update progress text for database update (remaining 20% of progress)
            document.getElementById('refreshProgressText').innerText = 'Updating database...';
            document.getElementById('refreshProgressBar').style.width = '90%';

            // Update database
            await new Promise((resolve, reject) => {
                $.ajax({
                    url: '<?php echo base_url("msm_v3/dashboard/update_idcard_orders") ?>',
                    type: "post",
                    data: {
                        overall_idcard_order_array: overall_idcard_order_array
                    },
                    success: function (data) {
                        console.log(data);
                        resolve(data);
                    },
                    error: function(err) {
                        reject(err);
                    }
                });
            });
        };

        try {
            await mapLoop();
            
            // Show completion message and 100% progress
            document.getElementById('refreshProgressText').innerText = 'Refresh complete!';
            document.getElementById('refreshProgressBar').style.width = '100%';
            
            // Remove progress bar after a short delay
            setTimeout(() => {
                progressContainer.remove();
            }, 1000);
        } catch (error) {
            document.getElementById('refreshProgressText').innerText = 'Error refreshing data';
            setTimeout(() => {
                progressContainer.remove();
            }, 2000);
        } finally {
            // Re-enable button
            btn.disabled = false;
            btn.innerText = "Refresh";
        }
    }   

    function get_idcard_orders_pipeline(school_code, school_domain, acad_year,school_id) {
        // console.log(school_id);
        
        return new Promise(function (resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: "post",
                data: {
                    school_code: school_code,
                    school_domain: school_domain,
                    acad_year: acad_year,
                    school_id:school_id,
                    api: "get_overall_idcard_orders",
                },
                success: function (data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function (err) {
                    reject(err);
                },
            });
        });
    }
</script>

<?php $this->load->view('msm_v3/scripts/id_card_dashboard_script'); ?>
