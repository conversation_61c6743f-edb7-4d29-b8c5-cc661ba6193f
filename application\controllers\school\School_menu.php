<?php

/**
 * Description of School_menu
 *
 * <AUTHOR>
 */
class School_menu extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('SCHOOL')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('SCHOOL.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('class_master_model');
    $this->load->model('school_model', 'sm');
    $this->load->library('filemanager');
    $this->config->load('form_elements');
    $this->load->model("create_academic_year_model");
    $this->load->model('feesv2/Fees_blueprint_model','fbm');
    $this->load->library('fee_library');
    $this->load->model('academics/ManageSubject_model');
  }

  function index() {
    /*$data['permitSchoolModule'] = $this->authorization->isAuthorized('SCHOOL.MODULE');
    $data['permitStaffQRCodes'] = $this->authorization->isAuthorized('SCHOOL.STAFF_QR_CODES');
    $data['permitStudentQRCodes'] = $this->authorization->isAuthorized('SCHOOL.STUDENT_QR_CODES');
    $data['permitStudentPhotos'] = $this->authorization->isAuthorized('SCHOOL.STUDENT_PHOTOS');
    $data['permitParentQRCodes'] = $this->authorization->isAuthorized('SCHOOL.PARENT_QR_CODES');
    $data['permitClassMaster'] = $this->authorization->isAuthorized('SCHOOL.CLASS_MASTER');
    $data['permitClassMaster_v2'] = $this->authorization->isAuthorized('SCHOOL.CLASS_MASTER_V2');*/

    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Class & Sections',
        'sub_title' => 'Add/Edit Classes & Sections',
        'icon' => 'svg_icons/classandsection.svg',
        'url' => $site_url.'school/school_menu/class_master_v2',
        'permission' => $this->authorization->isAuthorized('SCHOOL.CLASS_SECTION')
      ],
     
      /*[
        'title' => 'Class & Sections V2',
        'sub_title' => 'Add/Edit Classes & Sections',
        'icon' => 'svg_icons/classandsection.svg',
        'url' => $site_url.'school/school_menu/class_masterv2',
        'permission' => $this->authorization->isAuthorized('SCHOOL.CLASS_MASTER_V2')
      ],
      [
        'title' => 'Class Master',
        'sub_title' => 'Add/Edit Classes & Sections',
        'icon' => 'svg_icons/classandsection.svg',
        'url' => $site_url.'school/school_menu/class_master',
        'permission' => $this->authorization->isAuthorized('SCHOOL.CLASS_MASTER')
      ],
      [
        'title' => 'Class',
        'sub_title' => 'Manage Classes',
        'icon' => 'svg_icons/classandsection.svg',
        'url' => $site_url.'school/class_controller',
        'permission' => $this->authorization->isAuthorized('SCHOOL.MODULE')
      ],*/
      [
        'title' => 'Master Classes',
        'sub_title' => 'Create Master Classes',
        'icon' => 'svg_icons/classmaster.svg',
        'url' => $site_url.'school/school_menu/master_classes',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Semesters',
        'sub_title' => 'Add/Edit Semesters',
        'icon' => 'svg_icons/classandsection.svg',
        'url' => $site_url.'school/school_menu/semesters',
        'permission' => $this->authorization->isAuthorized('SCHOOL.SEMESTER')
      ],
      [
        'title' => 'Mapping Semester',
        'sub_title' => 'Class-master - Semester Association',
        'icon' => 'svg_icons/classmaster.svg',
        'url' => $site_url.'school/school_menu/semester_association',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Subject Master',
        'sub_title' => 'Manage subjects master',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'school/subject_master',
        'permission' => $this->authorization->isAuthorized('SUBJECT.MASTER')
      ],
      [
        'title' => 'Elective Master Group',
        'sub_title' => 'Manage elective master groups',
        'icon' =>'svg_icons/electivereports.svg' ,
        'url' => $site_url.'student/Electives/groups',
        'permission' => $this->authorization->isAuthorized('SUBJECT.ELECTIVE_MASTER')
      ],
      [
        'title' => 'Student Elective Master',
        'sub_title' => 'Manage student elective master',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'student/Electives',
        'permission' => $this->authorization->isAuthorized('SUBJECT.STUDENT_ELECTIVE')
      ],
      [
        'title' => 'Student Elective Report',
        'sub_title' => 'Student Elective Report',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'student/Electives/student_elective_report',
        'permission' => $this->authorization->isAuthorized('SUBJECT.STUDENT_ELECTIVE')
      ],
      [
        'title' => 'School Report Format',
        'sub_title' => 'Reference UI',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'school/school_menu/report_reference',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Transport Stops Entry',
        'sub_title' => 'Stops Entry',
        'icon' => 'svg_icons/stop.svg',
        'url' => $site_url.'feesv2/fees_transport/stops',
        'permission' => $this->authorization->isAuthorized('SCHOOL.PERMIT_ASSING_STOP')
      ],
      [
        'title' => 'Manage Academic year',
        'sub_title' => 'Academic year information',
        'icon' => 'svg_icons/classmaster.svg',
        'url' => $site_url.'school/school_menu/Create_academic_year',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Combinations',
        'sub_title' => 'Manage Combinations',
        'icon' => 'svg_icons/classmaster.svg',
        'url' => $site_url.'school/school_menu/creat_combinations',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Email Bounce Report',
        'sub_title' => 'Reference UI',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'school/school_menu/email_bounce_report',
        'permission' => $this->authorization->isSuperAdmin()
      ],
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['localhost'] = array(
      [
        'title' => 'Staff QR Code',
        'sub_title' => 'Generate Staff QR Codes',
        'icon' => 'svg_icons/staffqrcode.svg',
        'url' => $site_url.'download_controller/generate_staff_qr_codes',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STAFF_QR_CODES')
      ],
      [
        'title' => 'Student QR Code',
        'sub_title' => 'Generate Student QR Codes',
        'icon' => 'svg_icons/studentqrcode.svg',
        'url' => $site_url.'download_controller/generate_student_qr_codes',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STUDENT_QR_CODES')
      ],
      [
        'title' => 'Student URL QR Code',
        'sub_title' => 'Generate Student URL QR Codes',
        'icon' => 'svg_icons/studentqrcode.svg',
        'url' => $site_url.'Url_qr_codes_controller/view_student_url_qr_codes',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STUDENT_URL_QR_CODES')
      ],
      [
        'title' => 'Student Photo',
        'sub_title' => 'Generate Student Photos',
        'icon' => 'svg_icons/studentphoto.svg',
        'url' => $site_url.'download_controller/generate_student_photos',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STUDENT_PHOTOS')
      ],
      [
        'title' => 'Staff Photo',
        'sub_title' => 'Download Staff Photos',
        'icon' => 'svg_icons/staff.svg',
        'url' => $site_url.'download_controller/generate_staff_photos',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STUDENT_PHOTOS')
      ],
      [
        'title' => 'Parent Photos',
        'sub_title' => 'Download Parent Photos',
        'icon' => 'svg_icons/talktous.svg',
        'url' => $site_url.'download_controller/generate_parent_photos',
        'permission' => $this->authorization->isAuthorized('SCHOOL.STUDENT_PHOTOS')
      ],
      [
        'title' => 'Parent QR Codes',
        'sub_title' => 'Generate Parent QR Codes',
        'icon' => 'svg_icons/parentqrcode.svg',
        'url' => $site_url.'download_controller/generate_parent_qr_codes',
        'permission' => $this->authorization->isAuthorized('SCHOOL.PARENT_QR_CODES')
      ],
      [
        'title' => 'Generate QR Code',
        'sub_title' => 'Generate QR Code',
        'icon' => 'svg_icons/generateqrcode.svg',
        'url' => $site_url.'library_controller/print_add_cart',
        'permission' => $this->authorization->isAuthorized('LIBRARY.GENERATE_QR_CODES')
      ]
    );
    $data['localhost'] = checkTilePermissions($data['localhost']);

    $data['tool_tile'] = array(
      [
        'title' => 'Order of Sections',
        'sub_title' => 'Ordering of Sections',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'school/school_menu/section_ordering',
        'permission' => $this->authorization->isAuthorized('SCHOOL.ORDER_OF_SECTION')
      ],
      [
        'title' => 'Order of Classes',
        'sub_title' => 'Ordering of Classes',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'school/school_menu/class_ordering',
        'permission' => $this->authorization->isAuthorized('SCHOOL.ORDER_OF_CLASS')
      ],
      [
        'title' => 'Order of Master Classes',
        'sub_title' => 'Ordering of Master Classes',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'school/school_menu/master_class_ordering',
        'permission' => $this->authorization->isAuthorized('SCHOOL.ORDER_OF_MASTER_CLASS')
      ],
      [
        'title' => 'Change Case of Names',
        'sub_title' => 'Changing Case of Names',
        'icon' => 'svg_icons/subjects.svg',
        'url' => $site_url.'school/school_menu/change_case_of_names',
        'permission' => $this->authorization->isAuthorized('SCHOOL.CHANGE_CASE_OF_NAME')
      ]
    );
    $data['tool_tile'] = checkTilePermissions($data['tool_tile']);


    $data['main_content'] = 'school/menu/index.php';
    $this->load->view('inc/template', $data);
  }

  public function report_reference() {
    $this->load->model('feesv2/reports_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_student_model');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();

    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;

    $supportedColumns = [
     'father_name', 'father_mobile_no', 'fee_amount', 'transaction', 'concession', 'components', 'transaction','fine_amount','card_charge_amount','discount_amount','balance','sts_number','academic_year_of_joining','refund_amount'
    ];

    $finalColumns = array();
    $payment_modes = array();

    $selected_columns = $this->reports_model->get_selected_columns($fee_blueprints->id);

 
    foreach ($supportedColumns as $key => $sc) {
      if (!empty($selected_columns)) {
        foreach ($selected_columns as $key => $columns) {
          if ($sc === $columns) {
            $finalColumns[] = $sc;
          }
        }
      }
    }
    $payment_modes = json_decode($fee_blueprints->allowed_payment_modes);
   
    $postData = $this->input->post();
    $filterMode = empty($postData);
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['category'] = $this->settings->getSetting('category');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['board'] = $this->settings->getSetting('board');
    $data['donors'] = $this->Student_Model->getDonorList();    
    $data['payment_mode'] = $payment_modes;
    $data['payment_modeJSON'] = json_encode($payment_modes);
    $data['combination'] = $this->Student_Model->getCombList();
    $data['selectedColumns'] = $this->input->post('selectedColumns');
    $classId = $this->input->post('class_name');
    $data['finalColumns'] = $finalColumns;
    $data['finalColumnsJSON'] = json_encode($finalColumns);
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/reports/report_reference';
    $this->load->view('inc/template', $data);
  }

  public function class_master_v2(){
    $data['period_templates'] = $this->ManageSubject_model->getPeriodTemplates();
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    $data['master_classes'] = $this->class_master_model->get_not_added_master_classes();
    $data['branches'] = $this->class_master_model->branchesName();
    $data['staff_list'] = $this->sm->get_staff_list();
    $data['board']       =  $this->settings->getSetting('board');
    $data['medium']       =  $this->settings->getSetting('medium');
    $class_types       =  $this->settings->getSetting('classType');
    $data['classTypes'] = [];
    foreach($class_types as $type) {
      $data['classTypes'][$type->value] = $type->name;
    }
    $data['sections'] = $this->class_master_model->get_sections();
    $receipt_book = $this->fbm->get_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
    $data['main_content']    = 'school/class/class_section_list';
    $this->load->view('inc/template', $data);
  }

  public function section_ordering() {
    $acad_year_id = $this->acad_year->getAcadYearId();
    $data['sections'] = $this->class_master_model->get_year_wise_classData($acad_year_id);
    $data['main_content']    = 'school/class/ordering_class_sections';

    $this->load->view('inc/template', $data);
  }

  public function section_ordering_submit() {
    $section_ids = $this->input->post('section_id');
    $order_nums = $this->input->post('order_num');
    $status = $this->class_master_model->submit_section_order($section_ids, $order_nums);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Sections ordered successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('school/school_menu/section_ordering');
  }

  public function class_ordering() {
    $data['classes'] = $this->class_master_model->getClasses();
    $data['main_content']    = 'school/class/ordering_classes';

    $this->load->view('inc/template', $data);
  }

  public function class_ordering_submit() {
    $class_ids = $this->input->post('class_id');
    $order_nums = $this->input->post('order_num');
    $status = $this->class_master_model->submit_class_order($class_ids, $order_nums);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Classes ordered successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('school/school_menu/class_ordering');
  }

  public function master_class_ordering() {
    $data['classes'] = $this->class_master_model->get_master_classes();
    $data['main_content']    = 'school/class/ordering_master_classes';

    $this->load->view('inc/template', $data);
  }

  public function master_class_ordering_submit() {
    $master_class_ids = $this->input->post('class_id');
    $order_nums = $this->input->post('order_num');
    $status = $this->class_master_model->submit_master_class_order($master_class_ids, $order_nums);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Classes ordered successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('school/school_menu/master_class_ordering');
  }

  public function change_case_of_names() {
    $data['main_content']    = 'school/class/change_case_of_names';
    $this->load->view('inc/template', $data);
  }

  public function change_case_of_names_submit() {
    $table = $this->input->post('table');
    $case = $this->input->post('case');
    $col = $this->input->post('col');
    $result = $this->sm->change_case_of_names($table, $case, $col);
    // echo $result;
  }

  public function get_classes_sections_by_year(){
    $yClass = $_POST['yClass'];
    $result = $this->class_master_model->get_classes_sections_by_year($yClass);
    echo json_encode($result);
  }

  public function save_cloned_class_section() {
    // echo '<pre>'; print_r($_POST); die();
    $acad_year_id = $this->acad_year->getAcadYearId();
    $status = $this->class_master_model->save_cloned_class_section($acad_year_id);
    if($status) {
      $this->session->set_flashdata('flashSuccess', 'Classes cloned successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong..');
    }
    redirect('school/school_menu/class_master_v2');
  }

  public function Create_academic_year_data(){
    $Create_academic_year=$this->create_academic_year_model->get_academic_year();
    echo json_encode($Create_academic_year);
  }
  
  public function insert_create_academic_date(){
    echo $this->create_academic_year_model->add_academic_year($this->input->post());
  }

  public function edit_create_academic_data(){
    echo $this->create_academic_year_model->edit_academic_year($this->input->post());
  }
  public function delete_academic_year_data(){
    $id = $_POST['id'];
    echo $this->create_academic_year_model->delete_academic_year($id);
  }
  public function Create_academic_year(){
    $data['main_content'] = 'school/class/create_academic_year';
    $this->load->view('inc/template', $data);
  }

  public function creat_combinations(){
    $data['main_content'] = 'school/class/creat_combinations_page';
    $this->load->view('inc/template', $data);
  }

  public function master_classes(){
    $data['master_classes'] = $this->class_master_model->get_master_classes();
    $data['all_combinations'] = $this->class_master_model->get_all_combinations();
    $data['main_content']    = 'school/class/master_classes';
    $this->load->view('inc/template', $data);
  }

  public function saveMasterClass(){
    $status = $this->class_master_model->saveMasterClass();
    echo $status;
  }

  public function get_classes(){
   
    $data['classes'] = $this->class_master_model->getClasses();
    $data['staff_list'] = $this->sm->get_staff_list();
    echo json_encode($data);
  }
  public function getSingleClassDetails(){
    $class_id = $_POST['class_id'];
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    $class_types       =  $this->settings->getSetting('classType');
   $data['classTypes'] = [];
    foreach($class_types as $type) {
      $data['classTypes'][$type->value] = $type->name;
    }
   // echo '<pre>';print_r($class_id); die();

    $data['single_class']= $this->class_master_model->getSingleClassDetails($class_id);
    foreach ($data['single_class'] as $key => &$val) {
      if (!empty($val->template_format)) {
        switch ($val->template_format) {
          case '1':
            $val->receipt = $val->infix.sprintf("%'.0".$val->digit_count."d",$val->running_number).'/'.$val->year;
            break;
          case '2':
          $val->receipt = $val->infix.sprintf("%'.0".$val->digit_count."d",$val->running_number);
            break;
          case '3':
          $val->receipt = $val->infix.'/'.$val->year.'/'.sprintf("%'.0".$val->digit_count."d",$val->running_number);
            break;
          case '4':
          $val->receipt = $val->infix.'/'.$val->year.'/'.sprintf("%'.0".$val->digit_count."d",$val->running_number);
            break;
          default:
           $val->receipt = $val->infix.sprintf("%'.0".$val->digit_count."d",$val->running_number);
            break;
        }
      }
     
    }

    $data['selected_period_templates'] = $this->ManageSubject_model->getSelectedPeriodTemplates($_POST);
    //echo '<pre>';print_r($data['single_class']); die();
    echo json_encode($data);
  }
  public function getSectionDetails(){
    $class_id = $_POST['class_id'];
   /* $data['classTypes'] = [];
    foreach($class_types as $type) {
      $data['classTypes'][$type->value] = $type->name;
    }*/
    //echo '<pre>';print_r($class_id); die();
  // $data['sections'] = $this->class_master_model->get_sections($class_id );
    $data['class'] = $this->class_master_model->getClassDetails($class_id);
    $data['section_data']= $this->class_master_model->getSectionDetails($class_id);
    //echo '<pre>';print_r($data['single_class']); die();
    echo json_encode($data);
  }
  public function getSections(){
    $class_id = $_POST['classId'];
    $data['section_data'] = $this->class_master_model->get_sections_classwise($class_id );
    echo json_encode($data);
  }
  


  public function class_masterv2(){
    $data['transvers'] = 1;
    $data['classlist']=$this->class_master_model->get_list_class_sections();
    $data['placeHolderAdded'] = 0;
    foreach ($data['classlist'] as $key => $value) {
      if($value->is_placeholder == 1) {
        $data['placeHolderAdded'] = 1;
      }
    }
    
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    // $data['class_details'] = $this->class_master_model->getClassDetailsById($id);
    $data['sections'] = $this->class_master_model->get_sections();
    
    $data['staff_list'] = $this->sm->get_staff_list();
    $data['board']       =  $this->settings->getSetting('board');
    $data['medium']       =  $this->settings->getSetting('medium');
    $class_types       =  $this->settings->getSetting('classType');
    $data['classTypes'] = [];
    foreach($class_types as $type) {
      $data['classTypes'][$type->value] = $type->name;
    }
    // $data['class_data'] = $this->sm->get_class_data_by_id();
    // $data['staff_list'] = $this->sm->get_staff_list();
    // echo $data['placeHolderAdded'];die();
    // echo '<pre>';print_r($data['classTypes']);
  //  echo '<pre>';print_r($data['staff_list']);
  //   die();
    $data['main_content']    = 'school/class/class_section';
    $this->load->view('inc/template', $data);
  }

  public function class_master(){
    $data['transvers'] = 1;
    $data['classlist']=$this->class_master_model->get_list_class_sections();
    $data['placeHolderAdded'] = 0;
    foreach ($data['classlist'] as $key => $value) {
      if($value->is_placeholder == 1) {
        $data['placeHolderAdded'] = 1;
      }
    }
    // echo $data['placeHolderAdded'];die();
    $promotionAcadYearId = $this->acad_year->getPromotionAcadYearId();
    $data['promotionClasses'] = $this->class_master_model->get_classes($promotionAcadYearId);
    $data['main_content']    = 'master/class/index';
    $this->load->view('inc/template', $data);
  }

  public function add_class() { 
    $data['transvers'] = 1;
    $data['board']       =  $this->settings->getSetting('board');
    $data['medium']       =  $this->settings->getSetting('medium');
    $data['classTypes']       =  $this->settings->getSetting('classType');
    $data['main_content'] = 'master/class/add_class';
    $this->load->view('inc/template', $data);
  }

  public function submit_classv2() {
    $result=$this->class_master_model->submit_data_classv2($this->s3FileUpload($_FILES['fee_structure_template']));
    /*if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('school/school_menu/class_masterv2');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('school/school_menu/class_masterv2');
    }*/
  }

  public function s3FileUpload($file,$folder_name='fees_structure_template') {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
  }


  public function submit_class() {
    $result=$this->class_master_model->submit_data_class();
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('school/school_menu/class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('school/school_menu/class_master');
    }
  }

  public function add_section($id) { 
    $data['transvers'] = 1;
    $data['class_details'] = $this->class_master_model->getClassDetailsById($id);
    $data['sections'] = $this->class_master_model->get_sections();
    $data['main_content']    = 'master/class/add_sections';
    $this->load->view('inc/template', $data);
  }

  
  public function submit_section($class_id) {
    $result=$this->class_master_model->submit_data_section($class_id);
    if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('school/school_menu/class_master');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('school/school_menu/class_master');
    }
  }
  public function submit_sectionv2() {
    $class_id = $_POST['classId'];
    // echo "<pre>";print_r($class_id);die();
    $result=$this->class_master_model->submit_data_sectionv2($class_id);
    /*if($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Submit Class.');
      redirect('school/school_menu/class_masterv2');
    } else {
      $this->session->set_flashdata('flashError', 'Something Wrong..');
      redirect('school/school_menu/class_masterv2');
    }*/
  }
  public function submit_section_v2() {
    $data = $this->class_master_model->submit_data_section_v2();
    echo json_encode($data);
    
   // $result=$this->class_master_model->submit_data_sectionv2($class_id);
    
  }

  public function semesters(){
    $data['semester'] = $this->class_master_model->get_semester_data();
    $data['main_content']    = 'school/semester/semester_list';
    $this->load->view('inc/template', $data);
  }

  public function saveSemester(){
    $status = $this->class_master_model->saveSemesterData();
    echo $status;
  }

  public function semester_association(){
    $data['semester'] = $this->class_master_model->get_semester_data();
    $data['master_classes'] = $this->class_master_model->get_master_classes();
    $data['mapping_semester'] = $this->class_master_model->get_mapping_semester_data();
    $data['main_content']    = 'school/semester/mapping_semester_list';
    $this->load->view('inc/template', $data);
  }

  public function saveMappingSemester(){
    $status = $this->class_master_model->savesaveMappingSemesterData();
    echo $status;
  }

  public function update_class_status_switch_check(){
    $classid = $_POST['classid'];
    $status = $_POST['status'];
    echo $this->class_master_model->update_class_status_switch_check($classid, $status);
  }

  public function delete_class(){
    $classid= $_POST['classId'];
    echo $this->class_master_model->delete_class($classid);
  }

  public function delete_section(){
    $class_id = $_POST['class_id'];
    $sec_id = $_POST['sec_id'];
    $section_name = $_POST['section_name'];
    echo $this->class_master_model->delete_section($class_id,$sec_id,$section_name);

    
  }

  public function add_combinations() {
    echo $this->class_master_model->add_combinations();
  }

  public function get_combinations(){
    echo json_encode($this->class_master_model->get_combinations());
  }

  public function update_combination_status(){
    echo $this->class_master_model->update_combination_status();
  }

  public function save_combination_mapping(){
    echo $this->class_master_model->save_combination_mapping();
  }

  public function email_bounce_report(){
    $data['email'] = $this->sm->get_email_bounce_report();
    $data['main_content']    = 'school/email_bounce.php';
    $this->load->view('inc/template', $data);
  }
}