<script>
    $(document).ready(function () {
        $(".overview_tb_btn").css("display", "none");
        $(".report_tb_dropdown").css("display", "block");
        $(".staff_transfer_tb_btn").css("display", "block");
    });

    $("#from_school").on("change", function(){
        var selectedSchool = [];
        $("#from_school option:selected").each(function() {
            var schoolCode = $(this).val();
            var schoolDomain = $(this).data("school-domain");
            if(schoolCode) {
                selectedSchool.push({ code: schoolCode, domain: schoolDomain });
            }
        });
        let selectedFromSchool = this.value;
        let toSchoolDropdown = document.getElementById("to_school");
        let toSchoolList = document.getElementById("to_school_list");

        toSchoolDropdown.innerHTML = `<option value="">Select School</option>`;
        document.querySelectorAll("#from_school option").forEach(option => {
            if (option.value !== selectedFromSchool && option.value !== "") {
                let newOption = document.createElement("option");
                newOption.value = option.value;
                newOption.textContent = option.textContent;
                newOption.setAttribute("data-school-domain", option.getAttribute("data-school-domain"));
                newOption.setAttribute("data-school-name", option.getAttribute("data-school-name"));
                toSchoolDropdown.appendChild(newOption);
            }
        });
        var schoolPromises = selectedSchool.map(function(school) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: '<?php echo site_url('msm_v3/dashboard/bridge'); ?>',
                    type: 'post',
                    data: {
                        'school_code': school.code,
                        'school_domain': school.domain,
                        'api': 'get_from_school_staffs'
                    },
                    success: function(data) {
                        var responseObj = JSON.parse(data);
                        var staffArray = JSON.parse(responseObj.response);

                        var labelP = document.createElement('p');
                        labelP.innerHTML = 'Select <b>FROM</b> Staff <font color="red">*</font>';

                        var selectDropdown = document.createElement('select');
                        selectDropdown.classList.add('form-select');
                        selectDropdown.id = 'from_staff_dropdown';

                        var defaultOption = document.createElement('option');
                        defaultOption.value = "";
                        defaultOption.textContent = "Select Staff";
                        selectDropdown.appendChild(defaultOption);

                        staffArray.forEach(function(staff) {
                            var option = document.createElement('option');
                            option.value = staff.id;
                            option.textContent = staff.staff_name.trim();
                            selectDropdown.appendChild(option);
                        });

                        var fromStaffsDiv = document.getElementById('from_staff_list');
                        fromStaffsDiv.innerHTML = '';
                        fromStaffsDiv.appendChild(labelP);
                        fromStaffsDiv.appendChild(selectDropdown);

                        $("#from_staff_list").css('display', 'block')
                        $("#to_school_list").css('display', 'block')
                        document.getElementById("transferStaffBtn").disabled = false;
                        resolve();
                    },
                    error: function(error) {
                        reject(error);
                    }
                });
            });
        });
    });

    function updateProgress(completed, total) {
        let percentage = Math.round((completed / total) * 100);
        $("#transferProgressBar")
            .css({
                "width": percentage + "%",
                "background-color": "#0F256E"
            })
            .attr("aria-valuenow", percentage)
            .text(percentage + "%");
    }

    function completeTransferUI(to_school_name) {
        $("#transferStatus").text("Transfer complete!");
        $("#transferProgressBar")
            .css({
                "width": "100%",
                "background-color": "#0F256E"
            })
            .attr("aria-valuenow", 100)
            .text("100%");
        $("#next_steps").css('display', 'block');
        $("#to_school_name").text(to_school_name);
    }

    function transfer_staff() {
        var from_school = [], to_school_code, to_school_domain, to_school_name;
        var selectedStaff = $('#from_staff_dropdown').val();
        console.log(selectedStaff);
        
        $("#from_school option:selected").each(function() {
            var schoolCode = $(this).val();
            var schoolDomain = $(this).data("school-domain");
            if(schoolCode) {
                from_school.push({ code: schoolCode, domain: schoolDomain });
            }
        });
        $("#to_school option:selected").each(function() {
            to_school_code = $(this).val();
            to_school_domain = $(this).data("school-domain");
            to_school_name = $(this).data("school-name");
        });

        if (!selectedStaff || selectedStaff.trim() === "") {
            alert("Please select a staff from the list.");
            return;
        }

        if (!to_school_code || to_school_code.trim() === "") {
            alert("Please select a school from the To School list.");
            return;
        }

        $("#transfer_ui").css('display', 'block');
        
        var staff_data = from_school.map(function(school) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: '<?php echo site_url("msm_v3/dashboard/bridge"); ?>',
                    type: 'post',
                    data: {
                        'school_code': school.code,
                        'school_domain': school.domain,
                        'staff_id': selectedStaff,
                        'api': 'get_from_staff_data'
                    },
                    success: function(response) {
                        resolve(JSON.parse(JSON.parse(response).response));
                    },
                    error: function(error) {
                        console.error("Error transferring staff:", error);
                        reject(error);
                    }
                });
            });
        });

        Promise.all(staff_data)
            .then(function(result) {
                console.log(result);
                let staffMasterObj;
                let userObject;
                let completed = 0;
                let totalTables = 0;
                
                result.forEach(element => {
                    element.forEach(tables => {
                        if (tables["table"] === "staff_master") {
                            staffMasterObj = tables;
                        }
                        else if (tables["table"] === "avatar"){
                            userObject = tables;
                        }
                    });
                });

                if (!staffMasterObj) {
                    throw new Error("No staff_master data found");
                }
                
                return new Promise(function(resolve, reject) {
                    let staffRecord = { ...staffMasterObj.res[0] };

                    const ignoreKeys = ['id', 'designation', 'department', 'exit_remarks', 'exit_update_on', 'exit_updated_by', "last_date_of_work", "last_modified_by", "resignation_date", "resignation_letter_doc", "created_on", "modified_on","previous_designation_name"];
                    ignoreKeys.forEach(key => delete staffRecord[key]);

                    staffRecord.status = 2;
                    totalTables++;

                    $.ajax({
                        url: '<?php echo site_url("msm_v3/dashboard/bridge"); ?>',
                        type: 'POST',
                        data: { 
                            'school_code': to_school_code,
                            'school_domain': to_school_domain,
                            'data': JSON.stringify(staffRecord),
                            'user_data': JSON.stringify(userObject),
                            'api': 'insert_staff_master' 
                        },
                        success: function(response) {
                            let new_staff_master_id = JSON.parse(JSON.parse(response).response);
                            console.log("staff_master inserted, id:", new_staff_master_id);
                            resolve({ result, new_staff_master_id });
                        },
                        error: function(err) {
                            reject(err);
                        }
                    });
                });
            })
            .then(function({ result, new_staff_master_id }) {
                let otherAjaxPromises = [];
                let totalTables = 0;
                let completed = 1;

                result.forEach(element => {
                    element.forEach(tables => {
                        if (tables["table"] !== "staff_master" && tables["table"] !== "avatar") {
                            totalTables += tables.res.length;
                            tables.res.forEach(record => {
                                let staffRecord = { ...record };
                                delete staffRecord.id;
                                staffRecord.staff_id = new_staff_master_id;

                                let p = new Promise(function(resolve, reject) {
                                    $.ajax({
                                        url: '<?php echo site_url("msm_v3/dashboard/bridge"); ?>',
                                        type: 'POST',
                                        data: { 
                                            'school_code': to_school_code,
                                            'school_domain': to_school_domain,
                                            'data': JSON.stringify(staffRecord),
                                            'table': tables["table"],
                                            'staff_id': new_staff_master_id,
                                            'api': 'insert_staff_tables' 
                                        },
                                        success: function(response) {
                                            completed++;
                                            updateProgress(completed, totalTables);
                                            
                                            let tableName = "Transfer of ";
                                            let formattedTableName = tables["table"]
                                                .replace(/_/g, ' ')
                                                .split(' ')
                                                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                                .join(' ');

                                            tableName += formattedTableName + " row is Completed";
                                            $("#transferStatus").text(tableName);

                                            console.log(tables["table"] + " inserted for staff_id:", new_staff_master_id);
                                            resolve(response);
                                        },
                                        error: function(err) {
                                            reject(err);
                                        }
                                    });
                                });
                                otherAjaxPromises.push(p);
                            });
                        }
                    });
                });
                return Promise.all(otherAjaxPromises);
            })
            .then(function(allResponses) {
                completeTransferUI(to_school_name);
            })
            .catch(function(err) {
                console.error("An error occurred:", err);
            });

    }

    //Back To Home
    function back_to_home(){
        $(".overview_cards_wrapper").css("display", "flex");
        $(".top_bar_btn").css("display", "none");
        $(".overview_tb_btn").css("display", "block");
    }
</script>