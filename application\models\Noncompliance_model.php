<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Noncompliance_model extends CI_Model
{
    private $yearId;
    private $current_branch;
    public function __construct()
    {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

    public function add_category($category_name, $category_color, $penalty_name, $penalty_staff_id)
    {
        $category_data = array(
            'name' => $category_name,
            'color_code' => $category_color,
            'penalty_name' => $penalty_name,
            'assigned_staff_id' => $penalty_staff_id
        );
        return $this->db->insert('snc_category', $category_data);
    }

    public function get_category_types()
    {
        $category_objs = $this->db_readonly->select("snc.id as snc_id, snc.name as snc_name, snc.color_code, snc.penalty_name, sm.id as assigned_staff_id, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS assigned_staff_name,snc.status")
            ->from('snc_category snc')
            ->join('staff_master sm', 'sm.id=snc.assigned_staff_id')
            ->where('snc.status',1)
            ->order_by('name')
            ->get()->result();
        return $category_objs;
    }

    public function get_category_types_new()
    {
        $category_objs = $this->db_readonly->select("snc.id as snc_id, snc.name as snc_name, snc.color_code, snc.penalty_name, sm.id as assigned_staff_id, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS assigned_staff_name,snc.status")
            ->from('snc_category snc')
            ->join('staff_master sm', 'sm.id=snc.assigned_staff_id')
            ->get()->result();
        return $category_objs;
    }

    public function delete_category($category_id)
    {
        $this->db->where('id', $category_id);
        return $this->db->delete('snc_category');
    }

    public function get_nc_categories()
    {
        $sql = "select * from snc_category
        order by id";
        $categories = $this->db->query($sql)->result();

        //echo "<pre>";print_r($categories); die();

        return $categories;
    }

    public function add_penalty($penalty_name)
    {
        $penalty_data = array(
            'penalty_name' => $penalty_name,
        );
        return $this->db->insert('snc_penalty', $penalty_data);
    }

    public function edit_penalty($penalty_name, $penalty_id)
    {
        $penalty_data = array(
            'penalty_name' => $penalty_name,
        );
        return $this->db->where('id', $penalty_id)->update('snc_penalty', $penalty_data);
    }

    public function get_penalty_types()
    {
        $result = $this->db->select("sncp.id, sncp.penalty_name as penalty_name")
            ->from('snc_penalty sncp')
            ->get()->result();

        return $result;
    }

    public function delete_penalty($penalty_id)
    {
        $this->db->where('id', $penalty_id);
        return $this->db->delete('snc_penalty');
    }

    public function get_nc_penalty()
    {
        $sql = "select * from snc_penalty
        order by name";
        $penalty = $this->db->query($sql)->result();

        //echo "<pre>";print_r($penalty); die();

        return $penalty;
    }

    public function getClassNames()
    {

        $this->db_readonly->select("cs.id, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name");
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
        $this->db_readonly->join('class_master cm', "cm.class_name = cs.class_name");
        $this->db_readonly->order_by('c.id, cs.id');
        $this->db_readonly->where('cs.is_placeholder!=1');

        $result = $this->db_readonly->get()->result();

        return $result;
    }
    public function  get_class_list(){
        $this->db_readonly->select('c.id as classId, c.class_name as className, promotion_class');
        $this->db_readonly->from('class as c');
        $this->db_readonly->where('c.acad_year_id',$this->yearId);
        $this->db_readonly->where('c.is_placeholder!=',1);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->order_by('c.display_order, c.id');
        return $this->db_readonly->get()->result();
    }

    public function get_all_staff()
    {
        $staff_details = $this->db_readonly->select("id, CONCAT(ifnull(first_name,''), ' ', ifnull(last_name,'')) AS staff_name")
            ->from('staff_master')
            ->where('status', '2')
            ->get()->result();

        return $staff_details;
    }

    public function get_capacity_types()
    {
        $sql = "select * from sov2_staff_capacity";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function getStaffList() {
        return $this->db_readonly->select("id as staff_id,reporting_manager_id,CONCAT(ifnull(first_name, ''),' ', ifnull(last_name, '')) as staff_name, designation")->where('status', 2)->order_by('first_name')->get('staff_master')->result();
    }

    public function get_penaly_by_snc_category() {
        return $this->db_readonly->select("*")->where('id', $_POST['categoryId'])->get('snc_category')->row();
    }

    public function get_single_staff_nc_data($class_section_id, $from_date, $to_date)
    {   

        $staff_id = $this->authorization->getAvatarStakeHolderId();
        if ($class_section_id == '0') {
            $class_section_id = "";
        } else {
            $class_section_id = "and snc.class_section_id = '$class_section_id'";
        }

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          }else {
            $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
          }

          $prefix_order_by = $this->settings->getSetting('prefix_order_by');
          $order_by = 'std.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'std.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'std.admission_number';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }

        $sql = "select snc.non_compliance_image,snc.id,snc.disabled, snc.std_admission_id,$std_name,CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS url_name, snc.class_section_id, snc.remarks, snc.penalty_name, snc.penalty_status, snc_category.color_code, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS created_staff_name,
        CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(snc.snc_date,'%d-%m-%Y') as snc_date, date_format(snc.created_on,'%d-%m-%Y %H:%i') as created_on, snc_category.name as category, CONCAT(ifnull(smc.first_name,''), ' ', ifnull(smc.last_name,'')) AS penalty_staff_name
        from snc_items snc
        left join snc_category snc_category on snc.snc_category_id = snc_category.id
        left join class_section cs on cs.id = snc.class_section_id
        left join staff_master sm on sm.id=snc.created_staff_id
        left join staff_master smc on smc.id=snc.penalty_assigned_staff_id
        left join student_admission std on std.id = snc.std_admission_id
        left join student_year sy on snc.std_year_id=sy.id
        where snc.snc_date>='$from_date' and snc.snc_date<='$to_date' and snc.acad_year_id='$this->yearId' $class_section_id and snc.disabled='0'
        ORDER BY cs.class_name, cs.section_name, $order_by";

        $nc_data = $this->db_readonly->query($sql)->result();
        for($i=0;$i<count($nc_data);$i++) {
            if(empty($nc_data[$i]->non_compliance_image)){
                $nc_data[$i]->non_compliance_image = 'No Image Attached';
            } else {
                $nc_data[$i]->non_compliance_image = $this->filemanager->getFilePath($nc_data[$i]->non_compliance_image);
            }
        }
        return $nc_data;
    }
    public function for_a_particular_student($student_name,$class_name) {
        $result = $this->db_readonly->select("snc.non_compliance_image,snc.id,snc.disabled, snc.std_admission_id, CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name, snc.class_section_id, snc.remarks, snc.penalty_name, snc.penalty_status, snc_category.color_code, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS created_staff_name, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(snc.snc_date,'%d-%m-%Y') as snc_date, date_format(snc.created_on,'%d-%m-%Y %H:%i') as created_on, snc_category.name as category, CONCAT(ifnull(smc.first_name,''), ' ', ifnull(smc.last_name,'')) AS penalty_staff_name")
                ->from('snc_items snc')
                ->where("CONCAT(ifnull(std.first_name,''),' ',ifnull(std.last_name,'')) =",$student_name)
                ->where("CONCAT(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) =",$class_name)
                ->join('snc_category snc_category','snc.snc_category_id = snc_category.id',"left")
                ->join('staff_master sm','sm.id=snc.created_staff_id',"left")
                ->join('staff_master smc','smc.id=snc.penalty_assigned_staff_id',"left")
                ->join('student_admission std','std.id = snc.std_admission_id',"left")
                ->join('class_section cs','cs.id = snc.class_section_id',"left")
                ->get()
                ->result();
        for($i=0;$i<count($result);$i++) {
            if(empty($result[$i]->non_compliance_image)){
                $result[$i]->non_compliance_image = 'No Image Attached';
            } else {
                $result[$i]->non_compliance_image = $this->filemanager->getFilePath($result[$i]->non_compliance_image);
            }
        }
        // echo "<pre>";
        // print_r($result);
        // die();
       return $result;
        
    }
               
               
                
               
                


    public function get_student_nc_history($sa_id)
    {
        $std_nc_data = $this->db_readonly->select("snc.penalty_status, snc.penalty_name, snc_category.color_code, snc.remarks, snc_category.name as category, date_format(snc.snc_date,'%d-%m-%Y %H:%i') as snc_date")
            ->from("snc_items snc")
            ->join("snc_category", "snc.snc_category_id=snc_category.id", "left")
            ->where('std_admission_id', $sa_id)
            ->order_by('snc_date', "desc")
            ->get()->result();

        return $std_nc_data;
    }

    public function get_resolve_penalty_types($show_all, $penalty_status,$acad_year_id)
    {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          } else if ($prefix_student_name == "alpha_rollnum") {
              $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
          }else {
            $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
          }

          $prefix_order_by = $this->settings->getSetting('prefix_order_by');
          $order_by = 'std.first_name';
          if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
          }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'std.enrollment_number';
          }else if($prefix_order_by == "admission_number"){
            $order_by = 'std.admission_number';
          }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
          }


        $this->db_readonly->select("snc.id, snc.std_admission_id, $std_name, snc.class_section_id, snc.remarks, snc.penalty_name, snc.penalty_status, snc_category.color_code, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS created_staff_name,
        CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name, date_format(snc.snc_date,'%d-%m-%Y') as snc_date, date_format(snc.created_on,'%d-%m-%Y %H:%i') as created_on, snc_category.name as category, CONCAT(ifnull(smc.first_name,''), ' ', ifnull(smc.last_name,'')) AS penalty_staff_name, if(isnull(penalty_served_marked_date), 'NA', DATE_FORMAT(penalty_served_marked_date, '%d-%m-%Y')) as penalty_served_date")
            ->from("snc_items snc")
            ->join("snc_category", "snc.snc_category_id = snc_category.id", "left")
            ->join("class_section cs", "cs.id = snc.class_section_id", "left")
            ->join("staff_master sm", "sm.id=snc.created_staff_id", "left")
            ->join("staff_master smc", "smc.id=snc.penalty_assigned_staff_id", "left")
            ->join("student_admission std", "std.id = snc.std_admission_id", "left")
            ->join("student_year sy", "snc.std_year_id=sy.id", "left")
            ->order_by('cs.class_name, cs.section_name,'.$order_by);

        if ($penalty_status != 'all')
            $this->db_readonly->where("snc.penalty_status", $penalty_status);
        if ($show_all != 1)
            $this->db_readonly->where("snc.penalty_assigned_staff_id", $this->authorization->getAvatarStakeHolderId());
        if($acad_year_id){
            $this->db_readonly->where('snc.acad_year_id',$acad_year_id);
        }
        $nc_data = $this->db_readonly->get()->result();

        return $nc_data;
    }

    private function Kolkata_datetime()
    {
        $timezone = new DateTimeZone("Asia/Kolkata");
        $date = new DateTime();
        $date->setTimezone($timezone);
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function resolve_nc($snc_id, $new_status,$add_grace_served_remarks)
    {
        $update_data = [
            'penalty_status' => $new_status,
            'penalty_served_marked_date' => $this->Kolkata_datetime(),
            'penalty_marked_by' => $this->authorization->getAvatarStakeHolderId(),
            'add_grace_served_remarks' => $add_grace_served_remarks
        ];

        return $this->db->where('id', $snc_id)
            ->update('snc_items', $update_data);
    }

    public function get_studentclassSectionwise($section_id)
    {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
        }else {
          $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'std.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 'std.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'std.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'std.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 'sy.alpha_rollnum';
        }

        $this->db_readonly->select("std.id, $std_name");
        $this->db_readonly->from('student_admission std');
        $this->db_readonly->join('student_year sy', "std.id=sy.student_admission_id");
        $this->db_readonly->where('admission_status', '2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where('sy.class_section_id', $section_id);
        $this->db_readonly->order_by("$order_by");
        return $this->db_readonly->get()->result();
    }

    public function add_multiple_student_nc($class_section_id, $student_id_arr, $snc_category_id, $staff_capacity_id, $remarks, $snc_date,$file_name, $is_apply_penalty_checked, $penalty, $penalty_observer)
    {  
        if (count($student_id_arr) == 0) {
            //Nothing to add
            return 1;
        }


        $sy_obj_arr = $this->db_readonly->select("id as syid, student_admission_id as sa_id")
            ->from('student_year')
            ->where_in("student_admission_id", $student_id_arr)
            ->where("class_section_id", $class_section_id)
            ->where("acad_year_id", $this->yearId)
            ->get()->result();
        
        // echo "<pre>";print_r($this->authorization->getAvatarStakeHolderId()); die();
        foreach ($sy_obj_arr as $sy_obj) {
            $nc_data[] = array(
                'std_admission_id' => $sy_obj->sa_id,
                'std_year_id' => $sy_obj->syid,
                'class_section_id' => $class_section_id,
                'acad_year_id' => $this->yearId,
                'created_staff_id' => $this->authorization->getAvatarStakeHolderId(),
                'staff_capacity_id' => $staff_capacity_id,
                'remarks' => $remarks,
                'snc_date' => $snc_date,
                'snc_category_id' => $snc_category_id,
                'penalty_name' => $is_apply_penalty_checked==1 ? $penalty : "NA",
                'penalty_assigned_staff_id' => $is_apply_penalty_checked==1 ? $penalty_observer : "0",
                'penalty_status' => $is_apply_penalty_checked==1 ? 'penalty' : 'no_penalty',
                'non_compliance_image' => isset($file_name['file_name']) ? $file_name['file_name'] :null
            );
        }
        return $this->db->insert_batch('snc_items', $nc_data);
    }

    public function add_single_student_nc($class_section_id, $student_id, $snc_category_id, $staff_capacity_id, $remarks, $snc_date)
    {
        $category_obj = $this->db_readonly->select("snc.penalty_name, snc.assigned_staff_id as assigned_staff_id")
            ->from('snc_category snc')
            ->where('id', $snc_category_id)
            ->get()->row();

        $sy_obj = $this->db_readonly->select("id as syid")
            ->from('student_year')
            ->where("student_admission_id", $student_id)
            ->where("class_section_id", $class_section_id)
            ->get()->row();

        // echo "<pre>";print_r($this->authorization->getAvatarStakeHolderId()); die();
        $nc_data = array(
            'std_admission_id' => $student_id,
            'std_year_id' => $sy_obj->syid,
            'class_section_id' => $class_section_id,
            'acad_year_id' => $this->yearId,
            'created_staff_id' => $this->authorization->getAvatarStakeHolderId(),
            'staff_capacity_id' => $staff_capacity_id,
            'remarks' => $remarks,
            'snc_date' => $snc_date,
            'snc_category_id' => $snc_category_id,
            'penalty_name' => $category_obj->penalty_name,
            'penalty_assigned_staff_id' => $category_obj->assigned_staff_id,
            'penalty_status' => 'penalty',
        );
        return $this->db->insert('snc_items', $nc_data);
    }

    public function get_student_wise_balance_report($section_id,$acad_year)
    {
    $prefix_student_name = $this->settings->getSetting('prefix_student_name');
	if ($prefix_student_name == "roll_number") {
      $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "enrollment_number") {
      $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "admission_number") {
      $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    } else if ($prefix_student_name == "alpha_rollnum") {
        $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    }else {
      $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
    }

    $prefix_order_by = $this->settings->getSetting('prefix_order_by');
    $order_by = 'sa.first_name';
    if ($prefix_order_by == "roll_number") {
      $order_by = 'sy.roll_no';
    }else if($prefix_order_by == "enrollment_number"){
      $order_by = 'sa.enrollment_number';
    }else if($prefix_order_by == "admission_number"){
      $order_by = 'sa.admission_number';
    }else if($prefix_order_by == "alpha_rollnum"){
      $order_by = 'sy.alpha_rollnum';
    }

        $this->db_readonly->select("sa.id as student_id,  sum(case when si.penalty_status='penalty' then 1 else 0 end)  as balance_nc, $std_name, sy.roll_no, concat(cs.class_name, cs.section_name) as section_name")
            ->from('snc_items si')
            ->join('student_admission sa', 'si.std_admission_id=sa.id')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            // ->where('si.acad_year_id', $this->yearId)
            ->join('class_section cs', 'sy.class_section_id=cs.id')
            ->group_by('si.std_admission_id')
            ->order_by('cs.class_name, cs.section_name,'.$order_by);
        // sum(case when si.penalty_status = 'penalty' then 1 else 0 end) as balance")
        if ($section_id != '-1') {
            $this->db_readonly->where('sy.class_section_id', $section_id);
        }
        if($acad_year){
            $this->db_readonly->where('si.acad_year_id',$acad_year);
        }
        return $this->db_readonly->get()->result();
    }

    public function get_penalties_for_widget(){
        $from_date=$_POST["fromDate"];
        $to_date=$_POST["toDate"];

        $sql="select count(penalty_status) as total_penalties,ifnull(sum(case when penalty_status='penalty' then 1 else 0 end),0) as remaining_penalties
        from snc_items where acad_year_id=$this->yearId";

        $this->db_readonly->select("count(si.penalty_status='penalty') as penalty, date_format(si.created_on,'%d-%b') as created_on")
        ->from('snc_items si')
        ->group_by("date_format(si.created_on,'%d-%m-%Y')")
        ->order_by('si.created_on','desc');
        $this->db_readonly->where('date_format(si.created_on,"%Y-%m-%d") BETWEEN "'.$to_date. '" and "'.$from_date.'"');

            $dateArry = array();         
            $toDate = strtotime($to_date); 
            $fromDate = strtotime($from_date);
            for ($currentDate = $toDate; $currentDate <= $fromDate; $currentDate += (86400)) {                         
            $Store = date('d-M', $currentDate); 
            $dateArry[$Store] = array('penalty'=>"0",'created_on'=>$Store,);
            }
            $transCount = $this->db_readonly->get()->result();
            foreach ($transCount as $key => $val) {
            if (array_key_exists($val->created_on, $dateArry)) {
                $dateArry[$val->created_on] = $val;
            }
            }
            
        $sql=$this->db_readonly->query($sql)->result();

        $sql3["penalty_data"]=$sql;
        $sql3["penalty_trend_data"]=$dateArry;
        return $sql3;
    }

    public function get_counselling_for_widget($from_date, $to_date){
        $trend = "select count(*) as count, counselling_date from student_counselling
        where counselling_date between '$from_date' and '$to_date'
        group by counselling_date";
        $trend_result = $this->db_readonly->query($trend)->result();
        $total = "select count(*) as count from student_counselling
        where counselling_date between '$from_date' and '$to_date'";
        $total_result = $this->db_readonly->query($total)->result();
        $dateArry = array();
        $toDate = strtotime($to_date); 
        $fromDate = strtotime($from_date);
        for ($currentDate = $fromDate; $currentDate <= $toDate; $currentDate += (86400)) {                         
            $Store = date('d-M', $currentDate); 
            $dateArry[$Store] = array('count'=>"0",'created_on'=>$Store,);
        }
        foreach ($trend_result as $key => $val) {
            $counsellingDate = date('d-M', strtotime($val->counselling_date));
            if (array_key_exists($counsellingDate, $dateArry)) {
                $dateArry[$counsellingDate]['count'] = $val->count;
            }
        }
        $result['total'] = $total_result;
        $result['trend'] = $dateArry;
        return $result;
    }

    public function get_observation_for_widget($from_date, $to_date){
        $trend = "select count(*) as count, created_on from student_observation
        where created_on between '$from_date' and '$to_date'
        group by created_on";
        $trend_result = $this->db_readonly->query($trend)->result();
        $total = "select count(*) as count from student_observation
        where created_on between '$from_date' and '$to_date'";
        $total_result = $this->db_readonly->query($total)->result();
        $dateArry = array();
        $toDate = strtotime($to_date); 
        $fromDate = strtotime($from_date);
        for ($currentDate = $fromDate; $currentDate <= $toDate; $currentDate += (86400)) {                         
            $Store = date('d-M', $currentDate); 
            $dateArry[$Store] = array('count'=>"0",'created_on'=>$Store,);
        }
        foreach ($trend_result as $key => $val) {
            $createdDate = date('d-M', strtotime($val->created_on));
            if (array_key_exists($createdDate, $dateArry)) {
                $dateArry[$createdDate]['count'] = $val->count;
            }
        }
        $result['total'] = $total_result;
        $result['trend'] = $dateArry;
        return $result;
    }

    public function get_student_nc_report($students_id,$acad_year)
    {
        $filter_column = 'sy.roll_no';
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "enrollment_number") {
            $filter_column = 'sa.enrollment_number';
        } else if ($prefix_student_name == "admission_number") {
            $filter_column = "sa.admission_no";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $filter_column = "sy.alpha_rollnum";
        }
        $this->db_readonly->select("ifnull(sa.enrollment_number,'-') as enrollment_number,concat(cs.class_name,' ',cs.section_name) as section,
        si.std_admission_id as Roll_no,
        si.snc_date as Date,
        sc.name as nc_category,
        concat(sm.first_name,' ',sm.last_name) as Assigned_by,
        si.penalty_status as Status,
        si.remarks as Remarks")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->join('snc_items si', 'si.std_admission_id=sa.id')
            ->join('snc_category sc', 'sc.id=si.snc_category_id')
            ->join('staff_master sm', 'sm.id=si.penalty_assigned_staff_id')
            ->join('class_section cs', 'cs.id=si.class_section_id')
            ->where('sa.id', $students_id)
            ->where('sy.acad_year_id', $this->yearId);
            if($acad_year){
                $this->db_readonly->where('si.acad_year_id',$acad_year);
            }
            $this->db_readonly->order_by($filter_column);
            $result = $this->db_readonly->get()->result();
            return $result;
                    // 

        // return $this->db_readonly->get()->result();
    }

    public function get_nc_analytics_report()
    {
        // old data model query starts
        // 🔽🔽🔽
        // $this->db_readonly->select("concat(c.class_name,' ',cs.section_name) as Section,SUM(CASE WHEN si.snc_category_id = '1' THEN 1 ELSE 0 END) AS `Late_commers`,
        // SUM(CASE WHEN si.snc_category_id = '2' THEN 1 ELSE 0 END) AS `Missing_stationary`,
        // SUM(CASE WHEN si.snc_category_id = '3' THEN 1 ELSE 0 END) AS `ID_Card`,
        // SUM(CASE WHEN si.snc_category_id = '4' THEN 1 ELSE 0 END) AS `Not_Wearing_Mask`,
        // SUM(CASE WHEN si.snc_category_id = '5' THEN 1 ELSE 0 END) AS `Class_Work`,
        // SUM(CASE WHEN si.snc_category_id = '6' THEN 1 ELSE 0 END) AS `Dress_Code`,
        // SUM(CASE WHEN si.snc_category_id = '7' THEN 1 ELSE 0 END) AS `Using_Lift`,
        // SUM(CASE WHEN si.snc_category_id = '8' THEN 1 ELSE 0 END) AS `Mobile_Phone`,
        // count(si.std_admission_id) as total,
        // sum(case when si.penalty_status = 'penalty' then 1 else 0 end) as balance")
        // ->from('snc_items si')
        // ->join('class_section cs','cs.id=si.class_section_id')
        // ->join('class c','c.id=cs.class_id')
        // ->group_by('concat(c.class_name," ",cs.section_name)');
        // 🔝🔝🔝
        // old data model query ends

        $category_objs = $this->noncompliance_model->get_nc_categories();
        $section_objs = $this->getClassNames();

        $penalty_objs = $this->db_readonly->select("concat(c.class_name,' ',c.section_name) as section, snci.class_section_id,
        snc_category_id, count(snc_category_id) as penalty_total,
        SUM(CASE WHEN snci.penalty_status = 'served' THEN 1 ELSE 0 END) AS `penalty_served`,
        SUM(CASE WHEN snci.penalty_status = 'grace' THEN 1 ELSE 0 END) AS `penalty_grace`,
        SUM(CASE WHEN snci.penalty_status = 'penalty' THEN 1 ELSE 0 END) AS `penalty_remaining`")
            ->from('snc_items snci ')
            ->join('snc_category sncc', 'sncc.id=snci.snc_category_id', 'left')
            ->join('class_section cs', 'cs.id=snci.class_section_id')
            ->join('class c', 'c.id=cs.class_id')
            ->where('snci.acad_year_id', $this->acad_year->getAcadYearId())
            ->group_by('snci.class_section_id')
            ->group_by('snc_category_id')
            ->order_by('class_section_id, snc_category_id')
            ->get()->result();

        // echo '<pre>';print_r($this->db_readonly->last_query());
        // echo '<pre>';print_r($section_objs);
        // echo '<pre>';print_r($penalty_objs);die();

        $final_data = [];
        foreach ($section_objs as $section_obj) {
            foreach ($category_objs as $category_obj) {
                $final_data[$section_obj->id][$category_obj->id] = [];
            }
        }

        foreach ($penalty_objs as $key => $pobj) {
            $final_data[$pobj->class_section_id][$pobj->snc_category_id] = $pobj;
        }

        foreach ($final_data as &$fsec_obj) {
            $penalty_total = 0;
            $penalty_grace = 0;
            $penalty_served = 0;
            $penalty_remaining = 0;
            foreach ($fsec_obj as &$fcat) {
                $penalty_total += (isset($fcat->penalty_total) ? $fcat->penalty_total : 0);
                $penalty_grace +=  (isset($fcat->penalty_grace) ? $fcat->penalty_grace : 0);
                $penalty_served +=  (isset($fcat->penalty_served) ? $fcat->penalty_served : 0);
                $penalty_remaining +=  (isset($fcat->penalty_remaining) ? $fcat->penalty_remaining : 0);
            }
            $aggregate = new stdClass();

            $aggregate->penalty_total = $penalty_total;
            $aggregate->penalty_grace = $penalty_grace;
            $aggregate->penalty_served = $penalty_served;
            $aggregate->penalty_remaining = $penalty_remaining;

            $fsec_obj['aggregate'] = $aggregate;
        }

        // echo '<pre>';print_r($category_objs);
        echo '<pre>';
        print_r($final_data);
        die();
    }

    public function get_student_by_class_section($classSectionId,$admission_status)
    {
        // $filter_column = 'sy.roll_no as roll_no';
        // $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        // if ($prefix_student_name == "enrollment_number") {
        //     $filter_column = 'sa.enrollment_number as roll_no';
        // } else if ($prefix_student_name == "admission_number") {
        //     $filter_column = "sa.admission_no as roll_no";
        // } else if ($prefix_student_name == "alpha_rollnum") {
        //     $filter_column = "sy.alpha_rollnum as roll_no";
        // }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');

        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
            $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
            $order_by = 'sa.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
            $order_by = 'sy.alpha_rollnum';
        }

                
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS student_name";
        }

        

        $this->db_readonly->select("sa.id as std_id,$std_name")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.acad_year_id', $this->yearId)
            ->where('sa.admission_status',$admission_status)
            ->join('class_section cs', 'sy.class_section_id=cs.id')
            ->order_by($order_by);
        if ($classSectionId != '-1') {
            $this->db_readonly->where('cs.id', $classSectionId);
        }
        return $this->db_readonly->get()->result();
    }
    public function get_class_wise_nc_report($section_id, $filter,$mode,$acad_year)
    {   
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_number';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 'sy.alpha_rollnum';
        }

        $this->db_readonly->select("sa.id as student_id, count(si.id) as total_balance_nc, sum(case when si.penalty_status = 'served' then 1 else 0 end) as served, sum(case when si.penalty_status = 'penalty' then 1 else 0 end) as balance, $std_name, concat(cs.class_name, cs.section_name) as section_name,sum(case when si.penalty_status = 'grace' then 1 else 0 end) as grace,sum(case when si.penalty_status = 'no_penalty' then 1 else 0 end) as no_penalty")
            ->from('snc_items si')
            ->join('student_admission sa', 'si.std_admission_id=sa.id')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.acad_year_id', $this->yearId)
            ->join('class_section cs', 'sy.class_section_id=cs.id')
            ->order_by('cs.class_name, cs.section_name,'.$order_by)
            ->group_by('si.std_admission_id');

        if($acad_year){
            $this->db_readonly->where('si.acad_year_id',$acad_year);
        }
        if ($filter == "true") {
            $this->db_readonly->where('si.penalty_status', 'penalty');
        } 
        if ($mode == "section") {
            if ($section_id != '-1') {
                $this->db_readonly->where('sy.class_section_id', $section_id);
            }
        } else {
            if ($section_id != '-1') {
                $this->db_readonly->where('sy.class_id', $section_id);
            }
        }
        return $this->db_readonly->get()->result();
    }

    public function disable_particular_non_compliance($data)
    {
        return $this->db->where("id", $data["non_complaince_id"])->update("snc_items", array("disabled" => 1));
    }

    public function get_nc_analytics($data)
    {
        return $result=$this->db_readonly->select("concat(cs.class_name,'',cs.section_name) as class_section_name,sum(case when (sc.name='Late to Class') then 1 else 0 end) as late,sum(case when (sc.name='Missing Stationary/materials') then 1 else 0 end) as stationary,sum(case when (sc.name='Not wearing ID Card') then 1 else 0 end) as id_card,sum(case when (sc.name='Not wearing Mask') then 1 else 0 end) as mask, sum(case when (sc.name='Delay in Claswork submission') then 1 else 0 end) as claswork,sum(case when (sc.name='Dress code Violation') then 1 else 0 end) as dress_code,sum(case when (sc.name='Using Lift') then 1 else 0 end) as lift,sum(case when (sc.name='Mobile Phone') then 1 else 0 end) as mobile,sum(case when (sc.name='Discipline') then 1 else 0 end) as discipline,cs.id as class_section_id")
        ->from("snc_items si")
        ->join("snc_category sc","sc.id=si.snc_category_id")
        ->join("class_section cs","cs.id=si.class_section_id")
        ->where('si.acad_year_id', $this->yearId)
        ->where('snc_date BETWEEN "' . date('Y-m-d', strtotime($_POST['from_date'])) . '" and "' . date('Y-m-d', strtotime($_POST['to_date'])) . '"')
        ->group_by("si.class_section_id")
        ->get()->result();
    }

    public function get_particular_nc_analytics($data)
    {
        // echo "<pre>"; echo print_r($data);
        $snc_category_id=0;
        $nc_type=$data["nc_type"];

        if($nc_type=="claswork"){
            $snc_category_id='5';
        }else if($nc_type=="discipline"){
            $snc_category_id='9';
        }else if($nc_type=="dress_code"){
            $snc_category_id='6';
        }else if($nc_type=="id_card"){
            $snc_category_id='3';
        }else if($nc_type=="late"){
            $snc_category_id='1';
        }else if($nc_type=="lift"){
            $snc_category_id='7';
        }else if($nc_type=="mask"){
            $snc_category_id='4';
        }else if($nc_type=="mobile"){
            $snc_category_id='8';
        }else if($nc_type=="stationary"){
            $snc_category_id='2';
        }

        return $result=$this->db_readonly->select("sy.roll_no,concat(sa.first_name,' ',ifnull(sa.last_name,'')) as std_name,si.snc_date,si.penalty_status")
            ->from("student_year sy")
            ->join('student_admission sa', 'sa.id=sy.student_admission_id')
            ->join('snc_items si', 'si.std_admission_id=sa.id')
            ->where('si.acad_year_id', $this->yearId)
            ->where('si.snc_date BETWEEN "' . date('Y-m-d', strtotime($data['from_date'])) . '" and "' . date('Y-m-d', strtotime($data['to_date'])) . '"')
            ->where('si.class_section_id', $data['cs_id'])
            ->where('si.snc_category_id', $snc_category_id)
            ->get()->result();
    }

    public function update_combination_status(){
        $id = $_POST['id'];
        $status = $_POST['status'];
        $data = array(
          'status' => $status,
        );
        return $this->db->where('id', $id)->update('snc_category', $data);
      }

    public function get_acad_years(){
        return $this->db->select('distinct(s.acad_year_id),acad_year')
        ->from('snc_items s')
        ->join('academic_year a','s.acad_year_id=a.id')
        ->get()->result();
    }
}
