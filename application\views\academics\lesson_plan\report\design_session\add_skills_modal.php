<div class="modal fade" id="add_skills" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add New Skill</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" class="session_id" name="skills_session_id" id="skills_session_id">
                <div class="form-group">
                    <label for="skill_type">Type <font style="color: red;">*</font></label>
                    <select class="form-control" name="skill_type" id="skill_type">
                        <option value="">Select Skill</option>
                    </select>
                    <div style="position: absolute; right: 25px; top: 19%; transform: translateY(-50%);">
                        <i class="fa fa-caret-down"></i>
                    </div>
                    <span id="skillTypeErr" style="display:none"></span>
                </div>
                <div class="form-group">
                    <label for="skill_desc">Description</label>
                    <textarea class="form-control" id="skill_desc" name="skill_desc" style="height: 11rem;" placeholder="Wanna Describe?"></textarea>
                    <span id="skillDescError" style="display:none"></span>
                </div>
                <div class="form-check form-switch pl-0">
                    <input class="form-check-input" type="checkbox" role="switch" id="visible_skills_to_students">
                    <label class="form-check-label" style="margin-left: 2rem;" for="visible_skills_to_students">Make visible to students</label>
                </div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="addSkill()">Update</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // $("#add_skills").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $.ajax({
        url: '<?php echo site_url("academics/Lesson_plan/get_skills_details"); ?>',
        type: "POST",
        data: {},
        success(data) {
            data = $.parseJSON(data);
            let options = `<option value="">Select Skill</option>`
            data.forEach((item, i) => {
                if (!item.skill_name) return;
                options += `<option value="${item.id}">${item.skill_name}</option>`
            })
            $("#skill_type").html(options);
        }
    })

    $("#skill_type, #skill_desc").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            $("#skillTypeErr, #skillDescError").hide();
            if(!$("#skill_type").val()){
                $("#skillTypeErr").text("Please Select Skill Type").css("color", "red").show();
                return;
            }
            // if(!$("#skill_desc").val()){
            //     $("#skillDescError").text("Please Enter Skill Description").css("color", "red").show();
            //     return;
            // }
            addSkillType();
        }
    })

    function addSkillType() {
        const session_id = $("#skills_session_id").val();
        const skill_id = $("#skill_type").val();
        const skill_description = $("#skill_desc").val();
        const visible_to_students = $("#visible_skills_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/add_new_skill"); ?>',
            type: "POST",
            data: { session_id, skill_id, skill_description, visible_to_students },
            success(data) {
                let parsedData =JSON.parse(data);
                if (parsedData) {
                    $("#add_skills").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        $("#skill_type").val("");
                        $("#skill_desc").val("");
                        getSessionData(session_id);
                        loadSkills();
                        showMainModal();
                    });
                } else {
                    $("#add_skills").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed to save activity",
                        text: "Please try again later!",
                    }).then(() => {
                        $("#add_skills").modal('show');
                    });
                }
            },
            error(err){
                console.log(err);
                $("#add_skills").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed to save activity",
                    text: "Please try again later!",
                }).then(() => {
                    $("#add_skills").modal('show');
                });
            }
        })
    }

    function addSkill() {
        $("#skillTypeErr, #skillDescError").hide();
        if(!$("#skill_type").val()){
            $("#skillTypeErr").text("Please Select Skill Type").css("color", "red").show();
            return;
        }
        // if(!$("#skill_desc").val()){
        //     $("#skillDescError").text("Please Enter Skill Description").css("color", "red").show();
        //     return;
        // }
        addSkillType();
        loadSkills();
    }

</script>
</script>