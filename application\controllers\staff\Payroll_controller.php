<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of student_controller
 *
 * <AUTHOR>
 */
class Payroll_controller extends CI_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('PAYROLL')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->library('filemanager');    
      $this->load->model('staff/Staff_Payroll_Model');
      $this->load->model('payroll_model');
      
    }

    public function index(){
        $site_url = site_url();

      $data['launch_tiles'] = array(
          [
            'title' => 'View Payslips',
            'sub_title' => '',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'staff/Payroll_controller/view_my_payslip',
            'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS')
          ],
          [
            'title' => 'Investment Declaration ',
            'sub_title' => '',
            'icon' => 'svg_icons/feetype.svg',
            'url' => $site_url.'staff/Payroll_controller/income_declaration/',
            'permission' => $this->authorization->isAuthorized('PAYROLL.VIEW_TAX_DECLARATION')
          ]
        );
        $data['launch_tiles'] = checkTilePermissions($data['launch_tiles']);

        // echo "<pre>";print_r($data);die();
        $data['main_content'] = 'staff/payroll/index_view';
        $this->load->view('inc/template', $data);
    }

    public function download_payslip_as_pdf() {
      $staff_id = $this->input->post('staff_id');
      $schedule_id = $this->input->post('schedule_id');
      $file_name_details = $this->payroll_model->get_staff_name_and_payslip_month($staff_id, $schedule_id);
      if(!empty($file_name_details)){
        $file_name = $file_name_details['staff_name'] . ' ' . $file_name_details['schedule_name'] . ' payslip.pdf';
      } else {
        $file_name = 'payslip.pdf';
      }
      $path = $this->Staff_Payroll_Model->get_payslip_pdf_path($staff_id, $schedule_id);
      $url = $this->filemanager->getFilePath($path);
      $data = file_get_contents($url);
      $this->load->helper('download');
      force_download($file_name, $data, TRUE);
    }


    public function view_my_payslip($selected_year_id = null){
      if (!$this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS')) {
        redirect('dashboard', 'refresh');
      }

      $data['financial_year'] = $this->payroll_model->get_financial_year_for_staff();
      if(!empty($data['financial_year'])){
        $current_year = date('Y');
        $current_month = date('n');
        $current_date = date('Y-m-d');
        if (!$selected_year_id) {
            foreach ($data['financial_year'] as $financial_year) {
                $start_date = date('Y-m-d', strtotime($financial_year->from_date));
                $start_year = date('Y', strtotime($start_date));

                if ($current_month >= 4) {
                    if ($current_date >= $start_date && $current_year == $start_year) {
                        $selected_year_id = $financial_year->id;
                        break;
                    }
                } else {
                    if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                        $selected_year_id = $financial_year->id;
                        break;
                    }
                }
            }

            if (!$selected_year_id && !empty($data['financial_year'])) {
                $selected_year_id = $data['financial_year'][0]->id;
            }
        }

        $data['selected_year_id'] = $selected_year_id;
        // echo "<pre>";print_r($data['selected_year_id']);die();
        $data['staff_id'] = $this->authorization->getAvatarStakeHolderId();
        $data['schedules'] = $this->Staff_Payroll_Model->getPayslipSchedules($data['staff_id'], $selected_year_id);
        $data['enable_password'] = $this->settings->getSetting('payroll_require_password_for_staff_payslip_download');
        // echo "<pre>";print_r($data['enable_password']);die();
          if ($this->mobile_detect->isTablet()) {
            $data['main_content'] = 'staff/payroll/payslip_new_tablet';
          }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff/payroll/payslip_new_mobile';
          }else{
            $data['main_content'] = 'staff/payroll/payslip_new';    
          }
      } else {
        $data['main_content'] = 'staff/payroll/payslip_error_page';
      }
      $this->load->view('inc/template', $data);
    }

    public function admin_index() {
      if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
        redirect('dashboard', 'refresh');
      }    
      $data['profileData'] = $this->Staff_Payroll_Model->getProfileData();
      //echo '<pre>';print_r($data['profileData']); 
      $data['main_content']    = 'staff/payslip_generation/admin/index';
      $this->load->view('inc/template', $data);
      }

    public function addPayProfile() {
      if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
        redirect('dashboard', 'refresh');
      }    
      $data['main_content'] = 'staff/payslip_generation/admin/add';
      $this->load->view('inc/template', $data);
    }

    public function submitNewPayProfile() {
      if (!$this->authorization->isAuthorized('PAYROLL.PAYROLL_ADMIN')) {
        redirect('dashboard', 'refresh');
      }    
      $status = (int) $this->Staff_Payroll_Model->submitNewPayProfile();
      if($status){
        $this->session->set_flashdata('flashSuccess', 'Successfully Added Profile Data.');
      } else {
        $this->session->set_flashdata('flashError', 'Something Went Wrong..');
      }
      redirect('staff/payroll_controller/admin_index');
    }

    

    public function getPayslip(){
      $data['school_name'] = $this->Staff_Payroll_Model->getSchoolName();
      $data['payroll_working_days'] = $this->settings->getSetting('payroll_working_days');
      $data['payslip'] = $this->Staff_Payroll_Model->get_payslip_info($_POST['staff_id'], $_POST['schedule_id']);
      // echo "<pre>"; print_r($data['payslip']); die();
      $data['noofleaves'] = $this->Staff_Payroll_Model->get_leaves($_POST['staff_id'], $_POST['schedule_id']);
      $data['staff_id'] = $_POST['staff_id'];
      $data['selecte_month'] = $_POST['schedule_id'];
      $school_name = $this->settings->getSetting('school_short_name');
      if ($school_name == 'manchesterglobal') {
        $data['main_content'] = 'staff/payroll/view_payslip_manchester_staff'; 
        $this->load->view('inc/template', $data);
      }elseif ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/payroll/view_payslip_tablet';
        $this->load->view('inc/template', $data);
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/payroll/view_payslip_mobile';
        $this->load->view('inc/template', $data);
      }else{
        $data['main_content'] = 'staff/payroll/view_payslip'; 
        $this->load->view('inc/template', $data);   	
      }

      
      // $this->load->view('inc/template_fee', $data);
    }

    public function declarations(){
      $data['main_content'] = 'staff/payroll/declarations';
      $this->load->view('inc/template', $data);
    }
    
    public function download_as_pdf($staff_id, $schedule_id)
    {
      $path = $this->Staff_Payroll_Model->getFilePath($staff_id, $schedule_id);
      $url = $this->filemanager->getFilePath($path);
      $data = file_get_contents($url);
      $this->load->helper('download');
      force_download('payslip.pdf', $data, TRUE);
    }

    public function income_declaration($selected_year_id = null) {
      $collectPerkTaxMode = $this->settings->getSetting('payroll_collect_perk_tax_mode');
      $data['collectPerkTaxMode'] = $collectPerkTaxMode;

      $data['financial_year'] = $this->payroll_model->get_financial_year_for_staff();
      // if (empty($selected_year_id)) {
      //   $data['selected_year_id'] = $data['financial_year'][0]->id;
      //   $data['selected_year_string'] =  $data['financial_year'][0]->f_year;
      //   $data['selected_year_from_date'] =  $data['financial_year'][0]->from_date;
      //   $data['selected_year_to_date'] =  $data['financial_year'][0]->to_date;
      //   $selected_year_id = $data['financial_year'][0]->id;
      // } else {
      //   foreach ($data['financial_year'] as $temp_year) {
      //     if ($temp_year->id == $selected_year_id) {
      //       $data['selected_year_id'] = $temp_year->id;
      //       $data['selected_year_string'] = $temp_year->f_year;
      //       $data['selected_year_from_date'] =  $temp_year->from_date;
      //       $data['selected_year_to_date'] =  $temp_year->to_date;
      //       break;
      //     }
      //   }
      // }
      $current_year = date('Y');
      $current_month = date('n');
      $current_date = date('Y-m-d');

      // 👇 Only set default selected year if not passed via URL
      if ($selected_year_id === null) {
          foreach ($data['financial_year'] as $financial_year) {
              $start_date = date('Y-m-d', strtotime($financial_year->from_date));
              $start_year = date('Y', strtotime($start_date));
              $start_month = date('n', strtotime($start_date));

              if ($current_month >= 4) {
                  if ($current_date >= $start_date && $current_year == $start_year) {
                      $selected_year_id = $financial_year->id;
                      $data['selected_year_string'] = $financial_year->f_year;
                      $data['selected_year_from_date'] = $financial_year->from_date;
                      $data['selected_year_to_date'] = $financial_year->to_date;
                      break;
                  }
              } else {
                  if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                      $selected_year_id = $financial_year->id;
                      $data['selected_year_string'] = $financial_year->f_year;
                      $data['selected_year_from_date'] = $financial_year->from_date;
                      $data['selected_year_to_date'] = $financial_year->to_date;
                      break;
                  }
              }
          }
      }

      // If still null, fallback to first available year
      if ($selected_year_id === null && !empty($data['financial_year'])) {
          $selected_year_id = $data['financial_year'][0]->id;
          $data['selected_year_string'] = $data['financial_year'][0]->f_year;
          $data['selected_year_from_date'] = $data['financial_year'][0]->from_date;
          $data['selected_year_to_date'] = $data['financial_year'][0]->to_date;
      } else {
          // 🔁 Set from selected year ID passed
          foreach ($data['financial_year'] as $financial_year) {
              if ($financial_year->id == $selected_year_id) {
                  $data['selected_year_string'] = $financial_year->f_year;
                  $data['selected_year_from_date'] = $financial_year->from_date;
                  $data['selected_year_to_date'] = $financial_year->to_date;
                  break;
              }
          }
      }

      $data['selected_year_id'] = $selected_year_id;

      $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
      $data['tax_declaration_window_status'] = $this->Staff_Payroll_Model->income_permission_check($data['staffid'], $data['selected_year_id']);
      $data['call_from'] = 'staff';
      if (empty($data['tax_declaration_window_status']->declaration_open_status)) {
        //Declaration not open
        $data['main_content'] = 'management/payroll/income_declaration/page1/declaration_not_open';
      } else if ($data['tax_declaration_window_status']->declaration_open_status == 'closed' && $data['tax_declaration_window_status']->staff_submit_status == 'Open') {
          //Show as you missed the bus
          $data['main_content'] = 'management/payroll/income_declaration/page1/declaration_closed';
      } else {
        //Window is open (Show for open or submitted)
        //Check if the salary record exists for the staff
        $data['check_sal_record'] = $this->payroll_model->check_salary_record($data['staffid']);
        if ($data['check_sal_record'] == -1) {
          $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
        }
        $data['reopenForProofSubmission'] = $this->payroll_model->reopenForProofSubmission($data['staffid'], $data['selected_year_id']);
        if ($data['check_sal_record'] != -1 || $data['reopenForProofSubmission'] == 'Reopened') {
          $data['staff'] = $this->payroll_model->getStaffname($data['staffid']);
          $data['staff_details'] = $this->payroll_model->getper_staff_income_details($data['staffid'], $selected_year_id);
          if ($data['check_sal_record'] == -1) {
            $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
          } else if(isset($data['staff_details']->error) && $data['staff_details']->error == -4) {
            $data['tax_calc_error'] = $data['staff_details']->error;
            $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
          } else if(isset($data['staff_details']->error) && $data['staff_details']->error == -5) {
            $data['tax_calc_error'] = $data['staff_details']->error;
            $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
          } else {
            $data['staff_details_house'] = $this->payroll_model->getper_staff_income_house_details($data['staffid'], $selected_year_id);
            $staff_proof_attachments = $this->payroll_model->getper_staff_proof_attachments($data['staffid'], $selected_year_id);
            $attachments = [];
            $anyRejectedProof = 0;
            if(!empty($staff_proof_attachments)){
              foreach($staff_proof_attachments as $file){
                $file->proof_file_url = $this->filemanager->getFilePath($file->proof_file_url);
                $attachments[$file->column_name] = $file;
                if($file->status == 'Rejected'){
                  $anyRejectedProof = 1;
                }
              }
            }
            $data['anyRejectedProof'] = $anyRejectedProof;
            $data['attachments'] = $attachments;
            $data['staff_proof_attachments'] = $staff_proof_attachments;
            $data['staff_months_in_year'] = $this->payroll_model->_get_staff_months($data['staff']->doj, $data['selected_year_from_date'], $data['selected_year_to_date']);
            $data['pf_epf'] = $this->payroll_model->getStaffpf_epf($data['staffid'], $data['staff_months_in_year']);
            $data['availing_company_accommodation'] = $data['staff_details']->availing_company_accommodation;
            $c80_close = ($data['staff_details']->public_provident_fund == 0) && ($data['staff_details']->nsc_investment == 0) && ($data['staff_details']->tax_saving_fixed_deposit == 0) && ($data['staff_details']->elss_mutual_fund == 0) && ($data['staff_details']->life_insurance == 0) && ($data['staff_details']->new_pension_scheme == 0) && ($data['staff_details']->pension_plan_for_insurance == 0) && ($data['staff_details']->principal_repayment_house_loan == 0) && ($data['staff_details']->sukanya_samriddhi_yojana == 0) && ($data['staff_details']->stamp_duty_registration_fees == 0) && ($data['staff_details']->tution_fees_for_children == 0) && ($data['staff_details']->other_80c_investments);
            $data['c80_open'] = !$c80_close;
      
            $c80_others_close = ($data['staff_details']->additional_deducation_for_nps == 0);
            $data['c80_others_open'] = !$c80_others_close;
      
            $d80_close = ($data['staff_details']->medical_insurance_premium_self_80d == 0) && ($data['staff_details']->medical_insurance_premium_parent_80d == 0) &&  ($data['staff_details']->preventive_health_checkup_80d == 0) && ($data['staff_details']->preventive_health_checkup_parents_80d == 0) && ($data['staff_details']->medical_insurance_premium_parent_80d_senior == 0) && ($data['staff_details']->medical_insurance_premium_self_80d_senior == 0) && ($data['staff_details']->medical_bills_for_self_senior == 0) && ($data['staff_details']->medical_bills_for_parents_senior == 0);
            $data['d80_open'] = !$d80_close;
  
            $other_investment_exemptions = ($data['staff_details']->interest_paid_education_80e == 0)  && ($data['staff_details']->medical_treatment_dependent_handicapped_80dd == 0) && ($data['staff_details']->medical_treatment_dependent_handicapped_servere_80dd == 0) && ($data['staff_details']->expenditure_medical_tretment_self_dependent_80ddb == 0) && ($data['staff_details']->expenditure_medical_tretment_self_dependent_80ddb_senior == 0) && ($data['staff_details']->donation_approved_funds_80ggc == 0) && ($data['staff_details']->rent_paid_no_hra_recived_80gg == 0) && ($data['staff_details']->physically_disabled_person_80u == 0) && ($data['staff_details']->b_senior_citizens_80tta == 0)  && ($data['staff_details']->donation_approved_funds_80ggc_fifty == 0) && ($data['staff_details']->physically_disabled_person_80u_severe == 0);
            $data['other_investment_exemptions'] = !$other_investment_exemptions;
            
            $others_close = ($data['staff_details']->other_employer_tds == 0) && ($data['staff_details']->other_employer_income == 0);
            $data['others_open'] = !$others_close;
  
            $_under_section_twenty_four = ($data['staff_details']->interest_paid_on_home_loan == 0);
            $data['_under_section_twenty_four'] = !$_under_section_twenty_four;
  
            $leave_travel_allowance = ($data['staff_details']->leave_travel_allowance == 0);
            $data['leave_travel_allowance'] = !$leave_travel_allowance;
  
            $data['leave_travel_allowance_limit'] = $data['staff_details']->lta_limit;
  
            $tax_calcs = $this->payroll_model->getincome_declaration($data['staffid'], $data['selected_year_id']);
            if ((isset($tax_calcs->error)) && ($tax_calcs->error == -4 || $tax_calcs->error == -5)) {
              $data['tax_calc_error'] = $tax_calcs->error;
            } else {
              //If error returned from tax_calcs, do not calculate any predication
              $data['or_approx_tds'] = $tax_calcs['or_tax_amt'];
              $data['nr_approx_tds'] = $tax_calcs['nr_tax_amt'];
  
              $data['previous_ctc_with_employee_pf'] = $tax_calcs['previous_ctc_with_employee_pf'];
              $data['previous_basic_salary_with_da'] = $tax_calcs['previous_basic_salary_with_da'];
              $data['previous_hra'] = $tax_calcs['previous_hra'];
              $data['previous_professional_tax'] = $tax_calcs['previous_professional_tax'];
              $data['previous_outside_ctc_allowance'] = $tax_calcs['previous_outside_ctc_allowance'];
              $data['previous_vpf'] = $tax_calcs['previous_vpf'];
              $data['previous_employee_pf_contribution'] = $tax_calcs['previous_employee_pf_contribution'];
              $data['staff_months_in_year'] = $tax_calcs['staff_months_in_year'];
              $tax_calc_max = $this->payroll_model->getincome_declaration_with_max_investment($data['staffid'], $data['selected_year_id']);
              $data['or_approx_tds_max'] = $tax_calc_max['or_tax_amt'];
            }
            $data['main_content'] = 'management/payroll/income_declaration/page1/index';
          }
        }
      }
      // echo "<pre>";print_r($data);die();
      $this->load->view('inc/template', $data);
    }

    public function income_tax_calculation($selected_financial_year_id, $staff_id, $type = '', $call_from = ''){
        $data['financial_year'] = $this->payroll_model->get_financial_year_for_staff();
        // echo "<pre>";print_r($data['financial_year']);die();
        $data['staffName'] = $this->payroll_model->getStaffname($staff_id)->staff_name;
        // $data['staffid'] = $this->authorization->getAvatarStakeHolderId();
        // $data['regime'] = $this->payroll_model->staff_selected_regime();
        $data['previous_selected_regime'] = $this->payroll_model->staff_previous_selected_regime($staff_id, $selected_financial_year_id);
        $data['staffid'] = $staff_id;
        $data['selected_financial_year_id'] = $selected_financial_year_id;
        $data['type'] = $type == '_' ? '' : $type;
        $data['call_from'] = $call_from;
        $collectPerkTaxMode = $this->settings->getSetting('payroll_collect_perk_tax_mode');
        $data['collectPerkTaxMode'] = $collectPerkTaxMode;
        $data['main_content'] = 'management/payroll/income_declaration/page2/index';
        $this->load->view('inc/template', $data);
    }

    public function save_incometax_decalaration(){
      $financial_year_id = $this->input->post('financial_year_id');
      $staff_id = $this->input->post('staff_id');
      $call_from = $this->input->post('call_from');
      $old_value = $this->input->post('old_value');
      $new_value = $this->input->post('new_value');
      $source = $this->input->post('source');
      if(!empty($old_value) && !empty($new_value)){
        $this->payroll_model->store_payroll_edit_history($staff_id,$old_value,$new_value, $source);
      }
      $result = $this->payroll_model->save_incometax_declaration();
      if($_POST['reopenForProofStatus'] == 'Reopened'){
        $result = $this->payroll_model->getincome_declaration($staff_id, $financial_year_id);
        if(!empty($result)){
          $this->save_old_regime_after_proof_submission($result,$staff_id, $financial_year_id);
        }
        redirect("staff/payroll_controller/add_incometax_declaration_proofs/$financial_year_id/$staff_id");
      }
      redirect("staff/payroll_controller/income_tax_calculation/$financial_year_id/$staff_id/_/$call_from");
    }
    
    private function save_old_regime_after_proof_submission($result, $staffId, $financialYearId){
      $or_tax_amt_field = $result['or_tax_amt'];
      $or_taxable_salary_field = $result['or_taxable_salary']; 
      $or_perquisite_income_field = $result['perquisite_income']; 
      $or_total_income_field = $result['total_income']; 
      $or_80c_field = $result['c_80']; 
      $or_80d_field = $result['d_80']; 
      $or_80ccd_field = $result['ccd_80'];
      $or_basic_tax_field = $result['or_basic_tax'];
      $or_cess_field = $result['or_cess'];
      $call_from = 'staff';
      return $this->payroll_model->old_regim_save($or_tax_amt_field, $staffId, $or_taxable_salary_field, $or_total_income_field, $or_80c_field, $or_80d_field, $or_cess_field, $or_80ccd_field, $financialYearId, $or_perquisite_income_field, $or_basic_tax_field, 'Proof Submission', $call_from);
    }

    public function add_incometax_declaration_proofs($selected_financial_year_id, $staff_id){
      $staff_details = $this->payroll_model->getper_staff_income_details($staff_id, $selected_financial_year_id);
      $staff_proof_attachments = $this->payroll_model->getper_staff_proof_attachments($staff_id, $selected_financial_year_id);
      $categories = [
          'Company_Accommodation' => [
            'availing_company_accommodation' => 'Proof of accommodation provided by the company (typically Form 12BB or company declaration)'
          ],

          'Rent' => [
            'grand_total_rent' => 'Rent receipts or lease agreement',
            'land_lord_pan_card' => 'PAN of the landlord (rent exceeds Rs. 1 lakh annually)',
          ],

          '80C_Investments' => [
              'public_provident_fund' => 'PPF passbook or statement showing deposits made',
              'nsc_investment' => 'NSC certificates for the initial investment and accrued interest statement',
              'tax_saving_fixed_deposit' => 'Fixed deposit receipts and bank statements showing the investment',
              'elss_mutual_fund' => 'ELSS mutual fund statement, transaction receipt',
              'life_insurance' => 'Life insurance premium receipts from the insurance provider',
              'other_80c_investments' => 'Documentation of any other eligible investments (e.g., Sukanya Samriddhi account, senior citizen savings scheme)',
              'new_pension_scheme' => 'NPS contribution receipts or statement',
              'pension_plan_for_insurance' => 'Proof of pension plan contributions (policy receipts)',
              'principal_repayment_house_loan' => 'Loan repayment schedule or certificate from the bank showing principal repayment',
              'sukanya_samriddhi_yojana' => 'Account statement or passbook showing investments',
              'stamp_duty_registration_fees' => 'Copy of sale deed and receipt for stamp duty and registration fees',
              'tution_fees_for_children' => 'Fee receipts or challans from the educational institution',
          ],

          '80C_Other_Investments_Exemptions' => [
              'additional_deducation_for_nps' => 'NPS contribution receipts or account statement showing additional contributions',
          ],

          '80D_Investments' => [
              'eightyd_medical_insurance_premium_self' => 'Premium receipts for health insurance',
              'medical_insurance_premium_self_80d_senior' => 'Premium receipts showing senior citizen coverage',
              'preventive_health_checkup_80d' => 'Receipts for preventive health checkups',
              'medical_bills_for_self_senior' => 'Medical bills and receipts for senior citizen expenses',
              'eightyd_medical_insurance_premium_parent' => 'Premium receipts for health insurance covering parents',
              'medical_insurance_premium_parent_80d_senior' => 'Premium receipts showing senior citizen coverage for parents',
              'preventive_health_checkup_parents_80d' => 'Receipts for preventive health checkups for parents',
              'medical_bills_for_parents_senior' => 'Medical bills for senior citizens',
          ],

          'Other_Investments_Exemptions' => [
              'eightydd_medical_treatment_dependent_handicapped' => 'Medical treatment certificate for a disabled dependent',
              'medical_treatment_dependent_handicapped_servere_80dd' => 'Medical treatment certificate for severely disabled dependent',
              'eightyddb_expenditure_medical_tretment_self_dependent' => 'Medical bills and doctor/s prescription',
              'expenditure_medical_tretment_self_dependent_80ddb_senior' => 'Medical bills, doctor prescription, and senior citizen certificates',
              'eightye_interest_paid_education' => 'Loan interest payment certificates or bank statements showing interest paid',
              'eightytta_b_senior_citizens' => 'Bank statements showing interest earned on savings accounts',
              'eightyggc_donation_approved_funds' => 'Donation receipts with the charity registration number',
              'donation_approved_funds_80ggc_fifty' => 'Donation receipts with the charity registration number',
              'eightygg_rent_paid_no_hra_recived' => 'Rent receipts or lease agreement, along with Form 10BA',
              'eightyu_physically_disabled_person' => 'Disability certificate issued by a government hospital',
              'physically_disabled_person_80u_severe' => 'Severe disability certificate issued by a government hospital',
          ],

          'Other_Income' => [
              'other_employer_income' => 'Salary slips or Form 16 for any additional income from the employer',
              'other_employer_tds' => 'TDS certificates or Form 16/16A showing the TDS deduction by the employer',
          ],

          'Home_Loan' => [
            'interest_paid_on_home_loan' => 'Home loan interest certificate issued by the bank or financial institution'
          ],

          'LTA_LTC' => [
            'leave_travel_allowance' => 'Travel bills, ticket copies, and proof of travel expenses'
          ],
      ];

      $alias_to_column_mapping = [
          'medical_insurance_premium_self_80d' => 'eightyd_medical_insurance_premium_self',
          'medical_insurance_premium_parent_80d' => 'eightyd_medical_insurance_premium_parent',
          'interest_paid_education_80e' => 'eightye_interest_paid_education',
          'medical_treatment_dependent_handicapped_80dd' => 'eightydd_medical_treatment_dependent_handicapped',
          'expenditure_medical_tretment_self_dependent_80ddb' => 'eightyddb_expenditure_medical_tretment_self_dependent',
          'donation_approved_funds_80ggc' => 'eightyggc_donation_approved_funds',
          'rent_paid_no_hra_recived_80gg' => 'eightygg_rent_paid_no_hra_recived',
          'physically_disabled_person_80u' => 'eightyu_physically_disabled_person',
          'b_senior_citizens_80tta' => 'eightytta_b_senior_citizens',
      ];

      $age_based_columns = [
          'self' => [
              'below_60' => ['eightyd_medical_insurance_premium_self', 'preventive_health_checkup_80d'],
              'above_60' => [
                  'medical_insurance_premium_self_80d_senior',
                  'preventive_health_checkup_80d',
                  'medical_bills_for_self_senior'
              ],
          ],
          'parent' => [
              'below_60' => ['eightyd_medical_insurance_premium_parent', 'preventive_health_checkup_parents_80d'],
              'above_60' => [
                  'medical_insurance_premium_parent_80d_senior',
                  'preventive_health_checkup_parents_80d',
                  'medical_bills_for_parents_senior'
              ],
          ],
      ];

      $self_age = $staff_details->self_age;
      $parent_age = $staff_details->parents_age;
      
      $selfAgeColumns = $age_based_columns['self'][$self_age];
      $parentAgeColumns = $age_based_columns['parent'][$parent_age];

      $merge80DColumns = array_merge($selfAgeColumns, $parentAgeColumns);
      foreach ($categories as $categoryKey => $categoryData) {
          if ($categoryKey == '80D_Investments') {
              $filteredCategoryData = array_intersect_key($categoryData, array_flip($merge80DColumns));
              $categories[$categoryKey] = $filteredCategoryData;
          }
      }

      $data = [];
      $data['proofDetails'] = [];
      $hideTitleFooter = 0;

      foreach ($categories as $investment_name => $columns) {
        foreach ($columns as $alias => $file_name) {
              $staff_column = array_search($alias, $alias_to_column_mapping);

              $staff_column = $staff_column ?: $alias;

              if (isset($staff_details->{$staff_column})) {
                  $amount = $staff_details->{$staff_column};

                  // Add to proofDetails if amount is greater than 0
                  if (!empty($amount) && $amount > 0 && isset($columns[$alias])) {
                      $data['proofDetails'][$investment_name][$alias] = [
                          'amount' => $alias == 'availing_company_accommodation' ? '' : $amount,
                          'file_name' => $file_name
                      ];
                  }

                  // Handle special case for rent exceeding Rs. 1 lakh
                  if ($alias == 'grand_total_rent' && $amount > 100000) {
                      $data['proofDetails'][$investment_name][$alias] = [
                          'amount' => $amount,
                          'file_name' => 'Rent receipts or lease agreement'
                      ];

                      // Additional proof for landlord PAN
                      $data['proofDetails'][$investment_name]['landlord_pan_card'] = [
                          'amount' => $amount,
                          'file_name' => 'PAN of the landlord if rent exceeds Rs. 1 lakh annually'
                      ];
                  }
              }

              // Set hideTitleFooter to 1 if any data is processed
              $hideTitleFooter = 1;
          }
      }
      $data['hideTitleFooter'] = $hideTitleFooter;
      $data['staff_id'] = $staff_id;
      $data['selected_financial_year_id'] = $selected_financial_year_id;
      $data['staff_details'] = $staff_details;
      if(!empty($staff_proof_attachments)){
        foreach($staff_proof_attachments as $file_url){
          $file_url->proof_file_url = $this->filemanager->getFilePath($file_url->proof_file_url);
        }
      }
      $data['staff_proof_attachments'] = $staff_proof_attachments;
      // echo "<pre>";print_r($data['proofDetails']);die();
      $data['main_content'] = 'management/payroll/income_declaration/page3/index';
      $this->load->view('inc/template', $data);
    }

    public function income_decalaration_cal(){
        $staffid = $_POST['staffid'];
        $financial_year_id = $_POST['financial_year_id'];
        $result = $this->payroll_model->getincome_declaration($staffid, $financial_year_id);
        echo json_encode($result);
    }

    public function new_regim_save(){
        $nr_tax_amt_field = $_POST['nr_tax_amt_field']; 
        $staffid = $_POST['staffid']; 
        $nr_taxable_salary_field = $_POST['nr_taxable_salary_field']; 
        $nr_perquisite_income_field = $_POST['nr_perquisite_income_field']; 
        $nr_80d_field = $_POST['nr_80d_field']; 
        $nr_80c_field = $_POST['nr_80c_field']; 
        $nr_80ccd_field = $_POST['nr_80ccd_field']; 
        $nr_total_income_field = $_POST['nr_total_income_field']; 
        $nr_basic_tax_field = $_POST['nr_basic_tax_field']; 
        $nr_cess_field = $_POST['nr_cess_field'];
        $selected_financial_year_id = $_POST['selected_financial_year_id'];
        $old_value = $this->input->post('old_value');
        $new_value = $this->input->post('new_value');
        $call_from = $this->input->post('call_from');
        $source = $this->input->post('source');
        if(json_decode($old_value)->selected_regime != json_decode($new_value)->selected_regime && !empty($old_value) && !empty($new_value)){
          $this->payroll_model->store_payroll_edit_history($staffid,$old_value,$new_value, $source);
        }
        $result = $this->payroll_model->save_new_regim($nr_tax_amt_field, $staffid, $nr_taxable_salary_field, $nr_80d_field, $nr_80c_field, $nr_total_income_field, $nr_basic_tax_field, $nr_cess_field, $nr_80ccd_field, $selected_financial_year_id, $nr_perquisite_income_field, $call_from);
        echo json_encode($result);
    }

    public function old_regim_save(){
        $or_tax_amt_field = $_POST['or_tax_amt_field']; 
        $staffid = $_POST['staffid']; 
        $or_taxable_salary_field = $_POST['or_taxable_salary_field']; 
        $or_perquisite_income_field = $_POST['or_perquisite_income_field']; 
        $or_total_income_field = $_POST['or_total_income_field']; 
        $or_80c_field = $_POST['or_80c_field']; 
        $or_80d_field = $_POST['or_80d_field']; 
        $or_80ccd_field = $_POST['or_80ccd_field']; 
        $or_basic_tax_field = $_POST['or_basic_tax_field']; 
        $or_cess_field = $_POST['or_cess_field'];
        $selected_financial_year_id = $_POST['selected_financial_year_id'];
        $old_value = $this->input->post('old_value');
        $new_value = $this->input->post('new_value');
        $source = $this->input->post('source');
        $call_from = $this->input->post('call_from');
        if(json_decode($old_value)->selected_regime != json_decode($new_value)->selected_regime  && !empty($old_value) && !empty($new_value)){
          $this->payroll_model->store_payroll_edit_history($staff_id,$old_value,$new_value, $source);
        }
        $result = $this->payroll_model->old_regim_save($or_tax_amt_field, $staffid, $or_taxable_salary_field, $or_total_income_field, $or_80c_field, $or_80d_field, $or_cess_field, $or_80ccd_field, $selected_financial_year_id, $or_perquisite_income_field, $or_basic_tax_field, 'Declaration', $call_from);
        echo json_encode($result);
    }

    public function incometax_payslip_calculation(){
        $data['schedules_list'] = $this->payroll_model->get_schedules_list();
        $data['staff_list'] = $this->payroll_model->get_staff_list();
        // echo "<pre>"; print_r($data['staff_list']); die();
        $data['main_content'] = 'management/payroll/incometax_payslip_calculation';
        $this->load->view('inc/template', $data);
    }

    public function income_decalaration_payslip_cal(){
        $staffid = $_POST['staffid']; 
        $result = $this->payroll_model->getincome_declaration_payslip_cal($staffid);
        echo json_encode($result);
    }

    public function new_regim_save_payslip_calculation(){
        $taxable_sal_save = $_POST['taxable_sal_save']; 
        $staffid = $_POST['staffid']; 
        $schedules = $_POST['schedules']; 
        $total_tax_amount = $_POST['total_tax_amount']; 

        $result = $this->payroll_model->new_regim_save_payslip_calculation($taxable_sal_save, $staffid, $schedules, $total_tax_amount);
        echo json_encode($result);
    }


    public function old_regim_save_payslip_calculation(){
        $taxable_sal_save = $_POST['taxable_sal']; 
        $staffid = $_POST['staffid']; 
        $schedules = $_POST['schedules']; 
        $total_tax_amount = $_POST['total_tax_amount']; 

        $result = $this->payroll_model->old_regim_save_payslip_calculation($taxable_sal_save, $staffid, $schedules, $total_tax_amount);
        echo json_encode($result);
    }

    public function income_permission_check(){
        $staffid = $_POST['staffid']; 
        $schedule_year = $_POST['schedule_year']; 
        $call_from = $_POST['call_from'];
        if($call_from == 'admin'){
          $result = new stdClass();
          $result->staff_submit_status = 'Open';
        } else {
          $result = $this->Staff_Payroll_Model->income_permission_check($staffid, $schedule_year);
        }
        echo json_encode($result);
    }

    public function assign_schedules_advance(){
        $selected_schedule = $_POST['start_schedule']; 
        $result = $this->payroll_model->get_schedules_list();

        $matched_schedules = array();
        foreach ($result as $schedule) {
            if ($selected_schedule <= $schedule->schedule_id) {
                $matched_schedules[] = $schedule;
            }
        }

        echo json_encode($matched_schedules);
    }

    public function delete_renthouse_row(){
      $recordId = $_POST['recordId']; 
      $result = $this->Staff_Payroll_Model->delete_renthouse_row($recordId);
      echo json_encode($result);
    }

    public function add_rented_house_details(){
      $result = $this->payroll_model->add_rented_house_details();
      echo json_encode($result);
    }

    public function update_rented_house_details(){
      $staff_id = $this->input->post('staff_id');
      $old_value = $this->input->post('old_value');
      $new_value = $this->input->post('new_value');
      $source = $this->input->post('source');
      if(!empty($old_value) && !empty($new_value)){
          $this->payroll_model->store_payroll_edit_history($staff_id,$old_value,$new_value, $source);
      }
      $result = $this->payroll_model->update_rented_house_details();
      echo json_encode($result);
    }

    public function delete_rented_house_details(){
      $result = $this->payroll_model->delete_rented_house_details();
      echo json_encode($result);
    }
     
    public function download_payslip_as_pdf_mob($staff_id, $schedule_id) {
      // echo '<pre>'; print_r($staff_id); die();
      // $staff_id = $this->input->post('staff_id');
      // $schedule_id = $this->input->post('schedule_id');
      $path = $this->Staff_Payroll_Model->get_payslip_pdf_path($staff_id, $schedule_id);
      // echo '<pre>'; print_r($path); die();
      if(!empty($path)){
        $file_name_details = $this->payroll_model->get_staff_name_and_payslip_month($staff_id, $schedule_id);
        if(!empty($file_name_details)){
          $file_name = $file_name_details['staff_name'] . ' ' . $file_name_details['schedule_name'] . ' payslip.pdf';
        } else {
          $file_name = 'payslip.pdf';
        }
        $url = $this->filemanager->getFilePath($path);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($file_name, $data, TRUE);
        $this->load->library('user_agent');
		    redirect($this->agent->referrer());
      }
     
    }

    public function validatePassword(){
      $input = $this->input->post();

      $password = $input['password'];
      $response = $this->Staff_Payroll_Model->validatePassword($password);
      echo json_encode($response);
    }

    public function redirect_income_declaration_admin(){
        $staff_id = $this->input->post('staff_id');
        $schedule_year = $this->input->post('schedule_year');
        
        if (!empty($staff_id) && !empty($schedule_year)) {
            $url = site_url('staff/payroll_controller/income_declaration_admin/'.$schedule_year.'/'.$staff_id);
            
            echo json_encode(['url' => $url]);
        } else {
            echo json_encode(['error' => 'Required parameters missing!']);
        }
    }

    public function income_declaration_admin($selected_year_id = '', $staff_id = '') {
      $collectPerkTaxMode = $this->settings->getSetting('payroll_collect_perk_tax_mode');
      $data['collectPerkTaxMode'] = $collectPerkTaxMode;
      $data['financial_year'] = $this->payroll_model->get_financial_year_for_staff();
      $current_year = date('Y');
      $current_month = date('n');
      $current_date = date('Y-m-d');

      if ($selected_year_id == '') {
          foreach ($data['financial_year'] as $financial_year) {
              $start_date = date('Y-m-d', strtotime($financial_year->from_date));
              $start_year = date('Y', strtotime($start_date));
              $start_month = date('n', strtotime($start_date));

              if ($current_month >= 4) {
                  if ($current_date >= $start_date && $current_year == $start_year) {
                      $selected_year_id = $financial_year->id;
                      $data['selected_year_string'] = $financial_year->f_year;
                      $data['selected_year_from_date'] = $financial_year->from_date;
                      $data['selected_year_to_date'] = $financial_year->to_date;
                      break;
                  }
              } else {
                  if ($current_date >= $start_date && ($current_year - 1) == $start_year) {
                      $selected_year_id = $financial_year->id;
                      $data['selected_year_string'] = $financial_year->f_year;
                      $data['selected_year_from_date'] = $financial_year->from_date;
                      $data['selected_year_to_date'] = $financial_year->to_date;
                      break;
                  }
              }
          }
      }

      if ($selected_year_id == '' && !empty($data['financial_year'])) {
          $selected_year_id = $data['financial_year'][0]->id;
          $data['selected_year_string'] = $data['financial_year'][0]->f_year;
          $data['selected_year_from_date'] = $data['financial_year'][0]->from_date;
          $data['selected_year_to_date'] = $data['financial_year'][0]->to_date;
      } else {
          foreach ($data['financial_year'] as $financial_year) {
              if ($financial_year->id == $selected_year_id) {
                  $data['selected_year_string'] = $financial_year->f_year;
                  $data['selected_year_from_date'] = $financial_year->from_date;
                  $data['selected_year_to_date'] = $financial_year->to_date;
                  break;
              }
          }
      }
      $data['selected_year_id'] = $selected_year_id;
      $data['staffid'] = $staff_id;
      $data['tax_declaration_window_status'] = $this->Staff_Payroll_Model->income_permission_check($data['staffid'], $data['selected_year_id']);
      $data['call_from'] = 'admin';
      $data['check_sal_record'] = $this->payroll_model->check_salary_record($data['staffid']);
      if ($data['check_sal_record'] == -1) {
        $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
      }
      $data['reopenForProofSubmission'] = $this->payroll_model->reopenForProofSubmission($data['staffid'], $data['selected_year_id']);
      if ($data['check_sal_record'] != -1 || $data['reopenForProofSubmission'] == 'Reopened') {
        $data['staff'] = $this->payroll_model->getStaffname($data['staffid']);
        $data['staff_details'] = $this->payroll_model->getper_staff_income_details($data['staffid'], $selected_year_id);
        if ($data['check_sal_record'] == -1) {
          $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
        } else if(isset($data['staff_details']->error) && $data['staff_details']->error == -4) {
          $data['tax_calc_error'] = $data['staff_details']->error;
          $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
        } else if(isset($data['staff_details']->error) && $data['staff_details']->error == -5) {
          $data['tax_calc_error'] = $data['staff_details']->error;
          $data['main_content'] = 'management/payroll/income_declaration/payroll_error_display';
        } else {
          $data['staff_details_house'] = $this->payroll_model->getper_staff_income_house_details($data['staffid'], $selected_year_id);
          $staff_proof_attachments = $this->payroll_model->getper_staff_proof_attachments($data['staffid'], $selected_year_id);
          $attachments = [];
          $anyRejectedProof = 0;
          if(!empty($staff_proof_attachments)){
            foreach($staff_proof_attachments as $file){
              $file->proof_file_url = $this->filemanager->getFilePath($file->proof_file_url);
              $attachments[$file->column_name] = $file;
              if($file->status == 'Rejected'){
                $anyRejectedProof = 1;
              }
            }
          }
          $data['anyRejectedProof'] = $anyRejectedProof;
          $data['attachments'] = $attachments;
          $data['staff_proof_attachments'] = $staff_proof_attachments;
          $data['staff_months_in_year'] = $this->payroll_model->_get_staff_months($data['staff']->doj, $data['selected_year_from_date'], $data['selected_year_to_date']);
          $data['pf_epf'] = $this->payroll_model->getStaffpf_epf($data['staffid'], $data['staff_months_in_year']);
          $data['availing_company_accommodation'] = $data['staff_details']->availing_company_accommodation;
          $c80_close = ($data['staff_details']->public_provident_fund == 0) && ($data['staff_details']->nsc_investment == 0) && ($data['staff_details']->tax_saving_fixed_deposit == 0) && ($data['staff_details']->elss_mutual_fund == 0) && ($data['staff_details']->life_insurance == 0) && ($data['staff_details']->new_pension_scheme == 0) && ($data['staff_details']->pension_plan_for_insurance == 0) && ($data['staff_details']->principal_repayment_house_loan == 0) && ($data['staff_details']->sukanya_samriddhi_yojana == 0) && ($data['staff_details']->stamp_duty_registration_fees == 0) && ($data['staff_details']->tution_fees_for_children == 0) && ($data['staff_details']->other_80c_investments);
          $data['c80_open'] = !$c80_close;
    
          $c80_others_close = ($data['staff_details']->additional_deducation_for_nps == 0);
          $data['c80_others_open'] = !$c80_others_close;
    
          $d80_close = ($data['staff_details']->medical_insurance_premium_self_80d == 0) && ($data['staff_details']->medical_insurance_premium_parent_80d == 0) &&  ($data['staff_details']->preventive_health_checkup_80d == 0) && ($data['staff_details']->preventive_health_checkup_parents_80d == 0) && ($data['staff_details']->medical_insurance_premium_parent_80d_senior == 0) && ($data['staff_details']->medical_insurance_premium_self_80d_senior == 0) && ($data['staff_details']->medical_bills_for_self_senior == 0) && ($data['staff_details']->medical_bills_for_parents_senior == 0);
          $data['d80_open'] = !$d80_close;

          $other_investment_exemptions = ($data['staff_details']->interest_paid_education_80e == 0)  && ($data['staff_details']->medical_treatment_dependent_handicapped_80dd == 0) && ($data['staff_details']->medical_treatment_dependent_handicapped_servere_80dd == 0) && ($data['staff_details']->expenditure_medical_tretment_self_dependent_80ddb == 0) && ($data['staff_details']->expenditure_medical_tretment_self_dependent_80ddb_senior == 0) && ($data['staff_details']->donation_approved_funds_80ggc == 0) && ($data['staff_details']->rent_paid_no_hra_recived_80gg == 0) && ($data['staff_details']->physically_disabled_person_80u == 0) && ($data['staff_details']->b_senior_citizens_80tta == 0)  && ($data['staff_details']->donation_approved_funds_80ggc_fifty == 0) && ($data['staff_details']->physically_disabled_person_80u_severe == 0);
          $data['other_investment_exemptions'] = !$other_investment_exemptions;
          
          $others_close = ($data['staff_details']->other_employer_tds == 0) && ($data['staff_details']->other_employer_income == 0);
          $data['others_open'] = !$others_close;

          $_under_section_twenty_four = ($data['staff_details']->interest_paid_on_home_loan == 0);
          $data['_under_section_twenty_four'] = !$_under_section_twenty_four;

          $leave_travel_allowance = ($data['staff_details']->leave_travel_allowance == 0);
          $data['leave_travel_allowance'] = !$leave_travel_allowance;

          $data['leave_travel_allowance_limit'] = $data['staff_details']->lta_limit;

          $tax_calcs = $this->payroll_model->getincome_declaration($data['staffid'], $data['selected_year_id']);
          if ((isset($tax_calcs->error)) && ($tax_calcs->error == -4 || $tax_calcs->error == -5)) {
            $data['tax_calc_error'] = $tax_calcs->error;
          } else {
            $data['or_approx_tds'] = $tax_calcs['or_tax_amt'];
            $data['nr_approx_tds'] = $tax_calcs['nr_tax_amt'];

            $data['previous_ctc_with_employee_pf'] = $tax_calcs['previous_ctc_with_employee_pf'];
            $data['previous_basic_salary_with_da'] = $tax_calcs['previous_basic_salary_with_da'];
            $data['previous_hra'] = $tax_calcs['previous_hra'];
            $data['previous_professional_tax'] = $tax_calcs['previous_professional_tax'];
            $data['previous_outside_ctc_allowance'] = $tax_calcs['previous_outside_ctc_allowance'];
            $data['previous_vpf'] = $tax_calcs['previous_vpf'];
            $data['previous_employee_pf_contribution'] = $tax_calcs['previous_employee_pf_contribution'];
            $data['staff_months_in_year'] = $tax_calcs['staff_months_in_year'];
            $tax_calc_max = $this->payroll_model->getincome_declaration_with_max_investment($data['staffid'], $data['selected_year_id']);
            $data['or_approx_tds_max'] = $tax_calc_max['or_tax_amt'];
          }
          $data['main_content'] = 'management/payroll/income_declaration/page1/index';
        }
      }
      $this->load->view('inc/template', $data);
    }

    public function get_staff_wise_payroll_data_for_agreement(){
      $staff_id = $this->input->post('staff_id');
      $financial_year = $this->input->post('financial_year');
      $result = $this->payroll_model->get_staff_wise_payroll_data($staff_id, $financial_year);
      $schedules = $this->payroll_model->staff_loan_amount_schedule($financial_year);
      $staff_details = $this->payroll_model->get_staff_details_for_agrteement($staff_id);
      $merged_data = [];
      foreach ($result as $id => $data) {
          foreach ($schedules as $schedule) {
              if ($schedule->id == $id) {
                  $data['schedule_name'] = $schedule->schedule_name;
                  $data['schedule_id'] = $schedule->id;
                  $merged_data[] = $data;
                  break;
              }
          }
      }
      $tax_details = $this->payroll_model->getincome_declaration($staff_id, $financial_year);
      $nr_tax_amt = $tax_details['nr_tax_amt'] != null ? $tax_details['nr_tax_amt'] : '0.00' ;
      $or_tax_amt = $tax_details['or_tax_amt'] != null ? $tax_details['or_tax_amt'] : '0.00' ;
      echo json_encode(array('merged_data'=>$merged_data, 'schedules'=>$schedules, 'staff_details'=>$staff_details, 'nr_tax_amt'=>$nr_tax_amt, 'or_tax_amt'=>$or_tax_amt));
    }

    public function tds_agreed_reopen_by_staff(){
      $staff_id = $this->input->post('staff_id');
      $financial_year = $this->input->post('financial_year');
      $status = $this->input->post('status');
      $result = $this->payroll_model->tds_agreed_reopen_by_staff($staff_id, $financial_year, $status);
      if($result && $status == 2){
        $email_details = $this->payroll_model->get_staff_detials_for_email($staff_id);
        $email_template = $this->payroll_model->get_email_template('staff tds reopen request');
        $acad_year_id = $this->settings->getSetting('academic_year_id');

        if (!empty($email_template) && !empty(trim($email_template->members_email))) {
          $email_content = $email_template->content;
          $subject = $email_template->email_subject;
          $email_content = str_replace('%%staff_name%%', $email_details['from_details']->staff_name, $email_content);
          $email_content = str_replace('%%employee_code%%', $email_details['from_details']->employee_code, $email_content);
          $body = $email_content;
          $member_email = [];
          $member_email_array = explode(',', $email_template->members_email);
          foreach ($member_email_array as $email) {
            $member_email[] = trim($email);
          }
          $this->load->model('Birthday_Notifications_Model');
          $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
          $email_data = [];
          if(!empty($members_data)){
            foreach ($members_data as $key => $val) {
                if(empty($val->stf_email))
                    continue;

                $email_obj = new stdClass();
                $email_obj->stakeholder_id = $val->staff_id;
                $email_obj->avatar_type = $val->avatar_type;
                $email_obj->email = $val->stf_email;
                $email_data[] = $email_obj;
            }
          }

          $email_master_data = array(
            'subject' => $subject,
            'body' => $body,
            'source' => 'Reopen of TDS Declaration Form Email',
            'sent_by' => $email_details['from_details']->staff_id,
            'recievers' => "Payroll Team",
            'from_email' => $email_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);

          if(!empty($email_data)){
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
          }

          $this->load->helper('email_helper');
          sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
        }
      }
      echo $result;
    }

    public function view_additional_allowance_break_down(){
      $input = $this->input->post();
      $result = $this->payroll_model->view_additional_allowance_break_down($input);
      echo json_encode($result);
    }

    public function noInvestmentSubmit(){
      $input = $this->input->post();
      $staffId = $input['staffId'];
      $financialYear = $input['financialYear'];
      $result = $this->Staff_Payroll_Model->noInvestmentSubmit($staffId, $financialYear);
      if($result){
        $this->sendFinalProofSubmissionEmail($staffId, $financialYear, 'Without Investments');
      }
      echo json_encode($result);
    }

    public function addStaffInvestmentProofFile(){
      $input = $this->input->post();
      $columnName = $this->input->post('column_name');
      $staffId = $this->input->post('staffId');
      $financialYear = $this->input->post('financialYear');
      $proofFile = $_FILES['proofFile'];
      if(!empty($proofFile)){
        $url = $this->s3FileUpload($_FILES['proofFile'], 'tax_declaration_proof_attachments');
      } else {
        echo json_encode(0);
        return;
      }
      if($url['status'] != 'success'){
        echo json_encode(0);
        return;
      }
      if($proofFile['name'] == ''){
        $proofFile['name'] = $columnName;
      }
      $result = $this->Staff_Payroll_Model->addStaffInvestmentProofFile($staffId, $financialYear, $columnName, $url, $proofFile['name']);
      if(!empty($result->proof_file_url)){
        $result->proof_file_url = $this->filemanager->getFilePath($result->proof_file_url);
      }
      echo json_encode($result);
    }

    private function s3FileUpload($file, $folder_name = 'tax_declaration_proof_attachments'){
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function removeStaffInvestmentProofFile(){
      $input = $this->input->post();
      $rowId = $this->input->post('rowId');
      $staffId = $this->input->post('staffId');
      $financialYear = $this->input->post('financialYear');
      $result = $this->Staff_Payroll_Model->removeStaffInvestmentProofFile($staffId, $financialYear, $rowId);
      echo json_encode($result);
    }

    public function finalProofSubmit(){
      $input = $this->input->post();
      $staffId = $input['staffId'];
      $financialYear = $input['financialYear'];
      $result = $this->Staff_Payroll_Model->finalProofSubmit($staffId, $financialYear);
      if($result == 1){
        $this->sendFinalProofSubmissionEmail($staffId, $financialYear, 'With Investments');
      }
      echo json_encode($result);
    }

    private function sendFinalProofSubmissionEmail($staffId, $financialYear, $type){
      $financial_year_details = $this->payroll_model->get_financial_year_details($financialYear);
      $email_details = $this->payroll_model->get_staff_detials_for_email($staffId);
      $email_template = [];
      if($type == 'With Investments'){
        $email_template = $this->payroll_model->get_email_template('payroll staff investment proof final submission');
      } else if($type == 'Without Investments'){
        $email_template = $this->payroll_model->get_email_template('payroll staff no investment proof final submission');
      }
      $acad_year_id = $this->settings->getSetting('academic_year_id');

      if (!empty($email_template) && !empty(trim($email_template->members_email))) {
        $email_content = $email_template->content;
        $subject = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_template->email_subject);
        $email_content = str_replace('%%staff_name%%', $email_details['from_details']->staff_name, $email_content);
        $email_content = str_replace('%%employee_code%%', $email_details['from_details']->employee_code, $email_content);
        $email_content = str_replace('%%financial_year%%', $financial_year_details->f_year, $email_content);
        $body = $email_content;
        $member_email = [];
        $member_email_array = explode(',', $email_template->members_email);
        foreach ($member_email_array as $email) {
            $member_email[] = trim($email);
        }
        $this->load->model('Birthday_Notifications_Model');
        $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
        $email_data = [];
        if(!empty($members_data)){
          foreach ($members_data as $key => $val) {
              if(empty($val->stf_email))
                  continue;
              $email_obj = new stdClass();
              $email_obj->stakeholder_id = $val->staff_id;
              $email_obj->avatar_type = $val->avatar_type;
              $email_obj->email = $val->stf_email;
              $email_data[] = $email_obj;
          }
        }
        $email_master_data = array(
          'subject' => $subject,
          'body' => $body,
          'source' => 'Staff Investment Proof Final Submission',
          'sent_by' => $email_details['from_details']->staff_id,
          'recievers' => "Payroll Team",
          'from_email' => $email_template->registered_email,
          'files' => NULL,
          'acad_year_id' => $acad_year_id,
          'visible' => 1,
          'sender_list' => NULL,
          'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        if(!empty($email_data)){
          $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        }

        $this->load->helper('email_helper');
        sendEmail($body, $subject, $email_master_id, $member_email, $email_template->registered_email, []);
      }
    }
  }
?>