<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/index') ?>">Student Certificate</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/certificates_index') ?>">Manage Certificate Templates</a></li>
    <li>Add/Edit Certificate Template</li>
</ul>
<hr>

<div class="col-md-12">
<form id="cert-form" action="<?php echo site_url('student/Certificates_controller/new_certificate') ?>" class="form-horizontal" data-parsley-validate method="POST" enctype="multipart/form-data">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('student/Certificates_controller/certificates_index/') ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    <?php echo ($isEdit) ? 'Edit ' . $certificate->template_name : 'Create New Certificate Template' ?>
                </h3>   
                </div>
            </div>
        </div>        

        <div class="card-body">
            <div class="col-md-6">
                <?php if ($isEdit) { ?>
                    <input type="hidden" name="certificate_type" value="<?php echo $certificate->type; ?>">
                <?php } ?>
                <div class="form-group">
                    <label class="col-md-4 control-label">Template Name <font color="red">*</font></label>
                    <div class="col-md-7">
                        <input name="name" class="form-control input-md" value="<?php echo ($isEdit) ? $certificate->template_name : ''; ?>" data-parsley-error-message="Cannot be empty" required="">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="sections">Purpose <font color="red">*</font></label>
                    <div class="col-md-7">
                        <input name="purpose" value="<?php echo ($isEdit) ? $certificate->purpose : ''; ?>" class="form-control input-md" data-parsley-error-message="Cannot be empty" required="">
                    </div>
                </div>
              
                <div class="form-group">
                    <div class="col-md-7 control-label">
                        <div id="dvPreview">
                        <?php if ($isEdit) { 
                             ?>
                            <img id="templatepreviewing" name="photograph" style="width: 120px;height: 80px;" src="<?php echo (!empty($certificate->template_background)) ? $this->filemanager->getFilePath($certificate->template_background) : 'https://upload.wikimedia.org/wikipedia/commons/6/65/No-Image-Placeholder.svg'; ?>" />
                            <?php } ?>
                            <?php if(!empty($certificate->template_background)) {?>
                                <img id="templatepreviewing" name="photograph" style="width: 120px;height: 80px;display:none" src="<?php echo  $this->filemanager->getFilePath($certificate->template_background); ?>" />
                            <?php } ?>
                            <span id="percentage_templ_bg_completed" style="font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;">0 %</span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="template">Upload Background Template for Certificate</label>
                    <div class="col-md-7">
                        <input class="form-control" id="tempupload" name="temp_background" type="file" accept="image/*" />
                        <input type="hidden" name="template_background" id="temp_background_url">
                        <span id="templateError" style="color:red;"></span>
                    </div>
                </div>
                <?php $receiptNumberId = isset($certificate) && is_object($certificate) ? $certificate->receipt_number_id : null; ?>
                <div class="form-group">
                    <label class="col-md-4 control-label" for="running_number">Certificate Number series</label>
                    <div class="col-md-7">
                        <select class="form-control" id="running_number" name="running_number"> 
                            <option value="" selected>Select number series</option>
                            <?php foreach($reciept_book as $reciept){ 
                                $select_field=$reciept->id == $receiptNumberId ? "selected" : "";
                                ?>
                                <option <?php echo $select_field ?> value="<?php echo $reciept->id ?>"><?php echo $reciept->infix ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>

                <div class="form-group" id="staff_div">
                        <label class="col-md-4 control-label" for="accessible_by_staff">Accessible by staff</label>
                        <div class="col-md-7">
                            <select data-parsley-errors-container="#status_error" class="form-control select2" placeholder="Select staff" multiple id="accessible_by_staff" name="accessible_by_staff[]">
                                <?php 
                                // Convert accessible staff to an array of IDs for easy lookup
                                $accessible_staff_ids = is_array($accessable_by_staff) ? array_column($accessable_by_staff, 'staff_id') : [];

                                foreach ($staff as $staff_individual) { 
                                    $selected = in_array($staff_individual->id, $accessible_staff_ids) ? 'selected' : '';
                                ?>
                                    <option value="<?php echo $staff_individual->id; ?>" <?php echo $selected; ?>>
                                        <?php echo $staff_individual->staff_name; ?>
                                    </option>
                                <?php } ?>
                            </select>
                            <div id="status_error"></div>
                        </div>
                </div>

                <div class="form-group">
                    <label class="col-md-4 control-label" for="template">Date format</label>
                    <div class="col-md-7">
                    <select name="date_format" id="date_format" class="form-control">
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'd-m-Y') ? 'selected' : ''; ?> value="d-m-Y">19-11-2025 (DD-MM-YYYY)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'd-M-Y') ? 'selected' : ''; ?> value="d-M-Y">19-Nov-2025 (DD-MMM-YYYY)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'M-d-Y') ? 'selected' : ''; ?> value="M-d-Y">Nov-19-2025 (MMM-DD-YYYY)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'Y-M-d') ? 'selected' : ''; ?> value="Y-M-d">2025-Nov-19 (YYYY-MMM-DD)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'Y-m-d') ? 'selected' : ''; ?> value="Y-m-d">2025-11-19 (YYYY-MM-DD)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'm/d/Y') ? 'selected' : ''; ?> value="m/d/Y">11/19/2025 (MM/DD/YYYY)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'd/m/Y') ? 'selected' : ''; ?> value="d/m/Y">19/11/2025 (DD/MM/YYYY)</option>
                        <option <?php echo (isset($certificate->date_format) && $certificate->date_format == 'm-d-Y') ? 'selected' : ''; ?> value="m-d-Y">11-19-2025 (MM-DD-YYYY)</option>
                    </select>
                    </div>
                </div>

                
            </div>

            <div class="col-md-6" style="border:1px solid #ccc;">
                <div class="col-md-6" id="mappingList">
                    <ul class="ClickWordList">
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%school_logo%%')
                        ">School Logo</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%admission_no%%')
                        ">Admission No.</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%student_name%%')
                        ">Student Name</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%academic_year%%')
                        ">Academic Year</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%class%%')
                        ">Class Name</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%gender%%')
                        ">Gender</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%dob%%')
                        ">Date Of Birth</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%reference_number%%')
                        ">Reference Number</a></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="ClickWordList">
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%dob_in_words%%')
                        ">Date Of Birth in Words</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%father_name%%')
                        ">Father Name</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%mother_name%%')
                        ">Mother Name</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%address%%')
                        ">Address</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%student_image%%')
                        ">Student Image</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%category%%')
                        ">Category</a></li>
                        <li><a data-placement='top' data-toggle='tooltip' data-original-title='Click to copy' onclick="copyData('%%caste%%')
                        ">Caste</a></li>
                    </ul>
                </div>
                
            </div>
            
         
            <div class="col-md-12">
                <div class="col-md-6" style="padding: 0px;margin-top:2%;"></div>
                
                <div class="col-md-6" style="padding: 0px;margin-top:2%;">
               
                    <textarea name="html_content" class="summernote" id="txt1">
                    <?php
                    if (!empty($isEdit) || isset($cloning)) {
                        echo $certificate->html_content;
                    }
                     ?>
                   </textarea>
                </div>
            </div>
                <div class="col-md-3" style="padding: 0px;margin-top:2%;"></div>
            </center>
        </div>
        <center>
            <div class="card-footer panel_footer_new">
                <?php if ($isEdit) { ?>
                    <input type="button" value="Update and View" id="update" class="btn btn-primary">
                    <a href="<?php echo site_url('student/Certificates_controller/certificates_index')?>" class='btn btn-warning mrg'>Back</a>
                <?php } else { ?>
                    <input type="submit" id="creat_new" value="Create Template"  class="btn btn-primary">
                    <a href="<?php echo site_url('student/Certificates_controller/certificates_index') ?>" class='btn btn-warning mrg'>Back</a>
                <?php } ?>
            </div>
        </center>
    </div>
</form>
</div>



<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/summernote/summernote.js"></script>
<style type="text/css">
    button[data-name=resizedDataImage] {
        position: relative;
        overflow: hidden;
    }

    button[data-name=resizedDataImage] input {
        position: absolute;
        top: 0;
        right: 0;
        margin: 0;
        opacity: 0;
        font-size: 200px;
        max-width: 100%;
        -ms-filter: 'alpha(opacity=0)';
        direction: ltr;
        cursor: pointer;
    }

    .ClickWordList {
        list-style-type: none;
    }

    .ClickWordList li {
        padding: 3px;
        /*border-bottom: 1px solid #ccc;*/
        cursor: pointer;
    }

    .ClickWordList li a {
        color: #000;
    }
</style>

<script type="text/javascript">

    $(document).ready(function() {
        $('#accessible_by_staff').select2({
                width: '100%',
                placeholder: 'Select staff'
        });

        $(document).on('click', function(e) {
            if (!$(e.target).closest('#staff_div').length) {
                $('#accessible_by_staff').select2('close');
            }
        });
    });
    function copyData(map) {
        var $temp = $("<input>");
        $("#mappingList").append($temp);
        $temp.val(map).select();
        document.execCommand("copy");
        $temp.remove();
    }

    $("#update").click(function() {
        var certificateId = "<?php echo empty($certificate) ? 0 : $certificate->id; ?>";
        var action = "<?php echo site_url('student/Certificates_controller/update_certificate/') ?>" + certificateId;
        $("#cert-form").attr('action', action);
        $("#cert-form").submit();
    });

    $('#tempupload').change(function() {
    var src = $(this).val();

    if (src && validatetemplatebackground(this.files[0], 'tempupload')) {

        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0]);
        $('#templatepreviewing').css('opacity', '0.3');
        $("#templateError").html("");
        readURL(this);
    } else {
        this.value = null;
    }
});

function validatetemplatebackground(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId + "Error").html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
			$('#templatepreviewing').show();
            $('#templatepreviewing').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function saveFileToStorage(file) {
    $('#percentage_templ_bg_completed').show();
    $('#tempupload').attr('disabled', 'disabled');
    $("#btnSubmit").prop('disabled', true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'public'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    $('#temp_background_url').val(path);
                    $('#percentage_templ_bg_completed').hide();
                    $('#tempupload').removeAttr('disabled');
                    $('#templatepreviewing').css('opacity', '1');
                    $("#btnSubmit").prop('disabled', false);
                   
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#percentage_templ_bg_completed").html(`${current_percentage} %`);
    return false;
}
</script>