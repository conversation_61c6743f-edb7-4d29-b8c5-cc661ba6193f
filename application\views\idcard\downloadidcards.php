<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('idcards/Idcards_controller')?>">Id cards</a></li>
    <li>Download Approved ID Cards</li>
</ul>

<!-- DataTables CSS and JS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">

<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('idcards/Idcards_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Download Approved ID Cards
          </h3>
        </div>
      </div>
        <div class="row mt-3 mb-3">
            <div class="col-md-3">
                <label for="template_size" class="form-label">Select Page</label>
                <select class="form-control" id="idcard_page" name="idcard_page">
                    <option value="front">Front Page</option>
                    <option value="back">Back Page</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="template_size" class="form-label">Select Order</label>
                <select class="form-control" id="order" name="order">
                    <option value="">select order</option>
                    <?php foreach($orders_list as $list){ ?>
                    <option value="<?=$list->id ?>"><?= $list->order_name ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="card_orientation" class="form-label">Card Orientation</label>
                <select class="form-control" id="card_orientation" name="card_orientation">
                    <option value="portrait">Portrait</option>
                    <option value="landscape">Landscape</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" onclick="get_idcards()" class="btn btn-primary w-100">Generate</button>
            </div>

        </div>
        <div class="row mt-3 mb-3">
            <div class="col-md-6 d-flex align-items-end href-for-download">
                <a class="btn btn-success" onclick="downloadGeneratedIdCardsPdf()" ><i class="fa fa-file-pdf-o"></i> Download PDF</a></a>
            </div>
        </div>

        <!-- DataTable for ID Cards -->
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="table-responsive" id="id_card_generated_data">

                </div>
            </div>
        </div>
        </div>

            <!-- Alert for messages -->
            <div id="alertContainer" class="alert alert-dismissible fade show mt-3" role="alert" style="display:none;">
                <span id="alertMessage"></span>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div id="cardGrid" class="card-grid"></div>

            <!-- Loading indicator -->
            <div id="loadingIndicator" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%, -50%); z-index:9999; background: rgba(255,255,255,0.8); padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.2);">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <div class="mt-2">Generating PDF, please wait...</div>
                    <div class="progress mt-3" style="height: 10px; width: 200px;">
                        <div id="downloadProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>



    <script>
function get_idcards() {
    var page = $('#idcard_page').val();
    var order_id = $('#order').val();
    var orientation = $('#card_orientation').val();

    if (!order_id) {
        showAlert('error', 'Please select an order first');
        return;
    }

    // Show loading indicator
    $('#loadingIndicator').show();
    $('#downloadProgress').css('width', '30%');

    $.ajax({
        url: '<?php echo site_url("idcards/Idcards_controller/generate_id_cards"); ?>',
        type: 'post',
        data: { page: page, order_id: order_id, orientation: orientation },
        success: function(res) {
            var resData = JSON.parse(res);
            $('#id_card_generated_data').html(constructIdCarddata(resData.data));

            // Initialize DataTable with search only
            if ($.fn.DataTable.isDataTable('#idcardsTable')) {
                $('#idcardsTable').DataTable().destroy();
            }

            $('#idcardsTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'excel'
                ],
                searching: true,
                paging: false,
                info: false
            });

            // Check all checkboxes by default
            $('.check-all').prop('checked', true).trigger('change');

            // Hide loading indicator
            $('#loadingIndicator').hide();
            $('#downloadProgress').css('width', '0%');

            showAlert('success', 'ID cards generated successfully');
        },
        error: function(xhr, status, error) {
            // Hide loading indicator
            $('#loadingIndicator').hide();
            $('#downloadProgress').css('width', '0%');
            showAlert('error', 'Failed to generate ID cards: ' + error);
        }
    });
}

function constructIdCarddata(resData){
    var html = '';
    html += '<div class="mb-3">';
    html += '<div class="form-check">';
    html += '<input class="form-check-input check-all" type="checkbox" id="checkAll" checked>';
    html += '<label class="form-check-label" for="checkAll">Check/Uncheck All</label>';
    html += '</div>';
    html += '</div>';

    html += '<table id="idcardsTable" class="table table-bordered">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';
    html += '<th>Image</th>';
    html += '<th>Select</th>';
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';
    var k = 0;
    for (let index = 0; index < resData.length; index++) {
        html += '<tr>';
        html += '<td>'+(k+1)+'</td>';
        html += '<td><img style="width:120px" src="'+resData[index].data_url+'" ></td>';
        html += '<td><input type="checkbox" class="generatedidcards" id="idCard_'+k+'" value="'+resData[index].data_url+'" checked></td>';
        html += '</tr>';
        k++;
    }
    html += '</tbody>';
    html += '</table>';

    // Add event listener for check all
    setTimeout(function() {
        $('.check-all').on('change', function() {
            $('.generatedidcards').prop('checked', $(this).prop('checked'));
        });

        $('.generatedidcards').on('change', function() {
            if ($('.generatedidcards:checked').length === $('.generatedidcards').length) {
                $('.check-all').prop('checked', true);
            } else {
                $('.check-all').prop('checked', false);
            }
        });
    }, 100);

    return html;
}


function downloadGeneratedIdCardsPdf() {
    var dataUrls = [];
    var page = $('#idcard_page').val();
    var order_id = $('#order').val();
    var orientation = $('#card_orientation').val();

    // Correctly select checked checkboxes and get their values
    $('.generatedidcards:checked').each(function() {
        dataUrls.push($(this).val());
    });

    // Check if any checkboxes are selected
    if (dataUrls.length === 0) {
        showAlert('error', 'Please select at least one ID card');
        return;
    }

    // Show loading indicator
    $('#loadingIndicator').show();
    $('#downloadProgress').css('width', '30%');

    $.ajax({
        url: '<?php echo site_url("idcards/Idcards_controller/downloadGeneratedIdCardsPdf"); ?>',
        type: 'post',
        data: {
            image_urls: dataUrls,
            page_type: page,
            order_id: order_id,
            orientation: orientation
        },
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.success) {
                    $('#downloadProgress').css('width', '100%');

                    // Create a download link for the PDF
                    var link = document.createElement('a');
                    link.href = result.pdf_url;
                    link.download = 'idcards.pdf';
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    setTimeout(function() {
                        $('#loadingIndicator').hide();
                        $('#downloadProgress').css('width', '0%');
                        showAlert('success', 'PDF generated and downloaded successfully');
                    }, 1000);
                } else {
                    $('#loadingIndicator').hide();
                    $('#downloadProgress').css('width', '0%');
                    showAlert('error', result.message || 'Failed to generate PDF');
                }
            } catch (e) {
                // If response is not JSON, it's likely a direct file download
                $('#loadingIndicator').hide();
                $('#downloadProgress').css('width', '0%');
                showAlert('success', 'PDF downloaded successfully');
            }
        },
        error: function(xhr, status, error) {
            $('#loadingIndicator').hide();
            $('#downloadProgress').css('width', '0%');
            showAlert('error', 'Failed to generate PDF: ' + error);
        }
    });
}
// Function to show alert messages
function showAlert(type, message) {
    var alertClass = 'alert-info';
    if (type === 'success') alertClass = 'alert-success';
    if (type === 'error') alertClass = 'alert-danger';
    if (type === 'warning') alertClass = 'alert-warning';

    $('#alertContainer')
        .removeClass('alert-success alert-danger alert-warning alert-info')
        .addClass(alertClass)
        .show();
    $('#alertMessage').text(message);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('#alertContainer').fadeOut();
    }, 5000);
}

// Function to show loading overlay
function showLoading(message) {
    // Create loading overlay if it doesn't exist
    if ($('.loading-overlay').length === 0) {
        const loadingHtml = `
            <div class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <div class="mt-2 loading-message">${message}</div>
            </div>
        `;
        $('body').append(loadingHtml);
    } else {
        $('.loading-message').text(message);
        $('.loading-overlay').show();
    }
}

// Function to hide loading overlay
function hideLoading() {
    $('.loading-overlay').hide();
}


</script>




<style>
    #cardGrid {
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* Changed from 3 to 2 items per row */
        gap: 16px;
        padding: 20px;
        width: 210mm; /* A4 width */
        margin: 0 auto;
    }

    .card-item {
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 10px;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        /* Fixed dimensions for ID cards */
        width: calc(100% - 20px);
        box-sizing: border-box;
    }

    .card-item img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }

    /* Portrait ID card size */
    .portrait-card {
        width: 55mm;
        height: 86mm;
        margin: 0 auto;
    }

    /* Landscape ID card size */
    .landscape-card {
        width: 86mm;
        height: 54mm;
        margin: 0 auto;
    }

    /* Print styles for A4 paper */
    @media print {
        @page {
            size: A4;
            margin: 10mm;
        }

        body {
            margin: 0;
            padding: 0;
        }

        #cardGrid {
            width: 100%;
            height: 100%;
            page-break-inside: avoid;
        }

        .card-row {
            page-break-inside: avoid;
            margin-bottom: 10mm;
        }

        .card-item {
            page-break-inside: avoid;
        }
    }

    /* DataTable styles */
    .dataTables_wrapper .dt-buttons {
        margin-bottom: 15px;
    }

    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate,
    .dataTables_wrapper .dataTables_filter {
        display: none;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
</style>


