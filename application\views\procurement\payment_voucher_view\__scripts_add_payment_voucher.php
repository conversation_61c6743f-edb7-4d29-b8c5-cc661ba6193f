
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    function save_basic_details(current, step_selector, step_number) {
        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            let insert_update_type_basic= $("#insert_update_type_basic").val();
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/payment_voucher_controller/submit_voucher_form'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: p_data.message
                            });
                        } else if(p_data.status == '-1') {
                            Swal.fire({
                                icon: 'error',
                                title: p_data.message
                            });
                        } else {
                            if(insert_update_type_basic == 'Add') {
                                __append_invoice_details(p_data.invoice_details);
                                $(".voucher_master_id").val(p_data.voucher_master_id);
                            }
                            $("#insert_update_type_basic").val('update');
                            // $("#voucher_no").val(p_data.derivedInvoiceNumber);
                            // $(".selected_purchase_order_id").val(p_data.purchase_order_id);
                            // $(".selected_vendor_id").val(p_data.vendor_id);
                            $("#step-1").addClass('hidden');
                            $("#step-2").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');
                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
    }

    function __append_invoice_details(details) {
        // alert('llll');
        console.log('details: ', details)
        let tbody= '';
        for(var i in details) {
            // let itemsArr= (details[i].groupedDeliveredItems).split(',');
            // let quantitiesArr= (details[i].groupedDeliveredQuantity).split(',');
            // let unitPriceArr= (details[i].groupedDeliveredUnitPrice).split(',');
            let strings= '';
            let subTotal= 0;
            // for(var index in itemsArr) {
            //     // subTotal += Number(quantitiesArr[index]) * Number(unitPriceArr[index]);
            //     // strings += `${itemsArr[index]} (${quantitiesArr[index]} * ${unitPriceArr[index]})${Number(index) == (itemsArr?.length - 1) ? '' : '<br>'}`;
            // }
            let sum_cgst_rate= Number(details[i].sum_cgst_rate);
            let sum_sgst_rate= Number(details[i].sum_sgst_rate);
            let sum_total_amount= Number(details[i].sum_total_amount);
            let invoice_amount_without_gst_and_with_discount= sum_total_amount - sum_cgst_rate - sum_sgst_rate;
            tbody += `<tr>
                        <td>
                            <input class="invoicesIdsClass" type="checkbox" name="invoicesIdsArr[]" id="invoicesIdsArr${i}" value="${details[i].invoice_id}" />
                        </td>
                        <td>${details[i].invoice_number} (${details[i].invoice_type})</td>
                        <td>${details[i].narration}</td>
                        <td>${details[i].total_amount}</td>
                        <td>
                            <select name="tds_section[]" id="tds_section${i}" class="form-control" onchange="calculate_tds_payble('${i}')">
                                <option value="None">Select Section</option>
                                <option value="194C___Individual Contractor___1">194C	Contractor (individual)	1%</option>
                                <option value="194C___Firm or Company___2">194C	Contractor (firm/company) 2%</option>
                            </select>
                        </td>
                        <td>
                            <input type="hidden" readonly class="form-control" value="${invoice_amount_without_gst_and_with_discount}" name="" id="reference_invoice_amount_without_gst_and_with_discount${i}" />
                            <input readonly type="hidden" class="form-control" value="${sum_total_amount}" name="reference_net_invoice_amount[]" id="reference_net_invoice_amount${i}" />
                            <input readonly type="hidden" class="form-control" value="${(sum_cgst_rate + sum_sgst_rate)}" name="" id="reference_gst_amount${i}" />

                            <input readonly class="form-control" value="0" name="tds_amounts[]" id="tds_amounts${i}" />
                        </td>
                        <td>
                            <input readonly class="form-control" value="${sum_total_amount}" name="net_invoice_amount[]" id="net_invoice_amount${i}" />
                        </td>
                        
                    </tr>`;

                    

        }
        $("#invoices-details-tbody").html(tbody);
        $("#invoices-details-div").show();
    }

    function calculate_tds_payble(index_number) {
        let tds_section = $("select#tds_section" + index_number).val();  
        let reference_invoice_amount_without_gst_and_with_discount = $("#reference_invoice_amount_without_gst_and_with_discount" + index_number).val(); 
        let reference_net_invoice_amount = $("#reference_net_invoice_amount" + index_number).val(); 
        let reference_gst_amount = $("#reference_gst_amount" + index_number).val(); 
        if(tds_section != 'None') {
            let [section, nature, rate] = tds_section.split('___');
            
            let tds_amount= Number(reference_invoice_amount_without_gst_and_with_discount) * (Number(rate) / 100);
            let net_invoice_amount= Number(reference_invoice_amount_without_gst_and_with_discount) - Number(tds_amount) + Number(reference_gst_amount);

            $("#tds_amounts" + index_number).val(tds_amount);
            $("#net_invoice_amount" + index_number).val(net_invoice_amount);
        } else {
            $("#tds_amounts" + index_number).val(0);
            let total= Number(reference_invoice_amount_without_gst_and_with_discount) + Number(reference_gst_amount);
            $("#net_invoice_amount" + index_number).val(total);
        }
        
    }

    function save_invoices_details(current, step_selector, step_number) {

        let current_status= $("#insert_update_type_items").val();
        if(current_status != 'Add') {
            $("#step-2").addClass('hidden');
            $("#step-3").removeClass('hidden');
            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

            return false;
        }

        let isQuantityValid= false;
        $("input.invoicesIdsClass").each(function() {
            if($(this).is(':checked')) {
                isQuantityValid= true;
                return false;
            }
        });

        if(!isQuantityValid) {
            return Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Please select at least one invoice from following!',
            });
        }

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);
            let insert_update_type_items= $("#insert_update_type_items").val();
            $.ajax({
                url: '<?php echo site_url('procurement/payment_voucher_controller/save_invoices_details'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            // __construct_approvers_details(p_data.approvers);
                            if(insert_update_type_items == 'Add') {
                                $(".voucher_master_id").val(p_data.voucher_master_id);
                            }
                            $("#insert_update_type_items").val('Update');
                            $("#step-2").addClass('hidden');
                            $("#step-3").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
    }

    function show_hide_tds_section(show_hide) {
        if(show_hide == 'show') {
            $("#tds-sec-div").show();
        } else {
            $("#tds-sec-div").hide();
        }
    }

    // Document
    function add_additional_notes(current, close_addClose) {
        
        let additional_description_notes= $("#additional_description_notes").val();
        let voucher_master_id= $(".voucher_master_id").val();
        
        const fileInput = document.getElementById('additional_attachements');
        const file = fileInput.files[0];
        const fileInfoDiv = document.getElementById('fileInfo');

        // Check if the file is a PDF
        if (file.type !== 'application/pdf') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Only PDF type files can be uploaded'
            });
            // Clear the file input
            fileInput.value = '';
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }



        $("#attachments-list-empty").hide();
        $("#additional_attachements_div").show();
        $("tr.tr_class_remover").remove();
        $("tbody#additional_attachements_tbody").append(
            `<tr class="tr_class_remover" class="text-center">
                                    <td colspan="3" style="height: 60px; vertical-align: middle; width: 100%; border-right: none;">
                                        <div class="col-md-12" style="width: 100%;"></div>
                                            <div id="show_progress_color" style="height: 20px; background: lightgreen; width: 1%;"><div>
                                        </div>
                                    </td>

                                    <td style="width: 50px; border-left: none;">
                                        <div class="pull-right" id="show_progress_percentage" style="text-align: right; font-weight: bold; color: black; font-weight: bold; font-size: 24px;"><div>
                                    </td>
                            </tr>`
        );

        let fileName1= '';
        let fileSizeFormatted1= '';
        let size= 0;
        let splittedFileType= [];
        
        if (file) {
            const fileName = file.name;
            const fileSizeBytes = file.size;
            const fileExtentionType = file.type;
            size= fileSizeBytes;
            splittedFileType= fileExtentionType.split('/');
            let fileSizeFormatted;
            if (fileSizeBytes < 1024) {
                fileSizeFormatted = fileSizeBytes + ' bytes';
            } else if (fileSizeBytes < 1024 * 1024) {
                fileSizeFormatted = (fileSizeBytes / 1024).toFixed(2) + ' KB';
            } else {
                fileSizeFormatted = (fileSizeBytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
            fileName1= fileName;
            fileSizeFormatted1= fileSizeFormatted;
        } else {
            return Swal.fire({
                icon: 'error',
                title: 'Fill all the necessary fields'
            });
        }
        

        var form = $('#docs_form')[0];
        var formData = new FormData(form);
        formData.append('fileName', fileName1);
        formData.append('fileSizeBytes', size);
        formData.append('fileExtentionType', splittedFileType[1]);
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/add_document'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,

            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                var lastPercent = 0; // Track the last logged percentage
                var interval; // For simulated progress

                // $("#show_attachements_progress_modal").modal('show');
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        var currentPercent = Math.round((evt.loaded / evt.total) * 100);
                        
                        // Clear any previous interval to avoid duplicates
                        if (interval) clearInterval(interval);
                        
                        // Simulate smooth progress between real events
                        interval = setInterval(function() {
                            if (lastPercent < currentPercent) {
                                lastPercent++;
                                $("div#show_progress_percentage, div#show_progress_color_modal").html(lastPercent + '%');
                                $("div#show_progress_color, div#show_progress_percentage_modal").css('width', lastPercent +'%');
                            } else {
                                clearInterval(interval);
                            }
                        }, 50); // Adjust speed (ms) for smoother/faster increments
                    }
                }, false);

                // Ensure 100% is logged when upload completes
                xhr.addEventListener("load", function() {
                    clearInterval(interval); // Stop simulation
                    // $("#show_attachements_progress_modal").modal('hide');
                    if (lastPercent < 100) {
                        $("div#show_progress_percentage, div#show_progress_color_modal").html(100 + '%');
                        $("div#show_progress_color, div#show_progress_percentage_modal").css('width', 100 +'%');
                    }
                });

                return xhr;
            },

            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    if(p_data.status == '1') {
                        var download_url= '<?php echo site_url('procurement/payment_voucher_controller/download_voucher_attachement/'); ?>' + p_data.voucher_attachments_id;

                        $("tbody#additional_attachements_tbody").append(
                            `<tr id="document_tr_id_${p_data.voucher_attachments_id}">
                                    <td>
                                        ${fileName1}
                                    </td>
                                    <td>${fileSizeFormatted1}</td>
                                    <td>${additional_description_notes.trim() == '' ? '-' : additional_description_notes}</td>
                                    <td>
                                    <span style="cursor: pointer;" class="fa fa-times text-danger text-bold" onclick="remove_document('${p_data.voucher_attachments_id}')"></span>
                                    <a type="button" style="cursor: pointer;" class="fa fa-download text-warning text-bold pull-right" href="${download_url}"></a>
                                    </td>
                            </tr>
                            `
                        );
                        $("tr.tr_class_remover").remove();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });

        if(close_addClose == 'close') {
            $("#add_additional_attachements_modal").modal('hide');
        }
        fileInput.value = '';
        fileName.textContent = 'No file selected';
        $("form#docs_form").trigger('reset');
    }

    function get_approver_details(current, step_selector, step_number) {
        $("#step-3").addClass('hidden');
        $("#step-4").removeClass('hidden');

        $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
        $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

        let voucher_master_id= $("input.voucher_master_id").val();
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/get_approver_details'); ?>',
            type: 'post',
            data: {voucher_master_id},
            success: function(payload) {
                payload= JSON.parse(payload);
                ____construct_approvers_details(payload);
            },
            errer: function(bug) {
                console.log(bug);
            }
        });
    }

    function ____construct_approvers_details(approvers) {
        if(!Object.keys(approvers)?.length) {
            $("#approvers_div").html(`
            <center> <img src="<?php echo base_url('assets/illustrations/no_approvers.svg'); ?>" class="img-fluid" style="width: 300px; height: 240px; object-fit: contain; margin-top: 20px;" /> <br><br> <h2>Approvers not found</h2> <p>Your invoice has not been assigned to any approver yet.</p> </center>
            `);
            return;
        }
        var html= `<table class="table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Approver Type</th>
                                <th>Approver</th>
                                <th>Department</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>`;
        if(Object.keys(approvers)?.length) {
            for(var v of approvers) {
                html += `<tr>
                            <td>${v.approval_type}</td>
                            <td>${v.staff}</td>
                            <td>${v.department}</td>
                            <td>${v.approval_status}</td>
                        </tr>`;
            }
        }
        html += ` </tbody>
                </table>`;
        $("#approvers_div").html(html);
    }

    function save_and_close(current, step_selector) {
        let voucher_master_id= $(".voucher_master_id").val();

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, save it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $(current).prop('disabled', true).html('Please Wait...');
                window.location.href= '<?php echo site_url('procurement/payment_voucher_controller/manage_payment_vouchers'); ?>';
            } else {
                $(current).prop('disabled', false).html('Save & Close');
                Swal.fire({
                    icon: 'warning',
                    title: 'Cancelled',
                    text: 'Your voucher is not saved!'
                });
            }
        });
    }

    function remove_document(voucher_attachments_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/remove_document'); ?>',
            type: 'post',
            data: {voucher_attachments_id},
            success: function(data) { 
                $("#document_tr_id_"+voucher_attachments_id).remove();
                var trCounts= $("tbody#additional_attachements_tbody").html();
                if(trCounts.trim() == '') {
                    $("tbody#additional_attachements_tbody").html(`<tr class="tr_class_remover">
                                                                            <td colspan="4" class="text-center">
                                                                            No documents found
                                                                            </td>
                                                                    </tr>`);
                }
            }
        });
       
    }

    function reach_at_prev_tab(current, previous_tab_selector) {
        $(".step-content").addClass('hidden');
        $("#" +previous_tab_selector).removeClass('hidden');
    }
   
   
</script>



<!-- Document Drag & drop -->
<script>
    // Get elements
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('additional_attachements');
    const fileName = document.getElementById('fileName');

    // Handle file selection via browse
    fileInput.addEventListener('change', function(e) {
        if (this.files.length) {
            fileName.textContent = this.files[0].name;
        }
    });

    // Handle drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
</script>




