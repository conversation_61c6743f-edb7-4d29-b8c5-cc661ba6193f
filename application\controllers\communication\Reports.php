<?php

class Reports extends CI_Controller {
  private $yearId;
  private $smsStatusCodes;
	function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
		}
    if(!$this->authorization->isAuthorized('COMMUNICATION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
  	$this->load->model('class_section');
  	$this->load->model('communication/reports_model');

    $this->smsStatusCodes = array(
          'AWAITED-DLR' => 'Awaited delivery',
          'DELIVRD' => 'Delivered',
          'Delivered' => 'Delivered',
          'DNDNUMB' => 'DND number',
          'OPTOUT-REJ' => 'Opt out from subscription',
          'INV-NUMBER' => 'Invalid Number',
          'NO-NUMBER' => 'No Number',
          'INVALID-NUM' => 'Invalid Number',
          'SENDER-ID-NOT-FOUND' => 'SENDER-ID-NOT-FOUND',
          'INV-TEMPLATE-MATCH' => 'Invalid template',
          'MAX-LENGTH' => 'Message length exceeded 100 charactes',
          'NO-CREDITS' => 'No credits',
          'SERIES-BLOCK' => 'Mobile number series blocked',
          'SERIES-BLK' => 'Series blocked by operator',
          'SERVER-ERR' => 'Server error',
          'SPAM' => 'Spam SMS',
          'BLACKLIST' => 'Blacklisted number',
          'BLACKLST' => 'Blacklisted number',
          'TEMPLATE-NOT-FOUND' => 'Template not found',
          'NOT-OPTIN' => 'Not subscribed for opt-in group',
          'TIME-OUT-PROM' => 'Time out for promotional SMS',
          'INVALID-SUB' => 'Number does not exist',
          'ABSENT-SUB' => 'Mobile Subscriber not reachable',
          'HANDSET-ERR' => 'Problem with Handset',
          'BARRED' => 'Message barred by user',
          'NET-ERR' => 'Subscriber’s operator not supported',
          'MEMEXEC' => 'Handset memory full',
          'FAILED' => 'Failed to send',
          'Failed' => 'Failed to send',
          'MOB-OFF' => 'Mobile handset in switched off mode',
          'HANDSET-BUSY' => 'Subscriber is in busy condition',
          'EXPIRED' => 'SMS expired after multiple re-try',
          'REJECTED' => 'SMS Rejected as the number is blacklisted by operator',
          'REJECTD' => 'SMS Rejected as the number is blacklisted by operator',
          'OUTPUT-REJ' => 'Unsubscribed from the group',
          'REJECTED-MULTIPART' => 'Validation fail',
          'UNDELIV' => 'Failed due to network errors',
          'NO-DLR-OPTR' => 'Status not acknowledged',
          '0' => 'Status not acknowledged',
          '' => 'Status not acknowledged',
          'Unknown' => 'Status not acknowledged',
          'SUBMITTED' => 'Submitted',
          'Templaet mis-matched at system level' => 'Invalid template'
    );
	}

  public function index() {
    $data['all_names'] = $this->reports_model->get_staff_student_names();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'communication/reports/reciever_wise_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'communication/reports/reciever_wise_mobile';
    }else{
      $data['main_content'] = 'communication/reports/reciever_wise';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function getRecievedCommunication() {
    $stakeholder_id = $_POST['stakeholder_id'];
    $type = $_POST['type'];
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    /*$from_date = '2020-06-01';
    $to_date = '2020-09-20';*/

    if($type == 'student') {
      $data['details'] = $this->reports_model->getStudentData($stakeholder_id);
      $data['communications'] = $this->reports_model->getStudentCommunications($stakeholder_id, $from_date, $to_date);
    } else {
      $data['details'] = $this->reports_model->getStaffData($stakeholder_id);
      $data['communications'] = $this->reports_model->getStaffCommunication($stakeholder_id, $from_date, $to_date);
    }

    echo json_encode($data);
  }

  public function aggregate_report() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'communication/reports/aggregate_report_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'communication/reports/aggregate_report_mobile';
    }else{
      $data['main_content'] = 'communication/reports/aggregate_report';
    }
    $this->load->view('inc/template', $data);
  }

  public function getAggregateCommunication() {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $data = $this->reports_model->getAggregateCommunication($from_date, $to_date);
    echo json_encode($data);
  }

  public function failed_emails() {
    $data['emails'] = $this->reports_model->getFailedEmails();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'communication/reports/failed_emails_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'communication/reports/failed_emails_mobile';
    }else{
      $data['main_content'] = 'communication/reports/failed_emails';     	
    }
    $this->load->view('inc/template', $data);
  }

}