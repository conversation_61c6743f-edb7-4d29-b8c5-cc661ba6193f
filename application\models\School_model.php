
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class School_model extends CI_Model
{
	private $yearId;
	public function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
	}

	public function get_class_data() {
		$result = $this->db->select("c.id as cid, c.class_name as cname, concat(ifnull(smp.first_name,''), ' ', ifnull(smp.last_name,'')) as principal, concat(ifnull(smco.first_name,''), ' ', ifnull(smco.last_name,'')) as coordinator, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as academic_director")
			->from('class c')
			->join ('staff_master smp', 'c.principal_id=smp.id','left')
			->join ('staff_master smco', 'c.coordinator_id=smco.id','left')
			->join ('staff_master smad', 'c.academic_director_id=smad.id','left')
			->where('acad_year_id', $this->yearId)
			->get()->result();

		return $result;
	}

	// public function get_class_data_by_id($id) {
	// 	$row = $this->db->select("c.id as cid, c.class_name as cname, c.principal_id, c.coordinator_id, c.academic_director_id, concat(ifnull(smp.first_name,''), ' ', ifnull(smp.last_name,'')) as principal, concat(ifnull(smco.first_name,''), ' ', ifnull(smco.last_name,'')) as coordinator, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as academic_director")
	// 		->from('class c')
	// 		->join ('staff_master smp', 'c.principal_id=smp.id','left')
	// 		->join ('staff_master smco', 'c.coordinator_id=smco.id','left')
	// 		->join ('staff_master smad', 'c.academic_director_id=smad.id','left')
	// 		->where('c.id', $id)
	// 		->get()->row();

	// 	return $row;
	// }

	public function get_class_data_by_id($id) {
		$row = $this->db->select("c.id as cid, c.class_name as cname,c.board,c.medium,c.type, c.principal_id, c.coordinator_id, c.academic_director_id, c.admin_id, cs.class_teacher_id,concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as class_teacher, concat(ifnull(smp.first_name,''), ' ', ifnull(smp.last_name,'')) as principal, concat(ifnull(smco.first_name,''), ' ', ifnull(smco.last_name,'')) as coordinator, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as academic_director, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as administrator")
			->from('class c')
			->join ('class_section cs', 'c.id=cs.class_id','left')
			->join ('staff_master sm', 'cs.class_teacher_id=sm.id','left')
			->join ('staff_master smp', 'c.principal_id=smp.id','left')
			->join ('staff_master smco', 'c.coordinator_id=smco.id','left')
			->join ('staff_master smad', 'c.academic_director_id=smad.id','left')
			->join ('staff_master smadin', 'c.admin_id=smadin.id','left')
			->where('c.id', $id)
			->get()->row();

		return $row;
	}

	public function get_class_data_by_classid($classId) {
		$row = $this->db->select("c.id as classId, c.class_name as cname,c.board,c.medium,c.type, c.principal_id, c.coordinator_id,cs.section_name as section_name, c.academic_director_id, c.donot_showin_enquiry,c.admin_id, c.viceprincipal, c.transport_manager, c.facilities_manager,  c.head_of_the_boarding, c.it_support, c.accountant, c.is_placeholder, cs.class_teacher_id, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as class_teacher, concat(ifnull(smp.first_name,''), ' ', ifnull(smp.last_name,'')) as principal, concat(ifnull(smco.first_name,''), ' ', ifnull(smco.last_name,'')) as coordinator, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as academic_director, concat(ifnull(smad.first_name,''), ' ', ifnull(smad.last_name,'')) as administrator, ifnull(c.attendance_type,'') as attendance_type,ifnull(date_format(c.min_dob,'%d-%m-%Y'),'') as min_dob,ifnull(date_format(c.max_dob,'%d-%m-%Y'),'') as max_dob,admission_number_format_id")
			->from('class c')
			->join ('class_section cs', 'c.id=cs.class_id','left')
			->join ('staff_master sm', 'cs.class_teacher_id=sm.id','left')
			->join ('staff_master smp', 'c.principal_id=smp.id','left')
			->join ('staff_master smco', 'c.coordinator_id=smco.id','left')
			->join ('staff_master smad', 'c.academic_director_id=smad.id','left')
			->join ('staff_master smadim', 'c.admin_id=smadim.id','left')
		 	->join ('staff_master vprinci', 'c.viceprincipal=vprinci.id','left')
	      	->join ('staff_master trnsmgr', 'c.transport_manager=trnsmgr.id','left')
	      	->join ('staff_master hotb', 'c.head_of_the_boarding=hotb.id','left')
	      	->join ('staff_master its', 'c.it_support=its.id','left')
	      	->join ('staff_master fmgr', 'c.facilities_manager=fmgr.id','left')
			->join ('staff_master accnt', 'c.accountant=accnt.id','left')
			->where('c.id', $classId)
			->get()->row();

			if($row->min_dob == '01-01-1970'){
				$row->min_dob = '';
			}
			if($row->max_dob == '01-01-1970'){
				$row->max_dob = '';
			}

		return $row;
	}

	public function change_class_name($class_name, $class_id) {
		$data = array('class_name' => $class_name);
		$this->db->where('id', $class_id);
		$this->db->update('class', $data);
		$this->db->where('class_id', $class_id);
		$this->db->update('class_section', $data);
		return true;
	}

	public function change_sec_name($new_sec_name, $sec_id) {
		$this->db->where('id', $sec_id);
		$this->db->update('class_section', array('section_name' => $new_sec_name));
		return true;
	}

	public function get_staff_list() {
		$staff_list = $this->db->select("sm.id, concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as staffName")
			->from('staff_master sm')
			->where('status', '2')
			->order_by('sm.first_name')
			->get()->result();

		return $staff_list;
	}

	public function update_class($id) {
		// echo '<pre>';print_r($this->input->post());die();
		$data = array(
			'principal_id'	=>	$this->input->post('principal'),
			'coordinator_id'	=>	$this->input->post('coordinator'),
			'academic_director_id'	=>	$this->input->post('academic_director')
		);
		$result = $this->db->where('id', $id)->update('class', $data);

		// echo '<pre>';print_r($this->db->last_query());die();

		return $result;
	}
	public function update_sectionv2() {
		$class_teacher=	$this->input->post('eclass_teacher');
		$assistant_class_teacher_1 =	$this->input->post('assistant_class_teacher_1');
		$assistant_class_teacher_2 =	$this->input->post('assistant_class_teacher_2');
		$data = array(
			'class_teacher_id' =>$class_teacher,
			'assistant_class_teacher_id' =>$assistant_class_teacher_1,
			'assistant_class_teacher_2' =>$assistant_class_teacher_2
		);
		$this->db->where('id', $this->input->post('eclass_section'));
		$result = $this->db->update('class_section', $data);
		return $result;
	}
	public function update_classv2($fee_temp_path) {
		// echo '<pre>';print_r($this->input->post());die();
		$data = array(
			'board' => $this->input->post('board'),
			'branch_id' => $this->input->post('branch_id'),
			'medium' => $this->input->post('medium'),
			'type' => $this->input->post('class_type'),
			'principal_id'	=>	$this->input->post('principal'),
			'coordinator_id'	=>	$this->input->post('coordinator'),
			'academic_director_id'	=>	$this->input->post('academic_director'),
			'admin_id'	=>	$this->input->post('administrator'),
			'viceprincipal'	=>	$this->input->post('viceprincipal'),
			'transport_manager'	=>	$this->input->post('transport_manager'),
			'facilities_manager'	=>	$this->input->post('facilities_manager'),
			'head_of_the_boarding'	=>	$this->input->post('head_of_the_boarding'),
			'it_support'	=>	$this->input->post('it_support'),
			'accountant'	=>	$this->input->post('accountant'),
			'attendance_type'	=>	$this->input->post('attendance_type'),
			'is_placeholder' => $this->input->post('placeholder'),
			'admission' => $this->input->post('admission'),
			'donot_showin_enquiry' => $this->input->post('showin_enquiry'),
			'min_dob'=>($this->input->post('edit_cutt_off_dob') == '') ? null : date("Y-m-d", strtotime($this->input->post('edit_cutt_off_dob'))),
			'max_dob'=>($this->input->post('edit_max_cutt_off_dob') == '') ? null : date("Y-m-d", strtotime($this->input->post('edit_max_cutt_off_dob'))),
			'admission_number_format_id'=>$this->input->post('edit_admission_no_format')
		);
		if(!empty($fee_temp_path['file_name'])){
			$data['fee_structure_template'] = $fee_temp_path['file_name'];
		}
		//echo '<pre>';print_r($data);die();
		//$result = $this->db->where('id', $class_id)->update('class', $data);
		$this->db->where('id', $this->input->post('classId'));
		$result = $this->db->update('class', $data);
		//echo '<pre>';print_r($result);die();
		 //echo '<pre>';print_r($this->db->last_query());die();

		return $result;
	}
	public function change_case_of_names($table, $case, $col){
		switch ($case) {
			case 'upper':
				$this->db->set($col, "UPPER(COALESCE(TRIM($col)))", false);
				$this->db->update($table);
				break;
			case 'title':
				$this->db->select('id, ' . $col);
				$query = $this->db->get($table);
				$updateArry =[];
				if ($query->num_rows() > 0) {
					foreach ($query->result_array() as $row) {
						$titleCaseName = null;
						if (!empty($row[$col])) {
							$titleCaseName = ucwords(strtolower(trim($row[$col])));
						}
						$updateArry[] =array(
							'id'=>$row['id'],
							$col=>$titleCaseName,
						);
						// $data = array($col => $titleCaseName);							
					}
					$this->db->update_batch($table, $updateArry,'id');
				}
				break;
			case 'lower':
				$this->db->set($col, "LOWER(COALESCE(TRIM($col)))", false);
				$this->db->update($table);
				break;
		}
	}

	public function get_email_bounce_report() {
		$emailStatus = ['Bounce-failed', 'Complaint-abuse', 'Bounce-', 'Complaint-'];		
		$emailMaster = $this->db_readonly->select('est.email, est.status, COUNT(*) as bounce_count')
			->from('email_master em')
			->join('email_sent_to est', 'em.id = est.email_master_id')
			->where_in('est.status', $emailStatus)
			->where('em.sent_on >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)')
			->where('em.recievers !=', 'Staff')
			->group_by('est.email, est.status')
			->get()
			->result();
		$emails = [];
		foreach ($emailMaster as $value) {
			$emails[] = $value->email;
		}
		if (!empty($emails)) {
			$bouncedEmails = $this->db_readonly->select("CONCAT(sa.first_name, ' ', IFNULL(sa.last_name, '')) as student_name,
				sa.admission_no,
				p.email as parent_email,
				sr.relation_type")
			->from('parent p')
			->join('student_relation sr', 'p.id = sr.relation_id')
			->join('student_admission sa', 'sr.std_id = sa.id')
			->where_in('p.email', $emails)
			->get()
			->result();

			return [
				'email_audit' => $emailMaster,
				'bounced_emails' => $bouncedEmails
			];
		}

		return [
			'email_audit' => $emailMaster,
			'bounced_emails' => []
		];
	}
}
