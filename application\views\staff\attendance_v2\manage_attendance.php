<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Manage Attendance</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Manage Attendance
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="row mx-0">
        <div class="col-md-2">
            <div class="form-group">
              <label class="control-label">Date</label>
              <div class='input-group date' id='datetimepicker'>
                  <input  type='text' id="date" class="form-control" value="<?php echo date('d-m-Y'); ?>" />
                  <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                  </span>
              </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
              <label class="control-label">Staff Type</label>
              <?php //echo '<pre>';print_r($staff_types);die(); ?>
              <select class="form-control" name="selected_staff_type" id="selected_staff_type" onchange="getStaffAttendance()">
                <option value="all">All</option>
                <?php foreach ($staff_types as $key=>$val) {
                  echo "<option value='$key'>$val</option>";
                }
                ?>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <button class="btn btn-primary" type="button" onclick="getStaffAttendance()" style="margin-top:25px;">Get</button>
            </div>
          </div>
      </div>
      <div id="staff-list" class="mt-4">
        <div class="d-flex justify-content-center align-items-center" style="height: 50vh;">
          <i class="fa fa-spinner fa-spin" style="font-size: 4rem"></i>
        </div>
      </div>
    </div>

    <div class="modal fade" id="showLeaveDocModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                <div id="external-files">
                    <iframe src="" frameborder="0" width="500px" height="500px"></iframe>
                </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
                </div>
            </div>
        </div>
  </div>
</div>

<div class="modal fade" id="editAttendance" tabindex="-1" role="dialog" aria-labelledby="editAttendanceLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editAttendanceLabel">Edit Attendance</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form class="">
          <input type="hidden" name="edit_attendance_id" id="edit_attendance_id">
          <input type="hidden" name="edit_staff_id" id="edit_staff_id">
          <input type="hidden" name="edit_staff_name" id="edit_staff_name">
          <input type="hidden" name="old_check_in_date" id="old_check_in_date">
          <input type="hidden" name="old_check_out_date" id="old_check_out_date">
          
          <div class="form-group">
            <label for="check_in_time">Check In Time</label>
            <input class="form-control" type="time" name="new_check_in_time" id="new_check_in_time">
          </div>

          <div class="form-group">
            <label for="check_out_time">Check Out Time</label>
            <input class="form-control" type="time" name="new_check_out_time" id="new_check_out_time">
          </div>

          <div class="form-group">
            <label for="edit_late">Late</label>
            <select class="form-control" name="edit_late" id="edit_late">
              <option id="edit_late_0" value="0">On Time</option>
              <option id="edit_late_1" value="1">Late</option>
            </select>
          </div>

          <div class="form-group">
            <label for="edit_remarks">Remarks</label>
            <textarea name="edit_remarks" id="edit_remarks" class="form-control" placeholder="edit remarks here"></textarea>
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="att_edit_button" onclick="editStaffAttendanceTime()">Save changes</button>
      </div>
    </div>
  </div>
</div>

<?php $mobile = 0;
if($this->mobile_detect->isMobile()){
    $mobile = 1;
} ?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script type="text/javascript">
  var is_mobile = 0;
  const isActiveMultiLevelLeaveApprovalMode="<?php echo $is_three_level_approve_enabled; ?>"
  const leave_v2_year_id="<?php echo $leave_v2_year_id; ?>"
  const isSuperAdmin="<?php echo $this->authorization->isSuperAdmin(); ?>"

  $(document).ready(function(){
    $('#datetimepicker, #date').datepicker({
      format: 'dd-mm-yyyy',
      autoclose: true,
      endDate: new Date()
    }).on('changeDate', function(ev){
      // getStaffAttendance();
    });
    is_mobile = <?php echo $mobile; ?>;
    getStaffAttendance();

    $('#showLeaveDocModal').on('shown.bs.modal', function (e) {
        const {doc_url,name}=e.relatedTarget.dataset;
        $("#exampleModalLabel").text(name);
        $("iframe").attr("src",doc_url);
        $("iframe").css("width","100%");
    })
  });

  function getStaffAttendance() {
    var selected_staff_type = $('#selected_staff_type').val();
    var date = $("#date").val();
    $("#staff-list").html('<div class="d-flex justify-content-center align-items-center" style="height: 50vh;"><i class="fa fa-spinner fa-spin" style="font-size: 4rem"></i></div>');
    $.ajax({
        url: "<?php echo site_url('staff/attendance/getStaffAttendanceByDate'); ?>",
        data: {'date': date, 'staff_type':selected_staff_type},
        type: 'post',
        success: function(data) {
            var staff = JSON.parse(data);
            constructStaffList(staff);
        },
        error: function(err) {
            console.log(err);
            Swal.fire({
                icon: 'error',
                title: 'Failed to get data!!'
            })
        }
    });
  }

  function select_all() {
    if ($("#checkall").is(':checked')) {
        $(".staff-attendance-update-checkbox").prop('checked', true);
    } else {
        $(".staff-attendance-update-checkbox").prop('checked', false);
    }
  }

  function isMeaningfulRemark(remark) {
    // Trim spaces at the beginning and end
    const trimmedRemark = remark.trim();

    // 1. Check if the remark is empty or just spaces
    if (trimmedRemark.length === 0) {
      return false;
    }

    // 2. Check if the remark is too short (e.g., < 3 meaningful characters)
    if (trimmedRemark.length < 3) {
      return false;
    }

    // 3. Check if the remark contains only repeated characters (e.g., "aaa")
    const repeatedChars = /^([a-zA-Z0-9])\1*$/;
    if (repeatedChars.test(trimmedRemark)) {
      return false;
    }

    // 4. Check for gibberish (only symbols or numbers, no words)
    const hasValidWords = /[a-zA-Z]/.test(trimmedRemark);
    if (!hasValidWords) {
      return false;
    }

    // If all validations pass, the remark is considered meaningful
    return true;
  }

  let massUpdateNewAttendanceStatus="P";
  function setStaffAttendanceMassUpdateStatus(newStatus){
    $(`#ma_AB`).removeClass("active");
    $(`#ma_HD`).removeClass("active");
    $(`#ma_P`).removeClass("active");
    $(`#ma_WO`).removeClass("active");
    $(`#ma_H`).removeClass("active");

    $(`#ma_${newStatus}`).addClass("active");

    massUpdateNewAttendanceStatus=newStatus;
  }

  async function massUpdateStaffAttendanceStatus(){
    const massUpdateDate=$("#date").val();
      
    if(massUpdateDate?.length<8){
          return  Swal.fire({
                    icon: "warning",
                    title: "Invalid date",
                    text: "Please enter valid date!",
                  });
        }
        
    // Keeping default status status as 'Present'=> "P";
    setStaffAttendanceMassUpdateStatus("P");
    const allSelectedStaffs=document.querySelectorAll(".staff-attendance-update-checkbox");

    if(typeof allSelectedStaffs!="object"){
      return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "Please select staff(s) to update status!",
          });
    }
    
    if(!allSelectedStaffs?.length){
      return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "Please select staff(s) to update status!",
          });
    }

    let isStaffSelected=false;
    allSelectedStaffs.forEach(s=>{
      if(s.checked){
        isStaffSelected=true;
      }
    });

    if(!isStaffSelected){
      return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "Please select staff(s) to update status!",
          });
    }

    // Get new status and optional update reason

    // Below is the Absent override status button
    // <button id="ma_AB" class="btn btn-secondary absent" onclick="setStaffAttendanceMassUpdateStatus('AB')">AB</button>
    let html=`
        <div class="btn-group btn-group-sm ml-2" style="padding-bottom: 1rem;width: 18rem;">
          <button id="ma_HD" class="btn btn-secondary halfday" onclick="setStaffAttendanceMassUpdateStatus('HD')">HD</button>
          <button id="ma_P" class="btn btn-secondary present active" onclick="setStaffAttendanceMassUpdateStatus('P')">P</button>
          <button id="ma_WO" class="btn btn-secondary weekoff" onclick="setStaffAttendanceMassUpdateStatus('WO')">WO</button>
          <button id="ma_H" class="btn btn-secondary holiday" onclick="setStaffAttendanceMassUpdateStatus('H')">H</button>
        </div>`;

    html+=`
      <textarea class="form-control" placeholder="reason for update..." id="mass_update_reason"></textarea>
    `;

    Swal.fire({
      title: "Change Status",
      html: `${html}`,
      // icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Update Status!"
      }).then(async (result) => {
      if (result.isConfirmed) {
        const massUpdateNewAttendanceReason=$("#mass_update_reason").val();

        // check for valid status
        if(!massUpdateNewAttendanceStatus){
         return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "Invalid update status!",
          }).then(e=>{
            massUpdateStaffAttendanceStatus();
          })
        }


        if(massUpdateNewAttendanceReason?.length){
          // If enterd some reason valiodate it
          const isMeaningful=isMeaningfulRemark(massUpdateNewAttendanceReason);

          if(!isMeaningful){
            // please enter meaningful remarks
            return Swal.fire({
                      icon: "warning",
                      title: "Oops...",
                      text: "Please enter meaningful remarks!",
                      }).then(e=>{
                        massUpdateStaffAttendanceStatus();
                        setStaffAttendanceMassUpdateStatus(massUpdateNewAttendanceStatus);
                    })
          }
        }

        const staffChangeStatusObj={};
        const staffChangeStatusArr=[];
        const newStatus=massUpdateNewAttendanceStatus;
        allSelectedStaffs.forEach(s=>{
          if(s.checked){
            const {staffId, staffShiftId, oldStatus}=s.dataset;
            if(oldStatus!=newStatus){
              staffChangeStatusObj[staffShiftId]={
                staffId,
                staffShiftId,
                oldStatus,
                newStatus,
                reason:massUpdateNewAttendanceReason
              }

              staffChangeStatusArr.push({
                staffId,
                staffShiftId,
                oldStatus,
                newStatus,
                reason:massUpdateNewAttendanceReason
              });
            }
          }
        });

        // send status change request
        const response=await handleMassStaffAttendanceStatus(staffChangeStatusArr,massUpdateDate);
        
        if(response.status=="error"){
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: `${response.message}`,
          });
        }else if(response.status=="warning"){
           Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: `${response.message}`,
          });
        }else if(response.status=="success"){
           // Trigger refresh here
          getStaffAttendance();
          
           Swal.fire({
            icon: "success",
            title: "Status updated",
            text: `${response.message}`,
          });
        }
        
      }
    });
  }

  async function handleMassStaffAttendanceStatus(staffChangeStatusArr,massUpdateDate){
    if(typeof staffChangeStatusArr!="object"){
      return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "Please select staff(s) to update status!",
          });
    }

    if(!Object.keys(staffChangeStatusArr)?.length){
      return Swal.fire({
            icon: "warning",
            title: "Oops...",
            text: "The status you chose is already assigned, Please try assigning different status!",
          });
    }

    let response;
    // chunk into multiple array of objects
    const chunkCount=Math.floor(staffChangeStatusArr.length/150);
    for(let i=0;i<chunkCount;i++){
      const chunk=staffChangeStatusArr.splice(0,150);
      const firstObject={};

      chunk.forEach(a=>{
        if(a.staffShiftId){
          firstObject[a.staffShiftId]=a;
        }
      })

      await $.ajax({
      url:"<?php echo site_url('staff/attendance/update_mass_staff_attendance_status') ?>",
        type: "POST",
        data: { staffChangeStatusObj: firstObject, massUpdateDate },
        success: function (data) {
          response = JSON.parse(data);
        }
      });
    }

    const lastChunk=staffChangeStatusArr.splice(0);
    if(lastChunk?.length){
      const lastObject={};
      
      lastChunk.forEach(a=>{
        if(a.staffShiftId){
          lastObject[a.staffShiftId]=a;
        }
      })
    
      await $.ajax({
        url:"<?php echo site_url('staff/attendance/update_mass_staff_attendance_status') ?>",
        type: "POST",
        data: { staffChangeStatusObj:lastObject, massUpdateDate },
        success: function (data) {
          response = JSON.parse(data);
        }
      });
    }
    return response;
  }

  function constructStaffList(staff) {
    var html = '';
    if(staff.length == 0) {
      html += '<h5>No Data Available</h5>';
    } else {
      html += '<table class="table table-bordered table-striped" id="staff_list_table">';
      html += `<thead>
              <tr>
              <th data-toggle="tooltip" onclick="clearStatusFilters()" data-original-title="Clear Filters" style="width:5%;cursor: pointer;color: #e04b4a;vertical-align: middle;font-size: 1.5rem;"><i class="fa fa-times"></i>
              <th><input onkeyup="filterStatusList()" type="text" class="form-control" id="staff-filter" placeholder="Search"/></th>
              <th width=15%></th>
              <th width=15%></th>
              <th width=20%></th>
              <th width=7%></th>
              <th width=8%></th>
              <th width=25%></th>
              <th width=25%>
                <button class="btn btn-primary" onclick="massUpdateStaffAttendanceStatus()">Mass update status</button>
              </th>
              </tr>`
      html += '<thead>';
      html += `
        <tr>
          <th width=5%>#</th>
          <th width=15%>Staff</th>
          <th width=15%>Shift</th>
          <th width=15%>Source</th>
          <th width=20%>Check-in/out</th>
          <th width=7%>Duration</th>
          <th width=8%>Status</th>
          <th width=25%>Update Status</th>
          <th width=25%>
            <label class="checkbox-inline"><input onclick="select_all()" type="checkbox" id="checkall" value="">Select All</label>
          </th>
        </tr>
      `  
          
      html += '</thead>';
      html += '<tbody>';
      for(var i=0; i<staff.length; i++) {
        html += `<tr id="staff_${staff[i].staff_id}" data-staff_name="${staff[i].staff_name.toLowerCase()}" class="staff-filters">`;
        html += '<td>'+(i+1)+'</td>';
        html += singleStaffData(staff[i]);
        html += '</tr>';
      }
      html += '</tbody>';
      html += '</table>';
    }

    $("#staff-list").html(html);
  }
  function clearStatusFilters() {
        $(`#staff-filter`).val('');
        filterStatusList();
  }
  function filterStatusList() {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById("staff-filter");
  filter = input.value.toUpperCase();
  table = document.getElementById("staff_list_table");
  tr = table.getElementsByTagName("tr");

  // Loop through all table rows, and hide those who don't match the search query
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[1];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toUpperCase().indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }
  }
  }

  function toggleSelect() {
    if($("#select-all").is(':checked')) {
      $(".approvals").prop('checked', true);
      $("#action").html('<button onclick="approveAttendance()" class="btn btn-sm btn-primary">Approve</button>');
    } else {
      $("#action").html('Action');
      $(".approvals").prop('checked', false);
    }
  }

  function singleStaffData(staff) {
    var html = '';
    html += '<td>'+staff.staff_name+'</td>';
    html += getShiftTime(staff);
    html += `<td>${staff.source || "-"}</td>`;
    html += getAttendedTime(staff);
    html += displayStatus(staff);
    return html;
  }

  async function get_leave_reporting_managers(staff_id) {
    let response="";
    //  Getting Approvers for  level 
    let getLeaveApproversURL="<?php echo site_url('staff/leaves/getReportingStaffDataperstaff'); ?>";
    if(isActiveMultiLevelLeaveApprovalMode==1){
      getLeaveApproversURL="<?php echo site_url('staff/leaves/getReportingStaffDataperstaff_3level'); ?>";
    }

    await $.ajax({
        url: getLeaveApproversURL,
        type: 'post',
        data: { 'staff_id': staff_id },
        success: function (json_data) {
          response = JSON.parse(json_data);
        }
    });
    return response;
  }

  async function applyLeave(staff_id, staff_shift_id) {
    let response="";
    try {
      response=await get_leave_reporting_managers(staff_id);
    } catch (err) {
      console.log(err.message);
    }

    var staff_name = $("#apply_leave"+staff_id).data('staff');
    var html = '<div class="card-body pt-1 pb-3"><div class="form-group row" id="quota"></div>';
    html += '<table class="table table-bordered text-left">';

    html += '<tr>';
    html += '<th>Leave</th>';
    html += '<td><select class="form-control" name="selection_type" id="selection_type"><option value="fullday">Full Day</option><option value="morning">Morning Session</option><option value="noon">Afternoon Session</option></select></td>';
    html += '</tr>';

    html += '<tr>';
    html += '<th>Date</th>';
    var date = $("#date").val();
    html += '<td><input type="hidden" id="noofdays" value="1"><input type="hidden" id="from_date" value="'+date+'"><input type="hidden" id="to_date" value="'+date+'">'+date+'</td>';
    html += '</tr>';

    html += '<tr>';
    html += '<th>Reason</th>';
    html += `<td>
               <textarea rows="5" class="form-control" id="reason"></textarea>
                </td>`;
    // Show Approvers / Reporting managers
    if(Object.entries(response)?.length){
      if(isActiveMultiLevelLeaveApprovalMode==1){
        // for 3 level
        if(+response.approver_mode<1){
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">No Approver(s)</p>`;
        }else{
          if(response.approver_mode == 1) {
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 1: ${response.approver_1.trim() || "NA"}</p>`;
          }
    
          if(response.approver_mode == 2) {
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 1: ${response.approver_1.trim() || "NA"}</p>`;
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 2: ${response.approver_2.trim() || "NA"}</p>`;
          }
    
          if(response.approver_mode == 3) {
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 1: ${response.approver_1.trim() || "NA"}</p>`;
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 2: ${response.approver_2.trim() || "NA"}</p>`;
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Approver 3: ${response.approver_3.trim() || "NA"}</p>`;
          }
        }
      }else{
        // for single level
        if(response.reporting_to?.length){
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">Reporting Manager: ${response.reporting_to}</p>`;
        }else{
            html+=`<p style="background: #f2f2f2;width: 30rem;margin: auto;padding: 3px;margin-bottom: 10px;">No Reporting Manager</p>`;
        }
      }
    }
    html+='<p style="width: 14rem;margin: auto;padding: 3px;margin-bottom: 10px;"></p>';

    const isLeaveQuotaAvailable=window.sessionStorage.getItem("isLeaveQuotaAvailable");
    if(+isLeaveQuotaAvailable==1){
      const applyLeaveUrl=`<?php echo site_url() ?>staff/leaves/${isActiveMultiLevelLeaveApprovalMode==1 ? "apply_leave_3level" : "apply_leave"}/${staff_id}`;
      html += `<a href="${applyLeaveUrl}" target="_blank" style="position: relative;padding: 5px;background: #f3f3f3;bottom: 8px;">Please visit this link to apply leave for more than 1 day(s)</a>`;
    }

    html += '</tr>';

    html += '</table>';
    Swal.fire({
      title: staff_name,
      html: html,
      width: '60%',
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      didOpen: function() {
        getAvailableStaffQuota(staff_id);
      },
      preConfirm: function() {
        var lc_button = $("input[type=radio][class=leave_category]:checked").attr('id');
        if(lc_button === undefined) {
          Swal.showValidationMessage('Select Leave Type');
        }
        
      }
    }).then((result) => {
      if (result.isConfirmed) {
        let leaveApplyingBtn=document.getElementById(`apply_leave${staff_id}`);
        const previousLeaveApplyBtn=leaveApplyingBtn;
        leaveApplyingBtn.closest("div").innerHTML=`<div><span id="apply_leave${staff_id}" class='label label-default ml-2 label-form'>Applying...</span></div>`;

        var lc_button = $("input[type=radio][class=leave_category]:checked").attr('id');
        var leave_category = $("#"+lc_button).val();
        var selection_type = $("#selection_type").val();
        var reason = $("#reason").val().trim();
        var date = $("#date").val();
        var input = {
          'staff_id' : staff_id,
          'from_date' : date,
          'to_date' : date,
          'selection_type' : selection_type,
          'noofdays' : 1,
          'reason' : reason,
          'leave_category' : leave_category,
          'leave_v2_year_id':leave_v2_year_id,
          'is_application_from_manage_attendance':1
        };

        if(isActiveMultiLevelLeaveApprovalMode==1){
          input.approver_mode=response?.approver_mode;
          input.approver_1=response?.rpid1;
          input.approver_2=response?.rpid2;
          input.approver_3=response?.rpid3;
        }

        let leaveURL="<?php echo site_url('staff/leaves/saveLeave'); ?>"
        if(isActiveMultiLevelLeaveApprovalMode==1){
          leaveURL="<?php echo site_url('staff/leaves/saveLeave_3level'); ?>";
        }

        $.ajax({
          url: leaveURL,
          type: 'post',
          data: input,
          success: function(data) {
            var data = JSON.parse(data);
            var status = parseInt(data.status);
            
            if(status != 0) {
              var sl = $("#staff_"+staff_id+" td:first").html();
              var html = '<td>'+sl+'</td>';
              html += singleStaffData(data.staff);
              $("#staff_"+staff_id).html(html);
              // show success modal
              Swal.fire({
                icon: "success",
                title: "Applied",
                text: `Leave Applied Successfully for ${staff_name}!`,
              });
            } else {
              leaveApplyingBtn=document.getElementById(`apply_leave${staff_id}`);
              leaveApplyingBtn.closest("div").innerHTML=`<div><span id="apply_leave${staff_id}" type="button" class='label label-default ml-2 label-form' data-staff="${staff_name}" onclick="applyLeave(${staff_id},${staff_shift_id})">Apply Leave</span></div>`;

              Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
              });
            }
          }
        });

      }
    });
  }

  function displayStatus(staff) {
    if(staff.type == null) {
      return '<td>-</td><td>-</td>';
    }

    // console.log(staff.leave_status);

    const is_three_level_approve_enabled="<?php echo $is_three_level_approve_enabled; ?>";

    var disabled = '';
    var staffId = staff.staff_id;
    var staff_shift_id = staff.staff_shift_id;
    var absent = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="absent'+staffId+'" class="btn btn-secondary absent btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+', \'absent\')">AB</button>';
    var halfday = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="halfday'+staffId+'" class="btn btn-secondary halfday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'halfday\')">HD</button>';
    var present = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="present'+staffId+'" class="btn btn-secondary present btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'present\')">P</button>';
    // var OOD = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="ood'+staffId+'" class="btn btn-secondary ood btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'ood\')">OOD</button>';
    var weekoff = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="weekoff'+staffId+'" class="btn btn-secondary weekoff btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'weekoff\')">WO</button>';
    var holiday = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="holiday'+staffId+'" class="btn btn-secondary holiday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'holiday\')">H</button>';
    var leave = '';
    var att_status = '-';
    if(staff.attendance_id == 0) {
      if(staff.type == 2) {
        weekoff = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="weekoff'+staffId+'" class="btn btn-secondary active weekoff btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'weekoff\')">WO</button>';
        $("#weekoff"+staffId).click();
        att_status = 'WO';
      } else if(staff.type == 3) {
        holiday = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="holiday'+staffId+'" class="btn btn-secondary active holiday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'holiday\')">H</button>';
        $("#holiday"+staffId).click();
        att_status = 'H';
      } else {
        att_status = '-';
        // active
        absent = `<button ${disabled} data-staff="${staff.staff_name}" type="button" id="absent${staffId}" class="btn btn-secondary active absent btn${staffId}" onclick="changeStatus('${staffId}', '${staff_shift_id}','absent')">AB</button>`;
        $("#absent"+staffId).click();

        if(staff.shift_end_time){
          att_status = 'AB';
          if(staff.leave_status==0 || +staff.leave_details.total_leaves_taken_count<1){
            if(leave_v2_year_id!=0){
              leave = '<div><span '+disabled+' class="label label-default ml-2 label-form" type="button" id="apply_leave'+staffId+'" data-staff="'+staff.staff_name+'" onclick="applyLeave('+staffId+', '+staff_shift_id+')">Apply Leave</span></div>';
            }
          }
        }

        if(staff.leave_status >= 1 && !+is_three_level_approve_enabled) {
          if(staff.leave_details?.total_leaves_taken_count>=0.5){
            const leaveDetails=Object.values(staff.leave_details);
            leaveDetails.pop();
            leaveDetails.forEach(l=>{
              if(l.status==0){
                leave += `<br> <span '+disabled+' class="label label-default ml-2 label-form" style="background:#4e770b;" id="approve_'+staff.leave_id+'" data-staff-name="${staff.staff_name}" onclick="approveLeave(${l.leave_id}, ${staffId}, '${staff.staff_name}')">Approve/Reject Leave</span>`;
              }
            })
          }
        }
      }
    } else {
      if(staff.status == 'P') {
        present = '<button '+disabled+' type="button" id="present'+staffId+'" class="btn btn-secondary active present btn'+staffId+'" data-staff="'+staff.staff_name+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'present\')">P</button>';
          $("#present"+staffId).click();
          att_status = 'P';
      // } else if(staff.status == 'OOD') {
      //   OOD = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="ood'+staffId+'" class="btn btn-secondary active ood btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'ood\')">OOD</button>';
      //   $("#ood"+staffId).click();
      //   att_status = 'OOD';
      }else if(staff.status == 'HD') {
        halfday = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="halfday'+staffId+'" class="btn btn-secondary active halfday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'halfday\')">HD</button>';
        $("#halfday"+staffId).click();
        att_status = 'HD';

        if(staff.leave_status==0 || +staff.leave_details.total_leaves_taken_count==0){
          if(leave_v2_year_id!=0){
            leave = '<div><span '+disabled+' class="label label-default ml-2 label-form" type="button" id="apply_leave'+staffId+'" data-staff="'+staff.staff_name+'" onclick="applyLeave('+staffId+', '+staff_shift_id+')">Apply Leave</span></div>';
          }
        }

      } else if(staff.status == 'AB') {
        absent = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="absent'+staffId+'" class="btn btn-secondary active absent btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'absent\')">AB</button>';
        $("#absent"+staffId).click(); 
        att_status = 'AB';

        if(staff.leave_status==0 || +staff.leave_details.total_leaves_taken_count<1){
          if(leave_v2_year_id!=0){
            leave = '<div><span '+disabled+' class="label label-default ml-2 label-form" type="button" id="apply_leave'+staffId+'" data-staff="'+staff.staff_name+'" onclick="applyLeave('+staffId+', '+staff_shift_id+')">Apply Leave</span></div>';
          }
        }
      } else if(staff.status == 'WO') {
        weekoff = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="weekoff'+staffId+'" class="btn btn-secondary active weekoff btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'weekoff\')">WO</button>';
        $("#weekoff"+staffId).click();
        att_status = 'WO';
      } else if(staff.status == 'H') {
        holiday = '<button '+disabled+' data-staff="'+staff.staff_name+'" type="button" id="holiday'+staffId+'" class="btn btn-secondary active holiday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'holiday\')">H</button>';
        $("#holiday"+staffId).click();
        att_status = 'H';
      } else {
        if(staff.last_check_out_time) {
          present = '<button '+disabled+' type="button" id="present'+staffId+'" class="btn btn-secondary active present btn'+staffId+'" data-staff="'+staff.staff_name+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'present\')">P</button>';
          $("#present"+staffId).click();
          att_status = 'P';
        }
        if(att_status=="-"){
          if(staff.leave_status==0 || +staff.leave_details.total_leaves_taken_count==0){
            if(leave_v2_year_id!=0){
              leave = '<div><span '+disabled+' class="label label-default ml-2 label-form" type="button" id="apply_leave'+staffId+'" data-staff="'+staff.staff_name+'" onclick="applyLeave('+staffId+', '+staff_shift_id+')">Apply Leave</span></div>';
            }
          }
        }
      }


      // Approve/reject options should also appear
      if(staff.leave_status >= 1 && !+is_three_level_approve_enabled) {
        if(staff.leave_details?.total_leaves_taken_count>=0.5){
          const leaveDetails=Object.values(staff.leave_details);
          leaveDetails.pop();
          leaveDetails.forEach(l=>{
            if(l.status==0){
              leave += `<span '+disabled+' class="label label-default ml-2 label-form" style="background:#4e770b;" id="approve_'+staff.leave_id+'" data-staff-name="${staff.staff_name}" onclick="approveLeave(${l.leave_id}, ${staffId}, '${staff.staff_name}')">Approve/Reject Leave</span>`;
            }
          })
        }
      }
    }

    // show convert leave button
    if(isSuperAdmin==1){
      if(+staff.leave_details.total_leaves_taken_count>=0.5){
        if(isActiveMultiLevelLeaveApprovalMode==1){
          leave+=`<div><a target="_blank" href="<?php echo site_url('staff/leaves/staff_leave_approval_3level') ?>/${staffId}" class="label label-default ml-2 label-form" type="button">Goto Convert Leave</a></div>`;
        }else{
          leave+=`<div><a target="_blank" href="<?php echo site_url('staff/leaves/staff_leave_approval') ?>/${staffId}" class="label label-default ml-2 label-form" type="button">Goto Convert Leave</a></div>`;
        }
      }
    }

    //Display Late status
    var late_string = (staff.is_late == 0) ? '' : '<br> <span style="color:#4F75FF;font-weight:700">(Late)</span>';

    //Display Override status
    var override_string = (staff.is_manually_changed == 0) ? '' : '<span style="color:#4F75FF;font-weight:700">(Overridden)</span>';

    let totalLeaveConsiderPresentLeaveCount=0;
    let isLOPLeaveAvailable=false;
    let lopString="";
    let isOnNormalLeave=false;
    let normalLeaveString="";
    let totalLeaveTakenDuration=0;
    let all_leave_taken_names=""
    let totalLeaveTakenNames={};
    let viewInfo="";
    
    if(staff.leave_status>=1){
      const leaveDetails=Object.values(staff.leave_details)
      totalLeaveTakenDuration=leaveDetails.at(-1);

      if(leaveDetails?.length){
        leaveDetails.forEach(l=>{
          if(typeof l=="object"){
            if(l.status==3 || l.status==4){
              return;
            }

            totalLeaveTakenNames[l.category_short_name]=l.category_short_name;

            if(+l.is_loss_of_pay===1){
              isLOPLeaveAvailable=true;
            }else{
              isOnNormalLeave=true;
            }

            if(+l.consider_present===1){
              totalLeaveConsiderPresentLeaveCount+=Number(l.leave_no_of_days);
            }
          }
        })
      }

      if(Object.keys(totalLeaveTakenNames)?.length){
        all_leave_taken_names=Object.keys(totalLeaveTakenNames).join(",");
      }

      if(totalLeaveConsiderPresentLeaveCount || isLOPLeaveAvailable || isOnNormalLeave){
        att_status = '';
      }
    }

    var on_leave_status="";
    if(staff.leave_status>=1){
      if(staff.leave_details["total_leaves_taken_count"]==0.50){
        on_leave_status = "View Info";
      }else if(staff.leave_details["total_leaves_taken_count"]>=1.00){
        on_leave_status = "View Info";
      }else{
        on_leave_status = "View Info!";
      }
    }

    if(on_leave_status!=""){
      viewInfo = `<br> <span style="color:#4F75FF;font-weight:700;text-decoration:underline;" id=leave-detail-${staff.leave_id} data-leave-details='${JSON.stringify(staff.leave_details)}' onclick="showLeaveAppliedDetails(${staff.leave_id})" data-staff-name="${staff.staff_name}">${on_leave_status}</span>`;
    }

    // Goto Convert Leave
    var html = `<td>${att_status} ${all_leave_taken_names?.length ? `On ${all_leave_taken_names}` : ""} ${late_string} <br>${override_string} ${viewInfo}</td>`;
    html += '<td>';
    if(+totalLeaveTakenDuration<=1){
      html += '<div class="btn-group btn-group-sm ml-2">';
      html += '<input type="hidden" value="'+att_status+'" id="status'+staffId+'"/>';
      // html += absent + halfday + present + OOD + weekoff + holiday; 
      if(totalLeaveConsiderPresentLeaveCount){
        if(staff.leave_status>=1){
          if(totalLeaveConsiderPresentLeaveCount==0.5){
            // check if the current attendance exit and is also HD
            if(staff.status=="HD"){
              present = '<button disabled type="button" id="present'+staffId+'" class="btn btn-secondary active present btn'+staffId+'" data-staff="'+staff.staff_name+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'present\')">P</button>';
              html+=`${present}`;
            }else{
              halfday = '<button disabled data-staff="'+staff.staff_name+'" type="button" id="halfday'+staffId+'" class="btn btn-secondary active halfday btn'+staffId+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'halfday\')">HD</button>';
              html+=`${halfday}`;
            }
          }else if(totalLeaveConsiderPresentLeaveCount>=1){
            present = '<button disabled type="button" id="present'+staffId+'" class="btn btn-secondary active present btn'+staffId+'" data-staff="'+staff.staff_name+'" onclick="changeStatus('+staffId+', '+staff_shift_id+',\'present\')">P</button>';
            html+=`${present}`;
          }else{
            html+=`${absent}`;
          }
        }
      }else{
        if(totalLeaveTakenDuration==0){
          html += absent + halfday + present + weekoff + holiday; 
        }else if(totalLeaveTakenDuration==0.5){
          html += absent + halfday;
        }
      }
      html += '</div>';
    }
    
    html += leave;
    if(staff.attendance_id != 0) {
      html += '<span id="history_'+staff.attendance_id+'" data-staff="'+staff.staff_name+'" data-staffid="'+staffId+'" onclick="viewAttendanceTransactions('+staff.attendance_id+')" class="label label-primary ml-2 label-form">View Changes</span>';
    }

    // edit attendance
    const editAttendancePrivilage = "<?php echo $this->authorization->isAuthorized("STAFF_ATTENDANCE.EDIT_ATTENDANCE");?>";
    if(editAttendancePrivilage==1 && staff.last_check_out_time){
      html += '<span id="edit_'+staff.attendance_id+'" data-attendance-id="'+staff.attendance_id+'" data-staff="'+staff.staff_name+'" data-staffid="'+staffId+'" class="label label-primary ml-2 label-form" data-toggle="modal" data-target="#editAttendance">Edit</span>';
    }
    html += '</td>';

    html+=`
      <td>`;
    if(+staff.attendance_id || +staff?.leave_details?.total_leaves_taken_count>=0.5){
      html+=`<p>-</p>`;
    }else{
      html+=`
      <label class="form-control" style="font-size: 13px;display: grid;gap: 0.5em;border: none;">
        <input type="checkbox" id="" value="" class="staff-attendance-update-checkbox" data-staff-id="${staffId}" data-staff-shift-id="${staff_shift_id}" data-old-status="${att_status}">
      </label>`;
    }

    html+=`</td>`;

    return html;
  }

  $("#editAttendance").on("shown.bs.modal",e=>{
    const {attendanceId, staffid:staffId, staff:staffName}=e.relatedTarget.dataset;
    
    $("#edit_attendance_id").val(attendanceId);
    $("#edit_staff_id").val(staffId);
    $("#edit_staff_name").val(staffName);

    $("#editAttendanceLabel").html(`Edit Attendance for ${staffName}`);
    
    getStaffAttendanceData(attendanceId);
  });

  function getStaffAttendanceData(attendanceId){
      $.ajax({
        url:"<?php echo site_url('staff/attendance/getStaffAttendanceData') ?>",
        type:"POST",
        data:{attendanceId},
        success:function(data){
          const {check_in_time:check_in_date_time,check_out_time:check_out_date_time,is_late,reason}=$.parseJSON(data);

          const [oldCheckInDate,check_in_time]=check_in_date_time.toString().split(" ");
          const [oldCheckOutDate,check_out_time]=check_out_date_time.toString().split(" ");

          $("#old_check_in_date").val(oldCheckInDate);
          $("#old_check_out_date").val(oldCheckOutDate);
          $("#edit_remarks").val(reason);
          
          $("#new_check_in_time").val(check_in_time);
          $("#new_check_out_time").val(check_out_time);

          let html=`<option ${is_late=="0" && "Selected"} id="edit_late_0" value="0">On Time</option>
            <option ${is_late=="1" && "Selected"} id="edit_late_1" value="1">Late</option>`;

        $("#edit_late").html(html);
        }
      });
  }

  function editStaffAttendanceTime(){
    // console.log(attendanceId);
    $("#att_edit_button").text("please_wait").prop("disabled",true);
    const attendanceId=$("#edit_attendance_id").val();
    const staffId=$("#edit_staff_id").val();
    const staffName=$("#edit_staff_name").val();

    const old_check_in_date=$("#old_check_in_date").val().toString().split("-").reverse().join("-");
    const old_check_out_date=$("#old_check_out_date").val().toString().split("-").reverse().join("-");

    
    let newCheckInTime=$("#new_check_in_time").val();
    let newCheckOutTime=$("#new_check_out_time").val();

    newCheckInTime=`${old_check_in_date} ${newCheckInTime}`;
    newCheckOutTime=`${old_check_out_date} ${newCheckOutTime}`;

    const editLate=$("#edit_late").val();
    const editRemarks=$("#edit_remarks").val();

    $.ajax({
        url:"<?php echo site_url('staff/attendance/editStaffAttendanceTime') ?>",
        type:"POST",
        data:{attendanceId,staffId,newCheckInTime,newCheckOutTime,editLate,editRemarks},
        success:function(data){
          $("#editAttendance").trigger("click");

          Swal.fire({
            icon:"success",
            title:"Edit Attendance",
            text:"Edit Successfull"
          }).then(e=>{
            window.location.reload();
          })
        }
    })
  }

  function viewAttendanceTransactions(attendance_id) {
    var date = $("#date").val();
    var staff_name = $("#history_"+attendance_id).data('staff');
    $.ajax({
        url: '<?php echo site_url('staff/attendance/getStaffAttendanceChanges'); ?>',
        data: {'attendance_id': attendance_id},
        type: "post",
        success: function (data) {
            var transactions = JSON.parse(data);
            var html = '<div class="mb-2"><h4>'+staff_name+' ('+date+')</h4></div>';
            html += '<table class="table table-bordered" style="text-align:left;">';
            html += '<thead>';
            html += '<tr>';
            html += '<th width="15%">Source</th>';
            html += '<th width="17%">Time</th>';
            html += '<th width="23%">Action</th>';
            html += '<th width="20%">Action By</th>';
            html += '<th width="25%">Comment</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            for(var i=0; i<transactions.length; i++) {
              var latitude = transactions[i].latitude;
              var longitude = transactions[i].longitude;
              html += '<tr>';
              html += '<td>'+transactions[i].source || 'Unavailable'+'</td>';
              html += '<td>'+transactions[i].action_at+'</td>';
              html += '<td>'+transactions[i].action;
              var is_outside_campus = transactions[i].is_outside_campus;
              if(is_outside_campus == 2) { //location not captured
                html += '<br><font style="color:red">Location : '+transactions[i].location_capture_error+'<font>';
              } else {
                if(latitude) {
                  html += '<br><b>Lat: </b>'+latitude+' <b>Long:</b>'+longitude;
                  if(is_outside_campus == 1) {
                    html += `<br><font style="color:red">Outside: ${transactions[i].distance_from_campus} meters from ${transactions[i].location_name}</font>`;
                  } else {
                    html += `<br><font>${transactions[i].location_name}</font>`;
                    // html += `<br><font>Inside the campus</font>`;
                  }
                }
              }
              // if(latitude != '' && latitude != null) {
              //   html += '<br><b>Lat: </b>'+latitude+' <b>Long:</b>'+longitude;
              //   if (transactions[i].distance_from_campus != null) {
              //     color = (transactions[i].is_outside != null && transactions[i].is_outside==1) ? 'style="color:red"' : '';
              //     html += `<br><font ${color}>${transactions[i].distance_from_campus} meters from campus</font>`;
              //   }
              // }
              html += '</td>';
              html += '<td>'+transactions[i].action_by+'</td>';
              html += '<td>'+transactions[i].reason+'</td>';
              html += '</tr>';
            }
            html += '</tbody>';
            html += '</table>';
            Swal.fire({
                title: 'Attendance status history',
                html: html,
                width: '50%',
                confirmButtonText: 'Okay'
            });
        },
        error: function (err) {
            console.log(err);
        }
    });
  }

  function approveLeave(leave_id, staff_id, staff_name) {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/staffLeave'); ?>',
        data: {'leave_id': leave_id},
        type: "post",
        success: function (data) {
            var leave = JSON.parse(data);
            leave.staff_name = staff_name;
            var html = '<table class="table table-bordered" style="text-align:left;">';
            html += '<tr><th style="width: 20%;">Staff</th><td>'+staff_name+'</td><tr>';
            html += '<tr><th>Leave type</th><td>'+leave.leave_type+'</td><tr>';
            html += '<tr><th>From date</th><td>'+leave.from_date+'</td><tr>';
            html += '<tr><th>To date</th><td>'+leave.to_date+'</td><tr>';
            html += '<tr><th>No. of days</th><td>'+leave.noofdays+'</td><tr>';
            html += '<tr><th>Reason</th><td>'+leave.reason+'</td><tr>';

            if(leave.leave_evidence_url){
                html += `<tr><th>Leave Evidence</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_evidence_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Evidence">View Doc</button></td><tr>`;
            }else{
                html+=`<tr><th>Leave Evidence</th><td>No attachment found</td>`;
            }

            if(leave.leave_plan_url){
                html += `<tr><th>Leave Plan</th><td><button type="button" class="btn btn-primary" data-doc_url='${leave.leave_plan_url}' data-toggle="modal" data-target="#showLeaveDocModal" data-name="Leave Plan">View Doc</button></td><tr>`;
            }else{
                html+=`<tr><th>Leave Plan</th><td>No attachment found</td>`;
            }

            html += '<tr><th>Status</th><td>';
            html += '<label class="radio-inline"><input value="1" type="radio" checked name="status">Approve</label>';
            html += '<label class="radio-inline"><input value="3" type="radio" name="status">Reject</label>';
            html += '</td><tr>';
            html += '<tr><th>Remarks</th><td><textarea id="description" class="form-control" id="description"></textarea></td><tr>';
            html += '</table>';
            Swal.fire({
                title: 'Approve/Reject Leave',
                html: html,
                width: '40%',
                confirmButtonText: 'Save',
                showCancelButton: true,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    var status = $("input[name='status']:checked").val();
                    var description = $("#description").val();
                    var data = {
                        'id' : leave_id,
                        'status' : status,
                        'description' : description,
                        'staff_id' : staff_id,
                        'staff_name':staff_name
                    };
                    saveLeaveStatus(data);
                }
            });
        },
        error: function (err) {
            console.log(err);
        }
    });
  }

  function saveLeaveStatus(input) {
    $.ajax({
        url: '<?php echo site_url('staff/leaves/saveLeaveStatus'); ?>',
        data: input,
        type: "post",
        success: function (data) {
            if(parseInt(data)){
                updateStaffRow(input.staff_id);
            } else{
                Swal.fire({
                  title: "Error",
                  text: "Failed to update status",
                  icon: "error",
                });
            }
        },
        error: function (err) {
            console.log(err);
        }
    });
  }

  function updateStaffRow(staff_id) {
    var date = $("#date").val();
    $.ajax({
        url: '<?php echo site_url('staff/attendance/getStaffAttendanceDataByStaffId'); ?>',
        data: {'staff_id':staff_id, 'date':date},
        type: "post",
        success: function (data) {
            var staff = JSON.parse(data);
            var sl = $("#staff_"+staff_id+" td:first").html();
            var html = '<td>'+sl+'</td>';
            html += singleStaffData(staff);
            $("#staff_"+staff_id).html(html);
        },
        error: function (err) {
            console.log(err);
        }
    });
  }

  function approveAttendance() {
    var staff_ids = [];
    var attendance_id = {};
    var staff_shift_id = {};
    var leave_id = {};
    var status = {};
    document.querySelectorAll('input[class="approvals"]:checked').forEach(function(ele){
      var staff_id = ele.value;
      staff_ids.push(staff_id);
      attendance_id[staff_id] = ele.dataset.attendanceid;
      staff_shift_id[staff_id] = ele.dataset.staffshiftid;
      leave_id[staff_id] = ele.dataset.leaveid;
      status[staff_id] = $("#status"+staff_id).val();
    });

    // console.log(staff_id);
    // console.log(attendance_id);
    // console.log(staff_shift_id);
    // console.log(leave_id);
    // console.log(status);
    var date = $("#date").val();
    var input = {
      'staff_ids' : staff_ids,
      'attendance_id' : attendance_id,
      'staff_shift_id' : staff_shift_id,
      'leave_id' : leave_id,
      'status' : status,
      'date' : date
    }
    $.ajax({
      url: '<?php echo site_url('staff/attendance/approveAttendance'); ?>',
      type: 'post',
      data: input,
      success: function(data) {
        var data = JSON.parse(data);
        if(data.status == 1) {
          staffList = data.staff;
          for(var i in staffList) {
            staff_id = staffList[i].staff_id;
            var sl = $("#staff_"+staff_id+" td:first").html();
            var html = '<td>'+sl+'</td>';
            html += singleStaffData(staffList[i]);
            $("#staff_"+staff_id).html(html);
          }
        } else {
          Swal.fire({
            title: "Error",
            text: "Failed to approve",
            icon: "error",
          });
        }
      }
    });    
  }

  function toggleApproval() {
    var selected = document.querySelectorAll('input[class="approvals"]:checked').length;
    if(selected) {
      $("#action").html('<button onclick="approveAttendance()" class="btn btn-sm btn-primary">Approve</button>');
    } else {
      $("#action").html('Action');
      $("#select-all").prop('checked', false);
    }
  }

  function getKeyByValue(object, value) {
    return Object.keys(object).find(key => object[key] === value);
  }

  function changeStatus(staff_id, staff_shift_id, status) {
    var att_status_object = {
      'present' : 'P',
      'absent' : 'AB',
      'halfday' : 'HD',
      'weekoff' : 'WO',
      'holiday' : 'H',
      'ood' : 'OOD',
    }
    $(".btn"+staff_id).removeClass('active');
    var staff_name = $("#"+status+''+staff_id).data('staff');
    $("#"+status+''+staff_id).addClass('active');
    var new_status = att_status_object[status];
    var old_status = $("#status"+staff_id).val();
    if(new_status == old_status) {
      return false;
    }
    $("#status"+staff_id).val(new_status);

    var html = '<div><p>Changing status from <b>'+old_status+'</b> to <b>'+new_status+'</b></p></div>';
    html += '<textarea placeholder="Enter reason here..." class="form-control" id="change_reason" rows="5"></textarea>';
    Swal.fire({
      title: staff_name,
      html: html,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      preConfirm: function() {
        var reason = $("#change_reason").val().trim();
        if(reason == '') {
          //Swal.showValidationMessage('Enter the reason');
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        var reason = $("#change_reason").val().trim();
        var selected_staff_type = $('#selected_staff_type').val();
        var input = {
          'staff_id' : staff_id,
          'staff_shift_id' : staff_shift_id,
          'old_status' : old_status,
          'new_status' : new_status,
          'reason' : reason,
          'date' : $("#date").val(),
          'staff_type':selected_staff_type
        };

        $.ajax({
          url: '<?php echo site_url('staff/attendance/changeStaffAttendanceStatus'); ?>',
          type: 'post',
          data: input,
          success: function(data) {
            var data = JSON.parse(data);
            var attendance_id = parseInt(data.attendance_id);
            if(attendance_id != 0) {
              var sl = $("#staff_"+staff_id+" td:first").html();
              var html = '<td>'+sl+'</td>';
              html += singleStaffData(data.staff);
              $("#staff_"+staff_id).html(html);
            } else {
              var old = getKeyByValue(att_status_object, old_status);
              $(".btn"+staff_id).removeClass('active');
              $("#"+old+''+staff_id).addClass('active');
              $("#status"+staff_id).val(old_status);
              Swal.fire({
                title: "Error",
                text: "Unable to change status",
                icon: "error",
              });
            }
          }
        });
      } else {
        var old = getKeyByValue(att_status_object, old_status);
        $(".btn"+staff_id).removeClass('active');
        $("#"+old+''+staff_id).addClass('active');
        $("#status"+staff_id).val(old_status);
      }
    });
  }

  function getShiftTime(staff) {
    var html = '<td>';
    if(staff.type == 1) {
      html += staff.shift_start_time + ' <span style="color:#b5b1b1">to</span> '+staff.shift_end_time;
    } else if(staff.type == 2) {
      html += '<span class="text-danger">Week-Off</span>';
    } else if(staff.type == 3) {
      html += '<span class="text-danger">Holiday</span>';
    } else {
      html += '<small style="color:#b5b1b1"><i>No Shift Assigned</i></small>';
    }
    html += '</td>';
    return html;
  }

  function showLeaveAppliedDetails(leaveId){
    const leaveDetailsObject=document.getElementById(`leave-detail-${leaveId}`).dataset;
    let leaveDetails=leaveDetailsObject.leaveDetails;
    leaveDetails=Object.values(JSON.parse(leaveDetails));
    leaveDetails.pop();
    const staffName=leaveDetailsObject.staffName;

    if(!leaveDetails.length) return;

    let html="";

    const currentLeaveStatus={
      0:"Pending Approval",
      1:"Approved",
      2:"Auto Approved",
      3:"Rejected",
      4:"Cancelled",
    }

    const countLeavesTypes={};

    leaveDetails.forEach((leave,index)=>{
      const leaveDuration=leave.leave_no_of_days==0.5 ? "Half Day" : "Full Day";
      // count each leave type
      if(leave.status>=0 && leave.status<=4){
        let leaveTypeInfo=`${leaveDuration} Leave(s) ${currentLeaveStatus[leave.status]} (${leave.category_name})`;
        if(leave.consider_present==1){
          leaveTypeInfo+=`<br> <small>This ${leave.category_name} is considered as Present</small>`;
        }
        countLeavesTypes[leaveTypeInfo]=++countLeavesTypes[leaveTypeInfo] || 1;
      }
    })
    
    html+=`
      <table class="table table-bordered">
      <tr>
        <th>Leave(s) description</th>
        </tr>
        `;

     for(let [leaveDescription, leaveCount] of Object.entries(countLeavesTypes)){
      html += `<tr style="text-align: left;">
                <td><span style="color: #4F75FF;"><i>${leaveCount} ${leaveDescription}</i></span></td>
                </tr>`;
              }

    html+=`</table>`;

    Swal.fire({
      title: `<strong><u>${staffName}</u></strong>`,
      icon: "info",
      html: `${html}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Close!`,
      confirmButtonAriaLabel: "Thumbs up, Ok!",
    });
  }

  function getAttendedTime(staff) {

    var html = '<td>';
    if(staff.attendance_id == 0) {
      if(staff.type == 2) {
        html += '<span class="text-danger">Week-Off</span>';
      } else if(staff.type == 3) {
        html += '<span class="text-danger">Holiday</span>';
      } else {
        if(staff.leave_status==0){
          html += '<span style="color: #4F75FF;cursor: text;"><i>Leave Not Applied<i></span>';
        }
      }
    } else {
      var checkin = ' - ';
      var checkout = ' - ';
      if(staff.first_check_in_time != '') {
        checkin = staff.first_check_in_time;
      }
      if(staff.last_check_out_time != '') {
        checkout = staff.last_check_out_time;
      }
      html += '<div class="d-flex justify-content-between">';
      html += '<span>IN: '+checkin+'</span>';
      if (staff.is_checkin_outside_campus == 1) {
        html += `
            <span style="color:#4F75FF"><i>(Outside campus check-in)</i></span>
        `;        
      } else if(staff.is_checkin_outside_campus == 2) {
        html += `
            <span style="color:#4F75FF"><i>(Couldn't capture Location)</i></span>
        `;
      }
      html += '</div>';

      // html += '<br>';
      // html += '&nbsp;To&nbsp;';
      html += '<div class="d-flex justify-content-between">';
      html += `<span>OUT: ${checkout} ${staff.is_auto_checked_out}</span>`;
      if (staff.is_checkout_outside_campus == 1) {
        html += `
            <span style="color:#4F75FF"><i>(Outside campus check-out detected)</i></span>
        `;        
      } else if(staff.is_checkout_outside_campus == 2) {
        html += `
            <span style="color:#4F75FF"><i>(Couldn't capture Location)</i></span>
        `;
      }
      html += '</div>';
      
    }
    html += '</td>';
    html += '<td>'+calculateTime(staff.duration)+'</td>';
    return html;
  }

  function calculateTime(duration) {
    if(duration < 60) {
      return duration + ' mins';
    } else {
      var hr = duration / 60;
      return parseInt(hr) + ' hr ' + (duration % 60) + ' mins';
    }
  }

  function getAvailableStaffQuota(staff_id){
    if(staff_id == 0) {
        $("#quota").html('');
        return false;
    }
    $.ajax({
        url: '<?php echo site_url('staff/leaves/getAvailableStaffQuota'); ?>',
        type: 'post',
        data: {'staff_id':staff_id},
        success: function(data) {
            var data = JSON.parse(data);
            // console.log(data);
            // var available = data.available_quota;
            if(data.length == 0) {
                window.sessionStorage.setItem("isLeaveQuotaAvailable",0);

                $("#quota").html('<span style="position: relative;left: 50%;transform: translateX(-50%);" class="text-danger">Leave Quota not available.</span>');
                return false;
            }

            window.sessionStorage.setItem("isLeaveQuotaAvailable",1);

            var html = '<div class="d-flex frb-group" style="display: flex!important;flex-wrap: wrap;justify-content: flex-start;align-items: center;">';
            if(is_mobile) {
                html = '<div class="d-flex flex-column frb-group">';
            }
            for(var i in data) {
                var available_quota = data[i].total_quota - data[i].used_quota;
                var is_disabled = (available_quota==0 && data[i].has_quota==1)?'disabled':'';
                html += '<div class="frb frb-primary '+is_disabled+'">';
                html += '<input data-hasquota="'+data[i].has_quota+'" data-available="'+available_quota+'" class="leave_category" '+is_disabled+' type="radio" id="'+data[i].short_name+'" name="leave-category" value="'+data[i].id+'">';
                html += '<label for="'+data[i].short_name+'">'
                html += '<span class="frb-title">'+data[i].name+' ('+data[i].short_name+')</span>';
                if(data[i].has_quota == 1) {
                    html += '<span class="frb-description">'+(available_quota)+' / '+parseFloat(data[i].total_quota)+' available.</span>';
                } else {
                    html += '<span class="frb-description">Unlimited</span>';
                }
                html += '</label>';
                html += '</div>';
            }
            html += '</div>';
            $("#quota").html(html);
        }
    });
  }
</script>

<style type="text/css">
  .absent.active {
    background: #E04B4A !important;
    color:white !important;
  }
  .halfday.active {
    background: #1caf9a !important;
    color:white !important;
  }

  .modal{
        z-index: 1061;
  }
  .weekoff.active, .holiday.active {
    background: #f39b19 !important;
    color:white !important;
  }
  .present.active {
    background: #95b75d !important;
    color:white !important;
  }
  .ood.active {
    background: #95b75d !important;
    color:white !important;
  }
  .late.active {
    background: #e8b61d !important;
    color:white !important;
  }
  .leave.active {
    background: #1984f3 !important;
    color:white !important;
  }
  .label-late {
    background: #e8b61d !important;
  }
  span{
    cursor: pointer;
  }
  .approvals {
    width: 1.5rem;
    height: 1.5rem;
  }

.frb-group {
    margin: 5px 0;
}

.frb ~ .frb {
    /*margin-top: 15px;*/
}
.frb {
    padding: 5px;
}

.frb input[type="radio"]:empty,
.frb input[type="checkbox"]:empty {
    display: none;
}

.frb input[type="radio"] ~ label:before,
.frb input[type="checkbox"] ~ label:before {
    font-family: FontAwesome;
    content: '\f096';
    position: absolute;
    top: 50%;
    margin-top: -11px;
    left: 15px;
    font-size: 22px;
}

.frb input[type="radio"]:checked ~ label:before,
.frb input[type="checkbox"]:checked ~ label:before {
    content: '\f046';
}

.frb input[type="radio"] ~ label,
.frb input[type="checkbox"] ~ label {
    position: relative;
    cursor: pointer;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f2f2f2;
}

.frb input[type="radio"] ~ label:focus,
.frb input[type="radio"] ~ label:hover,
.frb input[type="checkbox"] ~ label:focus,
.frb input[type="checkbox"] ~ label:hover {
    box-shadow: 0px 0px 3px #333;
}

.frb input[type="radio"]:checked ~ label,
.frb input[type="checkbox"]:checked ~ label {
    color: #fafafa;
}

.frb input[type="radio"]:checked ~ label,
.frb input[type="checkbox"]:checked ~ label {
    background-color: #f2f2f2;
}

.frb.frb-default input[type="radio"]:checked ~ label,
.frb.frb-default input[type="checkbox"]:checked ~ label {
    color: #333;
}

.frb.frb-primary input[type="radio"]:checked ~ label,
.frb.frb-primary input[type="checkbox"]:checked ~ label {
    background-color: #337ab7;
}

.frb.frb-success input[type="radio"]:checked ~ label,
.frb.frb-success input[type="checkbox"]:checked ~ label {
    background-color: #5cb85c;
}

.frb.frb-info input[type="radio"]:checked ~ label,
.frb.frb-info input[type="checkbox"]:checked ~ label {
    background-color: #5bc0de;
}

.frb.frb-warning input[type="radio"]:checked ~ label,
.frb.frb-warning input[type="checkbox"]:checked ~ label {
    background-color: #f0ad4e;
}

.frb.frb-danger input[type="radio"]:checked ~ label,
.frb.frb-danger input[type="checkbox"]:checked ~ label {
    background-color: #d9534f;
}

.frb.disabled input[type="radio"] ~ label{
    cursor: no-drop;
    background-color: #dadbdd;
}

.frb input[type="radio"]:empty ~ label span,
.frb input[type="checkbox"]:empty ~ label span {
    display: inline-block;
}

.frb input[type="radio"]:empty ~ label span.frb-title,
.frb input[type="checkbox"]:empty ~ label span.frb-title {
    font-size: 16px;
    font-weight: 700;
    margin: 5px 5px 5px 50px;
}

.frb input[type="radio"]:empty ~ label span.frb-description,
.frb input[type="checkbox"]:empty ~ label span.frb-description {
    font-weight: normal;
    font-style: italic;
    color: #999;
    margin: 5px 5px 5px 50px;
}

.frb input[type="radio"]:empty:checked ~ label span.frb-description,
.frb input[type="checkbox"]:empty:checked ~ label span.frb-description {
    color: #fafafa;
}

.frb.frb-default input[type="radio"]:empty:checked ~ label span.frb-description,
.frb.frb-default input[type="checkbox"]:empty:checked ~ label span.frb-description {
    color: #999;
}

.modal-dialog{
  width:50%;
  margin:auto;
}

.swal2-validation-message {
  margin:0
}

.swal2-modal{
  width:30%;
}
</style>