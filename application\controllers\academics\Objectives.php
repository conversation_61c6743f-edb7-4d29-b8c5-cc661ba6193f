<?php

class Objectives extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
      $this->load->library('filemanager');
      $this->load->model('academics/objective_model');
  }

  public function index() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/objectives/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/objectives/index_mobile';
    }else{
      $data['main_content'] = 'academics/objectives/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function getAllObjectives(){
    $viewObjectives = $this->objective_model->getObjectives();
    echo json_encode($viewObjectives);
  }

  public function submitObjective(){
    $objective_name = $_POST['objective_name'];
    $objective_description = $_POST['objective_description'];
    $data = $this->objective_model->submitObjective($objective_name, $objective_description);
    echo ($data);
  }
}