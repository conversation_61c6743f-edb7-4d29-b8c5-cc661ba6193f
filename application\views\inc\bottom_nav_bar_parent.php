<div class="navbar">
  <a href="#" id="bottom-back-button" class="d-flex align-items-center"><i class="fa fa-caret-left" aria-hidden="true"></i><span class="ml-3" style="font-size:20px">Back</span></a>
  <a href="<?php echo site_url('dashboard') ?>" class="d-flex align-items-center"><i class="fa fa-home" aria-hidden="true"></i><span class="ml-3" style="font-size:20px">Home</span></a>
</div>

<script>
    $("#bottom-back-button").click(function(){
        var backBtn = $("#backBtn");
        if (backBtn.length === 0 || backBtn.attr('href') === undefined) {
          window.location = '<?php echo site_url("dashboard"); ?>';
            // if (window.history.length > 1) {
            //     window.history.back();
            // } else {
            //     window.location = '<?php //echo site_url("dashboard"); ?>';
            // }
        } else {
            backBtn.click();
        }
    });
    //$(document).ready(function(){
        //var href = $("#backBtn").attr('href');
        //$("#bottom-back-button").attr('href', href);
    //});
</script>

<style type="text/css">
.navbar {
  overflow: hidden;
  background-color: #fff;
  position: fixed;
  bottom: 6px;
  width: 75%;
  margin-bottom: 0px;
  box-shadow: 0px 0px 6px #c3c3c3;
  /* height: 55px; */
  padding: 0px;
  z-index: 5;
  left:12.5%;
  border-radius:24px;
}

.navbar a:disabled {
  pointer-events: none; 
  color: #eee
}

.navbar a {
  float: left;
  display: block;
  color: #444444;
  text-align: center;
  padding: 0px 10px;
  text-decoration: none;
  font-size: 12px;
}

.navbar a:hover {
  /* background: #f1f1f1; */
  color: #ff6806;
}
.navbar a.active {
    /* background-color: #6893CA; */
    color: #6893CA;
    /* border-radius: 8px; */
    font-size: 14px;
    font-weight: bold;
    pointer-events: none;
    padding-bottom: 0px;
}
p
{
  margin-bottom: 0px;
  margin-top: -3px;
}
.navbar a i {
    font-size: 22px;
}
.navbar a i.active {
    font-size: 25px;
}
.disabled_new{
  color: #eee !important;
}
</style>