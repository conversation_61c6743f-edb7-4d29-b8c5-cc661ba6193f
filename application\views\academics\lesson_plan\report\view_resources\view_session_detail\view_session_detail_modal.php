<div class="modal" tabindex="-1" role="dialog" id="full_session_detail">
    <div class="modal-dialog modal-dialog-scrollable" role="document" >
        <div class="modal-content" style="border-radius:1rem; width: 80%; margin-top: 1% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title"><span id="session-name"></span> Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body" id="view_full_session_detail_data">
                <div class="session-overview mb-4">
                    <div class="panel-heading px-1">
                    <h1 class="panel-title">
                            <strong>
                                Subject: <span id="show_subject_name" class="text-primary">Mathematics</span>
                                <i class="fa fa-arrow-right mx-2"></i>
                                Lesson: <span id="show_lesson_name" class="text-primary">Numbers</span>
                            </strong>
                        </h1>
                    </div>
                </div>

                <div class="session-details">
                    <div class="topic-section mb-4">
                        <div class="alert alert-info">
                            <h5 class="text-center mb-0">
                                <strong>Topic: <span id="show_topic_name">Practical uses of Complex Numbers</span></strong>
                            </h5>
                        </div>
                    </div>

                    <div class="session-content">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <!-- Meta Data Section -->
                                <tr class="category-header">
                                    <td colspan="2" class="bg-primary text-white">
                                        <strong>Meta Data</strong>
                                    </td>
                                </tr>
                                <tr id="learning_context_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Learning Context</td>
                                    <td id="show_learning_context"></td>
                                </tr>
                                <tr id="learningObjectiveType_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Learning Objectives</td>
                                    <td>
                                        <div class="show_learning_objective_type"></div>
                                    </td>
                                </tr>
                                <tr id="learning_intention_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Learning Intention</td>
                                    <td id="show_learning_intention"></td>
                                </tr>
                                <tr id="skillType_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Skills</td>
                                    <td>
                                        <div class="show_skillType"></div>
                                    </td>
                                </tr>
                                <tr id="success_criteria_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Success Criteria</td>
                                    <td id="show_success_criteria"></td>
                                </tr>

                                <!-- Execution Plan Section -->
                                <tr class="category-header">
                                    <td colspan="2" class="bg-primary text-white">
                                        <strong>Execution Plan</strong>
                                    </td>
                                </tr>
                                <tr id="beginning_plan_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Beginning Plan</td>
                                    <td>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-clock-o mr-2"></i>
                                            <span id="show_beginning_minute"></span>
                                        </div>
                                        <div id="show_beginning_plan"></div>
                                    </td>
                                </tr>
                                <tr id="middle_plan_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Mid Plan</td>
                                    <td>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-clock-o mr-2"></i>
                                            <span id="show_middle_minute"></span>
                                        </div>
                                        <div id="show_middle_plan"></div>
                                    </td>
                                </tr>
                                <tr id="end_plan_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">End Plan</td>
                                    <td>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-clock-o mr-2"></i>
                                            <span id="show_end_minute"></span>
                                        </div>
                                        <div id="show_end_plan"></div>
                                    </td>
                                </tr>
                                <tr id="extended_learning_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Extended Learning</td>
                                    <td id="show_extended_learning"></td>
                                </tr>
                                <tr id="contingency_plan_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Contingency Plan</td>
                                    <td id="show_contingency_plan"></td>
                                </tr>

                                <!-- Resources Section -->
                                <tr class="category-header">
                                    <td colspan="2" class="bg-primary text-white">
                                        <strong>Resources</strong>
                                    </td>
                                </tr>
                                <tr id="resourceType_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Digital Resources</td>
                                    <td>
                                        <div class="heading-elements"></div>
                                    </td>
                                </tr>
                                <tr id="book_resourceType_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Book References</td>
                                    <td>
                                        <div class="book-resource-type"></div>
                                    </td>
                                </tr>
                                <tr id="additional_information_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Additional Information</td>
                                    <td id="show_additional_information"></td>
                                </tr>
                                <tr id="assessmentType_td" style="display: none;">
                                    <td class="bg-light fw-bold" style="width:25%">Assessments</td>
                                    <td>
                                        <div class="show_assessments"></div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
    .grey {
        color: grey;
    }
    
    .category-header td {
        padding: 0.75rem 1rem;
        font-size: 1.1rem;
    }
    
    .bg-primary {
        background-color: #3498db !important;
    }
    
    .text-white {
        color: #fff !important;
    }

    /* New styles for the updated UI */
    .objective-card, .skill-card, .assessment-card {
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .objective-card:hover, .skill-card:hover, .assessment-card:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .objective-title, .skill-title, .assessment-title {
        font-weight: 600;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        border-top: none;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .btn-outline-primary, .btn-outline-success {
        border-width: 1px;
    }

    .btn-outline-primary:hover, .btn-outline-success:hover {
        transform: translateY(-1px);
    }
</style>

<script>
    $("#full_session_detail").on("shown.bs.modal", e => {
        // getSessionData(session_id);
        // show_session_detail();
        // $("#session-name").html(`<strong>${session_name}</strong>`);

        const session_name= $("#select_session option:selected").text();

        $("#session-name").html(`<strong>${session_name}</strong>`);

        const subjectName = $("#select_subject option:selected").text();
        $("#show_subject_name").text(subjectName);

        const lessonName = $("#select_lesson option:selected").text();
        $("#show_lesson_name").text(lessonName);

        const topicName = $("#select_topic option:selected").text();
        $("#show_topic_name").text(topicName);
    })

    function show_session_detail() {
        // getSessionData(session_id);s
        if (session_details?.learning_context) {
            $("#show_learning_context").text(`${session_details?.learning_context}`).removeClass("grey");
        } else {
            $("#show_learning_context").text(`Not added`).addClass("grey");
        }

        if (session_details?.learning_intention) {
            $(`#show_learning_intention`).text(session_details?.learning_intention).removeClass("grey");
        } else {
            $(`#show_learning_intention`).text(`Not added`).addClass("grey");
        }

        if (session_details?.success_criteria) {
            $(`#show_success_criteria`).text(session_details?.success_criteria).removeClass("grey");
        } else {
            $(`#show_success_criteria`).text(`Not added`).addClass("grey");
        }

        if (session_details?.extended_learning) {
            $(`#show_extended_learning`).text(session_details?.extended_learning).removeClass("grey");
        } else {
            $(`#show_extended_learning`).text(`Not added`).addClass("grey");
        }

        if (session_details?.contingency_plan) {
            $(`#show_contingency_plan`).text(session_details?.contingency_plan).removeClass("grey");
        } else {
            $(`#show_contingency_plan`).text(`Not added`).addClass("grey");
        }

        if (session_details?.beginning_plan) {
            $(`#show_beginning_plan`).text(session_details?.beginning_plan).removeClass("grey");
        } else {
            $(`#show_beginning_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.beginning_minute) {
            $(`#show_beginning_minute`).text(session_details?.beginning_minute + " minute(s)").removeClass("grey");
        } else {
            $(`#show_beginning_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.middle_plan) {
            $(`#show_middle_plan`).text(session_details?.middle_plan).removeClass("grey");
        } else {
            $(`#show_middle_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.middle_minute) {
            $(`#show_middle_minute`).text(session_details?.middle_minute + " minute(s)").removeClass("grey");
        } else {
            $(`#show_middle_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.end_plan) {
            $(`#show_end_plan`).text(session_details?.end_plan).removeClass("grey");
        } else {
            $(`#show_end_plan`).text(`Not added`).addClass("grey");
        }

        if (+session_details?.end_minute) {
            $(`#show_end_minute`).text(session_details?.end_minute + " minute(s)").removeClass("grey");
        } else {
            $(`#show_end_minute`).text(`Not added`).addClass("grey");
        }

        if (session_details?.additional_information) {
            $(`#show_additional_information`).text(session_details?.additional_information).removeClass("grey");
        } else {
            $(`#show_additional_information`).text(`Not added`).addClass("grey");
        }

        if (learningObjectiveType?.length) {
            let html = `<div class="learning-objectives-container">`;
            learningObjectiveType.forEach(l => {
                html += `<div class="objective-card mb-3 p-3 border rounded">
                    <h6 class="objective-title mb-2 text-primary">${l.objective_name}</h6>
                    <p class="objective-description mb-0">${l.manage_objective_description}</p>
                </div>`;
            })
            html += `</div>`;
            $(".show_learning_objective_type").html(html).removeClass("grey");
        } else {
            $(".show_learning_objective_type").text(`Not added`).addClass("grey");
        }

        if (skillType?.length) {
            let html = `<div class="skills-container">`;
            skillType.forEach(s => {
                html += `<div class="skill-card mb-3 p-3 border rounded">
                    <h6 class="skill-title mb-2 text-primary">${s.skill_name}</h6>
                    <p class="skill-description mb-0">${s.skill_description}</p>
                </div>`;
            })
            html += `</div>`;
            $(".show_skillType").html(html).removeClass("grey");
        } else {
            $(".show_skillType").text(`Not added`).addClass("grey");
        }

        if (assessmentType?.length) {
            let html = `<div class="assessments-container">`;
            assessmentType.forEach(a => {
                html += `<div class="assessment-card mb-3 p-3 border rounded">
                    <h6 class="assessment-title mb-2 text-primary">${a.name}</h6>
                    <p class="assessment-description mb-0">${a.assessment_remarks}</p>
                </div>`;
            })
            html += `</div>`;
            $(".show_assessments").html(html).removeClass("grey");
        } else {
            $(".show_assessments").text(`Not added`).addClass("grey");
        }

        if (resourceType?.length) {
            let html = `<div class="resources-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>Resource Name</th>
                                <th>Type</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>`;

            resourceType.forEach(r => {
                const resourceURL = r.resource_url.replaceAll("/", "-");
                const avoidDownloadRerourceTypes = ["Other", "Hyper Link", "Video Link"];
                const linkUrl = "https://" + r.resource_url.split("https://").at(-1);
                
                html += `<tr>
                    <td class="align-middle">${r.name}</td>
                    <td class="align-middle"><span class="badge badge-info">${r.resource_type}</span></td>
                    <td class="text-center">
                        <a href="${avoidDownloadRerourceTypes.includes(r.resource_type) && linkUrl || r.resource_url}" target="_blank" class="btn btn-sm btn-primary mr-1">
                            <i class="fa fa-eye mr-0"></i>
                        </a>
                        <a href="<?php echo site_url('academics/Lesson_plan/download_LP_resource') ?>/${r.name}/${resourceURL}" style="display:${avoidDownloadRerourceTypes.includes(r.resource_type) && "none"}" class="btn btn-sm btn-success">
                            <i class="fa fa-download mr-0"></i>
                        </a>
                    </td>
                </tr>`;
            });
            
            html += `</tbody></table></div></div>`;
            $(".heading-elements").html(html);
        } else {
            $(".heading-elements").addClass("grey");
            $(".heading-elements").text(`Not added`).addClass("grey");
        }

        if (book_resourceType?.length) {
            let html = `<div class="book-references-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th width="40%">Book Name</th>
                                <th width="60%">References</th>
                            </tr>
                        </thead>
                        <tbody>`;

            book_resourceType.forEach(b => {
                html += `<tr>
                    <td class="align-middle"><strong>${b.book_name}</strong></td>
                    <td class="align-middle">${b.reference_detail}</td>
                </tr>`;
            });
            
            html += `</tbody></table></div></div>`;
            $(".book-resource-type").html(html).removeClass("grey");
        } else {
            $(".book-resource-type").text(`Not added`).addClass("grey");
        }
    }

    var totalActivities = [
        "learning_context",
        "learningObjectiveType",
        "learning_intention",
        "skillType",
        "success_criteria",
        "beginning_plan",
        "middle_plan",
        "end_plan",
        "contingency_plan",
        "extended_learning",
        "resourceType",
        "book_resourceType",
        "additional_information",
        "assessmentType",
    ];

    var enabled_activities_for_lms = <?php echo json_encode($this->settings->getSetting("enable_lms_activities")) ?>;

    if(enabled_activities_for_lms==false){
        enabled_activities_for_lms=totalActivities;
    }
    
    var enabledActiviesObj = {};
    if (enabled_activities_for_lms?.length) {
        $("#message").hide();
        enabled_activities_for_lms.forEach(enabledActivity => {
            enabledActiviesObj[enabledActivity] = enabledActivity;
        })
    } else {
        $("#message").show();
    }

    totalActivities.forEach(activity => {
        if (activity in enabledActiviesObj) {
            $(`#${activity}_td`).show();
        } else {
            $(`#${activity}_id`).hide();
        }
    });
</script>