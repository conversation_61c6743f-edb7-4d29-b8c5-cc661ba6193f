<?php 
   $isMobile = $this->mobile_detect->isMobile();
?>
<div class="panel-body" id="income_tax_submited" style="display:none;">
   <div>
      <?php if($staff_details->has_tax_regime_changed == 1){ ?>
         <div style="background: linear-gradient(to right, #fff5f5, #ffeaea); color: #a80000; padding: 15px 20px; border-left: 6px solid #ff4d4d; border-radius: 6px; font-weight: 500; font-size: 15px; margin-bottom: 20px; line-height: 1.6;">
            <strong>🚨 Important:</strong><br>
            The <strong>Tax Regime</strong> you opted for has been <strong>changed by <?php echo $staff_details->regime_changed_by; ?></strong> on <strong><?php echo $staff_details->regime_changed_on; ?></strong>.<br>
            <strong>Remarks:</strong> <?php echo $staff_details->regime_change_remarks; ?><br>
            <em>For further information, please contact the Accounts Department.</em>
         </div>
      <?php } ?>
      <table class="table table-bordered" style="width:40%">
         <tr>
            <td style="vertical-align:middle;" width=60%>Selected Regime</td>
            <td style="color: blue;font-size:16px;" width=40%><input type="text"  style="border:none;" readonly value="<?= $staff_details->tax_regime_name ?>"></td>
         </tr>
         <tr>
            <td>Taxable Salary </td>
            <td><input type="text" style="border:none;" readonly value="<?php echo formatIndianCurrency($staff_details->taxable_salary); ?>"></td>
         </tr>
         <tr>
            <td>TDS </td>
            <td><input type="text" style="border:none;" readonly value="<?php echo formatIndianCurrency($staff_details->total_declared_tds) ?>"></td>
         </tr>
         <tr>
            <td><?= $staff_details->tax_regime_name ?> Calculation </td>
            <td><button class="btn btn-primary" title="Vew Regime Calculation" onclick="get_mytds_slip()">View Calculation</button></td>
         </tr>
         <tr>
            <td>Tax Regime Comparison</td>
            <td><button class="btn btn-primary" title="Vew Regime Calculation" onclick="getTaxCalculationsComparison()">View Comparison</button></td>
         </tr>
         <?php if($this->settings->getSetting('enable_staff_agreement_to_tds_payroll') == 1){?>
            <tr>
               <td>TDS Agreement</td>
               <?php if($staff_details->tds_agreed_reopen_status == 1 && $staff_details->tds_status == "Approved"){?>
                  <td><span style="color: green;"><b>Agreed</b></span> <button class="btn btn-primary" title="Confirm Agreement" onclick='get_monthly_payslip_and_view("<?php echo ($staff_details->total_declared_tds) ?>", "<?= $staff_details->tax_regime_name ?>")'>View</button></td>
               <?php } else if($staff_details->tds_agreed_reopen_status == 2){?>
                  <td><span style="color: red;"><b>Request To Re-open TDS Submitted</b></span></td>
               <?php } else if($staff_details->tds_status == "Approved") {?>
                  <td><button class="btn btn-primary" title="Confirm Agreement" onclick='get_monthly_payslip_and_agreement("<?php echo ($staff_details->total_declared_tds) ?>", "<?= $staff_details->tax_regime_name ?>")'>View and Agree</button></td>
               <?php } else { ?>
                  <td>TDS Approval Is In Progress.</td>
               <?php }?>
            </tr>
         <?php }?>
      </table>
   </div>
   <div <?php echo ($isMobile ? 'style="overflow-x: auto;"' : ''); ?>>
      <table class="table table-bordered">
         <tr>
            <td colspan="4"><b>Perquisite Income</b></td>
         </tr>
         <tr>
            <td width="35%">Availing Company Accommodation?</td>
            <?php
               if (isset($attachments['availing_company_accommodation']) && $staff_details->availing_company_accommodation == 1) {
                  $attachment = $attachments['availing_company_accommodation'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . ($staff_details->availing_company_accommodation == 0 ? "No" : "Yes") . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'availing_company_accommodation\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . ($staff_details->availing_company_accommodation == 0 ? "No" : "Yes") . '"></td>';
               }
            ?>
         </tr>
         <?php if (!empty($staff_details->house_rent)) : ?>
            <tr>
               <td colspan="5"><b>DECLARATION FOR HOUSE RENT ALLOWANCE RELIEF</b></td>
            </tr>
            <tr>
                  <td colspan="3">
                     <table style="margin: 0 auto;">
                        <tr>
                              <th  style="width: 10%">#</th>
                              <th  style="width: 30%">From Date</th>
                              <th  style="width: 30%">To Date</th>
                              <th  style="width: 22%">Rent Per Month</th>
                              <th  style="width: 20%">Total Rent</th>
                        </tr>
                        <?php foreach ($staff_details->house_rent as $key => $rent) : ?>
                              <tr>
                                 <td><?= $key + 1 ?></td>
                                 <td><?= format_month_year($rent['from_date']) ?? '' ?></td>
                                 <td><?= format_month_year($rent['to_date']) ?? '' ?></td>
                                 <td><?= formatIndianCurrency($rent['monthly_amount']) ?></td>
                                 <td><?= formatIndianCurrency($rent['rent_amount_cal']) ?></td>
                              </tr>
                        <?php endforeach; ?>
                     </table>
                  </td>
                  <td colspan="2">
                     <table style="margin: 0 auto;">
                        <tr>
                              <th>Grand Total Rent</th>
                        </tr>
                        <tr>
                           <?php
                              if (isset($attachments['grand_total_rent']) && $staff_details->grand_total_rent > 0) {
                                 $rentProof = $attachments['grand_total_rent'];
                                 echo '<td class="d-flex justify-content-between align-items-center">
                                          <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->grand_total_rent) . '">';
                                    if($staff_details->grand_total_rent > 100000 && isset($attachments['landlord_pan_card'])){
                                       $landLordProof = $attachments['landlord_pan_card'];
                                       echo '<button class="btn btn-primary mr-2" onclick="viewAttachment(\'' . $landLordProof->proof_file_url . '\', \'landlord_pan_card\')" title="' . $landLordProof->file_name . '">
                                                <i class="fa fa-paperclip mr-0"></i>
                                             </button>
                                             <button class="btn btn-primary" onclick="viewAttachment(\'' . $rentProof->proof_file_url . '\', \'grand_total_rent\')" title="' . $rentProof->file_name . '">
                                                <i class="fa fa-paperclip mr-0"></i>
                                             </button>';
                                    } else {
                                       echo '<button class="btn btn-primary" onclick="viewAttachment(\'' . $rentProof->proof_file_url . '\', \'grand_total_rent\')" title="' . $rentProof->file_name . '">
                                                <i class="fa fa-paperclip mr-0"></i>
                                             </button>';
                                    }
                                 echo '</td>';
                              } else {
                                 echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->grand_total_rent) . '"></td>';
                              }
                           ?>
                        </tr>
                     </table>
                  </td>
            </tr>
         <?php else : ?>
            <!-- <tr>
                  <td colspan="5"><b>No details added</b></td>
            </tr> -->
         <?php endif; ?>
         <tr>
            <td colspan="4"><b>80C Investments </b></td>
         </tr>
         <tr>
            <td width="35%">EPF & VPF Contribution </td>
            <td width="15%"><input type="text" style="border:none;" readonly value="<?php echo formatIndianCurrency($pf_epf->total_pf_epf) ?>"></td>
            <td width="35%">Public Provident Fund (PPF) </td>
            <?php
               if (isset($attachments['public_provident_fund']) && $staff_details->public_provident_fund > 0) {
                  $attachment = $attachments['public_provident_fund'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->public_provident_fund) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'public_provident_fund\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->public_provident_fund) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>N.S.C (Investment + accrued Interest before Maturity Year)</td>
            <?php
               if (isset($attachments['nsc_investment']) && $staff_details->nsc_investment > 0) {
                  $attachment = $attachments['nsc_investment'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->nsc_investment) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'nsc_investment\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->nsc_investment) . '"></td>';
               }
            ?>
            <td>Tax Saving Fixed Deposit (5 Years and above) </td>
            <?php
               if (isset($attachments['tax_saving_fixed_deposit']) && $staff_details->tax_saving_fixed_deposit > 0) {
                  $attachment = $attachments['tax_saving_fixed_deposit'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->tax_saving_fixed_deposit) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'tax_saving_fixed_deposit\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->tax_saving_fixed_deposit) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>E.L.S.S (Tax Saving Mutual Fund)</td>
            <?php
               if (isset($attachments['elss_mutual_fund']) && $staff_details->elss_mutual_fund > 0) {
                  $attachment = $attachments['elss_mutual_fund'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->elss_mutual_fund) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'elss_mutual_fund\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->elss_mutual_fund) . '"></td>';
               }
            ?>
            <td>Life Insurance Premiums paid </td>
            <?php
               if (isset($attachments['life_insurance']) && $staff_details->life_insurance > 0) {
                  $attachment = $attachments['life_insurance'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->life_insurance) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'life_insurance\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->life_insurance) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>New Pension Scheme (NPS) (U/S 80CCC) </td>
            <?php
               if (isset($attachments['new_pension_scheme']) && $staff_details->new_pension_scheme > 0) {
                  $attachment = $attachments['new_pension_scheme'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->new_pension_scheme) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'new_pension_scheme\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->new_pension_scheme) . '"></td>';
               }
            ?>
            <td>Pension Plan from Insurance Co./Mutual Funds (u/s 80CCC) </td>
            <?php
               if (isset($attachments['pension_plan_for_insurance']) && $staff_details->pension_plan_for_insurance > 0) {
                  $attachment = $attachments['pension_plan_for_insurance'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->pension_plan_for_insurance) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'pension_plan_for_insurance\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->pension_plan_for_insurance) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>Principal Repayment on House Building Loan </td>
            <?php
               if (isset($attachments['principal_repayment_house_loan']) && $staff_details->principal_repayment_house_loan > 0) {
                  $attachment = $attachments['principal_repayment_house_loan'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->principal_repayment_house_loan) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'principal_repayment_house_loan\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->principal_repayment_house_loan) . '"></td>';
               }
            ?>
            <td>Sukanya Samriddhi Yojana </td>
            <?php
               if (isset($attachments['sukanya_samriddhi_yojana']) && $staff_details->sukanya_samriddhi_yojana > 0) {
                  $attachment = $attachments['sukanya_samriddhi_yojana'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->sukanya_samriddhi_yojana) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'sukanya_samriddhi_yojana\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->sukanya_samriddhi_yojana) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>Stamp Duty & Registration Fees on House Buying</td>
            <?php
               if (isset($attachments['stamp_duty_registration_fees']) && $staff_details->stamp_duty_registration_fees > 0) {
                  $attachment = $attachments['stamp_duty_registration_fees'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->stamp_duty_registration_fees) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'stamp_duty_registration_fees\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->stamp_duty_registration_fees) . '"></td>';
               }
            ?>
            <td>Tuition Fees for Children(max 2 Children) </td>
            <?php
               if (isset($attachments['tution_fees_for_children']) && $staff_details->tution_fees_for_children > 0) {
                  $attachment = $attachments['tution_fees_for_children'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->tution_fees_for_children) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'tution_fees_for_children\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->tution_fees_for_children) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>Other 80C Investments</td>
            <?php
               if (isset($attachments['other_80c_investments']) && $staff_details->other_80c_investments > 0) {
                  $attachment = $attachments['other_80c_investments'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_80c_investments) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'other_80c_investments\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_80c_investments) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td colspan="4"><b>80C Other Investments & Exemptions</b></td>
         </tr>
         <tr>
            <td>Additional Deduction for NPS U/S 80CCD(1B) - Max 50000</td>
            <?php
               if (isset($attachments['additional_deducation_for_nps']) && $staff_details->additional_deducation_for_nps > 0) {
                  $attachment = $attachments['additional_deducation_for_nps'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->additional_deducation_for_nps) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'additional_deducation_for_nps\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->additional_deducation_for_nps) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td colspan="4"><b>80D Exemptions</b></td>
         </tr>
         <tr>
            <td>Age of Self</td>
            <td><input type="text" style="border:none;" readonly value="<?php echo ($staff_details->self_age == "above_60" ? "Above 60" : "Below 60") ?>"></td>
            <td>Age of Parents</td>
            <td><input type="text" style="border:none;" readonly value="<?php echo ($staff_details->parents_age == "above_60" ? "Above 60" : "Below 60" ) ?>"></td>
         </tr>
         <tr>
            <td>80D Preventive Health Checkup </td>
            <?php
               if (isset($attachments['preventive_health_checkup_80d']) && $staff_details->preventive_health_checkup_80d > 0) {
                  $attachment = $attachments['preventive_health_checkup_80d'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->preventive_health_checkup_80d) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'preventive_health_checkup_80d\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->preventive_health_checkup_80d) . '"></td>';
               }
            ?>
            <td>80D Preventive Health Checkup for Parents </td>
            <?php
               if (isset($attachments['preventive_health_checkup_parents_80d']) && $staff_details->preventive_health_checkup_parents_80d > 0) {
                  $attachment = $attachments['preventive_health_checkup_parents_80d'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->preventive_health_checkup_parents_80d) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'preventive_health_checkup_parents_80d\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->preventive_health_checkup_parents_80d) . '"></td>';
               }
            ?>
         </tr>
         <tr <?php echo $staff_details->self_age == 'above_60' && $staff_details->parents_age == 'above_60' ? 'class="d-none"' : ''?>>
            <?php if($staff_details->self_age == 'below_60'){?>
               <td>80D Medical Insurance Premium (for Self, Spouse & Children)</td>
               <?php
                  if (isset($attachments['eightyd_medical_insurance_premium_self']) && $staff_details->medical_insurance_premium_self_80d > 0) {
                     $attachment = $attachments['eightyd_medical_insurance_premium_self'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_self_80d) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightyd_medical_insurance_premium_self\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_self_80d) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Insurance Premium (for Self, Spouse & Children)</td>
               <td class="textMuted"><input type="text" style="border:none;" readonly value="0"></td>
            <?php }?>

            <?php if($staff_details->parents_age == 'below_60'){?>
               <td>80D Medical Insurance Premium (for Parents)</td>
               <?php
                  if (isset($attachments['eightyd_medical_insurance_premium_parent']) && $staff_details->medical_insurance_premium_parent_80d > 0) {
                     $attachment = $attachments['eightyd_medical_insurance_premium_parent'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_parent_80d) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightyd_medical_insurance_premium_parent\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_parent_80d) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Insurance Premium (for Parents)</td>
               <td class="textMuted"><input type="text" style="border:none;" readonly value="0"></td>
            <?php }?>
         </tr>
         <tr <?php echo $staff_details->self_age == 'below_60' && $staff_details->parents_age == 'below_60' ? 'class="d-none"' : ''?>>
            <?php if($staff_details->self_age == 'above_60'){?>
               <td>80D Medical Bills for Self, Spouse, Children - Senior Citizen </td>
               <?php
                  if (isset($attachments['medical_bills_for_self_senior']) && $staff_details->medical_bills_for_self_senior > 0) {
                     $attachment = $attachments['medical_bills_for_self_senior'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_bills_for_self_senior) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'medical_bills_for_self_senior\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_bills_for_self_senior) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Bills for Self, Spouse, Children - Senior Citizen </td>
               <td class="textMuted"><input type="text"  style="border:none;" readonly value="0"></td>
            <?php }?>

            <?php if($staff_details->parents_age == 'above_60'){?>
               <td>80D Medical Bills for Parents - Senior Citizen </td>
               <?php
                  if (isset($attachments['medical_bills_for_parents_senior']) && $staff_details->medical_bills_for_parents_senior > 0) {
                     $attachment = $attachments['medical_bills_for_parents_senior'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_bills_for_parents_senior) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'medical_bills_for_parents_senior\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_bills_for_parents_senior) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Bills for Parents - Senior Citizen </td>
               <td class="textMuted"><input type="text"  style="border:none;" readonly value="0"></td>
            <?php }?>
         </tr>
         <tr <?php echo $staff_details->self_age == 'below_60' && $staff_details->parents_age == 'below_60' ? 'class="d-none"' : ''?>>
            <?php if($staff_details->self_age == 'above_60'){?>
               <td>80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen </td>
               <?php
                  if (isset($attachments['medical_insurance_premium_self_80d_senior']) && $staff_details->medical_insurance_premium_self_80d_senior > 0) {
                     $attachment = $attachments['medical_insurance_premium_self_80d_senior'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_self_80d_senior) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'medical_insurance_premium_self_80d_senior\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_self_80d_senior) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Insurance Premium (for Self, Spouse & Children) - Senior Citizen </td>
               <td class="textMuted"><input type="text"  style="border:none;" readonly value="0"></td>
            <?php }?>

            <?php if($staff_details->parents_age == 'above_60'){?>
               <td>80D Medical Insurance premium (for Parents) - Senior Citizen </td>
               <?php
                  if (isset($attachments['medical_insurance_premium_parent_80d_senior']) && $staff_details->medical_insurance_premium_parent_80d_senior > 0) {
                     $attachment = $attachments['medical_insurance_premium_parent_80d_senior'];
                     echo '<td class="d-flex justify-content-between align-items-center">
                              <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_parent_80d_senior) . '">
                              <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'medical_insurance_premium_parent_80d_senior\')" title="' . $attachment->file_name . '">
                                 <i class="fa fa-paperclip mr-0"></i>
                              </button>
                           </td>';
                  } else {
                     echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_insurance_premium_parent_80d_senior) . '"></td>';
                  }
               ?>
            <?php }else{?>
               <td class="textMuted">80D Medical Insurance premium (for Parents) - Senior Citizen </td>
               <td class="textMuted"><input type="text"  style="border:none;" readonly value="0"></td>
            <?php }?>
         </tr>
         <tr>
            <td colspan="4"><strong>Other Investments & Exemptions</strong> </td>
         </tr>
         <tr>
            <td>80E Interest Paid on Education Loan</td>
            <?php
               if (isset($attachments['eightye_interest_paid_education']) && $staff_details->interest_paid_education_80e > 0) {
                  $attachment = $attachments['eightye_interest_paid_education'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->interest_paid_education_80e) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightye_interest_paid_education\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->interest_paid_education_80e) . '"></td>';
               }
            ?>
            <td>80DD Medical Treatment for Dependent Handicapped</td>
            <?php
               if (isset($attachments['80DD Medical Treatment for Dependent Handicapped']) && $staff_details->medical_treatment_dependent_handicapped_80dd > 0) {
                  $attachment = $attachments['80DD Medical Treatment for Dependent Handicapped'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_treatment_dependent_handicapped_80dd) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'80DD Medical Treatment for Dependent Handicapped\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_treatment_dependent_handicapped_80dd) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>80DDB Expenditure on Medical Treatment for Self/ Dependent </td>
            <?php
               if (isset($attachments['eightyddb_expenditure_medical_tretment_self_dependent']) && $staff_details->expenditure_medical_tretment_self_dependent_80ddb > 0) {
                  $attachment = $attachments['eightyddb_expenditure_medical_tretment_self_dependent'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->expenditure_medical_tretment_self_dependent_80ddb) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightyddb_expenditure_medical_tretment_self_dependent\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->expenditure_medical_tretment_self_dependent_80ddb) . '"></td>';
               }
            ?>
            <!-- <td>80G, 80GGA, 80GGC Donation to approved funds </td>
            <td><input type="text"  style="border:none;" readonly value="<?php //echo formatIndianCurrency($staff_details->donation_approved_funds_80ggc) ?>"></td> -->
            <td>80U For Physically Disabled Person - Severe </td>
            <?php
               if (isset($attachments['physically_disabled_person_80u_severe']) && $staff_details->physically_disabled_person_80u_severe > 0) {
                  $attachment = $attachments['physically_disabled_person_80u_severe'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->physically_disabled_person_80u_severe) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'physically_disabled_person_80u_severe\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->physically_disabled_person_80u_severe) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>80GG Rent paid in case of no HRA Received</td>
            <?php
               if (isset($attachments['eightygg_rent_paid_no_hra_recived']) && $staff_details->rent_paid_no_hra_recived_80gg > 0) {
                  $attachment = $attachments['eightygg_rent_paid_no_hra_recived'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->rent_paid_no_hra_recived_80gg) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightygg_rent_paid_no_hra_recived\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->rent_paid_no_hra_recived_80gg) . '"></td>';
               }
            ?>
            <td>80U For Physically Disabled Person</td>
            <?php
               if (isset($attachments['eightyu_physically_disabled_person']) && $staff_details->physically_disabled_person_80u > 0) {
                  $attachment = $attachments['eightyu_physically_disabled_person'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->physically_disabled_person_80u) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightyu_physically_disabled_person\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->physically_disabled_person_80u) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>80G Donations to Approved Funds (100% Exemption) </td>
            <?php
               if (isset($attachments['eightyggc_donation_approved_funds']) && $staff_details->donation_approved_funds_80ggc > 0) {
                  $attachment = $attachments['eightyggc_donation_approved_funds'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->donation_approved_funds_80ggc) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightyggc_donation_approved_funds\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->donation_approved_funds_80ggc) . '"></td>';
               }
            ?>
            <td>80DD Medical Treatment for Dependent Handicapped - Severe </td>
            <?php
               if (isset($attachments['medical_treatment_dependent_handicapped_servere_80dd']) && $staff_details->medical_treatment_dependent_handicapped_servere_80dd > 0) {
                  $attachment = $attachments['medical_treatment_dependent_handicapped_servere_80dd'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_treatment_dependent_handicapped_servere_80dd) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'medical_treatment_dependent_handicapped_servere_80dd\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->medical_treatment_dependent_handicapped_servere_80dd) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td>80G Donation to Approved Funds (50% Exemption) </td>
            <?php
               if (isset($attachments['donation_approved_funds_80ggc_fifty']) && $staff_details->donation_approved_funds_80ggc_fifty > 0) {
                  $attachment = $attachments['donation_approved_funds_80ggc_fifty'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->donation_approved_funds_80ggc_fifty) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'donation_approved_funds_80ggc_fifty\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->donation_approved_funds_80ggc_fifty) . '"></td>';
               }
            ?>
            <td>80DDB Expenditure on Medical Treatment for Self/ Dependent - Senior </td>
            <?php
               if (isset($attachments['expenditure_medical_tretment_self_dependent_80ddb_senior']) && $staff_details->expenditure_medical_tretment_self_dependent_80ddb_senior > 0) {
                  $attachment = $attachments['expenditure_medical_tretment_self_dependent_80ddb_senior'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->expenditure_medical_tretment_self_dependent_80ddb_senior) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'expenditure_medical_tretment_self_dependent_80ddb_senior\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->expenditure_medical_tretment_self_dependent_80ddb_senior) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            
            <!-- <td>80TTA/B - Interest from Savings Account (10,000 for others & 50,000 for Senior Citizens) </td>
            <td><input type="text"  style="border:none;" readonly value="<?php //echo formatIndianCurrency($staff_details->b_senior_citizens_80tta) ?>"></td> -->
            <td>80TTA/B Interest from Savings Account (10,000 for Others & 50,000 for Senior Citizens) </td>
            <?php
               if (isset($attachments['eightytta_b_senior_citizens']) && $staff_details->b_senior_citizens_80tta > 0) {
                  $attachment = $attachments['eightytta_b_senior_citizens'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->b_senior_citizens_80tta) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'eightytta_b_senior_citizens\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->b_senior_citizens_80tta) . '"></td>';
               }
            ?>
         </tr>
         <!-- <tr> -->
            <!-- <td>80TTA/B - Interest from Savings Account (10,000 for others & 50,000 for Senior Citizens) </td>
            <td><input type="text"  style="border:none;" readonly value="<?php //echo formatIndianCurrency($staff_details->b_senior_citizens_80tta) ?>"></td> -->
            <!-- <td>Others </td>
            <td><input type="text"  style="border:none;" readonly value="<?php //echo formatIndianCurrency($staff_details->others_funds_80d) ?>"></td> -->
         <!-- </tr> -->
         <tr>
            <td colspan="4"><strong>Others</strong> </td>
         </tr>
         <tr>
            <td>Other Employer Income</td>
            <?php
               if (isset($attachments['other_employer_income']) && $staff_details->other_employer_income > 0) {
                  $attachment = $attachments['other_employer_income'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_employer_income) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'other_employer_income\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_employer_income) . '"></td>';
               }
            ?>
            <td>Other Employer TDS</td>
            <?php
               if (isset($attachments['other_employer_tds']) && $staff_details->other_employer_tds > 0) {
                  $attachment = $attachments['other_employer_tds'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_employer_tds) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'other_employer_tds\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->other_employer_tds) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td colspan="4"><strong>24 Home Loan</strong> </td>
         </tr>
         <tr>
            <td>Interest Paid On Home Loan</td>
            <?php
               if (isset($attachments['interest_paid_on_home_loan']) && $staff_details->interest_paid_on_home_loan > 0) {
                  $attachment = $attachments['interest_paid_on_home_loan'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->interest_paid_on_home_loan) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'interest_paid_on_home_loan\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->interest_paid_on_home_loan) . '"></td>';
               }
            ?>
         </tr>
         <tr>
            <td colspan="4"><strong>Section 10(5) - LTA / LTC</strong> </td>
         </tr>
         <tr>
            <td>Leave Travel Allowance (LTA) / Leave Travel Concession (LTC)</td>
            <?php
               if (isset($attachments['leave_travel_allowance']) && $staff_details->leave_travel_allowance > 0) {
                  $attachment = $attachments['leave_travel_allowance'];
                  echo '<td class="d-flex justify-content-between align-items-center">
                           <input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->leave_travel_allowance) . '">
                           <button class="btn btn-primary" onclick="viewAttachment(\'' . $attachment->proof_file_url . '\', \'leave_travel_allowance\')" title="' . $attachment->file_name . '">
                              <i class="fa fa-paperclip mr-0"></i>
                           </button>
                        </td>';
               } else {
                  echo '<td><input type="text" style="border:none;" readonly value="' . formatIndianCurrency($staff_details->leave_travel_allowance) . '"></td>';
               }
            ?>
         </tr>
      </table>
   </div>
</div>
<div class="col-12 text-center loading-icon" style="display: none;">
   <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
</div>
<div class='panel-footer' id="income_tax_open_footer"></div>

<div class="modal fade" id="staffTaxDetails" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="padding: 0px 25px !important; z-index: 10000;">
   <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="margin-top: 5rem;">
      <div class="modal-content">
      <div class="modal-header bg-body-secondary rounded-top">
         <h5 class="modal-title" id="staticBackdropLabel">Calculation As Per Selected Regime</h5>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="close_staff_regime_cal()">
         <span aria-hidden="true">&times;</span>
         </button>
      </div>
      <div class="modal-body bg-body-secondary">
         <table class="table table-bordered" id="taxCalculationNewRegime" style="display: none; /*box-shadow: #404040 -3px 0px 2px 0px;*/border: 2px solid #404040;">
            <thead>
               <tr>
                  <th colspan="4" style="text-align:center;">New Regime Calculation</th>
               </tr>
            </thead>
            <tbody>
               <tr>
                  <td width="55%"><strong>Income from Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Basic Salary</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_basic_salary" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">House Rent Allowance </td>
                  <td width="15%"><input type="text" id="nr_hra" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Allowance</strong> </td>
                  <td width="15%"><input type="text" id="nr_other_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Additional Allowance</strong></td>
                  <td width="15%"><input type="text" id="nr_additional_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"><button id="additional_allowance_info_btn" class="btn btn-secondary" onclick="view_additional_allowance_break_down()">More Info</button></td>
               </tr>
               <tr>
                  <td width="55%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_hra_other_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <?php if($collectPerkTaxMode == 'employee') { ?>
                  <tr>
                     <td width="55%"><strong>Perquisite Income</strong></td>
                     <td width="15%"></td>
                     <td width="15%"><input type="text" id="nr_perquisite_income" style="border:none;" readonly></td>
                     <td width="15%"></td>
                  </tr>
               <?php } ?>
               <tr>
                  <td width="55%"><strong>Less Exempted</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Standard deduction u/s 16 </td>
                  <td width="15%"><input type="text" id="nr_sd" style="border:none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Taxable Income From Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_income_from_salary_pt" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income from other Employer</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_other_employer_income" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Gross Salary Income</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_gross_salary_income" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Taxable Income as per New Regime</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_taxable_salary" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income Tax As Per New Regime</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_basic_tax" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Tax Rebate</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_tax_rebate" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Net Income Tax</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_net_income_tax" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Surcharge</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_surcharge" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_net_income_tax_surcharge" value="" style="border:none;" readonly></td>
               </tr>

               <tr>
                  <td width="55%"><strong>Cess</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_cess" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Tax Including Cess (New Regime)</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><strong><input type="text" id="nr_yearly_tds" value="" style="border:none;color:red" readonly></strong></td>
               </tr>
               <tr>
                  <td width="55%"><strong>TDS Deducted from Other Employer</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="nr_other_employer_tds" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>TDS to be Deducted (New Regime)</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><strong><input type="text" id="nr_final_tds" value="" style="border:none;color:red" readonly></strong></td>
               </tr>
            </tbody>
         </table> 
         <table class="table table-bordered" id="taxCalculationOldRegime" style="display: none;  /*box-shadow: #404040 -3px 0px 2px 0px;*/border: 2px solid #404040;">
            <thead>
               <tr>
                  <th colspan="4" style="text-align:center;">Old Regime Calculation</th>
               </tr>
            </thead>
            <tbody>
               <tr>
                  <td width="55%"><strong>Income from Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Basic Salary</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_basic_salary" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">House Rent Allowance </td>
                  <td width="15%"><input type="text" id="or_hra" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Allowance</strong> </td>
                  <td width="15%"><input type="text" id="or_other_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Additional Allowance</strong> </td>
                  <td width="15%"><input type="text" id="or_additional_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"><button id="additional_allowance_info_btn" class="btn btn-secondary" onclick="view_additional_allowance_break_down()">More Info</button></td>
               </tr>
               <tr>
                  <td width="55%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_hra_other_allowance" style="border: none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <?php if($collectPerkTaxMode == 'employee') { ?>
                  <tr>
                     <td width="55%"><strong>Perquisite Income</strong></td>
                     <td width="15%"></td>
                     <td width="15%"><input type="text" id="or_perquisite_income" style="border:none;" readonly></td>
                     <td width="15%"></td>
                  </tr>
               <?php } ?>
               <tr>
                  <td width="55%"><strong>Less Exempted</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">House Rent Allowance u/s 10(13A) </td>
                  <td width="15%"><input type="text" id="or_hra_exemption" style="border:none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>				
               <tr>
                  <td width="55%">Standard deduction u/s 16 </td>
                  <td width="15%"><input type="text" id="or_sd" style="border:none;" readonly></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_sd_hra" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income From Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_income_from_salary" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Deduction u/s 16: Professional Tax</strong></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_pt" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Taxable Income From Salary</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_income_from_salary_pt" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income from other Employer</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_other_employer_income" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Interest Paid On Home Loan</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_sec24" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Gross Salary Income</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_gross_salary_income" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Less : Deduction Under Chapter VI A</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80C</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80c" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80CCD</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80ccd" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80D</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80d" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80DD</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80dd" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80DDB</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80ddb" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80G</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80g" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80E</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80e" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80U</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80u" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 80TTA/B</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_80ttab" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <!-- <tr>
                  <td width="55%">Total Deductions</td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="total_80_deductions" style="border:none;" readonly></td>
               </tr> -->
               <tr>
                  <td width="55%"><strong>Less : Deduction Under Chapter III</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Deduction Under Section 10(5) - LTA / LTC</td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_lta" style="border:none;" readonly></td>
                  <td width="15%"></td>
               </tr>
               <tr>
                  <td width="55%">Total Deductions</td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="total_80_deductions" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Taxable Income as per Old Regime</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_taxable_salary" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income Tax As Per Old Regime</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_basic_tax" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Tax Rebate</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_tax_rebate" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Net Income Tax</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_net_income_tax" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Surcharge</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_surcharge" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Income Tax Including Surcharge</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_net_income_tax_surcharge" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Cess</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_cess" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>Tax Including Cess (Old Regime)</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><strong><input type="text" id="or_yearly_tds" value="" style="border:none;color:red" readonly></strong></td>
               </tr>
               <tr>
                  <td width="55%">TDS Deducted from Other Employer</td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><input type="text" id="or_other_employer_tds" value="" style="border:none;" readonly></td>
               </tr>
               <tr>
                  <td width="55%"><strong>TDS to be Deducted (Old Regime)</strong></td>
                  <td width="15%"></td>
                  <td width="15%"></td>
                  <td width="15%"><strong><input type="text" id="or_final_tds" value="" style="border:none;color:red" readonly></strong></td>
               </tr>
            </tbody>
         </table> 
      </div>
      <div class="modal-footer bg-body-secondary rounded-bottom">
         <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="close_staff_regime_cal()">Close</button>
      </div>
      </div>
   </div>
</div>
<style>
   .textMuted{
      text-decoration: line-through;
   }
   .modal-header {
      padding: 1rem 1.5rem;
      min-height: 56px;
   }
</style>