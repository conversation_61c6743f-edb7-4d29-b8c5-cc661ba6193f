
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?>
          </h3>
        </div>
      </div>
    </div>

    <div class="modal-body">
      <div class="row" style="margin: 0px;">
        <div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Select Date</label>
            <div id="reportrange" class="dtrange">  
              <span></span>           
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
              </div> 
          </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
          <div class="form-group">
            <button type="button"  class="btn btn-primary form-control" style="margin-top:1.9rem" onclick="generate_chronicles_report()">Get Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-body">
      <div class="chronicles"></div>
    </div>
</div>
</div>

<div class="modal fade" id="view_chronicles" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></h4>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <div id="chronicles-detail">
                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
