<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('Master_dashboard') ?>">Master Dashboard</a></li>
  <li><a href="#">Config Management Login</a></li>
</ul>

<div class="col-md-12">
    <div class="card cd_border" style="padding-bottom:2px;">
		<div class="card-header panel_heading_new_style_staff_border_v2">
			<div class="row" style="margin: 0px;">
				<div class="col-6 d-flex align-items-center justify-content-between">
					<h3 class="card-title panel_title_new_style_staff mb-2">
						<a class="back_anchor" href="<?php echo site_url('Master_dashboard');?>">
							<span class="fa fa-arrow-left"></span>
						</a> 
						Config Management Login
					</h3>
				</div>
            </div>
        </div>
        <div class="card-body">
            <div class="col-md-12 d-flex justify-content-center">
                <form id="otpForm" style="width: 400px">
                    <div class="mb-3">
                        <label for="adminEmail" class="form-label">Name <font color="red">*</font></label>
                        <input type="text" title="Please enter letters, spaces, apostrophes, or hyphens only." class="form-control" id="adminName" name="name" placeholder="Enter Name" required maxlength="25">
                    </div>

                    <div class="mb-3">
                        <label for="adminEmail" class="form-label">Email <font color="red">*</font></label>
                        <input type="email" class="form-control" id="adminEmail" name="email" placeholder="Enter Email" required maxlength="50">
                    </div>

                    <div id="otpSection" style="display:none">
                        <div class="mb-3">
                            <label for="otp" class="form-label">Enter OTP <font color="red">*</font></label>
                            <input type="number" class="form-control" id="otp" name="otp" placeholder="Enter OTP" required maxlength="6">
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary" id="sendOtpBtn">Send OTP</button>
                    <button type="submit" class="btn btn-success" id="verifyOtpBtn" style="display:none">Login</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    $('#sendOtpBtn').click(function () {
        const email = $('#adminEmail').val();
        const name = $('#adminName').val();

        if (!name) {
            $(function(){
                new PNotify({
                    title: 'Alert',
                    text: "Please enter name",
                    type: 'warning',
                });
            });
            return;
        }else if (!email) {
            $(function(){
                new PNotify({
                    title: 'Alert',
                    text: "Please enter email.",
                    type: 'warning',
                });
            });
            return;
        }
        
        $.ajax({
            url: '<?php echo site_url("config/send_otp") ?>',
            type: 'POST',
            data: { email: email, name: name },
            success: function (resp) {
                const res = JSON.parse(resp);
                if (res.status === 'ok') {
                    $('#otpSection').show();
                    $('#verifyOtpBtn').show();
                    $('#sendOtpBtn').hide()
                    $(function(){
                        new PNotify({
                            title: 'Success',
                            text: "OTP Sent!",
                            type: 'success',
                        });
                    });
                } else {
                    $(function(){
                        new PNotify({
                            title: 'Error',
                            text: res.msg,
                            type: 'error',
                        });
                    });
                }
            }
        });
    });

    $('#otpForm').submit(function (e) {
        e.preventDefault();
        var $form = $('#otpForm');
        if ($form.parsley().validate()) {
            const email = $('#adminEmail').val();
            const otp = $('#otp').val();
            const name = $('#adminName').val();
            $.ajax({
                url: '<?php echo site_url("config/verify_otp") ?>',
                type: 'POST',
                data: { email: email, otpCode:otp, name: name },
                success: function (data) {
                    var res = JSON.parse(data);

                    if (res.status === 'ok') {
                        window.location.href = '<?php echo site_url("config") ?>';
                    } else {
                        new PNotify({
                            title: 'Error',
                            text: res.msg || 'Incorrect OTP. Please try again.',
                            type: 'error',
                        });

                        $('#errorPopUp').show(); // optional, only if such a div exists
                        $('#error_otp').html(res.msg || 'Incorrect OTP. Please try again.');
                    }
                }
            });
        }
    });
</script>

