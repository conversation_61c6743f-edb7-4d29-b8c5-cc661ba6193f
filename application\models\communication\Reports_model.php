<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
class Reports_model extends CI_Model {
	private $yearId;
	private $smsStatusCodes;
    public function __construct()
  	{
       	// Call the CI_Model constructor
	 	parent::__construct();
	 	$this->yearId = $this->acad_year->getAcadYearID();
	 	$this->smsStatusCodes = array(
          'AWAITED-DLR' => 'Awaited delivery',
          'DELIVRD' => 'Delivered',
          'DNDNUMB' => 'DND number',
          'OPTOUT-REJ' => 'Opt out from subscription',
          'INV-NUMBER' => 'Invalid Number',
          'NO-NUMBER' => 'No Number',
          'INVALID-NUM' => 'Invalid Number',
          'SENDER-ID-NOT-FOUND' => 'SENDER-ID-NOT-FOUND',
          'INV-TEMPLATE-MATCH' => 'Invalid template',
          'MAX-LENGTH' => 'Message length exceeded 100 charactes',
          'NO-CREDITS' => 'No credits',
          'SERIES-BLOCK' => 'Mobile number series blocked',
          'SERIES-BLK' => 'Series blocked by operator',
          'SERVER-ERR' => 'Server error',
          'SPAM' => 'Spam SMS',
          'BLACKLIST' => 'Blacklisted number',
          'BLACKLST' => 'Blacklisted number',
          'TEMPLATE-NOT-FOUND' => 'Template not found',
          'TEMPLATE-INACTIVE' => 'Template is not active',
          'NOT-OPTIN' => 'Not subscribed for opt-in group',
          'TIME-OUT-PROM' => 'Time out for promotional SMS',
          'INVALID-SUB' => 'Number does not exist',
          'ABSENT-SUB' => 'Mobile Subscriber not reachable',
          'HANDSET-ERR' => 'Problem with Handset',
          'BARRED' => 'Message barred by user',
          'NET-ERR' => 'Subscriber’s operator not supported',
          'MEMEXEC' => 'Handset memory full',
          'FAILED' => 'Failed to send',
          'MOB-OFF' => 'Mobile handset in switched off mode',
          'HANDSET-BUSY' => 'Subscriber is in busy condition',
          'EXPIRED' => 'SMS expired after multiple re-try',
          'REJECTED' => 'SMS Rejected as the number is blacklisted by operator',
          'REJECTD' => 'SMS Rejected as the number is blacklisted by operator',
          'OUTPUT-REJ' => 'Unsubscribed from the group',
          'REJECTED-MULTIPART' => 'Validation fail',
          'UNDELIV' => 'Failed due to network errors',
          'NO-DLR-OPTR' => 'Status not acknowledged',
          '0' => 'Status not acknowledged',
          '' => 'Status not acknowledged',
          'Unknown' => 'Status not acknowledged'
    );
  	}

  	public function get_staff_student_names(){
	    $staff =  $this->db->select("sm.id as staff_id, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as s_name")
	    ->from("staff_master sm")
	    ->where('status',2)
	    ->get()->result();

	    $student =  $this->db->select("sd.id as student_id, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, cs.class_name")
	      	->from('student_year sy')
	      	->join('student_admission sd','sy.student_admission_id=sd.id')
	      	->join('class_section cs','sy.class_section_id=cs.id', 'left')
	      	->where('sd.admission_status','2')
	      	->where('sy.promotion_status!=', '4')
	      	->where('sy.promotion_status!=', '5')
	      	->where('sy.acad_year_id',$this->yearId)
	      	->get()->result();

	    return array('staff'=>$staff, 'student'=>$student);
	}

	public function getStudentData($student_id) {
		return $this->db->query("SELECT sa.id, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,''), ' (',cs.class_name,'',cs.section_name, ')') AS name FROM student_admission sa JOIN student_year sy ON sy.student_admission_id=sa.id JOIN class_section cs ON cs.id=sy.class_section_id WHERE sy.acad_year_id=$this->yearId AND sa.id=$student_id")->row();
	}

	public function getStaffData($staff_id) {
		return $this->db->query("SELECT sm.id, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) AS name FROM staff_master sm WHERE sm.id=$staff_id")->row();
	}

	public function getStudentCommunications($student_id, $from_date, $to_date) {
		$parents = $this->db->query("SELECT id FROM parent WHERE student_id=$student_id")->result();
		$parent_ids = [];
		foreach ($parents as $k => $parent) {
			$parent_ids[] = $parent->id;
		}

		if(empty($parent_ids)) 
			return array();

		$ids = implode(",", $parent_ids);

		$text_sql = "SELECT tm.id, if(ts.avatar_type=2,sr.relation_type,'Student') AS relation, tm.message, tm.source, (CASE WHEN ts.mode=1 THEN 'Notification' ELSE 'SMS' END) AS mode, ts.status, ts.is_read, ts.mobile_no, DATE_FORMAT(tm.sent_on, '%d-%m-%Y') AS sent_date, tm.sent_on 
				FROM texting_master tm 
				JOIN text_sent_to ts ON ts.texting_master_id=tm.id 
				JOIN student_relation sr ON ts.stakeholder_id=sr.relation_id 
				WHERE ((ts.avatar_type=2 AND ts.stakeholder_id IN ($ids)) OR (ts.avatar_type=1 AND ts.stakeholder_id=$student_id)) 
				AND DATE_FORMAT(tm.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(tm.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY tm.sent_on DESC";

		$text_messages = $this->db->query($text_sql)->result_array();

		$texts = array();
		foreach ($text_messages as $t => $text) {
			$texts[$text['id']]['id'] = $text['id'];
			$texts[$text['id']]['body'] = $text['message'];
			$texts[$text['id']]['source'] = $text['source'];
			$texts[$text['id']]['sent_date'] = $text['sent_date'];
			$texts[$text['id']]['sent_on'] = $text['sent_on'];
			$texts[$text['id']]['mode'] = $text['mode'];
			$texts[$text['id']]['status'] = $text['status'];
			$texts[$text['id']]['recivers'][] = array(
				'relation' => $text['relation'],
				'is_read' => $text['is_read'],
				'status' => $text['status'],
				'mode' => $text['mode'],
				'mobile_no' => $text['mobile_no']
			);
		}


		$circular_sql = "SELECT cm.id, sr.relation_type AS relation, cm.title, cm.body, cs.is_read, cs.email, DATE_FORMAT(cm.sent_on, '%d-%m-%Y') AS sent_date, cm.sent_on 
				FROM circularv2_master cm 
				JOIN circularv2_sent_to cs ON cs.circularv2_master_id=cm.id 
				JOIN student_relation sr ON cs.stakeholder_id=sr.relation_id 
				WHERE cs.avatar_type=2 AND cs.stakeholder_id IN ($ids) 
				AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY cm.sent_on DESC";

		$circular_sent = $this->db->query($circular_sql)->result_array();

		$circulars = array();
		foreach ($circular_sent as $t => $circular) {
			$circulars[$circular['id']]['id'] = $circular['id'];
			$circulars[$circular['id']]['title'] = $circular['title'];
			$circulars[$circular['id']]['body'] = $circular['body'];
			$circulars[$circular['id']]['sent_date'] = $circular['sent_date'];
			$circulars[$circular['id']]['sent_on'] = $circular['sent_on'];
			$circulars[$circular['id']]['recivers'][] = array(
				'relation' => $circular['relation'],
				'is_read' => $circular['is_read'],
				'email' => $circular['email']
			);
		}

		$email_sql = "SELECT em.id, sr.relation_type AS relation, em.subject, em.body, em.source, es.status, es.email, DATE_FORMAT(em.sent_on, '%d-%m-%Y') AS sent_date, em.sent_on 
				FROM email_master em 
				JOIN email_sent_to es ON es.email_master_id=em.id 
				JOIN student_relation sr ON es.stakeholder_id=sr.relation_id 
				WHERE es.avatar_type=2 AND es.stakeholder_id IN ($ids) 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY em.sent_on DESC";

		$email_sent = $this->db->query($email_sql)->result_array();

		$emails = array();
		foreach ($email_sent as $t => $email) {
			$emails[$email['id']]['id'] = $email['id'];
			$emails[$email['id']]['subject'] = $email['subject'];
			$emails[$email['id']]['body'] = $email['body'];
			$emails[$email['id']]['source'] = $email['source'];
			$emails[$email['id']]['sent_date'] = $email['sent_date'];
			$emails[$email['id']]['sent_on'] = $email['sent_on'];
			$emails[$email['id']]['recivers'][] = array(
				'relation' => $email['relation'],
				'status' => $email['status'],
				'email' => $email['email']
			);
		}

		$std_email_sql = "SELECT em.id, 'Student' AS relation, em.subject, em.body, em.source, es.status, es.email, DATE_FORMAT(em.sent_on, '%d-%m-%Y') AS sent_date, em.sent_on 
				FROM email_master em 
				JOIN email_sent_to es ON es.email_master_id=em.id 
				JOIN student_admission s ON es.stakeholder_id=s.id 
				WHERE es.avatar_type=1 AND es.stakeholder_id=$student_id 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY em.sent_on DESC";

		$std_email_sent = $this->db->query($std_email_sql)->result_array();
		foreach ($std_email_sent as $t => $std_email) {
			$emails[$std_email['id']]['recivers'][] = array(
				'relation' => $std_email['relation'],
				'status' => $std_email['status'],
				'email' => $std_email['email']
			);
		}

		$communications = $this->_mergeCommunicationData($texts, $circulars, $emails);
		return $communications;
		// echo '<pre>'; print_r($communications); die();
	}

	public function getStaffCommunication($staff_id, $from_date, $to_date) {
		$text_sql = "SELECT tm.id, tm.message AS body, tm.source, (CASE WHEN ts.mode=1 THEN 'Notification' ELSE 'SMS' END) AS mode, ts.status, ts.is_read, ts.mobile_no, DATE_FORMAT(tm.sent_on, '%d-%m-%Y') AS sent_date, tm.sent_on 
				FROM texting_master tm 
				JOIN text_sent_to ts ON ts.texting_master_id=tm.id 
				JOIN staff_master sm ON sm.id=ts.stakeholder_id 
				WHERE ts.avatar_type=4 AND ts.stakeholder_id=$staff_id  
				AND DATE_FORMAT(tm.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(tm.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY tm.sent_on DESC";

		$texts = $this->db->query($text_sql)->result_array();

		$circular_sql = "SELECT cm.id, cm.title, cm.body, cs.is_read, cs.email, DATE_FORMAT(cm.sent_on, '%d-%m-%Y') AS sent_date, cm.sent_on 
				FROM circularv2_master cm 
				JOIN circularv2_sent_to cs ON cs.circularv2_master_id=cm.id 
				JOIN staff_master sm ON sm.id=cs.stakeholder_id 
				WHERE cs.avatar_type=4 AND cs.stakeholder_id=$staff_id 
				AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(cm.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY cm.sent_on DESC";

		$circulars = $this->db->query($circular_sql)->result_array();

		$email_sql = "SELECT em.id, em.subject, em.body, em.source, es.status, es.email, DATE_FORMAT(em.sent_on, '%d-%m-%Y') AS sent_date, em.sent_on 
				FROM email_master em 
				JOIN email_sent_to es ON es.email_master_id=em.id 
				JOIN staff_master sm ON sm.id=es.stakeholder_id 
				WHERE es.avatar_type=4 AND es.stakeholder_id=$staff_id 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')>='$from_date' 
				AND DATE_FORMAT(em.sent_on, '%Y-%m-%d')<='$to_date' 
				ORDER BY em.sent_on DESC";

		$emails = $this->db->query($email_sql)->result_array();

		$communications = $this->_mergeCommunicationData($texts, $circulars, $emails);
		return $communications;
		// echo '<pre>'; print_r($communications); die();
	}

	private function _mergeCommunicationData($texts, $circulars, $emails, $notifications=[]) {
		$communications = array();
		
		if(!empty($notifications)) {
			foreach ($notifications as $t => $not) {
				$not['sent_time'] = local_time($not['sent_on'], 'h:i a');
				$not['type'] = 'Notification';
				$communications[$not['sent_date']][] = $not;
			}
		}

		foreach ($texts as $t => $text) {
			$text['sent_time'] = local_time($text['sent_on'], 'h:i a');
			if($text['mode'] == 'SMS') {
				if (isset($this->smsStatusCodes[$text['status']]))
					$text['status'] = $this->smsStatusCodes[$text['status']];
				else 
					$text['status'] = 'Unknown Issue';
			}
			$text['type'] = 'Texting';
			$communications[$text['sent_date']][] = $text;
		}


		foreach ($circulars as $c => $circular) {
			$circular['sent_time'] = date("h:i a", strtotime($circular['sent_on']));
			$circular['type'] = 'Circular';
			$communications[$circular['sent_date']][] = $circular;
		}

		foreach ($emails as $e => $email) {
			if($email['sent_on']) {
				$email['sent_time'] = local_time($email['sent_on'], 'h:i a');
				$email['type'] = 'Email';
				$communications[$email['sent_date']][] = $email;
			}
		}

		$data = array();
		foreach ($communications as $date => $com) {
			$temp['date'] = date('d M', strtotime($date));
			$temp['data'] = $com;
			unset($communications[$date]);
			usort($temp['data'], function($a, $b){ 
				$d1 = strtotime($a['sent_on']);
				$d2 = strtotime($b['sent_on']);
				return ($d2 - $d1);
			});
			$data[] = $temp;
		}

		return $data;
	}

	public function getAggregateCommunication($from_date, $to_date) {
		$not_sql = "SELECT 
                    DATE_FORMAT(tm.sent_on, '%d-%m-%Y') AS sent_date, 
                    ts.mode, 
					tm.sent_on,
                    COUNT(CASE WHEN ts.status = 'Sent' THEN 1 END) AS sent, 
                    COUNT(CASE WHEN ts.status != 'Sent' THEN 1 END) AS failed, 
                    COUNT(CASE WHEN ts.status = 'Sent' AND ts.is_read = 1 THEN 1 END) AS is_read, 
                    COUNT(CASE WHEN ts.status = 'Sent' AND ts.is_read = 0 THEN 1 END) AS not_read  
                FROM texting_master tm 
                JOIN text_sent_to ts ON ts.texting_master_id = tm.id 
                WHERE DATE(tm.sent_on) BETWEEN '$from_date' AND '$to_date' 
                AND ts.mode = 1 
                GROUP BY sent_date, ts.mode
                ORDER BY tm.sent_on DESC";

		$notifications = $this->db->query($not_sql)->result_array();

		$text_sql = "SELECT 
                    DATE_FORMAT(tm.sent_on, '%d-%m-%Y') AS sent_date, 
                    ts.mode, 
					tm.sent_on,
                    COUNT(CASE WHEN ts.status IN ('DELIVRD', 'Delivered') THEN 1 END) AS sent, 
                    COUNT(CASE WHEN ts.status IN ('AWAITED-DLR', 'SUBMITTED') THEN 1 END) AS awaited, 
                    COUNT(CASE WHEN ts.status NOT IN ('AWAITED-DLR', 'DELIVRD', 'Delivered', 'NO-NUMBER') THEN 1 END) AS failed, 
                    COUNT(CASE WHEN ts.status IN ('DELIVRD', 'Delivered') AND ts.is_read = 1 THEN 1 END) AS is_read, 
                    COUNT(CASE WHEN ts.status IN ('DELIVRD', 'Delivered') AND ts.is_read = 0 THEN 1 END) AS not_read  
                FROM texting_master tm 
                JOIN text_sent_to ts ON ts.texting_master_id = tm.id 
                WHERE DATE(tm.sent_on) BETWEEN '$from_date' AND '$to_date' 
                AND ts.mode = 2 
                GROUP BY sent_date, ts.mode
                ORDER BY tm.sent_on DESC";

		$texts = $this->db->query($text_sql)->result_array();

		$circular_sql = "SELECT 
                        DATE_FORMAT(cm.sent_on, '%d-%m-%Y') AS sent_date, 
						cm.sent_on,
						COUNT(cs.id) AS circular_total,
                        COUNT(CASE WHEN cs.is_read = 1 THEN 1 END) AS is_read, 
                        COUNT(CASE WHEN cs.is_read = 0 THEN 1 END) AS not_read  
                    FROM circularv2_master cm 
                    JOIN circularv2_sent_to cs ON cs.circularv2_master_id = cm.id 
                    WHERE DATE(cm.sent_on) BETWEEN '$from_date' AND '$to_date' 
                    GROUP BY sent_date
                    ORDER BY cm.sent_on DESC";

		$circulars = $this->db->query($circular_sql)->result_array();

		$email_sql = "SELECT 
                    em.id, 
                    DATE_FORMAT(em.sent_on, '%d-%m-%Y') AS sent_date, 
					em.sent_on,
                    COUNT(CASE WHEN es.status = 'Delivered' THEN 1 END) AS delivered, 
                    COUNT(CASE WHEN es.status = 'Opened' THEN 1 END) AS opened, 
                    COUNT(CASE WHEN es.status NOT IN ('Opened', 'Delivered') THEN 1 END) AS failed 
                FROM email_master em 
                JOIN email_sent_to es ON es.email_master_id = em.id 
                WHERE DATE(em.sent_on) BETWEEN '$from_date' AND '$to_date' 
                AND em.sent_on IS NOT NULL 
                GROUP BY sent_date, em.id
                ORDER BY em.sent_on DESC";

		$emails = $this->db->query($email_sql)->result_array();

		$communications = $this->_mergeCommunicationData($texts, $circulars, $emails, $notifications);
		// echo "<pre>"; print_r($communications); die();
		return $communications;
	}

	public function getFailedEmails() {
		$sql = "SELECT es.email, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name, '')) AS parent_name, sr.relation_type, CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name, '')) AS student_name, sa.id AS student_id, p.id AS parent_id, CONCAT(cs.class_name,cs.section_name) AS class_section, COUNT(es.id) AS failed_times 
				FROM email_sent_to es 
				JOIN parent p ON p.id=es.stakeholder_id 
				JOIN student_relation sr ON sr.relation_id=p.id 
				JOIN student_admission sa ON sa.id=sr.std_id 
				JOIN student_year sy ON sy.student_admission_id=sa.id 
				JOIN class_section cs ON cs.id=sy.class_section_id 
				WHERE es.avatar_type=2 
				AND es.status LIKE 'Bounce-%' 
				AND sy.acad_year_id=$this->yearId 
				GROUP BY es.stakeholder_id 
				ORDER BY cs.id, sa.first_name";

		$parent = $this->db->query($sql)->result();

		$sql = "SELECT es.email, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name, '')) AS staff_name, sm.id AS staff_id, COUNT(es.id) AS failed_times 
				FROM email_sent_to es 
				JOIN staff_master sm ON sm.id=es.stakeholder_id 
				WHERE es.avatar_type=4 
				AND es.status LIKE 'Bounce-%' 
				GROUP BY es.stakeholder_id 
				ORDER BY sm.first_name";

		$staff = $this->db->query($sql)->result();
		$result = array('parents' => $parent, 'staff' => $staff);
		return $result;
		// echo "<pre>"; print_r($result); die();
	}

}