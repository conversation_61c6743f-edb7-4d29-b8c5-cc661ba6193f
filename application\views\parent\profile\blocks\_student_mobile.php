<?php $gender = array('M'=>'Male', 'F'=>'Female');?>
  <div class="card" style="box-shadow: none;border:none;">
    <?php
      /**
       * If isNextYearStudent is not set in cache, then, we assume that he is a old student.
       * For new students, we will display only FEES and PROFILE. Other features are hidden.
       */
      $isNewStudent = isset($this->parentcache->getParentCache()->isNextYearStudent)?$this->parentcache->getParentCache()->isNextYearStudent:'0';
    ?>

    <div class="card-header panel_heading_new_style_padding d-flex justify-content-between align-items-center mt-3 py-0 px-4">
      <h3 class="card-title panel_title_new_style m-0"><strong>Student Profile</strong></h3>
            <?php if ($studentData->profile_status == 'Unlock') { ?>
        <!-- <ul class="panel-controls"> -->
          <a  class="circleButton_noBackColor" href="<?php echo site_url('parent_controller/edit_profile_parent_mobile/'.$callFrom); ?>"><i class="fa fa-pencil fontColor_orange" style="font-size: 19px;"></i></a>
          <!-- <a href="https://www.nextelement.in/se_help/parent/index.htm#t=user_guide%2Fprofile%2Fedit_pro.htm" class="circleButton_noBackColor pull-right" add target="_blank" data-toggle="tooltip" data-placement="top" title="Help">
            <span class="fa fa-question fontColor_orange" style="font-size: 19px;"></span>
          </a> -->
        <!-- </ul> -->
      <?php } ?>
    </div>
	  <div class="card-body" style="padding-bottom: 0px;padding-top: 0.5rem;">
        <div class="col-md-12 jContainer">
          <div class="jHead">
            <h4>
              <strong>Student</strong>

            </h4>

          </div>
            <table class="table">
              <tr>
                <th style="height: 64px;">
                  <?php
                  $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
                  $gender = 'Female';
                  if($studentData->gender == 'M'){
                    $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
                    $gender = 'Male';
                  }?>
                  <?php if ($this->settings->isProfile_profile_enabled('STUDENT_PHOTO')) : ?>
                  <img class="img-fluid" style="height:160px;width:120px" src="<?php echo (empty($studentData->picture_url)) ? $picUrl : $this->filemanager->getFilePath($studentData->picture_url); ?>">
                  <?php endif ?>
                </th>
                <td>
                <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NAME')) : ?>
                  <span style="font-size: 20px;font-weight: 500;text-transform: uppercase;"><?php echo ucfirst($studentData->stdName) ;?></span>
                  <?php endif ?>
                </td>
              </tr>

              <?php if($this->settings->isProfile_profile_enabled('ADMISSION_NO') && !$isNewStudent){ ?>
              <tr>
                <th><strong>Admission No </strong></th>
                <td><h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->admissionNo;?></h5></td>
              </tr>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('ENROLLMENT_NUMBER')){ ?>
              <tr>
                <th><strong>Enrollment Number </strong></th>
                <td><h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->enrollment_number;?></h5></td>
              </tr>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('ALPHA_ROLL_NUMBER')){ ?>
              <tr>
                <th><strong>Alpha Roll Number </strong></th>
                <td><h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->alpha_rollnum;?></h5></td>
              </tr>
              <?php } ?>

              <?php if($this->settings->isProfile_profile_enabled('CLASS_SECTION') && !$isNewStudent){ ?>
              <tr>
                <th><strong>Class / Section </strong></th>
                <!-- <td><h5 class="form-control-plaintext" style="padding :0px;"><?php // echo $studentData->className.'/'.$studentData->sectionName ;?></h5></td> -->
                <td>
                  <h5 class="form-control-plaintext" style="padding: 0;">
                      <?php 
                      echo !empty($studentData->className) && !empty($studentData->sectionName)  ? ($studentData->className) . '/' . ($studentData->sectionName) : '';
                      ?>
                  </h5>
              </td>
              </tr>
              <?php } ?>

              <?php if($this->settings->getSetting('is_semester_scheme')){ ?>
              <tr>
                <th><strong>Semester </strong></th>
                <td><h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->semester;?></h5></td>
              </tr>
              <?php } ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_EMAIL')) : ?>
              <th><strong>Email ID </strong></th>
                <td>
                  <h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->student_email == '' ? 'Not available' : $studentData->student_email; ?> </h5>
                </td>
              </tr>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('INITIAL_PASSWORD')) : ?>
              <th><strong>Password </strong></th>
                <td>
                  <h5 class="form-control-plaintext" style="padding :0px;"><?php echo $studentData->student_email_password == '' ? 'Not available' : $studentData->student_email_password; ?> </h5>
                </td>
              </tr>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_DOB')) : ?>
              <th><strong>Date of Birth </strong></th>
                <td>
                  <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->dob)?date('d-M-Y', strtotime($studentData->dob)):'' ?> </h5>
                </td>
              </tr>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOTHER_TONGUE')) : ?>
                <tr>
                  <th><strong>Mother Tongue </strong> </th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->mother_tongue == '')? 'Not available' : $studentData->mother_tongue ;?></h5>
                  </td>
                </tr>
              <?php endif ?>
              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_GENDER')) : ?>
                <tr>
                  <th><strong>Gender </strong></th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;"><?php echo $gender ;?></h5>
                  </td>
                </tr>
              <?php endif ?>
                <?php if ($this->settings->isProfile_profile_enabled('STUDENT_BLOOD_GROUP')) : ?>
                <tr>
                  <th><strong>Blood Group</strong> </th>
                  <?php if(! empty($studentData)): ?>
                    <td>
                        <h5 class="form-control-plaintext" style="padding: 0px;">
                            <?php echo ($studentData->blood_group == '') ? 'Not available' : $studentData->blood_group; ?>
                        </h5>
                    </td>
                <?php else: ?>
                    <td>
                        <h5 class="form-control-plaintext" style="padding: 0px;">Not available</h5>
                    </td>
                <?php endif; ?>
                </tr>
                <?php endif ?>

                <?php if ($this->settings->isProfile_profile_enabled('STUDENT_STOP')) : ?>
                  <tr>
                    <th><strong>Stop</strong> </th>
                    <td>
                        <h5 class="form-control-plaintext" style="padding: 0px;">
                            <?php 
                            echo (! empty($stops[$studentData->stop])) 
                                ? $stops[$studentData->stop] 
                                : 'Not available'; 
                            ?>
                        </h5>
                    </td>
                  </tr>
                <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_PICKUP_MODE')) : ?>
                <tr>
                  <th><strong>Pickup Mode</strong> </th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->pickup_mode == '')? 'Not available' : $studentData->pickup_mode ;?></h5>
                  </td>
                </tr>
              <?php endif ?>

                <?php if ($this->settings->isProfile_profile_enabled('STUDENT_NATIONALITY')) : ?>
                <tr>
                  <th><strong>Nationality</strong> </th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;">
                          <?php 
                          if (isset($studentData->nationality)) {
                              echo ($studentData->nationality == '') ? 'Not available' : $studentData->nationality;
                          } else {
                              echo 'Not available';
                          }
                          ?>
                      </h5>
                  </td>
                </tr>
                <?php endif ?>

                <?php if ($this->settings->isProfile_profile_enabled('CATEGORY')) : ?>
                <tr>
                  <th><strong>Category</strong></th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->category == '' || $studentData->category == '0') ? 'Not available' : $studentData->category ;?></h5>
                  </td>
                </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_CASTE')) : ?>
                <tr>
                  <th><strong>Caste</strong></th>
                  <td>
                    <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->caste == '' || $studentData->caste == '0') ? 'Not available' : $studentData->caste ;?></h5>
                  </td>
                </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_RELIGION')) : ?>
              <tr>
                <th><strong>Religion</strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->religion == '' || $studentData->religion == '0') ? 'Not available' : $studentData->religion;?></h5>
                </td>
              </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_MOBILE_NUMBER')) : ?>
              <tr>
                <th><strong>Student Mobile Number</strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->student_mobile_no == '') ? 'Not available' : $studentData->student_mobile_no;?></h5>
                </td>
              </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('PREFFERED_CONTACT_NUMBER')) : ?>
              <tr>
                <th><strong>Preffered Contact Number</strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->preferred_contact_no == '') ? 'Not available' : $studentData->preferred_contact_no;?></h5>
                </td>
              </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_AADHAR')) : ?>
              <tr>
                <th><strong>Aadhar Number</strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->aadhar_no == '') ? 'Not available' : $studentData->aadhar_no;?></h5>
                </td>
              </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('NAME_AS_PER_AADHAR')) : ?>
              <tr>
                <th><strong>Name As Per Aadhar Number</strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php echo ($studentData->name_as_per_aadhar == '') ? 'Not available' : $studentData->name_as_per_aadhar;?></h5>
                </td>
              </tr>
              <?php endif ?>

          <?php if ($this->settings->isProfile_profile_enabled('STUDENT_ADDRESS') && !$isNewStudent) : ?>

            <?php if (!empty($studentAddress)) {
              foreach ($studentAddress as $val => $address) { ?>
              <tr>
                <th><strong><?php echo $val ?>  </strong></th>
                <?php
                if (empty($address)) {
                  echo "<td></td>";
                }
                  foreach ($address as $key => $s_ad) { ?>
                <td>
                  <h5 class="form-control-plaintext">
                        <?php if($s_ad->Address_line1 == '' && $s_ad->Address_line2 == '' && $s_ad->area == '' && $s_ad->district == '' && $s_ad->state == '' && $s_ad->country == '' && $s_ad->pin_code == ''){ echo 'Not available';}else{ echo $s_ad->Address_line1 .' '.$s_ad->Address_line2.' '.$s_ad->area.' '.$s_ad->district.' '.$s_ad->state.' '.$s_ad->country.' '.$s_ad->pin_code; } ?>
                  </h5>
                </td>
                  <?php } ?>
              </tr>
                  <?php  } } ?>

              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('STUDENT_HOUSE')) : ?>
              <tr>
                <th><strong>House </strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php 
                      if ($studentData->student_house !=0 || $studentData->student_house !=null) {
                        echo  ucwords($studentData->student_house);
                      }else{
                        echo "";
                      } 
                    ?></h5>
                </td>
              </tr>
              <?php endif ?>

              <?php if ($this->settings->isProfile_profile_enabled('COMBINATION')) : ?>
              <tr>
                <th><strong>Combination </strong></th>
                <td>
                   <h5 class="form-control-plaintext" style="padding :0px;"><?php 
                      if ($studentData->combination_name !=0 || $studentData->combination_name !=null) {
                        echo  ucwords($studentData->combination_name);
                      }else{
                        echo "";
                      } 
                    ?></h5>
                </td>
              </tr>
              <?php endif ?>

            </table>
        </div>

  </div>
</div>

<style type="text/css">
.jContainer{
padding: 0px;
border:solid 1px #ccc;
margin-bottom: 10px;
border-radius: 16px;
}

.jHead {
padding: 3%;
background: #DAE6FA;
border-top-left-radius:16px;
border-top-right-radius:16px;
}

.jHead>h5{
color: #000 !important;
}
  h4{
    margin-bottom: 0px;
  }
  table{
    font-size: 15px;
  }
</style>