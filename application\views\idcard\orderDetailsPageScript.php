<script>
    document.addEventListener("DOMContentLoaded", function () {
        const steps = document.querySelectorAll(".form-step");
        const progressSteps = document.querySelectorAll(".progress-step");
        const progressBar = document.querySelector(".progress");

        function updateProgressBar(index) {
            progressSteps.forEach((step, i) => {
                const icon = step.querySelector("span");

                if (i < index) {
                    step.classList.add("completed");
                    step.classList.remove("progress-step-active");
                    icon.classList.remove("fa-circle");
                    icon.classList.add("fa-check");
                    icon.style.color = "white";
                } else if (i === index) {
                    step.classList.add("progress-step-active");
                    step.classList.remove("completed");
                    icon.classList.remove("fa-check");
                    icon.classList.add("fa-circle");
                    icon.style.color = "white";
                } else {
                    step.classList.remove("progress-step-active", "completed");
                    icon.classList.remove("fa-check");
                    icon.classList.add("fa-circle");
                    icon.style.color = "#ccc";
                }
            });

            // Update progress bar width
            const progressPercent = (index / (progressSteps.length - 1)) * 100;
            progressBar.style.width = `${progressPercent}%`;
        }

        function showStep(index) {
            // Check if the order is for a digital card
            const isDigitalCard = '<?= strtolower($order_details->id_card_type) ?>' === 'digital card';

            // For digital cards, lock payment processing, printing, and delivered steps
            if (isDigitalCard && (index === 3 || index === 4 || index === 5)) {
                return false;
            }

            // For non-digital cards, use the original status-based restrictions
            if (!isDigitalCard && (
                (order_status === "in review" && (index === 3 || index === 4 || index === 5)) ||
                (order_status === "order_submitted" && (index === 4 || index === 5)) ||
                (order_status === "in printing" && index === 5) ||
                (order_status === "delivered" && false))) {
                return false;
            }

            steps.forEach((step, i) => {
                if (i === index) {
                    step.classList.add("form-step-active");
                } else {
                    step.classList.remove("form-step-active");
                }
            });
            updateProgressBar(index);
            return true;
        }

        // Update the initial step display based on order status and card type
        if ('<?= strtolower($order_details->id_card_type) ?>' === 'digital card') {
            // For digital cards, set active step to Verification (index 2) and lock payment/printing steps
            showStep(2);
            progressSteps.forEach((step, index) => {
                if (index === 3 || index === 4 || index === 5) {
                    step.classList.add('locked-step');
                    step.classList.remove('progress-step-active', 'completed');
                    const lockIcon = document.createElement('i');
                    lockIcon.className = 'fa fa-lock lock-icon';
                    step.appendChild(lockIcon);
                }
            });
            // Set progress bar to verification step
            const progress = document.getElementById('progress');
            progress.style.width = '40%';
        } else if (order_status === "in review") {
            // Set the active step to Verification (index 2)
            showStep(2);
        } else if (order_status === "order_submitted") {
            // Set the active step to Payment Processing (index 3)
            showStep(3);
        } else if (order_status === "in printing") {
            // Set the active step to In Printing (index 4)
            showStep(4);
        } else if (order_status === "delivered") {
            // Set the active step to Delivered (index 5)
            showStep(5);
        } else {
            // Default behavior
            updateProgressBar(0);
        }

        progressSteps.forEach((step, index) => {
            // Add tooltip container to each step
            const tooltipContainer = document.createElement('div');
            tooltipContainer.className = 'tooltip-container';
            step.appendChild(tooltipContainer);

            step.addEventListener("click", function (event) {
                const isDigitalCard = '<?= strtolower($order_details->id_card_type) ?>' === 'digital card';

                // For digital cards, prevent clicking on payment, printing, and delivered steps
                if (isDigitalCard && (index === 3 || index === 4 || index === 5)) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }

                // For non-digital cards, use the original status-based restrictions
                if (!isDigitalCard && (
                    (order_status === "in review" && (index === 3 || index === 4 || index === 5)) ||
                    (order_status === "order_submitted" && (index === 4 || index === 5)) ||
                    (order_status === "in printing" && index === 5))) {
                    // Prevent default action for restricted steps
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }

                showStep(index);
            });

            // Add visual indication for locked steps
            const isDigitalCard = '<?= strtolower($order_details->id_card_type) ?>' === 'digital card';
            if ((isDigitalCard && (index === 3 || index === 4 || index === 5)) ||
                (order_status === "in review" && (index === 3 || index === 4 || index === 5)) ||
                (order_status === "order_submitted" && (index === 4 || index === 5)) ||
                (order_status === "in printing" && index === 5)) {
                step.classList.add('locked-step');

                // Add hover effect to show tooltip
                step.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'progress-tooltip';

                    let tooltipMessage = '';
                    if (isDigitalCard) {
                        tooltipMessage = 'This step is not applicable for digital cards.';
                    } else if (order_status === "in review") {
                        tooltipMessage = 'This step is locked. Order is currently under verification.';
                    } else if (order_status === "order_submitted") {
                        tooltipMessage = 'This step is locked. Order is currently in payment processing.';
                    } else if (order_status === "in printing" && index === 5) {
                        tooltipMessage = 'This step is locked. Order is currently in printing.';
                    }

                    tooltip.innerHTML = `<div class="tooltip-arrow"></div><div class="tooltip-content"><i class="fa fa-info-circle"></i> ${tooltipMessage}</div>`;

                    // Clear any existing tooltips
                    tooltipContainer.innerHTML = '';
                    tooltipContainer.appendChild(tooltip);
                });

                step.addEventListener('mouseleave', function() {
                    // Clear tooltip immediately on mouse leave
                    tooltipContainer.innerHTML = '';
                });

                // Add lock icon
                const lockIcon = document.createElement('i');
                lockIcon.className = 'fa fa-lock lock-icon';
                step.appendChild(lockIcon);
            }
        });
    });
</script>