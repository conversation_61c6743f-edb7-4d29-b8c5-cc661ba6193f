<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Role extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    //Utility Methods
    public function Kolkata_datetime() {
        $timezone = new DateTimeZone("Asia/Kolkata");
        $date = new DateTime();
        $date->setTimezone($timezone);
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    //Role - CRUD functions
    public function submit_role() {
        $userId = $this->authorization->getAvatarStakeHolderId();
        $roles = array(
            'name' => $this->input->post('role_name'),
            'description' => $this->input->post('description'),
            'active' => 1,
            'created' => $this->Kolkata_datetime(),
            'modified' => $this->Kolkata_datetime(),
            'last_modified_by' => $userId,
        );
        return $this->db->insert('roles', $roles);
    }

    public function get_all_roles() {   
        $result = $this->db->select("r.*, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName, sm.status")
                ->from('roles r')
                ->join('roles_staff rs', 'r.id=rs.role_id', 'left')
                ->join('staff_master sm', 'sm.id=rs.staff_id', 'left')
                ->get()->result();
        $data = array();
        foreach ($result as $key => $value) {
            if(!array_key_exists($value->id, $data)) {
                $data[$value->id] = array();
                $data[$value->id]['name'] = $value->name;
                $data[$value->id]['description'] = $value->description;
                $data[$value->id]['active'] = $value->active;
                $data[$value->id]['staff'] = array();
            }
            if ($value->status == 2) {
                $data[$value->id]['staff'][] = $value->staffName;
            }
        }
        // echo '<pre>'; print_r($data); die();
        return $data;
    }

    public function switch_role_mode() {
        $role_id = $this->input->post('role_id');
        $mode = $this->input->post('mode');

        //Flip the role to active/inactive
        $newMode = !$mode;
        $status = $newMode ? 'Role Activated' : 'Role Deactivated';
        $history_data = array(
            'role_id' => $role_id,
            'staff_id' => '-',
            'role_staff_status' => $status,
            'modified_date_time' => date('Y-m-d H:i:s'),
            'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->insert('roles_staff_history', $history_data);
//				echo '<pre>';print_r($newMode);die();
        //$userId = $this->ion_auth->get_user_id();
        $data = array(
            'active' => $newMode,
            'created' => $this->Kolkata_datetime(),
            'modified' => $this->Kolkata_datetime(),
            'last_modified_by' => $this->authorization->getAvatarStakeHolderId(),
        );
        $this->db->where('id', $role_id);

        return $this->db->update('roles', $data);
    }

    //Privileges CRUD functions
    public function get_privileges() {
        $this->db->select('p.id as p_id, ps.id as ps_id,p.name as p_name, ps.name as ps_name, p.enable_allocation');
        $this->db->from('privileges p');
        $this->db->join('privileges_sub ps', 'ps.privilege_id=p.id');
        $this->db->order_by('p.name, ps.name');
        return $this->db->get()->result();
    }

    public function get_all_staff() {
        $this->db->select("sm.id,concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as staffName, staff_type");
        $this->db->from('staff_master sm');
        $this->db->where('sm.status',2);
        //$this->db->join('roles_staff','roles_staff.staff_id=users.id','left');
        return $this->db->get()->result();
    }

    public function get_staffassignedby_roleid($role_id) {
        $this->db->select("sm.id,concat(ifnull(sm.first_name,''), ' ',ifnull(sm.last_name,'')) as first_name");
        $this->db->from('roles_staff');
        $this->db->where('role_id', $role_id);
        $this->db->join('staff_master sm', 'sm.id=roles_staff.staff_id');
        $this->db->where('sm.status',2);
        return $this->db->get()->result();
    }

    public function updateStaffPermissionsTable() {
        //get the latest permissions assigned
        $sql = "SELECT concat(p.name, '.', ps.name) as privilege, rs.staff_id 
                from privileges p 
                join privileges_sub ps on ps.privilege_id=p.id 
                join roles_privileges_sub rsp on rsp.privilege_sub_id=ps.id 
                join roles_staff rs on rs.role_id=rsp.role_id 
                join roles r on r.id=rs.role_id 
                where r.active=1 and p.active=1";
        $permissions = $this->db->query($sql)->result_array();

        $this->db->trans_start();
        //truncate the existing data
        $this->db->query("TRUNCATE TABLE staff_permissions");

        //insert latest permissions set
        $this->db->insert_batch('staff_permissions', $permissions);
        $this->db->trans_complete();

        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return false;
        }
        $this->db->trans_commit();
        return true;
    }

    public function assign_staff_and_privileges_to_roles($role_id) {
        $this->db->trans_start();
        $staffIdArr = $this->input->post('staffIdArr');
        $subPrivilegesArr = $this->input->post('privileges');
        // $subPrivilegesArr = $this->input->post('subPrivilegesArr');

        //Prepare data array to insert
        foreach ($staffIdArr as $key => $staff) {
            $staffData[] = array(
                'role_id' => $role_id,
                'staff_id' => $staffIdArr[$key],
                'created' => $this->Kolkata_datetime(),
                'modified' => $this->Kolkata_datetime(),
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
        }

        $this->db->select('staff_id')
            ->from('roles_staff')
            ->where('role_id', $role_id);
        $active_staff_query = $this->db->get();
        $active_staff_ids = array_column($active_staff_query->result(), 'staff_id');
        
        //Delete all existing staff for the role
        $this->db->where('role_id', $role_id);
        $query = $this->db->get('roles_staff')->row();
        if (!empty($query)) {
            //There are existing staff for this role, delete all of them
            $this->db->where('role_id', $role_id);
            $del = $this->db->delete('roles_staff');
        }
        //Insert staff for the role
        $this->db->insert_batch('roles_staff', $staffData);

        $staffs_to_add = array_diff($staffIdArr, $active_staff_ids); // IDs in $staffIdArr but not in $active_staff_ids
        $staffs_to_remove = array_diff($active_staff_ids, $staffIdArr); // IDs in $active_staff_ids but not in $staffIdArr

        $role_name = $this->get_role_name($role_id);
        $staff_sub = $this->staff_data_for_email($staffs_to_add, $staffs_to_remove, $role_name);

        // Add new staff to the roles_staff_history table
        foreach ($staffs_to_add as $staff_id) {
            $data = array(
                'role_id' => $role_id,
                'staff_id' => $staff_id,
                'role_staff_status' => 'Role Added', // Set status as 'added'
                'modified_date_time' => date('Y-m-d H:i:s'), // Current modified_date_time
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
            $this->db->insert('roles_staff_history', $data);
        }

        // Remove staff from the roles_staff_history table
        foreach ($staffs_to_remove as $staff_id) {
            $data = array(
                'role_id' => $role_id,
                'staff_id' => $staff_id,
                'role_staff_status' => 'Role Removed', // Set status as 'removed'
                'modified_date_time' => date('Y-m-d H:i:s'), // Current modified_date_time
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
            $this->db->insert('roles_staff_history', $data);
        }

        //Now, insert the privileges for the role
        foreach ($subPrivilegesArr as $key => $subPrivilegeId) {
            $subPrivilegesData[] = array(
                'role_id' => $role_id,
                'privilege_sub_id' => $subPrivilegesArr[$key],
                'created_on' => $this->Kolkata_datetime(),
                'modified_on' => $this->Kolkata_datetime(),
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
        }

        $this->db->select('privilege_sub_id')
            ->from('roles_privileges_sub')
            ->where('role_id', $role_id);
        $active_staff_query = $this->db->get();
        $active_sub_privilege_ids = array_column($active_staff_query->result(), 'privilege_sub_id');

        //Delete all existing privileges for the role
        $this->db->where('role_id', $role_id);
        $result = $this->db->get('roles_privileges_sub')->row();
        if (!empty($result)) {
            //There are exisiting privileges, delete them
            $this->db->where('role_id', $role_id);
            $del1 = $this->db->delete('roles_privileges_sub');
        }

        if(!empty($subPrivilegesData))
            $this->db->insert_batch('roles_privileges_sub', $subPrivilegesData);

        $this->db->trans_complete();

        $privileges_to_add = array_diff($subPrivilegesArr, $active_sub_privilege_ids); // IDs in $subPrivilegesArr but not in $active_sub_privilege_ids
        $privileges_to_remove = array_diff($active_sub_privilege_ids, $subPrivilegesArr); // IDs in $active_sub_privilege_ids but not in $staffIdArr
        
        // Add new staff to the roles_privileges_history table
        $privilege_sub = $this->privilege_data_for_email($privileges_to_add, $privileges_to_remove, $role_name);
        foreach ($privileges_to_add as $sub_privilege_id) {
            $data = array(
                'role_id' => $role_id,
                'sub_privilege_id' => $sub_privilege_id,
                'role_privilege_status' => 'Privilege Added', // Set status as 'added'
                'modified_date_time' => date('Y-m-d H:i:s'), // Current modified_date_time
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
            $this->db->insert('roles_privileges_history', $data);
        }

        // Remove staff from the roles_privileges_history table
        foreach ($privileges_to_remove as $sub_privilege_id) {
            $data = array(
                'role_id' => $role_id,
                'sub_privilege_id' => $sub_privilege_id,
                'role_privilege_status' => 'Privilege Removed', // Set status as 'removed'
                'modified_date_time' => date('Y-m-d H:i:s'), // Current modified_date_time
                'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
            );
            $this->db->insert('roles_privileges_history', $data);
        }
        $db_res = $this->db->trans_status();
        if ($db_res) {
            $email_res = $this->send_role_change_email($staff_sub, $privilege_sub);
            return ['db_res' => true, 'email_res' => $email_res];
        } else {
            return ['db_res' => false, 'email_res' => false];
        }      
    }

    public function send_role_change_email($staff_sub, $privilege_sub){
        // Load required models and helpers
        $this->load->helper('email_helper');
        $this->load->model('communication/emails_model'); // Load email model once
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        // Fetch role change email template
        $role_change_template = $this->get_template_for_role_change();

        if(empty($role_change_template))
            return 0;

        if(trim($role_change_template->members_email) == '')
            return 0;

        $memberEmail = [];
        
        $merge = array_merge($staff_sub, $privilege_sub);
        
        $all_email_contents = '';
        foreach ($merge as $key => $value) {
            $all_email_contents .= $value['email_content'] . "\n";
        }

        $email_master_data = array(
            'subject' => "Role/Privilege Change",
            'body' => $all_email_contents,
            'source' => 'Role/Privilege Change',
            'sent_by' => 1,
            'recievers' => $role_change_template->members_email,
            'from_email' => $role_change_template->registered_email,
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => $role_change_template->members_email,
            'sending_status' => 'Completed'
        );
        
        $email_master_id = $this->emails_model->saveEmail($email_master_data);

        $this->load->model('Birthday_Notifications_Model');
        $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($role_change_template->members_email);
        $email_data = [];
        foreach ($members_data as $key => $val) {
            if(empty($val->stf_email))
                continue;
            if(!empty($val->stf_email))
                $memberEmail[] = trim($val->stf_email);

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $val->staff_id;
            $email_obj->avatar_type = $val->avatar_type;
            $email_obj->email = $val->stf_email;
            $email_data[] = $email_obj;
        }
        if(!empty($email_data)){
            $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        }

        $this->load->helper('email_helper');
        if(!empty($memberEmail)){
            return sendEmail($all_email_contents, "Role/Privilege Change", $email_master_id, $memberEmail, $role_change_template->registered_email, []);
        } else {
            return 0;
        }
    }

    private function get_staff_data($staff_id){
        $sql = "SELECT CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) AS name
                FROM staff_master sm
                WHERE id = ?";
        return $this->db_readonly->query($sql, array($staff_id))->row();
    }

    private function staff_data_for_email($staffs_to_add, $staffs_to_remove, $role) {
        $all_staff = [];
    
        // Process staff to add
        if (!empty($staffs_to_add)) {
            foreach ($staffs_to_add as $staff_id) {
                $staff_data = $this->get_staff_data($staff_id);
                $staff_data->action = 'Added';
                $all_staff[] = $staff_data;
            }
        }
    
        // Process staff to remove
        if (!empty($staffs_to_remove)) {
            foreach ($staffs_to_remove as $staff_id) {
                $staff_data = $this->get_staff_data($staff_id);
                $staff_data->action = 'Removed';
                $all_staff[] = $staff_data;
            }
        }
    
        // Formatting for email
        $email_data = [];
        foreach ($all_staff as $staff) {
            // Set email content for each staff member
            $email_content = "<p>Staff: {$staff->name} has been {$staff->action} to Role {$role->name}.</p>";

            $email_data[] = [
                'email_content' => $email_content
            ];
        }
        return $email_data;
    } 

    private function get_privileges_data($privilege_id){
        $sql = "SELECT CONCAT(p.name, ' - ', ps.name) as name
                from privileges_sub ps
                join privileges p on p.id = ps.privilege_id
                where ps.id = $privilege_id";
        return $this->db_readonly->query($sql)->row();
    }

    private function privilege_data_for_email($privileges_to_add, $privileges_to_remove, $role) {
        $all_staff = [];
    
        // Process staff to add
        if (!empty($privileges_to_add)) {
            foreach ($privileges_to_add as $privileges_id) {
                $staff_data = $this->get_privileges_data($privileges_id);
                $staff_data->action = 'Added';
                $all_staff[] = $staff_data;
            }
        }
    
        // Process staff to remove
        if (!empty($privileges_to_remove)) {
            foreach ($privileges_to_remove as $privileges_id) {
                $staff_data = $this->get_privileges_data($privileges_id);
                $staff_data->action = 'Removed';
                $all_staff[] = $staff_data;
            }
        }
    
        $email_data = [];
        foreach ($all_staff as $staff) {
            $email_content = "<p>Privilige: {$staff->name} has been {$staff->action} to Role {$role->name}.</p>";
            $email_data[] = [
                'email_content' => $email_content
            ];
        }
    
        return $email_data;
    } 
   
    public function get_roles(){
        $sql = "select id, name from roles where active = 1";
        return $this->db_readonly->query($sql)->result();
    }

    public function get_role_name($role_id){
        $sql = "select name from roles where id = $role_id";
        return $this->db_readonly->query($sql)->row();
    }

    public function get_role_history($role_id, $from_date, $to_date) {
        // Start building the base SQL query
        $from_date = date('Y-m-d', strtotime($from_date));
        $to_date = date('Y-m-d', strtotime($to_date));

        $sql = "
            SELECT 
                rsh.role_id AS role_id,
                r.name AS entity_name,
                rsh.role_staff_status AS status,
                rsh.modified_date_time AS modified_date_time,
                CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) AS name,
                CONCAT(CASE 
                    WHEN sm1.first_name IS NULL AND sm1.last_name IS NULL THEN '-' 
                    ELSE CONCAT(IFNULL(sm1.first_name,''), ' ', IFNULL(sm1.last_name,'')) 
                END) AS modified_by
            FROM roles_staff_history rsh
            LEFT JOIN roles r on r.id = rsh.role_id
            LEFT JOIN staff_master sm ON sm.id = rsh.staff_id
            LEFT JOIN staff_master sm1 ON sm1.id = rsh.last_modified_by
            WHERE DATE(rsh.modified_date_time) BETWEEN '$from_date' AND '$to_date'
        ";

        if (!empty($role_id)) {
            $sql .= " AND rsh.role_id = $role_id";
        }

        $sql .= "
            UNION ALL
            SELECT 
                rph.role_id AS role_id,
                CONCAT(p.name, ' - ',ps.name) AS name,
                rph.role_privilege_status AS status,
                rph.modified_date_time AS modified_date_time,
                r.name AS entity_name,
                CONCAT(CASE 
                    WHEN sm1.first_name IS NULL AND sm1.last_name IS NULL THEN '-' 
                    ELSE CONCAT(IFNULL(sm1.first_name,''), ' ', IFNULL(sm1.last_name,'')) 
                END) AS modified_by
            FROM roles_privileges_history rph
            LEFT JOIN roles r on r.id = rph.role_id
            LEFT JOIN privileges_sub ps on ps.id = rph.sub_privilege_id
            LEFT JOIN privileges p on p.id = ps.privilege_id
            LEFT JOIN staff_master sm1 ON sm1.id = rph.last_modified_by
            WHERE DATE(rph.modified_date_time) BETWEEN '$from_date' AND '$to_date'
        ";

        if (!empty($role_id)) {
            $sql .= " AND rph.role_id = $role_id";
        }
        $sql .= " ORDER BY modified_date_time DESC";

        // Execute the combined query
        $combined_result = $this->db_readonly->query($sql)->result();
        return $combined_result;
        // return $this->db_readonly->last_query();
    }
    

    public function get_privilegesassignedby_roleid($role_id) {
        $this->db->where('role_id', $role_id);
        $result = $this->db->get('roles_privileges_sub')->result();

        foreach ($result as $key => $res) {
            $this->db->select('privileges.id as p_id,privileges_sub.id as ps_id,privileges.name as p_name, privileges_sub.name as ps_name');
            $this->db->from('privileges_sub');
            $this->db->where('privileges_sub.id', $res->privilege_sub_id);
            $this->db->join('privileges', 'privileges.id=privileges_sub.privilege_id');
            $this->db->order_by('privileges.name, privileges_sub.name');
            $resultvalue[] = $this->db->get()->result();
        }

        //TODO
        if (!empty($resultvalue)) {
            $arraydata = array_filter(array_map('array_filter', $resultvalue));
            foreach ($arraydata as $key => $srach) {
                foreach ($srach as $key => $val) {
                    $data[] = $val;
                }
            }
            return $data;
        } else {
            return 0;
        }
    }

    // Privileges insert data

    public function submit_privileges() {
        $privileges = $this->input->post('privileges');
        $sub_privileges = $this->input->post('sub_privileges'); // array data
        $sub_description = $this->input->post('description'); // array data

        $this->db->trans_start();
        $privileges_data = array(
            'name' => $privileges,
            'active' => 1,
        );

        $this->db->insert('privileges', $privileges_data);
        $privilege_id = $this->db->insert_id();
        foreach ($sub_privileges as $key => $sub) {
            $sub_privileges_data[] = array(
                'name' => $sub,
                'description' => $sub_description[$key],
                'privilege_id' => $privilege_id,
                'active' => 1
            );
        }
        $this->db->insert_batch('privileges_sub', $sub_privileges_data);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function addPrivilege($name) {
        $privileges_data = array(
            'name' => $name,
            'active' => 1,
        );

        return $this->db->insert('privileges', $privileges_data);
    }

    public function addSubPrivilege($sub_name, $privilege_id) {
        // check if it does not exists
        $is_sub_privilege_alredy_exists=$this->db->select("name")
        ->from("privileges_sub")
        ->where("name",$sub_name)
        ->where("privilege_id",$privilege_id)
        ->get()->result();

        if(!empty($is_sub_privilege_alredy_exists)){
            // sub privilege already exists under specific privilege, so no need to add once again
            return 2;
        }

        $sub_privilege_data = array(
            'name' => $sub_name,
            'description' => '',
            'privilege_id' => $privilege_id,
            'active' => 1
        );
        return $this->db->insert('privileges_sub', $sub_privilege_data);
    }

    public function removeSubPrivilege($sub_priv_id) {
        $this->db->trans_start();
        $this->db->where('privilege_sub_id', $sub_priv_id)->delete('roles_privileges_sub');
        $this->db->where('id', $sub_priv_id)->delete('privileges_sub');
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function get_privilegesAll() {
        $data_info = array();
        $pData = $this->db->select('p.id as pId, p.name as pName, p.enable_allocation')
                        ->from('privileges p')
                        ->get()->result();
        $data_info['prev_data'] = $pData;
        //echo "<pre>"; print_r( $data_info['pData']);
        $psData = $this->db->select('ps.id as psId, ps.name as spName,ps.privilege_id')
                        ->from('privileges_sub ps')
                        ->get()->result();
        $data_info['sub_prev_data'] = $psData;


        return $data_info; //echo "<pre>"; print_r($rr); die();
//	    	$result = $this->db->get('privileges')->result();
//                
//	    	foreach ($result as $key => $res){ 
//	    		$this->db->select('*');
//	    		$this->db->where('privilege_id',$res->id);
//	    	 $sub_data[$res->name] = $this->db->get('privileges_sub')->result();
//	    	}
//	    	if (!empty($sub_data)) {
//	    		return  $sub_data;
//	    	}else{
//	    		return 0;
//	    	}
    }

    public function get_privilegesnamebyId($privilege_id) {
        $this->db->select('privileges.id as privilege_id, privileges.name');
        $this->db->from('privileges');
        $this->db->where('privileges_sub.privilege_id', $privilege_id);
        $this->db->join('privileges_sub', 'privileges_sub.privilege_id=privileges.id');
        return $this->db->get()->row();
    }

    public function submit_add_sub_privileges($privilege_id) {
        $sub_privileges = $this->input->post('sub_privileges'); // array data
        $sub_description = $this->input->post('description'); // array data
        foreach ($sub_privileges as $key => $sub) {
            $sub_addprivileges_data[] = array(
                'name' => $sub,
                'description' => $sub_description[$key],
                'privilege_id' => $privilege_id,
                'active' => 1
            );
        }
        return $this->db->insert_batch('privileges_sub', $sub_addprivileges_data);
    }

    public function edit_Privileges($id) {
        $this->db->select('*');
        $this->db->where('id', $id);
        $this->db->from('privileges');
        return $this->db->get()->row();
    }

    public function update_Privileges($id) {
        $input = $this->input->post();
        $data = array(
            'name' => $input['edit_privileges'],
        );
        $this->db->where('id', $id);
        return $this->db->update('privileges', $data);
    }

    public function delete_Privileges($id) {
        $this->db->where('id', $id);
        return $this->db->delete('privileges');
    }

    public function get_SubPrivileges($id) {
        $this->db->select('*');
        $this->db->where('privilege_id', $id);
        $this->db->from('privileges_sub');
        return $this->db->get()->result();
        //  echo "<pre>";print_r();die()
    }

    public function update_SubPrivileges($id, $privilege_id, $name) {

        $data = array(
            'name' => $name,
        );
        $this->db->where('id', $id);
        $this->db->where('privilege_id', $privilege_id);
        return $this->db->update('privileges_sub', $data);
    }

    public function delete_SubPrivileges($id, $privilege_id) {
        $this->db->where('id', $id);
        $this->db->where('privilege_id', $privilege_id);
        return $this->db->delete('privileges_sub');
    }

    public function getRoleName($role_id) {
        return $this->db->select('name')->where('id', $role_id)->get('roles')->row()->name;
    }

    public function getPrivilegeIds($priv_name, $subs){
        $subStr = implode("' OR s.name LIKE '", $subs);
        return $this->db->select('s.id as psId, p.id as pId, s.name as psName, p.name as pName')
            ->from('privileges_sub s')
            ->join('privileges p', 'p.id=s.privilege_id')
            ->where("p.name LIKE '$priv_name'")
            ->where("(s.name LIKE '$subStr')")
            ->get()->result();
        // $subIds = array();
        // foreach ($return as $value) {
        //     array_push($subIds, $value);
        // }
        return $subIds;
    }

    public function getStaffListByPrivilege($privilege, $sub_privilege) {
        $sql = "select rs.staff_id 
                from roles_staff rs 
                join roles_privileges_sub rps on rps.role_id=rs.role_id 
                where rps.privilege_sub_id in (
                    select id from privileges_sub where name='$sub_privilege' and privilege_id in (
                        select id from privileges where name='$privilege'
                    )
                )";
        $staffs = $this->db->query($sql)->result();
        $staff_ids = [];
        foreach ($staffs as $staff) {
            $staff_ids[] = $staff->staff_id;
        }
        return $staff_ids;
    }

    public function changeAllocationStatus($privilege_id, $status) {
        return $this->db->where('id', $privilege_id)->update('privileges', ['enable_allocation' => $status]);
    }

    public function get_staff_list(){
        $sql = "select sm.id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name
        from staff_master sm order by first_name,last_name";

        $staff_list = $this->db->query($sql)->result();

        return $staff_list;

    }

    public function viewRolesByStaff($staff_id){
        $sql = "select rs.id as roles_staff_id, rs.staff_id, rs.role_id, r.name as role_name from roles_staff rs
        join roles r on rs.role_id=r.id 
        where staff_id = '$staff_id'
        order by r.name";

        $staff_roles = $this->db->query($sql)->result();
        
        $data['staff_roles'] = $staff_roles;
        return $data;
    }

    public function get_role_privileges($staff_id, $role_id){

        $sql = "select id, name from privileges ";
        $all_privileges = $this->db->query($sql)->result();

        $sql = "select id, name, privilege_id from privileges_sub order by name";
        $all_sub_privileges = $this->db->query($sql)->result();

        if($role_id==0){
            $sql = "select rs.role_id, r.name as role_name, rps.privilege_sub_id as sub_privilege_id, ps.name as sub_privilege_name from roles_staff rs
            join roles r on rs.role_id=r.id 
            join roles_privileges_sub rps on r.id = rps.role_id
            join privileges_sub ps on ps.id = rps.privilege_sub_id
            where staff_id = '$staff_id' order by r.name, rps.privilege_sub_id";
            $privileges = $this->db->query($sql)->result();
        }
        else{
            $sql = "select rs.role_id, r.name as role_name, rps.privilege_sub_id as sub_privilege_id, ps.name as sub_privilege_name from roles_staff rs
            join roles r on rs.role_id=r.id 
            join roles_privileges_sub rps on r.id = rps.role_id
            join privileges_sub ps on ps.id = rps.privilege_sub_id
            where staff_id = '$staff_id' and r.id='$role_id'
            order by r.name, rps.privilege_sub_id";
            $privileges = $this->db->query($sql)->result();
        }
        

        foreach($all_privileges as $p){
            $p->sub_privileges = array();
            foreach($all_sub_privileges as $sp){
                $temp_obj = new stdClass();
                if($p->id == $sp->privilege_id){           
                    $temp_obj->sub_privilege_id = $sp->id;
                    $temp_obj->sub_privilege_name = $sp->name;
                    $temp_obj->assigned = '0';
                    $temp_obj->from = '';
                    $p->sub_privileges[] = $temp_obj;
                }
            }
        }

        foreach($all_privileges as $p){
            $from_all = '';
            foreach($p->sub_privileges as &$sub){
                    $assigned = 0;
                    $from = '';
                foreach($privileges as $item){
                    if($sub->sub_privilege_id == $item->sub_privilege_id){
                        $assigned = '1';
                        if(!empty($from))
                        $from = $from .',';
                        $from .= $from . $item->role_name;
                    }
                }
                $sub->assigned=$assigned;
                $sub->from=implode(',', array_filter(array_unique(explode(',',$from))));
                if(!empty($from_all))
                $from_all = $from_all .',';
                $from_all .= $sub->from;
                

            }
            $p->from = implode(',', array_filter(array_unique(explode(',',$from_all))));
        }

        $data['all_privileges'] = $all_privileges;

        //echo "<pre>"; print_r($all_privileges);die();

        return $data;
    }

    public function get_privilege_list(){
        $sql = "select id, name from privileges";
        $privilege_list = $this->db->query($sql)->result();

        return $privilege_list;
    }

    public function get_sub_previleges_list($privilege_id){
        $sql = "select id, name, privilege_id from privileges_sub where privilege_id = $privilege_id order by id";
        $sub_privileges_list = $this->db->query($sql)->result();

        return $sub_privileges_list;
    }

    public function viewRolesByprivilege($privilege_id, $sub_privilege_id){
        $sql = "SELECT rs.role_id, r.name AS role_name, rps.privilege_sub_id AS sub_privilege_id, 
                ps.name AS sub_privilege_name, staff_id, 
                CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name
                FROM roles_staff rs
                JOIN roles r ON rs.role_id = r.id 
                JOIN staff_master sm ON sm.id = rs.staff_id
                JOIN roles_privileges_sub rps ON r.id = rps.role_id
                JOIN privileges_sub ps ON ps.id = rps.privilege_sub_id
                WHERE privilege_id = $privilege_id";
        if (!empty($sub_privilege_id)) {
            $sql .= " AND rps.privilege_sub_id = $sub_privilege_id";
        }
        $sql .= " ORDER BY sm.first_name, rps.privilege_sub_id";

        $privileges = $this->db->query($sql)->result();

        $grouped_data = [];
        foreach ($privileges as $privilege) {
            $sub_privilege_id = $privilege->sub_privilege_id;
            $role_id = $privilege->role_id;
            
            // Grouping starts from sub_privileges
            if (!isset($grouped_data[$sub_privilege_id])) {
                $grouped_data[$sub_privilege_id] = [
                    'sub_privilege_name' => $privilege->sub_privilege_name,
                    'sub_privilege_id' => $privilege->sub_privilege_id,
                    'roles' => []
                ];
            }
            
            // Grouping within the sub_privileges by roles
            if (!isset($grouped_data[$sub_privilege_id]['roles'][$role_id])) {
                $grouped_data[$sub_privilege_id]['roles'][$role_id] = [
                    'role_name' => $privilege->role_name,
                    'staff' => []
                ];
            }
            
            // Add staff details to the respective role within the sub_privilege
            $grouped_data[$sub_privilege_id]['roles'][$role_id]['staff'][] = [
                'staff_name' => $privilege->staff_name,
                'staff_id' => $privilege->staff_id
            ];
        }

        // Assign the grouped data to the output
        $data['grouped_privileges'] = $grouped_data;
        return $data;
    }

    public function unassign_staff_from_role($staff_id, $role_id){
        $this->db->where('role_id', $role_id);
        $this->db->where('staff_id',$staff_id);
        $x = $this->db->delete('roles_staff');

        return $x;
    }

    public function get_template_for_role_change(){
		$this->db_readonly->select('registered_email, members_email');
		$this->db_readonly->from('email_template');
		$this->db_readonly->where('name',"role change template");
		return $this->db_readonly->get()->row();
	}

}
