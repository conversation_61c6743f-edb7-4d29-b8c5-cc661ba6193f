<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Admission_controller extends CI_Controller {

	public function __construct() {		
        parent::__construct();
        if (!isset($_SESSION['loginstatus']))
            redirect('admissions');

        $this->load->model('Admission_model');
        $this->load->library('session');
        $this->load->helper('captcha');
    	$this->config->load('form_elements');
        $this->load->library('filemanager');
        $this->load->library('payment_application');
	}

    public function form_templates(){
        $data['disable_new_admissions_tab'] = $this->settings->getSetting('disable_new_admissions_tab');
		$data['no_application_display'] = $this->settings->getSetting('admissions_no_application_display_text');
        $data['my_application'] = $this->Admission_model->get_all_admission_process($data['au_id']);
        $data['admissions'] = $this->Admission_model->admission_settings_get();
        foreach ($data['my_application'] as $key => &$application) {
            $application->revert_info =  $this->Admission_model->get_revert_admission_info($application->id);
            $application->rejected_documents = $this->Admission_model->get_rejected_documents($application->id);
            foreach ($data['admissions'] as $key => $val) {
                $class_applied_for = json_decode($val['class_applied_for'], true);
                if ($val['open_for_admissions'] == TRUE) {
                    if ($application->admission_setting_id == $val['id']) {
                        $application->enable_partial_payment = $val['enable_partial_payment'];
                        if (in_array($application->grade_applied_for, $class_applied_for)) {
                            $application->status = 'enabled';
                        }
                    }
                }
            }
        }
        $data['au_id'] = $this->session->userdata('admission_user_id');
        $data['main_content'] = 'admission/form/selection_classes';
        $this->load->view('admission/inc/short_application_template', $data);
    }

    // private function _get_selection_template_values($file){
    //     $admissions = $this->Admission_model->admission_settings_get();
    //     $form_value= array();
    //     foreach ($admissions as $key => $val) {
    //         if ($val['instructin_file'] == $file) {
    //             $form_value = $val;
    //         }
    //     }
    //     return $form_value;
    // }

    private function _get_admissions_settings_byId($id){
      return  $this->Admission_model->admission_settings_getbyId($id);
    }

    public function instruction(){
        $data['au_id'] = $this->input->post('auId');
        $data['admission_setting_id'] = $this->input->post('admission_setting_id');
        $data['admissions_data'] = $this->_get_admissions_settings_byId($data['admission_setting_id']);
        // echo "<pre>"; print_r($data['admissions_data']); die();
        $data['main_content'] = 'admission/form/guidelines';
        $this->load->view('admission/inc/template', $data);
    }

   
    public function created_application_no(){
        $au_Id = $this->input->post('auId');
        $admission_setting_id = $this->input->post('admission_setting_id');

        $this->db->trans_begin();
        $lastId = $this->Admission_model->create_id_admission_form_table($au_Id, $admission_setting_id,0);
        if (empty($lastId)) {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('admissions/instructions'); 
        } else {
            $this->session->set_userdata('admission_setting_id', $admission_setting_id);
            $this->session->set_userdata('lastId', $lastId);
            // $application = $application_no['name'].'/'.$application_no['year'].'/'.$application_no['prefix'].'/'.sprintf("%'.0".$application_no['digit_count']."d\n", $lastId);
            // $update_app_no = $this->Admission_model->update_application_no_lastId($au_Id,$lastId,$application);
            // if(empty($update_app_no)){
            //     $this->db->trans_rollback();
            //     $this->session->set_flashdata('flashError', 'Something went wrong');
            //     redirect('admissions/instructions');    
            // }
            $this->db->trans_commit();
            if ($this->db->trans_status()) {
                redirect('admissions/new');
            } else {
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Something went wrong');
                redirect('admissions/instructions'); 
            }
        }
    }

    public function fill_partial_application(){
        if(empty($this->input->post('auId')) || empty($this->input->post('admission_setting_id'))) {
            redirect('admissions');
        }
        $data['af_id'] = 0;
        if(!empty($this->input->post('lastId'))){
            $data['af_id'] = $this->input->post('lastId');
            $data['adm_data'] = $this->Admission_model->get_admission_minimum_data($this->input->post('lastId'));
        }
        $au_Id = $this->input->post('auId');
        $admission_setting_id = $this->input->post('admission_setting_id');
        $data['au_id']=$au_Id;
        $data['admission_setting_id']=$admission_setting_id;
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['class_applied_for'] = json_decode($data['config_val']['class_applied_for'], true);
        $data['class_friendly_name'] = $this->Admission_model->get_class_master_friendly();
        $data['admission_dispay_fields'] = $this->settings->getSetting('admission_show_enabled_fields');
        $required_fields = $this->Admission_model->get_admission_required_fields();
        $data['admission_required_fields'] = $this->__construct_name_wise_required($required_fields);
        // echo '<pre>';print_r($required_fields);die();
        $data['main_content'] = 'admission/form/new_admission/partial_application';
        $this->load->view('admission/inc/short_application_template', $data);
    }

    public function start_admission_from(){
        if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        if(empty($admission_setting_id)){
            redirect('admissions');
        }
        $au_Id = $this->session->userdata('admission_user_id');
        $data['au_id']=$au_Id;
        $data['insert_id']=$lastId;
        $data['admission_setting_id']=$admission_setting_id;
        $data['need_qualification_dropdown']=$this->Admission_model->get_need_qualification($admission_setting_id);
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        if(!empty($data['config_val']['custom_fields_json'])){
            $data['config_val']['custom_field'] = json_decode($data['config_val']['custom_fields_json']);
        }
        $data['class_friendly_name'] = $this->Admission_model->get_class_master_friendly();
        $data['class_applied_for'] = json_decode($data['config_val']['class_applied_for'], true);
        // echo "<pre>"; print_r($data['class_applied_for']);
        // echo "<pre>"; print_r($data['class_friendly_name']);
        // die();
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        $doc = array();
        if($data['config_val']['document_input_version'] == 'V1'){
            $data['documents'] = json_decode($data['config_val']['documents'], true);
            if(!empty($data['documents'])){
                foreach ($data['documents'] as $key => $docs) {
                    $doc[$docs] = $docs;
                }
            }
        }else{
            $data['documents'] = json_decode($data['config_val']['documents']);
            if(!empty($data['documents'])){
                foreach ($data['documents'] as $key => $docs) {
                    $doc[$docs->name] = $docs->name;
                } 
            }else{
                $data['documents'] = array();
            }
        }
       
        $data['documents_required_fields'] = json_decode($data['config_val']['documents_required_fields'], true);

        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['academic_years'] = $this->db->select('id,acad_year')->from('academic_year')->get()->result();
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructin_file'] = $data['config_val']['instruction_file'];
        $data['show_previous_schooling_overall_total_marks'] = $data['config_val']['show_previous_schooling_overall_total_marks'];
        $data['show_previous_schooling_overall_grade'] = $data['config_val']['show_previous_schooling_overall_grade'];
        $data['previous_schooling_classes'] = $data['config_val']['previous_schooling_classes'];
        $data['show_previous_schooling_subjects'] = $data['config_val']['show_previous_schooling_subjects'];
        $data['admission_medical'] = $this->Admission_model->get_admission_form_medical_details($lastId);
        $data['admission_vaccination'] = $this->Admission_model->get_admission_form_vaccination_details($lastId);
        $data['show_previous_schooling_subject_total_marks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_percentage'] = $data['config_val']['show_previous_schooling_subject_percentage'];
        
        // echo "<pre>"; print_r($data['show_previous_schooling_subjects']);
        //  die();
        $data['show_previous_schooling_overall_percentage'] = $data['config_val']['show_previous_schooling_overall_percentage'];
        $freez_primary_fields = $this->settings->getSetting('freez_primary_fields');
        $data['boards'] = $this->settings->getSetting('admission_board');
        $data['image_size_in_admissions'] = $this->settings->getSetting('image_size_in_admissions');
        if(empty($data['image_size_in_admissions'])){
            $data['image_size_in_admissions'] = 2;
        }
        $data['documents_size_in_admissions'] = $this->settings->getSetting('documents_size_in_admissions');
        if(empty($data['documents_size_in_admissions'])){
            $data['documents_size_in_admissions'] = 2;
        }
        $data['institute_group_name'] = $this->settings->getSetting('institute_group_name');
        if(empty($data['institute_group_name'])){
            $data['institute_group_name'] = $this->settings->getSetting('school_name');
        }
        $data['freez_primary_fields'] = explode(',', $freez_primary_fields);
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);
        if(!empty($data['final_preview']->custom_field)){
            $data['final_preview']->custom_field = (array)json_decode($data['final_preview']->custom_field);
        }
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['admission_stream'] = $this->Admission_model->get_admission_form_combinations($lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);
        if (!empty($data['prev_eduction_info'])) {
            $data['previous_year_settings'] = $data['prev_eduction_info']['year'];
        }else{
            $data['previous_year_settings'] = null;
        }

        if(!empty($data['prev_eduction_info']['class'])){
            if (!empty($data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for])) {
                $data['previous_subjects'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for];
            }
        }
       
       // echo "<pre>";print_r($data['previous_subjects']); die();
        $years = array();
        if (!empty($data['prev_eduction_info']['year'])) {
            foreach ($data['prev_eduction_info']['year'] as $key => $year) {
                $years[$year] = $year;
            }
        }

        $ryears = array();
        if (!empty($data['admission_prev_schools'])) {
            foreach ($data['admission_prev_schools'] as $key => $pSchool) {
                $ryears[$pSchool->year] = $pSchool->year;
            }
        }

        $result=array_diff($years,$ryears);
        $data['yearinfo'] = $result;

        $rdocs = array();
        if($data['config_val']['document_input_version'] == 'V1'){
            if (!empty($data['admission_form'])) {
                foreach ($data['admission_form'] as $key => $document) {
                    $rdocs[$document->document_type] = $document->document_type;
                    if(!in_array($document->document_type, $data['documents'], true)){
                        array_push($data['documents'], $document->document_type);
                    }
                }
            }
        }else{
            if (!empty($data['admission_form'])) {
                $count = count($data['documents']);
                foreach ($data['admission_form'] as $key => $document) {
                    $rdocs[$document->document_type] = $document->document_type;
                    if(!in_array($document->document_type, $doc, true)){
                        array_push($data['documents'],(object) ['name' => $document->document_type]);
                        $count++;
                    }
                }
            }
        }
        $data['subArry'] = array(
            'lang_1_choice'=> $data['final_preview']->lang_1_choice, 
            'lang_2_choice'=> $data['final_preview']->lang_2_choice, 
            'lang_3_choice'=> $data['final_preview']->lang_3_choice
        );
        $document = array_diff($doc,$rdocs);
        // $doucments = [];
        // foreach ($document as $key => $value) {
        //     if(!empty($value)){
        //         array_push($doucments, $value);
        //     }
        // }
        // $data['doucments'] = $doucments;

        // getting caste from db
        $caste = $this->Admission_model->load_caste();
        $Category = $this->settings->getSetting('category');
        $categoryOptions=[];

        // final_preview
        if(!empty($caste) && !empty($Category)){
            foreach ($caste as $key => $value) {
                foreach ($Category as $k => $v) {
                if(!array_key_exists($value->category,$categoryOptions) && $value->category == $v){
                    $object = new stdClass();
                    $object->category = ucwords($value->category);
                    $object->value = $k;
                    $categoryOptions[$value->category]=$object;
                }
                }
            }
        }

        $casteOptions = [];
        if(!empty($caste)){
            foreach ($caste as $key => $value) {
                if (!array_key_exists($value->caste, $casteOptions)) {
                    $object = new stdClass();
                    $object->category = $value->category;
                    $object->caste = ucwords($value->caste);
                    $casteOptions[$value->caste] = $object;
                }
            }
        }
        $subCasteOptions = [];
        foreach ($caste as $key => $value) {
            if (!array_key_exists($value->sub_caste, $subCasteOptions)) {
                $object = new stdClass();
                $object->caste = $value->caste;
                $object->sub_caste = ucwords($value->sub_caste);
                $subCasteOptions[$value->sub_caste] = $object;
            }
        }

        $data['caste'] = $caste;
        $data['categoryOptions'] = $categoryOptions;
        $data['casteOptions'] = $casteOptions;
        $data['subCasteOptions']=$subCasteOptions;

        if(!empty($data['caste'])){
            $student_caste_present_in_db=true;
        }else{
            $student_caste_present_in_db = false;
        }

        $data['student_caste_present_in_db']=$student_caste_present_in_db;

        $required_fields = $this->Admission_model->get_admission_required_fields();
        $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
        $required_fields = $this->Admission_model->get_health_required_fields_admission();
        $data['health_required_fields'] = $this->__construct_name_wise_health_required($required_fields);
        $data['health_disabled_fields'] = $this->Admission_model->get_health_disabled_fields_admission();
        $data['admission_form_word'] = $this->settings->getSetting('admission_form_word');
        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        $data['freez_parent_details'] = 1;
        $data['main_content'] = 'admission/form/new_admission/index';
        $this->load->view('admission/inc/template', $data);
    }

    private function __construct_name_wise_health_required($requiredData){
        $fields = $this->db->list_fields('student_health');
        $rData = [];
        foreach ($fields as $key => $val) {
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
            }else{
                $rData[$val] = array('font' =>'', 'required' =>'');
            }
        }
        return $rData;
    }

    public function get_language_class_wise_data(){
       $lang_selection = $this->Admission_model->get_language_selection_data($this->input->post('admsettingId'),$this->input->post('selectedClass'));
       echo json_encode($lang_selection);
    }

    public function get_subject_details(){
        $class = $_POST['selectedClass'];
        $admission_setting_id = $_POST['admsettingId'];
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);

        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        if(!empty($data['prev_eduction_info']['class'])){
            if (!empty($data['prev_eduction_info']['class'][$class])) {
                echo 1;
            }else{
                echo 0;
            }
        }else{
            echo 0;
        }
    }

    public function doucment_grading_dispaly(){
       if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        $au_Id = $this->session->userdata('admission_user_id');
        $data['au_id']=$au_Id;
        $data['insert_id']=$lastId;
        $data['admission_setting_id']=$admission_setting_id;

        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);

        $preSchoolingmarks = $config_val['show_previous_schooling_overall_total_marks'];

        // $show_previous_schooling_subjects = $config_val['show_previous_schooling_subjects'];

        $show_previous_schooling_subject_total_marks = $config_val['show_previous_schooling_subject_total_marks'];

        $show_previous_schooling_subject_percentage = $config_val['show_previous_schooling_subject_percentage'];

        $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);

        $template = '';
        if(!empty($prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'])){
            $template .= '<table class="table">';
            $template .= '<tr>';
            $template .= '<th>Subject</th>';
    
    
            if ($show_previous_schooling_subject_total_marks == 1) {
                $template .= '<th>Max Marks</th>';
                $template .= '<th>Marks Scored</th>';
    
            }
            if($show_previous_schooling_subject_percentage == 1){
                $template .= '<th>Percent</th>';
                $template .= '<th>Grade</th>';
            }
    
            $template .='</tr>';
            $subjectArray = $prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'];
            $m = 1;
            foreach ($subjectArray as $subject) {
                $template.= '<tr>';
                $template .= '<td>';
                $template .= ' <input type="hidden" value="'. $subject['id'] . '" name="sub_id[]">';
                switch ($subject['type']) {
                    case 'label':
                      $template .= $subject['name'];
                      $template .= ' <input type="hidden" value="'. $subject['name'] . '" name="sub_name[]">';
                      break;
                    case 'text': 
                      $template .= ' <input type="text" class="form-control" onkeyup="check_is_value_not_empty(this,'.$m.')" placeholder="'.$subject['name'].'" value="" name="sub_name[]">';
                }
                $template .= '</td>';
    
                if ($show_previous_schooling_subject_total_marks == 1) {
                    $template.= '<td><input type="number" data-parsley-error-message="Invalid number" id="maxMarks_'.$m.'" onkeyup="max_marks_total('.$m.')"name="max_marks[]" value="0" class="form-control maxMarks per"></td>';
                    $template.= '<td><input type="number" data-parsley-error-message="Invalid number" id="maxMarkScored_'.$m.'" onkeyup="max_marks_scored_total('.$m.')" name="marks_scored[]" value="0" class="form-control maxMakrsScored per"></td>';
                }
                if ($show_previous_schooling_subject_percentage == 1) {
                    $template.= '<td><input type="number"  min="0" max="100" name="percentage[]" value="0" class="form-control per"></td>';
                    $template.= '<td><input type="text" style="text-transform:uppercase; width:64px" name="grades[]" placeholder="Grade" class="form-control grd"></td>';
                }
    
              $template.= '</tr>';
              $m++;
            }
            $template .= '</table>';
        }
        
        print($template);
    }

    public function my_application(){
        $data['admission_form_word'] = $this->settings->getSetting('admission_form_word');
        $this->session->unset_userdata('lastId');
        $this->session->unset_userdata('admission_setting_id');
        $data['au_id'] = $this->session->userdata('admission_user_id');
        $data['my_application'] = $this->Admission_model->get_all_admission_process($data['au_id']);
        $data['no_application_display'] = $this->settings->getSetting('admissions_no_application_display_text');
        $admissions = $this->Admission_model->admission_settings_get();
        foreach ($data['my_application'] as $key => &$application) {
            $application->revert_info =      $this->Admission_model->get_revert_admission_info($application->id);
            foreach ($admissions as $key => $val) {
                $class_applied_for = json_decode($val['class_applied_for'], true);
                if ($val['open_for_admissions'] == TRUE) {
                    if ($application->admission_setting_id == $val['id']) {
                        if (in_array($application->grade_applied_for, $class_applied_for)) {
                            $application->status = 'enabled';
                        }
                    }
                }
            }
        }

        $data['main_content'] = 'admission/form/my_application/index';
        $this->load->view('admission/inc/template', $data);
    }

    public function preview_data(){
        $update_stream = $this->Admission_model->update_combinationfor_value();
        $update_lang = $this->Admission_model->update_language_selection_value();
        $au_Id = $this->session->userdata('admission_user_id');
        $lastId = $this->input->post('lastId');
        $admission_setting_id = $this->input->post('admission_setting_id');
        $data['au_id'] = $au_Id;
        $data['insert_id'] = $lastId;
        $data['admission_setting_id'] = $admission_setting_id;
        // echo "<pre>"; print_r($data); die();
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['enabled_medical_form'] = $this->settings->getSetting('enabled_medical_form_tab_in_admissions');
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);   
        $data['preSchoolingmarks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_total_marks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_percentage'] = $data['config_val']['show_previous_schooling_subject_percentage'];
        $data['show_previous_schooling_overall_total_marks'] = $data['config_val']['show_previous_schooling_overall_total_marks'];
        $data['show_previous_schooling_overall_percentage'] = $data['config_val']['show_previous_schooling_overall_percentage'];
        
        $data['final_preview'] = $this->Admission_model->get_admission_form_final_detailsby_all_auId($au_Id,$lastId);
        // echo "<pre>"; print_r($data['final_preview']);die();
        $data['subject_master'] = $this->Admission_model->get_subject_master_list();
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);
        if (!empty($data['prev_eduction_info']['class'])) {
            if (!empty($data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for])) {
               $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
            }
        }
        $health_disabled_fields = $this->Admission_model->get_health_disabled_fields_admission();
        $healthField = $this->db->list_fields('student_health');

        $uncheckFieldsHelath = ['id','student_id','created_on','modified_on','last_modified_by','academic_year_id','admission_form_id'];
        $hData =[];
        foreach ($healthField as $hfield){
            if (!in_array($hfield, $uncheckFieldsHelath) ) {
                array_push($hData, $hfield);
            }
        }

        $displayFields_health = [];
        foreach ($hData as $key => $value) {
            if (!in_array($value, $health_disabled_fields)) {
               array_push($displayFields_health, $value);
            }
        }
        
        $data['health_display_field'] = $displayFields_health;
        $data['admission_medical'] = (array) $this->Admission_model->get_admission_form_medical_details($lastId);
        $data['admission_vaccination'] = $this->Admission_model->get_admission_form_vaccination_details($lastId);
        $fields = $this->db->list_fields('admission_forms');
        $fData = [];
        $uncheckFields = ['id','au_id','created_on','admission_setting_id','filled_by','enquiry_id','receipt_html_path','receipt_number','template_pdf_path','seat_allotment_pdf_path','seat_allotment_no','std_photo_uri','last_modified_by','pdf_status','modified_on','custom_field','instruction_file','seat_allotment_date','nationality_other','religion_other','mother_tongue_other','father_mother_tongue_other','f_addr','f_area','f_district','f_state','f_county','f_pincode','m_addr','m_area','m_district','m_state','m_county','m_pincode','g_addr','g_state','g_county','g_pincode', 'f_company_addr','f_company_area','f_company_district','f_company_state','f_company_county','f_company_pincode','m_company_addr','m_company_area','m_company_district','m_company_state','m_company_county','m_company_pincode','g_company_addr','g_company_area','g_company_district','g_company_state','g_company_county','g_company_pincode','s_present_addr','s_present_area','s_present_district','s_present_state','s_present_country','s_present_pincode','s_permanent_addr','s_permanent_area','s_permanent_district','s_permanent_state','s_permanent_country','s_permanent_pincode','lang_1_choice','lang_2_choice','lang_3_choice','application_no','mother_mother_tongue_other','g_photo_uri','m_signature','f_signature','has_sibling','sibling_inschool_other','assigned_to','special_needs_description','res_ph','family_photo','father_photo','mother_photo','admission_link_expire_date','std_photo_uri_resize','student_signature','fee_paid_status','fee_paid_mode','is_ready_to_take_proficiency_test'];
        
        foreach ($fields as $field){
            if (!in_array($field, $uncheckFields) ) {
                array_push($fData, $field);
            }
        }

        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        // echo "<pre>"; print_r($data['admission_prev_schools']);
        // echo "<pre>"; print_r($data['disabled_fields']); die();
        $displayFields = [];
        foreach ($fData as $key => $value) {
            if (!in_array($value, $data['disabled_fields'])) {
               array_push($displayFields, $value);
            }
        }
        if (!in_array('s_present_addr', $data['disabled_fields'])) {
            array_push($displayFields, 's_present_address');
        }
        if (!in_array('s_permanent_addr', $data['disabled_fields'])) {
            array_push($displayFields, 's_permanent_address');
        }
        if (!in_array('f_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'f_address');
        }
        if (!in_array('m_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'm_address');
        }
        if (!in_array('g_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'g_address');
        }
        if (!in_array('f_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'f_company_address');
        }
        if (!in_array('f_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'm_company_address');
        }
        if (!in_array('g_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'g_company_address');
        }
        $data['fields'] = $this->_prepareAdmissionSeperateInput($displayFields);
        // echo "<pre>"; print_r($data['fields']); die();
        $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_Id,$lastId);
        // echo "<pre>"; print_r($data['combinations']);die();
        if (!empty($data['streams'])) {
            $combArray = $data['streams'];
            if(!empty($data['combinations']->combination)){
                $combArray = $data['streams'][$data['combinations']->combination];
            }
            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if(!empty($val)){
                    if ($val['id'] == $data['combinations']->combination_id) {
                        $data['comb'] = $val['name'];
                    }
                }
                
            }
        }
        
        if (!empty($data['streams'])) {
            $combArray = $data['streams'];
            if(!empty($data['combinations']->combination)){
                $combArray = $data['streams'][$data['combinations']->combination];
            }

            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if(!empty($val)){
                    if ($val['id'] == $data['combinations']->combination_id) {
                        $data['comb'] = $val['name'];
                    }
                }
            }
        }
        $data['hospitalization_details'] = $this->Admission_model->get_hospital_details($lastId);
        $data['previewData'] = (array)$data['final_preview'];
        if(!empty($data['previewData']['custom_field'])){
            $data['previewData']['custom_field'] = (array)json_decode($data['previewData']['custom_field']);
        }
        $data['main_content'] = 'admission/form/new_admission/_blocks/final_preview';
        $this->load->view('admission/inc/template', $data);
    }
    private function _prepareAdmissionSeperateInput(&$input){
        $return_data = [];
        foreach ($input as $k => $v) {
            $start_key = substr($v, 0, 2);
            if ($start_key == 'f_' || $start_key == 'fa') {
              $key = str_replace("f_", "", $v);
              $return_data['father'][$key] = $v;
            } elseif ($start_key == 'm_' || $start_key == 'mo') {
              $key = str_replace("m_", "", $v);
              $return_data['mother'][$key] = $v;
            }elseif ($start_key == 'g_' || $start_key == 'gu') {
              $key = str_replace("g_", "", $v);
              $return_data['gurdian'][$key] = $v;
            }
            else {
              $return_data['student'][$v] = $v;
            }
        }
        //echo '<pre>';print_r($return_data);

        return $return_data;
    }


    private function _sms_to_parent_admission($admin_no, $mobileNumber){
        $smsint = (array) $this->settings->getSetting('smsintergration');
        $admissionSMS = $this->settings->getSetting('admissions_sms',0);
        if (!empty($admissionSMS)) {
            $msg =  $admissionSMS;
            $msg = str_replace('%%admission_no%%',$admin_no, $msg);
        }else{
            $msg = "Your online application for registration number ".$admin_no." has been successfully submitted. To complete the registration process, kindly print, sign and submit the form at the school front office within 5 working days.";
        }
        $content =  urlencode(''.$msg.'');

        $get_url = 'http://'.$smsint['url'].'?method=sms&api_key='.$smsint['api_key'].'&to='.$mobileNumber.'&sender='.$smsint['sender'].'&message='.$content;
        return $this->curl->simple_get($get_url);
    }

    private function _email_to_parent_admission($get_email_template,$application_no=''){
        $this->load->helper('email_helper');
        if(!empty($application_no)){
            $receipt_and_admission_form = $this->db->select('template_pdf_path,receipt_html_path')->from('admission_forms')->where('application_no',$application_no)->get()->row();
        }
        $members = [];
        array_push($members, $get_email_template->f_email_id, $get_email_template->m_email_id, $get_email_template->g_email_id);
        $memberEmail = [];
        foreach ($members as $key => $val) {
            $memberEmail[] = $val;
        }
        $files_array = array();
        if(!empty($receipt_and_admission_form->template_pdf_path)) {
            array_push($files_array, array('name' => 'Admission Form.pdf', 'path' => $receipt_and_admission_form->template_pdf_path));
        }
        if(!empty($receipt_and_admission_form->receipt_html_path)) {
            array_push($files_array, array('name' => 'Admission Form Fee Receipt.pdf', 'path' => $receipt_and_admission_form->receipt_html_path));
        }
        $files_string = '';
        if(!empty($files_array)) {
            $files_string = json_encode($files_array);
        }
        $get_email_template->content = str_replace('%%student_name%%',$get_email_template->student_name,$get_email_template->content);
        $get_email_template->content = str_replace('%%application_no%%',$application_no,$get_email_template->content);
        $get_email_template->content = str_replace('%%grade_applied%%',$get_email_template->grade_applied_for,$get_email_template->content);

        $this->load->model('communication/emails_model');
        $email_data = [];
        foreach ($members as $key => $val) {
            if(empty($val)){
                continue;
            }
            $email_obj = new stdClass();
            $email_obj->stakeholder_id = 0;
            $email_obj->avatar_type = 2;
            $email_obj->email = $val;
            $email_data[] = $email_obj;
        }
        $sendersList = implode(',', $members);
        $email_master_data = array(
            'subject' => $get_email_template->email_subject,
            'body' => $get_email_template->content,
            'source' => 'Admission form filed by the parent',
            'sent_by' => 0,
            'recievers' => "Parents",
            'from_email' => $get_email_template->registered_email,
            'files' => empty($files_string) ? '':$files_string,
            'acad_year_id' => $get_email_template->academic_year_applied_for,
            'visible' => 1,
            'sending_status' => 'Completed',
            'sender_list'=>$sendersList
        );
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        return sendEmail($get_email_template->content, $get_email_template->email_subject, 0, $members,$get_email_template->registered_email, json_decode($files_string));
    }

    private function _email_to_staff_admission($get_email_template){
        if($get_email_template->members_email == ''){
            return;
        }
        $this->load->model('Birthday_Notifications_Model');
        $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($get_email_template->members_email);
        $memberEmail = [];
        $email_data = [];
        foreach ($members_data as $key => $val) {
            if(empty($val->stf_email))
                continue;
            if(!empty($val->stf_email))
                $memberEmail[] = $val->stf_email;

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $val->staff_id;
            $email_obj->avatar_type = $val->avatar_type;
            $email_obj->email = $val->stf_email;
            $email_data[] = $email_obj;
        }
        $sent_by = $this->authorization->getAvatarStakeHolderId();
        $email_master_data = array(
            'subject' => $get_email_template->email_subject,
            'body' => $get_email_template->content,
            'source' => 'Final Submit of Admission Form',
            'sent_by' => $sent_by,
            'recievers' => "Staffs",
            'from_email' => $get_email_template->registered_email,
            'files' => '',
            'acad_year_id' => $this->acad_year->getAcadYearID(),
            'visible' => 1,
            'sender_list'=> NULL,
            'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data,$email_master_id);
        $this->load->helper('email_helper');
        return sendEmail($get_email_template->content, $get_email_template->email_subject, $email_master_id, $memberEmail, $get_email_template->registered_email, []);     
    }

    public function preview_minimum_fields(){
        $data['au_id'] = $this->input->post('au_id') ? $this->input->post('au_id') : $this->session->userdata('au_id');
        $data['lastId'] = $this->input->post('lastId') ? $this->input->post('lastId') : $this->session->userdata('lastId');
        $data['admission_setting_id'] = $this->input->post('admission_setting_id') ? $this->input->post('admission_setting_id') : $this->session->userdata('admission_setting_id');
        $data['config_val'] = $this->_get_admissions_settings_byId($data['admission_setting_id']);
        $data['admission_data'] = $this->Admission_model->get_preview_minimum_fields($data['lastId']);
        if(!empty($data['admission_data']->boarding)){
            $data['admission_data']->boarding = $this->settings->getSetting('boarding')[$data['admission_data']->boarding];
        }
        $data['admission_dispay_fields'] = $this->settings->getSetting('admission_show_enabled_fields');
        $data['main_content'] = 'admission/form/fields_filled_from_enquiry_new';
		$this->load->view('admission/inc/short_application_template', $data);
    }

    public function make_partial_payment(){
        $lastId = $this->input->post('lastId');
        $admission_setting_id = $this->input->post('admission_setting_id');
        $application_fee_amount = $this->Admission_model->get_application_fees_amount($admission_setting_id);
        // echo '<pre>';print_r($application_fee_amount);die();
        $this->payment_application->init_payment_to_school($application_fee_amount, 'APPLICATION FEES', $lastId, 'admission_controller/application_fee_trans_done');
    }

    public function submit_final_form(){
        $au_id = $this->session->userdata('admission_user_id');
        $lastId = $this->input->post('lastId');
        $ready_to_take_test = '';
        if(isset($_POST['ready_to_take_test'])){
            $ready_to_take_test = $_POST['ready_to_take_test'];
        }
        $admission_setting_id = $this->input->post('admission_setting_id');
        if($this->input->post('partial_payement') == 1){
            $result=$this->Admission_model->submit_final_data($lastId,'Application Amount Paid',$ready_to_take_test);
        }else{
            $result=$this->Admission_model->submit_final_data($lastId,'Submitted',$ready_to_take_test);
        }
        $fee_paid_status = $this->db->select('payment_status')->from('admission_status')->where('af_id',$lastId)->get()->row()->payment_status;
        if($result){
            if ($this->input->post('online_enable') == 1 && $this->input->post('partial_payement') != 1 && $fee_paid_status != 'SUCCESS') {
                $this->Admission_model->submit_final_data($lastId,'Payment Pending',$ready_to_take_test);
                $get_online_payment_email_template = $this->Admission_model->get_online_payment_email_template_byID($admission_setting_id,$lastId);
                if (!empty($get_online_payment_email_template)){
                    $this->_email_to_parent_admission($get_online_payment_email_template);
                }
                $application_fee_amount = $this->input->post('application_fee_amount');
                $this->payment_application->init_payment_to_school($application_fee_amount, 'APPLICATION FEES', $lastId, 'admission_controller/application_fee_trans_done');

                //If the call comes back from init_payment_to_school function, it means some failure happened.
                $this->Admission_model->update_transcation_details($lastId,'FAILED','Draft','Online');
                $this->Admission_model->submit_final_data($lastId,'Payment Pending',$ready_to_take_test);
                $this->session->set_flashdata('flashError', 'Transcation unsuccessful');
                redirect('admissions/home');
            }
            $this->session->set_userdata('admission_setting_id', $admission_setting_id);
            $this->session->set_userdata('lastId', $lastId);

            // if receipt is already generated, we have already updated the admission forms. No need to update again. Just show the already generated receipt.
            $isApplicationGenerated = $this->Admission_model->is_application_no_generated($lastId);
            if ($isApplicationGenerated){
                redirect('admissions/print');
            }

            $this->Admission_model->update_application_receipt($lastId);
            $this->Admission_model->update_admission_application_receipt($lastId);
            $this->_generate_admission_form_pdf($lastId,$au_id,$admission_setting_id);
            $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($au_id,$lastId);

            $mobileNumber = $this->session->userdata('loginstatus');
            // Email is send only if email_template_id in admissin_settings is set. if it is null email is not send.
            $get_email_template = $this->Admission_model->get_email_template_byId($admission_setting_id,$lastId);

            

            // Email is send for staff only if email_template_id_staff and staff_id in admissin_settings is set. if it is null email is not send.
          
            if (!empty($get_email_template)){
              $this->_email_to_parent_admission($get_email_template,$admin_no);
            }

            $get_staff_email_template = $this->Admission_model->get_email_staff_template_byId($admission_setting_id,$lastId);
            if (!empty($get_staff_email_template->email_template_id_staff)){
              $this->_email_to_staff_admission($get_staff_email_template);
            }
            if (!empty($get_staff_email_template->staff_id)){
                $stafIds = json_decode($get_staff_email_template->staff_id);
                $title = 'Admission';
                $message = 'New admission form submitted through website.';
                $url = site_url('dashboard');
                $this->load->helper('texting_helper');
                $input_arr = array();
                  $input_arr['staff_ids'] = $stafIds;
                  $input_arr['mode'] = 'notification';
                  $input_arr['source'] = 'Admissions';
                  $input_arr['message'] = $message;
                  $input_arr['title'] = 'Admission';
                  sendText($input_arr);
                // sendStaffNotifications($stafIds, $title, $message, $url);
            }

            $check_returned = $this->_sms_to_parent_admission($admin_no, $mobileNumber);

            if(!empty($check_returned)) {
                $check_returned = json_decode($check_returned);
                if($check_returned->status == 'OK') {
                    redirect('admissions/print');
                } else {
                    // $this->session->set_flashdata('flashError', 'Unable to send SMS');
                    redirect('admissions/print');
                }
            } else {
                    // $this->session->set_flashdata('flashError', 'Unable to send SMS');
                    redirect('admissions/print');
            }

          // $this->session->set_flashdata('flashSuccess', 'Application submitted to the school succesfully');
        }else{
            $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
            redirect('admissions/print');
        }
    }

    public function application_fee_trans_done(){
        // trigger_error("Response at application_fee_trans_done");
        // trigger_error(json_encode($_POST));

        if ($_POST['response_type'] === 'IMMEDIATE') {
            $this->__handle_immediate_op_response($_POST);
        } elseif ($_POST['response_type'] === 'DELAYED') {
            // $this->__handle_delayed_op_response($_POST);
            $this->__handle_immediate_op_response($_POST);
        } else {
            $this->__handle_recon_op_response($_POST);
        }
    }

    private function __handle_immediate_op_response($response)
    {
        $is_payment_before_application_enabled = $this->db->select('enable_partial_payment')->from('admission_forms af')->
        join('admission_settings as','as.id=af.admission_setting_id')->where('af.id',$response['source_id'])->get()->row();
        $submit_short_application_after_payment = $this->settings->getSetting('admissions_submit_short_application_after_payment');
        if ($response['transaction_status'] === 'SUCCESS') {
            
            // if receipt is already generated, we have already updated the admission forms. No need to update again. Just show the already generated receipt.
            $isApplicationGenerated = $this->Admission_model->is_application_no_generated($response['source_id']);

            if($isApplicationGenerated && $submit_short_application_after_payment == 1){
                redirect('admissions/print');
            }

            if ($isApplicationGenerated && $is_payment_before_application_enabled->enable_partial_payment != 1){
                redirect('admissions/print');
            }

            if ($isApplicationGenerated && $is_payment_before_application_enabled->enable_partial_payment == 1){
                redirect('admissions/home');
            }
            

            if($is_payment_before_application_enabled->enable_partial_payment == 1 && $submit_short_application_after_payment != 1){
                $this->Admission_model->update_transcation_details($response['source_id'], 'SUCCESS','Draft','Online');
            }else{
                $this->Admission_model->update_transcation_details($response['source_id'], 'SUCCESS','Application Amount Paid','Online');
            }
            
            $this->Admission_model->update_application_receipt($response['source_id']);
            
            $this->Admission_model->update_admission_application_receipt($response['source_id']);
                       
            $admUsers = $this->Admission_model->get_auId_mobileNumber($response['source_id']);
            $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($admUsers->au_id,$response['source_id']);

            $this->session->set_userdata('admission_setting_id', $admUsers->admission_setting_id);
            $this->session->set_userdata('lastId', $response['source_id']);

            $admission_setting_id = $this->session->userdata('admission_setting_id');
            
            $lastId = $this->session->userdata('lastId');

            $this->_generate_admission_form_pdf($response['source_id'],$admUsers->au_id,$admUsers->admission_setting_id);

            $get_email_template = $this->Admission_model->get_email_template_byId($admUsers->admission_setting_id,$response['source_id']); 
            if ($get_email_template) {
              $this->_email_to_parent_admission($get_email_template,$admin_no);
            }

            $get_staff_email_template = $this->Admission_model->get_email_staff_template_byId($admUsers->admission_setting_id,$response['source_id']);
            if (!empty($get_staff_email_template->email_template_id_staff)){
              $this->_email_to_staff_admission($get_staff_email_template);
            }
            
            if (!empty($get_staff_email_template->staff_id)){
                $stafIds = json_decode($get_staff_email_template->staff_id);
                $title = 'Admission';
                $message = 'New admission form submitted through website.';
                $url = site_url('dashboard');
                $this->load->helper('notification_helper');
                sendStaffNotifications($stafIds, $title, $message, $url);
            }

            $this->_sms_to_parent_admission($admin_no, $admUsers->mobile_no);
            // $this->session->set_flashdata('flashSuccess', 'Transcation successful');
            // redirect('admissions/print/' . $response['source_id'] . '/' . $response['transaction_id'] . '/' . $response['transaction_date'] . '/' . $response['transaction_time']);
            if($is_payment_before_application_enabled->enable_partial_payment == 1 && $submit_short_application_after_payment !=1){
            redirect('admissions/home');
            }else{
                redirect('admissions/print');
            }
        } else {
            $this->Admission_model->update_transcation_details($response['source_id'],'FAILED','Draft','Online');

            if ($is_payment_before_application_enabled->enable_partial_payment == 1){

                $this->Admission_model->submit_final_data($response['source_id'],'Draft','');
            }else{
                $this->Admission_model->submit_final_data($response['source_id'],'Payment Pending','');
            }
            $this->session->set_flashdata('flashError', 'Transcation Unsuccessful');
            redirect('admissions/home');
        }
    }

    
    public function print_application() {

        // $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($au_id,$lastId);
        // $mobileNumber = $this->session->userdata('loginstatus');

        // $smsint = $this->settings->getSetting('smsintergration');

        // $msg = "Your online application for registration number ".$admin_no." has been successfully submitted. To complete the registration process, kindly print, sign and submit the form at the school front office within 5 working days.";
        // $content =  urlencode(''.$msg.'');
        // $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$mobileNumber.'&sender='.$smsint->sender.'&message='.$content;

        // $check_returned = $this->curl->simple_get($get_url);

        // if(!empty($check_returned)) {
        //     $check_returned = json_decode($check_returned);
        //     if($check_returned->status == 'OK') {
        //        $this->session->set_flashdata('flashSuccess', 'Successful to send SMS');
        //     } else {
        //         $this->session->set_flashdata('flashError', 'Unable to send SMS');
        //     }
        // } else {
        //     $this->session->set_flashdata('flashError', 'Unable to send SMS');
        // }
      
        $au_id = $this->session->userdata('admission_user_id');

        if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        $data['au_id'] = $au_id;
        $data['admission_setting_id'] = $admission_setting_id;
        $data['insert_id'] = $lastId;
        $data['receipts_view'] = '0';
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['application_html'] = $data['config_val']['application_form_html'];
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        // $data['transaction_id'] = $transaction_id;
        // $data['transaction_date'] = $transaction_date;
        // $data['transaction_time'] = $transaction_time;
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$lastId);
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['application_fee_status'] =  $this->Admission_model->application_fee_status($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_id,$lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);
        if (!empty($data['streams'])) {
            $combArray = $data['streams'][$data['combinations']->combination];

            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if ($val['id'] == $data['combinations']->combination_id) {
                    $data['comb'] = $val['name'];
                }
            }
        }
        if (!empty($data['prev_eduction_info']['class'])) {
            if(!empty($data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject']))
            $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
        }
        $data['main_content'] = 'admission/receipts/header_user_new';
        $this->load->view('admission/inc/print_template', $data);
    }

    public function check_application_form(){
        $admissionId = $_POST['admissionId'];
        $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
        if($admissions->pdf_status == 1){
            echo 1;
        }else{
            echo 0;
        }   
    }

    public function download_application_form($admissionId){
        $link = $this->Admission_model->get_application_form_pdf_path($admissionId);
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('Application Form.pdf', $data, TRUE); 
    }

    public function gender_pdf_application_form(){
        $admissionId = $_POST['admissionId'];
        $au_id = $_POST['au_id'];
        $admission_setting_id = $_POST['admission_setting_id'];
        echo $this->_generate_admission_form_pdf($admissionId,$au_id,$admission_setting_id);
    }


    private function _generate_admission_form_pdf($id,$au_id,$admission_setting_id){
      $enquiry_number = $this->Admission_model->get_enquiry_number($id);
      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
      if (!empty($config_val['application_form_html'])) {
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
        $admission_doc = $this->Admission_model->get_admission_form_detailsby_auId($id);
        $admission_prev_schools = $this->Admission_model->get_admission_form_previous_school_details($id);
        $subject_master = $this->Admission_model->get_subject_master_list();
        $admission_stream = $this->Admission_model->get_admission_form_combinations($id);
        $medical_health = $this->Admission_model->get_admission_form_medical_details($id);
        $streams = [];
        if(!empty($config_val['streams'])){
            $streams = json_decode($config_val['streams']);
        }
        $result = $this->_construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $config_val['application_form_html'], $subject_master, $admission_stream,$streams,$medical_health,$enquiry_number);
        if ($result) {
          // $update =  $this->Admission_model->update_admission_html_receipt($result, $id);
          $this->_generate_admisison_pdf_receipt($result, $id);
        }
      }
    }
    
    public function _construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $template, $subjectMaster, $admission_stream, $streams,$medical_health,$enquiry_number){
       $combination = '';
        if (!empty($admission_stream)) {
            $combination = $admission_stream->combination;
                foreach ($streams->$combination as $key => $val) {
                    if ($val->id == $admission_stream->combination_id) {
                        $combination = $val->name;
                    }
                }
        }
        // $student_quota = '';
        // if (!empty($final_preview->student_quota)) {
        //     foreach ($this->settings->getSetting('quota') as $key => $value) {
        //         if ($final_preview->student_quota == $key) {
        //             $student_quota = $value;
        //         }
        //     }   
        // }
        $language = '';
            if (!empty($final_preview->lang_1_choice)) {
              foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_1_choice){
                  $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_2_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_2_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_3_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_3_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }
      $formData = (array)$final_preview;
      $fields = $this->db->list_fields('admission_forms');
      $prevSchoolFields = $this->db->list_fields('admission_prev_school');

      $merge = array_merge($fields, $prevSchoolFields);
      $fData = [];
      foreach ($merge as $field){
        if ($field !='id' && $field!='au_id') {
          array_push($fData, $field);
        } 
      }
      $healthField = $this->db->list_fields('student_health');
        $health_disabled_fields = $this->Admission_model->get_health_disabled_fields_admission();

        $uncheckFieldsHelath = ['id','student_id','created_on','modified_on','last_modified_by','academic_year_id','admission_form_id'];
        $hData =[];
        foreach ($healthField as $hfield){
            if (!in_array($hfield, $uncheckFieldsHelath) ) {
                array_push($hData, $hfield);
            }
        }

        $displayFields_health = [];
        foreach ($hData as $key => $value) {
            if (!in_array($value, $health_disabled_fields)) {
               array_push($displayFields_health, $value);
            }
        }
      $photo = '';
      if ($final_preview->std_photo_uri !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->std_photo_uri);
        $photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$getURL.'" />';
      }
      $father_photo = '';
      if ($final_preview->father_photo !='') {
        $f_getURL = $this->filemanager->getFilePath($final_preview->father_photo);
        $father_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$f_getURL.'" />';
      }
      $mother_photo = '';
      if ($final_preview->mother_photo !='') {
        $m_getURL = $this->filemanager->getFilePath($final_preview->mother_photo);
        $mother_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$m_getURL.'" />';
      }
      $guardian_photo = '';
      if ($final_preview->g_photo_uri !='') {
        $g_getURL = $this->filemanager->getFilePath($final_preview->g_photo_uri);
        $guardian_photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$g_getURL.'" />';
      }
      $fatherSignature = '';
      if ($final_preview->f_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->f_signature);
        $fatherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }
      $motherSignature = '';
      if ($final_preview->m_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->m_signature);
        $motherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }

      $family_photo = '';
      if ($final_preview->family_photo !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->family_photo);
        $family_photo = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }
      $category = '';
      if ($final_preview->category !='0' && !empty($final_preview->category)) {
        $category = $this->settings->getSetting('category')[$final_preview->category];
      }
      $student_quota = '';
      if ($final_preview->student_quota !='0' && !empty($final_preview->student_quota)) {
        $student_quota = $this->settings->getSetting('quota')[$final_preview->student_quota];
      }
      $boarding = '';
      if ($final_preview->boarding !='0' && !empty($final_preview->boarding)) {
        $boarding = $this->settings->getSetting('boarding')[$final_preview->boarding];
      }
      $student_passport_expiry_date = $final_preview->passport_expiry_date;
      if ($final_preview->passport_expiry_date == '01-01-1970' || empty($final_preview->passport_expiry_date)) {
        $student_passport_expiry_date = '-';
      }
      $blood_group = '';
      if($final_preview->student_blood_group == 'A +ve'){
        $blood_group = 'A positive';
      }elseif($final_preview->student_blood_group == 'B +ve'){
        $blood_group = 'B positive';
      }elseif($final_preview->student_blood_group == 'O +ve'){
        $blood_group = 'O positive';
      }elseif($final_preview->student_blood_group == 'A -ve'){
        $blood_group = 'A negative';
      }elseif($final_preview->student_blood_group == 'B -ve'){
        $blood_group = 'B negative';
      }elseif($final_preview->student_blood_group == 'O -ve'){
        $blood_group = 'O negative';
      }elseif($final_preview->student_blood_group == 'AB +ve'){
        $blood_group = 'AB positive';
      }elseif($final_preview->student_blood_group == 'AB -ve'){
        $blood_group = 'AB negative';
      }elseif($final_preview->student_blood_group == 'A1B+'){
        $blood_group = 'A1B positive';
      }elseif($final_preview->student_blood_group == 'Unknown'){
        $blood_group = 'Unknown';
      }
      $prevSchool = '';
      $template_medical = '';
      $f_position = $final_preview->f_position;
      if($f_position == 'Not working'){
        $f_position = 'NA';
      }
      $m_position = $final_preview->m_position;
      if($m_position == 'Not working'){
        $m_position = 'NA';
      }
      $has_sibling = 'NA';
      if($final_preview->has_sibling == 1){
        $has_sibling = 'Yes';
      }else if($final_preview->has_sibling == 0){
        $has_sibling = 'No';
      }
      $student_present_address = 'NA';
      $student_permanent_address = 'NA';
      $father_address = 'NA';
      $mother_address = 'NA';
      $father_office_address = 'NA';
      $mother_office_address = 'NA';
      $guardian_address = 'NA';
      $student_present_address_parts = array_filter([
        $final_preview->s_present_addr,
        $final_preview->s_present_area,
        $final_preview->s_present_district,
        $final_preview->s_present_state,
        $final_preview->s_present_country,
        $final_preview->s_present_pincode,
    ]);
    
    $student_present_address = implode(', ', $student_present_address_parts);

    $student_permanent_address_parts = array_filter([
        $final_preview->s_permanent_addr,
        $final_preview->s_permanent_area,
        $final_preview->s_permanent_district,
        $final_preview->s_permanent_state,
        $final_preview->s_permanent_country,
        $final_preview->s_permanent_pincode,
    ]);
    
    $student_permanent_address = implode(', ', $student_permanent_address_parts);

    $father_address_parts = array_filter([
        $final_preview->f_addr,
        $final_preview->f_area,
        $final_preview->f_district,
        $final_preview->f_state,
        $final_preview->f_county,
        $final_preview->f_pincode,
    ]);
    
    $father_address = implode(', ', $father_address_parts);

    $mother_address_parts = array_filter([
        $final_preview->m_addr,
        $final_preview->m_area,
        $final_preview->m_district,
        $final_preview->m_state,
        $final_preview->m_county,
        $final_preview->m_pincode,
    ]);
    
    $mother_address = implode(', ', $mother_address_parts);
    
    $father_office_address_parts = array_filter([
        $final_preview->f_company_addr,
        $final_preview->f_company_area,
        $final_preview->f_company_district,
        $final_preview->f_company_state,
        $final_preview->f_company_county,
        $final_preview->f_company_pincode,
    ]);
    
    $father_office_address = implode(', ', $father_office_address_parts);

    $mother_office_address_parts = array_filter([
        $final_preview->m_company_addr,
        $final_preview->m_company_area,
        $final_preview->m_company_district,
        $final_preview->m_company_state,
        $final_preview->m_company_county,
        $final_preview->m_company_pincode,
    ]);
    
    $mother_office_address = implode(', ', $mother_office_address_parts);

    $guardian_address_parts = array_filter([
        $final_preview->g_addr,
        $final_preview->g_area,
        $final_preview->g_district,
        $final_preview->g_state,
        $final_preview->g_county,
        $final_preview->g_pincode,
    ]);
    
    $guardian_address = implode(', ', $guardian_address_parts);

      $template = str_replace('%%enquiry_number%%', $enquiry_number, $template);
      $template = str_replace('%%academic_year_applied_for%%', $this->acad_year->getAcadYearById($final_preview->academic_year_applied_for), $template);
      $template = str_replace('%%boarding%%', $boarding, $template);
      $template = str_replace('%%has_sibling%%', $has_sibling, $template);
      $template = str_replace('%%student_blood_group%%', $blood_group, $template);
      $template = str_replace('%%language%%', $language, $template);
      $template = str_replace('%%student_photo%%', $photo, $template);
      $template = str_replace('%%family_photo%%', $family_photo, $template);
      $template = str_replace('%%father_photo%%', $father_photo, $template);
      $template = str_replace('%%mother_photo%%', $mother_photo, $template);
      $template = str_replace('%%guardian_photo%%', $guardian_photo, $template);
      $template = str_replace('%%student_passport_expiry_date%%', $student_passport_expiry_date, $template);
      $template = str_replace('%%father_signtuare%%', $fatherSignature, $template);
      $template = str_replace('%%mother_signtuare%%', $motherSignature, $template);
      $template = str_replace('%%application_date%%', $final_preview->created_date, $template);
      $template = str_replace('%%s_first_name%%', $final_preview->std_name, $template);
      $template = str_replace('%%s_middle_name%%', $final_preview->student_middle_name, $template);
      $template = str_replace('%%s_last_name%%', $final_preview->student_last_name, $template);
      $template = str_replace('%%category%%', $category, $template);
      $template = str_replace('%%student_quota_name%%', $student_quota, $template);
      $template = str_replace('%%f_position%%', $f_position, $template);
      $template = str_replace('%%m_position%%', $m_position, $template);
      $template = str_replace('%%student_present_address%%', $student_present_address, $template);
      $template = str_replace('%%student_permanent_address%%', $student_permanent_address, $template);
      $template = str_replace('%%father_address%%', $father_address, $template);
      $template = str_replace('%%mother_address%%', $mother_address, $template);
      $template = str_replace('%%father_office_address%%', $father_office_address, $template);
      $template = str_replace('%%mother_office_address%%', $mother_office_address, $template);
      $template = str_replace('%%guardian_address%%', $guardian_address, $template);
      // $template = str_replace('%%extracurricular_activities%%', $final_preview->extracurricular_activities, $template);
      // $template = str_replace('%%joining_period%%', $final_preview->joining_period, $template);
      // $template = str_replace('%%previous_school_name%%', '', $template);
      // $template = str_replace('%%previous_class_name%%', '', $template);
      // $template = str_replace('%%previous_board%%', '', $template);
      // $template = str_replace('%%previous_total_marks%%', '', $template);
      // $template = str_replace('%%previous_total_marks_scored%%', '', $template);
      // $template = str_replace('%%previous_total_percentage%%', '', $template);
      // $template = str_replace('%%previous_year%%', '', $template);
      // $template = str_replace('%%previous_school_address%%', '', $template);
      // $template = str_replace('%%medium_of_instruction%%', '', $template);
      
      if (!empty($admission_prev_schools)) {
        $template = str_replace('%%previous_school_name%%', $admission_prev_schools[0]->school_name, $template);
        $template = str_replace('%%previous_class_name%%', $admission_prev_schools[0]->class, $template);
        $template = str_replace('%%previous_board%%', $admission_prev_schools[0]->board, $template);
        $template = str_replace('%%previous_total_marks%%', $admission_prev_schools[0]->total_marks, $template);
        $template = str_replace('%%previous_total_marks_scored%%', $admission_prev_schools[0]->total_marks_scored, $template);
        $template = str_replace('%%previous_total_percentage%%', $admission_prev_schools[0]->total_percentage, $template);
        $template = str_replace('%%previous_year%%', $admission_prev_schools[0]->year, $template);
        $template = str_replace('%%previous_school_address%%', $admission_prev_schools[0]->school_address, $template);
        $template = str_replace('%%medium_of_instruction%%', $admission_prev_schools[0]->medium_of_instruction, $template);
        $template = str_replace('%%registration_no%%', $admission_prev_schools[0]->registration_no, $template);
        $template = str_replace('%%expelled_or_suspended%%', $admission_prev_schools[0]->expelled_or_suspended, $template);
        $template = str_replace('%%transfer_reason%%', $admission_prev_schools[0]->transfer_reason, $template);
        $template = str_replace('%%previous_school_ratings%%', $admission_prev_schools[0]->previous_school_ratings, $template);
        $template = str_replace('%%expelled_or_suspended_description%%', $admission_prev_schools[0]->expelled_or_suspended_description, $template);
      }else{
        $template = str_replace('%%previous_school_name%%','', $template);
      $template = str_replace('%%previous_class_name%%Std.', '', $template);
      }
      $prevSchoolnotinmarks = '';
      $your_word_for_institute = $this->settings->getSetting('your_word_for_institute');
      if (!empty($admission_prev_schools)) {
        $prevSchoolnotinmarks .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500;margin-bottom: 2rem;margin-top: 2rem;">Previous School Information</h5>';        
        $prevSchoolnotinmarks .= '<table style="border:none;margin-bottom: 8px; width: 98%; margin-top: -1rem; padding-bottom: -12px; margin-left: auto; margin-right: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">' . (!empty($your_word_for_institute) ? $your_word_for_institute : 'School') . ' name</td>
                    <td style="font-size: 11px;">' . (!empty($your_word_for_institute) ? $your_word_for_institute : 'School') . ' Address</td>
                    <td style="font-size: 11px;">Transfer reason</td>
                    <td style="font-size: 11px;">Expelled/Suspended</td>
                    <td style="font-size: 11px;">Expelled/Suspended description</td>
                </tr>';
            foreach ($admission_prev_schools as $key => $value) {
                $prevSchoolnotinmarks .= '<tr>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->year.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->board.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_name.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_address.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->transfer_reason.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended_description.'</td>';
                $prevSchoolnotinmarks .= '</tr>';
            }
            $prevSchoolnotinmarks .= '</table>';
      }
      if (!empty($admission_prev_schools)) {
        $prevSchool .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">EDUCATIONAL QUALIFICATION</h5>';
        $prevSchool .= '<table style="width: 98%; margin: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' Name</td>
                    <td style="font-size: 11px;">'.(!empty($your_word_for_institute) ? $your_word_for_institute : 'School').' Address</td>
                    <td style="font-size: 11px;">Subjects</td>
                    <td style="font-size: 11px;">Max Marks</td>
                    <td style="font-size: 11px;">Marks Obtained</td>
                </tr>';
          $rowspan = 0;
          $totalMarks = 0;
          $totalMarksScored = 0;
          $totalPercentage = 0;
          $break = 1;
          foreach ($admission_prev_schools as $key => $value) {
            if($break == 1){
                $rowspan =0;
                if (!empty($value->marks)) {
                  $rowspan = sizeof($value->marks);
                }
                $totalPercentage = $value->total_percentage;    
                $prevSchool .= '<tr>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->year.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->board.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_name.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_address.'</td>';
                // $prevSchool .= '</tr>';
                $close = 1;
                if (!empty($value->marks)) {
                  foreach ($value->marks as $key => $val) {
                    $jsan =  json_decode($val->sub_name);
                    $totalMarks += $val->marks;
                    $totalMarksScored += $val->marks_scored;
                    $subName = $jsan->name;
                    if($jsan->name == ''){
                      $subName = '-';
                    }
                    if($close != 1) {
                      $prevSchool .= '<tr>';
                    }
                    $prevSchool .= '<td style="font-size: 11px;">'.$subName.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks_scored.'</td>';
                    $prevSchool .= '</tr>';
                    $close++;
                  }
                }
                
              }
              break;
            }
            $prevSchool .= '<tr style="font-weight:bold">';
            $prevSchool .= '<td style="font-size: 11px;border-right:none" colspan="4"><span>Percentage : '.$totalPercentage.'</span></td>';
            $prevSchool .= '<td style="font-size: 11px;border:none;text-align:right" ><span>Total Marks </span></td>';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarks, 2, '.', '').'</td> ';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarksScored, 2, '.', '').'</td> ';
            $prevSchool .= '</tr>';
            $prevSchool .= '</table>';
      }


      foreach ($fData as $key => $val) {
        if (array_key_exists($val, $formData)) {
            if (!empty($formData[$val])) {
              $template = str_replace('%%'.$val.'%%',$formData[$val], $template);
            }else{
              $template = str_replace('%%'.$val.'%%','NA', $template);
            }
          
        }
      }
      if(!empty($final_preview->custom_field)){
        $final_preview->custom_field = (array)json_decode($final_preview->custom_field);
        foreach($final_preview->custom_field as $key => $value){
          $template = str_replace('%%'.$key.'%%',$value, $template);
        }
      }
      $medical_health = (array)$medical_health;
      foreach ($displayFields_health as $key => $val) {
        if (array_key_exists($val, $medical_health)) {
            if (!empty($medical_health[$val])) {
              $template_medical = str_replace('%%'.$val.'%%',$medical_health[$val], $template);
            }else{
              $template_medical = str_replace('%%'.$val.'%%','-', $template);
            }
        }
      }
        $construct_medical_template = '';
        $construct_medical_template .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">Medical Details</h5>';
        $construct_medical_template .='<table style="width: 98%; margin: auto;">';
        if (!empty($displayFields_health)) {
            foreach (array_chunk($displayFields_health, 2)  as $key => $value) { 
                $construct_medical_template .='<tr>';
                foreach ($value as $key => $val) { 
                    $search = ["foodytpe","_"];
                    $replace = ["Food type"," "];
                    $resVal = ucwords(str_replace($search, $replace, $val));
                    $construct_medical_template .='<th style="text-align:left !important;">'.$resVal.': </th>';
                    if (empty($medical_health[$val])) {
                        $valueDis = '-';
                    }else{
                        $valueDis = $medical_health[$val];
                    } 
                    $construct_medical_template .='<td>'.$valueDis.'</td>';
                }
                $construct_medical_template .='</tr>';
            }
        }
        $construct_medical_template .='</table>';
      $income_yes_no = 'No';
      if (!empty($final_preview->caste_income_certificate_number)) {
        $income_yes_no = 'Yes';
      }
      $physical_disability_yes_no = 'No';
      if (!empty($final_preview->physical_disability == 'Y')) {
        $physical_disability_yes_no = 'Yes';
      }
      $student_gender = '';
      if ($final_preview->gender =='M') {
          $student_gender = 'Male';
      }else if($final_preview->gender == 'F'){
        $student_gender = 'Female';
      }else if($final_preview->gender == 'O'){
        $student_gender = 'Other';
      }
      $dobAge = $final_preview->year.'years '.$final_preview->month.'months '.$final_preview->day.'days';
      $template = str_replace('%%prevoius_school_not_in_marks%%', $prevSchoolnotinmarks, $template);
      $template = str_replace('%%previous_education_details%%', $prevSchool, $template);
      $template = str_replace('%%income_yes_no%%', $income_yes_no, $template);
      $template = str_replace('%%physical_disability_yes_no%%', $physical_disability_yes_no, $template);
      $template = str_replace('%%s_permanent_pincode%%', $final_preview->s_permanent_pincode, $template);
      $template = str_replace('%%combinations%%', $combination, $template);
      $template = str_replace('%%student_quota%%', $student_quota, $template);
      $template = str_replace('%%dob_age%%', $dobAge, $template);
      $template = str_replace('%%student_gender%%', $student_gender, $template);
      $template = str_replace('%%father_name%%', $final_preview->father_name, $template);
      $template = str_replace('%%mother_name%%',$final_preview->mother_name, $template);
      $template = str_replace('%%student_name%%',$final_preview->student_name, $template);
      $dob_in_words = $this->dateToWords($final_preview->dob);
      $template = str_replace('%%dob_in_words%%',$dob_in_words, $template);
      $template = str_replace('%%student_medical%%', $template_medical, $template);
      $template = str_replace('%%construct_medical_template%%', $construct_medical_template, $template);
      // echo "<pre>"; print_r($template); die();
      return $template;
    }

    private function numberToWords($num) {
        $ones = array(
            0 => "Zero", 1 => "One", 2 => "Two", 3 => "Three", 4 => "Four", 5 => "Five",
            6 => "Six", 7 => "Seven", 8 => "Eight", 9 => "Nine", 10 => "Ten",
            11 => "Eleven", 12 => "Twelve", 13 => "Thirteen", 14 => "Fourteen", 15 => "Fifteen",
            16 => "Sixteen", 17 => "Seventeen", 18 => "Eighteen", 19 => "Nineteen"
        );
    
        $tens = array(
            0 => "Zero", 1 => "Ten", 2 => "Twenty", 3 => "Thirty", 4 => "Forty", 5 => "Fifty",
            6 => "Sixty", 7 => "Seventy", 8 => "Eighty", 9 => "Ninety"
        );
    
        if ($num < 20) {
            return $ones[$num];
        } elseif ($num < 100) {
            return $tens[intval($num / 10)] . (($num % 10 > 0) ? " " . $ones[$num % 10] : "");
        } elseif ($num < 1000) {
            return $ones[intval($num / 100)] . " Hundred" . (($num % 100 > 0) ? " " . $this->numberToWords($num % 100) : "");
        } else {
            return $this->numberToWords(intval($num / 1000)) . " Thousand" . (($num % 1000 > 0) ? " " . $this->numberToWords($num % 1000) : "");
        }
    }
    
    private function dateToWords($dateStr) {
        $date = DateTime::createFromFormat('d-m-Y', $dateStr);
        $day = intval($date->format('d'));
        $month = $date->format('F');
        $year = intval($date->format('Y'));
    
        $dayInWords = $this->numberToWords($day);
        $yearInWords = $this->numberToWords($year);
    
        return "$dayInWords - $month - $yearInWords";
    }

    private function _generate_admisison_pdf_receipt($html, $afId) {
      $school = CONFIG_ENV['main_folder'];
      $path = $school.'/admissions_reciepts/'.uniqid().'-'.time().".pdf";
      $bucket = $this->config->item('s3_bucket');
      $status = $this->Admission_model->update_admission_form_path($afId, $path);
      $page_size = 'a4';
      $page = 'portrait';
      $curl = curl_init();
      $postData = urlencode($html);
      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      $return_url = site_url().'Callback_Controller/updateApplicationPdfLink';

      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
          CURLOPT_HTTPHEADER => array(
                  "Accept: application/json",
                  "Cache-Control: no-cache",
                  "Content-Type: application/x-www-form-urlencoded",
                  "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
              ),
          ));

          $response = curl_exec($curl);
          $err = curl_error($curl);
          curl_close($curl);
        }

	public function saveAdmission() {
		$input = $this->input->post();
		if($input['captchaWord'] != $input['captcha']) {
			$this->session->set_flashdata('flashError', "Captcha code mis-match");
        	redirect('Admission_controller/verifyMobile');
		}
	}

	public function refresh()
    {
        $config = array(
            'img_url' => base_url() . 'assets/image_for_captcha/',
	        'img_path' => 'assets/image_for_captcha/',
            'img_height' => 40,
            'word_length' => 5,
            'img_width' => 150,
            'font_size' => 12
        );
        $captcha = create_captcha($config);
        $this->session->unset_userdata('valuecaptchaCode');
        $this->session->set_userdata('valuecaptchaCode', $captcha['word']);

        $data['captchaImg'] = $captcha['image'];
	    $data['captchaWord'] = $captcha['word'];

        echo json_encode($data);        
    }


    public function s3FileUpload($file) {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'Admission_form_document');
    }
    
    public function updateStudentDetails(){
    	$input = $this->input->post();
    
        $student_photo_path = '';
        // if(isset($_FILES['student_photo'])){
        //   $student_photo_path = $this->s3FileUpload($_FILES['student_photo']);
        // }
       
        $sResigedPhoto['file_name'] = '';
        if(isset($_FILES['student_photo'])){
          $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);
          $picture = array('tmp_name' => $min_size, 'name' => $_FILES['student_photo']['name']);
          $sResigedPhoto = $this->s3FileUpload($picture);
        }

        $family_photo_path = '';
        if(isset($_FILES['family_photo'])){
          $family_photo_path = $this->s3FileUpload($_FILES['family_photo']);
        }

        $stud_sign_path['file_name'] = '';
        if(isset($_FILES['student_signature'])){
          $stud_sign_path = $this->s3FileUpload($_FILES['student_signature']);
        }
    	echo $this->Admission_model->insert_student_details($input,$student_photo_path,$family_photo_path, $sResigedPhoto,$stud_sign_path);
    }

    public function update_parent_details(){
    	$input = $this->input->post();
        $mSignature = '';
        if (isset($_FILES['m_signature'])) {
         $mSignature = $this->s3FileUpload($_FILES['m_signature']);
        }
        $fSignature = '';
        if (isset($_FILES['f_signature'])) {
         $fSignature = $this->s3FileUpload($_FILES['f_signature']);
        }
        $f_photo = '';
        if (isset($_FILES['father_photo'])) {
         $f_photo = $this->s3FileUpload($_FILES['father_photo']);
        }
        $m_photo = '';
        if (isset($_FILES['mother_photo'])) {
         $m_photo = $this->s3FileUpload($_FILES['mother_photo']);
        }
    	echo $this->Admission_model->insert_parent_details($input, $mSignature, $fSignature,$f_photo,$m_photo);
    }

    public function get_parent_data(){
        $result = $this->Admission_model->get_parent_data($_POST['af_id']); 
        // echo '<pre>';print_r($_POST['af_id']);die();
        echo json_encode($result); 
    }

    public function update_guardian_details(){
        $input = $this->input->post();
        $gPhoto = '';
        if (isset($_FILES['guardian_photo'])) {
         $gPhoto = $this->s3FileUpload($_FILES['guardian_photo']);
        }
        echo $this->Admission_model->insert_guardian_details($input, $gPhoto);
    }
    public function update_documents(){
    	$input = $this->input->post();
    	echo $this->Admission_model->insert_documents($input,$this->s3FileUpload($_FILES['document_photo']));
    }

  
    public function update_previous_school_details(){
        $input = $this->input->post();
        echo $this->Admission_model->insert_prevous_school_details($input);
    }
     public function check_sibling_ad_nobyinput(){
    	$sb_ad = $this->input->post('sb_ad');
    	$result = $this->Admission_model->check_sibling_ad_nobysb_id($sb_ad);
    	if($result != ''){
	       echo '<span style="color:green;">This admission number available</span>';
      	}else{
	       echo '<span style="color:red;">This admission number not available</span>';
      	}
    }

    public function get_admission_form_document(){
        $afid = $this->input->post('afid');
    	$result = $this->Admission_model->get_admission_form_detailsby_auId($afid);
    	echo json_encode($result);
    }

    public function delete_documentbyId(){
    	$d_id = $this->input->post('d_id');
    	echo $this->Admission_model->delete_document_byid($d_id);
    }

    public function get_previous_school_details(){
        $afid = $this->input->post('afid');
        $result = $this->Admission_model->get_admission_form_previous_school_details($afid);
        echo json_encode($result);
    }

    public function delete_details_schooling_byId(){
        $apsid = $this->input->post('apsid');
        echo $this->Admission_model->delete_schooling_details_byid($apsid);
    }
 

    public function logout(){
        unset($_SESSION['loginstatus']);
        $this->session->unset_userdata('admission_user_id');
        redirect('admissions');        
    }


    public function check_admissions_draft(){
        $au_id = $this->input->post('au_id');
        echo $this->Admission_model->check_draft_applications($au_id);
    }

    public function get_remaing_years_selecting(){
        if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $prev_eduction_info = json_decode($data['config_val']['prev_eduction_info'], true);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $years = array();
        foreach ($prev_eduction_info['year'] as $key => $year) {
            $years[$year] = $year;
        }
        $ryears = array();
        if (!empty($data['admission_prev_schools'])) {
            foreach ($data['admission_prev_schools'] as $key => $pSchool) {
                $ryears[$pSchool->year] = $pSchool->year;
            }
        }

        $result = array_diff($years,$ryears);
        $yearsinfo = array();
        foreach ($result as $key => $val) {
            array_push($yearsinfo, $val);
        }
        echo json_encode($yearsinfo);
    }

    public function get_remaing_document_selecting(){
        if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $documents = json_decode($data['config_val']['documents'], true);
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);    
        $doc = array();
        foreach ($documents as $key => $docs) {
            $doc[$docs] = $docs;
        }
        $rdocs = array();
        if (!empty($data['admission_form'])) {
            foreach ($data['admission_form'] as $key => $document) {
                $rdocs[$document->document_type] = $document->document_type;
            }
        }

        $result = array_diff($doc,$rdocs);
        $docsinfo = array();
        foreach ($result as $key => $val) {
            array_push($docsinfo, $val);
        }
        echo json_encode($docsinfo);
    }

    public function dispaly_stream_based_on_combinations(){
        $input = $this->input->post();
        $config_val = $this->_get_admissions_settings_byId($input['admission_setting_id']);
        $streams = json_decode($config_val['streams'], true);        
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($input['au_id'],$input['lastId']);

        if (!empty($streams)) {
            $subjectArray = $streams;
        }
        if (!empty($input['combination'])) {
            $subjectArray = $streams[$input['combination']];
            $template = '';
            $i = 1;
            foreach ($subjectArray as $key => $val) { 
                $template .= "<table class='table table-bordered' style='margin-bottom:8px'>";
                $template .= "<tr>";
                $template .= "<td style='width: 6%; vertical-align: middle;'>".$i++."</td>";
                $template .= "<td style='text-align: left; vertical-align: middle;'>";
                $template .= $val['name'];
                $template .= "</td>";  
                if (!empty($input['combination_id'])) {
                    if ($input['combination_id'] == $val['id']) {
                        $template .= "<td style='width:6%; vertical-align: middle; padding: 4px;'><label style='margin-top: 7px;'><input type='radio' checked name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                    }else{
                        $template .= "<td style='width:6%; vertical-align: middle; padding: 4px; '><label style='margin-top: 7px;'><input required='' type='radio' name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                    }
                }else{
                    $template .= "<td style='width:6%; vertical-align: middle; padding: 4px;'><label style='margin-top: 7px;'><input type='radio' required='' name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                }
               
                $template .= "</tr>"; 
                $template .= "</table>";  
            }
            print($template);
        }
    }

    public function fetch_stream_based_on_combinations(){
        $admission_setting_id = $_POST['admission_setting_id'];
        $combination = $_POST['combination'];

        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
        $streams = json_decode($config_val['streams'], true);
         if (!empty($streams)) {
            $subjectArray = $streams;
        }
        if (!empty($combination)) {
            $subjectArray = $streams[$combination];
        }
        echo json_encode($subjectArray);
    }

    public function __construct_name_wise_required($requiredData){
        $fields = $this->db->list_fields('admission_forms');
        $prevFields = $this->db->list_fields('admission_prev_school');
        $merges = array_merge($fields, $prevFields);
        $rData = [];
        if (empty($requiredData)) {
            foreach ($merges as $key => $val) {
                $rData[$val] = array('required' =>'');
            }
            return $rData;
        }
        foreach ($merges as $key => $val) {
        // echo '<pre>';print_r($requiredData );die();
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('required' =>'required');
            }else{
                $rData[$val] = array('required' =>'');
            }
        }
        return $rData;
    }


    public function print_receipt(){
        $admissionId = $_POST['admissionId'];
        $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
        if($admissions->receipt_html_path !=''){
            echo 1;
        }else{
            echo 0;
        }

        // if ($admissions->receipt_html_path == '') {
        //     $result = $this->_create_template_admission_receipt($admissions, $admissions->receipt_html);
        //     $pdfResult = $this->__generatefee_admission_pdf_receipt($result, $admissionId);
        // }
        
        // $url = $this->filemanager->getFilePath($link);
        // $data = file_get_contents($url);
        // $this->load->helper('download');
        // force_download('admission_receipt.pdf', $data, TRUE);
    }

    public function download_receipt($admissionId){
        $link = $this->Admission_model->download_admission_receipt($admissionId);
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download('admission_receipt.pdf', $data, TRUE); 
    }

    public function generate_print_receipt(){
        $admissionId = $_POST['admissionId'];
        $admissions = $this->Admission_model->admission_receipt_by_id($admissionId);
        $result = $this->_create_template_admission_receipt($admissions, $admissions->receipt_html);
        echo $this->__generatefee_admission_pdf_receipt($result, $admissionId); 
    }



    public function _create_template_admission_receipt($admData, $template){    
        $template = str_replace('%%receipt_no%%',$admData->receipt_number, $template);
        $template = str_replace('%%application_no%%',$admData->application_no, $template);
        $template = str_replace('%%student_name%%',$admData->student_name, $template);
        $template = str_replace('%%admission_amount%%',$admData->admission_fee_amount, $template);
        $template = str_replace('%%transaction_id%%',$admData->tx_id, $template);
        if (!empty($admData->payment_date)) {
            $template = str_replace('%%admission_date%%',$admData->payment_date, $template);
        }else{
            $template = str_replace('%%admission_date%%',$admData->receipt_date, $template);
        }
        switch ($admData->payment_type) {
          case '10':
            $type = 'Online Payment';
            break;
          case '4':
            $type = 'Cheque';
            break;
          case '8':
            $type = 'Net Banking';
            break;  
          default:
            $type = 'Cash';
            break;
        }
        $template = str_replace('%%payment_type_mode%%',$type, $template);
        $payment_mode ='<tr>';
        $payment_mode.='<td colspan="2" style="width: 60%; border-top: none !important; border-bottom:  none !important;"><b>Payment Type : </b>'.$type.'<td>';
        $payment_mode.='</tr>';
        if ($admData->payment_type == '4') {
            $payment_mode.='<tr>';
            $payment_mode.='<td style="border: none !important;"><b>Bank : </b>'.$admData->bank_name.'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Branch : </b>  '.$admData->bank_branch.'</td>';
            $payment_mode.='<td style="border: none !important;"><b>Cheque No  </b>'.$admData->cheque_dd_nb_cc_dd_number.'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>Date : </b>  '.$admData->cheque_or_dd_date.'</td>';              
            $payment_mode.='</tr>';
        }
        $template = str_replace('%%payment_type%%',$payment_mode, $template);

        $amountInWords = $this->getIndianCurrency($admData->admission_fee_amount);
        $template = str_replace('%%amoun_in_words%%',$amountInWords, $template);

        return $template;
    }

    public function getIndianCurrency(float $number){
        $schoolName = $this->settings->getSetting('school_short_name');
        $decimal = round($number - ($no = floor($number)), 2) * 100;
        $hundred = null;
        $digits_length = strlen($no);
        $i = 0;
        $str = array();
        $words = array(0 => '', 1 => 'One', 2 => 'Two',
            3 => 'Three', 4 => 'Four', 5 => 'Five', 6 => 'Six',
            7 => 'Seven', 8 => 'Eight', 9 => 'Nine',
            10 => 'Ten', 11 => 'Eleven', 12 => 'Twelve',
            13 => 'Thirteen', 14 => 'Fourteen', 15 => 'Fifteen',
            16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen',
            19 => 'Nineteen', 20 => 'twenty', 30 => 'Thirty',
            40 => 'Forty', 50 => 'Fifty', 60 => 'Sixty',
            70 => 'Seventy', 80 => 'Eighty', 90 => 'Ninety');
        $digits = array('', 'hundred','thousand','lakh', 'crore');
        while( $i < $digits_length ) {
            $divider = ($i == 2) ? 10 : 100;
            $number = floor($no % $divider);
            $no = floor($no / $divider);
            $i += $divider == 10 ? 1 : 2;
            if ($number) {
                $plural = (($counter = count($str)) && $number > 9) ? '' : null;
                $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
                $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
            } else $str[] = null;
        }
        $Rupees = implode('', array_reverse($str));
        $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
        if ($schoolName === 'prarthana') {
          return 'Rupees ' . ( $Rupees ? $Rupees . 'Only ' : ' ') . $paise ;
        }
        return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
    }

    private function __generatefee_admission_pdf_receipt($html, $admissionId) {
        $school = CONFIG_ENV['main_folder'];
        $path = $school.'/admission_fee_reciepts/'.uniqid().'-'.time().".pdf";

        $bucket = $this->config->item('s3_bucket');

        $status = $this->Admission_model->update_admission_receipt_Path($admissionId, $path);
        $page = 'portrait';
        $page_size = 'a4';
        $curl = curl_init();
        $postData = urlencode($html);
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = '';

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
    }

    public function get_class_dob(){
        $ageGuidlness = $this->settings->getSetting('admission_age_guidelines_message');
        if ($ageGuidlness) {
            echo 1;
        }else{
            $selectedClass = $_POST['selectedClass'];
            $dob = $_POST['dob'];
            echo $this->Admission_model->check_class_dob($selectedClass,$dob);
        }
    }
    
    public function get_previous_school_detail_for_the_year(){
        $afid = $_POST['afid'];
        $year = $_POST['year'];
        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        if(in_array('year',$data['disabled_fields'])){
            $result = $this->Admission_model->get_previous_school_detail_for_the_class($afid, $year);
        }else{
            $result = $this->Admission_model->get_previous_school_detail_for_the_year($afid, $year);
        }
        echo json_encode($result);
    }
    
    public function update_previous_school_details_year_wise(){
        $input = $this->input->post();
        $report_card = '';
        if (isset($_FILES['report_card'])) {
            $report_card = $this->s3FileUpload($_FILES['report_card']);
        }
        echo $this->Admission_model->insert_previous_school_details_year_wise($input, $report_card);
    }

    public function update_previous_school_subjects_year_wise(){
        $input = $this->input->post();
        // echo '<pre>'; print_r($input); die();
        echo $this->Admission_model->insert_previous_school_subjects_year_wise($input);
    }
    
    public function get_subject_details_from_admission_settings(){
      
        $admission_setting_id=$_POST['admission_setting_id'];
        $lastId=$_POST['afId'];
        $au_Id=$_POST['au_id'];
        $pre_school_year= $_POST['pre_school_year'];
        $apsId = $_POST['apsId'];
        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
        $preSchoolingmarks = $config_val['show_previous_schooling_overall_total_marks'];

        // $show_previous_schooling_subjects = $config_val['show_previous_schooling_subjects'];

        $show_previous_schooling_subject_total_marks = $config_val['show_previous_schooling_subject_total_marks'];

        $show_previous_schooling_subject_percentage = $config_val['show_previous_schooling_subject_percentage'];

        $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);
        
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);
         $marks= $this->Admission_model->get_pre_school_subject_marks($apsId);
         $total_marks= $this->Admission_model->get_pre_school_total_marks($apsId);
        $template = '';
        $template .= '<table class="table table-borderd">';
        $template .= '<tr>';
        $template .= '<th>Subject</th>';


        if ($show_previous_schooling_subject_total_marks == 1) {
            $template .= '<th>Max Marks</th>';
            $template .= '<th>Marks Scored</th>';

        }
        if($show_previous_schooling_subject_percentage == 1){
            $template .= '<th>Percent</th>';
            $template .= '<th>Grade</th>';
        }

        $template .='</tr>';
        $subjectArray = $prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'];
        $m = 1;
        foreach ($subjectArray as $subject) {
            $subName = $subject['name'];
            $subNameValue = '';
            $grade = '';
            $enter_marks = 0;
            $marks_scored = 0;
            $percentage = 0;
            if(!empty($marks)){
                foreach ($marks as $key => $val) {
                    if($subject['id'] == $val->sub_id){
                        if($val->sub_name !=''){
                            $subName = $val->sub_name;
                            $subNameValue = $val->sub_name;
                            $percentage = $val->percentage;
                            $grade = $val->grade;
                            $enter_marks = $val->marks;
                            $marks_scored = $val->marks_scored;
                        }
                       
                    }
                }
            }
            $template.= '<tr>';
            $template .= '<td>';
            $template .= ' <input type="hidden" value="'.$subject['id'].'" name="sub_id[]">';
            switch ($subject['type']) {
                case 'label':
                  $template .= $subName;
                  $template .= ' <input type="hidden" value="'.$subName.'" name="sub_name[]">';
                  break;
                case 'text': 
                  $template .= ' <input type="text" class="form-control" onkeyup="check_is_value_not_empty(this,'.$m.')" placeholder="'.$subName.'" value="'.$subNameValue.'" name="sub_name[]">';
            }
            $template .= '</td>';

            if ($show_previous_schooling_subject_total_marks == 1) {
                $template.= '<td><input type="number" step=".01" id="maxMarks_'.$m.'" onkeyup="max_marks_total('.$m.')"name="max_marks[]" value="'.$enter_marks.'" class="form-control maxMarks per"></td>';
                $template.= '<td><input type="number" step=".01" id="maxMarkScored_'.$m.'" onkeyup="max_marks_scored_total('.$m.')" name="marks_scored[]" value="'.$marks_scored.'" class="form-control maxMakrsScored per"></td>';
            }
            if ($show_previous_schooling_subject_percentage == 1) {
                $template.= '<td><input type="number" step=".01"  min="0" max="100" name="percentage[]" value="'.$percentage.'" class="form-control per"></td>';
                $template.= '<td><input type="text" style="text-transform:uppercase; width:64px" name="grades[]" placeholder="Grade" value="'.$grade.'" class="form-control grd"></td>';
            }

          $template.= '</tr>';
          $m++;
        }
        $template .= '</table><hr>';
        if($config_val['show_previous_schooling_overall_total_marks'] == 1){
            $template .= '<div class="form-group">
                            <label class="control-label col-md-3">Total Marks Scored </label>
                            <div class="col-md-8">
                                <input type="number" step=".01" min="0" max="1000" readonly data-parsley-error-message="Invalid number" name="total_marks_scored" id="total_marks_scored" class="form-control" value="'.$total_marks->total_marks.'">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-3">Total Marks </label>
                            <div class="col-md-8">
                                <input type="number" step=".01" data-parsley-error-message="Invalid number" readonly min="0" max="1000" name="total_max_marks_entry" id="total_max_marks_entry" class="form-control" value="'.$total_marks->total_marks_scored.'">
                            </div>
                        </div>';
        }
        if($config_val['show_previous_schooling_overall_grade'] == 1){
            $template .=   '<div class="form-group">
                                <label class="control-label col-md-3">Grade </label>
                                <div class="col-md-8">
                                    <input type="text" step=".01" name="total_grade" id="total_grade" class="form-control">
                                </div>
                            </div>';
        }
        if($config_val['show_previous_schooling_overall_percentage'] == 1){
            $template .=   '<div class="form-group">
                                <label class="control-label col-md-3">Total Percentage </label>
                                <div class="col-md-8">
                                    <input type="text" step=".01" data-parsley-error-message="Invalid number" readonly name="total_percentage" id="total_percentage" class="form-control" value="'.$total_marks->total_percentage.'">
                                </div>
                            </div>';
        }
        
        print($template);
    }

    public function admission_previous_school_report_card($apsId){
        $document = $this->Admission_model->get_admission_previous_school_report_card($apsId);
        $link = $document->report_card;
        $file = explode("/", $link);
        $file_name = 'report_card';
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        // echo '<pre>'; print_r($fname); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
    }

    public function update_documents_new(){
        $path = $_POST['path'];
        $document_for = $_POST['document_for'];
        $af_id = $_POST['af_id'];
        echo $this->Admission_model->insert_documents_new($path, $document_for, $af_id);
    }

    public function reupload_rejected_documents(){
        $path = $this->input->post('path');
        $doc_id = $this->input->post('doc_id');
        $af_id = $this->input->post('af_id');
        $adm_setting_id = $this->input->post('adm_setting_id');
        $result = $this->Admission_model->update_documents($path, $doc_id);
        if($result){
            $email_data = $this->Admission_model->get_data_doc_reuploaded($af_id);
            if(!empty($email_data)){
                $email_data['template_content'] = str_replace('%%application_number%%',$email_data['to_email']->application_no,$email_data['template_content']);
                $sent_mail = $this->_send_doc_reuploaded_mail($email_data, 'Parents');
            }
            $send_email_to_staff_template = $this->Admission_model->get_staff_email_Ids($adm_setting_id,$af_id);
            if (!empty($send_email_to_staff_template)){
                $send_email_to_staff_template['template_content'] = str_replace('%%application_number%%',$send_email_to_staff_template['application_no'],$send_email_to_staff_template['template_content']);
                $this->_send_doc_reuploaded_mail($send_email_to_staff_template, 'Staffs');
            }
        }
        echo $result;
    }

    private function _send_doc_reuploaded_mail($input, $type){
        $emailData = $this->saveEmailData($input, $type);
        $this->load->helper('email_helper');
        if(!empty($emailData['memberEmail'])){
            return sendEmail($input['template_content'], $input['email_subject'], $emailData['email_master_id'], $emailData['memberEmail'], $input['registered_email'], []);
        }
    }

    private function saveEmailData($input, $senderList){
        $members_data = $input['to_emails'];
        $email_data = [];
        $memberEmail = [];
        foreach ($members_data as $members) {
            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $members['stakeholder_id'];
            $email_obj->avatar_type = $members['avatar_type'];
            $email_obj->email = $members['email'];

            $email_data[] = $email_obj;
            if (!empty($members['email'])) {
                $memberEmail[] = $members['email'];
            }
        }
        if($senderList == 'Parents'){
            $senderList = implode(', ', $memberEmail);
        }
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        $email_master_data = array(
            'subject' => $input['email_subject'],
            'body' => $input['template_content'],
            'source' => 'Admissions Reupload Documents By Parents',
            'sent_by' => 0, //Sent By Parent during admission reupload documents.
            'recievers' => $senderList,
            'from_email' => $input['registered_email'],
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );
        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        return array('email_master_id' => $email_master_id, 'memberEmail' => $memberEmail);
    }

    public function admission_download_document_card($docId){
        $document = $this->Admission_model->get_admission_document_download($docId);
        $link = $document->document_uri;
        $file = explode("/", $link);
        $file_name = $document->document_type;
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
    }
     public function admission_previous_reportcard($apsId){
        $previousSchoolReport = $this->Admission_model->get_admission_previous_school_report_card($apsId);
        $link = $previousSchoolReport->report_card;
        $file = explode("/", $link);
        $file_name = 'Report Card';
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
    }

    public function update_medical_form_details(){
        $input = $this->input->post();
        echo $this->Admission_model->insert_medical_form_details($input);
    }

    public function submit_admission_documents(){
        $document_path = '';
        if(isset($_FILES['document_file'])){
          $document_path = $this->s3FileUpload($_FILES['document_file']);
        }
        $ackn_path = '';
        if(isset($_FILES['acknowledgement_file'])){
          $ackn_path = $this->s3FileUpload($_FILES['acknowledgement_file']);
        }
        $declaration_path = '';
        if(isset($_FILES['declaration_file'])){
          $declaration_path = $this->s3FileUpload($_FILES['declaration_file']);
        }

        echo $this->Admission_model->submit_admission_documents($_POST,$document_path,$ackn_path,$declaration_path);
    }

    public function reupload_admission_documents(){
        $document_path = '';
        if(isset($_FILES['document_file'])){
          $document_path = $this->s3FileUpload($_FILES['document_file']);
        }
        $ackn_path = '';
        if(isset($_FILES['acknowledgement_file'])){
          $ackn_path = $this->s3FileUpload($_FILES['acknowledgement_file']);
        }
        $declaration_path = '';
        if(isset($_FILES['declaration_file'])){
          $declaration_path = $this->s3FileUpload($_FILES['declaration_file']);
        }
        $af_id = $this->input->post('af_id');
        $adm_setting_id = $this->input->post('adm_setting_id');
        $result = $this->Admission_model->reupload_admission_documents($_POST,$document_path,$ackn_path,$declaration_path);
        if($result){
            $email_data = $this->Admission_model->get_data_doc_reuploaded($af_id);
            if(!empty($email_data)){
                $email_data['template_content'] = str_replace('%%application_number%%',$email_data['to_email']->application_no,$email_data['template_content']);
              $sent_mail = $this->_send_doc_reuploaded_mail($email_data, 'Parents');
            }
            $send_email_to_staff_template = $this->Admission_model->get_staff_email_Ids($adm_setting_id,$af_id);
            if (!empty($send_email_to_staff_template)){
              $send_email_to_staff_template['template_content'] = str_replace('%%application_number%%',$send_email_to_staff_template['application_no'],$send_email_to_staff_template['template_content']);
              $this->_send_doc_reuploaded_mail($send_email_to_staff_template, 'Staffs');
            }
        }
        echo $result;
    }

    public function get_admission_documents(){
        $result = $this->Admission_model->get_admission_documents($_POST);
        echo json_encode($result);
    }

    public function delete_uploaded_documents(){
        echo $this->Admission_model->delete_uploaded_documents($_POST);
    }

    public function check_document_uploaded(){
        echo $this->Admission_model->check_document_uploaded($_POST);
    }
    private function _resize_image($file, $max_resolution, $type)
    {
      if (file_exists($file)) {
        if ($type == 'image/jpeg')
          $original_image = imagecreatefromjpeg($file);
        else
          $original_image = imagecreatefrompng($file);
  
        //check orientation 
        // $exif = exif_read_data($file);
  
        try {
          $exif = exif_read_data($file);
        } catch (Exception $exp) {
          $exif = false;
        }
  
        if ($exif) {
          if (!empty($exif['Orientation'])) {
            switch ($exif['Orientation']) {
              case 3:
                $original_image = imagerotate($original_image, 180, 0);
                break;
  
              case 6:
                $original_image = imagerotate($original_image, -90, 0);
                break;
  
              case 8:
                $original_image = imagerotate($original_image, 90, 0);
                break;
            }
          }
        }
  
        //resolution
        $original_width = imagesx($original_image);
        $original_height = imagesy($original_image);
  
        //try width first
        $ratio = $max_resolution / $original_width;
        $new_width = $max_resolution;
        $new_height = $original_height * $ratio;
  
        //if that dosn't work
        if ($new_height > $max_resolution) {
          $ratio = $max_resolution / $original_height;
          $new_height = $max_resolution;
          $new_width = $original_width * $ratio;
        }
        
        if ($original_image) {
          $new_image = imagecreatetruecolor($new_width, $new_height);
          imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
          if ($type == 'image/jpeg')
            imagejpeg($new_image, $file);
          else
            imagepng($new_image, $file);
        }
  
        return $file;
        // echo '<br>Resized: ';
        // echo filesize($file); 
  
        // echo '<pre>'; print_r($file); die();
      }
    }

    public function download_declaration($adm_setting_id,$file_name,$type){
        $document = $this->Admission_model->get_declaration_from_settings($adm_setting_id,$type);
        $document_url = $this->filemanager->getFilePath($document);
        $document_data = file_get_contents($document_url);
        $this->load->helper('download');
        force_download($file_name.'.pdf', $document_data, TRUE);
    }

    public function get_rejected_documents(){
        $result = $this->Admission_model->get_rejected_documents($this->input->post('af_id'),$this->input->post('adm_setting_id'));
        echo json_encode($result);
    }

    public function check_student_boarding(){
        echo $this->Admission_model->get_student_boarding($this->input->post('af_id'));
    }

    public function get_hospital_details(){
        $result = $this->Admission_model->get_hospital_details($this->input->post('af_id'));
        echo json_encode($result);
    }

    public function submit_hospital_details(){
        echo $this->Admission_model->submit_hospital_details();
    }

    public function InsertStudentDetails(){
        $af_id = $this->Admission_model->insert_student_admission_data();
        $this->session->set_userdata([
            'au_id' => $_POST['au_id'],
            'lastId' => $af_id,
            'admission_setting_id' => $_POST['admission_setting_id']
        ]);
        redirect('admissions/short_application');
    }
}
