<?php

class Skills extends CI_Controller {
  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
      $this->load->model('academics/skill_model');
  }

  public function index() {
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/skills/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/skills/index_mobile';
    }else{
      $data['main_content'] = 'academics/skills/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function getAllSkills(){
    $viewSkills = $this->skill_model->getSkills();
    echo json_encode($viewSkills);
  }

  public function submitSkill(){
    $skill_name = $_POST['skill_name'];
    $skill_description = $_POST['skill_description'];
    $data = $this->skill_model->submitSkill($skill_name, $skill_description);
    echo json_encode($data);
  }
}