<div class="modal" tabindex="-1" role="dialog" id="view_skillType">
    <div class="modal-dialog" role="document" id="view_skillType_modal">
        <div class="modal-content" style="border-radius:1rem;width: 70%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Skills</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_skillType_data">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('add_skills', 'view_skillType')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Add More</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function loadSkills() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ skillType } = data);
                let html = `<table class="table table-bordered">
                        <tr class="bg-light">
                            <th>#</th>
                            <th>Skill Type</th>
                            <th>Skill Description</th>
                            <th style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Delete</th>
                        </tr>`

                skillType.forEach((r, i) => {
                    // console.log(r);
                    html += `
                    <tr>
                        <td>${++i}</td>
                        <td id="skill_${r.session_skill_id}">${r.skill_name}</td>
                        <td>${r.skill_description}</td>
                        <td style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;"><a onClick="delete_session_skill('${r.session_skill_id}')" class="remove btn btn-danger "><i class="fa fa-trash-o mr-0"></i></a></td>
                    </tr>
                    `
                })

                html + `</table>`
                $("#view_skillType_data").html(html);
            }
        })
    }

    $("#view_skillType").on("shown.bs.modal", e => {
        loadSkills();
    })

    function delete_session_skill(session_skill_id) {
        const skill_name = $(`#skil_${session_skill_id}`).text();
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('academics/lesson_plan/delete_session_skill') ?>',
                    type: 'post',
                    data: { session_skill_id },
                    success: function (data) {
                        let parsedData = JSON.parse(data);
                        if (parsedData) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted',
                                text: 'Skill deleted successfully',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                loadSkills();
                                getSessionData(session_id);
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500
                            })
                        }
                    },
                    error: function (data) {
                        console.log(data);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Something went wrong',
                            showConfirmButton: false,
                            timer: 1500
                        })
                    }
                });
            }
        });
    }
</script>