<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/Expense/');?>">Expenses</a></li>
    <li>Manage Amendments</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('management/Expense/');?>"><span class="fa fa-arrow-left"></span></a>Manage Amendments</h3>
        </div>
        <?php if(! empty($this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE_AMENDMENTS_BUTTON'))){ ?>
        <div class="col-md-3 d-flex justify-content-end">
          <a onclick="open_enter_voucher_no_popup()" class="new_circleShape_res backgroundColor_organge mr-2" data-toggle="tooltip" data-placement="top" title="Create">
            <span class="fa fa-plus" style="font-size: 19px;"></span>
          </a>              
        </div>
        <?php } ?>
      </div>
    </div>
    <div class="col-md-3 form-group">
      <label for="fromdateId" class="control-label">Select Date range</label>
          <div id="reportrange" class="dtrange custom-select " style="width:  100%">                                            
            <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>
    <div id="data_div" class="panel-body col-md-12"></div>

  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script>

    $(document).ready(function(){
      $("#reportrange").daterangepicker({
          ranges: {
              'All': ['All'],
              'Today': [moment(), moment()],
              'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
              'Last 7 Days': [moment().subtract(6, 'days'), moment()],
              'Last 30 Days': [moment().subtract(29, 'days'), moment()],
              'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
          },
          opens: 'right',
          buttonClasses: ['btn btn-default'],
          applyClass: 'btn-small btn-primary',
          cancelClass: 'btn-small btn-primary pull-right',
          format: 'MM.DD.YYYY',
          separator: 'to',
          startDate: moment().subtract(6, 'days'),
          endDate: moment()             
      }, function(start, end, label) {
          if (label === 'All') {
              $('#reportrange span').html('All'); 
              $('#from_date').val('01-01-0001');
              $('#to_date').val('31-12-9999');
              $('#reportrange').data('daterangepicker').setStartDate(moment('0001-01-01'));
              $('#reportrange').data('daterangepicker').setEndDate(moment('9999-12-31'));
          } else {
              $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
              $('#from_date').val(start.format('DD-MM-YYYY'));
              $('#to_date').val(end.format('DD-MM-YYYY'));
          }

          get_expense_amendment_data();
      });

    // Set initial values
    $("#reportrange span").html("All");
    $('#from_date').val('01-01-0001');
    $('#to_date').val('31-12-9999');

      get_expense_amendment_data();
    });

    async function open_enter_voucher_no_popup() {
        const { value: text } = await Swal.fire({
            input: "text",
            title: "Enter Voucher Number",
            inputPlaceholder: "Enter voucher number here...",
            inputAttributes: {
                "aria-label": "Enter voucher number here"
            },
            showCancelButton: true,
            preConfirm: (value) => {
            if (!value) {
                Swal.showValidationMessage("Voucher No. is requried!");
            }
            return value;
        }
        });

        if (text) {

            $.ajax({
                url: '<?php echo site_url('management/Expense/check_expense_voucher_num'); ?>',
                type: 'post',
                data: {text} ,
                success: function(data) {
                    parsed_data = $.parseJSON(data);
                    if(parsed_data !=0){
                            window.location.href = '<?php echo site_url('management/Expense/add_expense_amendments/') ?>'+parsed_data;
                    }else{
                      Swal.fire({
                        icon: "error",
                        title:  "The voucher number " + text + " either does not exist or is not approved.",
                      });
                    }
                },
                error: function (err) {
                    console.log(err);
                }
            });
        }
    }

    function get_expense_amendment_data(){
      var from_date = $("#from_date").val();
      var to_date = $("#to_date").val();
      $.ajax({
          url: '<?php echo site_url('management/Expense/get_expense_amendment_data'); ?>',
          type: 'post',
          data: {
            from_date,to_date
          },
          success: function(data) {
            var parsed_data = $.parseJSON(data);
                    html = construct_expense_amendment_data(parsed_data);
                    $("#data_div").html(html);
                    $('#expense_amendment_table').DataTable( {
                            ordering:false,
                            scrollY :'40vh',
                            "language": {
                                    "search": "",
                                    "searchPlaceholder": "Enter Search..."
                                },
                                "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                                "pageLength": 10,
                                dom: 'lBfrtip',
                                buttons: [
                                    {
                                        extend: 'excelHtml5',
                                        text: 'Excel',
                                        filename: 'expense_amendment_report',
                                        className: 'btn btn-info',
                                        exportOptions: {
                                            columns: ':not(:last-child)' // Exclude the last column
                                        }
                                    },
                                    {
                                        extend: 'print',
                                        text: 'Print',
                                        filename: 'expense_amendment_report',
                                        className: 'btn btn-info',
                                        exportOptions: {
                                            columns: ':not(:last-child)' // Exclude the last column
                                        }
                                    },
                                    {
                                        extend: 'pdfHtml5',
                                        text: 'PDF',
                                        filename: 'expense_amendment_report',
                                        className: 'btn btn-info',
                                        exportOptions: {
                                            columns: ':not(:last-child)' // Exclude the last column
                                        }
                                    }
                                ]
                            } );
          },
          error: function (err) {
              console.log(err);
          }
      });
    }

    function construct_expense_amendment_data(parsed_data){
      var html ='';

      if (parsed_data.length == 0) {   
        html += '<h4 class="no-data-display">No Data</h4>';
      }
      else{

        html += `
        <table id="expense_amendment_table" class="table table-bordered">
                <thead>
                  <tr style="white-space: nowrap">
                    <th>#</th>
                    <th>Voucher No.</th>
                    <th>Date</th>
                    <th>Created by</th>
                    <th>Category</th>
                    <th>Approved/Rejected By</th>
                    <th>Remarks</th>
                    <th>Status</th>
                    <th>Details</th>
                  </tr>
                </thead>
        
        `;
        html += `<tbody>`;
        for(var i=0;i<parsed_data.length;i++){
          let button_status="";
          var data = parsed_data[i];
          // console.log(data['status']);
          html+=`
                  <tr>
                    <td>${i+1}</td>
                    <td>${data['voucher_number']}</td>
                    <td>${data['created_on']}</td>
                    <td>${data['created_by']}</td>
                    <td>${data['category_name'] || '-'}</td>
                    <td>${data['reject_approved_by'] || '-'}</td>
                    <td>${data['reject_approve_remarks'] || '-'}</td>`
                    if(data['status']=='submitted'){
                      html+=`<td style="background:#949494;">Submitted</td>`;
                    }else if(data['status']=='approved'){
                      html+=`<td style="background:lightgreen;">Approved</td>`;
                    }else{
                      html+=`<td style="background:#ff4a4a;">Rejected</td>`;
                    }
                    html+=`<td style="width:20%;"><button class="btn btn-primary" onclick="show_expense_amendment_items(${data['id']},'${data['voucher_number']}','${data['reject_approved_on']}','${data['reject_approved_by']}','${data['status']}')">View</button>`;
                    data['status'] !='submitted' ? button_status="disabled" : ""; 
                    <?php if(! empty($this->authorization->isAuthorized('EXPENSE.APPROVE_REJECT_AMENDMENTS_BUTTON'))){ ?>
                      html+=`<button ${button_status} style="margin-left:4px;" class="btn btn-warning" onclick="approve_reject_amendment(${data['id']},'${data['voucher_number']}','${data['em_id']}')">Approve/Reject</button></td>`;
                    <?php } ?>

          html+=`</tr>`;
        }
        html+=`</tbody>
            </table>`;
      }
      return html;
    }

    function show_expense_amendment_items(id,voucher_number,reject_approved_on,reject_approved_by,status){
      // console.log(status);
      $.ajax({
          url: '<?php echo site_url('management/Expense/get_expense_amendment_items'); ?>',
          type: 'post',
          data: {id},
          success: function(data) {
            var parsed_data = $.parseJSON(data);
            let html='';

            html+=`<table class="table table-bordered" style="text-align:left">`;
            for (let i = 0; i < parsed_data.length; i++) {
              const element = parsed_data[i];
              // console.log(element);
              if(element['field_name']=='name'){
                html+=`
                <tr>
                  <th style="width:50%">New Expense Name</th>
                  <td>${element['new_value']}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Old Expense Name</th>
                  <td>${element['old_value']}</td>
                </tr>`;
              }
              if(element['field_name']=='vendor_bill_no'){
                html+=`
                <tr>
                  <th style="width:50%">New Vendor Bill No</th>
                  <td>${element['new_value']}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Old Vendor Bill No</th>
                  <td>${element['old_value']}</td>
                </tr>`;
              }
              if(element['field_name']=='amount'){
                html+=`
                <tr>
                  <th style="width:50%">New Amount</th>
                  <td>${element['new_value']}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Old Amount</th>
                  <td>${element['old_value']}</td>
                </tr>`;
              }
              if(element['field_name']=='made_on'){
                html+=`
                <tr>
                  <th style="width:50%">New Bill/Expense Date</th>
                  <td>${element['new_value']}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Old Bill/Expense Date</th>
                  <td>${element['old_value']}</td>
                </tr>`;
              }

            }
            if(status=="rejected"){
                console.log("hi");
                html+=`
                <tr>
                  <th style="width:50%">Rejected on</th>
                  <td>${reject_approved_on}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Rejected By</th>
                  <td>${reject_approved_by}</td>
                </tr>`;
              }
              if(status=="approved"){
                html+=`
                <tr>
                  <th style="width:50%">Approved on</th>
                  <td>${reject_approved_on}</td>
                </tr>`;
                html+=`
                <tr>
                  <th style="width:50%">Approved By</th>
                  <td>${reject_approved_by}</td>
                </tr>`;
              }

            html+=`</table>`;
            Swal.fire({
              title: "Modified fields of voucher - "+voucher_number,
              width: '42em',
              html: html,
            });
          },
          error: function (err) {
              console.log(err);
          }
      });
    }

    function approve_reject_amendment(expense_amendment_id,voucher_number,em_id){
      window.location.href = '<?php echo site_url('management/Expense/approve_reject_amendment/') ?>'+expense_amendment_id+'/'+voucher_number+'/'+em_id;
    }

</script>
<style>
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
    cursor: pointer;
  }
  .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
  /* styles for DataTable end */
  .dataTables_scrollBody{
    margin-top: -13px;
  }
</style>