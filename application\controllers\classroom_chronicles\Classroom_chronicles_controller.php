<?php

class Classroom_chronicles_controller extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('CLASSROOM_CHRONICLES')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('classroom_chronicles/Classroom_chronicles_model');
    $this->load->model('staff/Staff_Model');
    $this->load->model('class_section');
    $this->load->model('communication/texting_model', 'texting_model');
    $this->load->library('filemanager');

    $this->templates = [
    ["name" => "Blank Template", "value" => ""],
    ['name'=>'Classroom Chronicles', 'value' => '<style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style><table><tr><td colspan="2"  style="text-align:center"><strong>DAILY CLASS ROOM CHRONICLE</strong></td></tr><tr><td width=50%>Schedule</td><td ></td></tr><tr><td >Assembly</td><td></td></tr><tr><td >Breakfast</td><td ></td></tr><tr><td >Period 1</td><td ></td></tr><tr><td >Period 2</td><td ></td></tr><tr><td >Short Break</td><td ></td></tr><tr><td >Period 3</td><td ></td></tr><tr><td >Period 4</td><td ></td></tr><tr><td >Period 5</td><td ></td></tr><tr><td >Lunch Break</td><td ></td></tr><tr><td >Period 6</td><td ></td></tr><tr><td >Period 7</td><td ></td></tr><tr><td >Period 8</td><td ></td></tr><tr><td >Dispersal</td><td ></td></tr><tr><td >Homework 1</td><td ></td></tr><tr><td >Homework 2</td><td ></td></tr><tr><td >Homework 3</td><td ></td></tr><tr><td >Homework 4</td><td ></td></tr><tr><td >Additonal Paritculars</td><td ></td></tr></table>'],
    ['name'=>'Class Work', 'value' => '<style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style>
<table>
  <tr>
    <td colspan="3"  style="text-align:center"><strong>Class Work</strong>
    </td>
  </tr>
  <tr>
    <td width=15%>Period</td>
    <td width=15%>Subject</td>
    <td width=50%>Class Work</td>
  </tr>
  <tr>
    <td >1</td>
    <td></td>
    <td></td>
  </tr>
  <tr>
    <td >2</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td >3</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr><td >4</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td >5</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td >6</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td >7</td>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td >8</td>
    <td ></td>
    <td ></td>
  </tr>
</table><br>

<style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style>
<table>
  <tr>
    <td colspan="2"  style="text-align:center"><strong>Home Assignment</strong>
    </td>
  </tr>
  <tr>
    <td width=15%>Subject</td>
    <td width=50%>Assignment</td>
  </tr>
  <tr>
    <td></td>
    <td></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
  <tr>
    <td ></td>
    <td ></td>
  </tr>
</table>']
    ];
  }

  public function index() {
    $site_url = site_url();
    $data['adm_tiles'] = array(
      [
        'title' => 'Create ' . ($this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Chronicles'),
        'sub_title' => 'Add ClassRoom chronicles',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'classroom_chronicles/Classroom_chronicles_controller/add_chronicles',  
        'permission' => $this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')
      ],
      [
        'title' => 'Mass Students',
        'sub_title' => 'Add ClassRoom chronicles',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'classroom_chronicles/Classroom_chronicles_controller/mass_add_students',  
        'permission' => $this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')
      ],
      [
        'title' => 'View ' . ($this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Chronicles'),
        'sub_title' => 'View From Parentside',
        'icon' =>'svg_icons/assessment.svg',
        'url' => $site_url.'classroom_chronicles/Classroom_chronicles_controller/view_chronicles',  
        'permission' => $this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')
      ]
    );
        
    $data['adm_tiles'] = checkTilePermissions($data['adm_tiles']);
    $data['report_tiles'] = array( 
      [
        'title' => ($this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Chronicles') .' Report',
      'sub_title' => 'Summary Report',
      'icon' =>'svg_icons/assessment.svg',
      'url' => $site_url.'classroom_chronicles/Classroom_chronicles_controller/chronicles_report',  
      'permission' => $this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
    $data['main_content']    = 'classroom_chronicles/classroom_chronicles_menu';
    $this->load->view('inc/template', $data);
  }

  public function add_chronicles(){
    $data['templates'] = $this->templates;
    $data['main_content']    = 'classroom_chronicles/add_chronicles';
    $this->load->view('inc/template', $data);
  }

  public function mass_add_students(){
    $data['classes'] = $this->class_section->getAllClassess();
    $data['templates'] = $this->templates;
    $data['main_content']    = 'classroom_chronicles/mass_add_chronicles';
    $this->load->view('inc/template', $data);
  }

  public function view_chronicles(){
    $data['main_content']    = 'classroom_chronicles/view_chronicles';
    $this->load->view('inc/template', $data);
  }

  public function chronicles_report(){
    $data['class_report'] = $this->Classroom_chronicles_model->get_class_wise_student_list_data();
    $data['main_content']    = 'classroom_chronicles/chronicles_report';
    $this->load->view('inc/template', $data);
  }

  public function get_class_section(){
    $result = $this->Classroom_chronicles_model->get_class_names();
    echo json_encode($result);   
  }

  public function get_class_section_student_data_names(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->Classroom_chronicles_model->get_class_section_student_data_names($class_id,$sectionId);
    echo json_encode($result);
  }

  public function get_class_section_student_names(){
    $cls_section = $this->input->post('cls_section');
    list($class_id,$sectionId) = explode('_',$cls_section);
    $result = $this->Classroom_chronicles_model->get_class_section_student_data($class_id,$sectionId);
    echo json_encode($result);
  }

  public function getStudentDetails(){
    $sectionId = $_POST['sectionId'];
    $selected_date = $_POST['selected_date'];
    $stdData = $this->Classroom_chronicles_model->getstdIdsBySection($sectionId);
    foreach ($stdData as $key => $val) {
      $val->sent_check = $this->Classroom_chronicles_model->check_send_data_by_selected_date($val->id, $selected_date);
    }
    echo json_encode($stdData);
  }

  public function save_chronicles(){
    $files_array= array();
    if(!empty($_FILES['certificate_name'])){
      $path = $this->s3FileUpload($_FILES['certificate_name'],'chronicles');
      if($path['file_name'] != '') {
        array_push($files_array, array('name' => $_FILES['certificate_name']['name'], 'path' => $path['file_name']));
      }
    }
  
    $result = $this->Classroom_chronicles_model->save_chronicles_model($files_array);
    
    if($result){
      $email_content =$this->Classroom_chronicles_model->get_email_chronicles_pdf();
       if (empty($email_content)) {
      echo "Email template not found";
      exit();
    }
    $content = $email_content->content;
    $files_array = array();
    if($path['file_name'] != '') {
      array_push($files_array, array('name' => $_FILES['certificate_name']['name'], 'path' => $path['file_name']));
    }
    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }
    $student_data = $this->Classroom_chronicles_model->getChroniclesStudentDetailsbyid($this->input->post('student_id'));
    $memberEmail = [];
    $sender_list = [];
    foreach ($student_data as $key => $val) {
      $memberEmail[]['email'] = $val->email;
      $sender_list['students'] = [
        'send_to' => 'Both',
        'send_to_type' => $val->relation_type,
        'ids' => $val->std_id,
      ];
    }
    $email_master_data = array(
      'subject' => $email_content->email_subject,
      'body' => $content,
      'source' => 'Classroom Chronicles',
      'sent_by' => $this->authorization->getAvatarId(),
      'recievers' => 'Parents',
      'from_email' => $email_content->registered_email,
      'files' => ($files_string=='')?NULL:$files_string,
      'acad_year_id' => $this->acad_year->getAcadYearId(),
      'visible' => 1,
      'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
      'sending_status' => 'Send Email',
      'texting_master_id'=>$email_content->id
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
  
    $sent_data = array();
    $stakeholder_ids = (array)$this->input->post('student_id');
    $sent_data = $this->Classroom_chronicles_model->getStudents_for_email($stakeholder_ids, 'Both');
    $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    $this->load->helper('email_helper');
    $email = $this->emails_model->getEmailInfo($email_master_id);
    $email_ids = [];
    foreach ($student_data as $key => $val) {
      if($val->email != '' && $val->email != null) {
        array_push($email_ids, $val->email);
      }
    }
    $email_ids[] = $email_content->members_email;
    echo sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }

    $school_name = $this->settings->getSetting('school_name');
    $input_array = array(
      'mode' => 'notification', 
      'title' => $school_name, 
      'message' => 'Classroom chronicles new notification', 
      'source' => 'Classroom Chronicles',
      'student_url' => '',
      'visible' => 1,
      'send_to' => 'Both',
      'acad_year_id' => $this->acad_year->getAcadYearId()
    );
    if(!empty($input['student_id'])) {
      $input_array['student_ids'] =(array)$this->input->post('student_id');
    }

    $this->load->helper('texting_helper');
    sendText($input_array);

  }

  private function s3FileUpload($file, $folder_name = 'profile'){
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  public function get_chronicles_by_student_id(){
    $student_id = $_POST['student_id'];
    $selected_date = $_POST['selected_date'];
    $data = $this->Classroom_chronicles_model->getchronicles($student_id, $selected_date);
    echo json_encode($data);
  }

  public function download_chronicles_by_rowid($rowId){
    $downloadfile = $this->Classroom_chronicles_model->download_chronicles_by_row_id($rowId);
    $link = json_decode($downloadfile->file_path);
    $file_name = $link[0]->name;
    $url = $this->filemanager->getFilePath($link[0]->path);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($file_name, $data, TRUE);

    // $document_data = file_get_contents($document_url);
    // $this->load->helper('download');
    // force_download('certificates.pdf', $document_data, TRUE);
  }

  public function get_chronicles_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $student_id = $_POST['student_id'];
    $result = $this->Classroom_chronicles_model->getChroniclesReport($from_date, $to_date, $student_id);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }

  public function getTemplate() {
    $templateName = $this->input->post('templateName');
    echo json_encode($this->__getTemplateByName($templateName));
  }

  private function __getTemplateByName ($templateName) {
    $templateValue = '';
    foreach ($this->templates as $temp) {
      if ($temp['name'] == $templateName) {
        $templateValue = $temp['value'];
        break;
      }
    }
    return $templateValue;
  }

  public function get_chronicles_by_id(){
    $id = $_POST['id'];
    $data = $this->Classroom_chronicles_model->getchronicles_id($id);
    echo json_encode($data);
  }

  public function get_chronicles_count_by_cls_id() {
    $class_section_id = $_POST['class_section_id'];
    $data = $this->Classroom_chronicles_model->getchronicles_count_byclsid($class_section_id);
    echo json_encode($data);
  }

  public function parent_view(){
    $data['main_content']    = 'classroom_chronicles/parentside_view_menu';
    $this->load->view('inc/template', $data);
  }

  public function getchronicles_clone() {
    $selectedDate = $_POST['selectedDate'];
    $chronicles = $this->Classroom_chronicles_model->getchronicles_clone($selectedDate);
    echo json_encode($chronicles);
  }

  public function getchroniclesById() {
      $chronicles_id = $_POST['chronicles_id'];
      echo json_encode($this->Classroom_chronicles_model->getchroniclesById($chronicles_id));
  }

  public function save_mass_student_chronicles(){
    $files_array= array();
    if(!empty($_FILES['certificate_name'])){
      $path = $this->s3FileUpload($_FILES['certificate_name'],'chronicles');
      if($path['file_name'] != '') {
        array_push($files_array, array('name' => $_FILES['certificate_name']['name'], 'path' => $path['file_name']));
      }
    }
    $result = $this->Classroom_chronicles_model->save_mass_student_chronicles_model($files_array);
    if($result){
      $email_content =$this->Classroom_chronicles_model->get_email_chronicles_pdf();
       if (empty($email_content)) {
      echo "Email template not found";
      exit();
    }
    $content = $email_content->content;
    $files_array = array();
    if($path['file_name'] != '') {
      array_push($files_array, array('name' => $_FILES['certificate_name']['name'], 'path' => $path['file_name']));
    }
    if(!empty($files_array)) {
      $files_string = json_encode($files_array);
    }
    $student_data = $this->Classroom_chronicles_model->getChroniclesStudentDetailsbymassid($this->input->post('student_ids'));
    // echo "<pre>"; printf($student_data); die();

    $memberEmail = [];
    $sender_list = [];
    foreach ($student_data as $key => $val) {
      $memberEmail[]['email'] = $val->email;
      $sender_list['students'] = [
        'send_to' => 'Both',
        'send_to_type' => $val->relation_type,
        'ids' => $val->std_id,
      ];
    }
    $email_master_data = array(
      'subject' => $email_content->email_subject,
      'body' => $content,
      'source' => 'Classroom Chronicles',
      'sent_by' => $this->authorization->getAvatarId(),
      'recievers' => 'Parents',
      'from_email' => $email_content->registered_email,
      'files' => ($files_string=='')?NULL:$files_string,
      'acad_year_id' => $this->acad_year->getAcadYearId(),
      'visible' => 1,
      'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
      'sending_status' => 'Send Email',
      'texting_master_id'=>$email_content->id
    );
    $this->load->model('communication/emails_model');
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
  
    $sent_data = array();
    $stakeholder_ids = (array)$this->input->post('student_ids');
    $sent_data = $this->Classroom_chronicles_model->getStudents_for_email($stakeholder_ids, 'Both');
    $status = $this->emails_model->save_sending_data($sent_data, $email_master_id);
    $this->load->helper('email_helper');
    $email = $this->emails_model->getEmailInfo($email_master_id);
    $email_ids = [];
    foreach ($student_data as $key => $val) {
      if($val->email != '' && $val->email != null) {
        array_push($email_ids, $val->email);
      }
    }
    $email_ids[] = $email_content->members_email;
    echo sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }

    $school_name = $this->settings->getSetting('school_name');
    $input_array = array(
      'mode' => 'notification', 
      'title' => $school_name, 
      'message' => 'Classroom chronicles new notification', 
      'source' => 'Classroom Chronicles',
      'student_url' => '',
      'visible' => 1,
      'send_to' => 'Both',
      'acad_year_id' => $this->acad_year->getAcadYearId()
    );
    if(!empty($input['student_id'])) {
      $input_array['student_ids'] =(array)$this->input->post('student_id');
    }

    $this->load->helper('texting_helper');
    sendText($input_array);

  }
}?>