<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('event/event_dashboard');?>">Events</a></li>
  <li><a href="<?php echo site_url('event');?>">Manage Event</a></li>
  <li><a href="<?php echo site_url('event/event_details/'.$event_id);?>">Event Detail</a></li>
  <li>Edit Event</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('event/event_details/'.$event_id);?>">
            <span class="fa fa-arrow-left"></span>
          </a>Edit Event</h3>
        </div>
      </div>
    </div>
    <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('event/update_event/'.$event_edit['event']->id);?>" data-parsley-validate="" class="form-horizontal">
      <div class="panel-body">
        <div class="col-md-12">                                                         
          <div class="col-md-6">

            <div class="form-group">
              <label class="control-label col-md-4">Event Name <font color="red">*</font></label>
              <div class="col-sm-8">
                <input type="text" required="" placeholder="Event Name" value="<?php echo $event_edit['event']->event_name; ?>" name="event_name" class="form-control">
              </div>
            </div>
        
           
            <div class="form-group">
              <label class="control-label col-md-4"># per registration <font color="red">*</font></label>
              <div class="col-sm-8">
                <input type="number" required="" placeholder="0" min="0" max="10" step="100"  data-parsley-validation-threshold="1"  value="<?php echo $event_edit['event']->no_of_person_allowed_per_registration; ?>" data-parsley-trigger="keyup"  data-parsley-type="digits" name="no_of_person_allowed" class="form-control">
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">Max allowed <font color="red">*</font></label>
              <div class="col-sm-8">
                <input type="number" required="" placeholder="0"  value="<?php echo $event_edit['event']->max_number_of_allowed; ?>" data-parsley-type="digits" name="max_number_of_allowed" class="form-control">
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">Start Date<font color="red">*</font></label>
              <div class="col-sm-8">
                  <input name="start_date_of_registration" value="<?php echo date('d-m-Y',strtotime($event_edit['event']->reg_start_date)) ; ?>" autocomplete="off" required="" class="form-control datetimepicker3" placeholder="Start Date of registration" type="text" id="datetimepicker3" /></td>
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">Start Time<font color="red">*</font></label>
              <div class="col-sm-8">
                  <input name="start_time_of_registration" autocomplete="off" required="" class="form-control starttimeofregistration" value="<?php echo date('H:i',strtotime($event_edit['event']->reg_start_date)) ; ?>" placeholder="Start Time of registration" type="text" id="starttimeofregistration" /></td>
              </div>
            </div>


            <div class="form-group">
              <label class="control-label col-md-4">End Date<font color="red">*</font></label>
              <div class="col-sm-8">
                <input name="end_date_of_registration" value="<?php echo date('d-m-Y', strtotime($event_edit['event']->reg_end_date)) ; ?>" autocomplete="off" required="" class="form-control datetimepicker4" placeholder="End Date of registration" type="text" id="datetimepicker4" /></td>
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">End Time<font color="red">*</font></label>
              <div class="col-sm-8">
                <input name="end_time_of_registration" autocomplete="off" required="" class="form-control endtimeofregistration" value="<?php echo date('H:i',strtotime($event_edit['event']->reg_end_date)) ; ?>" placeholder="End Time of registration" type="text" id="endtimeofregistration" /></td>
              </div>
            </div>

           <div class="form-group">
              <label class="control-label col-md-4">Registration Amount</label>
              <div class="col-sm-8">
                <input type="text" placeholder="Registration Amount" value="<?php echo $event_edit['event']->registration_amount; ?>"  name="registration_amount"  class="form-control" value="0">
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">Safety Deposit Amount</label>
              <div class="col-sm-8">
                <input type="text" placeholder="Deposit Amount" value="<?php echo $event_edit['event']->safety_deposit_amount; ?>" name="safety_deposit_amount"  class="form-control" value="0">
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-md-4">Class Section applicable for<font color="red">*</font></label>
              <div class="col-sm-8">
                <?php 
                 $selectedClassesSection = json_decode($event_edit['event']->class_sections_id);
                  $arr = array();
                  foreach ($sections as $key => $section) { 
                    $arr[$section->id] = $section->class_name.''.$section->section_name; 
                  }
                ?>
                <select name="section_name[]" id="sectionId" multiple title="Select Class Section" class="form-control select">
                  <?php foreach ($arr as $key => $section) { ?>
                    <option <?php if(in_array($key, $selectedClassesSection)) echo 'selected' ?> value="<?= $key ?>"><?php echo $section;?></option>
                  <?php } ?>
                </select>
              </div>
            </div>


            <div class="form-group">
              <label class="control-label col-md-4">Organizer</label>
              <div class="col-sm-8">
                <input type="text" placeholder="Organizer Name"  data-parsley-pattern="^[a-zA-Z ]+$" name="organizer"  value="<?php echo $event_edit['event']->organizer; ?>" class="form-control" value="">
              </div>
            </div>
           
            <div class="form-group">
              <label class="control-label col-md-4" for="Prospect">Venue</label>
                <div class="col-sm-8"> 
                  <label class="radio-inline" for="suffix-1">
                  <input checked=""  <?php if ($event_edit['event']->event_venue=='On Campus') echo "checked"  ?> type="radio" name="event_venue" id="suffix-1" value="On Campus" >
                  On Campus
                  </label>
                  <label class="radio-inline" for="suffix-0">
                  <input  type="radio" <?php if ($event_edit['event']->event_venue=='Off Campus') echo "checked"  ?> name="event_venue"  id="suffix-0" value="Off Campus"  >
                  Off Campus
                  </label>
                </div>
            </div>

            <div class="form-group abc" id="show_off_campus11" style="display: none;">
              <label class="control-label col-md-4">Address</label>
                <div class="col-sm-8">
                  <textarea name="event_venue_address" class="form-control"><?php echo $event_edit['event']->event_venue_address; ?></textarea>
                </div>
            </div>
            <div class="form-group">
              <label for="fileupload" class="col-md-4 control-label"> Add Attachment</label>
              <div class="col-sm-8">
                <input type="file" class="form-control" id="fileupload" name="event_pdf_file" accept="application/pdf">
                <span id="fileuploadError" style="color: red;"></span>
              </div>
            </div>

         
            <div class="form-group">
              <label class="control-label col-md-4">Who Can Register</label>
              <div class="col-sm-8">
                <select class="form-control" name="who_can_register">
                  <option <?php if ($event_edit['event']->who_can_register=='parent') echo "selected"  ?> value="parent">Parents/Students</option>
                  <option <?php if ($event_edit['event']->who_can_register=='school') echo "selected"  ?>  value="school">School</option>
                  <option <?php if ($event_edit['event']->who_can_register=='both') echo "selected"  ?> value="both">Both</option>
                </select>
              </div>
            </div>


            <div class="form-group">
              <label class="control-label col-md-4">Description</label>
              <div class="col-sm-8">
                <textarea name="event_description" placeholder="Description of the event" class="summernote"><?php echo $event_edit['event']->event_description; ?></textarea>
              </div>
            </div>


            <div class="form-group">
              <label class="control-label col-md-4">Terms & Conditions</label>
              <div class="col-sm-8">
                <textarea name="terms_conditions" placeholder="Terms and Conditions" class="summernote"><?php echo $event_edit['event']->terms_conditions; ?></textarea>
              </div>
            </div>
          </div>
        <div class="col-md-6">
            <label>Event Date And Time</label>
             <ul class="panel-controls">
                <li><a href="javascript:void(0)" data-placement='top' data-toggle='tooltip' data-original-title='Add Row' onclick="insRow()" id="addmorePOIbutton"  class="control-primary"><span class="fa fa-plus"></span></a></li>
              </ul>
            <div class="form-group">
              <div class="col-md-12">
                 <div id="POItablediv">
                    <table id="POITable">
                      <tr style>
                        <td style="display: none;">No</td>
                        <th>Date</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                      </tr>
                      <?php if (empty($event_edit['event_date'])) { ?>
                        <tr>
                          <td>
                            <input  name="event_start_date[]" class="form-control datetimepicker1" placeholder="Date" type="text" id="datetimepicker1" />
                          </td>
                          <td>
                            <input name="event_start_time[]" id="startTime" class="form-control startTime" placeholder="Start Time" type="text"/>
                          </td>
                          <td>
                            <input name="event_end_time[]" id="EndTime" class="form-control EndTime" placeholder="End Time" type="text"/>
                          </td>
                          <td>
                            <input type="text" name="description[]" class="form-control" required="" id="description"placeholder="Description of the event">
                          </td>

                          <td>
                            <ul class="panel-controls" id="delPOIbutton" onclick="deleteRow(this)">
                             <li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li>
                            </ul>
                          </td>
                        </tr>
                      <?php }else{
                        $i = 1;
                        foreach ($event_edit['event_date'] as $key => $val) { ?>
                          
                          <tr>
                            <td style="display: none;">1
                              <input id="autoid<?php echo $i++;?>" value="<?php echo $val->id;?>" data-parsley-group="block2" name="auto_id[]" placeholder="Enter Name" class="form-control" type="hidden"/>
                            </td>
                            <td>
                              <input  name="event_start_date[]"  value="<?php echo date("d-m-Y", strtotime($val->event_date))  ?>" class="form-control datetimepicker1" placeholder="Date" type="text" id="datetimepicker1" /></td>
                            <td>
                              <input required="" name="event_start_time[]" id="startTime" value="<?php echo $val->start_time ?>" class="form-control startTime" placeholder="Start Time" type="text"/>
                            </td>
                            <td>
                              <input name="event_end_time[]" id="EndTime" value="<?php echo $val->end_time ?>" class="form-control EndTime" placeholder="End Time" type="text"/>
                            </td>
                            <td>
                              <input type="text" name="description[]" value="<?php echo $val->description ?>" class="form-control" required="" id="description"placeholder="Description of the event">
                            </td>
                            
                            <td>
                              <ul class="panel-controls" id="delPOIbutton" onclick="deleteRow(this)">
                               <li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li>
                              </ul>
                            </td>
                          </tr>

                        <?php }
                      }
                      ?>
                     
                    </table> 
                </div> 
                <div class="form-group">
              <label class="control-label col-md-4">Add Sub-events? </label>
              <div class="col-sm-8" style="margin-top: 0.5rem;">
               <div class="form-check form-check-inline">
                  <input class="form-check-input" <?php if($event_edit['event']->is_sub_event == 'Yes') echo 'checked' ?> type="radio" id="is_sub_event" name="is_sub_event" value="Yes">
                  <label class="form-check-label" for="is_sub_event">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio"  <?php if($event_edit['event']->is_sub_event == 'No') echo 'checked' ?> id="is_sub_event1" name="is_sub_event" value="No" >
                  <label class="form-check-label" for="is_sub_event1">No</label>
                </div>
              
              </div>
            </div>

            <div id="subeventSelections" style="display: none;">

              <div class="form-group">
                <label class="control-label col-md-4"># of Sub-events student can select? </label>
                <div class="col-sm-8">
                  <input type="text" placeholder="Enter Number"  id="sub_event_select_number"  name="sub_event_select_number"  class="form-control" value="<?php echo $event_edit['event']->sub_event_select_number; ?>">
                </div>
              </div>

              <div class="form-group">
                <label class="control-label col-md-4">Sub-event header </label>
                <div class="col-sm-8">
                  <input type="text" placeholder="Enter Header"  id="sub_event_header" name="sub_event_header"  class="form-control" value="<?php echo $event_edit['event']->sub_event_header; ?>">
                </div>
              </div>
          
              <div class="form-group" >
                <label class="control-label col-md-4">Is Sub-events selection mandatory? </label>
                <div class="col-sm-8" style="margin-top: 0.5rem;">
                 <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" <?php if($event_edit['event']->is_sub_event_mandatory == 'Yes') echo 'checked' ?> id="ismandatory" name="is_sub_event_mandatory" value="Yes">
                    <label class="form-check-label" for="ismandatory">Yes</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" <?php if($event_edit['event']->is_sub_event_mandatory == 'No') echo 'checked' ?> id="ismandatory1" name="is_sub_event_mandatory" value="No">
                    <label class="form-check-label" for="ismandatory1">No</label>
                  </div>

                </div>
              </div>
            </div>

              </div>
            </div>
          </div>

        </div>
      </div>
      <div class="card-footer">
        <center>
          <button type="submit" class="btn btn-primary">Update</button>
          <a class="btn btn-danger" href="<?php echo site_url('event/event_details/'.$event_id);?>">Cancel</a>
        </center>
      </div>
    </form>
  
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/summernote/summernote.js"></script>

<script type="text/javascript">

$(document).ready(function() {
if ($('#is_sub_event').prop('checked')) {
    $("#subeventSelections").show();
  } else {
    $("#subeventSelections").hide();
  }
});





$('#is_sub_event').click(function(){
    if($(this).val()==="Yes"){
      $("#subeventSelections").show();
      
    } 
  });
  $('#is_sub_event1').click(function(){
    if($(this).val()==="No"){
      $("#subeventSelections").hide();
      $('#sub_event_select_number').val('');
      $('#sub_event_header').val('');
      $('#ismandatory1').attr('checked',false);
      $('#ismandatory').attr('checked',false);
    } 
  });

  function add_more_dates() {
    $('#show_more_event_date').show();
    $('.datetimepicker2').prop('required',true);
    $('.startTime').prop('required',true);
    $('.EndTime').prop('required',true);
  }


  $('#fileupload').change(function(){
    var src = $(this).val();
    if(src && validateFile(this.files[0], 'fileupload')){
        $("#fileuploadError").html("");
    } else{
        this.value = null;
    }
  });

  function validateFile(file,errorId){
    if (file.size > 15000000 || file.fileSize > 15000000)
    {
       $("#"+errorId+"Error").html("Allowed file size exceeded. (Max. 10 MB)")
       return false;
    }
    if(file.type != 'application/pdf') {
        $("#"+errorId+"Error").html("Allowed file type is PDF");
        return false;
    }
    return true;
  }


$(document).ready(function(){
  $('#datetimepicker1, #datetimepicker3,#datetimepicker4').datepicker({
    startDate: '1d',
    format: 'd-m-yyyy',
    "autoclose": true
  });
});
$(document).ready(function(){
  $('#startTime, #EndTime,  #starttimeofregistration, #endtimeofregistration').datetimepicker({
    format: 'HH:mm',
  });
});
  function deleteRow(row){
    var i=row.parentNode.parentNode.rowIndex;
    if(i==1){
      $('#show_more_event_date').hide();
      $('.datetimepicker2').prop('required',false);
      $('.startTime').prop('required',false);
      $('.EndTime').prop('required',false);
    }else{
     document.getElementById('POITable').deleteRow(i);
     var tot=$('#noOfStop').val();
     $('#noOfStop').val(tot - 1);
    } 
  }

function insRow(){
    var x=document.getElementById('POITable');
    var new_row = x.rows[1].cloneNode(true);
    var len = x.rows.length;
    new_row.cells[0].innerHTML = len;
    var inp1 = new_row.cells[1].getElementsByTagName('input')[0];
    inp1.id += len;
    inp1.value += '';
    var inp2 = new_row.cells[2].getElementsByTagName('input')[0];
    inp2.id += len;
    inp2.value += '';
    x.appendChild( new_row );
    
    $('body').on('focus',".datetimepicker1", function(){
      $(this).datepicker({
        format: 'd-m-yyyy',
        startDate: '1d',
        "autoclose": true
      });
    });
    $('body').on('focus',".startTime,.EndTime,.reportingTime", function(){
      $(this).datetimepicker({
        format: 'hh:mm',
      });
  });
}
</script>

<script type="text/javascript">
  $('#suffix-0').click(function(){
    if($(this).val()==="Off Campus"){
      $("#show_off_campus11").show();
      $("#show_off_campus22").show();
    } 
  });
  $('#suffix-1').click(function(){
    if($(this).val()==="On Campus"){
      $("#show_off_campus11").hide();
      $("#show_off_campus22").hide();
    } 
  });
</script>


 <script type="text/javascript">
  $(document).ready(function(){ 
    var post_edit = '<?php echo $event_edit['event']->event_venue;?>';
    if (post_edit === 'Off Campus') {
      $("#show_off_campus11").show();
      $("#show_off_campus22").show();
    }
  });
  </script>
<style>
  @media only screen and (max-width: 768px){
    input[type=radio], input[type=checkbox] {
        margin: 1px -14px -6px;
    }
  }
</style>