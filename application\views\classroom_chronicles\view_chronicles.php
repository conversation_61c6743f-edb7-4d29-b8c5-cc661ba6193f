<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller');?>"><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></li>
  <li>View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?>
          </h3>
        </div>
      </div>
    </div>

    <div class="modal-body">
      <div class="col-md-12">

        <div class="col-md-3">
          <div class="form-group">
            <label class="control-label" for="section_id">Class/Section</label>
              <select required="" class="form-control" id="class_section" name="class_section" >
              </select>
          </div>
        </div>

        <div class="col-md-3">
          <div class="form-group">
            <label class="control-label" for="student_id"> Select Student(s)</label>
              <select required="" class="form-control" id="student_name" name="student_name">
              
                <option>Select Class / Section</option></select>
          </div>
        </div>

        <div class="col-md-3">
          <div class="form-group">
            <label class="control-label" for="student_id">Select Date</label>
            <div id="reportrange" class="dtrange form-control">  
              <span></span>                                          
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
              </div> 
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label class="control-label"> </label>
            <button type="button"  class="btn btn-primary form-control" onclick="generate_chronicles_report()">Get Report</button>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-body">
      <div class="chronicles"></div>
    </div>
</div>
</div>

<div class="modal fade" id="view_chronicles" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></h4>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <div id="chronicles-detail">
                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>

<script type="text/javascript">
  $(document).ready(function() {
    var maxDate = new Date();
    maxDate.setDate(maxDate.getDate());
    var minDate = new Date();
    minDate.setFullYear( minDate.getFullYear() - 1);
    var dateNow = new Date();

    $('#datePicker,#select_date,#datetimepicker1').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
  });

  function Click() {
    var q = document.getElementById("name").value;
    if(q != 0) {
      swal("Loading !....", { 
        button: false,
      });  
    }
  }

  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     //'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

</script>

<script type="text/javascript">
  // get_student_attendance_current_month_calendar();
    $(document).ready(function() {
        get_class_section();
    });

    function get_class_section(){
        $('#student_name').show();
    $('#search').show();
        $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_class_section'); ?>',
          type: "post",
          success: function (data) {
          var clsSection = $.parseJSON(data);
          option = '<option value="">Class Section</option>';
          for (i = 0; i < clsSection.length; i++) {
          option +='<option value="'+clsSection[i].cId+'_'+clsSection[i].csId+'">'+clsSection[i].classSection+'</option>'
          }
          $('#class_section').html(option);
          },

          error: function (err) {
            console.log(err);
          }
    });
    }


    $("#class_section").on('change', function(){    
    var cls_section = $('#class_section').val();
    $('#clsSection').show();
    $('#student_name').show();
    $('#search').show();
    $.ajax({
          url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_class_section_student_data_names'); ?>',
        type: "post",
        data :{'cls_section':cls_section},
          success: function (data) {
            var list = $.parseJSON(data);
          option = '';
          option += '<option value="">Select </option>';
          for (i = 0; i < list.length; i++) {
          option +='<option value="'+list[i].sId+'">'+list[i].sName+'</option>'
          }
          $('#userSelect').show();
          $('#student_name').html(option);
        

          },
          error: function (err) {
            console.log(err);
          }
      });
  });

  $("#student_name").on('change', function(){    
    $('#search').show();
  });

  function checkvalueDisabled(i) {
  var checkvalue = $('#check'+i).val();
  const checkBox=$('#check'+i);

  if ($(checkBox).is(':checked')) {
    // $(checkBox).attr('value', 'true');
    $('#view_button'+i).prop('disabled',false);
  } else {
    // $(checkBox).attr('value', 'false');
    $('#view_button'+i).prop('disabled',true);
  }

 }

$('#search').on('click',function(){
      var from_date = $('#from_date').val();
      var to_date = $('#to_date').val();
      var student_id = $('#student_name').val();
      $('#fromDate').html(from_date);
      $('#toDate').html(to_date);
  });
 function generate_chronicles_report(){
  var student_id = $('#student_name').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $('#fromDate').html(from_date);
    $('#toDate').html(to_date);
    $.ajax({
        url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_chronicles_report'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date,'student_id':student_id},
        success: function(data) {
          var echr_data = JSON.parse(data);
          console.log(echr_data);
          if (echr_data.length > 0) {
            $(".chronicles").html(prepare_chronicles_table(echr_data));            
          }else{
             $(".chronicles").html('<div class="no-data-display">Result not found</div>');
          }
          

          $('#reportTable').DataTable({ });
        }
    });
  }

  function prepare_chronicles_table(chr_data) {
    var html = '';
  var total = 0;
  var i=1;

  html +='<table id="reportTable" class="table table-bordered">';
  html +='<thead>';
  html +='<tr>';
  html +='<th>#</th>';
  html +='<th>Date</th>';
  html +='<th>View</th>';
  html +='</thead>';
  html +='<tbody>';
  for(var key in chr_data){

  html += '<tr>'
      html += '<td>'+i+'</td>';
      html += '<td>'+chr_data[key].made_date+'</td>';
      html += '<td><a href="#" onclick="view_chronicles('+chr_data[key].id+')" data-target="#view_chronicles"  data-toggle="modal" id="view_button'+i+'" class="btn btn-primary">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></td>';
      i++;
 }
  html +='</tbody>';
  html +='</table>';
  return html;
}

function view_chronicles(id) {
  $.ajax({
    url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_chronicles_by_id'); ?>',
    type: 'post',
    data: {
      'id': id,
    },
    success: function(data) {
      var resData = $.parseJSON(data);
      $('#chronicles-detail').html(construct_view_chronicles(resData));
    }
  });
}

function construct_view_chronicles(resData) {
  var viewhtml = '';
  viewhtml += '<div class="content-div" style="height:350px; overflow-y: auto;""> <b> Contant : </b> <br>' +resData.chronicle_content+ '</div><br>';
  viewhtml += '<br>';
  if(resData.file_path != ''){
    viewhtml += '<b>Attachment: <b>';
    viewhtml += '<br>';
    viewhtml += '<a onclick="download_chronicles('+resData.id+')" class="btn btn-primary"><i class="fa fa-download"> </i> Download </a>';
  }

  return viewhtml;
}

function download_chronicles(rowid) {
  window.location.href = '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/download_chronicles_by_rowid/'); ?>' + rowid;
}
</script>

<style type="text/css">
.content-div {
    padding: 5px 5px;
    border: 2px solid #ccc;
    border-radius: 10px;
    word-wrap: break-word;
    max-height: 30%;
    overflow-y: scroll;
}
</style>