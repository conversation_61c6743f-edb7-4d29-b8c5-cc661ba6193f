<div class="modal" tabindex="-1" role="dialog" id="view_end_plan">
    <div class="modal-dialog" role="document" id="">
        <div class="modal-content" style="border-radius:1rem;width: 40%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View End Plan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_end_plan_data">
                <!-- <input type="text" class="minute-here form-control" disabled> -->
                <input type="text" class="minute-here form-control mb-2" disabled>
                <textarea class="data-here form-control mt-2" id="" name="" style="height: 11rem;"
                            placeholder="wanna describe ?" disabled></textarea>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="hideMainModal('add_plan', 'view_end_plan')" data-plan="End" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Edit</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $("#view_end_plan").on("shown.bs.modal", e => {
        const dataSet = e.relatedTarget.dataset;
        const minute = dataSet.minute;
        const content = dataSet.value;

        $(".data-here").val(content);
        $(".minute-here").val(minute+ " minute(s)");
    })

    function loadEndPlan() {
        $.ajax({
            url: "<?php echo site_url('academics/Lesson_plan/get_session_details') ?>",
            type: "POST",
            data: {
                session_id
            },
            success(data) {
                data = $.parseJSON(data);
                ({ session_details } = data);
                $(".minute-here").val(session_details.end_minute + " minute(s)");
                $(".data-here").val(session_details.end_plan);

            }
        })
    }
</script>