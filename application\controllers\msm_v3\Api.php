<?php

require APPPATH . 'libraries/REST_Controller.php';

class Api extends REST_Controller
{
	function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->setAcadYearID($this->settings->getSetting('academic_year_id'));
	}

	public function get_fee_collection_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$no_of_days = $this->post('no_of_days');

		$to_date =  date('Y-m-d');
		if ($no_of_days == '7'){
			$from_date = date('Y-m-d', strtotime('-7 days'));			
			$yesterday = date('Y-m-d', strtotime('-1 days'));
		}
		else{
			$from_date = date('Y-m-d', strtotime('-30 days'));
		}
		$data = $this->todays_feed_model->get_fee_collection_tf($from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_collection_cache_post(){
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$data = $this->todays_feed_model->get_fee_collection_cache();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_attendance_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$from_date = $this->post('from_date');

		$data = $this->todays_feed_model->get_staff_attendance_tf($from_date);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_enquiry_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$from_date = date('Y-m-d', strtotime('-7 days'));		
		$yesterday = date('Y-m-d', strtotime('-1 days'));
		$to_date =  date('Y-m-d');

		$data = $this->todays_feed_model->leads_enquiry_tf($school_code, $acad_year, $from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_leads_collection_cache_post(){
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$acad_year = $this->post('acad_year');

		$data = $this->todays_feed_model->get_leads_collection_cache($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_parent_ticketing_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');

		$from_date = date('Y-m-d', strtotime('-7 days'));		
		$yesterday = date('Y-m-d', strtotime('-1 days'));
		$to_date =  date('Y-m-d');

		$data = $this->todays_feed_model->get_parent_ticketing_tf($from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_parent_collection_cache_post(){
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$data = $this->todays_feed_model->get_parent_collection_cache();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_count_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		
		$from_date = date('Y-m-d', strtotime('-7 days'));		
		$yesterday = date('Y-m-d', strtotime('-1 days'));
		$to_date =  date('Y-m-d');
		$acad_year = $this->post('acad_year');


		$data = $this->todays_feed_model->get_infirmary_count_tf($acad_year, $from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_collection_cache_post(){
		$acad_year = $this->post('acad_year');

		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$data = $this->todays_feed_model->get_infirmary_collection_cache($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_application_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');
		$from_date = date('Y-m-d', strtotime('-7 days'));		
		$yesterday = date('Y-m-d', strtotime('-1 days'));
		$to_date =  date('Y-m-d');

		$data = $this->todays_feed_model->admission_application_tf($school_code, $acad_year, $from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_admission_collection_cache_post(){
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$data = $this->todays_feed_model->get_admission_collection_cache($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_internal_ticketing_activity_tf_post() {
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');

		$from_date = date('Y-m-d', strtotime('-7 days'));		
		$yesterday = date('Y-m-d', strtotime('-1 days'));
		$to_date =  date('Y-m-d');

		$data = $this->todays_feed_model->get_internal_ticketing_activity_tf($from_date, $yesterday, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_internal_collection_cache_post(){
		$this->load->model('msm_v3/todays_feed_model','todays_feed_model');
		$data = $this->todays_feed_model->get_internal_collection_cache();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_msm_landing_page_data_cache_post(){
		$this->load->model('msm_v3/overview_model','overview');

		$acad_year = $this->post('acad_year');
		$school_code = $this->post('school_code');
		$data = $this->overview->get_msm_landing_page_data($acad_year, $school_code);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_admission_pipeline_data_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');
		$data = $this->admission_model->get_admission_pipeline_data($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_admission_statistics_data_post() {
		$this->load->model('msm_v3/admission_model','admission_model');

		$acad_year = $this->post('acad_year');
		$school_code = $this->post('school_code');
		$data = $this->admission_model->get_admission_statistics_data($acad_year, $school_code);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function target_vs_converted_application_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->target_vs_converted_application($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_application_trend_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');
		$date_type = $this->post('date_type');

		$data = $this->admission_model->admission_application_trend($school_code, $acad_year, $date_type);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_activity_trend_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->admission_activity_trend($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function admission_statuswise_data_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');
		$data = $this->admission_model->admission_statuswise_data($school_code, $acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_tc_list_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->student_tc_list($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_conversion_statistics_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->leads_conversion_statistics($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_enquiry_trend_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');
		$date_type = $this->post('date_type');

		$data = $this->admission_model->leads_enquiry_trend($school_code, $acad_year, $date_type);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_enquiry_comparision_trend_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->leads_enquiry_comparision_trend($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_activity_trend_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$school_code = $this->post('school_code');
		$acad_year = $this->post('acad_year');

		$data = $this->admission_model->leads_activity_trend($school_code, $acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function leads_statuswise_data_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$acad_year = $this->post('acad_year');
		$data = $this->admission_model->leads_statuswise_data($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

		public function leads_sourcewise_data_post() {
		$this->load->model('msm_v3/admission_model','admission_model');
		$acad_year = $this->post('acad_year');
		$data = $this->admission_model->leads_sourcewise_data($acad_year);
		
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_management_post() {
		$this->load->model('msm_v3/fees_model','fees_model');
		$acad_year = $this->post('acad_year');

		$data = $this->fees_model->get_fee_management($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_collection_post() {
		$this->load->model('msm_v3/fees_model','fees_model');
		$no_of_days = $this->post('no_of_days');

		$to_date =  date('Y-m-d');
		if ($no_of_days == '7')
			$from_date = date('Y-m-d', strtotime('-6 days'));
		else
			$from_date = date('Y-m-d', strtotime('-30 days'));

		$data = $this->fees_model->get_fee_collection($from_date, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_payment_statistics_post() {
		$this->load->model('msm_v3/fees_model','fees_model');
		$acad_year = $this->post('acad_year');

		$data = $this->fees_model->get_fee_payment_statistics($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_monthwise_data_post() {
		$this->load->model('msm_v3/fees_model','fees');
		$current_year = $this->post('current_year');
		$prev_year = $this->post('prev_year');

		$data['previous'] = $this->fees->get_fee_monthwise_data($prev_year);
		$data['current'] = $this->fees->get_fee_monthwise_data($current_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_prediction_collection_data_post() {
		$this->load->model('msm_v3/fees_model','fees');
		$current_year = $this->post('current_year');
		$data['current'] = $this->fees->get_fee_monthwise_data($current_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_blueprint_for_year_post(){
		$this->load->model('msm_v3/fees_model','fees');
		$acad_year = $this->post('acad_year');

		$data = $this->fees->get_blueprint_for_year($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_collection_bpwise_post() {
		$this->load->model('msm_v3/fees_model','fees');
		$bp_id = $this->post('bp_id');

		$data = $this->fees->get_fee_collection_bpwise($bp_id);
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_fee_collection_student_wise_post() {
		$this->load->model('msm_v3/fees_model','fees');
		$acad_year = $this->post('acad_year');
		$bp_id = $this->post('bp_id');
		$fps_bool = $this->post('fps_bool');

		$data = $this->fees->get_fee_collection_student_wise($acad_year, $bp_id, $fps_bool);
        $this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_data($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function get_student_non_compliance_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_non_compliance_data($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_observation_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_observation_data($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_counselling_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_counselling_data($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_counselling_statuswise_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_counselling_statuswise_data($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_nationalitywise_data_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->get_student_nationalitywise_data($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function student_birthday_list_post() {
		$this->load->model('msm_v3/student_model','student_model');
		$acad_year = $this->post('acad_year');

		$data = $this->student_model->student_birthday_list($acad_year);

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_attendance_trend_data_post() {
		$this->load->model('msm_v3/staff_model','staff_model');

		$data = $this->staff_model->get_staff_attendance_trend_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function get_staff_attendance_data_post() {
		$this->load->model('msm_v3/staff_model','staff_model');
		$from_date = $this->post('from_date');

		$data = $this->staff_model->get_staff_attendance_data($from_date);
		// print_r($data);die();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_gender_data_post() {
		$this->load->model('msm_v3/staff_model','staff_model');

		$data = $this->staff_model->get_staff_gender_data();

		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_qualification_data_post() {
		$this->load->model('msm_v3/staff_model','staff_model');

		$data = $this->staff_model->get_staff_qualification_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_attrition_data_post() {
		$this->load->model('msm_v3/staff_model','staff_model');
		$acad_year = $this->post('acad_year');

		$data = $this->staff_model->get_staff_attrition_data($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function staff_birthday_list_post() {
		$this->load->model('msm_v3/staff_model','staff_model');
		$data = $this->staff_model->staff_birthday_list();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_parent_ticketing_trend_data_post() {
		$this->load->model('msm_v3/parent_model','parent');

		$data = $this->parent->get_parent_ticketing_trend_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}	

	public function get_parent_ticketing_statistics_data_post() {
		$this->load->model('msm_v3/parent_model','parent');
		$acad_year = $this->post('acad_year');

		$data = $this->parent->get_parent_ticketing_statistics_data($acad_year);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_parent_enquiry_trend_data_post() {
		$this->load->model('msm_v3/parent_model','parent');

		$data = $this->parent->get_parent_enquiry_trend_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_parent_activity_trend_data_post() {
		$this->load->model('msm_v3/parent_model','parent');

		$data = $this->parent->get_parent_activity_trend_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_count_data_post() {
		$this->load->model('msm_v3/infirmary_model','infirmary_model');
		if($this->post('date_type') == 'week'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-6 days'));
		}
		else if($this->post('date_type') == 'month'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-1 month', strtotime($to_date)));
		}
		else if($this->post('date_type') == 'year'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-1 year', strtotime($to_date)));
		}
		else if($this->post('date_type') == 'fiveyear'){
			$to_date =  date('Y-m-d');
			$from_date = date('Y-m-d', strtotime('-5 year', strtotime($to_date)));
		}
		$data = $this->infirmary_model->get_infirmary_count_data($from_date, $to_date);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_statistics_data_post() {
		$this->load->model('msm_v3/infirmary_model','infirmary_model');

		$data = $this->infirmary_model->get_infirmary_statistics_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_infirmary_monthwise_statistics_post() {
		$this->load->model('msm_v3/infirmary_model','infirmary_model');

		$data = $this->infirmary_model->get_infirmary_monthwise_statistics();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_internal_ticketing_activity_data_post() {
		$this->load->model('msm_v3/internal_model','internal_ticketing');

		$data = $this->internal_ticketing->get_internal_ticketing_activity_data();
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_student_boarding_count_data_post() {
		$this->load->model('msm_v3/boarding_model','boarding_model');

		$data = $this->boarding_model->get_student_boarding_count_data();
		$boarding_type = $this->settings->getSetting('boarding');
		$boarding_type = array_slice($boarding_type, 1);
		$responseData = array(
			'data' => $data,
			'boarding_type' => $boarding_type
		);
		
		$this->response($responseData, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_boarding_statistics_data_post() {
		$this->load->model('msm_v3/boarding_model','boarding_model');
		$data = $this->boarding_model->get_boarding_statistics_data();
	
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_boarding_nationalitywise_data_post() {
		$this->load->model('msm_v3/boarding_model','boarding_model');
		$data = $this->boarding_model->get_boarding_nationalitywise_data();
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_from_school_staffs_post(){
		$this->load->model('msm_v3/staff_model','staff_model');
		$data = $this->staff_model->get_from_school_staffs();
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_from_staff_data_post(){
		$this->load->model('msm_v3/staff_model','staff_model');
		$staff_id = $this->post('staff_id');

		$data = $this->staff_model->get_from_staff_data($staff_id);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function insert_staff_master_post(){
		$this->load->model('msm_v3/staff_model','staff_model');
		$data = $this->post('data');
		$user_data = $this->post('user_data');

		$data = $this->staff_model->insert_staff_master($data, $user_data);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function insert_staff_tables_post(){
		$this->load->model('msm_v3/staff_model','staff_model');
		$data = $this->post('data');
		$table = $this->post('table');

		$data = $this->staff_model->insert_staff_tables($data, $table);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_staff_leaves_approve_data_post(){
		$this->load->model('msm_v3/staff_leave_model','staff_leave_model');

		$staff_id = $this->post('staff_id');
		$status_id = $this->post('status_id');

		$data = $this->staff_leave_model->getLeavesData_3level($staff_id, $status_id);
		
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

    public function cancelLeave_3level() {
		$institution=$this->post("institution");
        $leave_id = $this->post('leave_id');
        $reason = $this->post('reason');

        $leave=$this->staff_leave->get_leave_final_status($institution, $leave_id);

        if($leave->final_status!=3 && $leave->final_status!=4){
            echo $this->staff_leave->cancelLeave($institution, $leave_id, $reason);
        }else{
            echo 0;
        }
    }

	public function staffLeave3Level_post() {
        $leave_id = $this->post('leave_id');

		$this->load->model('msm_v3/staff_leave_model','staff_leave');

        $leave=$this->staff_leave->get_leave_final_status($leave_id);
        if($leave->final_status!=3 && $leave->final_status!=4){
            $data = $this->staff_leave->getLeaveData($leave_id);
            
			$this->response($data, REST_Controller::HTTP_OK);
			// $this->output->_display();
        }else{
            echo 0;
        }
    }

	public function getLeavesHistory_post() {
        $staff_id = $this->post('staff_id');
		$this->load->model('msm_v3/staff_leave_model','staff_leave');

        $leave_year = $this->staff_leave->getLeaveYear();
        $data = $this->staff_leave->getStaffLeaveDetails($staff_id, $leave_year->start_date, $leave_year->end_date);
        // echo json_encode($leaves);
		$this->response($data, REST_Controller::HTTP_OK);
		// $this->output->_display();
        // echo "<pre>"; print_r($leaves); die();
    }

	public function getLeavesStatistics_post() {
        $staff_id = $this->post('staff_id');
		
		$this->load->model('msm_v3/staff_leave_model','staff_leave');

        $leave_year = $this->staff_leave->getLeaveYear();

        // echo "<pre>"; print_r($leave_year); die();
        $data = $this->staff_leave->getLeavesStatistics($staff_id,$leave_year->id);
        // echo json_encode($leaves);
		$this->response($data, REST_Controller::HTTP_OK);
		// $this->output->_display();
    }

	public function saveLeaveStatus_3level_post() {
        $approval_mode = $this->post('approval_mode');
        $leave_id = $this->post('leave_id');

		$this->load->model('msm_v3/staff_leave_model','staff_leave');

        $isLeaveExists=$this->staff_leave->get_leave_final_status($leave_id);

        if($isLeaveExists->final_status!=3 && $isLeaveExists->final_status!=4){        
            $status = $this->post('radio_status');
            $description = $this->post('description');
            $staff_name = $this->post('staff_name');
            $approved_status_type = $this->post('status_type');
            $approved_status_2 = $this->post('approve_status_2');
            $approved_status_3 = $this->post('approve_status_3');
            $response = $this->staff_leave->saveLeaveStatus_3level($leave_id, $status, $description,$approved_status_type,$approved_status_2,$approved_status_3,$approval_mode);
			// $this->output->_display();
            // if($response) {
            //     $leave = $this->staff_leave->getLeaveData_3level($leave_id);
            //     $reporting_manager_name = $this->staff_leave->get_reporting_manager_name_by_status_type($approved_status_type,$leave->staff_id,$leave_id);
            
            //     $approve_status = ($status == 1)?'Approved':'Rejected';
            //     $message = "Leave by $staff_name";
            //     // $message = "Leave $approve_status, for application of leave by ".$staff_name;
            //     if($leave->from_date == $leave->to_date) {
            //         $message .= ' On '.$leave->from_date;
            //     } else {
            //         $message .= ' From '.$leave->from_date.' to '.$leave->to_date;
            //     }
            //     $message .= " is  $approve_status by $reporting_manager_name->staff_name";
            //     $this->load->model('role');
            //     $admins = $this->role->getStaffListByPrivilege('LEAVE', 'STAFF_LEAVE_ADMIN');
            //     $staff_ids = $admins;
                
            //     $rep_manager_id = $this->staff_leave->get_reporting_manager_id($leave->staff_id);
                
            //     $staff_ids = $admins;
                
            //     foreach($rep_manager_id  as $id) {
            //         if(!empty($id)) {
            //             $staff_ids[] = $id;
            //         }
            //     }
            //     $staff_ids[] = $leave->staff_id;
            //     if(!empty($staff_ids)) {
            //         $this->load->helper('texting_helper');
            //         $input_arr = array();
            //         $input_arr['staff_ids'] = $staff_ids;
            //         $input_arr['mode'] = 'notification';
            //         $input_arr['source'] = 'Staff Leave';
            //         $input_arr['message'] = $message;
            //         sendText($input_arr);
            //     }

            //     // sending email
            //     // $email_body=$message;
            //     // $this->sendStaffLeaveEmail($staff_ids,$message);
            //     // sent email
            // }
            $data=$response;
        }else{
            $data=0;
        }
        // // echo "<pre>"; print_r($data); die();

        // echo $data;
		$this->response($data, REST_Controller::HTTP_OK);
		// $this->output->_display();
    }

	public function sendStaffLeaveEmail($staff_ids,$email_body){
        $this->load->helper('email_helper');
        $this->load->model('communication/emails_model');

        $from_email = $this->settings->getSetting('leave_application_from_email');
        if (empty($from_email)) {
            return false;
            // $from_email = "<EMAIL>";
        }

        $sender_list = [];
        $emaildata = array();
        $leave_approvers = $this->staff_leave->get_leave_approvers($staff_ids);
        $email_ids = [];

        if (!empty($leave_approvers)) {
            foreach ($leave_approvers as $key => $val) {
                $sender_list['staff'] = [
                    'send_to_type' => 'Staff',
                    'id' => $val->staff_id,
                ];

                $object = new stdclass();
                $object->id = $val->staff_id;
                $object->email = $val->email;
                $object->avatar_type = '4';
                array_push($emaildata, $object);

                if ($val->email) {
                    array_push($email_ids, $val->email);
                }
            }
        }

        $email_master_data = array(
            'subject' => 'Leave application',
            'body' => $email_body,
            'source' => 'Staff Leave',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => 'Staff',
            'from_email' => $from_email,
            'files' => '',
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' => empty($sender_list) ? NULL : json_encode($sender_list),
            'sending_status' => 'Send Email'
        );

        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_data((object) $emaildata, $email_master_id);
        $email = $this->emails_model->getEmailInfo($email_master_id);
        return sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
    }

	public function get_idcard_order_details_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$data = $this->idcards_model->getOrderDetails($order_id);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_idcard_entities_for_order_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$id_card_for = $this->post('id_card_type');
		$data = $this->idcards_model->getAllEntitiesForTheSpecificOrder($order_id, $id_card_for);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function get_overall_idcard_orders_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$school_id = $this->post('school_id');
		$school_code = $this->post('school_code');
		$data = $this->idcards_model->get_submitted_idcards_orders($school_id,$school_code);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function update_invoice_payment_status_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$payment_status = $this->post('payment_status');
		$data = $this->idcards_model->update_payment_status_post($order_id,$payment_status);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function update_in_printing_status_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$status = $this->post('status');
		$estimated_time_of_completion_for_printing = $this->post('estimated_time_of_completion_for_printing');
		$data = $this->idcards_model->update_in_printing_status_post($order_id,$status,$estimated_time_of_completion_for_printing);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function update_in_delivery_status_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$status = $this->post('status');
		$estimated_time_of_completion_for_delivery = $this->post('estimated_time_of_completion_for_delivery');
		$data = $this->idcards_model->update_in_delivery_status_post($order_id,$status,$estimated_time_of_completion_for_delivery);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

	public function update_delivered_status_post(){
		$this->load->model('idcards/Idcards_model','idcards_model');
		$order_id = $this->post('order_id');
		$status = $this->post('status');
		$data = $this->idcards_model->update_delivered_status_post($order_id,$status);
		$this->response($data, REST_Controller::HTTP_OK);
		$this->output->_display();
		exit;
	}

}
?>
