<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/payment_voucher_controller/payment_voucher_dashboard'); ?>">Payment Voucher Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/payment_voucher_controller/manage_payment_vouchers'); ?>">Manage Payment Vouchers</a></li>
    <li>View Payment Voucher</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/payment_voucher_controller/manage_payment_vouchers') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        View Payment Voucher
                    </h3>
                    <span class="pull-right card-title panel_title_new_style_staff" style="text-align: right;"><?php echo !empty($paDetails->voucher_status) ? $paDetails->voucher_status : 'N/A'; ?></span>
                </div>
            </div>
        </div>
        <div class="col-md-12 panel-body">
            <input type="hidden" id="voucher_master_id" value="<?php echo $voucher_master_id; ?>">
            <input type="hidden" id="loggenIn_userId" value="<?php echo $loggenIn_userId; ?>">
            <?php
                $status_approvers_array= array('Pending');
                $voucher_status= $paDetails->voucher_status;
                $canStaffApprove= in_array($voucher_status, $status_approvers_array);
            ?>


            <section class="details-history-section mb-4">
                <div class="">
                    <div class="row" style="margin: auto 0rem;">
                        <div class="col-md-12 mb-3">
                            <div class="col-md-12">
                                <h5 class="font-weight-bold" style="margin-left: -10px;">Details</h5>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>Created By:</strong>
                                        <?php echo !empty($paDetails->staff) ? $paDetails->staff : 'N/A'; ?>
                                    </p>
                                    <p><strong>Voucher Number:</strong>
                                        <?php echo !empty($paDetails->voucher_number) ? $paDetails->voucher_number : 'N/A'; ?>
                                    </p>
                                    <p><strong>Supplier:</strong>
                                        <?php echo !empty($paDetails->vendor_name) ? $paDetails->vendor_name : 'N/A'; ?> - <?php echo !empty($paDetails->vendor_code) ? $paDetails->vendor_code : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Created On:</strong>
                                        <?php echo !empty($paDetails->voucher_date) ? date('d M Y', strtotime($paDetails->voucher_date)) : 'N/A'; ?>
                                    </p>
                                    <p><strong>Voucher Type:</strong>
                                        <?php echo !empty($paDetails->voucher_type) ? $paDetails->voucher_type : 'N/A'; ?>
                                    </p>
                                    <p><strong>Voucher Date:</strong>
                                        <?php echo !empty($paDetails->voucher_date) ? $paDetails->voucher_date : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Voucher Status:</strong>
                                        <?php echo !empty($paDetails->voucher_status) ? $paDetails->voucher_status : 'N/A'; ?>
                                    </p>
                                    <p><strong>Narration:</strong>
                                        <?php echo !empty($paDetails->remarks) ? $paDetails->remarks : 'N/A'; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <!-- Invoices -->
             <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Invoices Details</h5>
                    <div class="table-responsive">
                        <?php
                            if(!empty($paInvoices)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Invoice Number</th>
                                            <th>Invoice For</th>
                                            <th>Narration</th>
                                            <th>Type</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($paInvoices as $key => $val) {
                                    $site_url= site_url('procurement/invoice_controller_v2/view_invoice/'.$val->invoice_id);
                                    $num= intval($key) + 1;
                                    $invoice_for= $val->delivery_challan_type == 'GDC' ? 'Goods' : 'Services';
                                    $narration= trim($val->narration) != '' ? $val->narration : '-';
                                    echo "
                                        <tr>
                                            <td><a class='' href='$site_url' target='_blank'>$val->invoice_number</a></td>
                                            <td>$invoice_for</td>
                                            <td>$narration</td>
                                            <td>$val->invoice_type</td>
                                            <td>$val->invoice_date</td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Invoices not added.</p>";
                            }
                        ?>
                    </div>
            </section>



            <!-- Attachments -->
             <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Additional Attachements</h5>
                    <div class="table-responsive">
                        <?php
                            if(!empty($paAttachments)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Type</th>
                                            <th>Document</th>
                                            <th>Narration</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($paAttachments as $key => $val) {
                                    $download_url= site_url('procurement/payment_voucher_controller/download_voucher_attachement/'.$val->voucher_attachements_id);
                                    $num= intval($key) + 1;
                                    echo "
                                        <tr>
                                            <td>$val->document_type</td>
                                            <td><a class='' href='$val->url' target='_blank'>$val->name</a></td>
                                            <td>$val->remarks</td>
                                            <td>
                                           



                                            <div class='dropdown float-right'>
                                                <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown' style='font-size: 24px;'>⋮</div>
                                                <div class='dropdown-menu'>
                                                     <a class='dropdown-item' href='$val->url' target='_blank'>View</a>
                                                    <a class='dropdown-item' href='$download_url' target='_blank'>Download</a>
                                                </div>
                                            </div>



                                            </td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Attachements not added.</p>";
                            }
                        ?>
                    </div>
            </section>


            <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">Voucher Approvers</h5>
                    <div class="table-responsive">
                        <?php
                            if(!empty($paApprovers)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Approver Type</th>
                                            <th>Approver Name</th>
                                            <th>Department</th>
                                            <th>Status </th>
                                            <th>Narration</th>
                                            <th>-</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($paApprovers as $key => $val) {
                                    $isFinallyApproed= intval($key) + 1 == count($paApprovers) ? '1' : '0';
                                    $status_styled= "<span class=''>$val->approval_status</span>";
                                    if($val->approval_status== 'Sent for Modification' && $invoice_status == 'Pending') {
                                        $status_styled= "<span class='text-warning'>$val->approval_status</span><br><span class='text-success'>Modification done.</span>";
                                    } else if($val->approval_status== 'Approved') {
                                        $status_styled= "<span class='text-success'>$val->approval_status</span>";
                                    } else if($val->approval_status== 'Rejected') {
                                        $status_styled= "<span class='text-danger'>$val->approval_status</span>";
                                    } else if($val->approval_status== 'Pending') {
                                        $status_styled= "<span class='text-primary'>$val->approval_status</span>";
                                    } else if($val->approval_status== 'Sent for Modification') {
                                        $status_styled= "<span class='text-warning'>$val->approval_status</span>";
                                    }
                                    $num= intval($key) + 1;
                                    $apr_html= "<span style='font-style: italic; color: lightgray;'>Not in approval state</span>";

                                    if($key == 0 && $canStaffApprove && $val->approval_status != 'Approved') {
                                        $apr_html= "
                                                <div class='dropdown float-right'>
                                                    <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown' style='font-size: 24px;'>⋮</div>
                                                    <div class='dropdown-menu'>
                                                       <button onclick='voucher_reject(`$val->staff_id`, `$val->voucher_approvals_id`, `$val->type`, `$val->staff`, `$val->approval_status`)' class='dropdown-item'>Reject</button>
                                                       <button onclick='voucher_approve(`$val->staff_id`, `$val->voucher_approvals_id`, `$val->type`, `$val->staff`, `$val->approval_status`, `$isFinallyApproed`)' class='dropdown-item'>Approve</button>
                                                    </div>
                                                </div>
                                                ";
                                    } else if($key != 0 && $canStaffApprove && $paApprovers[$key-1]->approval_status == 'Approved' && $val->approval_status != 'Approved') {
                                        $apr_html= "
                                                <div class='dropdown float-right'>
                                                    <div style='cursor: pointer; font-size: 18px;' data-toggle='dropdown' style='font-size: 24px;'>⋮</div>
                                                    <div class='dropdown-menu'>
                                                       <button onclick='voucher_reject(`$val->staff_id`, `$val->voucher_approvals_id`, `$val->type`, `$val->staff`, `$val->approval_status`)' class='dropdown-item'>Reject</button>
                                                       <button onclick='voucher_approve(`$val->staff_id`, `$val->voucher_approvals_id`, `$val->type`, `$val->staff`, `$val->approval_status`, `$isFinallyApproed`)' class='dropdown-item'>Approve</button>
                                                    </div>
                                                </div>
                                                ";
                                    }

                                    echo "
                                        <tr>
                                            <td>$val->type</td>
                                            <td>$val->staff</td>
                                            <td>$val->department</td>
                                            <td>$val->approval_status</td>
                                            <td>$val->approval_comments</td>
                                            <td>
                                            
                                                $apr_html
                                                
                                            </td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>Approvers not added.</p>";
                            }
                        ?>
                    </div>
            </section>



            <!-- Hstory -->
             <section class="product-section mb-4">
                <h5 class="font-weight-bold mb-3">History</h5>
                    <div class="table-responsive">
                        <?php
                            if(!empty($paHistory)) {
                                echo "
                                <table class='table table-bordered '>
                                    <thead class='thead-dark'>
                                        <tr>
                                            <th>Action Type</th>
                                            <th>Narration</th>
                                            <th>Action By</th>
                                            <th>Action Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                ";
                                foreach($paHistory as $key => $val) {
                                    echo "
                                        <tr>
                                            <td>$val->action_type</td>
                                            <td>$val->action_description</td>
                                            <td>$val->staff</td>
                                            <td>$val->action_on</td>
                                        </tr>
                                        ";
                                }
                                echo "</tbody>
                                    </table>";
                            } else {
                                echo "<p class='no-data-display'>History not added.</p>";
                            }
                        ?>
                    </div>
            </section>



        </div>
    </div>
</div>



<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    function voucher_reject(staff_id, voucher_approvals_id, type, staff_name, approval_status) {
        let loggenIn_userId= $("#loggenIn_userId").val();
        if(Number(staff_id) !== Number(loggenIn_userId)) {
            __log_failed_attempt(staff_id, voucher_approvals_id, type, staff_name, approval_status, 'Reject');
            return Swal.fire({
                icon: 'warning',
                title: 'Access Denied',
                text: `You are not authorized to perform this action. Please note that unauthorized access attempts are logged and may be subject to disciplinary action.`
            }).then((res) => {
                window.location.reload();
            });
        }
        Swal.fire({
            icon: 'question',
            title: 'Reject',
            html: `<p>Are you sure you want to reject the voucher?<p><textarea class="form-control" id="rejection_remarks" placeholder="Reason for reject" rows="5"></textarea>`,
            showCancelButton: true,
            cancelButtonText: 'Cancel',
            showConfirmButton: true,
            confirmButtonText: 'Reject'
        }).then((result) => {
            if(result.isConfirmed) {
                let rejection_remarks= $("#rejection_remarks").val();
                __reject_the_vochers(staff_id, voucher_approvals_id, type, staff_name, approval_status, rejection_remarks);
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Cancelled'
                });
            }
        });
    }

    function __reject_the_vochers(staff_id, voucher_approvals_id, type, staff_name, approval_status, rejection_remarks) {
        let voucher_master_id= $("#voucher_master_id").val();
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/reject_the_voucher'); ?>',
            type: 'post',
            data: {staff_id, voucher_approvals_id, type, staff_name, approval_status, rejection_remarks, voucher_master_id},
            success: function(payload) {
                payload= JSON.parse(payload);
                if(payload) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: `The status has been changed from ${approval_status} to Rejected.`
                    }).then((res) => {
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong.'
                    });
                }
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    function voucher_approve(staff_id, voucher_approvals_id, type, staff_name, approval_status, isFinallyApproed) {
        let voucher_master_id= $("#voucher_master_id").val();
        let loggenIn_userId= $("#loggenIn_userId").val();
        if(Number(staff_id) !== Number(loggenIn_userId)) {
            __log_failed_attempt(staff_id, voucher_approvals_id, type, staff_name, approval_status, 'Approve');
            return Swal.fire({
                icon: 'warning',
                title: 'Access Denied',
                text: `You are not authorized to perform this action. Please note that unauthorized access attempts are logged and may be subject to disciplinary action.`
            }).then((res) => {
                window.location.reload();
            });
        }
        Swal.fire({
            icon: 'question',
            title: 'Approve',
            html: `<p>Are you sure you want to approve the voucher?<p><textarea class="form-control" id="approved_remarks" placeholder="Reason for Approve" rows="5"></textarea>`,
            showCancelButton: true,
            cancelButtonText: 'Cancel',
            showConfirmButton: true,
            confirmButtonText: 'Approve'
        }).then((result) => {
            if(result.isConfirmed) {
                let approved_remarks= $("#approved_remarks").val();
                __approve_the_vochers(staff_id, voucher_approvals_id, type, staff_name, approval_status, approved_remarks, isFinallyApproed);
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Cancelled'
                });
            }
        });
    }

    function __approve_the_vochers(staff_id, voucher_approvals_id, type, staff_name, approval_status, approved_remarks, isFinallyApproed) {
        let voucher_master_id= $("#voucher_master_id").val();
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/approve_the_voucher'); ?>',
            type: 'post',
            data: {staff_id, voucher_approvals_id, type, staff_name, approval_status, approved_remarks, voucher_master_id, isFinallyApproed},
            success: function(payload) {
                payload= JSON.parse(payload);
                if(payload) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: `The status has been changed from ${approval_status} to Approved.`
                    }).then((res) => {
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong.'
                    });
                }
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    function __log_failed_attempt(staff_id, voucher_approvals_id, type, staff_name, approval_status, approval_type) {
        let voucher_master_id= $("#voucher_master_id").val();
        let loggenIn_userId= $("#loggenIn_userId").val();
        $.ajax({
            url: '<?php echo site_url('procurement/payment_voucher_controller/log_failed_attempt'); ?>',
            type: 'post',
            data: {loggenIn_userId, type, staff_name, approval_type, voucher_master_id},
            success: function(payload) {
                console.warn('Activity logged.');
            },
            error: function(e) {
                console.log(e);
            }
        });
    }
</script>