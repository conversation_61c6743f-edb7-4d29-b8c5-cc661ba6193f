<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  09 May 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee_Master
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Fees_fast_collection extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isAuthorized('FEESV2.COLLECT_FEES')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/fees_fast_collection_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('avatar');
    $this->load->library('Payment_settlement');
    $this->load->library('fee_library');
    $this->load->library('filemanager');
    $this->load->model('parent_model');
  }


  /**
   * Landing function for Fee Collection
   */
  public function index () {
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/transaction2/index';
    $this->load->view('inc/template', $data);
  }

  public function search_class_student(){
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'admission_no':
        $admission_no = $_POST['admission_no'];
        $stdData = $this->fees_fast_collection_model->get_fee_all_std_data($admission_no, 0);
        break;
      case 'class':
        $classId = $_POST['classId'];
        $stdData = $this->Student_Model->getstdDataByClass($classId, 2);
        break;
      case 'student':
        $student_id = $_POST['student_id'];
        $stdData = $this->fees_fast_collection_model->get_fee_all_std_data(0,$student_id);
        break;
    }
    echo json_encode($stdData);
  }

  public function get_installments_data(){
    $fee_student_schedule_id = $_POST['fee_student_schedule_id'];
    $fsiId = $_POST['fsiId'];
    $fee_amount = $this->fees_fast_collection_model->get_std_fee_amount_blueprint_installmentwise($fee_student_schedule_id,$fsiId);
    $no_of_comp = $this->fees_fast_collection_model->get_std_fee_amount_blueprint_components($fee_student_schedule_id, $fsiId);
    $no_of_ins = count($fsiId);
    echo json_encode(array('fee_amount'=>$fee_amount, 'no_of_ins'=>$no_of_ins, 'no_of_comp'=>$no_of_comp));
  }

  // public function insert_fast_collection_fee_data(){

  //   $input = $this->input->post();
  //   $fTransids =[];
  //   foreach ($input['fsicompId'] as $stdSchId => $val) {
  //     if (!array_key_exists($stdSchId, $input['fee_blueprint'])) {
  //       continue;
  //     }
  //   $this->db->trans_begin();
  //     $inputData = array();
  //     $inputData['transaction_mode'] = $input['transaction_mode'];
  //     $inputData['student_id'] = $input['student'];
  //     $inputData['receipt_date'] = $input['receipt_date'];
  //     $inputData['fee_student_schedule_id'] = $stdSchId;
  //     $inputData['blueprint_id'] = $input['blueprint_id'][$stdSchId];
  //     $inputData['cohort_student_id'] = $input['cohort_student_id'][$stdSchId];
  //     $inputData['fsicompId'] = $val;
  //     $inputData['fsInsId'] = $input['fsInsId'][$stdSchId];
  //     $inputData['conc_amount'] = $input['conc_amount'][$stdSchId];
  //     $inputData['pay_amount'] = $input['pay_amount'][$stdSchId];
  //     $inputData['payment_type'] = $input['payment_type'];
  //     $inputData['total_amount'] = $input['final_amount'][$stdSchId];
  //     $inputData['discount_amount'] =  '';
  //     $inputData['fine_amount'] = '';

  //     list($mode, $modevalue) = explode('_', $input['payment_type']);
  //     $inputData['cheque_dd_nb_cc_dd_number'] = '';
  //     $inputData['dd_number'] = '';
  //     $inputData['cc_number'] = '';
  //     $inputData['nb_number'] = '';
  //     $inputData['bank_name'] = '';
  //     $inputData['branch_name'] = '';
  //     $inputData['bank_date'] = '';
  //     $inputData['remarks'] = '';
  //     $inputData['card_reference_no'] = '';
      
  //     if($mode != 9) { 
  //       if ($mode == 4 || $mode == 1 || $mode == 6) {
  //         $inputData['bank_name'] = $input['bank_name'];
  //         $inputData['branch_name'] = $input['branch_name'];
  //         $inputData['bank_date'] = $input['bank_date'];
  //         if($mode == 1) {
  //           $inputData['dd_number'] = $input['dd_number'];
  //         } else {
  //           $inputData['cheque_dd_nb_cc_dd_number'] = $input['cheque_dd_nb_cc_dd_number'];
  //         }
  //       }
  //       if($mode == 8) {
  //         $inputData['nb_number'] = $input['nb_number'];
  //       } 
  //       if($mode == 7 || $mode == 2 || $mode == 3) {
  //         $inputData['cc_number'] = $input['cc_number'];
  //       }
  //       $inputData['remarks'] = $input['remarks'];
  //     }
  //     $tpayAmount = 0;
  //     foreach ($input['pay_amount'][$stdSchId] as $pAmount) {
  //       $tpayAmount += array_sum($pAmount);
  //     }
  //     $fTrans = $this->fees_collection_model->insert_fee_transcation($inputData);

  //     if (empty($fTrans)) 
  //     {
  //       $this->db->trans_rollback();
  //     }
  //     else
  //     {
  //       array_push($fTransids, $fTrans);
  //       // generate and update receipt number after transcation
  //       $this->fees_collection_model->update_receipt_transcation_wise($fTrans);
  //       // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
  //       $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
  //       if ($rconStatus->reconciliation_status == 1) {
  //         $this->db->trans_commit();
  //         $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');
  //         // redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
  //       }else{
  //         $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
  //         if (empty($result)) 
  //         {
  //           $this->db->trans_rollback();
  //         }
  //         if ($this->db->trans_status()) 
  //         {
  //           $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($input['blueprint_id'][$stdSchId]);

  //           $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

  //           if ($blue_print->enable_sms == TRUE) {
  //             $this->__sms_fees_payment_counter($data['fee_trans']->amount_paid, $data['fee_trans']->student->stdName, $data['fee_trans']->student->stdId, $blue_print->name);  
  //           }
  //           $this->db->trans_commit();
  //         }
  //         else
  //         {
  //           $this->session->set_flashdata('flashError', 'Something went wrong');
  //         }
    
  //         }
  //       } 
  //   }
    
  //   $this->session->set_flashdata('flashSuccess', 'Fee collected Successfully');
  //   // $this->_fee_fast_reciept($fTransids);
  //    $this->session->set_userdata('fTransids', $fTransids);
  //    redirect('feesv2/fees_fast_collection/fee_fast_reciept');
  // }


  public function fee_fast_reciept(){
    $fTransids = $this->session->userdata('fTransids');
    $student_id = $this->session->userdata('fees_student_id');
    
    if (empty($fTransids)) {
      redirect('feesv2/fees_collection/fee_student_blueprints_v1/'.$student_id);
    }
    $fee_trans_data = [];
    foreach ($fTransids as $key => &$fTrans) {

      $fee_trans = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
      $created_name = $this->avatar->getAvatarById($fee_trans->collected_by);

      array_push($fee_trans_data, $fee_trans);

      $this->fees_collection_model->create_pdf_template_for_fee_receipts($fTrans, '', $fee_trans->paid_datetime, '');

      $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($fee_trans->no_of_ins->feev2_blueprint_id);

      $payment_modes = json_decode($blue_print->allowed_payment_modes);
    }
    // $this->session->unset_userdata('fTransids');
    $data['fee_trans_list'] = $fee_trans_data;
    $data['student_id']= $student_id;
    $data['payment_modes'] = $payment_modes;
    $data['created_name'] = $created_name;
    $data['main_content'] = 'feesv2/receiptv1/header_fast_fees';
    $this->load->view('inc/template_fee', $data);
  }

   private function __generatefee_pdf_receipt($html, $fTrans, $receipt_for) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/fee_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->fees_collection_model->updateFeePath($fTrans, $path);
    $page = 'landscape';
    $page_size = 'a4';
    if ($receipt_for === 'ourschool_academic') {
      $page_size = 'a5';
      $page = 'portrait';
    }
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateFeePdfLink';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

  public function _create_template_fee_counter_amount($fee_trans, $template, $payment_modes, $created_name)
  {
    $medium = $this->settings->getSetting('medium')[$fee_trans->student->medium];
    $class = $fee_trans->student->classSection;
    if ($fee_trans->student->is_placeholder == 1) {
      $class = $fee_trans->student->clsName;
    }
    $template = str_replace('%%receipt_no%%',$fee_trans->receipt_number, $template);
    $template = str_replace('%%class%%',$class, $template);
    $template = str_replace('%%class_medium%%',$class.'/'.$medium, $template);
    $template = str_replace('%%transaction_id%%', 'NA', $template);
    $template = str_replace('%%transaction_date%%', date('d-m-Y', strtotime($fee_trans->paid_datetime)), $template);
    $template = str_replace('%%f_number%%',$fee_trans->student->mobile_no, $template);
    $template = str_replace('%%student_name%%', $fee_trans->student->stdName, $template);
    $template = str_replace('%%father_name%%', $fee_trans->student->fName, $template);
    $template = str_replace('%%admission_no%%', $fee_trans->student->admission_no, $template);
    $template = str_replace('%%academic_year%%', $this->acad_year->getAcadYear(), $template);

    $i=1;
    $cnt= 0; 
    $sl=0;
    $t=1;
    $totalAmount = 0;
    if ($fee_trans->no_of_ins->ins_count > 1) {
      $colspan=3;
    }else{
      $colspan=2;
    }
    $header_part = '<table>';
    $header_part .= '<tr>';
    $header_part .= '<th width="10%">#</th>';
    if ($fee_trans->no_of_ins->ins_count > 1) {
      $header_part .= '<th width="25%">Installment</th>';
    }
    $header_part .= '<th>Particulars</th>';
    $header_part .= '<th>Amount</th>';
    $header_part .= '</tr>';
    $rowspan = $fee_trans->no_of_comp->comp_count;
    $component_part = '';
    foreach ($fee_trans->comp as $key => $trans) {      
      $totalAmount += $trans->amount_paid + $trans->concession_amount;
      $component_part.='<tr>';
      if (!$sl) {
        $component_part.='<td style="vertical-align: middle;" rowspan="'.$rowspan .'">'.$i++.'</td>';
        $sl .= $rowspan;
      }
      $sl--;

      if($fee_trans->no_of_ins->ins_count >= 2){ 
      if(!$cnt) { 
        $component_part.= '<td style="vertical-align: middle;" rowspan="'.$rowspan.'" >'.$trans->insName.'</td>';
        $cnt = $rowspan; }
        $cnt--;     
      }

      $component_part.='<td>'.$trans->compName.'</td>';
      $component_part.='<td>'.($trans->amount_paid + $trans->concession_amount).'</td>';

      $component_part.='</tr>';
    }
    $without_comp = '';
    $without_comp.='<tr>';
    $without_comp.='<td>'.$i++.'</td>';     
    $without_comp.='<td>'.$fee_trans->no_of_comp->blueprint_name.'</td>';
    $without_comp.='<td>'.$totalAmount.'</td>';
    $without_comp.='</tr>';

    $footer_part = '<tr>';
    $footer_part.='<td colspan="'.$colspan.'" style="text-align: right;">Total Fee</td>';
    $footer_part.='<td>'.$totalAmount.'</td>'; 
    $footer_part.='</tr>';

    if($fee_trans->concession_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Concession (-)</td>';
      $footer_part.='<td>'.$fee_trans->concession_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->discount_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Discount (-)</td>';
      $footer_part.='<td>'.$fee_trans->discount_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->fine_amount != 0) {
      $footer_part.='<tr>';
      $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;">Fine Amount</td>';
      $footer_part.='<td>'.$fee_trans->fine_amount.'</td>';
      $footer_part.='</tr>';
    }

    if($fee_trans->card_charge_amount != 0) {
      $footer_partfooter_part.='<tr>';
      $footer_partfooter_part.='<td colspan="'.$colspan.'" style="text-align:right;">Card Charge Amount</td>';
      $footer_partfooter_part.='<td>'.$fee_trans->card_charge_amount.'</td>';
      $footer_partfooter_part.='</tr>';
    }

    $footer_part.='<tr>';
    $footer_part.='<td colspan="'.$colspan.'" style="text-align:right;border: solid 1px #474747;"><strong>Total</strong></td>';
    $footer_part.='<td style="border: solid 1px #474747;">'.$fee_trans->amount_paid.'</td>';
    $footer_part.='</tr>';

    $footer_part .= '</table>';
    $without_comp_dynamic_part = $header_part.$without_comp.$footer_part;
    $dynamic_part = $header_part.$component_part.$footer_part;

    $footer_yashasvi = '<table>';
    $footer_yashasvi .= '<tr>';
    $footer_yashasvi .= '<th>Balance Amount</th>';
    $footer_yashasvi .= '<th>Entered By</th>';
    $footer_yashasvi .= '</tr>';
    $footer_yashasvi.='<tr>';
    $footer_yashasvi.='<td>'.($fee_trans->no_of_ins->total_fee -  $fee_trans->no_of_ins->total_fee_paid -  $fee_trans->no_of_ins->total_concession_amount_paid).'</td>';
    $footer_yashasvi.='<td>'.$created_name->fName.'</td>';
    $footer_yashasvi.='</tr>';
     $footer_yashasvi.='<tr>';
    $footer_yashasvi.='<td></td>';
    $footer_yashasvi.='<td>This is computer generated receipt. No signature required</td>';
    $footer_yashasvi.='</tr>';
    $footer_yashasvi .= '</table>';
    $fee_balance = '';
    $fee_balance .= $fee_trans->no_of_ins->total_fee -  $fee_trans->amount_paid -  $fee_trans->concession_amount - $fee_trans->discount_amount + $fee_trans->fine_amount;
    $amountInWords = $this->getIndianCurrency($fee_trans->amount_paid);

    $payment_mode = '<table>';
    foreach ($payment_modes as $key => $type) {
      if ($type->value == $fee_trans->payment_type ) {
        if ($type->value == '1' || $type->value == '4') {
          $payment_mode .='<tr>';
          $payment_mode .='<td style="border:none" >Payment Type : '.strtoupper($type->name).'</td>';
          $payment_mode .='<td style="border:none">Date :'.$fee_trans->cheque_or_dd_date.'</td>';
          $payment_mode .='<td style="border:none">Drawn On :'.$fee_trans->bank_name.'</td>';
          $payment_mode .='<td style="border:none">Number :'.$fee_trans->cheque_dd_nb_cc_dd_number.'</td>';
          $payment_mode .='</tr>';
        }else if($type->value == '11'){
          $payment_mode .='<tr>';
          $payment_mode .='<td style="border:none" >Payment Type : '.strtoupper($type->name).'</td>';
          $payment_mode .='<td style="border:none">Date :'.$fee_trans->cheque_or_dd_date.'</td>';
          $payment_mode .='<td style="border:none">Number :'.$fee_trans->cheque_dd_nb_cc_dd_number.'</td>';
          $payment_mode .='</tr>';
        }else{
          $payment_mode .='<tr>';
          $payment_mode .='<td>Payment Type '.strtoupper($type->name).'</td>';
          $payment_mode .='</tr>';
        }
      }
    }
    $payment_mode .= '</table>';

    $template = str_replace('%%installements%%',$dynamic_part, $template);
    $template = str_replace('%%installements_without_components%%',$without_comp_dynamic_part, $template);
    $template = str_replace('%%payment_modes%%',$payment_mode, $template);
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    $template = str_replace('%%footer_yashasvi%%',$footer_yashasvi, $template);
    $template = str_replace('%%fee_balance%%',$fee_balance, $template);
    return $template;
  }

  public function getIndianCurrency(float $number)
  {
      $decimal = round($number - ($no = floor($number)), 2) * 100;
      $hundred = null;
      $digits_length = strlen($no);
      $i = 0;
      $str = array();
      $words = array(0 => '', 1 => 'one', 2 => 'two',
          3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
          7 => 'seven', 8 => 'eight', 9 => 'nine',
          10 => 'ten', 11 => 'eleven', 12 => 'twelve',
          13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
          16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
          19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
          40 => 'forty', 50 => 'fifty', 60 => 'sixty',
          70 => 'seventy', 80 => 'eighty', 90 => 'ninety');
      $digits = array('', 'hundred','thousand','lakh', 'crore');
      while( $i < $digits_length ) {
          $divider = ($i == 2) ? 10 : 100;
          $number = floor($no % $divider);
          $no = floor($no / $divider);
          $i += $divider == 10 ? 1 : 2;
          if ($number) {
              $plural = (($counter = count($str)) && $number > 9) ? '' : null;
              $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
              $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
          } else $str[] = null;
      }
      $Rupees = implode('', array_reverse($str));
      $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
      return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
  }


  // Fastfee collection new

  public function insert_fast_collection_fee_datav1(){
    $input = $this->input->post();
    $fTransids =[];
    foreach ($input['blueprint_amount'] as $bpId => $bp_amount) {
      $enterAmount = $bp_amount;
      if(!empty($input['blueprint_discount_amount'][$bpId])){
        $enterAmount = $bp_amount + $input['blueprint_discount_amount'][$bpId];
      }

      $bpSchudeAmount = $this->_consturct_student_blueprint_wise_data($bpId, $enterAmount, $input['fast_fee_student_id']);

      $bpSchudeAmount['total_amount'] = $enterAmount;
      $bpSchudeAmount['discount_amount'] = isset($input['blueprint_discount_amount'][$bpId]) ? $input['blueprint_discount_amount'][$bpId] : 0;
      $bpSchudeAmount['loan_provider_charges'] = 0;
      $bpSchudeAmount['final_amount'] = $enterAmount;
      $bpSchudeAmount['payment_type'] = $input['payment_type'];
      $bpSchudeAmount['bank_name'] = $input['blueprint_bank_name'];
      $bpSchudeAmount['branch_name'] = $input['blueprint_branch_name'];
      $bpSchudeAmount['cheque_dd_nb_cc_dd_number'] = $input['blueprint_cheque_dd_nb_cc_dd_number'];
      $bpSchudeAmount['dd_number'] = $input['blueprint_dd_number'];
      $bpSchudeAmount['bank_date'] = $input['blueprint_bank_date'];
      $bpSchudeAmount['cc_number'] = $input['blueprint_cc_number'];
      $bpSchudeAmount['nb_number'] = $input['blueprint_nb_number'];
      $bpSchudeAmount['remarks'] = $input['blueprint_fast_remarks'];
      $bpSchudeAmount['excess_amount_id'] = (isset($input['excess_amount_id'])) ? $input['excess_amount_id'] : '';

      $bpSchudeAmount['student_id'] = $input['fast_fee_student_id'];
      $bpSchudeAmount['transaction_mode'] = 'COUNTER';
      $bpSchudeAmount['blueprint_id'] = $bpId;
      $bpSchudeAmount['receipt_date'] = $input['blueprint_date'];

      $fTrans = $this->fees_collection_model->insert_fee_transcation($bpSchudeAmount);
      
      if (empty($fTrans)) {
        $this->db->trans_rollback();
      }else{
        array_push($fTransids, $fTrans);
        // generate and update receipt number after transcation
        $this->fees_collection_model->update_receipt_transcation_wise($fTrans);
        // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
        $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
        if ($rconStatus->reconciliation_status == 1) {
          $this->db->trans_commit();
          $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');
          // redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
        }else{
          $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans);
          if (empty($result)) 
          {
            $this->db->trans_rollback();
          }
          if ($this->db->trans_status()) 
          {
            $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($bpId);

            $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

            if ($blue_print->enable_sms == TRUE) {
              $this->__sms_fees_payment_counter($data['fee_trans']->amount_paid, $data['fee_trans']->student->stdName, $data['fee_trans']->student->stdId, $blue_print->name);  
            }
            $this->db->trans_commit();
          }
          else
          {
            $this->session->set_flashdata('flashError', 'Something went wrong');
          }
        }
      }
    }
    $ePayment_type = explode('_', $input['payment_type']);
    $payment_type = $ePayment_type[0];
    if($input['difference_amount'] > 0){
      $this->fees_collection_model->insert_additional_amount_details($input['fast_fee_student_id'],$input['difference_amount'], 'Excess amount for collecting fees', $payment_type, $input['blueprint_bank_name'], $input['blueprint_branch_name'], $input['blueprint_cheque_dd_nb_cc_dd_number'], $input['blueprint_bank_date'], $input['blueprint_date']);
    }
    
    if($payment_type == '777'){
      $this->fees_collection_model->update_online_challan_payment_source_id_status($input['online_challan_order_id'], $fTrans,'Started');
    }
		

    $this->session->set_flashdata('flashSuccess', 'Fee collected Successfully');
    $this->session->set_userdata('fTransids', $fTransids);
    $this->session->set_userdata('fees_student_id', $input['fast_fee_student_id']);
    redirect('feesv2/fees_fast_collection/fee_fast_reciept');
  }

  private function __sms_fees_payment_counter($amount_paid, $student_name, $student_admission_id, $blueprint_name){

    $stake_holder_id = [$student_admission_id];
    /*$sent_by = $this->authorization->getAvatarId();
    $ph_type = 'Both';
    $sent_to_str = 'Student Individual';
    $source = 'Fee';
    $sh_type = 'Student';*/
    $fee_payment_sms = $this->settings->getSetting('fee_payment_sms');
    if(!empty($fee_payment_sms)){
			if ($fee_payment_sms->sms_enabled == TRUE) {
        $message = $fee_payment_sms->message;
        $message = str_replace('%%student_name%%', $student_name, $message);
        $message = str_replace('%%amount%%', $amount_paid, $message);
			}
    }else{
      $message = $student_name . " Fee payment of Rs. ".$amount_paid." towards ".$blueprint_name." collected successfully. \n - Cashier, ". ucwords($this->settings->getSetting('school_short_name')).' -NXTSMS';
    }
    $input_arr = array();
    $this->load->helper('texting_helper');
    $input_arr['student_ids'] = $stake_holder_id;
    // $text_mode_to_use = $this->settings->getSetting('text_mode_to_use');
    $input_arr['mode'] = 'notification_sms';
    /*if($text_mode_to_use) {
      $input_arr['mode'] = $text_mode_to_use;
    }*/
    $input_arr['source'] = 'Fee';
    $input_arr['send_to'] = 'Both';
    $input_arr['message'] = $message;
    return $response = sendText($input_arr);
    // return sendCommonSMS($stake_holder_id, $sh_type, $source, $message, $sent_by, $sent_to_str, $ph_type);
  }

  private function _consturct_student_blueprint_wise_data($bpId, $bp_amount, $student_id){
    $result = $this->db->select('fsic.id as fee_student_installments_components_id, fsic.blueprint_component_id, ifnull(fsic.component_amount,0) as component_amount, ifnull(component_amount_paid,0) as component_amount_paid, ifnull(concession_amount,0) as concession_amount, ifnull(concession_amount_paid,0) as concession_amount_paid, fsi.id as fee_student_installments_id, fsi.feev2_installments_id, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, fsi.total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) as total_concession_amount_paid, fss.id as fee_student_schedule_id, (ifnull(fsic.component_amount,0) - ifnull(fsic.component_amount_paid,0) -  ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0)) as balance_amount, fcs.id as cohort_student_id')
    ->from('feev2_cohort_student fcs')
    ->where('fcs.student_id',$student_id)
    ->where('fcs.blueprint_id',$bpId)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    ->get()->result();

    $components_split = array();
    $input = array();
    $tAllocAmount = $bp_amount;
    foreach ($result as $key => $val) {    
      if($tAllocAmount >= $val->balance_amount){
        $input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->balance_amount;
        $tAllocAmount = $tAllocAmount - $val->balance_amount;
      }else{
        $input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $tAllocAmount;
        $tAllocAmount = $tAllocAmount - $val->balance_amount;
        if ($tAllocAmount < 0) $tAllocAmount = 0; 
      } 
      $input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->concession_amount;
      $input['comp_id'][] = $val->blueprint_component_id;
      $input['fsicompId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_components_id;
      $input['fsInsId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_id;
      $input['cohort_student_id'] = $val->cohort_student_id;
      $input['fee_student_schedule_id'] = $val->fee_student_schedule_id;
    }
    return $input;
  }

//   $amount = 1000;
// $numInstallments = 5;
// $installmentType = 'equal';

// $installments = calculateInstallments($amount, $numInstallments, $installmentType);

//   public function calculateInstallments($amount, $numInstallments, $installmentType) {
//     if ($numInstallments <= 0) {
//         return "Number of installments must be greater than zero.";
//     }

//     if ($installmentType == 'equal') {
//         $installmentAmount = $amount / $numInstallments;
//         $installmentAmount = round($installmentAmount, 2); // Round to two decimal places
//     } elseif ($installmentType == 'last_larger') {
//         $installmentAmount = floor($amount / $numInstallments);
//         $lastInstallment = $amount - ($installmentAmount * ($numInstallments - 1));
//     } else {
//         return "Invalid installment type. Please choose 'equal' or 'last_larger'.";
//     }

//     $installments = array();
//     for ($i = 0; $i < $numInstallments - 1; $i++) {
//         $installments[] = $installmentAmount;
//     }

//     if ($installmentType == 'last_larger') {
//         $installments[] = $lastInstallment;
//     } else {
//         $installments[] = $installmentAmount;
//     }

//     return $installments;
// }

public function fee_reciept_view_fast_fee($fTrans, $canceled = ''){
 
  $data['cancel'] = $canceled;
  // $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
  $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);
  $data['created_name'] = $this->avatar->getAvatarById($data['fee_trans']->collected_by);
  $data['blue_print'] = $this->fees_collection_model->ge_blueprint_details_by_id($data['fee_trans']->no_of_ins->feev2_blueprint_id);
  $data['payment_modes'] = json_decode($data['blue_print']->allowed_payment_modes);

  $isAutorizedInstallment = $this->authorization->isAuthorized('FEESV2.FAST_COLLECTION_INSTALLMENT_WISE');
  if($isAutorizedInstallment){
    $data['main_content'] = 'feesv2/receipts/'.$data['blue_print']->receipt_for;
  }else{
    $fileCheck = FCPATH."application/views/feesv2/receipts/".$data['blue_print']->receipt_for.'.php';
    if (!file_exists($fileCheck)) {
      $data['main_content'] = 'feesv2/receiptv1/standard';
    }else{
      $data['main_content'] = 'feesv2/receiptv1/'.$data['blue_print']->receipt_for;
    }
  }
 
  $this->load->view('inc/template_fee', $data);
}


// fast collection fees new
public function insert_fast_collection_fee_data_new(){
  $input = $this->input->post();
  $fTransids =[];
  foreach ($input['blueprint_amount'] as $bpId => $ins_data) {
    $insAmount = 0;
    foreach ($ins_data as $key => $amount) {
      $insAmount += $amount;
    }

    $totalFineAmount = 0;
    foreach ($input['installment_fine_amount'][$bpId] as $key => $fineAmount) {
      $totalFineAmount += $fineAmount;
    }
    $bpSchudeAmount = $this->_consturct_student_blueprint_wise_data_new($bpId, $ins_data, $insAmount, $input['fast_fee_student_id'], $input['installment_concession_discount_amount'], $input['installment_fine_amount'][$bpId], $totalFineAmount);
    $bpSchudeAmount['total_amount'] = $insAmount - $totalFineAmount;
    $bpSchudeAmount['discount_amount'] = '';
    $bpSchudeAmount['loan_provider_charges'] = 0;
    $bpSchudeAmount['total_fine_amount'] = $totalFineAmount;
    $bpSchudeAmount['final_amount'] = $insAmount;
    $bpSchudeAmount['payment_type'] = $input['payment_type'];
    $bpSchudeAmount['bank_name'] = $input['blueprint_bank_name'];
    $bpSchudeAmount['branch_name'] = $input['blueprint_branch_name'];
    $bpSchudeAmount['cheque_dd_nb_cc_dd_number'] = $input['blueprint_cheque_dd_nb_cc_dd_number'];
    $bpSchudeAmount['dd_number'] = $input['blueprint_dd_number'];
    $bpSchudeAmount['bank_date'] = $input['blueprint_bank_date'];
    $bpSchudeAmount['cc_number'] = $input['blueprint_cc_number'];
    $bpSchudeAmount['nb_number'] = $input['blueprint_nb_number'];
    $bpSchudeAmount['remarks'] = $input['blueprint_fast_remarks'];
    $bpSchudeAmount['excess_amount_id'] = isset($input['excess_amount_id']) ? $input['excess_amount_id'] : '';
    $bpSchudeAmount['student_id'] = $input['fast_fee_student_id'];
    $bpSchudeAmount['transaction_mode'] = 'COUNTER';
    $bpSchudeAmount['blueprint_id'] = $bpId;
    $bpSchudeAmount['receipt_date'] = $input['blueprint_date'];
    $fTrans = $this->fees_collection_model->insert_fee_transcation($bpSchudeAmount);

    if (empty($fTrans)) {
      $this->db->trans_rollback();
    }else{
      array_push($fTransids, $fTrans);
      // generate and update receipt number after transcation
      $this->fees_collection_model->update_receipt_transcation_wise($fTrans);
      // Check Reconcilication Status if 1 pending... not allowed updated student schedule table
      $rconStatus = $this->fees_collection_model->lastFeeTranscationReconCheck($fTrans);
      if ($rconStatus->reconciliation_status == 1) {
        $this->db->trans_commit();
        $this->session->set_flashdata('flashInfo', 'Fee collected Successfully. Reconcilication pending');
        // redirect('feesv2/fees_collection/fee_reciept/'.$fTrans);
      }else{
        $totalConSum = 0;
        if(!empty($bpSchudeAmount['discount_conc_amount'])){
         
          foreach($bpSchudeAmount['discount_conc_amount'] as $schInstallmentId => $schComponents) {
              foreach($schComponents as $schComponentId => $amount) {
                $totalConSum += $amount;
              }
          }
          $query = $this->db->select('feev2_blueprint_installment_types_id')
          ->from('feev2_student_schedule')
          ->where('id',$bpSchudeAmount['fee_student_schedule_id'])
          ->get()->row();
          $insert_id = $this->fees_collection_model->insert_pre_defined_concession_details_new($bpSchudeAmount['cohort_student_id'], $totalConSum, 'Discount Concession', $totalConSum, $bpSchudeAmount['remarks']);
          $this->fees_collection_model->insert_pre_defined_concession_component_details($insert_id, $bpSchudeAmount['discount_conc_amount'],$query->feev2_blueprint_installment_types_id);
        }
        
        $result =  $this->fees_collection_model->update_student_schedule_all_table($fTrans, $totalConSum);
        if (empty($result)) 
        {
          $this->db->trans_rollback();
        }
        if ($this->db->trans_status()) 
        {
          $blue_print = $this->fees_collection_model->ge_blueprint_details_by_id($bpId);

          $data['fee_trans'] = $this->fees_collection_model->get_fee_transcation_for_receipt($fTrans);

          if ($blue_print->enable_sms == TRUE) {
            $this->__sms_fees_payment_counter($data['fee_trans']->amount_paid, $data['fee_trans']->student->stdName, $data['fee_trans']->student->stdId, $blue_print->name);  
          }
          $this->db->trans_commit();
        }
        else
        {
          $this->session->set_flashdata('flashError', 'Something went wrong');
        }
      }
    }
  }
  $ePayment_type = explode('_', $input['payment_type']);
  $payment_type = $ePayment_type[0];
  if($input['difference_amount'] > 0){
    $this->fees_collection_model->insert_additional_amount_details($input['fast_fee_student_id'],$input['difference_amount'], 'Excess amount for collecting fees', $payment_type, $input['blueprint_bank_name'], $input['blueprint_branch_name'], $input['blueprint_cheque_dd_nb_cc_dd_number'], $input['blueprint_bank_date'], $input['blueprint_date']);
  }
  
  if($payment_type == '777'){
    $this->fees_collection_model->update_online_challan_payment_source_id_status($input['online_challan_order_id'], $fTrans,'Started');
  }
  

  $this->session->set_flashdata('flashSuccess', 'Fee collected Successfully');
  $this->session->set_userdata('fTransids', $fTransids);
  $this->session->set_userdata('fees_student_id', $input['fast_fee_student_id']);
  redirect('feesv2/fees_fast_collection/fee_fast_reciept');
}

private function _consturct_student_blueprint_wise_data_new($bpId, $ins_data, $insAmount, $student_id, $concession_discount, $installmentFineAmount, $totalFineAmount){
  $fsiIds = [];
  foreach ($ins_data as $SchinsId => $value) {
    array_push($fsiIds, $SchinsId);
  }
  $result = $this->db->select('fsic.id as fee_student_installments_components_id, fsic.blueprint_component_id, ifnull(fsic.component_amount,0) as component_amount, ifnull(component_amount_paid,0) as component_amount_paid, ifnull(concession_amount,0) as concession_amount, ifnull(concession_amount_paid,0) as concession_amount_paid, fsi.id as fee_student_installments_id, fsi.feev2_installments_id, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, fsi.total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) as total_concession_amount_paid, fss.id as fee_student_schedule_id, (ifnull(fsic.component_amount,0) - ifnull(fsic.component_amount_paid,0) -  ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0)) as balance_amount, fcs.id as cohort_student_id, ifnull(fsi.total_fine_amount,0) as total_fine_amount, ifnull(fsi.total_fine_amount_paid,0) as total_fine_amount_paid, ifnull(fsi.total_fine_waived,0) as total_fine_waived, fbc.is_concession_eligible')
  ->from('feev2_cohort_student fcs')
  ->where('fcs.student_id',$student_id)
  ->where('fcs.blueprint_id',$bpId)
  ->where_in('fsi.id',$fsiIds)
  ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
  ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
  ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
  ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
  ->get()->result();

  $tAllocAmount = $insAmount - $totalFineAmount;
  foreach ($result as $key => $val) {
    $balance_amount = $val->balance_amount;
    // Check if concession is eligible and apply discount concession
    $discount_concession = 0;
    if (!empty($concession_discount[$val->fee_student_installments_id]) && $val->is_concession_eligible == 1) {
      $discount_concession = $concession_discount[$val->fee_student_installments_id];
    }
    // Adjust balance amount based on concession
      $adjusted_balance = $balance_amount - $discount_concession;
    // Allocate amount based on adjusted balance
    if ($tAllocAmount >= $adjusted_balance) {
      $val->allocate_amount = $adjusted_balance;
      $tAllocAmount -= $adjusted_balance;
    } else {
      $val->allocate_amount = $tAllocAmount;
      $tAllocAmount -= $adjusted_balance;
      if ($tAllocAmount < 0) $tAllocAmount = 0;
    }
  }

  $input = array();
  foreach ($result as $key => $val) {
    $discount_concession = 0;
    if(!empty($concession_discount[$val->fee_student_installments_id])){
      $discount_concession = $concession_discount[$val->fee_student_installments_id];
      $input['discount_conc_amount'][$val->fee_student_installments_id][$val->fee_student_installments_components_id] = $discount_concession;
    }
    $input['pay_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->allocate_amount;
    $input['fine_amount_ins'][$val->fee_student_installments_id] =  $installmentFineAmount[$val->fee_student_installments_id];

    if (!empty($val->is_concession_eligible) && $val->is_concession_eligible == 1) {
      $input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->concession_amount + $discount_concession;
    } else {
      $input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = 0; 
    }
    // $input['conc_amount'][$val->feev2_installments_id][$val->blueprint_component_id] =  $val->concession_amount + $discount_concession;
    
    $input['comp_id'][] = $val->blueprint_component_id;
    $input['fsicompId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_components_id;
    $input['fsInsId'][$val->feev2_installments_id][$val->blueprint_component_id] = $val->fee_student_installments_id;
    $input['cohort_student_id'] = $val->cohort_student_id;
    $input['fee_student_schedule_id'] = $val->fee_student_schedule_id;
  }
  foreach ($result as $key => $val) {
    if(!empty($input['fine_amount_ins'])){
      $input['fine_amount'][$val->feev2_installments_id][$val->blueprint_component_id] = $input['fine_amount_ins'][$val->fee_student_installments_id];
      break;
    }
  }
  return $input;
}

}