<div class="modal fade" id="add_additional_information" role="dialog" data-backdrop="static" style="z-index:2000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="border-radius:1rem;width: 50%; margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">Add Additional Information</h5>
                <button type="button" class="close" data-dismiss="modal" onclick="showMainModal();"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <input type="hidden" class="session_id" name="additional_session_id" id="additional_session_id">
                    <div class="form-group">
                        <label for="additional_information_desc" class="control-label">Additional Information <font style="color: red;">*</font></label>
                        <textarea class="form-control" id="additional_information_desc" name="additional_information_desc" style="height: 11rem;" placeholder="Wanna Describe?"></textarea>
                        <span id="additionalInformationError" style="display: none;"></span>
                    </div>

                    <div class="form-check form-switch pl-0">
                        <input class="form-check-input" type="checkbox" role="switch" id="visible_info_to_students">
                        <label class="form-check-label" style="margin-left: 2rem;" for="visible_info_to_students">Make visible to students</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="showMainModal();">Close</button>
                <button type="button" class="btn btn-primary mt-0" onClick="updateAdditionalInfo()">Update</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // $("#add_additional_information").on("shown.bs.modal", e => {
        // $("#resources_modal").modal("hide");

        // const showresource = e.relatedTarget.dataset.show_resource;
        // if (showresource == "no") {
        //     $(".btn-secondary").attr("onClick", "")
        // } else {
        //     $(".btn-secondary").attr("onClick", "showResourcesModal()")
        // }
    // })

    $("#additional_information_desc").keydown(e => {
        if (e.keyCode == 13 && !e.shiftKey) {
            e.preventDefault();
            updateAdditionalInformation();
            loadAdditionalInfo();
        }
    })

    function updateAdditionalInformation() {
        const id = $("#additional_session_id").val();
        const value = $("#additional_information_desc").val();
        const visible_to_students = $("#visible_info_to_students").is(":checked") && 1 || 0;

        $.ajax({
            url: '<?php echo site_url("academics/Lesson_plan/updateAdditionalInformation"); ?>',
            type: "POST",
            data: { id, value, visible_to_students },
            success(data) {
                let parsedData = JSON.parse(data);
                if (parsedData) {
                    $("#add_additional_information").modal('hide');
                    Swal.fire({
                        icon: "success",
                        title: "Activity saved",
                        text: "Activity saved successfully!",
                    }).then(() => {
                        getSessionData(id);
                        loadAdditionalInfo();
                        showMainModal();
                    });
                } else {
                    $("#add_additional_information").modal('hide');
                    Swal.fire({
                        icon: "error",
                        title: "Failed to save activity",
                        text: "Please try again later.",
                    }).then(() => {
                        $("#add_additional_information").modal('show');
                    });
                }
            },
            error(err) {
                console.log(err);
                $("#add_additional_information").modal('hide');
                Swal.fire({
                    icon: "error",
                    title: "Failed to save activity",
                    text: "Please try again later.",
                }).then(() => {
                    $("#add_additional_information").modal('show');
                });
            }
        })
    }

    function updateAdditionalInfo() {
        $("#additionalInformationError").hide();
        if($("#additional_information_desc").val() == ""){
            $("#additionalInformationError").html("Additional Information Is Required").css("color", "red").show();
            return false;
        }
        updateAdditionalInformation();
        loadAdditionalInfo();
    }
</script>