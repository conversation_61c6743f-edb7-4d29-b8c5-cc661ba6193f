<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="text-align: center;padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Texting Delivery Report</strong>
            </h3>
        </div>
        <div class="card-body px-0 py-1">
            <div class="row">
                <div class="col-md-12">
                    <span class="text-muted" style="font-size: 15px; font-weight: 600;">Note: This report is based on the selected academic year.</span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <h3 class="text-center pt-2">Texting Delivery Status</h3>
                        <div id="sms-graph" style="min-height: 200px;"></div>
                        <div class="mt-2">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Status</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $i=1; $total=0; foreach ($sms as $key => $s) {
                                        echo '<tr>';
                                        echo '<td>'.($i++).'</td>';
                                        echo '<td>'.str_replace('_', ' ', $s->status_meaning).'</td>';
                                        echo '<td>'.$s->status_count.'</td>';
                                        echo '</tr>';
                                        $total += $s->status_count;
                                    } ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="2" class="text-right">Total Sent</th>
                                        <th><?php echo $total; ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card">
                        <h3 class="text-center pt-2">Notification Delivery Status</h3>
                        <div id="notification-graph" style="min-height: 200px;"></div>
                        <div class="mt-2">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Status</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $i=1; $total = 0; foreach ($notifications as $key => $n) {
                                        echo '<tr>';
                                        echo '<td>'.($i++).'</td>';
                                        echo '<td>'.str_replace('_', ' ', $n->status).'</td>';
                                        echo '<td>'.$n->status_count.'</td>';
                                        echo '</tr>';
                                        $total += $n->status_count;
                                    } ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="2" class="text-right">Total Sent</th>
                                        <th><?php echo $total; ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<a href="<?php echo site_url('communication_dashboard');?>" id="backBtn" onclick="loader()"><span
        class="fa fa-mail-reply"></span></a>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript">
$(document).ready(function() {
    var sms = JSON.parse('<?php echo json_encode($sms_graph) ?>');
    var notification = JSON.parse('<?php echo json_encode($notification_graph) ?>').map(item => {
        // Ensure status values with underscores are replaced correctly
        let updatedItem = {};
        for (let key in item) {
            let newKey = key.replace(/_/g, ' '); // Replace underscores in keys
            updatedItem[newKey] = item[key];
        }
        return updatedItem;
    });
    var notification_labels = JSON.parse('<?php echo json_encode($notification_labels) ?>').map(label => label.replace(/_/g, ' '));
    // console.log(sms)
    Morris.Bar({
        element: 'sms-graph',
        data: sms,
        barSize: 25,
        xkey: 'status',
        ykeys: ['Awaited', 'Failed', 'Delivered'],
        labels: ['Awaited', 'Failed', 'Delivered'],
        resize: true,
        barColors: ['#94375E', '#33414E', '#95B75D'],
        parseTime: false,
        hideHover: true,
    });

    Morris.Bar({
        element: 'notification-graph',
        data: notification,
        barSize: 25,
        xkey: 'status',
        ykeys: notification_labels,
        labels: notification_labels,
        resize: true,
        barColors: ['#94375E', '#405d27', '#feb236', '#ff7b25', '#6893ca', '#33414E', '#95B75D'],
        parseTime: false,
        hideHover: true,
    });
});
</script>