<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller');?>">Classroom <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></a></li>
  <li><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> Report</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('classroom_chronicles/classroom_chronicles_controller'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> Reports
          </h3>
        </div>
      </div>
    </div>

    <div class="modal-body">
      <table class="table table-bordered">
        <thead>
        <tr>
          <th>Class/Section</th>
          <th># of Students</th>
          <th># of <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> </th>
          <th>Action </th>
        </tr>
        </thead>
        <tbody>
        <?php 
        $approved = 0;
        $chr_count = 0;
        foreach ($class_report as $key => $val) { 
            if ($val->approved != 0) { 
              $approved +=$val->approved;
            } if ($val->chr_count != 0) { 
              $chr_count +=$val->chr_count;
            } ?>
            <tr>
              <td><?php echo $val->classSection ?></td>
              <td><?php echo $val->approved ?></td>
              <td><?php echo $val->chr_count ?></td>
              <td><a onclick="view_chronicles_count_secid('<?php echo $val->class_section_id ?>')" id="add_button" data-target="#view_chronicles_count" data-toggle="modal" class="btn btn-primary" >View Details</a></td>
                        
            </tr>
      
          
        <?php } ?>
        </tbody>
        <tfoot>
          <tr>
            <th>Total</th>
            <th><?php echo $approved  ?></th>
            <th><?php echo $chr_count  ?></th>
            <th></th> 
            
          </tr>
        </tfoot>
      </table>
    </div>

  </div>
</div>


<div class="modal fade" id="view_chronicles_count" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">View <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?> by Date-wise</h4>

      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <div id="chronicles-detail">

          </div>
          

        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>

        </div>
    </div>
  </div>
</div>

<script type="text/javascript">
function view_chronicles_count_secid(class_section_id) {
   $.ajax({
    url: '<?php echo site_url('classroom_chronicles/classroom_chronicles_controller/get_chronicles_count_by_cls_id'); ?>',
    type: 'post',
    data: {
      'class_section_id': class_section_id,
    },
    success: function(data) {
      var resData = $.parseJSON(data);
      console.log(resData);
      $('#chronicles-detail').html(prepare_chronicles_table(resData));
    }
  });
}

function prepare_chronicles_table(resData) {
    var html = '';
  var total = 0;
  var header = '';
  var thead = '';
  var body = '';
  var i=1;

  thead +='<thead>';
  thead +='<tr>';
  thead +='<th>#</th>';
  thead +='<th>Date</th>';
  thead +='<th># of <?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></th>';

  for(var key in resData){


  body += '<tr>'
      body += '<td>'+i+'</td>';
      body += '<td>'+resData[key].made_date+'</td>';
      body += '<td>'+resData[key].chr_count+'</td>';
      i++;
       }
 
  header +='<table id="reportTable" class="table table-bordered">';

  html += header + thead + body;

  return html;
}
</script>