<?php

class Calendar_events_v2_model extends CI_model {
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
    }



public function save_calendar_events($input){
    $event = $input['events'];
    $from_date = strtotime($event['eventStartDate']);
    $to_date = strtotime($event['eventEndDate']);
    $start_format = date('Y-m-d', $from_date);
    $end_format = date('Y-m-d', $to_date);

    $insert = array(
        'calendar_v2_master_id'  => $input['id'],
        'event_name'             => $event['eventName'],
        'from_date'              => $start_format,
        'to_date'                => $end_format,
        'event_type'             => $event['eventType'],
    );
    $this->db->insert('calendar_events_v2', $insert);
    return true;

}

public function get_events(){
    $result= $this->db_readonly->select("id,event_name,DATE_FORMAT(from_date, '%d-%b-%Y') as from_date,DATE_FORMAT(to_date, '%d-%b-%Y') as to_date")
    ->from('calendar_events_v2')
    ->get()->result();
    return $result;
}

public function get_events_display(){
    $input =  $this->input->post();
    $result= $this->db_readonly->select("id, event_name, event_type, DATE_FORMAT(from_date, '%d-%b-%Y') as from_date, DATE_FORMAT(to_date, '%d-%b-%Y') as to_date")
    ->where('calendar_v2_master_id', $input['id'])
    ->from('calendar_events_v2')
    ->get()->result();
    return $result;
}

public function save_calendar_name($input) {
    $this->db->trans_begin();
    $start_date_formatted = DateTime::createFromFormat('d-m-Y', $input['start_date'])->format('Y-m-d');
    $end_date_formatted = DateTime::createFromFormat('d-m-Y', $input['end_date'])->format('Y-m-d');
    $master_data = array(
        'calendar_name' => $input['calendar_name'],
        'academic_year' => $input['academic_year'],
        'target_group' => $input['target_group'],
        'start_date' => $start_date_formatted,
        'end_date' => $end_date_formatted,
        'status' => 0, // Set status to 0 (unlocked) by default
        'created_at' => date('Y-m-d H:i:s')
    );

    $this->db->insert('calendar_v2_master', $master_data);
    $master_id = $this->db->insert_id();

    if ($input['target_group'] == 'students' && !empty($input['sessions'])) {
        $sessions_data = array();
        foreach ($input['sessions'] as $day => $count) {
            $sessions_data[] = array(
                'calendar_v2_master_id' => $master_id,
                'day_name' => $day,
                'session_count' => $count,
                'created_at' => date('Y-m-d H:i:s')
            );
        }

        if (!empty($sessions_data)) {
            $this->db->insert_batch('calendar_events_v2_override_sessions', $sessions_data);
        }
    }

    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return array(
            'status' => 'error',
            'message' => 'Failed to save calendar template'
        );
    } else {
        $this->db->trans_commit();
        return array(
            'status' => 'success',
            'message' => 'Calendar template created successfully',
            'id' => $master_id
        );
    }
}

public function get_events_type(){
    $result= $this->db_readonly->select("*")
    ->from('calendar_v2_master')
    ->where('academic_year', $this->yearId)
    ->get()->result();
    return $result;
}

public function get_class_calendar(){
    $input = $this->input->post('calendarID');
    $show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
        $this->db_readonly->select('c.id as classID, cs.id as sectionID, c.class_name as class_name, cs.section_name as  section_name')
                          ->from('class c')
                          ->join('class_section  cs' ,'cs.class_id=c.id')
                          ->where('c.acad_year_id', $this->yearId)
                          ->where('c.is_placeholder !=',1)
                          ->order_by('cs.display_order');
        if(!$show_placeholder_sections) {
          $this->db_readonly->where('cs.is_placeholder !=', 1);
        }

          $result = $this->db_readonly->get()->result();
          $this->db_readonly->select('calendar_v2_master_id, assigned_class_id, assigned_section_id, assigned_type')
                      ->from('calendar_events_v2_assigned')
                      ->where('assigned_type !=', 'STAFF')
                      ->where('calendar_v2_master_id', $input);

    $editdata = $this->db_readonly->get()->result();
    $calArry=[];
    if(!empty($editdata)){
        foreach ($editdata as $key => $val) {
            $calArry[$val->assigned_section_id]= $val;
        }
    }


    foreach ($result as $value) {
        $value->isChecked =0;
        if(array_key_exists($value->sectionID, $calArry)){
            $value->isChecked =1;
        }
    }


    return $result;


}

public function calendar_class() {
    $input = $this->input->post();

    $check_result = $this->check_existing_mappings($input, 'class');
    if ($check_result['status'] === 'error') {
        return $check_result;
    }

    $this->db->trans_begin();

    try {
        if (!empty($input['classSection'])) {
            $insert = [];
            foreach ($input['classSection'] as $selected_class) {
                list($class, $section) = explode('_', $selected_class);

                $exists = $this->db->select('id')
                    ->from('calendar_events_v2_assigned')
                    ->where('calendar_v2_master_id', $input['id'])
                    ->where('assigned_class_id', $class)
                    ->where('assigned_section_id', $section)
                    ->where('assigned_type', 'SEC')
                    ->get()
                    ->row();

                if (!$exists) {
                    $insert[] = array(
                        'assigned_class_id' => $class,
                        'assigned_section_id' => $section,
                        'assigned_type' => 'SEC',
                        'calendar_v2_master_id' => $input['id']
                    );
                }
            }

            if (!empty($insert)) {
                $this->db->insert_batch('calendar_events_v2_assigned', $insert);
            }
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return array('status' => 'error', 'message' => 'Failed to update class mappings');
        }

        $this->db->trans_commit();
        return array('status' => 'success', 'message' => 'Class mappings updated successfully');

    } catch (Exception $e) {
        $this->db->trans_rollback();
        return array('status' => 'error', 'message' => 'An error occurred while updating class mappings');
    }
}

public function check_existing_mappings($input, $type = 'staff') {
    if ($type === 'staff') {
        $existing = $this->db_readonly->select('cv.calendar_name, cea.assigned_staff_type')
            ->from('calendar_events_v2_assigned cea')
            ->join('calendar_v2_master cv', 'cv.id = cea.calendar_v2_master_id')
            ->where('cea.assigned_type', 'STAFF')
            ->where('cea.calendar_v2_master_id !=', $input['id'])
            ->where_in('cea.assigned_staff_type', $input['staff'])
            ->get()
            ->result_array();

        if (!empty($existing)) {
            $mappings = [];
            foreach ($existing as $map) {
                $mappings[] = "{$map['assigned_staff_type']} is mapped to {$map['calendar_name']}";
            }
            return array(
                'status' => 'error',
                'message' => 'Staff types already mapped: ' . implode(', ', $mappings)
            );
        }
    } else {
        $sections = array_map(function($item) {
            list($class, $section) = explode('_', $item);
            return $section;
        }, $input['classSection']);

        $existing = $this->db_readonly->select('cv.calendar_name, c.class_name, cs.section_name')
            ->from('calendar_events_v2_assigned cea')
            ->join('calendar_v2_master cv', 'cv.id = cea.calendar_v2_master_id')
            ->join('class c', 'c.id = cea.assigned_class_id')
            ->join('class_section cs', 'cs.id = cea.assigned_section_id')
            ->where('cea.assigned_type', 'SEC')
            ->where('cea.calendar_v2_master_id !=', $input['id'])
            ->where_in('cea.assigned_section_id', $sections)
            ->get()
            ->result_array();

        if (!empty($existing)) {
            $mappings = [];
            foreach ($existing as $map) {
                $mappings[] = "{$map['class_name']} {$map['section_name']} is mapped to {$map['calendar_name']}";
            }
            return array(
                'status' => 'error',
                'message' => 'Classes already mapped: ' . implode(', ', $mappings)
            );
        }
    }

    return array('status' => 'success');
}

public function calendar_staff_type($input) {
    $check_result = $this->check_existing_mappings($input, 'staff');
    if ($check_result['status'] === 'error') {
        return $check_result;
    }

    $this->db->trans_begin();

    try {
        if (!empty($input['staff'])) {
            $insert = array_map(function($staff_type) use ($input) {
                return array(
                    'assigned_staff_type' => $staff_type,
                    'assigned_type' => 'STAFF',
                    'calendar_v2_master_id' => $input['id']
                );
            }, $input['staff']);

            $this->db->insert_batch('calendar_events_v2_assigned', $insert);
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return array('status' => 'error', 'message' => 'Failed to update staff mappings');
        }

        $this->db->trans_commit();
        return array('status' => 'success', 'message' => 'Staff mappings updated successfully');

    } catch (Exception $e) {
        $this->db->trans_rollback();
        return array('status' => 'error', 'message' => 'An error occurred while updating staff mappings');
    }
}

public function update_calendar() {
    $data = $this->input->post();



    $this->db->trans_begin();

    try {
        $start_date_formatted = DateTime::createFromFormat('d-m-Y', $data['start_date'])->format('Y-m-d');
        $end_date_formatted = DateTime::createFromFormat('d-m-Y', $data['end_date'])->format('Y-m-d');

        // Get current status to preserve it
        $current_status = $this->db_readonly->select('status')
            ->from('calendar_v2_master')
            ->where('id', $data['calendar_id'])
            ->get()
            ->row();

        $status = ($current_status && $current_status->status) ? $current_status->status : 0;

        $update_data = array(
            'calendar_name' => $data['calendar_name'],
            'academic_year' => $data['academic_year'],
            'target_group' => $data['target_group'],
            'start_date' => $start_date_formatted,
            'end_date' => $end_date_formatted,
            'status' => $status, // Preserve the current status
            'updated_at' => date('Y-m-d H:i:s')
        );

        $this->db->where('id', $data['calendar_id']);
        $this->db->update('calendar_v2_master', $update_data);

        if ($data['target_group'] === 'students') {
            $this->db->where('calendar_v2_master_id', $data['calendar_id']);
            $this->db->delete('calendar_events_v2_override_sessions');

            if (!empty($data['sessions'])) {
                $sessions_data = array();
                foreach ($data['sessions'] as $day => $count) {
                    $sessions_data[] = array(
                        'calendar_v2_master_id' => $data['calendar_id'],
                        'day_name' => $day,
                        'session_count' => $count,
                        'created_at' => date('Y-m-d H:i:s')
                    );
                }

                if (!empty($sessions_data)) {
                    $this->db->insert_batch('calendar_events_v2_override_sessions', $sessions_data);
                }
            }
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return array('status' => 'error', 'message' => 'Failed to update calendar');
        }

        $this->db->trans_commit();
        return array('status' => 'success', 'message' => 'Calendar updated successfully');

    } catch (Exception $e) {
        $this->db->trans_rollback();
        log_message('error', 'Calendar update error: ' . $e->getMessage());
        return array('status' => 'error', 'message' => 'An error occurred while updating calendar');
    }
}

public function getCalendardetails(){
    $result= $this->db_readonly->select("*")
    ->from('calendar_v2_master')
    ->get()->result();
    return $result;

}

public function getevents_calendar_V2($secID,$seldate){
    $result= $this->db_readonly->select("cea.assigned_class_id,cea.assigned_section_id,cea.calendar_v2_master_id,ce.from_date,ce.to_date")
    ->from('calendar_events_v2_assigned cea')
    ->join('calendar_events_v2 ce','cea.calendar_v2_master_id=ce.calendar_v2_master_id')
    ->where('cea.assigned_section_id',$secID)
    ->where('ce.from_date <=', $seldate)
    ->where('ce.to_date >=', $seldate)
    ->get()->row();

    return $result;

}

public function get_calendar_detail($calendar_id) {
    $calendar = $this->db_readonly->select('id, calendar_name, academic_year, target_group,
        DATE_FORMAT(start_date, "%d-%m-%Y") as start_date,
        DATE_FORMAT(end_date, "%d-%m-%Y") as end_date')
        ->from('calendar_v2_master')
        ->where('id', $calendar_id)
        ->get()
        ->row_array();

    if (!$calendar) {
        return array('status' => 'error', 'message' => 'Calendar not found');
    }

    if ($calendar['target_group'] === 'students') {
        $sessions = $this->db_readonly->select('day_name, session_count')
            ->from('calendar_events_v2_override_sessions')
            ->where('calendar_v2_master_id', $calendar_id)
            ->get()
            ->result_array();

        $sessionsArray = array();
        foreach ($sessions as $session) {
            $sessionsArray[$session['day_name']] = $session['session_count'];
        }

        $calendar['sessions'] = $sessionsArray;
    }
    return array('status' => 'success', 'data' => $calendar);
}

public function get_selected_staff_types($calendar_id) {
    return $this->db_readonly->select('assigned_staff_type')
        ->from('calendar_events_v2_assigned')
        ->where('calendar_v2_master_id', $calendar_id)
        ->where('assigned_type', 'STAFF')
        ->get()
        ->result_array();
}

public function deleteCalandarTemplate() {
    $input = $this->input->post();
    $calendar_id = $input['id'];

    // Check for events associated with this calendar template
    $this->db->where('calendar_v2_master_id', $calendar_id);
    $events_query = $this->db->get('calendar_events_v2');
    $has_events = ($events_query->num_rows() > 0);

    // Check for staff type mappings
    $this->db->where('calendar_v2_master_id', $calendar_id);
    $this->db->where('assigned_type', 'STAFF');
    $staff_query = $this->db->get('calendar_events_v2_assigned');
    $has_staff_mappings = ($staff_query->num_rows() > 0);

    // Check for class/section mappings
    $this->db->where('calendar_v2_master_id', $calendar_id);
    $this->db->where('assigned_type', 'SEC');
    $class_query = $this->db->get('calendar_events_v2_assigned');
    $has_class_mappings = ($class_query->num_rows() > 0);

    // If there are events or mappings, return error with details - no force delete option
    if ($has_events || $has_staff_mappings || $has_class_mappings) {
        $message = 'This calendar template cannot be deleted because it has:';
        $details = [];

        if ($has_events) {
            $details[] = $events_query->num_rows() . ' event(s)';
        }

        if ($has_staff_mappings) {
            $details[] = $staff_query->num_rows() . ' staff type mapping(s)';
        }

        if ($has_class_mappings) {
            $details[] = $class_query->num_rows() . ' class/section mapping(s)';
        }

        $message .= ' ' . implode(', ', $details) . '. Please remove these associations before deleting.';

        return [
            'status' => 'error',
            'message' => $message,
            'is_mapped' => true,
            'has_events' => $has_events,
            'has_staff_mappings' => $has_staff_mappings,
            'has_class_mappings' => $has_class_mappings,
            'events_count' => $events_query->num_rows(),
            'staff_mappings_count' => $staff_query->num_rows(),
            'class_mappings_count' => $class_query->num_rows()
        ];
    }

    // If there are no associations, proceed with deletion
    $this->db->trans_begin();

    try {
        // Delete any session overrides
        $this->db->where('calendar_v2_master_id', $calendar_id);
        $this->db->delete('calendar_events_v2_override_sessions');

        // Delete the calendar template itself
        $this->db->where('id', $calendar_id);
        $this->db->delete('calendar_v2_master');

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return [
                'status' => 'error',
                'message' => 'Failed to delete calendar template.'
            ];
        }

        $this->db->trans_commit();
        return [
            'status' => 'success',
            'message' => 'Calendar template deleted successfully.'
        ];

    } catch (Exception $e) {
        $this->db->trans_rollback();
        log_message('error', 'Delete calendar template error: ' . $e->getMessage());
        return [
            'status' => 'error',
            'message' => 'An error occurred while deleting the calendar template: ' . $e->getMessage()
        ];
    }
}

public function get_mapping_info($calendar_id) {
    $sections = $this->db_readonly->select('DISTINCT CONCAT(c.class_name, " ", cs.section_name) as section_name')
        ->from('calendar_events_v2_assigned cea')
        ->join('class c', 'c.id = cea.assigned_class_id', 'inner')
        ->join('class_section cs', 'cs.id = cea.assigned_section_id', 'inner')
        ->where('cea.calendar_v2_master_id', $calendar_id)
        ->where('cea.assigned_type', 'SEC')
        ->get()
        ->result_array();

    $staff_types = $this->db_readonly->select('assigned_staff_type')
        ->from('calendar_events_v2_assigned')
        ->where('calendar_v2_master_id', $calendar_id)
        ->where('assigned_type', 'STAFF')
        ->get()
        ->result_array();

    return array(
        'sections' => !empty($sections) ? array_column($sections, 'section_name') : [],
        'staff_types' => !empty($staff_types) ? array_column($staff_types, 'assigned_staff_type') : []
    );
}

public function delete_event($event_id) {
    $this->db->where('id', $event_id);
    if ($this->db->delete('calendar_events_v2')) {
        return ['status' => 'success', 'message' => 'Event deleted successfully'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to delete event'];
    }
}

public function delete_staff_type($input) {
    $this->db->where('calendar_v2_master_id', $input['id']);
    $this->db->where_in('assigned_staff_type', $input['staff']);
    $this->db->where('assigned_type', 'STAFF');

    if ($this->db->delete('calendar_events_v2_assigned')) {
        return ['status' => 'success', 'message' => 'Staff type(s) deleted successfully'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to delete staff type(s)'];
    }
}

public function delete_class_section($input) {
    $this->db->where('calendar_v2_master_id', $input['id']);
    $this->db->where_in('CONCAT(assigned_class_id, "_", assigned_section_id)', $input['classSection']);
    $this->db->where('assigned_type', 'SEC');

    if ($this->db->delete('calendar_events_v2_assigned')) {
        return ['status' => 'success', 'message' => 'Class/section(s) deleted successfully'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to delete class/section(s)'];
    }
}

public function save_mapping($input) {
    try {
        $calendar_id = $input['calendar_id'];

        $this->db->trans_begin();

        $this->db->where('calendar_v2_master_id', $calendar_id);
        $this->db->delete('calendar_events_v2_assigned');

        $insert_data = [];

        if (!empty($input['staff'])) {
            foreach ($input['staff'] as $staff_type) {
                $insert_data[] = [
                    'calendar_v2_master_id' => $calendar_id,
                    'assigned_staff_type' => $staff_type,
                    'assigned_type' => 'STAFF'
                ];
            }
        }

        if (!empty($input['classSections'])) {
            foreach ($input['classSections'] as $classSection) {
                list($class_id, $section_id) = explode('_', $classSection);
                $insert_data[] = [
                    'calendar_v2_master_id' => $calendar_id,
                    'assigned_class_id' => $class_id,
                    'assigned_section_id' => $section_id,
                    'assigned_type' => 'SEC'
                ];
            }
        }

        if (!empty($insert_data)) {
            $this->db->insert_batch('calendar_events_v2_assigned', $insert_data);
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return ['status' => 'error', 'message' => 'Failed to save mappings'];
        }

        $this->db->trans_commit();
        return ['status' => 'success', 'message' => 'Mappings saved successfully'];

    } catch (Exception $e) {
        $this->db->trans_rollback();
        log_message('error', 'Save mapping error: ' . $e->getMessage());
        return ['status' => 'error', 'message' => 'An error occurred while saving mappings'];
    }
}


public function lock_calendar_template(){
    $input = $this->input->post();
    $calendar_id = $input['calendar_id'];
    $taken_by = $this->authorization->getAvatarStakeHolderId();

    // Get current status
    $current_status = $this->db_readonly->select('status')
        ->from('calendar_v2_master')
        ->where('id', $calendar_id)
        ->get()
        ->row();

    $new_status = ($current_status && $current_status->status == 1) ? 0 : 1;

    $lock_history_row = $this->db_readonly->select('lock_history')
        ->from('calendar_v2_master')
        ->where('id', $calendar_id)
        ->get()
        ->row();

    $lock_history = [];
    if ($lock_history_row && !empty($lock_history_row->lock_history)) {
        $lock_history = json_decode($lock_history_row->lock_history, true);
        if (!is_array($lock_history)) $lock_history = [];
    }

    $history_entry = [
        'avatar_id' => $taken_by,
        'timestamp' => date('Y-m-d H:i:s'),
        'remarks' => $new_status == 1 ? 'locked' : 'unlocked'
    ];
    $lock_history[] = $history_entry;

    $this->db->where('id', $calendar_id);
    $this->db->update('calendar_v2_master', [
        'status' => $new_status,
        'lock_history' => json_encode($lock_history)
    ]);

    return [
        'status' => 'success',
        'message' => $new_status == 1 ? 'Calendar template locked successfully' : 'Calendar template unlocked successfully',
        'lock_status' => $new_status,
        'lock_history' => $lock_history
    ];
}

    public function getAttDataIdWise($id) {
        $sectionResult = $this->db_readonly->select('assigned_section_id')
            ->from('calendar_events_v2_assigned')
            ->where('calendar_v2_master_id', $id)
            ->where('assigned_type', 'SEC')
            ->get()
            ->result();

        if (empty($sectionResult)) {
            return false;
        }

        $sectionIDs = array_column($sectionResult, 'assigned_section_id');
        $attendance_query = $this->db_readonly->select('id')
            ->from('attendance_std_day_v2_session')
            ->where_in('class_section_id', $sectionIDs)
            ->limit(1)
            ->get();

        return $attendance_query->num_rows() > 0;
    }

public function clone_calendar_template($calendar_id) {
    $this->db->trans_begin();

    try {
        // Get the original calendar template
        $original = $this->db_readonly->select('*')
            ->from('calendar_v2_master')
            ->where('id', $calendar_id)
            ->get()
            ->row_array();

        if (!$original) {
            return ['status' => 'error', 'message' => 'Calendar template not found'];
        }

        // Create new calendar name by appending " (Copy)"
        $original['calendar_name'] = $original['calendar_name'] . ' (Copy)';
        unset($original['id']); // Remove the ID to create a new record
        $original['created_at'] = date('Y-m-d H:i:s');
        $original['updated_at'] = null;
        $original['status'] = 0; // Set status to unlocked for the clone

        // Insert the new calendar template
        $this->db->insert('calendar_v2_master', $original);
        $new_calendar_id = $this->db->insert_id();

        // Clone sessions if they exist
        $sessions = $this->db_readonly->select('*')
            ->from('calendar_events_v2_override_sessions')
            ->where('calendar_v2_master_id', $calendar_id)
            ->get()
            ->result_array();

        if (!empty($sessions)) {
            foreach ($sessions as &$session) {
                $session['calendar_v2_master_id'] = $new_calendar_id;
                $session['created_at'] = date('Y-m-d H:i:s');
                unset($session['id']);
            }
            $this->db->insert_batch('calendar_events_v2_override_sessions', $sessions);
        }

        // Clone calendar events
        $events = $this->db_readonly->select('*')
            ->from('calendar_events_v2')
            ->where('calendar_v2_master_id', $calendar_id)
            ->get()
            ->result_array();

        if (!empty($events)) {
            foreach ($events as &$event) {
                $event['calendar_v2_master_id'] = $new_calendar_id;
                unset($event['id']);
            }
            $this->db->insert_batch('calendar_events_v2', $events);
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return ['status' => 'error', 'message' => 'Failed to clone calendar template'];
        }

        $this->db->trans_commit();
        return [
            'status' => 'success',
            'message' => 'Calendar template cloned successfully',
            'new_calendar_id' => $new_calendar_id
        ];

    } catch (Exception $e) {
        $this->db->trans_rollback();
        log_message('error', 'Clone calendar template error: ' . $e->getMessage());
        return ['status' => 'error', 'message' => 'An error occurred while cloning the calendar template'];
    }
}

public function update_event($input) {
    if (empty($input['event_id']) || empty($input['event_name']) || empty($input['event_type']) || empty($input['from_date']) || empty($input['to_date'])) {
        return ['status' => 'error', 'message' => 'Missing required fields'];
    }

    // Convert date format to Y-m-d for DB
    $from_date = date('Y-m-d', strtotime($input['from_date']));
    $to_date = date('Y-m-d', strtotime($input['to_date']));

    $update = [
        'event_name' => $input['event_name'],
        'event_type' => $input['event_type'],
        'from_date' => $from_date,
        'to_date' => $to_date
    ];

    $this->db->where('id', $input['event_id']);
    if ($this->db->update('calendar_events_v2', $update)) {
        return ['status' => 'success', 'message' => 'Event updated successfully'];
    } else {
        return ['status' => 'error', 'message' => 'Failed to update event'];
    }
}

}
?>