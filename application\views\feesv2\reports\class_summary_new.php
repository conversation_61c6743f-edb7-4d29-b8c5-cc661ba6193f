<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Fees Class wise Summary</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-10">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a>
              Class wise Summary
            </h3>
           <!--   <label class="checkbox-inline"><input type="checkbox" name="class_section" id="classSectionID"><span style="font-size:16px; margin-left: 10px;">Class/Sections</span></label> -->
          </div>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">

      <div class="col-md-12">
        <div class="row">
          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: 6px; margin-left:5px;">Select Fee Type <font color="red">*</font></p>
            <select class="form-control select" id="blueprint_type" title="Select Fee Type" required="" name="fee_type[]" multiple="multiple">
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
            <small class="text-muted" style="font-family: poppins; margin-left:5px;">You can select multiple fee types</small>
          </div>

          <div class="col-md-2 form-group">
            
            <input type="button" value="Get Report" id="getReport" class="btn btn-primary" style="margin-top: 25px;">
          </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
      </div>

      <div class="col-md-12 pt-2" style="overflow: hidden;">

        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
        <div id="printArea">
          <div class="total_summary">
          </div>

          <div id="fees_student_status" class="fee_balance pt-3 table-responsive">

          </div>
        </div>

      </div>
    </div>


  </div>
</div>

<script type="text/javascript">
  function exportToExcel_fee_status(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };
    var $clone = $("#printArea").clone();
    $clone.find('.dt-buttons').remove();
    $clone.find('.search-box').remove();

    var $summaryTable = $clone.find('.total_summary table');
    var $mainTable = $clone.find('#fee_summary_data');
    if ($summaryTable.length && $mainTable.length) {
      $('<table><tr><td colspan="100" style="height:30px"></td></tr></table>').insertAfter($summaryTable);
    }


    var mainTable = $clone.html();
    mainTable = mainTable.replace(/<a[^>]*>([^<]*)<\/a>/g, '$1');
    htmls = mainTable;
    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "class_wise_summary_report.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }
</script>

<script>

  var loan_column = '<?php echo $loan_column ?>';
  var adjust = '<?php echo $adjustment ?>';
  var fineAmount_config = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
  var refund =  '<?php echo $feeRefund ?>';
  var completed = 0;
  var total_students = 0;
  var total_fee = 0;
  var total_collected_amount = 0;
  var concession = 0;
  var adjustment = 0;
  var total_fine_amount = 0;
  var total_fine_amount_collected = 0;
  var total_fine_amount_waived = 0;
  var balance = 0;
  var std_count = 0;
  var fineAmount = 0;
  var trfundAmount = 0;
  var total_loan_provider_charges = 0;
  var total_discount = 0;
  var total_assigned_count = 0;
  var fee_type_data = {}; // Store fee type specific data
  var classData = []; // Store class data with student counts
  var grandTotalData = null; // Store grand total data
  $(document).ready(function(){
    // Initialize select2 for multiple selection
    // $('.select').select2({
    //   placeholder: "Select Fee Type(s)",
    //   allowClear: true
    // });

    $('#getReport').on('click',function(){
      var fee_type = $('#blueprint_type').val();
      if (!fee_type || fee_type.length === 0) {
        alert('Please select at least one fee type');
        return false;
      }

      $('.loading-icon').show();
      $('#getReport').prop('disabled', true).val('Please wait...');
      // Reset all counters and totals
      total_students = 0;
      completed = 0;
      total_fee = 0;
      total_collected_amount = 0;
      concession = 0;
      adjustment = 0;
      total_fine_amount = 0;
      total_fine_amount_collected = 0;
      total_fine_amount_waived = 0;
      balance = 0;
      std_count = 0;
      fineAmount = 0;
      trfundAmount = 0;
      total_loan_provider_charges = 0;
      total_discount = 0;
      total_assigned_count = 0;
      fee_type_data = {}; // Store fee type specific data

      $('.fee_balance').html('');
      $('.total_summary').html('');

      $('input[type="checkbox"]').click(function(){
        if($(this).prop("checked") == true){
          $('#classSectionID').val('1');
        }
        else if($(this).prop("checked") == false){
          $('#classSectionID').val('0');
        }
      });

      var classSectionId = '';
      if($(this).prop("checked") == true){
        classSectionId = 1;
      }

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/get_fee_class_summary_student_count'); ?>',
        data: {'fee_type': fee_type, 'classSectionId': classSectionId},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          var response = JSON.parse(data);

          // Store class data with student counts
          classData = response.class_data;

          // Get class IDs for backward compatibility
          classidS = response.class_chunks;

          // Calculate total students for progress bar
          total_students = parseInt(2 * (classidS.length - 2)) + parseInt(classidS[classidS.length - 1].length);

          // Initialize progress bar
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*2+'%';
          $("#progress").show();

          // Start fetching class details
          callReportGetter(0);
        },
        error: function (err) {
          $('.loading-icon').hide();
          console.log(err);
          alert('Error fetching class data. Please try again.');
        }
      });
    });
  });

  function getReport(index) {
    var class_ids = classidS[index];
    var classSectionId = '';
    if($(this).prop("checked") == true){
      classSectionId = 1;
    }
    var fee_type = $('#blueprint_type').val();

    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_fee_class_summary_student_details'); ?>',
      data: {class_ids: class_ids, 'fee_type': fee_type, 'classSectionId': classSectionId},
      type: "post",
      success: function (data) {
        var response = JSON.parse(data);

        // Get class data and grand total
        var classSummaryStudent = response.classes;
        var grandTotal = response.grand_total;

        // Initialize the table header on first call
        if (index == 0) {
          constructConcessionHeader(fee_type);
        }

        // Update totals with this batch of data
        construct_balance_summary(classSummaryStudent);

        // Update progress bar
        completed += classSummaryStudent.length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*2+'%';

        // Add rows to the table
        construct_concession_table(index, classSummaryStudent);

        // If this is the last batch, add the grand total row
        if (index === classidS.length - 1) {
          // Store the grand total for later use
          window.grandTotalData = grandTotal;
        }
      },
      error: function (err) {
        console.log(err);
        alert('Error fetching class details. Please try again.');
      }
    });
  }


  function constructConcessionHeader(fee_types) {
    var h_output = '<table id="fee_summary_data" class="table table-bordered" style="margin-top:10px!important">';
    h_output += '<thead>';
    h_output += '<tr>';
    h_output += '<th rowspan="2">Class</th>';
    h_output += '<th rowspan="2"># of Students Strength</th>';

    if (fee_types && fee_types.length > 0) {
      fee_types.forEach(function(ft) {
        var ftName = $('#blueprint_type option[value="' + ft + '"]').text();
        h_output += '<th colspan="2">' + ftName + '</th>';
      });
    }
    h_output += '<th rowspan="2">Grand Total</th>';
    h_output += '<th rowspan="2">Concession</th>';
    if (loan_column) {
      h_output += '<th rowspan="2">Loan Provider Charges</th>';
    } 
    h_output += '<th rowspan="2">Discount</th>';
    h_output += '<th rowspan="2">Total Fees Received</th>';
    h_output += '<th rowspan="2">Balance</th>';
    if (fineAmount_config) {
      h_output += '<th rowspan="2">Total Fine</th>';
      h_output += '<th rowspan="2">Fine Collected</th>';
      h_output += '<th rowspan="2">Fine Waived</th>';
    }
    h_output += '</tr>';
    h_output += '<tr>';
    if (fee_types && fee_types.length > 0) {
      fee_types.forEach(function() {
        h_output += '<th>Assigned</th>';
        h_output += '<th>Fee Amount</th>';
      });
    }
    h_output += '</tr>';
    h_output += '</thead>';
    h_output += '<tbody>';
    h_output += '</tbody>';
    h_output += '</table>';
    $('.fee_balance').html(h_output);
  }

  function construct_concession_table(index, classSummaryStudent) {
    var fee_types = $('#blueprint_type').val();
    var html = '';
    var j = 0;
    // Add rows for each class
    for(var i = 0; i < classSummaryStudent.length; i++) {
      var classData = classSummaryStudent[i];
      html += '<tr>';
      html += '<td>' + classData.class_name + '</td>';
      html += '<td>' + classData.student_count + '</td>';
      var grandTotalFees = 0;
      var total_collected_amount = 0;
      var totalCollectedAmount = 0;
      var totalConcession = 0;
      var totalBalance= 0;
      var totalLoanProviderCharges= 0;
      var totalTotalFine= 0;
      var totalTotalFineCollected= 0;
      var totalTotalFineWaived= 0;
      var totalTotalDiscount= 0;
      if (fee_types && fee_types.length > 0 && classData.fee_types) {
        fee_types.forEach(function(ft) {
          if (classData.fee_types[ft]) {
            var ftData = classData.fee_types[ft];
            grandTotalFees += parseFloat(ftData.total_fee);
            totalCollectedAmount += parseFloat(ftData.total_collected_amount);
            totalConcession += parseFloat(ftData.concession);
            totalBalance += parseFloat(ftData.balance);
            totalLoanProviderCharges += parseFloat(ftData.loan_provider_charges);
            totalTotalFine += parseFloat(ftData.total_fine);
            totalTotalFineCollected += parseFloat(ftData.total_fine_amount_paid);
            totalTotalFineWaived += parseFloat(ftData.total_fine_waived);
            totalTotalDiscount += parseFloat(ftData.discount);
            html += '<td>' + ftData.assigned_count+ '</td>';
            html += '<td>'+ numberToCurrency(ftData.total_fee) + '</td>';
          } else {
            html += '<td>0</td>';
            html += '<td>0</td>';
          }
        });
      }
      html += '<td>'+numberToCurrency(grandTotalFees)+'</td>';
      html += '<td>'+numberToCurrency(totalConcession)+'</td>';
      if (loan_column) {
        html += '<td>'+numberToCurrency(totalLoanProviderCharges)+'</td>';
      } 

      html += '<td>'+numberToCurrency(totalTotalDiscount)+'</td>';
      html += '<td>'+numberToCurrency(totalCollectedAmount)+'</td>';
      html += '<td>'+numberToCurrency(totalBalance)+'</td>';
      if (fineAmount_config) {
        html += '<td>'+numberToCurrency(totalTotalFine)+'</td>';
        html += '<td>'+numberToCurrency(totalTotalFineCollected)+'</td>';
        html += '<td>'+numberToCurrency(totalTotalFineWaived)+'</td>';
      }
      html += '</tr>';
      j++;
    }

   

    $('#fee_summary_data').append(html);
    index++;
    callReportGetter(index);
  }

  function construct_balance_summary(classSummaryStudent) {
    // Process each class's data
    for(var i = 0; i < classSummaryStudent.length; i++) {
      var classData = classSummaryStudent[i];

      // Add to overall totals
      total_fee += parseFloat(classData.total_fee || 0);
      total_collected_amount += parseFloat(classData.total_collected_amount || 0);
      concession += parseFloat(classData.concession || 0);
      adjustment += parseFloat(classData.adjustment || 0);
      total_fine_amount += parseFloat(classData.total_fine || 0);
      total_fine_amount_collected += parseFloat(classData.total_fine_amount_paid || 0);
      total_fine_amount_waived += parseFloat(classData.total_fine_waived || 0);
      balance += parseFloat(classData.balance || 0);
      std_count += parseFloat(classData.student_count || 0);
      total_assigned_count += parseFloat(classData.assigned_count || 0);
      total_loan_provider_charges += parseFloat(classData.loan_provider_charges || 0);
      trfundAmount += parseFloat(classData.refund_amount || 0);
      total_discount += parseFloat(classData.discount || 0);

      // Process fee type specific data if available
      if (classData.fee_types) {
        for (var ft_id in classData.fee_types) {
          if (!classData.fee_types.hasOwnProperty(ft_id)) continue;

          // Initialize fee type data structure if not exists
          if (!fee_type_data[ft_id]) {
            fee_type_data[ft_id] = {
              total_fee: 0,
              total_collected_amount: 0,
              concession: 0,
              discount: 0,
              balance: 0,
              assigned_count: 0,
              loan_provider_charges: 0,
              total_fine: 0,
              total_fine_amount_paid: 0,
              total_fine_waived: 0
            };
          }

          // Add to fee type totals
          var ftData = classData.fee_types[ft_id];
          fee_type_data[ft_id].total_fee += parseFloat(ftData.total_fee || 0);
          fee_type_data[ft_id].total_collected_amount += parseFloat(ftData.total_collected_amount || 0);
          fee_type_data[ft_id].concession += parseFloat(ftData.concession || 0);
          fee_type_data[ft_id].discount += parseFloat(ftData.discount || 0);
          fee_type_data[ft_id].balance += parseFloat(ftData.balance || 0);
          fee_type_data[ft_id].assigned_count += parseFloat(ftData.assigned_count || 0);
          fee_type_data[ft_id].loan_provider_charges += parseFloat(ftData.loan_provider_charges || 0);
          fee_type_data[ft_id].total_fine += parseFloat(ftData.total_fine || 0);
          fee_type_data[ft_id].total_fine_amount_paid += parseFloat(ftData.total_fine_amount_paid || 0);
          fee_type_data[ft_id].total_fine_waived += parseFloat(ftData.total_fine_waived || 0);
        }
      }
    }
  }

  function callReportGetter(index){
    if(index < classidS.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $('#exportIcon').show();
      $('#send_fee_sms').show();

      // Get fee type names for the summary
      var fee_types = $('#blueprint_type').val();
      var fee_type_names = [];
      fee_types.forEach(function(ft) {
        fee_type_names.push($('#blueprint_type option[value="' + ft + '"]').text());
      });

      // Create summary table with enhanced information
      con_summary = '<div class="table-responsive"">';
      con_summary += '<h4 style="font-weight: 600; font-family: Poppins, sans-serif; text-transform: uppercase; font-size: 1rem; color: #555; border-left: 4px solid #0d6efd; padding-left: 8px; margin-bottom: 12px;">Summary</h4>';
      con_summary +='<table class="table table-bordered">';
      con_summary +='<thead>';
      con_summary +='<tr>';
      con_summary += '<th>Total Students</th>';
      con_summary += '<th>Assigned Students</th>';
      con_summary += '<th>Total Fee Amount</th>';
      con_summary += '<th>Total Collected Amount</th>';
      if (refund) {
         con_summary +='<th>Total Refund</th>';
      }
      con_summary += '<th>Total Concession</th>';
      if (adjust) {
        con_summary += '<th>Total Adjustment</th>';
      }
      if (loan_column) {
         con_summary += '<th>Loan Provider Charges</th>';
      }
      con_summary += '<th>Total Discount</th>';
      con_summary += '<th>Total Balance</th>';
      if (fineAmount_config) {
         con_summary +='<th>Total Fine</th>';
         con_summary +='<th>Fine Collected</th>';
         con_summary +='<th>Fine Waived</th>';
      }
      con_summary +='</tr>';
      con_summary +='</thead>';
      con_summary +='<tbody>';
      con_summary += '<tr>';
      con_summary +='<th>'+std_count+'</th>';
      con_summary +='<th>'+total_assigned_count+'</th>';
      con_summary +='<th>'+numberToCurrency(total_fee)+'</th>';
      con_summary +='<th>'+numberToCurrency(total_collected_amount - total_loan_provider_charges)+'</th>';
      if (refund) {
        con_summary +='<th>'+numberToCurrency(trfundAmount)+'</th>';
      }
      con_summary +='<th> ( '+numberToCurrency(concession)+' ) </th>';
      if (adjust) {
        con_summary +='<th> ( '+numberToCurrency(adjustment)+' ) </th>';
      }
      if (loan_column) {
        con_summary += '<th>'+numberToCurrency(total_loan_provider_charges)+'</th>';
      }
      con_summary +='<th>'+numberToCurrency(total_discount)+'</th>';
      con_summary +='<th>'+numberToCurrency(balance)+'</th>';
      if (fineAmount_config) {
        con_summary +='<th> ( '+numberToCurrency(total_fine_amount)+' ) </th>';
        con_summary +='<th> ( '+numberToCurrency(total_fine_amount_collected)+' ) </th>';
        con_summary +='<th> ( '+numberToCurrency(total_fine_amount_waived)+' ) </th>';
      }
      con_summary += '</tr>';
      con_summary +='</tbody>';
      con_summary +='</table>';

      // Add fee type specific summary if multiple fee types are selected
      if (fee_types && fee_types.length > 1) {
        con_summary += '<h4 style="font-weight:600; font-family: Poppins, sans-serif; text-transform: uppercase; font-size: 1rem; color: #555; border-left: 4px solid #0d6efd; padding-left: 8px; margin-top:24px; margin-bottom: 12px;">Fee type</h4>';
        con_summary += '<table class="table table-bordered" style="margin-top:16px">';
        con_summary += '<thead><tr>';
        con_summary += '<th>Fee Type</th>';
        con_summary += '<th>Assigned Students</th>';
        con_summary += '<th>Total Fee</th>';
        con_summary += '<th>Collected</th>';
        con_summary += '<th>Concession</th>';
        if (loan_column) {
          con_summary += '<th>Loan Provider Charges</th>';
        }
        con_summary += '<th>Discount</th>';
        con_summary += '<th>Balance</th>';
        if (fineAmount_config) {
          con_summary +='<th>Total Fine</th>';
          con_summary +='<th>Fine Collected</th>';
          con_summary +='<th>Fine Waived</th>';
        }
        con_summary += '</tr></thead><tbody>';

        fee_types.forEach(function(ft) {
          if (fee_type_data[ft]) {
            var ftData = fee_type_data[ft];
            var ftName = $('#blueprint_type option[value="' + ft + '"]').text();

            con_summary += '<tr>';
            con_summary += '<td>' + ftName + '</td>';
            con_summary += '<td>' + ftData.assigned_count + '</td>';
            con_summary += '<td>' + numberToCurrency(ftData.total_fee) + '</td>';
            con_summary += '<td>' + numberToCurrency(ftData.total_collected_amount) + '</td>';
            con_summary += '<td> ( ' + numberToCurrency(ftData.concession) + ' ) </td>';
            if (loan_column) {
              con_summary += '<th>'+numberToCurrency(ftData.loan_provider_charges)+'</th>';
            }
            con_summary += '<td>' + numberToCurrency(ftData.discount) + '</td>';
            con_summary += '<td>' + numberToCurrency(ftData.balance) + '</td>';
            if (fineAmount_config) {
              con_summary +='<td> ( '+numberToCurrency(ftData.total_fine)+' ) </th>';
              con_summary +='<td> ( '+numberToCurrency(ftData.total_fine_amount_paid)+' ) </th>';
              con_summary +='<td> ( '+numberToCurrency(ftData.total_fine_waived)+' ) </th>';
            }
            con_summary += '</tr>';
          }
        });

        con_summary += '</tbody></table>';
      }

      con_summary += '</div>';
      $(".total_summary").html(con_summary);
      $('#getReport').prop('disabled', false).val('Get Report');

      // Initialize DataTable with export options
      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        paging: false,
        searching: false,
        dom: 'Bfrtip',
        info: false,
        buttons: [
          {
            text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            title: 'Fee Class wise Summary report',
            footer: true,
            exportOptions: {
              columns: ':visible',
            },
            action: function () {
              var printcontent = document.getElementById('printArea').innerHTML;
              var printWindow = window.open('', '_blank');

              printWindow.document.open();
              printWindow.document.write(`
                <html>
                  <head>
                    <title>Fee Class wise Summary report</title>
                    <style>
                      body {
                        padding: 20px;
                      }
                      table {
                        width: 100%;
                        border-collapse: collapse;
                      }
                      th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        font-size: 12px;
                      }
                      h3 { margin: 15px 0; }
                      @media print {
                        .dt-buttons, .search-box { display: none !important; }
                        table { page-break-inside: auto }
                        tr { page-break-inside: avoid }
                      }
                    </style>
                  </head>
                  <body>
                    <h3>Fee Class wise Summary report</h3>
                    ${printcontent}
                    <script>
                      window.onload = function() {
                        window.print();
                      };
                      window.onafterprint = function() {
                        window.close();
                      };
                    <\/script>
                  </body>
                </html>
              `);
              printWindow.document.close();
            }

          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
            title: 'Fee Class wise Summary report',
            action: function () {
              exportToExcel_fee_status();
            },
          },
        ],
      });
      // initializeColVisButton(table, 'fee_summary_data_wrapper');

      // Insert custom search bar to the left of Print/Excel buttons
      let $dtButtons = $('#fee_summary_data_wrapper .dt-buttons');
      if ($dtButtons.find('.search-box').length === 0) {
        $dtButtons.prepend(`
          <div class="search-box" style="display:inline-block; margin-right:6px;">
            <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
          </div>
        `);
      }

      $dtButtons.off('keyup', '#table-search').on('keyup', '#table-search', function() {
        const searchText = $(this).val().toLowerCase();
        $('#fee_summary_data tbody tr').each(function() {
          let rowVisible = false;
          $(this).find('td').each(function() {
            if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
              rowVisible = true;
              return false;
            }
          });
          $(this).toggle(rowVisible);
        });
      });
    }
  }

$(document).off('keyup', '#table-search').on('keyup', '#table-search', function() {
  const searchText = $(this).val().toLowerCase();
  $('#fee_summary_data tbody tr').each(function() {
    let rowVisible = false;
    $(this).find('td').each(function() {
      if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
        rowVisible = true;
      }
    });
    $(this).toggle(rowVisible);
  });
});

function initializeColVisButton(table, wrapperId) {
  // Add a column visibility button to the given table
  new $.fn.dataTable.Buttons(table, {
    buttons: [
      {
        extend: 'colvis',
        text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
        className: 'btn btn-info',
      },
    ],
  }).container().appendTo($(`#${wrapperId} label`));
}
</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>

<style>
 @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

  table, #fee_summary_data, .total_summary table {
      font-family: 'Poppins', sans-serif !important;
  }

  .gap-left {
    margin-left: 20px !important;
  }

  .search-box {
    display: inline-block;
    position: relative;
    margin-right: 2px;
    vertical-align: middle;
  }

  .input-search {
    line-height: 1.5;
    padding: 5px 10px;
    display: inline;
    width: 177px;
    height: 27px;
    background-color: #f2f2f2 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-right: 0 !important;
    font-size: 14px;
    color: #495057;
    outline: none;
  }

  .input-search::placeholder {
    color: rgba(73, 80, 87, 0.5);
    font-size: 14px;
    font-weight: 300;
  }

  #fee_summary_data, .total_summary table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
  }

  #fee_summary_data thead th, .total_summary table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
  }

  #fee_summary_data th, #fee_summary_data td, .total_summary table th, .total_summary table td {
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
  }

  #fee_summary_data tbody tr:nth-child(even), .total_summary table tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  #fee_summary_data tbody tr:hover, .total_summary table tbody tr:hover {
    background-color: #f1f5f9;
  }

  #fee_summary_data tfoot tr, .total_summary table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .buttons-print,
  .buttons-colvis {
    padding: 2px !important;
  }

  .buttons-excel {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .dt-button {
    border: none !important;
    background: none !important;
    margin: 0 -2px 5px -2px;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .dt-button .btn {
    line-height: 20px;
  }

  .dt-buttons {
    text-align: right;
    float: right;
    /*position: absolute;*/
  }

  .dataTables_scrollHeadInner table {
    margin: 0px;
  }
</style>