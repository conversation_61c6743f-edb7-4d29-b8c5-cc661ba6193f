<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  25 Sept 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Admission_staff_controller extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		$this->load->model('admission_staff_model');
		$this->load->model('Admission_model');
		$this->config->load('form_elements');
        $this->load->library('filemanager');
    }

    public function mobile_registration(){
        $data['enquiry_module'] = $this->authorization->isModuleEnabled('ENQUIRY');
    	$data['main_content'] = 'admission/staff/application/users_mobile';
      	$this->load->view('inc/template', $data);
    }

    public function mobile_registered(){
       $enquiry_id =  $this->input->post('enquiry_id');
       if (empty($enquiry_id)) 
        $enquiry_id =0;
    	$au_Id = $this->admission_staff_model->insert_register_mobile();
    	if ($au_Id) {
    		redirect('admission_staff_controller/select_appllication_class/'.$au_Id.'/'.$enquiry_id); 
    	}else{
    		$this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('admission_staff_controller/mobile_registration'); 
    	}
    }

    public function select_appllication_class($au_Id,$eId){
        $data['au_Id'] = $au_Id;
    	$data['eId'] = $eId;
        $data['open_for_admissions'] = $this->settings->getSetting('open_for_admissions');
    	$data['admissions'] = $this->Admission_model->admission_settings_get();
        // echo "<pre>"; print_r($data['admissions']); die();
    	$data['main_content'] = 'admission/staff/application/select_class';
      	$this->load->view('inc/template', $data);
    }

    public function register_new_form($au_Id){
        $admSettingId = $this->input->post('admission_setting_id');
        $eId = $this->input->post('enquiry_id');
    	$lastId = $this->Admission_model->create_id_admission_offline_form($au_Id, $admSettingId, $eId);
    	if ($lastId) {
    		 redirect('Admission_staff_controller/new_form/'.$au_Id.'/'.$admSettingId.'/'.$lastId.'/'.$eId); 
    	}else{
    		$this->session->set_flashdata('flashError', 'Something went wrong');
            redirect('Admission_staff_controller/select_appllication_class/'.$au_Id); 
    	}
    }

    private function _get_admissions_settings_byId($id){
      return  $this->Admission_model->admission_settings_getbyId($id);
    }

	public function new_form($au_Id, $admSettingId,$lastId, $eId){
        if ($eId !=0) {
           $data['enquiry'] = $this->Admission_model->get_enquiry_form_data($eId);
        }
	 	$data['au_id']=$au_Id;
        $data['insert_id']=$lastId;
        $data['eId']=$eId;
        $data['admission_setting_id']=$admSettingId;
		$data['config_val'] = $this->_get_admissions_settings_byId($admSettingId);
	 	$data['class_applied_for'] = json_decode($data['config_val']['class_applied_for'], true);
        // echo "<pre>"; print_r($data['config_val']); die();
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        $data['documents_required_fields'] = json_decode($data['config_val']['documents_required_fields'], true);
        $doc = array();
        if($data['config_val']['document_input_version'] == 'V1'){
            $data['documents'] = json_decode($data['config_val']['documents'], true);
            if(!empty($data['documents'])){
                foreach ($data['documents'] as $key => $docs) {
                    $doc[$docs] = $docs;
                } 
            }
        }else{
            $data['documents'] = json_decode($data['config_val']['documents']);
            if (!empty($data['documents'])) {
                foreach ($data['documents'] as $key => $docs) {
                    $doc[$docs->name] = $docs->name;
                }
            }
        }
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['need_qualification_dropdown']=$this->Admission_model->get_need_qualification($data['admission_setting_id']);
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructin_file'] = $data['config_val']['instruction_file'];
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);
        $data['admission_medical'] = $this->Admission_model->get_admission_form_medical_details($lastId);
        $data['show_previous_schooling_overall_total_marks'] = $data['config_val']['show_previous_schooling_overall_total_marks'];
        $data['show_previous_schooling_overall_grade'] = $data['config_val']['show_previous_schooling_overall_grade'];
        $data['previous_schooling_classes'] = $data['config_val']['previous_schooling_classes'];
        $data['show_previous_schooling_subjects'] = $data['config_val']['show_previous_schooling_subjects'];

        $data['show_previous_schooling_subject_total_marks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_percentage'] = $data['config_val']['show_previous_schooling_subject_percentage'];
        
        // echo "<pre>"; print_r($data['show_previous_schooling_subjects']);
        //  die();
        $data['show_previous_schooling_overall_percentage'] = $data['config_val']['show_previous_schooling_overall_percentage'];

        if ($data['final_preview']->std_name=='') {
            $data['final_preview']->std_name = (isset($data['enquiry']->student_name)=='')?'':$data['enquiry']->student_name;
        }
        if ($data['final_preview']->student_last_name=='') {
            $data['final_preview']->student_last_name = (isset($data['enquiry']->student_last_name)=='')?'':$data['enquiry']->student_last_name;
        }
        if ($data['final_preview']->gender=='') {
            $data['final_preview']->gender = (isset($data['enquiry']->gender)=='')?'':$data['enquiry']->gender;
        }
        if ($data['final_preview']->dob =='00-00-0000') {
            $data['final_preview']->dob = (isset($data['enquiry']->student_dob)=='')?'':$data['enquiry']->student_dob;
        }
        if ($data['final_preview']->grade_applied_for =='') {
            $data['final_preview']->grade_applied_for = (isset($data['enquiry']->class_name)=='')?'':$data['enquiry']->class_name;
        }
        if ($data['final_preview']->curriculum_currently_studying =='') {
            $data['final_preview']->curriculum_currently_studying = (isset($data['enquiry']->board_opted)=='')?'':$data['enquiry']->board_opted;
        }      
        if ($data['final_preview']->boarding =='') {
            $data['final_preview']->boarding = (isset($data['enquiry']->boarding_type)=='')?'':$data['enquiry']->boarding_type;
        }
        if ($data['final_preview']->student_email_id =='') {
            $data['final_preview']->student_email_id = (isset($data['enquiry']->student_email_id))?$data['enquiry']->student_email_id:'';
        }
        if ($data['final_preview']->student_mobile_no =='') {
            $data['final_preview']->student_mobile_no = (isset($data['enquiry']->student_phone_number)=='')?'':$data['enquiry']->student_phone_number;
        }
        if ($data['final_preview']->f_name =='') {
            $data['final_preview']->f_name =(isset($data['enquiry']->parent_name))? ((empty($data['enquiry']->parent_name)) ? $data['enquiry']->father_name : $data['enquiry']->parent_name):'';
        }
        if ($data['final_preview']->m_name =='') {
            $data['final_preview']->m_name =(isset($data['enquiry']->mother_name))? $data['enquiry']->mother_name : '';
        }
        if ($data['final_preview']->f_mobile_no =='' && isset($data['enquiry'])) {
            $data['final_preview']->f_mobile_no = !empty($data['enquiry']->mobile_number) ? $data['enquiry']->mobile_number : $data['enquiry']->father_phone_number ;
        }
        if ($data['final_preview']->m_mobile_no =='') {
            $data['final_preview']->m_mobile_no = (isset($data['enquiry']->mother_phone_number))? $data['enquiry']->mother_phone_number : '';
        }
        if ($data['final_preview']->f_email_id =='') {
            $data['final_preview']->f_email_id =(isset($data['enquiry']->email))? ((empty($data['enquiry']->email)) ? $data['enquiry']->father_email_id : $data['enquiry']->email):'';
        }
        if ($data['final_preview']->m_email_id =='') {
            $data['final_preview']->m_email_id =(isset($data['enquiry']->mother_email_id))? $data['enquiry']->mother_email_id :'';
        }
        if ($data['final_preview']->curriculum_interested_in =='') {
            $data['final_preview']->curriculum_interested_in =(isset($data['enquiry']->interested_in))? $data['enquiry']->interested_in :'';
        }
        if ($data['final_preview']->transport =='') {
            $data['final_preview']->transport =(isset($data['enquiry']->transportation_facility))? $data['enquiry']->transportation_facility :'';
        }
        if ($data['final_preview']->category =='') {
            $data['final_preview']->category =(isset($data['enquiry']->category))? $data['enquiry']->category :'';
        }
        if ($data['final_preview']->student_caste =='') {
            $data['final_preview']->student_caste =(isset($data['enquiry']->caste))? $data['enquiry']->caste :'';
        }
        
        if ($data['final_preview']->s_present_addr =='') {
            $data['final_preview']->s_present_addr =(isset($data['enquiry']->residential_address))? $data['enquiry']->residential_address :'';
        }
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['admission_stream'] = $this->Admission_model->get_admission_form_combinations($lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admSettingId, $data['final_preview']->grade_applied_for);
        // echo "<pre>"; print_r($data['lang_selection']); die();
      
        if(!empty($data['prev_eduction_info']['class'])){
            if (!empty($data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for])) {
                $data['previous_subjects'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for];
            }
        }
        if (!empty($data['prev_eduction_info'])) {
            $data['previous_year_settings'] = $data['prev_eduction_info']['year'];
        }else{
            $data['previous_year_settings'] = null;
        }

        $years = array();
        if (!empty($data['prev_eduction_info']['year'])) {
            foreach ($data['prev_eduction_info']['year'] as $key => $year) {
                $years[$year] = $year;
            }
        }
        $ryears = array();
        if (!empty($data['admission_prev_schools'])) {
            foreach ($data['admission_prev_schools'] as $key => $pSchool) {
                $ryears[$pSchool->year] = $pSchool->year;
            }
        }
        $result=array_diff($years,$ryears);
        $data['yearinfo'] = $result;

                
        $rdocs = array();
        if($data['config_val']['document_input_version'] == 'V1'){
            if (!empty($data['admission_form'])) {
                foreach ($data['admission_form'] as $key => $document) {
                    $rdocs[$document->document_type] = $document->document_type;
                    if(!in_array($document->document_type, $data['documents'], true)){
                        array_push($data['documents'], $document->document_type);
                    }
                }
            }
        }else{
            $count = count($data['documents']);
            if (!empty($data['admission_form'])) {
                foreach ($data['admission_form'] as $key => $document) {
                    $rdocs[$document->document_type] = $document->document_type;
                    if(!in_array($document->document_type, $doc, true)){
                        array_push($data['documents'],(object) ['name' => $document->document_type]);
                        $count++;
                    }
                }
            }
        }

        $caste = $this->Admission_model->load_caste();
        $Category = $this->settings->getSetting('category');
        $categoryOptions=[];

        // final_preview
        if(!empty($caste) && !empty($Category)){
            foreach ($caste as $key => $value) {
                foreach ($Category as $k => $v) {
                if(!array_key_exists($value->category,$categoryOptions) && $value->category == $v){
                    $object = new stdClass();
                    $object->category = ucwords($value->category);
                    $object->value = $k;
                    $categoryOptions[$value->category]=$object;
                }
                }
            }
        }

        $casteOptions = [];
        if(!empty($caste)){
            foreach ($caste as $key => $value) {
                if (!array_key_exists($value->caste, $casteOptions)) {
                    $object = new stdClass();
                    $object->category = $value->category;
                    $object->caste = ucwords($value->caste);
                    $casteOptions[$value->caste] = $object;
                }
            }
        }
        // echo '<pre>';print_r($casteOptions);die();
        
        $subCasteOptions = [];
        foreach ($caste as $key => $value) {
            if (!array_key_exists($value->sub_caste, $subCasteOptions)) {
                $object = new stdClass();
                $object->caste = $value->caste;
                $object->sub_caste = ucwords($value->sub_caste);
                $subCasteOptions[$value->sub_caste] = $object;
            }
        }

        $data['caste'] = $caste;
        $data['categoryOptions'] = $categoryOptions;
        $data['casteOptions'] = $casteOptions;
        $data['subCasteOptions']=$subCasteOptions;

        if(!empty($data['caste'])){
            $student_caste_present_in_db=true;
        }else{
            $student_caste_present_in_db = false;
        }

        $data['student_caste_present_in_db']=$student_caste_present_in_db;

        $document = array_diff($doc,$rdocs);
        $data['doucments'] = $document;
        $required_fields = $this->Admission_model->get_admission_required_fields();
        $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
        // echo "<pre>"; print_r($data['required_fields']); die();
        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        $required_fields = $this->Admission_model->get_health_required_fields_admission();
        $data['health_required_fields'] = $this->__construct_name_wise_health_required($required_fields);
        $data['health_disabled_fields'] = $this->Admission_model->get_health_disabled_fields_admission();
        $data['admission_vaccination'] = $this->Admission_model->get_admission_form_vaccination_details($lastId);
        $data['freez_parent_details'] = 0;
        $data['boards'] = $this->settings->getSetting('admission_board');
        $data['image_size_in_admissions'] = $this->settings->getSetting('image_size_in_admissions');
        if($data['image_size_in_admissions'] == ''){
            $data['image_size_in_admissions'] = 2;
        }
        $data['documents_size_in_admissions'] = $this->settings->getSetting('documents_size_in_admissions');
        if($data['documents_size_in_admissions'] == ''){
            $data['documents_size_in_admissions'] = 2;
        }
        $freez_primary_fields = $this->settings->getSetting('freez_primary_fields');
        $data['freez_primary_fields'] = explode(',', $freez_primary_fields);
        // echo "<pre>"; print_r($data['health_required_fields']); die();
      	$data['main_content'] = 'admission/staff/application/index';
      	$this->load->view('inc/template_fee', $data);
	}

    private function __construct_name_wise_health_required($requiredData){
        $fields = $this->db->list_fields('student_health');
        $rData = [];
        foreach ($fields as $key => $val) {
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
            }else{
                $rData[$val] = array('font' =>'', 'required' =>'');
            }
        }
        return $rData;
    }

    public function update_medical_form_details(){
        $input = $this->input->post();

        echo $this->Admission_model->insert_medical_form_details($input);
    }

    public function fetch_stream_based_on_combinations(){
        $admission_setting_id = $_POST['admission_setting_id'];
        $combination = $_POST['combination'];

        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
        $streams = json_decode($config_val['streams'], true);
         if (!empty($streams)) {
            $subjectArray = $streams;
        }
        if (!empty($combination)) {
            $subjectArray = $streams[$combination];
        }
        echo json_encode($subjectArray);
    }

	public function s3FileUpload($file) {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'Admission_form_document');
    }
	

    public function updateStudentDetails_erp(){
    	$input = $this->input->post();
        $student_photo_path = '';
        // if(isset($_FILES['student_photo'])){
        //   $student_photo_path = $this->s3FileUpload($_FILES['student_photo']);
        // }

        $sResigedPhoto['file_name'] = '';
        if(isset($_FILES['student_photo'])){
          $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);
          $picture = array('tmp_name' => $min_size, 'name' => $_FILES['student_photo']['name']);
          $sResigedPhoto = $this->s3FileUpload($picture);
        }

        $family_photo_path = '';
        if(isset($_FILES['family_photo'])){
          $family_photo_path = $this->s3FileUpload($_FILES['family_photo']);
        }

        $stud_sign_path = '';
        if(isset($_FILES['student_signature'])){
          $stud_sign_path = $this->s3FileUpload($_FILES['student_signature']);
        }
    	echo $this->Admission_model->insert_student_details($input,$student_photo_path,$family_photo_path,$sResigedPhoto,$stud_sign_path);
    }

    public function update_parent_details_erp(){
    	$input = $this->input->post();
        $mSignature = '';
        if (isset($_FILES['m_signature'])) {
         $mSignature = $this->s3FileUpload($_FILES['m_signature']);
        }
        $fSignature = '';
        if (isset($_FILES['f_signature'])) {
         $fSignature = $this->s3FileUpload($_FILES['f_signature']);
        }
        $f_photo = '';
        if (isset($_FILES['father_photo'])) {
         $f_photo = $this->s3FileUpload($_FILES['father_photo']);
        }
        $m_photo = '';
        if (isset($_FILES['mother_photo'])) {
         $m_photo = $this->s3FileUpload($_FILES['mother_photo']);
        }
    	echo $this->Admission_model->insert_parent_details($input, $mSignature, $fSignature,$f_photo,$m_photo);
    }

    public function get_parent_data(){
        $result = $this->Admission_model->get_parent_data($_POST['af_id']); 
        echo json_encode($result); 
    }

    public function update_guardian_details_erp(){
        $input = $this->input->post();
        $gPhoto = '';
        if (isset($_FILES['guardian_photo'])) {
         $gPhoto = $this->s3FileUpload($_FILES['guardian_photo']);
        }
        echo $this->Admission_model->insert_guardian_details($input, $gPhoto);
    }
    public function update_documents_erp(){
    	$input = $this->input->post();
    	echo $this->Admission_model->insert_documents($input,$this->s3FileUpload($_FILES['document_photo']));
    }

  
    public function update_previous_school_details_erp(){
        $input = $this->input->post();
        echo $this->Admission_model->insert_prevous_school_details($input);
    }

    public function get_admission_form_document_erp(){
        $afid = $this->input->post('afid');
        $result = $this->Admission_model->get_admission_form_detailsby_auId($afid);
        echo json_encode($result);
    }

    public function delete_documentbyId_erp(){
        $d_id = $this->input->post('d_id');
        echo $this->Admission_model->delete_document_byid($d_id);
    }


    public function get_previous_school_details_erp(){
        $afid = $this->input->post('afid');
        $result = $this->Admission_model->get_admission_form_previous_school_details($afid);
        echo json_encode($result);
    }

    public function delete_details_schooling_byId_erp(){
        $apsid = $this->input->post('apsid');
        echo $this->Admission_model->delete_schooling_details_byid($apsid);
    }

    public function dispaly_stream_based_on_combinations_erp(){
        $input = $this->input->post();
        $config_val = $this->_get_admissions_settings_byId($input['admission_setting_id']);
        $streams = json_decode($config_val['streams'], true);        
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($input['au_id'],$input['lastId']);

        if (!empty($streams)) {
            $subjectArray = $streams;
        }
        if (!empty($input['combination'])) {
            $subjectArray = $streams[$input['combination']];
            $template = '';
            $i = 1;
            foreach ($subjectArray as $key => $val) { 
                $template .= "<table class='table table-bordered' style='margin-bottom:8px'>";
                $template .= "<tr>";
                $template .= "<td style='width: 6%; vertical-align: middle;'>".$i++."</td>";
                $template .= "<td style='text-align: left; vertical-align: middle;'>";
                $template .= $val['name'];
                $template .= "</td>";  
                if (!empty($input['combination_id'])) {
                    if ($input['combination_id'] == $val['id']) {
                        $template .= "<td style='width:6%; vertical-align: middle; padding: 4px;'><label style='margin-top: 7px;'><input type='radio' checked name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                    }else{
                        $template .= "<td style='width:6%; vertical-align: middle; padding: 4px; '><label style='margin-top: 7px;'><input required='' type='radio' name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                    }
                }else{
                    $template .= "<td style='width:6%; vertical-align: middle; padding: 4px;'><label style='margin-top: 7px;'><input type='radio' required='' name='checkvalue' value=".$val['id']." id='checkradio' style='width: 20px; height: 16px;' ></label></td>";
                }
               
                $template .= "</tr>"; 
                $template .= "</table>";  
            }
            print($template);
        }

    }

    public function doucment_grading_dispaly_erp(){
        $input = $this->input->post();
        $data['au_id']=$input['au_id'];
        $data['insert_id']=$input['lastId'];
        $data['admission_setting_id']=$input['admission_setting_id'];

        $config_val = $this->_get_admissions_settings_byId($input['admission_setting_id']);

        $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);

        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($data['au_id'],$data['insert_id']);

        $template = '';
        $template .= '<table class="table">';
        $template .= '<tr>';
        $template .= '<th>Subject</th>';
        $template .= '<th>Percent</th>';
        $template .= '<th>Grade</th>';
        $template .='</tr>';

        $subjectArray = $prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'];
        foreach ($subjectArray as $subject) {
          $template.= '<tr>';
          $template .= '<td>';
          $template .= ' <input type="hidden" value="'. $subject['id'] . '" name="sub_id[]">';
          switch ($subject['type']) {
            case 'label':
              $template .= $subject['name'];
              $template .= ' <input type="hidden" value="'. $subject['name'] . '" name="sub_name[]">';
              break;
            case 'text': 
              $template .= ' <input type="text" class="form-control" placeholder="'.$subject['name'].'" value="" name="sub_name[]">';
          }
          $template .= '</td>';
          $template.= '<td><input type="number"  min="1" max="100" name="percentage[]" placeholder="%" class="form-control per"></td>';
          $template.= '<td><input type="text" style="text-transform:uppercase; width:64px" name="grades[]" placeholder="Grade" class="form-control grd"></td>';

          $template.= '</tr>';

        }
        $template .= '</table>';
        print($template);
    }

     public function doucment_grading_dispaly(){
       if (!empty($this->input->post())) {
            $lastId = $this->input->post('lastId');
            $admission_setting_id = $this->input->post('admission_setting_id');
        }else{
            $admission_setting_id = $this->session->userdata('admission_setting_id');
            $lastId = $this->session->userdata('lastId');
        }
        $au_Id = $this->session->userdata('admission_user_id');
        $data['au_id']=$au_Id;
        $data['insert_id']=$lastId;
        $data['admission_setting_id']=$admission_setting_id;

        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);

        $preSchoolingmarks = $config_val['show_previous_schooling_overall_total_marks'];

        // $show_previous_schooling_subjects = $config_val['show_previous_schooling_subjects'];

        $show_previous_schooling_subject_total_marks = $config_val['show_previous_schooling_subject_total_marks'];

        $show_previous_schooling_subject_percentage = $config_val['show_previous_schooling_subject_percentage'];

        $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);
        $final_preview = $this->get_admission_form_staff_detailsby_all_auId($lastId);
        $template = '';
        $template .= '<table class="table">';
        $template .= '<tr>';
        $template .= '<th>Subject</th>';


        if ($show_previous_schooling_subject_total_marks == 1) {
            $template .= '<th>Max Marks</th>';
            $template .= '<th>Marks Scored</th>';

        }
        if($show_previous_schooling_subject_percentage == 1){
            $template .= '<th>Percent</th>';
            $template .= '<th>Grade</th>';
        }

        $template .='</tr>';
        $subjectArray = $prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'];
        $m = 1;
        foreach ($subjectArray as $subject) {
            $template.= '<tr>';
            $template .= '<td>';
            $template .= ' <input type="hidden" value="'. $subject['id'] . '" name="sub_id[]">';
            switch ($subject['type']) {
                case 'label':
                  $template .= $subject['name'];
                  $template .= ' <input type="hidden" value="'. $subject['name'] . '" name="sub_name[]">';
                  break;
                case 'text': 
                  $template .= ' <input type="text" class="form-control" onkeyup="check_is_value_not_empty(this,'.$m.')" placeholder="'.$subject['name'].'" value="" name="sub_name[]">';
            }
            $template .= '</td>';

            if ($show_previous_schooling_subject_total_marks == 1) {
                $template.= '<td><input type="number" data-parsley-error-message="Invalid number" id="maxMarks_'.$m.'" onkeyup="max_marks_total('.$m.')"name="max_marks[]" value="0" class="form-control maxMarks per"></td>';
                $template.= '<td><input type="number" data-parsley-error-message="Invalid number" id="maxMarkScored_'.$m.'" onkeyup="max_marks_scored_total('.$m.')" name="marks_scored[]" value="0" class="form-control maxMakrsScored per"></td>';
            }
            if ($show_previous_schooling_subject_percentage == 1) {
                $template.= '<td><input type="number"  min="1" max="100" name="percentage[]" value="0" class="form-control per"></td>';
                $template.= '<td><input type="text" style="text-transform:uppercase; width:64px" name="grades[]" placeholder="Grade" class="form-control grd"></td>';
            }

          $template.= '</tr>';
          $m++;
        }
        $template .= '</table>';
        print($template);
    }

    
    public function get_admission_form_staff_detailsby_all_auId($lastId){
        return $this->db->select('af.*, au.mobile_no as preffered_no')
        ->from('admission_forms af')
        ->where('af.id',$lastId)
        ->join('admission_user au','af.au_id=au.id')
        ->get()->row();
    }


    public function get_remaing_years_selecting_erp(){

        $input = $this->input->post();
        $data['au_id']=$input['au_id'];
        $data['insert_id']=$input['lastId'];
        $data['admission_setting_id']=$input['admission_setting_id'];


        $data['config_val'] = $this->_get_admissions_settings_byId($data['admission_setting_id']);
        $prev_eduction_info = json_decode($data['config_val']['prev_eduction_info'], true);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($data['insert_id']);
        $years = array();
        foreach ($prev_eduction_info['year'] as $key => $year) {
            $years[$year] = $year;
        }
        $ryears = array();
        if (!empty($data['admission_prev_schools'])) {
            foreach ($data['admission_prev_schools'] as $key => $pSchool) {
                $ryears[$pSchool->year] = $pSchool->year;
            }
        }

        $result = array_diff($years,$ryears);
        $yearsinfo = array();
        foreach ($result as $key => $val) {
            array_push($yearsinfo, $val);
        }
        echo json_encode($yearsinfo);
    }

    public function preview_data_erp(){
        $update_stream = $this->Admission_model->update_combinationfor_value();
        $update_lang = $this->Admission_model->update_language_selection_value();
        $lastId = $this->input->post('lastId');
        $au_Id = $this->input->post('au_id');
        $admission_setting_id = $this->input->post('admission_setting_id');
        $eId = $this->input->post('enquiry_id');
        $this->session->set_userdata('admission_setting_id', $admission_setting_id);
        $this->session->set_userdata('lastId', $lastId);

        $data['au_id'] = $au_Id;
        $data['insert_id'] = $lastId;
        $data['admission_setting_id'] = $admission_setting_id;
        $data['eId'] = $eId;

        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        $data['preSchoolingmarks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_total_marks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_percentage'] = $data['config_val']['show_previous_schooling_subject_percentage'];
        $data['show_previous_schooling_overall_total_marks'] = $data['config_val']['show_previous_schooling_overall_total_marks'];
        $data['show_previous_schooling_overall_percentage'] = $data['config_val']['show_previous_schooling_overall_percentage'];

        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);

        $category = $this->settings->getSetting('category');
        $boarding = $this->settings->getSetting('boarding');
        $quota = $this->settings->getSetting('quota');

        if(!empty($category)){            
            if (!empty($data['final_preview']->category)) {
                $data['final_preview']->category = $category[$data['final_preview']->category];
            }else{
                $data['final_preview']->category ='';
            }
        }
        if(!empty($boarding)){            
            if (!empty($data['final_preview']->boarding)) {
              $data['final_preview']->boarding = $boarding[$data['final_preview']->boarding];
            }else{
                $data['final_preview']->boarding ='';
            }
        }
        if(!empty($quota)){            
            if (!empty($data['final_preview']->student_quota)) {
              $data['final_preview']->student_quota = $quota[$data['final_preview']->student_quota];
            }else{
                $data['final_preview']->student_quota ='';
            }
        }
        // echo '<pre>';print_r($data['final_preview']);die();
        $data['subject_master'] = $this->Admission_model->get_subject_master_list();
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);

        if (!empty($data['prev_eduction_info']['class'])) {
            if (!empty($data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for])) {
               $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
            }
        }
        
        $fields = $this->db->list_fields('admission_forms');
        $fData = [];
        $uncheckFields = ['id','au_id','created_on','admission_setting_id','filled_by','enquiry_id','receipt_html_path','receipt_number','template_pdf_path','seat_allotment_pdf_path','seat_allotment_no','std_photo_uri','last_modified_by','pdf_status','modified_on','custom_field','instruction_file','seat_allotment_date','nationality_other','religion_other','mother_tongue_other','father_mother_tongue_other','f_addr','f_area','f_district','f_state','f_county','f_pincode','m_addr','m_area','m_district','m_state','m_county','m_pincode','g_addr','g_state','g_county','g_pincode', 'f_company_addr','f_company_area','f_company_district','f_company_state','f_company_county','f_company_pincode','m_company_addr','m_company_area','m_company_district','m_company_state','m_company_county','m_company_pincode','g_company_addr','g_company_area','g_company_district','g_company_state','g_company_county','g_company_pincode','s_present_addr','s_present_area','s_present_district','s_present_state','s_present_country','s_present_pincode','s_permanent_addr','s_permanent_area','s_permanent_district','s_permanent_state','s_permanent_country','s_permanent_pincode','lang_1_choice','lang_2_choice','lang_3_choice','application_no','mother_mother_tongue_other','g_photo_uri','m_signature','f_signature','has_sibling','sibling_inschool_other','assigned_to','special_needs_description','res_ph','family_photo','father_photo','mother_photo','admission_link_expire_date','std_photo_uri_resize','student_signature','fee_paid_status','fee_paid_mode','is_ready_to_take_proficiency_test'];

     
        foreach ($fields as $field){
            if (!in_array($field, $uncheckFields) ) {
                array_push($fData, $field);
            }
        }

        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        $health_disabled_fields = $this->Admission_model->get_health_disabled_fields_admission();
        $healthField = $this->db->list_fields('student_health');

        $uncheckFieldsHelath = ['id','student_id','created_on','modified_on','last_modified_by','academic_year_id','admission_form_id'];
        $hData =[];
        foreach ($healthField as $hfield){
            if (!in_array($hfield, $uncheckFieldsHelath) ) {
                array_push($hData, $hfield);
            }
        }

        $displayFields_health = [];
        foreach ($hData as $key => $value) {
            if (!in_array($value, $health_disabled_fields)) {
               array_push($displayFields_health, $value);
            }
        }
        $data['health_display_field'] = $displayFields_health;
        $data['admission_medical'] = (array) $this->Admission_model->get_admission_form_medical_details($lastId);
        // echo '<pre>';print_r($data['admission_medical']['foodytpe'] );die();
        $data['admission_vaccination'] = $this->Admission_model->get_admission_form_vaccination_details($lastId);
        // echo "<pre>"; print_r($data['admission_prev_schools']);
        // echo "<pre>"; print_r($data['disabled_fields']); die();
        $displayFields = [];
        foreach ($fData as $key => $value) {
            if (!in_array($value, $data['disabled_fields'])) {
               array_push($displayFields, $value);
            }
        }
        if (!in_array('s_present_addr', $data['disabled_fields'])) {
            array_push($displayFields, 's_present_address');
        }
        if (!in_array('s_permanent_addr', $data['disabled_fields'])) {
            array_push($displayFields, 's_permanent_address');
        }
        if (!in_array('f_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'f_address');
        }
        if (!in_array('m_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'm_address');
        }
        if (!in_array('g_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'g_address');
        }
        if (!in_array('f_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'f_company_address');
        }
        if (!in_array('f_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'm_company_address');
        }
        if (!in_array('g_company_addr', $data['disabled_fields'])) {
            array_push($displayFields, 'g_company_address');
        }
        $data['fields'] = $this->_prepareAdmissionSeperateInput($displayFields);

        $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_Id,$lastId);

        if (!empty($data['streams'])) {
            $combArray = $data['streams'][$data['combinations']->combination];

            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if ($val['id'] == $data['combinations']->combination_id) {
                    $data['comb'] = $val['name'];
                }
            }
        }
        $data['previewData'] = (array)$data['final_preview'];
        $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
        $data['main_content'] = 'admission/staff/application/_blocks/final_preview';
      	$this->load->view('inc/template', $data);
    }

    private function _prepareAdmissionSeperateInput(&$input){
        $return_data = [];
        foreach ($input as $k => $v) {
            $start_key = substr($v, 0, 2);
            if ($start_key == 'f_' || $start_key == 'fa') {
              $key = str_replace("f_", "", $v);
              $return_data['father'][$key] = $v;
            } elseif ($start_key == 'm_' || $start_key == 'mo') {
              $key = str_replace("m_", "", $v);
              $return_data['mother'][$key] = $v;
            }elseif ($start_key == 'g_' || $start_key == 'gu') {
              $key = str_replace("g_", "", $v);
              $return_data['gurdian'][$key] = $v;
            }
            else {
              $return_data['student'][$v] = $v;
            }
        }
        //echo '<pre>';print_r($return_data);

        return $return_data;
    }

    private function _email_to_parent_admission($get_email_template){
        $members = [];
        array_push($members, $get_email_template->f_email_id, $get_email_template->m_email_id, $get_email_template->g_email_id);
        $this->load->model('communication/emails_model');
        $sent_by = $this->authorization->getAvatarStakeHolderId();
        $email_master_data = array(
            'subject' => $get_email_template->email_subject,
            'body' => $get_email_template->content,
            'source' => 'Admission form filed by the Staff',
            'sent_by' => $sent_by,
            'recievers' => "Parents",
            'from_email' => $get_email_template->registered_email,
            'files' => '',
            'acad_year_id' => $get_email_template->academic_year_applied_for,
            'visible' => 1,
            'sending_status' => 'Completed',
            'sender_list'=>'',
        );
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $memberEmail = [];
        $email_data = [];
        foreach ($members as $key => $val) {
            if(empty($val))
                continue;

            $trimmed_email = trim($val);

            if(!empty($trimmed_email))
                $memberEmail[] = $trimmed_email;

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = 0;
            $email_obj->avatar_type = 2;
            $email_obj->email = $trimmed_email;

            $email_data[] = $email_obj;
        }
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        $this->load->helper('email_helper');
        return sendEmail($get_email_template->content, $get_email_template->email_subject, $email_master_id, $memberEmail, $get_email_template->registered_email, []);
    }

    public function submit_final_form_erp(){
        $input = $this->input->post();
        if ($input['enquiry_id']!=0) {
           $this->admission_staff_model->update_admission_form_id_to_enquriy($input['enquiry_id'],$input['lastId']);   
        }

        $application_fees_collect = $this->settings->getSetting('application_fees_donot_collect');
        if(empty($application_fees_collect)){
            $transaction = $this->admission_staff_model->insert_transcations_details($input);
            if (empty($transaction))    
            {
                $this->db->trans_rollback();
                $this->session->set_flashdata('flashError', 'Something went wrong');    
                redirect('Admission_staff_controller/new_form/'.$input['au_id'].'/'.$input['admission_setting_id'].'/'.$input['lastId']);
            }else{
                $this->Admission_model->update_transcation_details($input['lastId'], 'SUCCESS','Application Amount Paid');
                $this->Admission_model->update_application_receipt($input['lastId']);
                if($this->settings->getSetting('show_admission_receipt_button')){
                    $this->Admission_model->update_admission_application_receipt($input['lastId']);
                }
            }
        }else{
            $this->Admission_model->update_transcation_details($input['lastId'],'','Submitted');
            $this->Admission_model->update_application_receipt($input['lastId']);
        }
        // if receipt is already generated, we have already updated the admission forms. No need to update again. Just show the already generated receipt.  
        
        
        $this->db->trans_commit();

        $this->_generate_admission_form_pdf($input['lastId'],$input['au_id'],$input['admission_setting_id']);
        
        $admUsers = $this->Admission_model->get_auId_mobileNumber($input['lastId']);
        $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($input['au_id'],$input['lastId']);
        $get_email_template = $this->Admission_model->get_email_template_byId($input['admission_setting_id'],$input['lastId']); 
        if ($get_email_template) {
            $this->_email_to_parent_admission($get_email_template);
        }
        $this->_sms_to_parent_admission($admin_no, $admUsers->mobile_no);

        $applicationv2 = strtolower($this->settings->getSetting('admission_filter_version'));

        $isApplicationGenerated = $this->Admission_model->is_application_no_generated($input['lastId']);
        if ($isApplicationGenerated){
            
            if($applicationv2 =='v2'){
                redirect('admission_process/individual_application_form/'.$input['lastId'].'/'.$input['au_id'].'/'.$input['admission_setting_id']);
            }else{
                redirect('Admission_staff_controller/print_erp/'.$input['au_id'].'/'.$input['lastId'].'/'.$input['admission_setting_id']);
            }
        }

        // if($applicationv2 =='v2'){
        //     redirect('admission_process/individual_application_form/'.$input['lastId'].'/'.$input['au_id'].'/'.$input['admission_setting_id']);
        // }else{
        //     redirect('Admission_staff_controller/print_erp/'.$input['au_id'].'/'.$input['lastId'].'/'.$input['admission_setting_id']);
        // }
        // redirect('admission_process/application_details_view/'.$input['lastId']);

    }

    public function print_erp($au_id,$lastId,$admission_setting_id){
        $data['v1'] = 0;
        $data['au_id'] = $au_id;
        $data['app_id'] = $lastId;
        $data['admission_setting_id'] = $admission_setting_id;
        $data['insert_id'] = $lastId;
        $data['receipts_view'] = '1';
        $data['redirect'] = '1';
        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        // $data['transaction_id'] = $transaction_id;
        // $data['transaction_date'] = $transaction_date;
        // $data['transaction_time'] = $transaction_time;
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$lastId);
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_id,$lastId);

        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admission_setting_id, $data['final_preview']->grade_applied_for);
        if (!empty($data['streams'])) {
            $combArray = $data['streams'][$data['combinations']->combination];

            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if ($val['id'] == $data['combinations']->combination_id) {
                    $data['comb'] = $val['name'];
                }
            }
        }
        if (!empty($data['prev_eduction_info'])) {
            $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
        }
        // echo "<pre>shortName"; print_r($data['school_short']); die();

        $data['main_content'] = 'admission/receipts/header_staff';
        $this->load->view('inc/template', $data);

        // $data['main_content'] = 'admission/receipts/'.$data['school_short'];
        // $this->load->view('inc/template', $data);

    }

    private function _sms_to_parent_admission($admin_no, $mobileNumber){
        $smsint = (array) $this->settings->getSetting('smsintergration');
        $admissionSMS = $this->settings->getSetting('admissions_sms',0);
        if (!empty($admissionSMS)) {
            $msg =  $admissionSMS;
            $msg = str_replace('%%admission_no%%',$admin_no, $msg);
        }else{
            $msg = "Your online application for registration number ".$admin_no." has been successfully submitted. To complete the registration process, kindly print, sign and submit the form at the school front office within 5 working days.";
        }
        $content =  urlencode(''.$msg.'');

        $get_url = 'http://'.$smsint['url'].'?method=sms&api_key='.$smsint['api_key'].'&to='.$mobileNumber.'&sender='.$smsint['sender'].'&message='.$content;
        return $this->curl->simple_get($get_url);
    }

    public function get_enquiry_procees_for_admission_details(){
        $result = $this->Admission_model->get_enquiry_admission_process_details();
        echo json_encode($result);
    }

    public function __construct_name_wise_required($requiredData){
        $fields = $this->db->list_fields('admission_forms');
        $prevFields = $this->db->list_fields('admission_prev_school');
        $merges = array_merge($fields, $prevFields);
        $rData = [];
        foreach ($merges as $key => $val) {
            if (in_array($val, $requiredData)) {
                $rData[$val] = array('required' =>'required');
            }else{
                $rData[$val] = array('required' =>'');
            }
        }
        return $rData;
    }

    public function get_submitted_dmission_details(){
        $result = $this->Admission_model->get_submitted_application_details();
        echo json_encode($result);
    }

    public function collect_offline_fee_for_application(){
        $input = $this->input->post();
        $this->db->trans_begin();
        $transaction = $this->admission_staff_model->insert_transcations_details($input);

        if (empty($transaction)) 
        {
            $this->db->trans_rollback();
            $this->session->set_flashdata('flashError', 'Something went wrong');    
            redirect('admission_process/application_details_view/'.$input['lastId']);
        }else{
            $this->Admission_model->update_transcation_details($input['lastId'], 'SUCCESS','Application Amount Paid');
            $isApplicationGenerated = $this->Admission_model->is_application_no_generated($input['lastId']);
            if ($isApplicationGenerated == 0){
                $this->Admission_model->update_application_receipt($input['lastId']);
            }
            $isReceiptNoGenerated = $this->Admission_model->is_application_receipt_no_generated($input['lastId']);
            if ($isReceiptNoGenerated == 0){
                $this->Admission_model->update_admission_application_receipt($input['lastId']);
            }
            $this->db->trans_commit();
            // $admUsers = $this->Admission_model->get_auId_mobileNumber($input['lastId']);
            // $admin_no = $this->Admission_model->get_admission_number_byauid_lastIdwise($input['au_id'],$input['lastId']);
            // $get_email_template = $this->Admission_model->get_email_template_byId($input['admission_setting_id'],$input['lastId']); 
            // if ($get_email_template) {
            //   $this->_email_to_parent_admission($get_email_template);
            // }
            // $this->_sms_to_parent_admission($admin_no, $admUsers->mobile_no);
            redirect('admission_process/application_details_view/'.$input['lastId']);
        }    
    }

     public function gender_pdf_application_staff_form(){
        $admissionId = $_POST['admissionId'];
        $au_id = $_POST['au_id'];
        $admission_setting_id = $_POST['admission_setting_id'];
        echo $this->_generate_admission_form_pdf($admissionId,$au_id,$admission_setting_id);
    }


    private function _generate_admission_form_pdf($id,$au_id,$admission_setting_id){
      $config_val = $this->_get_admissions_settings_byId($admission_setting_id);
      if (!empty($config_val['application_form_html'])) {
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_id,$id);
        $admission_doc = $this->Admission_model->get_admission_form_detailsby_auId($id);
        $admission_prev_schools = $this->Admission_model->get_admission_form_previous_school_details($id);
        $subject_master = $this->Admission_model->get_subject_master_list();
        $admission_stream = $this->Admission_model->get_admission_form_combinations($id);
        $result = $this->_construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $config_val['application_form_html'], $subject_master, $admission_stream, json_decode($config_val['streams'], true));
        if ($result) {
          // $update =  $this->Admission_model->update_admission_html_receipt($result, $id);
            $this->_generate_admisison_pdf_receipt($result, $id);
        }
      }
    }
    
    public function _construct_admisison_form_template($final_preview, $admission_doc, $admission_prev_schools, $template, $subjectMaster, $admission_stream, $streams){
        $combination = '';
        if (!empty($admission_stream)) {
            foreach ($streams[$admission_stream->combination] as $key => $val) {
                if ($val['id'] == $admission_stream->combination_id) {
                    $combination = $val['name'];
                }
            }
        }
        $student_quota = '';
        if (!empty($final_preview->student_quota)) {
            foreach ($this->settings->getSetting('quota') as $key => $value) {
                if ($final_preview->student_quota == $key) {
                    $student_quota = $value;
                }
            }   
        }
        $language = '';
            if (!empty($final_preview->lang_1_choice)) {
              foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_1_choice){
                  $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_2_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_2_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }

        if (!empty($final_preview->lang_3_choice)) {
            foreach ($subjectMaster as $key => $val) { 
                if($val->id == $final_preview->lang_3_choice){
                    $language =  $val->subject_name;
                } 
            } 
        }
      $formData = (array)$final_preview;
      $fields = $this->db->list_fields('admission_forms');
      $prevSchoolFields = $this->db->list_fields('admission_prev_school');

      $merge = array_merge($fields, $prevSchoolFields);
      $fData = [];
      foreach ($merge as $field){
        if ($field !='id' && $field!='au_id') {
          array_push($fData, $field);
        } 
      }
      $photo = '';
      if ($final_preview->std_photo_uri !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->std_photo_uri);
        $photo = '<img style="margin-top: -8rem !important; float: right; margin-right: 0rem;width:132px !important; height:150px!important" src="'.$getURL.'" />';
      }
      $fatherSignature = '';
      if ($final_preview->f_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->f_signature);
        $fatherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }
      $motherSignature = '';
      if ($final_preview->m_signature !='') {
        $getURL = $this->filemanager->getFilePath($final_preview->m_signature);
        $motherSignature = '<img style="width:100px !important; height:80px!important" src="'.$getURL.'" />';
      }

      $category = '';
      if ($final_preview->category !='0' && !empty($final_preview->category)) {
        $category = $this->settings->getSetting('category')[$final_preview->category];
      }
      $boarding = '';
      if ($final_preview->boarding !='0' && !empty($final_preview->boarding)) {
        $boarding = $this->settings->getSetting('boarding')[$final_preview->boarding];
      }
      $student_passport_expiry_date = $final_preview->passport_expiry_date;
      if ($final_preview->passport_expiry_date == '01-01-1970' || empty($final_preview->passport_expiry_date)) {
        $student_passport_expiry_date = '-';
      }
      $prevSchool = '';
      $template = str_replace('%%academic_year_applied_for%%', $this->acad_year->getAcadYearById($final_preview->academic_year_applied_for), $template);
      $template = str_replace('%%boarding%%', $boarding, $template);
      $template = str_replace('%%language%%', $language, $template);
      $template = str_replace('%%student_photo%%', $photo, $template);
      $template = str_replace('%%student_passport_expiry_date%%', $student_passport_expiry_date, $template);
      $template = str_replace('%%father_signtuare%%', $fatherSignature, $template);
      $template = str_replace('%%mother_signtuare%%', $motherSignature, $template);
      $template = str_replace('%%application_date%%', $final_preview->created_date, $template);
      $template = str_replace('%%s_first_name%%', $final_preview->std_name, $template);
      $template = str_replace('%%s_middle_name%%', $final_preview->student_middle_name, $template);
      $template = str_replace('%%s_last_name%%', $final_preview->student_last_name, $template);
      $template = str_replace('%%category%%', $category, $template);
      $template = str_replace('%%student_quota_name%%', $student_quota, $template);
      // $template = str_replace('%%extracurricular_activities%%', $final_preview->extracurricular_activities, $template);
      // $template = str_replace('%%joining_period%%', $final_preview->joining_period, $template);
      // $template = str_replace('%%previous_school_name%%', '', $template);
      // $template = str_replace('%%previous_class_name%%', '', $template);
      // $template = str_replace('%%previous_board%%', '', $template);
      // $template = str_replace('%%previous_total_marks%%', '', $template);
      // $template = str_replace('%%previous_total_marks_scored%%', '', $template);
      // $template = str_replace('%%previous_total_percentage%%', '', $template);
      // $template = str_replace('%%previous_year%%', '', $template);
      // $template = str_replace('%%previous_school_address%%', '', $template);
      // $template = str_replace('%%medium_of_instruction%%', '', $template);

      if (!empty($admission_prev_schools)) {
        $template = str_replace('%%previous_school_name%%', $admission_prev_schools[0]->school_name, $template);
        $template = str_replace('%%previous_class_name%%', $admission_prev_schools[0]->class, $template);
        $template = str_replace('%%previous_board%%', $admission_prev_schools[0]->board, $template);
        $template = str_replace('%%previous_total_marks%%', $admission_prev_schools[0]->total_marks, $template);
        $template = str_replace('%%previous_total_marks_scored%%', $admission_prev_schools[0]->total_marks_scored, $template);
        $template = str_replace('%%previous_total_percentage%%', $admission_prev_schools[0]->total_percentage, $template);
        $template = str_replace('%%previous_year%%', $admission_prev_schools[0]->year, $template);
        $template = str_replace('%%previous_school_address%%', $admission_prev_schools[0]->school_address, $template);
        $template = str_replace('%%medium_of_instruction%%', $admission_prev_schools[0]->medium_of_instruction, $template);
        $template = str_replace('%%registration_no%%', $admission_prev_schools[0]->registration_no, $template);
        $template = str_replace('%%expelled_or_suspended%%', $admission_prev_schools[0]->expelled_or_suspended, $template);
        $template = str_replace('%%transfer_reason%%', $admission_prev_schools[0]->transfer_reason, $template);
        $template = str_replace('%%expelled_or_suspended_description%%', $admission_prev_schools[0]->expelled_or_suspended_description, $template);
      }else{
        $template = str_replace('%%previous_school_name%%','', $template);
        $template = str_replace('%%previous_class_name%%Std.', '', $template);
      }
      $prevSchoolnotinmarks = '';
      if (!empty($admission_prev_schools)) {
        $prevSchoolnotinmarks .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500;margin-bottom: 2rem;margin-top: 2rem;">Previous School Information</h5>';        
        $prevSchoolnotinmarks .= '<table style="border:none;margin-bottom: 8px; width: 98%; margin-top: -1rem; padding-bottom: -12px; margin-left: auto; margin-right: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">School name</td>
                    <td style="font-size: 11px;">School Address</td>
                    <td style="font-size: 11px;">Transfer reason</td>
                    <td style="font-size: 11px;">Expelled/Suspended</td>
                    <td style="font-size: 11px;">Expelled/Suspended description</td>
                </tr>';
            foreach ($admission_prev_schools as $key => $value) {
                $prevSchoolnotinmarks .= '<tr>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->year.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->board.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_name.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->school_address.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->transfer_reason.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended.'</td>';
                $prevSchoolnotinmarks .= '<td style="font-size: 11px;">'.$value->expelled_or_suspended_description.'</td>';
                $prevSchoolnotinmarks .= '</tr>';
            }
            $prevSchoolnotinmarks .= '</table>';
      }
      if (!empty($admission_prev_schools)) {
        $prevSchool .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">EDUCATIONAL QUALIFICATION</h5>';
        $prevSchool .= '<table style="width: 98%; margin: auto; ">
                <tr>
                    <td style="font-size: 11px;">Year</td>
                    <td style="font-size: 11px;">Board</td>
                    <td style="font-size: 11px;">School Name</td>
                    <td style="font-size: 11px;">School Address</td>
                    <td style="font-size: 11px;">Subjects</td>
                    <td style="font-size: 11px;">Max Marks</td>
                    <td style="font-size: 11px;">Marks Obtained</td>
                </tr>';
          $rowspan = 0;
          $totalMarks = 0;
          $totalMarksScored = 0;
          $totalPercentage = 0;
          $break = 1;
          foreach ($admission_prev_schools as $key => $value) {
            if($break == 1){
                $rowspan =0;
                if (!empty($value->marks)) {
                  $rowspan = sizeof($value->marks);
                }
                $totalPercentage = $value->total_percentage;    
                $prevSchool .= '<tr>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->year.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->board.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_name.'</td>';
                $prevSchool .= '<td style="font-size: 11px;" rowspan="'.$rowspan.'">'.$value->school_address.'</td>';
                // $prevSchool .= '</tr>';
                $close = 1;
                if (!empty($value->marks)) {
                  foreach ($value->marks as $key => $val) {
                    $jsan =  json_decode($val->sub_name);
                    $totalMarks += $val->marks;
                    $totalMarksScored += $val->marks_scored;
                    $subName = $jsan->name;
                    if($jsan->name == ''){
                      $subName = '-';
                    }
                    if($close != 1) {
                      $prevSchool .= '<tr>';
                    }
                    $prevSchool .= '<td style="font-size: 11px;">'.$subName.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks.'</td>';
                    $prevSchool .= '<td style="font-size: 11px;">'.$val->marks_scored.'</td>';
                    $prevSchool .= '</tr>';
                    $close++;
                  }
                }
                
              }
              break;
            }
            $prevSchool .= '<tr>';
            $prevSchool .= '<td style="font-size: 11px;" colspan="5"><span>Percentage : '.$totalPercentage.'</span></td>';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarks, 2, '.', '').'</td> ';
            $prevSchool .= '<td style="text-align:left !important;font-size: 11px;">'.number_format((float)$totalMarksScored, 2, '.', '').'</td> ';
            $prevSchool .= '</tr>';
            $prevSchool .= '</table>';
      }


      foreach ($fData as $key => $val) {
        if (array_key_exists($val, $formData)) {
            if (!empty($formData[$val])) {
              $template = str_replace('%%'.$val.'%%',$formData[$val], $template);
            }else{
              $template = str_replace('%%'.$val.'%%','-', $template);
            }
          
        }
      }
      $income_yes_no = 'No';
      if (!empty($final_preview->caste_income_certificate_number)) {
        $income_yes_no = 'Yes';
      }
      $physical_disability_yes_no = 'No';
      if (!empty($final_preview->physical_disability == 'Y')) {
        $physical_disability_yes_no = 'Yes';
      }
      $student_gender = '';
        if ($final_preview->gender =='M') {
            $student_gender = 'Male';
        }else if($final_preview->gender == 'F'){
            $student_gender = 'Female';
        }else if($final_preview->gender == 'O'){
            $student_gender = 'Other';
        }
      $learning_disability_yes_no = 'No';
      if (!empty($final_preview->learning_disability == 'Y')) {
        $learning_disability_yes_no = 'Yes';
      }
      $student_gender = 'Female';
      if ($final_preview->gender =='M') {
          $student_gender = 'Male';
      }
      $documents = '<p>No Documents Uploaded</p>';
      if(!empty($admission_doc)){
        $documents = '<table class="table table-bordered">';
        $documents .='<th colspan="4">Documents Submitted</th>';
        $documents .='<tr>';
        $documents .='<td colspan="4">';
        $i=1; 
        foreach ($admission_doc as $key => $doc) {
          $documents .=''.$i++.'. ';
            if ($doc->document_type == 'Others') {
          $documents .=''.strtoupper($doc->document_other).'<br>';
            }else{
          $documents .=''.strtoupper($doc->document_type).'<br>';
            }
          }
          $documents .='</td>';
          $documents .='</tr>';
          $documents .='</table>';
      }
      $dobAge = $final_preview->year.'years '.$final_preview->month.'months '.$final_preview->day.'days';
      $template = str_replace('%%prevoius_school_not_in_marks%%', $prevSchoolnotinmarks, $template);
      $template = str_replace('%%previous_education_details%%', $prevSchool, $template);
      $template = str_replace('%%income_yes_no%%', $income_yes_no, $template);
      $template = str_replace('%%physical_disability_yes_no%%', $physical_disability_yes_no, $template);
      $template = str_replace('%%learning_disability_yes_no%%', $learning_disability_yes_no, $template);
      $template = str_replace('%%s_permanent_pincode%%', $final_preview->s_permanent_pincode, $template);
      $template = str_replace('%%combinations%%', $combination, $template);
      $template = str_replace('%%student_quota%%', $student_quota, $template);
      $template = str_replace('%%dob_age%%', $dobAge, $template);
      $template = str_replace('%%student_gender%%', $student_gender, $template);
      $template = str_replace('%%documents%%',$documents, $template);


    //   echo "<pre>"; print_r($template); die();
      return $template;
    }

    // public function get_age_pdf_template($dateVal){
    //     $dob_as_on = new Date();
    //     $onSelectDate = new Date($dateVal),
    //     $today = new Date($dob_as_on),
    //     $ageInMilliseconds = new Date($today - $onSelectDate),
    //     $years = $ageInMilliseconds / (24 * 60 * 60 * 1000 * 365.25 ),
    //     $months = 12 * ($years % 1),
    //     $days = Math.floor(30 * ($months % 1));

    //     $result = html(Math.floor($years) + ' years ' + Math.floor($months) + ' months ' + $days + ' days as on ' + moment($dob_as_on).format('MMM DD, YYYY'));
    //     return $result
        
    // }
    private function _generate_admisison_pdf_receipt($html, $afId) {
      $school = CONFIG_ENV['main_folder'];
      $path = $school.'/admissions_reciepts/'.uniqid().'-'.time().".pdf";
      $bucket = $this->config->item('s3_bucket');
      $status = $this->Admission_model->update_admission_form_path($afId, $path);
      $page_size = 'a4';
      $page = 'portrait';
      $curl = curl_init();
      $postData = urlencode($html);
      $username = CONFIG_ENV['job_server_username'];
      $password = CONFIG_ENV['job_server_password'];
      $return_url = site_url().'Callback_Controller/updateApplicationPdfLink';

      curl_setopt_array($curl, array(
          CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
          CURLOPT_HTTPHEADER => array(
                  "Accept: application/json",
                  "Cache-Control: no-cache",
                  "Content-Type: application/x-www-form-urlencoded",
                  "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
              ),
          ));

          $response = curl_exec($curl);
          $err = curl_error($curl);
          curl_close($curl);
    }


    public function edit_application_form($au_Id, $admSettingId,$lastId, $eId){
        if ($eId !=0) {
           $data['enquiry'] = $this->Admission_model->get_enquiry_form_data($eId);
        }
        $data['au_id']=$au_Id;
        $data['insert_id']=$lastId;
        $data['eId']=$eId;
        $data['admission_setting_id']=$admSettingId;
        $data['config_val'] = $this->_get_admissions_settings_byId($admSettingId);
        $data['class_applied_for'] = json_decode($data['config_val']['class_applied_for'], true);
        // echo "<pre>"; print_r($data['config_val']); die();
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
        $data['documents'] = json_decode($data['config_val']['documents'], true);
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructin_file'] = $data['config_val']['instruction_file'];
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);

        $data['show_previous_schooling_overall_total_marks'] = $data['config_val']['show_previous_schooling_overall_total_marks'];
        $data['show_previous_schooling_overall_grade'] = $data['config_val']['show_previous_schooling_overall_grade'];
        $data['previous_schooling_classes'] = $data['config_val']['previous_schooling_classes'];
        $data['show_previous_schooling_subjects'] = $data['config_val']['show_previous_schooling_subjects'];

        $data['show_previous_schooling_subject_total_marks'] = $data['config_val']['show_previous_schooling_subject_total_marks'];
        $data['show_previous_schooling_subject_percentage'] = $data['config_val']['show_previous_schooling_subject_percentage'];
        
        // echo "<pre>"; print_r($data['show_previous_schooling_subjects']);
        //  die();
        $data['show_previous_schooling_overall_percentage'] = $data['config_val']['show_previous_schooling_overall_percentage'];

        if ($data['final_preview']->std_name=='') {
            $data['final_preview']->std_name = (isset($data['enquiry']->student_name)=='')?'':$data['enquiry']->student_name;
        }
        if ($data['final_preview']->gender=='') {
            $data['final_preview']->gender = (isset($data['enquiry']->gender)=='')?'':$data['enquiry']->gender;
        }
        if ($data['final_preview']->dob =='0000-00-00') {
            $data['final_preview']->dob = (isset($data['enquiry']->student_dob)=='')?'':$data['enquiry']->student_dob;
        }
        if ($data['final_preview']->f_name =='') {
            $data['final_preview']->f_name =(isset($data['enquiry']->parent_name)=='')?'':$data['enquiry']->parent_name;
        }
        if ($data['final_preview']->f_mobile_no =='') {
            $data['final_preview']->f_mobile_no =(isset($data['enquiry']->mobile_number)=='')?'':$data['enquiry']->mobile_number;
        }
        if ($data['final_preview']->m_mobile_no =='') {
            $data['final_preview']->m_mobile_no = (isset($data['enquiry']->alternate_mobile_number)=='')?'':$data['enquiry']->alternate_mobile_number;
        }
        if ($data['final_preview']->f_email_id =='') {
            $data['final_preview']->f_email_id = (isset($data['enquiry']->email)=='')?'':$data['enquiry']->email;
        }
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        $data['admission_stream'] = $this->Admission_model->get_admission_form_combinations($lastId);
        $data['lang_selection'] = $this->Admission_model->get_language_selection_by_id($admSettingId, $data['final_preview']->grade_applied_for);
        // echo "<pre>"; print_r($data['lang_selection']); die();
        $years = array();
        if (!empty($data['prev_eduction_info']['year'])) {
            foreach ($data['prev_eduction_info']['year'] as $key => $year) {
                $years[$year] = $year;
            }
        }
        $ryears = array();
        if (!empty($data['admission_prev_schools'])) {
            foreach ($data['admission_prev_schools'] as $key => $pSchool) {
                $ryears[$pSchool->year] = $pSchool->year;
            }
        }
        $result=array_diff($years,$ryears);
        $data['yearinfo'] = $result;

        $doc = array();
        foreach ($data['documents'] as $key => $docs) {
            $doc[$docs] = $docs;
        }
                
        $rdocs = array();
        if (!empty($data['admission_form'])) {
            foreach ($data['admission_form'] as $key => $document) {
                $rdocs[$document->document_type] = $document->document_type;
            }
        }
        $document = array_diff($doc,$rdocs);
        $data['doucments'] = $document;
        $required_fields = $this->Admission_model->get_admission_required_fields();
        $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
        // echo "<pre>"; print_r($data['required_fields']); die();
        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        $freez_primary_fields = $this->settings->getSetting('freez_primary_fields');
        $data['freez_primary_fields'] = explode(',', $freez_primary_fields);
        $data['need_qualification_dropdown']=$this->Admission_model->get_need_qualification($data['admission_setting_id']);
        $data['image_size_in_admissions'] = $this->settings->getSetting('image_size_in_admissions');
        if($data['image_size_in_admissions'] == ''){
            $data['image_size_in_admissions'] = 2;
        }
        // echo "<pre>"; print_r($data['required_fields']); die();
        $data['main_content'] = 'admission/staff/edit_application/index';
        $this->load->view('inc/template_fee', $data);
    }
    public function edit_updateStudentDetails_erp(){
        $input = $this->input->post();
        echo $this->Admission_model->update_student_details_by_id($input,$this->s3FileUpload($_FILES['student_photo']));
    }
    public function edit_preview_data_erp(){
        $update_stream = $this->Admission_model->update_combinationfor_value();
        $update_lang = $this->Admission_model->update_language_selection_value();
        $lastId = $this->input->post('lastId');
        $au_Id = $this->input->post('au_id');
        $admission_setting_id = $this->input->post('admission_setting_id');
        $eId = $this->input->post('enquiry_id');
        $this->session->set_userdata('admission_setting_id', $admission_setting_id);
        $this->session->set_userdata('lastId', $lastId);

        $data['au_id'] = $au_Id;
        $data['insert_id'] = $lastId;
        $data['admission_setting_id'] = $admission_setting_id;
        $data['eId'] = $eId;

        $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
        $data['form_name'] = $data['config_val']['form_name'];
        $data['form_year'] = $data['config_val']['form_year'];
        $data['school_name'] = $data['config_val']['school_name'];
        $data['school_short'] = $data['config_val']['school_short'];
        $data['instructions'] = $data['config_val']['instruction_file'];
        $data['streams'] = json_decode($data['config_val']['streams'], true);
        $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);      
        $data['final_preview'] = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);
        $data['admission_form'] = $this->Admission_model->get_admission_form_detailsby_auId($lastId);
        $data['admission_prev_schools'] = $this->Admission_model->get_admission_form_previous_school_details($lastId);
        if (!empty($data['prev_eduction_info'])) {
            $data['subjectArray'] = $data['prev_eduction_info']['class'][$data['final_preview']->grade_applied_for]['subject'];
        }
        
        $data['combinations'] = $this->Admission_model->get_combinations_for_select_list($au_Id,$lastId);

        if (!empty($data['streams'])) {
            $combArray = $data['streams'][$data['combinations']->combination];

            $resultArray = array();
            foreach ($combArray as $key => $val) {
                if ($val['id'] == $data['combinations']->combination_id) {
                    $data['comb'] = $val['name'];
                }
            }
        }
        $data['admissions_payment_mode'] = $this->settings->getSetting('admissions_payment_mode');
        $data['disabled_fields'] = $this->Admission_model->get_admission_enabled_fields();
        $data['main_content'] = 'admission/staff/edit_application/_blocks/final_preview';
        $this->load->view('inc/template', $data);
    }

    public function get_subject_details_from_admission_settings(){
      
        $admission_setting_id=$_POST['admission_setting_id'];
        $lastId=$_POST['afId'];
        $au_Id=$_POST['au_id'];
        $pre_school_year= $_POST['pre_school_year'];
        $apsId = $_POST['apsId'];
        $config_val = $this->_get_admissions_settings_byId($admission_setting_id);

        $preSchoolingmarks = $config_val['show_previous_schooling_overall_total_marks'];

        // $show_previous_schooling_subjects = $config_val['show_previous_schooling_subjects'];

        $show_previous_schooling_subject_total_marks = $config_val['show_previous_schooling_subject_total_marks'];

        $show_previous_schooling_subject_percentage = $config_val['show_previous_schooling_subject_percentage'];

        $prev_eduction_info = json_decode($config_val['prev_eduction_info'], true);
        
        $final_preview = $this->Admission_model->get_admission_form_detailsby_all_auId($au_Id,$lastId);
         $marks= $this->Admission_model->get_pre_school_subject_marks($apsId);
         $total_marks= $this->Admission_model->get_pre_school_total_marks($apsId);
        $template = '';
        $template .= '<table class="table table-borderd">';
        $template .= '<tr>';
        $template .= '<th>Subject</th>';


        if ($show_previous_schooling_subject_total_marks == 1) {
            $template .= '<th>Max Marks</th>';
            $template .= '<th>Marks Scored</th>';

        }
        if($show_previous_schooling_subject_percentage == 1){
            $template .= '<th>Percent</th>';
            $template .= '<th>Grade</th>';
        }

        $template .='</tr>';
        $subjectArray = $prev_eduction_info['class'][$final_preview->grade_applied_for]['subject'];
        $m = 1;
        foreach ($subjectArray as $subject) {
            $subName = $subject['name'];
            $subNameValue = '';
            $grade = '';
            $enter_marks = 0;
            $marks_scored = 0;
            $percentage = 0;
            if(!empty($marks)){
                foreach ($marks as $key => $val) {
                    if($subject['id'] == $val->sub_id){
                        if($val->sub_name !=''){
                            $subName = $val->sub_name;
                            $subNameValue = $val->sub_name;
                            $percentage = $val->percentage;
                            $grade = $val->grade;
                            $enter_marks = $val->marks;
                            $marks_scored = $val->marks_scored;
                        }
                       
                    }
                }
            }
            $template.= '<tr>';
            $template .= '<td>';
            $template .= ' <input type="hidden" value="'.$subject['id'].'" name="sub_id[]">';
            switch ($subject['type']) {
                case 'label':
                  $template .= $subName;
                  $template .= ' <input type="hidden" value="'.$subName.'" name="sub_name[]">';
                  break;
                case 'text': 
                  $template .= ' <input type="text" class="form-control" onkeyup="check_is_value_not_empty(this,'.$m.')" placeholder="'.$subName.'" value="'.$subNameValue.'" name="sub_name[]">';
            }
            $template .= '</td>';

            if ($show_previous_schooling_subject_total_marks == 1) {
                $template.= '<td><input type="number" step=".01" data-parsley-error-message="Invalid number" id="maxMarks_'.$m.'" onkeyup="max_marks_total('.$m.')"name="max_marks[]" value="'.$enter_marks.'" class="form-control maxMarks per"></td>';
                $template.= '<td><input type="number" data-parsley-error-message="Invalid number" id="maxMarkScored_'.$m.'" onkeyup="max_marks_scored_total('.$m.')" step=".01" name="marks_scored[]" value="'.$marks_scored.'" class="form-control maxMakrsScored per"></td>';
            }
            if ($show_previous_schooling_subject_percentage == 1) {
                $template.= '<td><input type="number"  min="1" max="100" name="percentage[]" value="'.$percentage.'" class="form-control per"></td>';
                $template.= '<td><input type="text" step=".01" style="text-transform:uppercase; width:64px" name="grades[]" placeholder="Grade" value="'.$grade.'" class="form-control grd"></td>';
            }

          $template.= '</tr>';
          $m++;
        }
        $template .= '</table>';

        if($config_val['show_previous_schooling_overall_total_marks'] == 1){
            $template .= '<div class="form-group">
                            <label class="control-label col-md-3">Total Marks Scored </label>
                            <div class="col-md-8">
                                <input type="number" step=".01" min="0" max="1000" readonly data-parsley-error-message="Invalid number" name="total_marks_scored" id="total_marks_scored" class="form-control" value="'.$total_marks->total_marks.'">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-3">Total Marks </label>
                            <div class="col-md-8">
                                <input type="number" step=".01" data-parsley-error-message="Invalid number" readonly min="0" max="1000" name="total_max_marks_entry" id="total_max_marks_entry" class="form-control" value="'.$total_marks->total_marks_scored.'">
                            </div>
                        </div>';
        }
        if($config_val['show_previous_schooling_overall_grade'] == 1){
            $template .=   '<div class="form-group">
                                <label class="control-label col-md-3">Grade </label>
                                <div class="col-md-8">
                                    <input type="text" step=".01" name="total_grade" id="total_grade" class="form-control">
                                </div>
                            </div>';
        }
        if($config_val['show_previous_schooling_overall_percentage'] == 1){
            $template .=   '<div class="form-group">
                                <label class="control-label col-md-3">Total Percentage </label>
                                <div class="col-md-8">
                                    <input type="text" step=".01" data-parsley-error-message="Invalid number" readonly name="total_percentage" id="total_percentage" class="form-control" value="'.$total_marks->total_percentage.'">
                                </div>
                            </div>';
        }
        print($template);
    }

    public function get_previous_school_detail_for_the_year(){
        $afid = $_POST['afid'];
        $year = $_POST['year'];
        $result = $this->Admission_model->get_previous_school_detail_for_the_year($afid, $year);
        echo json_encode($result);
    }

    public function admission_previous_school_report_card($apsId){
        $document = $this->Admission_model->get_admission_previous_school_report_card($apsId);
        $link = $document->report_card;
        $file = explode("/", $link);
        $file_name = 'report_card';
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        // echo '<pre>'; print_r($fname); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
    }

    public function update_previous_school_details_year_wise(){
        $input = $this->input->post();
        $report_card = '';
        if (isset($_FILES['report_card'])) {
            $report_card = $this->s3FileUpload($_FILES['report_card']);
        }
        echo $this->Admission_model->insert_previous_school_details_year_wise($input, $report_card);
    }

    public function update_previous_school_subjects_year_wise(){
        $input = $this->input->post();
        // echo '<pre>'; print_r($input); die();
        echo $this->Admission_model->insert_previous_school_subjects_year_wise($input);
    }

    public function delete_documentbyId(){
        $d_id = $this->input->post('d_id');
        echo $this->Admission_model->delete_document_byid($d_id);
    }

    public function update_documents_new(){
        $path = $_POST['path'];
        $document_for = $_POST['document_for'];
        $af_id = $_POST['af_id'];
        echo $this->Admission_model->insert_documents_new($path, $document_for, $af_id);
    }

    private function _resize_image($file, $max_resolution, $type)
    {
      if (file_exists($file)) {
        if ($type == 'image/jpeg')
          $original_image = imagecreatefromjpeg($file);
        else
          $original_image = imagecreatefrompng($file);
  
        //check orientation 
        // $exif = exif_read_data($file);
  
        try {
          $exif = exif_read_data($file);
        } catch (Exception $exp) {
          $exif = false;
        }
  
        if ($exif) {
          if (!empty($exif['Orientation'])) {
            switch ($exif['Orientation']) {
              case 3:
                $original_image = imagerotate($original_image, 180, 0);
                break;
  
              case 6:
                $original_image = imagerotate($original_image, -90, 0);
                break;
  
              case 8:
                $original_image = imagerotate($original_image, 90, 0);
                break;
            }
          }
        }
  
        //resolution
        $original_width = imagesx($original_image);
        $original_height = imagesy($original_image);
  
        //try width first
        $ratio = $max_resolution / $original_width;
        $new_width = $max_resolution;
        $new_height = $original_height * $ratio;
  
        //if that dosn't work
        if ($new_height > $max_resolution) {
          $ratio = $max_resolution / $original_height;
          $new_height = $max_resolution;
          $new_width = $original_width * $ratio;
        }
        
        if ($original_image) {
          $new_image = imagecreatetruecolor($new_width, $new_height);
          imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
          if ($type == 'image/jpeg')
            imagejpeg($new_image, $file);
          else
            imagepng($new_image, $file);
        }
  
        return $file;
        // echo '<br>Resized: ';
        // echo filesize($file); 
  
        // echo '<pre>'; print_r($file); die();
      }
    }

    public function get_language_class_wise_data(){
        $lang_selection = $this->Admission_model->get_language_selection_data($this->input->post('admsettingId'),$this->input->post('selectedClass'));
        echo json_encode($lang_selection);
     }
 
     public function get_subject_details(){
         $class = $_POST['selectedClass'];
         $admission_setting_id = $_POST['admsettingId'];
         $data['config_val'] = $this->_get_admissions_settings_byId($admission_setting_id);
         $data['prev_eduction_info'] = json_decode($data['config_val']['prev_eduction_info'], true);
 
         if(!empty($data['prev_eduction_info']['class'])){
             if (!empty($data['prev_eduction_info']['class'][$class])) {
                 echo 1;
             }else{
                 echo 0;
             }
         }else{
            echo 0;
         }
 
         
     }
 
    
}