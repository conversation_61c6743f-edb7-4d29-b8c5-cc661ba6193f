<?php
	defined('BASEPATH') or exit('No direct script access allowed');

	class Tasks extends CI_Controller{
		public function __construct(){
			parent::__construct();
		    if (!$this->ion_auth->logged_in()) {
		      redirect('auth/login', 'refresh');
		    }
		    $this->load->model('student_tasks/Tasks_model', 'task');
		    $this->load->library('filemanager');
		    $this->load->helper('text');

			$this->templates = [
				["name" => "Blank Template", "value" => ""],
				["name" => "3-Column Table template", "value" => "<style>table-class {  font-family: arial, sans-serif;  border-collapse: collapse;  width: 100%;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 6px;}table-class>tr:nth-child(even) {  background-color: #dddddd;}</style><table class='table-class'>  <tbody><tr>    <th widht='20%'>SUBJECT</th>    <th width='40%'>CHAPTER</th>    <th width='40%'>HOME WORK</th>  </tr>    <tr>    <td>English</td><td>-</td>    <td>-</td>  </tr>  <tr>    <td>Maths</td>    <td>-</td>    <td>-</td>  </tr>  <tr>    <td>Science</td>    <td>-</td>    <td>-</td>  </tr>  <tr>    <td>GP/EVM/SST</td><td>-</td>    <td>-</td>  </tr>  <tr>    <td>Language</td><td>-</td>    <td>-</td>  </tr><tr>    <td>ICT</td><td>-</td>    <td>-</td>  </tr></tbody></table>"],
				["name" => "2-Column Table template", "value" => "<style>table-class {  font-family: arial, sans-serif;  border-collapse: collapse;  width: 100%;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 6px;}table-class>tr:nth-child(even) {  background-color: #dddddd;}</style><table class='table-class'>  <tbody><tr>    <th width='20%'>SUBJECT</th> <th width='80%'>HOME WORK</th>  </tr>    <tr>    <td>English</td>  <td>-</td>  </tr>  <tr>    <td>Maths</td>    <td>-</td>  </tr>  <tr>    <td>Science</td>    <td>-</td>  </tr>  <tr>    <td>GP/EVM/SST</td><td>-</td>  </tr>  <tr>    <td>Language</td><td>-</td>  </tr><tr>    <td>ICT</td><td>-</td>  </tr></tbody></table>"],
				["name" => "Simple Template", "value" => "<html><body><p><b>English</b><br>Put your homework here<br></p><p><b>Maths</b><br>Put your homework here<br></p></body></html>"],
				["name" => "Template 1", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold;font-size:20px;'>Subjects</span></td><td align='center'><span style='font-weight: bold;font-size:20px;'>Assignments</span></td></tr><tr><td>Mathematics</td><td><br></td></tr><tr><td>English</td><td><br></td></tr><tr><td>Social Science</td><td><br></td></tr><tr><td>Science</td><td><br></td></tr><tr><td>Languages</td><td><br></td></tr></tbody></table><p><br></p><table class='table table-bordered'><tbody><tr><td rowspan='2' style='height:100px;'>Notes :</td></tr></tbody></table><p><br></p>"],
				["name" => "Template 2", "value" => "<center><p></p><h4><span style='font-weight: bold;'>Homework Planner</span></h4><p></p></center><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold; font-size:12px;min-width: 90px'>Class</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Subject</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Homework</span></td><td align='center'><span style='font-weight: bold;font-size:12px;min-width: 116px'>Due Date</span></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p>"],
				["name" => "Template 3", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td align='center'><span style='font-weight: bold;font-size:14px; min-width: 116px;'>Subjects</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Homework</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Topics to be covered</span></td><td align='center'><span style='font-weight: bold;font-size:14px;min-width: 116px;'>Notes (if any)</span></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p>"],
				["name" => "Template 4", "value" => "<p><br></p><table class='table table-bordered'><tbody><tr><td style='min-width:95px;'><b>Subject</b></td><td><span style='font-weight: bold;font-size:17px;'>Mathematics</span></td></tr><tr><td>Reading</td><td>Something to Read</td></tr><tr><td>Writing</td><td>Something to Write</td></tr><tr><td>Notes (If any)</td><td>And here is a note</td></tr></tbody></table><p><br></p>"]
			  ];


		}


		public function dashboard(){
			$tsk_name= $this->settings->getSetting('student_task_module_name') != null ? $this->settings->getSetting('student_task_module_name') : 'Student Task';
			$site_url = site_url();
	        $data['tiles'] = array(
	          [
	            'title' => 'Add and View '. $tsk_name,
	            'sub_title' => 'Add and View Student Tasks',
	            'icon' => 'svg_icons/add.svg',
	            'url' => $site_url.'student_tasks/tasks',
	            'permission' => 1
	          ],
	          [
	            'title' => $tsk_name. ' Report',
	            'sub_title' => 'Student Task Report',
	            'icon' => 'svg_icons/studentwisehomeworkreport.svg',
	            'url' => $site_url.'student_tasks/tasks/student_tasks_report',
	            'permission' => 1
	          ]
			//   ,
	        //   [
	        //     'title' => 'Management Report',
	        //     'sub_title' => 'Task summary Report',
	        //     'icon' => 'svg_icons/assessment.svg',
	        //     'url' => $site_url.'student_tasks/tasks/task_summary_report',
	        //     'permission' => 1
	        //   ]

			,
			[
	            'title' => 'Testing Super Admin',
	            'sub_title' => 'Student Task Report',
	            'icon' => 'svg_icons/studentwisehomeworkreport.svg',
	            'url' => $site_url.'student_tasks/tasks/testing_for_superadmin_only',
	            'permission' => $this->authorization->isSuperAdmin()
	          ]
	      	);
	      $data['tiles'] = checkTilePermissions($data['tiles']);
	        if ($this->mobile_detect->isTablet()) {
	           $data['main_content']   = 'student_tasks/dashboard_tablet';
	        }else if($this->mobile_detect->isMobile()){
	          $data['main_content']    = 'student_tasks/dashboard_mobile';
	        }else {
			  $data['main_content']    = 'student_tasks/dashboard';      	
	        }
			// $data['main_content'] = 'student_tasks/dashboard';
    		$this->load->view('inc/template', $data);

		}

		function testing_for_superadmin_only() {
			// $superAdminData['dates']= $this->task->get_all_dates_to_tests();
			$superAdminData['main_content']    = 'student_tasks/testing_for_superadmin_only'; 
			$this->load->view('inc/template', $superAdminData);
		}

		public function index(){
			$data['templates'] = $this->templates;
    		$data['classesList'] = $this->task->getAllClasses();
				$data['classSectionsList'] = $this->task->getAllClassSections();
			$lesson_config = $this->settings->getSetting('lesson_plan');
			if($lesson_config){
				$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
				$data['size'] = $lesson_config->size;
			}else{
				$data['lp_task_max_submit_file'] = "2";
				$data['size'] = "5MB";
			}
			//echo "<pre>";print_r($staff_login);die();
			$data['staff_login'] = $this->authorization->getAvatarStakeHolderId();

			$data['selected_staff_option'] = $data['staff_login'];
			

			$data['staff_option'] = $this->task->getStaffList();
		
    		$data['resource_types'] = $this->task->getResourceTypes();
    		
				$data['is_task_admin'] = $this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN');

				// $data['aws'] = $this->get_chunk_upload_secret();
				// $this->load->library('aws_library');
				// $data['aws'] = $this->aws_library->getSignatureData();

			$data['homework_default_template'] = $this->settings->getSetting('homework_default_template');
		 	if ($this->mobile_detect->isTablet()) {
	           $data['main_content']   = 'student_tasks/index_tablet';
	        }else if($this->mobile_detect->isMobile()){
	          $data['main_content']    = 'student_tasks/index_mobile';
	        }else{
	          $data['main_content']    = 'student_tasks/index_desktop_new';   	
	          // $data['main_content']    = 'student_tasks/index_desktop';   	
			}
    		$this->load->view('inc/template', $data);
		}

		public function confirmResubmission() {
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			$comments = $_POST['comments'];
			$status = $this->task->confirmResubmission($lp_tasks_student_id, $comments);
			echo $status;
		}
		

		public function addTask(){
			$data['templates'] = $this->templates;
			$data['classesList'] = $this->task->getAllClasses();
			$data['classSectionsList'] = $this->task->getAllClassSections();
			$data['resource_types'] = $this->task->getResourceTypes();
			$data['homework_default_template'] = $this->settings->getSetting('homework_default_template');
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']    = 'student_tasks/add_task_tablet';
			}else if($this->mobile_detect->isMobile()){
				$data['main_content']    = 'student_tasks/add_task_mobile';
			}
			$this->load->view('inc/template', $data);
			
		}

		public function getClassName(){
			$data= $this->task->getClassName();
			echo json_encode($data);
		}

		public function getSectionsandSubjects(){
			$data['sectionsList'] = $this->task->getSectionsList();
			$data['subjectsList'] = $this->task->getSubjectsList();
			$data['groupsList'] = $this->task->getGroupList();
			$data['studentsList'] = $this->task->getClassStudentsList();
			echo json_encode($data);
		}

		public function getResources(){
			$data['resources'] = $this->task->getResources();
			echo json_encode($data);
		}

		public function getSelectedResources(){
			$resources_ids_string = $_POST['resources_ids_string'];
			$resources_ids = json_decode($resources_ids_string);
			$data['resources'] = $this->task->getSelectedResources($resources_ids);
			echo json_encode($data);
		}

		private function __s3FileUpload($file) {
			if($file['tmp_name'] == '' || $file['name'] == '') {
			  return ['status' => 'empty', 'file_name' => ''];
			}        
			return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'tasks');
		}

		public function getTemplate () {
			$templateName = $this->input->post('templateName');
			echo json_encode($this->__getTemplateByName($templateName));
		}
		private function __getTemplateByName ($templateName) {
			$templateValue = '';
			foreach ($this->templates as $temp) {
				if ($temp['name'] == $templateName) {
				$templateValue = $temp['value'];
				break;
				}
			}
			return $templateValue;
		}

		private function _taskPublisher() {
			$assign_type = $_POST['assign_type'];
			//get the students based on type
			if($assign_type == 'group') {
				$students_data = $this->task->getGroupStudents($_POST['group_id'], $_POST['class_id']);
			} else if($assign_type == 'student') {
				$students_data = $this->task->getStudentsByIds($_POST['student_ids']);
			} else {
				$students_data = $this->task->getStudents($_POST['section_ids']);
			}

			if(empty($students_data)) return 0;

			$student_ids = array();
			$class_section_ids = array();
			foreach ($students_data as $key => $std) {
				array_push($student_ids, $std->student_id);
				if(!in_array($std->section_id, $class_section_ids)) {
					array_push($class_section_ids, $std->section_id);
				}
			}
			$files_array = array();
			$files_string = '';
			if(isset($_FILES['attachment'])) {
				foreach ($_FILES['attachment']['name'] as $key => $file_name) {
					$file = array(
						'name' => $file_name,
						'type' => $_FILES['attachment']['type'][$key],
						'tmp_name' => $_FILES['attachment']['tmp_name'][$key],
						'error' => $_FILES['attachment']['error'][$key],
						'size' => $_FILES['attachment']['size'][$key]
					);
					$path = $this->__s3FileUpload($file);
					if($path['file_name'] != '') {
						array_push($files_array, array('name' => $file_name, 'path' => $path['file_name']));
					}
				}
				// $files_string = implode(",", $files_array);
				if(!empty($files_array)) {
					$files_string = json_encode($files_array);
				}
			}
		
			$status = $this->task->insertNewTask($class_section_ids, $students_data,$files_string);
			$pub_tym= $_POST['pub_tym'];
			if($pub_tym == 'immediate' && !empty($student_ids) && $status) {
				/*$this->load->helper('notification_helper');
				$title = $this->settings->getSetting('school_name');
				$message = 'New Task is added for your kid';
				$url = site_url('parent_controller/student_task_view');
				sendStudentNotifications($student_ids, $title, $message, $url, 'Both');*/

				$this->load->helper('texting_helper');
				$input_arr = array();
				$input_arr['student_ids'] = $student_ids;
				$input_arr['mode'] = 'notification';
				$input_arr['source'] = 'Student Task';
				$input_arr['send_to'] = 'Both';
				$input_arr['message'] = 'New Task is added for your kid';
				$input_arr['student_url'] = site_url('Parent_controller/student_task_view');
				// echo "<pre>"; print_r($input_arr); die();
				sendText($input_arr);
			}
			// echo "<pre>"; print_r($status); die();
			return $status;
		}

		public function publishNewTask(){
			echo $this->_taskPublisher();
		}

		public function publishNewTaskMobile(){
			$status = $this->_taskPublisher();
			if($status){
				$this->session->set_flashdata('flashSuccess', 'Task added successfully');
			} else {
				$this->session->set_flashdata('flashError', 'Something Wrong..');
			}
			redirect('student_tasks/tasks');
		}

		public function view_task_details($task_id, $section_id){
			$data['taskDetails'] = $this->task->getTaskDetails($task_id, $section_id);
			// $data['aws'] = $this->get_chunk_upload_secret();
			// $this->load->library('aws_library');
			// $data['aws'] = $this->aws_library->getSignatureData();
			$lesson_config = $this->settings->getSetting('lesson_plan');
			if($lesson_config){
				$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
				$data['size'] = $lesson_config->size;
			}else{
				$data['lp_task_max_submit_file'] = "2";
				$data['size'] = "5MB";
			}
			$data['staff_login'] = $this->authorization->getAvatarStakeHolderId();
				$data['is_task_admin'] = $this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN');


			// echo "<pre>";print_r($data);die();
			// $data['taskName'] = $taskDetails['name'];
			
		
			$data['main_content']    = 'student_tasks/mobile_task_details';      	
			$this->load->view('inc/template', $data);
		}

		public function view_task_details_staff($task_id, $staff_id){
			$data['taskDetails'] = $this->task->getTaskDetailsStaff($task_id, $staff_id);
			// $data['aws'] = $this->get_chunk_upload_secret();
			// $this->load->library('aws_library');
			// $data['aws'] = $this->aws_library->getSignatureData();
			$lesson_config = $this->settings->getSetting('lesson_plan');
			if($lesson_config){
				$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
				$data['size'] = $lesson_config->size;
			}else{
				$data['lp_task_max_submit_file'] = "2";
				$data['size'] = "5MB";
			}
			$data['staff_login'] = $this->authorization->getAvatarStakeHolderId();
			$data['is_task_admin'] = $this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN');


			// echo "<pre>";print_r($data);die();
			// $data['taskName'] = $taskDetails['name'];
			
		
			$data['main_content']    = 'student_tasks/mobile_task_details_staff';      	
			$this->load->view('inc/template', $data);
		}

		public function view_task_data($task_id, $id, $type) {
			$data['task_id'] = $task_id;
			$data['id'] = $id;
			$data['type'] = $type;
			// $data['aws'] = $this->get_chunk_upload_secret();
			// $this->load->library('aws_library');
			// $data['aws'] = $this->aws_library->getSignatureData();
			$lesson_config = $this->settings->getSetting('lesson_plan');
			if($lesson_config){
				$data['lp_task_max_submit_file'] = $lesson_config->lp_task_max_submit_file;
				$data['size'] = $lesson_config->size;
			}else{
				$data['lp_task_max_submit_file'] = "2";
				$data['size'] = "5MB";
			}
			$data['staff_login'] = $this->authorization->getAvatarStakeHolderId();
			$data['is_task_admin'] = $this->authorization->isAuthorized('STUDENT_TASKS.TASKS_ADMIN');
			$data['main_content']    = 'student_tasks/mobile_task_data';      	
			$this->load->view('inc/template', $data);
		}

		public function submitEvaluation(){
			$id = $this->input->post('lp_tasks_student_id');
			// $file = $this->s3FileUpload($_FILES['evaluation_files']);
			// $status = $this->task->submitEvaluation($id, $file['file_name']);
			$status = $this->task->submitEvaluation($id);
				if($status) {
					$this->load->helper('texting_helper');
					$student_id = $this->task->getStudentId($id);
					$taskName = $this->task->getTaskName($id);
					$notify_array = [];
					$notify_array['student_ids'] = [$student_id];
					$notify_array['mode'] = 'notification';
					$notify_array['send_to'] = 'Both';
					$notify_array['source'] = 'Student Tasks';
					$notify_array['student_url'] = site_url('parent_controller/student_task_view');
					$notify_array['message'] = 'Evaluation done for '.$taskName->task_name;
					sendText($notify_array);
					echo 1;
			} else {
				echo 0;
			}
		}

		public function getSubjectsList(){
			$class_id = $this->task->getClassId($_POST['class_section_id']);
			$data = $this->task->getSubjetsListByClass($class_id);
			echo json_encode($data);
		}

		public function getTasks() {
			if(isset($_POST['from_date']) && isset($_POST['end_date'])) {
				$data['tasks'] = $this->task->getFilteredTasks();
				echo json_encode($data);
			} else {
				echo json_encode([]);
			}
		}

		public function getSelectedTasks(){
			$data['tasks'] = $this->task->getSelectedTasks();
			//$data['tasks'] = $this->task->getSelectedTasksStaffWise();
			echo json_encode($data);
		}
		public function getSelectedTasksStaffWise(){
			//$data['tasks'] = $this->task->getSelectedTasks();
			$data['tasks'] = $this->task->getSelectedTasksStaffWise();
			echo json_encode($data);
		}

		public function getTaskData() {
			// echo '<pre>'; print_r($_POST); die();
			if(!isset($_POST['task_id']) || $_POST['task_id'] <= 0 || $_POST['task_id'] == '') {
				$this->session->set_flashdata('flashError', 'Something Wrong..');
				redirect('student_tasks/tasks');
			}
			if(!isset($_POST['id'])) {
				$this->session->set_flashdata('flashError', 'Something Wrong..');
				redirect('student_tasks/tasks');
			}
			if(!isset($_POST['type'])) {
				$this->session->set_flashdata('flashError', 'Something Wrong..');
				redirect('student_tasks/tasks');
			}
			$task_id = $_POST['task_id'];
			$id = $_POST['id'];
			$type = $_POST['type'];
			$data['single_task'] = $this->task->getTaskData($task_id,$id, $type);
			if(empty($data['single_task'])) {
				$this->session->set_flashdata('flashError', 'Something Wrong..');
				redirect('student_tasks/tasks');
			}
			// if(empty($data['single_task']) || !isset($data['single_task']->resource_ids) || empty($data['single_task']->resource_ids)) {
			// 	redirect('student_tasks/tasks/dashboard'); 
			// }
			$data['resources'] = array();
			if(isset($data['single_task']->resource_ids) && !empty(json_decode($data['single_task']->resource_ids))){
				$resources = $this->task->getSelectedResources(json_decode($data['single_task']->resource_ids));
		        if(sizeof($resources)!=0) {
		          	foreach ($resources as $key =>$value) {
		          		if($value->resource_type=='Video Link'){
							//Resource is a video link
							array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $value->resource_file));
						} else if($value->resource_type=='Hyper Link'){

							array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $value->resource_file));

						} else if($value->resource_type=='Vimeo') {
							array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $value->resource_file));
						} else {
							//File is stored in S3
							if($value->resource_file!=''){
								array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getSignedUrlWithExpiry($value->resource_file, '+5 minutes')));
							}
							else{
								array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => 'No Files'));
							}	
						}
		        	}
		      	}
			}
			// getting uploaded task documents
			$documents = $this->task->getTaskDocuments($task_id);
			$uploaded_task_document=json_decode($documents->task_file_path);
			
			if (!empty($uploaded_task_document)) {
				foreach ($uploaded_task_document as $key => $value) {
					$value->file_path=$this->filemanager->getFilePath($value->path);
					$f = explode('.', $value->name);
					$value->file_type = strtolower($f[count($f) - 1]);
				}
			}
			$data['uploaded_task_documents']=$uploaded_task_document;
			// echo '<pre>'; print_r($data); die();
			echo json_encode($data);
		}

		public function getSingleTaskDetails(){
			$task_id = $_POST['task_id'];
			$section_id = $_POST['section_id'];
			$data['single_task'] = $this->task->getSingleTaskDetails($task_id,$section_id);
			$data['resources'] = array();
			if(!empty(json_decode($data['single_task']->resource_ids))){
				$resources = $this->task->getSelectedResources(json_decode($data['single_task']->resource_ids));
		        if(sizeof($resources)!=0) {
		          foreach ($resources as $key =>$value) {
		          	if($value->resource_type=='Video Link'){
								//Resource is a video link
								array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $value->resource_file));
							} else {
								//File is stored in S3
								if($value->resource_file!=''){
									array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
								}
								else{
									array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => 'No Files'));
								}	
							}
		        }
		      }
			}
			echo json_encode($data);
		}
		public function getSingleTaskDetailsStaffwise(){
			$task_id = $_POST['task_id'];
			$staff_id = $_POST['staff_id'];
			$data['single_task'] = $this->task->getSingleTaskDetailsStaffwise($task_id,$staff_id);
			$data['resources'] = array();
			if(!empty(json_decode($data['single_task']->resource_ids))){
				$resources = $this->task->getSelectedResources(json_decode($data['single_task']->resource_ids));
		        if(sizeof($resources)!=0) {
		          foreach ($resources as $key =>$value) {
		          	if($value->resource_file!=''){
		            	array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
		          	}
		          	else{
		            	array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => 'No Files'));
		          	}
		          }
		        }
			}
			echo json_encode($data);
		}

		public function getYouTubeVideo(){
			$resource = $this->task->getResourceToPlay();
			$data['resources'] = array(); 
				if(sizeof($resource)!=0) {
					foreach ($resource as $key =>$value) {
						if($value->resource_file!=''){
							array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' =>$value->resource_file));
						}
					}
				}
				echo json_encode($data);
		}

		public function submit_evaluated_files(){
			$location = $_POST['location'];
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];


	
		$order = $this->input->post('file_order');
		$fileName = $this->input->post('fileName');
		$prefix = $this->filemanager->getFilePath('');
		$loc = str_replace($prefix, "", $location);
		// foreach($location as $key=>$row){
		$files_data[] = array(
						'lp_tasks_students_id' => $lp_tasks_student_id,
						'file_path' => $loc,
						'file_order' => $order,
						'type' => 'Evaluation',
						'file_name' => $fileName
					);
		$status = $this->task->submit_evaluated_files($lp_tasks_student_id, $files_data);
		if ($status) {
		
			$this->session->set_flashdata('flashSuccess', 'Successfully submitted');
		} else {
			$this->session->set_flashdata('flashError', 'Something Wrong..');
		}
		echo json_encode($status);
		}

		public function showAddedFiles(){
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			// $studentId = $this->task->getStudentIdOfLoggedInParent();
			$data['studentName'] = $this->task->getStudentName($lp_tasks_student_id);
	
			$data['fileDetails'] = $this->task->getAddedFileDetails($lp_tasks_student_id);
			echo json_encode($data);
		}

		public function deleteFile(){
			$file_id = $_POST['file_id'];
			
			$data = $this->task->deleteFile($file_id);
			echo ($data);
		}

		public function getSectionWiseTaskDetails(){
			$data['single_section_data'] = $this->task->getSectionWiseTaskDetails();
			echo json_encode($data);
		}
		public function getSectionWiseTaskDetailsStaffwise(){
			$data['single_section_data'] = $this->task->getSectionWiseTaskDetailsStaffwise();
			echo json_encode($data);
		}

		public function getTaskReadStatusList() {
			$data['students'] = $this->task->getTaskReadStatusList();
			echo json_encode($data);
		}

		public function disableTask(){
			$data = $this->task->disableTask();
			echo json_encode($data);
		}

		public function downloadTasksAttachment($id, $index){
			$file_link = $this->task->downloadTaskAttachment($id);

			$signed_resource = $this->filemanager->getSignedUrlWithExpiry($file_link->resource_file, '+5 minutes');

			//Get actual file name
			$file = explode("/", $file_link->resource_file);
			$file_name = 'task'.($index+1);
			$fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
			//Download the file
			$data = file_get_contents($signed_resource);
			$this->load->helper('download');
			force_download($fname, $data, TRUE);
			$this->load->library('user_agent');
			redirect($this->agent->referrer());	

			// $file_link = $this->task->downloadTaskAttachment($id);
			
			// $link = $file_link->resource_file;
			// $file = explode("/", $link);
			// $file_name = 'task'.($index+1);
			// $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
			// // echo '<pre>'; print_r($fname); die();
			// $url = $this->filemanager->getFilePath($link);
			// $data = file_get_contents($url);
			// $this->load->helper('download');
			// force_download($fname, $data, TRUE);
			// $this->load->library('user_agent');
			// redirect($this->agent->referrer());
		}

		public function downloadTaskDocumentAttachment($link){
			$link=str_replace("__","/",$link);

			$file = explode("/", $link);
			$file_name = 'document';
			$fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
			$url = $this->filemanager->getFilePath($link);
			$data = file_get_contents($url);
			$this->load->helper('download');
			force_download($fname, $data, TRUE);
		}

		public function getTaskSubmissionData(){
			$task_id = $_POST['task_id'];
			$id = $_POST['id'];
			$type = $_POST['type'];

			$data['submissions'] = $this->task->getTaskSubmissionData($task_id,$id, $type);
			// $data['submissions'] = array();
			$temp = $this->task->getEvaluationStatus($task_id);
			$data['require_evaluation'] = $temp->require_evaluation;
			$data['task_type'] = $this->task->getTaskType($task_id);
			$data['status'] = $temp->status;
			$data['created_by'] = $temp->created_by;
	        echo json_encode($data);
		}

		public function getStudentSubmissions(){
			$task_id = $_POST['task_id'];
			$section_id = $_POST['section_id'];

			$data['submissions'] = $this->task->getStudentSubmissions($task_id,$section_id);
			// $data['submissions'] = array();
			$temp = $this->task->getEvaluationStatus($task_id);
			$data['require_evaluation'] = $temp->require_evaluation;
			$data['task_type'] = $this->task->getTaskType($task_id);
			$data['status'] = $temp->status;
			$data['created_by'] = $temp->created_by;
	        echo json_encode($data);
		}
		public function getStudentSubmissionsStaffwise(){
			$task_id = $_POST['task_id'];
			$staff_id = $_POST['staff_id'];

			$data['submissions'] = $this->task->getStudentSubmissionsStaffwise($task_id,$staff_id);
			// $data['submissions'] = array();
			$temp = $this->task->getEvaluationStatus($task_id);
			$data['require_evaluation'] = $temp->require_evaluation;
			$data['task_type'] = $this->task->getTaskType($task_id);
			$data['status'] = $temp->status;
			$data['created_by'] = $temp->created_by;
			echo json_encode($data);
		}

		public function getSubmittedFiles(){
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			$data['submitted_files'] = array();
			$submitted_files = $this->task->getSubmittedFiles($lp_tasks_student_id);
			if(sizeof($submitted_files)!=0) {
				foreach ($submitted_files as $key =>$value) {
					if($value->file_path!=''){
						$f = explode('.', $value->file_path);
						$f_type = strtolower($f[count($f)-1]);
						array_push($data['submitted_files'], array('file_id'=>$value->id,'file_order'=>$value->file_order,'file_name'=>$value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path), 'file_type' => $f_type));
					}
				}
			}
			echo json_encode($data);
		}

		public function getSubmittedFilesV2(){
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			$data['submitted_files'] = array();
			$submitted_files = $this->task->getSubmittedFilesV2($lp_tasks_student_id);
			$task_status = $this->task->getTaskStudentStatus($lp_tasks_student_id);
			// echo '<pre>'; print_r($submitted_files); die();
			if(sizeof($submitted_files)!=0) {
				foreach ($submitted_files as $key =>$value) {
					if($value->file_path!=''){
						$file = array();
						$f = explode('.', $value->file_path);
						$f_type = strtolower($f[count($f)-1]);
						$file['file_path'] = $this->filemanager->getFilePath($value->file_path);
						$file['file_order'] = $value->file_order;
						$file['file_id'] = $value->id;
						$file['file_name'] = $value->file_name;
						$file['file_type'] = $f_type;
						$file['evaluation_id'] = $value->evaluation_id;
						if($value->evaluation_file_path!=''){
							$ef = explode('.', $value->evaluation_file_path);
							$e_ext = strtolower($ef[count($ef)-1]);
							$ef_type = ($e_ext)?$e_ext:$f_type;
							$file['evaluation_file_type'] = $ef_type;
							$file['evaluation_file_path'] = $this->filemanager->getFilePath($value->evaluation_file_path);
							$file['evaluation_file_id'] = $value->evaluation_file_id;
							$file['evaluation_file_name'] = $value->evaluation_file_name;
						}
						array_push($data['submitted_files'], $file);
					}
				}
			}
			$data['task_student'] = $task_status;
			echo json_encode($data);
		}

		public function getEvaluatedFiles(){
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			$data['created_by'] = $this->task->getCreatedBy($lp_tasks_student_id);
			$data['evaluated_files'] = array();
			$evaluated_files = $this->task->getEvaluatedFiles($lp_tasks_student_id);
			if(sizeof($evaluated_files)!=0) {
				foreach ($evaluated_files as $key =>$value) {
					if($value->file_path!=''){
						$f = explode('.', $value->file_path);
						$f_type = strtolower($f[count($f)-1]);
						array_push($data['evaluated_files'], array('file_id'=>$value->id,'file_order'=>$value->file_order,'file_name'=>$value->file_name,  'file_path' => $this->filemanager->getFilePath($value->file_path), 'file_type' => $f_type));
					}
				}
			}
			echo json_encode($data);
		}

		public function saveEvaluationFile() {
			$status = 0;
			if(!empty($_FILES['file'])) {
				$file = $this->s3FileUpload($_FILES['file'], 'evaluation');
				if($file['file_name'] != '') {
					$status = $this->task->saveEvaluationFile($file['file_name'], $_POST['submission_file_id']);
				}
			} 
			echo $status;
		}

		public function getFiles(){
			$file_id = $_POST['file_id'];
			$data['created_by'] = $this->task->getCreatedBy($lp_tasks_student_id);
			$data['evaluated_files'] = array();
			$evaluated_files = $this->task->getFiles($file_id);
			if(sizeof($evaluated_files)!=0) {
				foreach ($evaluated_files as $key =>$value) {
					if($value->file_path!=''){
						array_push($data['evaluated_files'], array( 'file_path' => $this->filemanager->getFilePath($value->file_path)));
					}
				}
			}
			echo json_encode($data);
		}

		

		private function get_chunk_upload_secret() {
			$this->config->load('s3');
			$bucket = $this->config->item('s3_bucket');
			$accessKeyId = $this->config->item('access_key');
			$this->load->library('aws_library');
		    $general = $this->aws_library->getPolicyAndSignature('*');
		    $pdf = $this->aws_library->getPolicyAndSignature('application/pdf');
		    return array('access' => $accessKeyId, 'signature' => $general['signature'], 'pdf_signature' => $pdf['signature'], 'policy' => $general['policy'], 'pdf_policy' => $pdf['policy'], 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder'], 'short_date' => $general['short_date'], 'iso_date' => $general['iso_date'], 'pdf_short_date' => $pdf['short_date'], 'pdf_iso_date' => $pdf['iso_date'], 'region' => $general['region']);

		
			// prepare policy
			$policy = base64_encode(json_encode(array(
				// ISO 8601 - date('c'); generates uncompatible date, so better do it manually
				'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),  
				'conditions' => array(
					array('bucket' => $bucket),
					array('acl' => 'public-read'),
					array('starts-with', '$key', ''),
					// for demo purposes we are accepting only images
					// array('starts-with', '$Content-Type', 'application/pdf, *'),
					array('starts-with', '$Content-Type', '*'),
					// Plupload internally adds name field, so we need to mention it here
					array('starts-with', '$name', ''),  
					array('success_action_status' => '201'),
					array('starts-with', '$chunk', ''),
					array('starts-with', '$chunks', ''),
					array('starts-with', '$Filename', ''),
				)
			)));
		
			// prepare policy for pdf
			$pdf_policy = base64_encode(json_encode(array(
				// ISO 8601 - date('c'); generates uncompatible date, so better do it manually
				'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),  
				'conditions' => array(
					array('bucket' => $bucket),
					array('acl' => 'public-read'),
					array('starts-with', '$key', ''),
					// for demo purposes we are accepting only images
					array('starts-with', '$Content-Type', 'application/pdf'),
					// Plupload internally adds name field, so we need to mention it here
					array('starts-with', '$name', ''),  
					array('success_action_status' => '201'),
					array('starts-with', '$chunk', ''),
					array('starts-with', '$chunks', ''),
					array('starts-with', '$Filename', ''),
				)
			)));
		
			// sign policy
			$signature = base64_encode(hash_hmac('sha1', $policy, $secret, true));
			$pdf_signature = base64_encode(hash_hmac('sha1', $pdf_policy, $secret, true));
			return array('access' => $accessKeyId, 'signature' => $signature, 'pdf_signature' => $pdf_signature, 'policy' => $policy, 'pdf_policy' => $pdf_policy, 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder']);
		}

		public function saveEvaluation() {
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			$comment = $_POST['comment'];
			$status = $this->task->saveEvaluation($lp_tasks_student_id, $comment);
			echo $status;
		}

		private function _getFileExtension($url){
			$file_path = $this->filemanager->getFilePath($url);
			$headers = get_headers($url, TRUE);
			$type = $headers['Content-Type'];
		    $mime_types = MIME_TYPE_EXTENSION;
		    if(array_key_exists($type, $mime_types)) {
		    	return $mime_types[$type];
		    }
		    return '';
		}

		public function downloadSubmissionAttachment($id, $index=0){
			$file_link = $this->task->downloadSubmissionAttachment($id);
			$fileStudentName = $this->task->getFileStudentName($id);
			$student_name = $fileStudentName->first_name;
			$fileName = $fileStudentName->file_name;
		
			
			$link = $file_link->file_path;
			$file = explode("/", $link);
			// $file_name = 'task'.($index+1);
			$file_name = $student_name.'_'.$fileName;

			// $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];

			$url = $this->filemanager->getFilePath($link);
			$ext = explode(".", $file[count($file)-1])[1];
			// $ext = ($ext == '')?'png':$ext;
			if($ext == '') {
				$ext = $this->_getFileExtension($url);
			}
			$fname = $file_name .'.'. $ext;
			// echo '<pre>'; print_r($fname); die();
			$data = file_get_contents($url);
			$this->load->helper('download');
			force_download($fname, $data, TRUE);
			$this->load->library('user_agent');
			redirect($this->agent->referrer());
		}
		public function downloadEvaluationAttachment($id, $index=0){
			$file_link = $this->task->downloadEvaluationAttachment($id);
			$fileStudentName = $this->task->getFileStudentName($id);
			$student_name = $fileStudentName->first_name;
			$fileName = $fileStudentName->file_name;
			
			$link = $file_link->file_path;
			$file = explode("/", $link);
			// $file_name = 'evaluation'.($index+1);
			$file_name = $student_name.'_'.$fileName;
			$ext = explode(".", $file[count($file)-1])[1];
			$ext = ($ext == '')?'png':$ext;
			$fname = $file_name .'.'. $ext;
			// $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
			// echo '<pre>'; print_r($fname); die();
			$url = $this->filemanager->getFilePath($link);
			$data = file_get_contents($url);
			$this->load->helper('download');
			force_download($fname, $data, TRUE);
			$this->load->library('user_agent');
			redirect($this->agent->referrer());
		}

		public function discardTask_info(){
			$task_id=$_POST['task_id'];
			$data = $this->task->discardTask_info($task_id);
			echo json_encode($data);
		}

		public function getTaskSettings() {
			$task_id=$_POST['task_id'];
			$data = $this->task->getTaskSettings($task_id);
			echo json_encode($data);
		}

		public function changeSubmissionStatus() {
			$task_id = $_POST['task_id'];
			$status = $_POST['status'];
			$result = $this->task->changeSubmissionStatus($task_id, $status);
			echo $result;
		}

		public function changeEvaluationReleaseStatus() {
			$task_id = $_POST['task_id'];
			$status = $_POST['status'];
			$result = $this->task->changeEvaluationReleaseStatus($task_id, $status);
			echo $result;
		}

		public function discardTask(){
			$task_id = $_POST['task_id'];
			$data = $this->task->discardTask($task_id);
			$stdIds = $this->task->getStudentIds($task_id);
			$taskName = $this->task->getTask($task_id);
			$student_ids = array();
			foreach ($stdIds as $key => $value) {
				array_push($student_ids, $value->student_id);
			}
			if(!empty($student_ids)) {
				$this->load->helper('notification_helper');
				$title = $this->settings->getSetting('school_name');
				$message = $taskName->task_name.' task unpublished';
				$url = site_url('parent_controller/student_task_view');
				sendStudentNotifications($student_ids, $title, $message, $url, 'Both');
			}
			echo json_decode($data);
		}

		

		public function getEvaluationDetails(){
			$id=$_POST['id'];
			$temp = $this->task->getEvaluationDetails($id);
			$data['evaluation'] = array();
			if($temp->evaluation_status==1){
				if($temp->evaluation_files!=''){
					array_push($data['evaluation'],array('id'=>$temp->id,'evaluation_status'=>$temp->evaluation_status,'evaluation_comments'=>$temp->evaluation_comments,'evaluation_files'=>$this->filemanager->getFilePath($temp->evaluation_files),'evaluation_on'=>$temp->evaluation_on,'student_id'=>$temp->student_id, 'student_name'=>$temp->first_name));
				}
				else{
					array_push($data['evaluation'],array('id'=>$temp->id,'evaluation_status'=>$temp->evaluation_status,'evaluation_comments'=>$temp->evaluation_comments,'evaluation_files'=>'No Files Uploaded','evaluation_on'=>$temp->evaluation_on,'student_id'=>$temp->student_id, 'student_name'=>$temp->first_name));
				}
			}
			else{
				$data['evaluation']=$temp;
			}
			echo json_encode($data);
		}

		public function getStudentName(){
			$lp_tasks_students_id = $_POST['lp_tasks_students_id'];
			$data['studentName'] = $this->task->getStudentName($lp_tasks_students_id);
			echo json_encode($data);
		}

		

		public function s3FileUpload($file, $folder='profile') {
	        if($file['tmp_name'] == '' || $file['name'] == '') {
	          return ['status' => 'empty', 'file_name' => ''];
	        }        
	        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'], $folder);
	    } 

	    public function getResourceToPlay(){
	    	$resource = $this->task->getResourceToPlay();
	    	$data['resources'] = array(); 
	        if(sizeof($resource)!=0) {
	          foreach ($resource as $key =>$value) {
	          	if($value->resource_file!=''){
	            	array_push($data['resources'], array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
	          	}
	          }
	        }
	    echo json_encode($data);
		}
		
		public function student_tasks_report(){
			$data['classesList'] = $this->task->getAllClasses();
			$data['classSectionsList'] = $this->task->getAllClassSections();
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']   = 'student_tasks/report_task_tablet';
			}else if($this->mobile_detect->isMobile()){
				$data['main_content']    = 'student_tasks/report_task_mobile';
			}else{
				$data['main_content']    = 'student_tasks/report_tasks';      	
			}

			// $data['main_content']='student_tasks/report_tasks';
			$this->load->view('inc/template', $data);
			
		}

		public function view_report_details($student_id, $subject_id, $from_date, $end_date){
			$data['taskDetails'] = $this->task->getStudentTaskDetailsMobile($student_id, $subject_id, $from_date, $end_date);
			$data['selected_date'] = $from_date;
			$data['end_date'] = $end_date;
			$data['subject_id'] = $subject_id;
			// echo "<pre>";print_r($data);die();


			$data['main_content']    = 'student_tasks/report_details_mobile';      	
			$this->load->view('inc/template', $data);
		}
		 
		public function getStudentId(){
			$data['getStudent']=$this->task->getStudentDetails();
			$data['getCount'] = $this->task->getTaskCount();
			$data['studentCount'] = $this->task->getStudentCount();
			$data['submissionCount'] = $this->task->getSubmissionCount();
			echo json_encode($data);

		}

		public function getStudentTaskDetails(){
			$data['taskDetails'] = $this->task->getStudentTaskDetails();

			echo json_encode($data);		
		}

		public function getStudentSubmissionsTasks(){
			// $task_id = $_POST['task_id'];
			$student_id = $_POST['student_id'];
			$section_id = $_POST['section_id'];
			$subject_id = $_POST['subject_id'];
			
			$data['submissions'] = $this->task->getStudentSubmissionsTasks($student_id,$section_id,$subject_id);
			$temp = $this->task->getEvaluationStatusTasks($student_id);
			$data['require_evaluation'] = $temp->require_evaluation;
			$data['status'] = $temp->status;
			$data['created_by'] = $temp->created_by;
			echo json_encode($data);
		}

		public function getStudentSubmissionsTasksMobile(){
				$student_id = $_POST['student_id'];
				$section_id = $_POST['section_id'];
				$subject_id = $_POST['subject_id'];
				$data['submissions'] = $this->task->getStudentSubmissionsTasks($student_id,$section_id,$subject_id);
				$temp = $this->task->getEvaluationStatusTasks($student_id);
				$data['require_evaluation'] = $temp->require_evaluation;
				$data['status'] = $temp->status;
				$data['created_by'] = $temp->created_by;
				echo json_encode($data);
			}
		
		public function getEvaluationDetailsTasks(){
			$id=$_POST['id'];
			$task_id = $_POST['task_id'];
			$temp = $this->task->getEvaluationDetailsTasks($id);
			$data['evaluation'] = array();
			if($temp->evaluation_status==1){
				if($temp->evaluation_files!=''){
					array_push($data['evaluation'],array('id'=>$temp->id,'evaluation_status'=>$temp->evaluation_status,'evaluation_comments'=>$temp->evaluation_comments,'evaluation_files'=>$this->filemanager->getFilePath($temp->evaluation_files),'evaluation_on'=>$temp->evaluation_on,'task_id'=>$temp->task_id));
				}
				else{
					array_push($data['evaluation'],array('id'=>$temp->id,'evaluation_status'=>$temp->evaluation_status,'evaluation_comments'=>$temp->evaluation_comments,'evaluation_files'=>'No Files Uploaded','evaluation_on'=>$temp->evaluation_on,'task_id'=>$temp->task_id));
				}
			}
			else{
				$data['evaluation']=$temp;
			}
			echo json_encode($data);
		}
		public function task_summary_report(){
			if ($this->mobile_detect->isTablet()) {
				$data['main_content']='student_tasks/task_summary_tablet';
			}else if($this->mobile_detect->isMobile()){
				$data['main_content']='student_tasks/task_summary_mobile';
			}else{
				$data['main_content']='student_tasks/task_summary';    	
			}
			$this->load->view('inc/template', $data);
			
			
		}
		 public function  report_details(){
			$data['taskDetails'] = $this->task->getTaskClassesSections(); 
			$data['tasks'] = $this->task->getTotalTasks();
			$data['submissioncount']=$this->task->getTotalSubmissions();
			$data['TotalStudents']=$this->task->getTotalStudents();
			echo json_encode($data);
		 } 

		 public function getConnectionDetails(){
			 $data['connection']=$this->task->getSectionTasks(); 
			 echo json_encode($data);
		 }

		
		public function getSubjectAssessments() {
			$subject_id = $_POST['subject_id'];
			$data = $this->task->getSubjectAssessments($subject_id);
			echo json_encode($data);
		}
	  
		public function getAssessmentQuestions() {
			$assessment_id = $_POST['assessment_id'];
			$data = $this->task->getAssessmentQuestions($assessment_id);
			echo json_encode($data);
		}

		public function getAssessmentResult() {
			$task_id = $_POST['task_id'];
			$assessment_id = $_POST['assessment_id'];
			$student_id = $_POST['student_id'];
			$this->load->model('parent_model');
			$data = $this->parent_model->getAssessmentQuestions($task_id, $assessment_id, $student_id);
			echo json_encode($data);
		}

		public function cancelEvaluation() {
			$lp_tasks_student_id = $_POST['lp_tasks_student_id'];
			echo $this->task->cancelEvaluation($lp_tasks_student_id);
		}

		public function sendReminders() {
			// echo "<pre>";print_r($_POST);die();
			$this->load->helper('texting_helper');
			$input_arr = array();
			$input_arr['student_ids'] = $_POST['reminder_stduents'];
			$input_arr['Title'] = 'Task Reminder';
			$input_arr['mode'] = 'notification';
			$input_arr['source'] = 'Student Task';
			$input_arr['send_to'] = 'Both';
			$input_arr['message'] = $_POST['reminder_message'];
			$input_arr['student_url'] = site_url('parent_controller/student_task_view');
			$result = sendText($input_arr);
			echo json_encode($result);
		}

		public function edit_task_if_possible() {
			echo  $this->task->edit_task_if_possible();
		}

		public function edit_task_mob_view($task_id) {
			$data['templates'] = $this->templates;
			$data['homework_default_template'] = $this->settings->getSetting('homework_default_template');
			$data['task_id']= $task_id;
			$task=  $this->task->get_task_name($task_id);
			$data['task_name']=  $task->task_name;
			$data['task_description']=  $task->task_description;
			$data['consider_this_task_as']=  $task->consider_this_task_as;
			$data['main_content']='student_tasks/edit_task_mob_view';    	
			$this->load->view('inc/template', $data);
		}

		public function downloadMobileCircularAttachment($id) {
			$file_link = $this->task->downloadTaskDocuments($id);
			$file_link= json_decode($file_link->task_file_path);
			$link= $file_link[0]->path;
			$file = explode("/", $link);
			$file_name = 'task1';
			$fname = $file_name . '.' . explode(".", $file[count($file) - 1])[1];
			$signed_resource = $this->filemanager->getSignedUrlWithExpiry($link, '+5 minutes');
			$data = file_get_contents($signed_resource);
			$this->load->helper('download');
			force_download($fname, $data, TRUE);
			$this->load->library('user_agent');
			redirect($this->agent->referrer());
		}

		public function delete_task_if_possible() {
			echo  json_encode($this->task->delete_task_if_possible());
		}
	}
?>

 
