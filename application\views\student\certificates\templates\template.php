<ul class="breadcrumb">
<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/index') ?>">Student Certificate</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/issue_certificate/'.$student_uid) ?>">Issue Certificate</a></li>
    <li><a href="<?php echo site_url('student/Certificates_controller/generate_ceritificates/'.$student_uid) ?>">Issue new certificate</a></li>
    <li><?= $template->template_name ?></li>
</ul>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php  echo site_url('student/Certificates_controller/generate_ceritificates/' . $student_uid); ?>">
						<span class="fa fa-arrow-left"></span>
						</a>
						<strong><?php echo $template->template_name; ?></strong> of <?php echo '<strong>' . $stdData->stdName . '</strong> (Class: ' . $stdData->className . ' / Section: ' . $stdData->sectionName . ') (Admission No: ' . $stdData->admission_no . ')' ?>
					</h3>
				</div>
                <ul class="panel-controls" style="float:right">
                    <button class="btn btn-success" id="" onClick="generate_certificate(<?php echo $template->receipt_number_id; ?>); this.disabled = true;"><span class="glyphicon glyphicon-list-alt" aria-hidden="true"></span>Generate Certificate</button>
                    <!-- <button class="btn btn-danger" id="printBtn" onClick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span>Issue and Print</button> -->
                    <!-- <button class="btn btn-primary" onClick="editTemplate()">Edit</button> -->
                </ul>
				<!-- <div class="col-md-2">
					<a href="<?php // echo site_url('student/Certificates_controller/generate_ceritificates/' . $student_uid); ?>" class="btn btn-primary" style="float: right;"> Issue New Certificate</a>
				</div> -->
			</div>
		</div>
        <div class="col-12 text-center loading-icon" style="display: none; padding: 100px">

            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            <center>
            <br>
            <h4>This process takes less than a minute...</h4>
            <h4>Displaying the certificate in PDF format in few seconds...</h4>
            </center>
        </div>
        <div id='ten_seconds' style="display: none;">
        </div>
        <!--     </div>

    <div class="panel panel-default panel1"> -->
        <?php
        $logo = site_url($school_logo);
        $seal = site_url($school_seal);
        $signature = site_url($signature);
        // echo "<pre>"; print_r($seal); die();
        $logo_link = "<img width='100' height='100' src='".$logo."'>";
        $seal_link = "<img width='100' height='70' src='".$seal."'>";
        $signature_link = "<img width='100' height='40' src='".$signature."'>";
        // $logo_link = "<img width='100' height='100' src='".$this->filemanager->getFilePath($school_logo)."'>"; ?>
        <div class="panel-body" id="exporter" >
            <div class="row">
                <div class="form-group col-md-3">
                    <select class="form-control" name="manual_fields" id="manual_fields">
                        <option value="" disabled selected>Manual Field</option>
                        <?php foreach ($manualfields as  $field) {
                            $value = $field["value"];
                            $name = $field["name"];
                            echo '<option value="' . $value . '">' . $name . '</option>';
                        } ?>
                    </select>
                    <span class="help-block">Use these fields to edit data manually.</span>
                </div>
                <div class="form-group col-md-3">
                    <input name="manual_value" class="form-control input-md" id="manual_value" placeholder="Manual field value" value="">
                </div>
                <div class="col-md-2"><button class="btn btn-primary" onclick="addManualValue()">Add</button></div>
            </div>


            <div class="col-md-3"></div>
            <div id="aadhar" class="col-md-6">
                <?php
                $template_edit = $template->html_content;
                $line1 = '';
                $line2 = '';
                $area = '';
                $district = '';
                $state = '';
                $country = '';
                $pincode = '';
                $htmlTemplate = '';
                $student_address = '';
                $stu_address = '';
                $birth_taluk = '';
                $birth_district = '' ;
                if (!empty($stdData->address) && is_array($stdData->address)) {
                    $address = $stdData->address[0];

                    $line1 = !empty($address->Address_line1) ? $address->Address_line1 : '';
                    $line2 = !empty($address->Address_line2) ? $address->Address_line2 : '';
                    $area = !empty($address->area) ? $address->area : '';
                    $district = !empty($address->district) ? $address->district : '';
                    $state = !empty($address->state) ? $address->state : '';
                    $country = !empty($address->country) ? $address->country : '';
                    $pincode = !empty($address->pin_code) ? $address->pin_code : '';

                    $s_address_parts = [];

                    if ($line1) $s_address_parts[] = $line1;
                    if ($line2) $s_address_parts[] = $line2;
                    if ($area) $s_address_parts[] = $area;
                    if ($district) $s_address_parts[] = $district;
                    if ($state) $s_address_parts[] = $state;
                    if ($country) $s_address_parts[] = $country;

                    $student_address = implode(', ', $s_address_parts);

                    if ($pincode) {
                        $student_address .= ' - ' . $pincode;
                    }

                }
                $father_address = '';
                $mother_address = '';
                if (!empty($stdFatherData->addresses) && is_array($stdFatherData->addresses)) {
                    $address = $stdFatherData->addresses[0];

                    $line1 = !empty($address->Address_line1) ? $address->Address_line1 : '';
                    $line2 = !empty($address->Address_line2) ? $address->Address_line2 : '';
                    $area = !empty($address->area) ? $address->area : '';
                    $district = !empty($address->district) ? $address->district : '';
                    $state = !empty($address->state) ? $address->state : '';
                    $country = !empty($address->country) ? $address->country : '';
                    $pincode = !empty($address->pin_code) ? $address->pin_code : '';

                    $f_address_parts = [];

                    if ($line1) $f_address_parts[] = $line1;
                    if ($line2) $f_address_parts[] = $line2;
                    if ($area) $f_address_parts[] = $area;
                    if ($district) $f_address_parts[] = $district;
                    if ($state) $f_address_parts[] = $state;
                    if ($country) $f_address_parts[] = $country;

                    $father_address = implode(', ', $f_address_parts);

                    if ($pincode) {
                        $father_address .= ' - ' . $pincode;
                    }


                }

                if (!empty($stdMotherData->addresses) && is_array($stdMotherData->addresses)) {
                    $address = $stdMotherData->addresses[0];

                    $line1 = !empty($address->Address_line1) ? $address->Address_line1 : '';
                    $line2 = !empty($address->Address_line2) ? $address->Address_line2 : '';
                    $area = !empty($address->area) ? $address->area : '';
                    $district = !empty($address->district) ? $address->district : '';
                    $state = !empty($address->state) ? $address->state : '';
                    $country = !empty($address->country) ? $address->country : '';
                    $pincode = !empty($address->pin_code) ? $address->pin_code : '';

                    $m_address_parts = [];

                    if ($line1) $m_address_parts[] = $line1;
                    if ($line2) $m_address_parts[] = $line2;
                    if ($area) $m_address_parts[] = $area;
                    if ($district) $m_address_parts[] = $district;
                    if ($state) $m_address_parts[] = $state;
                    if ($country) $m_address_parts[] = $country;

                    $mother_address = implode(', ', $m_address_parts);

                    if ($pincode) {
                        $mother_address .= ' - ' . $pincode;
                    }

                }
                if (!empty($stdData)) {
                    $birth_taluk = $stdData->birth_taluk;
                    $birth_district = $stdData->birth_district;
                }

                // if (!empty($stdData->address)) {
                //     $line1 = $stdData->address[0]->Address_line1;
                //     $line2 = $stdData->address[0]->Address_line2;
                //     $area = $stdData->address[0]->area;
                //     $district = $stdData->address[0]->district;
                //     $state = $stdData->address[0]->state;
                //     $country = $stdData->address[0]->country;
                //     $pincode = $stdData->address[0]->pin_code;
                //     // $student_address = $stdData->address[0]->Addre
                // } else if (!empty($stdData)) {
                //     $birth_taluk = $stdData->birth_taluk;
                //     $birth_district = $stdData->birth_district;
                // }else if (!empty($stdFatherData->address)) {
                //     $line1 = $stdFatherData->address[0]->Address_line1;
                //     $line2 = $stdFatherData->address[0]->Address_line2;
                //     $area = $stdFatherData->address[0]->area;
                //     $district = $stdFatherData->address[0]->district;
                //     $state = $stdFatherData->address[0]->state;
                //     $country = $stdFatherData->address[0]->country;
                //     $pincode = $stdFatherData->address[0]->pin_code;
                // } else if (!empty($stdMotherData->address)) {
                //     $line1 = $stdMotherData->address[0]->Address_line1;
                //     $line2 = $stdMotherData->address[0]->Address_line2;
                //     $area = $stdMotherData->address[0]->area;
                //     $district = $stdMotherData->address[0]->district;
                //     $state = $stdMotherData->address[0]->state;
                //     $country = $stdMotherData->address[0]->country;
                //     $pincode = $stdMotherData->address[0]->pin_code;
                // }

                // if ($line1 != '') {
                //     $student_address .= $line1 . ', ';
                // }
                // if ($line2 != '') {
                //     $student_address .= $line2 . ', ';
                // }
                // if ($area != '') {
                //     $student_address .= $area . ', ';
                // }
                // if ($district != '') {
                //     $student_address .= $district . ', ';
                // }
                // if ($state != '') {
                //     $student_address .= $state . ', ';
                // }
                // if ($country != '') {
                //     $student_address .= $country;
                // }
                // if ($pincode != '') {
                //     $student_address .= ' - ' . $pincode;
                // }

                if ($birth_taluk != '') {
                    $stu_address .= $birth_taluk . ', ';
                }
                if ($birth_district != '') {
                    $stu_address .= $birth_district ;
                }

                $father_office_add = '';
                if(!empty($stdFatherData->addresses[0]) && $stdFatherData->addresses[0]->address_type == 'Office Address'){
                    $father_office_add = $stdFatherData->addresses[0]->address;
                  }else if(!empty($stdFatherData->addresses[1]) && $stdFatherData->addresses[1]->address_type == 'Office Address'){
                    $father_office_add = $stdFatherData->addresses[1]->address;
                  }
                $mother_office_add = '';
                if(!empty($stdMotherData->addresses[0]) && $stdMotherData->addresses[0]->address_type == 'Office Address'){
                    $mother_office_add = $stdMotherData->addresses[0]->address;
                }else if(!empty($stdMotherData->addresses[1]) && $stdMotherData->addresses[1]->address_type == 'Office Address'){
                    $mother_office_add = $stdMotherData->addresses[1]->address;
                }

                if ($stdData->gender == "M") {
                    $g = "Male";
                    $a = "his";
                    $b = "S/o";
                    $c = 'Mr';
                } else if($stdData->gender == "F"){
                    $g = "Female";
                    $a = "her";
                    $b = "D/o";
                    $c = 'Ms';
                }else{
                    $g = "";
                    $a = "";
                    $b = "";
                    $c = '';
                }
                $physical_disability ='';
                $learning_disability ='';
                $sibilingStudying ='';
                $sibling_student_name ='';
                $sibling_school_name ='';
                $sibling_student_class ='';
                if(!empty($admission_from_data)){
                    $physical_disability = 'No';
                    if ($admission_from_data->physical_disability == 'Y') {
                        $physical_disability = 'Yes';
                    }
                    $learning_disability = 'No';
                    if ($admission_from_data->learning_disability == 'Y') {
                        $learning_disability = 'Yes';
                    }

                    $sibilingStudying = 'No';
                    if ($admission_from_data->sibling_student_class != '') {
                        $sibilingStudying = 'Yes';
                    }

                    $sibling_student_name = '';
                    if ($admission_from_data->sibling_student_name != '') {
                        $sibling_student_name = $admission_from_data->sibling_student_name;
                    }
                    $sibling_school_name = '';
                    if ($admission_from_data->sibling_school_name != '') {
                        $sibling_school_name = $admission_from_data->sibling_school_name;
                    }
                    $sibling_student_class = '';
                    if ($admission_from_data->sibling_student_class != '') {
                        $sibling_student_class = $admission_from_data->sibling_student_class;
                    }
                }


                $guardianName = '';
                $guardianEmail = '';
                $guardianMobile = '';
                $guardianDesignation = '';
                if (!empty($stdGuardianData)) {
                    $guardianName = $stdGuardianData->pName;
                    $guardianEmail = $stdGuardianData->email;
                    $guardianMobile = $stdGuardianData->mobile_no;
                    $guardianDesignation = $stdGuardianData->designation.' '.$stdGuardianData->occupation;
                }

                $documents = '';
                if (!empty($student_documents)) {
                    $documents .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">Details of Previous Schooling</h5>';
                    $documents .= '<table style="width: 98%; margin: auto; ">';
                    foreach ($student_documents as $key => $val) {
                        $documents .= '<tr>';
                        $documents .= '<td>'.$val->document_type.'</td>';
                        $documents .= '</tr>';
                    }
                    $documents .= '</table>';
                }
                $prevSchool = '';
                if (!empty($student_previous)) {
                    foreach ($student_previous as $key => $sPrev) {
                        if(!empty($sPrev->marks)){
                            $colCount = sizeof($sPrev->marks)*2;
                        }else{
                            $colCount = '';
                        }
                    }

                    $prevSchool .= '<h5 style="border-bottom: 4px solid #000; font-weight: 500; margin-top: 1rem !important;">Details of Previous Schooling</h5>';
                    $prevSchool .= '<table style="width: 98%; margin: auto; ">
                    <tr>
                        <td style="font-size: 11px;">Year</td>
                        <td style="font-size: 11px;">School Name</td>
                        <td style="font-size: 11px;">School Address</td>
                        <td style="font-size: 11px;">Class</td>
                        <td style="font-size: 11px;">Board</td>
                        <td style="font-size: 11px;" colspan="'.$colCount.'" >Grades and marks obtained in Final Exam</td>
                    </tr>';

                    foreach ($student_previous as $key => $value) {
                        $prevSchool .= '<tr>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->year.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->school_name.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->school_address.'</td>';
                        $prevSchool .= '<td style="font-size: 11px;">'.$value->class.'</td>';
                        $board = $value->board;
                        if ($value->board == 'Others') {
                           $board = $value->board.''.$value->board_other;
                        }
                        $prevSchool .= '<td style="font-size: 11px;">'.$board.'</td>';

                        if (!empty($value->marks)) {
                            foreach ($value->marks as $key => $val) {
                                $prevSchool.= '<td style="font-size: 11px;">'.$val->sub_name.'</td>';
                                $prevSchool.= '<td style="font-size: 11px;">'.$val->percentage.'% '.' ('.$val->grade.')'.'</td>';
                            }
                        }
                        $prevSchool .= '</tr>';
                    }
                    $prevSchool .= '</table>';
                }

                $friendly_name = '';
                if(!empty($std_fees_data->friendly_name)){
                    $friendly_name = $std_fees_data->friendly_name;
                }
                if(!empty($custom_fields)){
                    foreach($custom_fields as $key => $val){
                        $template->html_content = str_replace('%%'.$key.'%%', '<span class="editable" data-field='.$key.' id='.$key.'>' . $stdData->$val . '</span>', $template->html_content);
                    }
                }

                // if (!empty($placeholders)) {
                //     foreach ($placeholders as $field) {
                //         $template->html_content = str_replace(
                //             "%%$field%%",
                //             "<span class='editable' data-field='$field'>%%$field%%</span>",
                //             $template->html_content
                //         );
                //     }
                // }

                $template->html_content = str_replace('%%school_logo%%', '<span class="editable" data-field="school_logo" id="school_logo">' . $logo_link . '</span>', $template->html_content);
                $template->html_content = str_replace('%%school_seal%%', '<span class="editable" data-field="school_seal" id="school_seal">' . $seal_link . '</span>', $template->html_content);
                $template->html_content = str_replace('%%signature%%', '<span class="editable" data-field="signature" id="signature">' . $signature_link . '</span>', $template->html_content);
                $template->html_content = str_replace('%%admission_no%%', '<span id="admission_no">' . $stdData->admission_no . '</span>', $template->html_content);
                $template->html_content = str_replace('%%admission_type%%', '<span class="editable" data-field="admission_type" id="admission_type">' . $stdData->admission_type . '</span>', $template->html_content);
                $template->html_content = str_replace('%%enrollment_number%%', '<span class="editable" data-field="enrollment_number" id="enrollment_number">' . $stdData->enrollment_number . '</span>', $template->html_content);
                $template->html_content = str_replace('%%nationality%%', '<span id="nationality">' . $stdData->nationality . '</span>', $template->html_content);
                $template->html_content = str_replace('%%sts_number%%', '<span class="editable" data-field="sts_number" id="sts_number">' . $stdData->sts_number . '</span>', $template->html_content);
                $template->html_content = str_replace('%%tc_number%%', '<span class="editable" data-field="tc_number" id="tc_number">' . $stdData->tc_number . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%student_name%%', '<span id="student_name">' . $stdData->stdName . '</span>', $template->html_content);
                $template->html_content = str_replace('%%class%%', '<span class="editable" data-field="class" id="class">' . $stdData->className . '/' . $stdData->sectionName . '</span>', $template->html_content);
                $template->html_content = str_replace('%%class_name%%', '<span id="class_name">' . $stdData->className . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%gender%%', '<span class="editable" data-field="gender" id="gender">' . $g . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%father_name%%', '<span id="father_name">' . $stdFatherData->pName . '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_name%%', '<span id="mother_name">' . $stdMotherData->pName . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%address%%', '<span class="editable" data-field="address" id="address">' . $student_address . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%student_address%%', '<span class="editable" data-field="student_address" id="address">' . $stu_address . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%His/Her%%', '<span class="editable" data-field="His/Her" id="His/Her">' . $a . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%Mr/Ms%%', '<span class="editable" data-field="Mr/Ms" id="Mr/Ms">' . $c . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%S/D%%', '<span class="editable" data-field="S/D" id="S/D">' . $b . '</span>', $template->html_content);
                $template->html_content = str_replace('%%student_email_id%%', '<span class="editable" data-field="student_email_id" id="student_email_id">' .$stdData->email. '</span>', $template->html_content);
                $template->html_content = str_replace('%%student_number%%', '<span class="editable" data-field="student_number" id="student_number">' .$stdData->preferred_contact_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%admission_year%%', '<span class="editable" data-field="admission_year" id="admission_year">' .$stdData->admission_year. '</span>', $template->html_content);
                $template->html_content = str_replace('%%stu_address%%', '<span class="editable" data-field="stu_address" id="stu_address">' .$stdData->birthplace. '</span>', $template->html_content);
                $template->html_content = str_replace('%%registration_no%%', '<span class="editable" data-field="registration_no" id="registration_no">' .$stdData->registration_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%electives%%', '<span class="editable" data-field="electives" id="electives">' .$stdData->electives. '</span>', $template->html_content);
                $template->html_content = str_replace('%%terminate_date%%', '<span class="editable" data-field="terminate_date" id="terminate_date">' .$stdData->terminate_date. '</span>', $template->html_content);
                $template->html_content = str_replace('%%board%%', '<span class="editable" data-field="board" id="board">' .$stdData->board. '</span>', $template->html_content);
                $template->html_content = str_replace('%%first_joined_class%%', '<span class="editable" data-field="first_joined_class" id="first_joined_class">' .$first_joined_class. '</span>', $template->html_content);

                $template->html_content = str_replace('%%dob%%', '<span class="editable" data-field="dob" id="dob">' . date($date_format, strtotime($stdData->dob)) . '</span>', $template->html_content);
                $template->html_content = str_replace('%%dob_in_date%%', '<span class="editable" data-field="dob_date" id="dob_date">' . date($date_format, strtotime($stdData->dob)) . '</span>', $template->html_content);
                $template->html_content = str_replace('%%dob_in_words%%', '<span id="dob_in_words">' . $dob_in_words . '</span>', $template->html_content);
                // $template->html_content =  str_replace('%%academic_year%%', '<span id="academic_year">academic year</span>', $template->html_content);
                $template->html_content =  str_replace('%%academic_year%%', '<span class="editable" data-field="academic_year" id="academic_year">' . $acad_year . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%fees_amount%%', '<span class="editable" data-field="fees_amount" id="fees_amount">Fees Amount</span>', $template->html_content);
                $template->html_content =  str_replace('%%fees_concession%%', '<span class="editable" data-field="fees_concession" id="fees_concession">Fees Concession</span>', $template->html_content);
                $template->html_content = str_replace('%%transportation_location%%', '<span class="editable" data-field="transportation_location" id="transportation_location">' .$friendly_name. '</span>', $template->html_content);

                $template->html_content =  str_replace('%%fees_amount_in_words%%', '<span class="editable" data-field="fees_amount_in_words" id="fees_amount_in_words">Fees Amount in words</span>', $template->html_content);
                $template->html_content =  str_replace('%%promoted_class%%', '<span class="editable" data-field="promoted_class" id="promoted_class">Promoted Class</span>', $template->html_content);
                $template->html_content =  str_replace('%%date%%', '<span class="editable" data-field="date" id="date">' . date($date_format) . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%caste%%', '<span id="caste">' . $stdData->caste . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%student_sub_caste%%', '<span class="editable" data-field="sub_caste" id="sub_caste">' . $stdData->student_sub_caste . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%category%%', '<span class="editable" data-field="category" id="category">' . $stdData->category . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%prefeered_mobile_no%%', '<span class="editable" data-field="prefeered_mobile_no" id="prefeered_mobile_no">' . $stdData->contact_no . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%subjects%%', '<span class="editable placeholder-text" data-field="subjects" id="subjects">Click to edit subjects</span>', $template->html_content);
                $template->html_content =  str_replace('%%bonafide_on%%', '<span class="editable placeholder-text" data-field="bonafide_on" id="bonafide_on">Click to edit bonafide on</span>', $template->html_content);
                $template->html_content =  str_replace('%%bonafied_issued_for%%', '<span class="editable placeholder-text" data-field="bonafied_issued_for" id="bonafied_issued_for">Click to edit bonafied issued for</span>', $template->html_content);
                $template->html_content =  str_replace('%%from_class%%', '<span class="editable placeholder-text" data-field="from_class" id="from_class">Click to edit from class</span>', $template->html_content);
                $template->html_content =  str_replace('%%to_class%%', '<span class="editable placeholder-text" data-field="to_class" id="to_class">Click to edit to class</span>', $template->html_content);
                $template->html_content =  str_replace('%%from_academic_year%%', '<span class="editable" data-field="from_academic_year" id="from_academic_year">'  .$stdData->admission_acad_year. '</span>', $template->html_content);
                $template->html_content =  str_replace('%%to_academic_year%%', '<span class="editable" data-field="to_academic_year" id="to_academic_year">'  .$stdData->current_acad_year. '</span>', $template->html_content);
                $template->html_content =  str_replace('%%mother_tongue%%', '<span class="editable" data-field="mother_tongue" id="mother_tongue">' .$stdData->mother_tongue. '</span>', $template->html_content);
                $template->html_content =  str_replace('%%combination%%', '<span class="editable" data-field="combination" id="combination">' .$stdData->combination_name. '</span>', $template->html_content);
                $template->html_content =  str_replace('%%sc_st%%', '<span class="editable placeholder-text" data-field="sc_st" id="sc_st">Click to edit SC/ST</span>', $template->html_content);
                $template->html_content =  str_replace('%%medium_instruction%%', '<span class="editable placeholder-text" data-field="medium_instruction" id="medium_instruction">Click to edit medium of instruction</span>', $template->html_content);
                $template->html_content = str_replace('%%religion%%', '<span class="editable" data-field="religion" id="religion">' .$stdData->religion. '</span>', $template->html_content);
                $template->html_content = str_replace('%%place_of_birth%%', '<span class="editable" data-field="place_of_birth" id="place_of_birth">' .$stdData->birthplace. '</span>', $template->html_content);
                $template->html_content = str_replace('%%blood_group%%', '<span class="editable" data-field="blood_group" id="blood_group">' .$stdData->blood_group. '</span>', $template->html_content);
                $template->html_content = str_replace('%%student_aadhar_no%%', '<span class="editable" data-field="student_aadhar_no" id="student_aadhar_no">' .$stdData->aadhar_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_aadhar_no%%', '<span class="editable" data-field="father_aadhar_no" id="father_aadhar_no">' .$stdFatherData->aadhar_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_qualification%%', '<span class="editable" data-field="father_qualification" id="father_qualification">' .$stdFatherData->qualification. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_profession%%', '<span class="editable" data-field="father_profession" id="father_profession">' .$stdFatherData->occupation. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_designation_office_address%%', '<span class="editable" data-field="father_designation_office_address" id="father_designation_office_address">' .$stdFatherData->occupation. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_number%%', '<span class="editable" data-field="father_number" id="father_number">' .$stdFatherData->mobile_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_blood_group%%', '<span class="editable" data-field="father_blood_group" id="father_blood_group">' .$stdFatherData->blood_group. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_number%%', '<span class="editable" data-field="father_number" id="father_number">' .$stdFatherData->mobile_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_office_number%%', '<span class="editable placeholder-text" data-field="father_office_number" id="father_office_number">Click to edit father office number</span>', $template->html_content);

                $template->html_content = str_replace('%%father_residential_number%%', '<span class="editable placeholder-text" data-field="father_residential_number" id="father_residential_number">Click to edit father residential number</span>', $template->html_content);
                $template->html_content = str_replace('%%father_email_id%%', '<span class="editable" data-field="father_email_id" id="father_email_id">' .$stdFatherData->email. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_annual_income%%', '<span class="editable" data-field="father_annual_income" id="father_annual_income">' .$stdFatherData->annual_income. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_address%%', '<span class="editable" data-field="father_address" id="father_address">' .$father_address. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_state%%', '<span class="editable" data-field="father_state" id="father_state">' .(isset($stdFatherData->addresses[0]->state) ? $stdFatherData->addresses[0]->state : ''). '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_district%%', '<span class="editable" data-field="father_district" id="father_district">' .(isset($stdFatherData->addresses[0]->district) ? $stdFatherData->addresses[0]->district : ''). '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_country%%', '<span class="editable" data-field="father_country" id="father_country">' .(isset($stdFatherData->addresses[0]->country) ? $stdFatherData->addresses[0]->country : ''). '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_pincode%%', '<span class="editable" data-field="father_pincode" id="father_pincode">' .(isset($stdFatherData->addresses[0]->pin_code) ? $stdFatherData->addresses[0]->pin_code : ''). '</span>', $template->html_content);
                $template->html_content = str_replace('%%reference_number%%', '<span class="editable placeholder-text" data-field="reference_number" id="reference_number">Click to edit reference number</span>', $template->html_content);

                $template->html_content = str_replace('%%mother_aadhar_no%%', '<span class="editable" data-field="mother_aadhar_no" id="mother_aadhar_no">' .$stdMotherData->aadhar_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_qualification%%', '<span class="editable" data-field="mother_qualification" id="mother_qualification">' .$stdMotherData->qualification. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_profession%%', '<span class="editable" data-field="mother_profession" id="mother_profession">' .$stdMotherData->occupation. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_designation_office_address%%', '<span class="editable" data-field="mother_designation_office_address" id="mother_designation_office_address">' .$stdMotherData->occupation. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_number%%', '<span class="editable" data-field="mother_number" id="mother_number">' .$stdMotherData->mobile_no. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_office_number%%', '<span class="editable placeholder-text" data-field="mother_office_number" id="mother_office_number">Click to edit mother office number</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_residential_number%%', '<span class="editable placeholder-text" data-field="mother_residential_number" id="mother_residential_number">Click to edit mother residential number</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_email_id%%', '<span class="editable" data-field="mother_email_id" id="mother_email_id">' .$stdMotherData->email. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_annual_income%%', '<span class="editable" data-field="mother_annual_income" id="mother_annual_income">' .$stdMotherData->annual_income. '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_address%%', '<span class="editable" data-field="mother_address" id="mother_address">' . $mother_address . '</span>', $template->html_content);
                $template->html_content = str_replace('%%mother_blood_group%%', '<span class="editable" data-field="mother_blood_group" id="mother_blood_group">' .$stdMotherData->blood_group. '</span>', $template->html_content);
                $template->html_content = str_replace('%%guardian_name%%', '<span class="editable" data-field="guardian_name" id="guardian_name">' .$guardianName. '</span>', $template->html_content);
                $template->html_content = str_replace('%%guardian_email%%', '<span class="editable" data-field="guardian_email" id="guardian_email">' .$guardianEmail. '</span>', $template->html_content);
                $template->html_content = str_replace('%%guardian_mobile_no%%', '<span class="editable" data-field="guardian_mobile_no" id="guardian_mobile_no">' .$guardianMobile. '</span>', $template->html_content);
                $template->html_content = str_replace('%%guardian_designation_occupation%%', '<span class="editable" data-field="guardian_designation_occupation" id="guardian_designation_occupation">' .$guardianDesignation. '</span>', $template->html_content);
                $template->html_content = str_replace('%%issued_by%%', '<span class="editable" data-field="issued_by" id="issued_by">' .$get_issued_by->fName. '</span>', $template->html_content);
                $template->html_content = str_replace('%%father_office_add%%','<span class="editable" data-field="father_office_add" id="father_office_add">' . $father_office_add .'</span>', $template->html_content);
				$template->html_content = str_replace('%%mother_office_add%%', '<span class="editable" data-field="mother_office_add" id="mother_office_add">' .$mother_office_add .'</span>', $template->html_content);


                $template->html_content = str_replace('%%electives_studied%%', '<span class="editable placeholder-text" data-field="electives_studied" id="electives_studied">Click to edit electives studied</span>', $template->html_content);
                $template->html_content = str_replace('%%medically_examined%%', '<span class="editable placeholder-text" data-field="medically_examined" id="medically_examined">Click to edit medically examined</span>', $template->html_content);
                $doj = date($date_format, strtotime($stdData->date_of_joining));
                if($doj == date($date_format, strtotime('1970-01-01'))){
                    $doj = '-';
                }
                $template->html_content =  str_replace('%%doj%%', '<span class="editable" data-field="doj" id="doj">' . $doj . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%doj_in_date%%', '<span class="editable" data-field="doj_in_date" id="doj_date">' . ($doj == '-' ? '-' : date($date_format, strtotime($stdData->date_of_joining))) . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%sts_number%%', '<span class="editable" data-field="sts_number" id="sts_number">' . $stdData->sts_number . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%promotion_class_name%%', '<span class="editable" data-field="promotion_class_name" id="promotion_class_name">' . $stdData->promotion_class_name . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%pen_number%%', '<span class="editable" data-field="pen_number" id="pen_number">' . $stdData->pen_number . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%promoted_to_higher_class%%', '<span class="editable" data-field="promoted_to_higher_class" id="promoted_to_higher_class">Yes</span>', $template->html_content);
                $template->html_content =  str_replace('%%is_dues_paid%%', '<span class="editable" data-field="is_dues_paid" id="is_dues_paid">Yes</span>', $template->html_content);

                $template->html_content =  str_replace('%%application_date%%', '<span class="editable" data-field="application_date" id="application_date">' . ($std_adm_data->application_date ? date($date_format, strtotime($std_adm_data->application_date)) : '') . '</span>', $template->html_content);
                $template->html_content =  str_replace('%%goto_know_by%%', '<span class="editable" data-field="goto_know_by" id="goto_know_by">'.$std_adm_data->know_about_us.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%passport_number%%', '<span class="editable" data-field="passport_number" id="passport_number">'.$stdData->passport_number.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%identification_mark1%%', '<span class="editable" data-field="identification_mark1" id="identification_mark1">'.$stdData->identification_mark1.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%f_position%%', '<span class="editable" data-field="f_position" id="f_position">'.$std_adm_data->f_position.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%m_position%%', '<span class="editable" data-field="m_position" id="m_position">'.$std_adm_data->m_position.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%f_company_name%%', '<span class="editable" data-field="f_company_name" id="f_company_name">'.$std_adm_data->f_company_name.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%m_company_name%%', '<span class="editable" data-field="m_company_name" id="m_company_name">'.$std_adm_data->m_company_name.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%f_company_addr%%', '<span class="editable" data-field="f_company_addr" id="f_company_addr">'.$std_adm_data->f_company_addr.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%m_company_addr%%', '<span class="editable" data-field="m_company_addr" id="m_company_addr">'.$std_adm_data->m_company_addr.'</span>', $template->html_content);
                $template->html_content =  str_replace('%%grade_admited_to%%', '<span class="editable placeholder-text" data-field="grade_admited_to" id="grade_admited_to">Click to edit grade admitted to</span>', $template->html_content);
                $template->html_content =  str_replace('%%stream_attended%%', '<span class="editable placeholder-text" data-field="stream_attended" id="stream_attended">Click to edit stream attended</span>', $template->html_content);
                $template->html_content =  str_replace('%%last_day%%', '<span class="editable placeholder-text" data-field="last_day" id="last_day">Click to edit last day</span>', $template->html_content);
                $template->html_content =  str_replace('%%result%%', '<span class="editable placeholder-text" data-field="result" id="result">Click to edit result</span>', $template->html_content);
                $template->html_content =  str_replace('%%reason_for_leaving%%', '<span class="editable placeholder-text" data-field="reason_for_leaving" id="reason_for_leaving">Click to edit reason for leaving</span>', $template->html_content);
                $template->html_content =  str_replace('%%document_issued%%', '<span class="editable placeholder-text" data-field="document_issued" id="document_issued">Click to edit document issued</span>', $template->html_content);

                $birthdate = '1990-07-19';
                $birthDateObj = new DateTime($stdData->dob);
                $currentDate = new DateTime('2025-06-01');
                $age = $currentDate->diff($birthDateObj);
                $age =  $age->y . ' years, ' . $age->m . ' months, and ' . $age->d . ' days.';
                $template->html_content =  str_replace('%%joining_year%%', '<span class="editable" data-field="joining_year" id="joining_year">' . $stdData->joining_year . '</span>', $template->html_content);

                $template->html_content =  str_replace('%%age%%', '<span class="editable" data-field="age" id="age">'.$age.'</span>', $template->html_content);

                $stdImage = '<img width="100" height="100" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdData->picture_url != '')
                    $stdImage = '<img width="100" height="100" src="' . $this->filemanager->getFilePath($stdData->picture_url) . '">';
                $template->html_content =  str_replace('%%student_image%%', $stdImage, $template->html_content);

                $father_image = '<img width="100" height="100" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdFatherData->picture_url != '')
                $father_image = '<img width="100" height="100" src="' . $this->filemanager->getFilePath($stdFatherData->picture_url) . '">';
                $template->html_content =  str_replace('%%father_image%%', $father_image, $template->html_content);

                $mother_image = '<img width="100" height="100" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdMotherData->picture_url != '')
                $mother_image = '<img width="100" height="100" src="' . $this->filemanager->getFilePath($stdMotherData->picture_url) . '">';
                $template->html_content =  str_replace('%%mother_image%%', $mother_image, $template->html_content);

                $stdAdmissionImage = '<img width="132px" style="float: right;margin-top: -4rem !important;" height="170px" src="' . site_url() . 'assets/img/icons/profile.png">';
                if ($stdData->picture_url != '')
                    $stdAdmissionImage = '<img width="132px"  style="float: right;margin-top: -8rem !important;"  height="170px" src="' . $this->filemanager->getFilePath($stdData->picture_url) . '">';
                $template->html_content =  str_replace('%%student_admission_photo%%', '<span class="editable" data-field="student_admission_photo" id="student_admission_photo">'. $stdAdmissionImage .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%previous_school_details%%','<span class="editable" data-field="previous_school_details" id="previous_school_details">'. $prevSchool  .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%application_no%%','<span class="editable" data-field="application_no" id="application_no">'. $application_no  .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%document_details%%','<span class="editable" data-field="document_details" id="document_details">'. $documents .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%physical_disability%%','<span class="editable" data-field="physical_disability" id="physical_disability">'. $physical_disability  .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%sibilingStudying%%','<span class="editable" data-field="sibilingStudying" id="sibilingStudying">'. $sibilingStudying  .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%learning_disability%%','<span class="editable" data-field="learning_disability" id="learning_disability">'. $learning_disability  .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%sibling_student_name%%','<span class="editable" data-field="sibling_student_name" id="sibling_student_name">'. $sibling_student_name .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%sibling_school_name%%', '<span class="editable" data-field="sibling_school_name" id="sibling_school_name">'. $sibling_school_name .'</span>', $template->html_content);
                $template->html_content =  str_replace('%%sibling_student_class%%', '<span class="editable" data-field="sibling_student_class" id="sibling_student_class">'. $sibling_student_class .'</span>', $template->html_content);

                // if (!empty($template->template_background)){
                //     $tempalte_Bg_path = $this->filemanager->getFilePath($template->template_background);
                //     $template->html_content = str_replace('%%template_background%%','<span id="template_background">'. $tempalte_Bg_path .'</span>', $template->html_content);
                //   }else {
                //     $template->html_content = str_replace('%%template_background%%','', $template->html_content);
                //   }

                  if (!empty($template->template_background)) {
                    $tempalte_Bg_path = $this->filemanager->getFilePath($template->template_background);
                    // Ensure only the URL is replaced directly
                        $template->html_content = str_replace('%%template_background%%', $tempalte_Bg_path, $template->html_content);
                    } else {
                        $template->html_content = str_replace('%%template_background%%', '', $template->html_content);
                    }
                // echo $template->html_content;
                $htmlTemplate = $template->html_content;
                $escaped_html = $htmlTemplate;
                $escaped_html = str_replace("\n", "", $escaped_html);
                $escaped_html = str_replace("\r", "", $escaped_html);


                preg_match_all('/%%(.*?)%%/', $template->html_content, $matches);

                // Ensure placeholders are found
                if (!empty($matches[1])) {
                    // echo "<pre>"; print_r($matches[1]); die();

                    foreach ($matches[1] as $field) {
                        if (in_array($field, $disable_for_edit_fields)) {
                            continue;
                        }
                        // Use placeholder text instead of showing the replacement string
                        $placeholder = isset($stdData->$field) && ! empty($stdData->$field) ? $stdData->$field : "Click to edit $field";
                        $replacement = "<span class='editable' data-field='$field'>$placeholder</span>";
                        // Replace placeholder dynamically
                        if (strpos($template->html_content, "%%$field%%") !== false) {
                            $template->html_content = str_replace("%%$field%%", $replacement, $template->html_content);
                        }
                    }
                }
                echo $template->html_content;

                ?>
            </div>
        </div>
        <div class="col-12 text-center" id="pdf-data" style="height: 1000px; display: none;">
        </div>
    </div>
</div>

<script>
    function printProfile() {
        // Get the HTML content
        var html = $("#aadhar").html();

        // Clean the HTML to remove placeholders and styling classes
        html = cleanCertificateData(html);

        console.log(html);
        var stdId = '<?php echo  $stdData->id ?>';
        var template_id = '<?php echo  $template_id ?>';
        $.ajax({
            url: '<?php echo site_url('student/Certificates_controller/save_andprint_certificates') ?>',
            type: 'post',
            data: {
                'html': html,
                'stdId': stdId,
                'template_id': template_id
            },
            success: function(data) {
                console.log(data);
                if (data == '1') {
                    var restorepage = document.body.innerHTML;
                    var printcontent = document.getElementById('aadhar').innerHTML;
                    document.body.innerHTML = printcontent;
                    window.print();
                    document.body.innerHTML = restorepage;
                } else {
                    console.log(data);
                }
            }
        });

    }

    // Helper function to clean certificate data before submission
    function cleanCertificateData(htmlContent) {
        var cleanedData = htmlContent;

        // Replace any remaining template placeholders
        cleanedData = cleanedData.replace(/(%%(?!reference_number%%)[A-Za-z_]+%%)/g, "");

        // Remove any placeholder text spans
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*placeholder-text[^"]*"[^>]*>[^<]*<\/span>/g, "");

        // Clean up any remaining editable spans that might have placeholder-like content
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*editable[^"]*"[^>]*>Click to edit[^<]*<\/span>/g, "");

        // Remove any empty editable spans
        cleanedData = cleanedData.replace(/<span[^>]*class="[^"]*editable[^"]*"[^>]*>\s*<\/span>/g, "");

        // Clean up any styling classes that shouldn't be in the final certificate
        cleanedData = cleanedData.replace(/class="[^"]*placeholder-text[^"]*"/g, "");
        cleanedData = cleanedData.replace(/class="[^"]*has-data[^"]*"/g, "");

        return cleanedData;
    }

    function generate_certificate(receipt_number_id) {
        console.log(receipt_number_id);
        $(".loading-icon").show();
        $("#exporter").hide();

        // Get the certificate data
        var certificate_data = $('#aadhar').html();

        // Clean the certificate data to remove all placeholders and empty fields
        certificate_data = cleanCertificateData(certificate_data);
        var student_id = <?php echo $student_uid; ?>;
        var template_id = <?php echo  $template_id; ?>;
        $.ajax({
            url: '<?php echo site_url('student/Certificates_controller/generate_pdf_certificate'); ?>',
            data: {
                'certificate_data': certificate_data,
                'template_id': template_id,
                'student_id': student_id,
                'manual_feilds_array':manual_feilds_array,
                'receipt_number_id':receipt_number_id
            },
            type: "post",
            success: function(data) {
                var pdf_link = $.parseJSON(data);
                // $(".loading-icon").hide();
                // $("#ten_seconds").hide();
                setTimeout(function(){
                    // document.getElementById("ten_seconds").style.display = "block";
                    window.location.href = "<?php echo site_url('student/Certificates_controller/issue_certificate/'.$student_uid); ?>";
                }, 5000);

            },
            error: function(err) {
                console.log(err);
                // location.reload();
            }
        });
    }


</script>
<style type="text/css">
    @media print {
        h3 {
            font-size: 20px;
        }

        h5 {
            font-size: 20px;
        }

        * {
            font-size: 20px;
        }

        .panel1 {
            padding: 0px;
        }

        .panel-body {
            padding: 0px;
        }

        .heading {
            text-align: center !important;
            font-weight: bold;
            text-decoration: underline;
            font-family: "Times New Roman";
            margin-bottom: 100px;
        }

        .abc {
            color: red !important;
        }
    }

    @page {
        size: A4;
        margin: 5%;
    }

    .ClickWordList {
        list-style-type: none;
    }

    .ClickWordList li {
        padding: 3px;
        /*border-bottom: 1px solid #ccc;*/
        cursor: pointer;
    }

    .ClickWordList li a {
        color: #000;
    }

    /* Styles for editable fields */
    .editable {
        cursor: pointer;
        padding: 3px 6px;
        border-radius: 4px;
        transition: all 0.3s ease;
        display: inline-block;
        min-width: 60px;
        min-height: 22px;
        border: 1px solid transparent;
        position: relative;
    }

    /* Fields with data */
    .has-data {
        color: #333;
        background-color: transparent;
    }

    /* Show edit icon on hover for fields with data */
    .has-data:hover::after {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        color: #1890ff;
        opacity: 0.7;
    }

    .has-data:hover {
        background-color: rgba(24, 144, 255, 0.05);
        border: 1px solid rgba(24, 144, 255, 0.3);
        box-shadow: 0 1px 3px rgba(24, 144, 255, 0.1);
    }

    /* Placeholder styling */
    .placeholder-text {
        color: #888;
        font-style: italic;
        background-color: #f9f9f9;
        border: 1px dashed #ccc;
        font-size: 0.95em;
    }

    .placeholder-text:hover {
        background-color: #f0f8ff;
        border: 1px dashed #1890ff;
        color: #666;
    }

    /* Active editing state */
    .editing {
        padding: 0;
        border: none;
        background-color: transparent;
    }

    /* Input field styling */
    .edit-input {
        width: 100%;
        padding: 3px 6px;
        border: 2px solid #1890ff;
        border-radius: 4px;
        outline: none;
        box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
        font-size: 14px;
        color: #333;
        background-color: white;
    }

    .edit-input:focus {
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
    }
</style>

</div>

<script>
    function copyData(map) {
        var $temp = $("<input>");
        $("#mappingList").append($temp);
        $temp.val(map).select();
        document.execCommand("copy");
        $temp.remove();
    }

    function editTemplate() {
        $("#aadhar").hide();
        $("#printBtn").hide();
        $("#editPanel").show();
    }

    function cancelBtn() {
        $("#aadhar").show();
        $("#printBtn").show();
        $("#editPanel").hide();
    }

    $("#manual_fields").change(function() {
        $("#manual_value").val('');
    });

    var manual_feilds_array=[];

    function addManualValue() {
        var id = $("#manual_fields").val();
        var value = $("#manual_value").val();
        if (id == '')
            return false;
        switch (id) {
            case "fees_amount":
                var amount_in_words = number_in_words(value);
                $("#fees_amount_in_words").html(amount_in_words + " Rupees Only");
                break;
            case "dob":
                var dob = value.split("-");
                var d = number_in_words(dob[0]);
                var m = number_in_words(dob[1]);
                var y = number_in_words(dob[2]);
                $("#dob_in_words").html(d + ' - ' + m + ' - ' + y);
                break;
            default:
        }

        $("#" + id).html(value);

        var  obj = {};
        obj[id] = value;
        manual_feilds_array.push(obj);

    }

    var manual_fields_array = [];

    $(document).ready(function () {
        // Process all editable fields on page load
        $(".editable").each(function() {
            var content = $(this).text().trim();
            // Check if content is empty or contains placeholder markers
            if (content === '' || content.includes('%%')) {
                var fieldName = $(this).data("field");
                // Format the field name for better readability
                var formattedFieldName = fieldName.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
                $(this).html("Click to edit " + formattedFieldName);
                $(this).addClass("placeholder-text");
            } else {
                // If data exists, add a subtle edit indicator on hover
                $(this).addClass("has-data");
            }
        });

        // Handle click on editable fields
        $(".editable").click(function () {
            // Save any currently open editing fields
            $(".editing").each(function () {
                saveEditedValue($(this).find("input"));
            });

            if (!$(this).hasClass("editing")) {
                var currentText = $(this).text().trim();
                var fieldName = $(this).data("field");

                // If it's a placeholder, clear it before editing
                if ($(this).hasClass("placeholder-text")) {
                    currentText = "";
                }

                var inputField = $("<input type='text' class='edit-input'>").val(currentText);

                $(this).html(inputField)
                       .addClass("editing")
                       .removeClass("placeholder-text has-data");

                inputField.focus();

                // Select all text in the input field for easy editing
                inputField[0].select();

                // Save on blur
                inputField.blur(function () {
                    saveEditedValue($(this));
                });

                // Save on Enter key
                inputField.keypress(function (e) {
                    if (e.which == 13) { // Enter key
                        saveEditedValue($(this));
                        return false; // Prevent form submission
                    }
                });
            }
        });

        function saveEditedValue(inputElement) {
            var newValue = inputElement.val().trim();
            var parentSpan = inputElement.parent();
            var field = parentSpan.data("field");
            var formattedFieldName = field.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });

            // If the value is empty, show the placeholder again
            if (newValue === '') {
                parentSpan.html("Click to edit " + formattedFieldName)
                         .removeClass("editing has-data")
                         .addClass("placeholder-text");
            } else {
                parentSpan.html(newValue)
                         .removeClass("editing placeholder-text")
                         .addClass("has-data");
            }

            // Store updated value in an object
            var obj = {};
            obj[field] = newValue;
            manual_fields_array.push(obj);

            // Apply special logic for specific fields
            if (field === "fees_amount") {
                var amount_in_words = number_in_words(newValue);
                $("#fees_amount_in_words").html(amount_in_words + " Rupees Only")
                                         .removeClass("placeholder-text")
                                         .addClass("has-data");
            }
            else if (field === "dob") {
                var dob = newValue.split("-");
                if (dob.length === 3) {
                    var d = number_in_words(dob[0]);
                    var m = number_in_words(dob[1]);
                    var y = number_in_words(dob[2]);
                    $("#dob_in_words").html(d + ' - ' + m + ' - ' + y)
                                     .removeClass("placeholder-text")
                                     .addClass("has-data");
                }
            }
        }
    });


    function number_in_words(amount) {
        var words = new Array();
        words[0] = 'Zero';
        words[1] = 'One';
        words[2] = 'Two';
        words[3] = 'Three';
        words[4] = 'Four';
        words[5] = 'Five';
        words[6] = 'Six';
        words[7] = 'Seven';
        words[8] = 'Eight';
        words[9] = 'Nine';
        words[10] = 'Ten';
        words[11] = 'Eleven';
        words[12] = 'Twelve';
        words[13] = 'Thirteen';
        words[14] = 'Fourteen';
        words[15] = 'Fifteen';
        words[16] = 'Sixteen';
        words[17] = 'Seventeen';
        words[18] = 'Eighteen';
        words[19] = 'Nineteen';
        words[20] = 'Twenty';
        words[30] = 'Thirty';
        words[40] = 'Forty';
        words[50] = 'Fifty';
        words[60] = 'Sixty';
        words[70] = 'Seventy';
        words[80] = 'Eighty';
        words[90] = 'Ninety';
        amount = amount.toString();
        var atemp = amount.split(".");
        var number = atemp[0].split(",").join("");
        var n_length = number.length;
        var words_string = "";
        if (n_length <= 9) {
            var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
            var received_n_array = new Array();
            for (var i = 0; i < n_length; i++) {
                received_n_array[i] = number.substr(i, 1);
            }
            for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
                n_array[i] = received_n_array[j];
            }
            for (var i = 0, j = 1; i < 9; i++, j++) {
                if (i == 0 || i == 2 || i == 4 || i == 7) {
                    if (n_array[i] == 1) {
                        n_array[j] = 10 + parseInt(n_array[j]);
                        n_array[i] = 0;
                    }
                }
            }
            value = "";
            for (var i = 0; i < 9; i++) {
                if (i == 0 || i == 2 || i == 4 || i == 7) {
                    value = n_array[i] * 10;
                } else {
                    value = n_array[i];
                }
                if (value != 0) {
                    words_string += words[value] + " ";
                }
                if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Crores ";
                }
                if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Lakhs ";
                }
                if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                    words_string += "Thousand ";
                }
                if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                    words_string += "Hundred and ";
                } else if (i == 6 && value != 0) {
                    words_string += "Hundred ";
                }
            }

            words_string = words_string.split(" ").join(" ");
        }
        return words_string;
    }
</script>