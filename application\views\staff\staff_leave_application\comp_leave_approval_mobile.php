<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/leaves/dashboard'); ?>">Staff Leaves Master</a></li>
    <li class="active">Approve Compensatory Leaves</li>
</ul>

<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding padding8px">
            <h3 class="card-title panel_title_new_style"><strong>Approve Compensatory Leaves</strong></h3>
        </div>        
        <div class="card-body padding8px">
            <?php if($applyLeave){ ?>
                <div class="form-group">
                    <select title="All" class="form-control select" name="status" id="status" multiple>
                        <option selected value="0">Pending</option>
                        <option value="1">Approved</option>
                        <option value="2">Rejected</option>
                    </select>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary btn-block" onclick="getLeaves();">Get</button>
                </div>
                <!-- <div class="col-md-3 form-group">
                    <select class="form-control" name="leaves" id="leaves">
                        <option value="3">All Leaves</option>
                        <option value="2">My Filed Leaves</option>
                        <option value="1">My Leaves</option>
                    </select>
                </div> -->
                <p style="font-size: 16px;"><strong>Applied leaves : </strong><?php echo empty($applied->total)? 0:$applied->total; ?></p>
                <p style="font-size: 16px;"><strong>Approved leaves : </strong><?php echo empty($approved->total)? 0:$approved->total; ?></p>
            <?php } ?>
                

            <div class="col-md-12 mt-3 leaveData p-0">
                
            </div>
        </div>
    </div>

    <div class="modal fade" id="showAttendanceInfo" tabindex="-1" role="dialog" aria-labelledby="showAttendanceInfoLabel" aria-hidden="true" style="">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="showAttendanceInfoLabel">Modal title</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="attendance_staus">
            <h1>
                hey there
            </h1>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary">Save changes</button>
          </div>
        </div>
      </div>
    </div>
</div>

<a href="<?php echo site_url('staff/leaves/dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js" integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
var approveLeave = 0;
var staffId = 0;
$(document).ready(function(){
    approveLeave = <?php echo $applyLeave; ?>;
    staffId = <?php echo $staff; ?>;
    getLeaves();
    $("#leaves").on('change', getLeaves);
});

function getLeaves() {
    var val = 1;
    const status=$("#status").val();
    $.ajax({
        url: '<?php echo site_url('staff/leaves/getAllCompLeaves'); ?>',
        data: {'val': val,"status":status},
        type: "post",
        success: function (data) {
            var leaves = JSON.parse(data);
            if(leaves.length == 0) {
                 let msg = `
                    <div style="color:red;text-align:center;
                        color: black;
                        border: 2px solid #fffafa;
                        text-align: center;
                        border-radius: 6px;
                        position: relative;
                        padding: 10px;
                        font-size: 14px;
                        margin-top: 14px;
                        background: #ebf3ff;">
                        No pending leaves to show
                        </div>
                    `;
                $(".leaveData").html(msg);
                return false;
            }
            var html = '<table class="table table-bordered table-responsive">';
            html += '<thead><tr><th>#</th><th>Staff Name</th><th>Applied On date</th><th>Applied for date</th><th>Details</th><th>Status</th><th>Approval Remarks</th><th>Action</th></tr></thead>';
            html += '<tbody>';
            for(var i=0; i<leaves.length; i++) {
                html += '<tr id="leave_'+leaves[i].id+'">';
                html += '<td>'+(i+1)+'</td>';
                html += constructLeave(leaves[i]);
                html += '</tr>';
            }
            html += '</tbody>';
            html += '</table>';
            $(".leaveData").html(html);
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function getStatus(status_val) {
    var status = "Pending";
    if (status_val == '1') {
        status = "<span class='text-success'>Approved</span>";
    } else if (status_val == '2') {
        status = "<span class='text-danger'>Rejected</span>";
    }
    return status;
}

//echo date('d-m-Y', strtotime($cl->worked_on))
function constructLeave(leave) {
    var html = '<td>'+leave.staff_name+'</td>';
    html += '<td>'+leave.requested_on+'</td>';
    if(leave.worked_on_date){
        html += '<td>'+leave.worked_on_date+'</td>';
    }else{
        html += '<td>'+leave.worked_on+'</td>';
    }
    html += '<td>'+leave.comments+'</td>';
    html += '<td>'+getStatus(leave.status)+'</td>';
    html += '<td>'+((leave.approval_comment)?leave.approval_comment:'-')+'</td>';
    var action = '';
    
    if(leave.status == '1' && approveLeave) {
        action = '<button class="btn btn-sm btn-danger" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\',\''+leave.worked_on+'\',\''+leave.comments+'\', 1)">Reject Leave</button>';
    }
    else if(leave.status == '0' && approveLeave) {
        action = '<button class="btn btn-sm btn-primary" onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\',\''+leave.worked_on+'\',\''+leave.comments+'\', 0)">Approve/Reject</button>';
    }
    else if(leave.status == '2'){
        action='-';
        // action = '<button class="btn btn-sm btn-green " onclick="updateLeaveStatus('+leave.id+', \''+leave.staff_name+'\',\''+leave.worked_on+'\',\''+leave.comments+'\', 2)">Approve Leave</button>';
    }
    html += '<td>'+action+'</td>';
    return html;
}

const getAttendanceInfo=async function(requestDate,staffId){
    let attendanceInfo;
    await $.ajax({
        url: '<?php echo site_url('staff/leaves/get_individual_staff_attendance_info'); ?>',
            data: { requestDate, staffId },
            type: "POST",
            success: function (data) {
                attendanceInfo = JSON.parse(data);
            }
        })
        return attendanceInfo;
    }


async function showAttendanceInfo(requestDate,staffId,leaveId,staffName,oldStatus){
    requestDate=requestDate.toString().split("-").reverse().join("-");
    const attendanceInfo=await getAttendanceInfo(requestDate,staffId);
    
   if(!attendanceInfo){
        return  Swal.fire({
            title: 'Attendace Transaction Info',
            html: "Attendance not found",
            width: '100%',
            customClass: 'view-custom-swal',
            allowOutsideClick: false,
            confirmButtonText: 'Okay',
            showConfirmButton: true,
            preConfirm: () => {
            }
        }).then((result) => {
            updateLeaveStatus(leaveId,staffName,oldStatus);
        });
    }

    let html="";

    attendanceInfo.forEach((a,i)=>{
        if(!a.hasOwnProperty("attendance_status")){
            const {event_time, event_type, latitude, longitude, source, is_outside_campus} = a;
            html+=`    
            <table class="table table-bordered" style="text-align:left;">
                        <tr>
                            <th>Event Type</th>
                            <td>${event_type}</td>
                        </tr>

                        <tr>
                            <th>Event Time</th>
                            <td>${event_time}</td>
                        </tr>

                        <tr>
                            <th>Source</th>
                            <td>${source}</td>
                        </tr>

                        <tr>
                            <th>Is Outside</th>
                            <td>${is_outside_campus && "Yes" || "No"}</td>
                        </tr>
                        </table>`;
        }else{
            const {attendance_status} = a;
             html+=`
                    <table class="table table-bordered" style="text-align:left;">
                        <tr>
                            <th>Attendance Status</th>
                            <td>${attendance_status}</td>
                        </tr>
                    </table>`;
        }
    })

    Swal.fire({
        title: 'Attendace Transaction Info',
        html: html,
        width: '100%',
        customClass: 'view-custom-swal',
        allowOutsideClick: false,
        confirmButtonText: 'Okay',
        showConfirmButton: true,
        preConfirm: () => {
        }
    }).then((result) => {
        updateLeaveStatus(leaveId,staffName,oldStatus);
    });
}

function updateLeaveStatus(leave_id, staff_name, worked_on, comments, old_status){

    $.ajax({
        url: '<?php echo site_url('staff/leaves/staffCompLeaves'); ?>',
        data: {'leave_id': leave_id},
        type: "post",
        success: function (data) {
            var leave = JSON.parse(data);
            leave.staff_name = staff_name;
            var leave_applied_for = leave.leave_applied_for;
            var leave_year_id = leave.leave_year_id;

            var html = '<table class="table table-bordered table-responsive" style="text-align:left;">';
            html += '<tr><th style="width: 20%;">Staff</th><td>'+leave.staff_name+'</td><tr>';
            html += `<tr><th>Worked on</th><td>${leave.worked_on} <button class="badge badge-info" onclick="showAttendanceInfo('${leave.worked_on}','${leave.staff_id}','${leave_id}','${staff_name}','${old_status}')">See Attendance Status</button></td> </td><tr>`;
            html += '<tr><th>Details</th><td>'+leave.comments+'</td><tr>';
            html += '<tr><th>Status</th><td>';
            html += '<label class="radio-inline"><input style="margin-top:1px;" value="1" type="radio" '+(leave.status==2?'checked':'')+' name="status">Approve</label>';
            html += '<label class="radio-inline"><input style="margin-top:1px;" value="2" type="radio" '+(leave.status==1?'checked':'')+' name="status">Reject</label>';
            html += '</td><tr>';
            html += '<tr><th>Remarks</th><td><textarea id="description" class="form-control"></textarea></td><tr>';
        //html += '<tr><td colspan="2" onclick="viewHistory('+staff_id+', '+leave_id+', \''+staff_name+'\')" style="text-align: right;"><button class="btn btn-primary btn-sm">View History</button></td></tr>';
            html += '</table>';
            Swal.fire({
                title: 'Approve/Reject Leave',
                html: html,
                width: 'auto%',
                confirmButtonText: 'Save',
                showCancelButton: true,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    var new_status = $("input[name='status']:checked").val();
                    var description = $("#description").val();
                    var data = {
                        'id' : leave_id,
                        'old_status' : old_status,
                        'new_status' : new_status,
                        'description' : description,
                        'leave_applied_for' : leave_applied_for,
                        'leave_year_id' : leave_year_id,
                        'no_of_days' : leave.no_of_days
                    };
                    saveLeaveStatus(data, leave);
                }
            });
        },
        error: function (err) {
            console.log(err);
        }
    });
}

function saveLeaveStatus(input, leave) {
    //document.write(input.description);
    $.ajax({
        url: '<?php echo site_url('staff/leaves/saveCompStatus'); ?>',
        data: input,
        type: "post",
        success: function (data) {
            if(parseInt(data)){
                Swal.fire({
                title: "Successful",
                text: "Updated status successfully",
                icon: "success",
                });
                leave.status = input.new_status;
                leave.approval_comment = input.description;
            } else{
                Swal.fire({
                title: "Oops",
                text: "Leave has been taken on this date already, Cannot be rejected!",
                icon: "error",
                });
            }
            var sl = $("#leave_"+input.id+" td:first").html();
            var html = '<td>'+sl+'</td>';
            html += constructLeave(leave);
            $("#leave_"+input.id).html(html);
        },
        error: function (err) {
            console.log(err);
        }
    });
}
</script>
