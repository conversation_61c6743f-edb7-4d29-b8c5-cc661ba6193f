
<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('admission_process');?>">Applications</a></li>
  <li><a href="<?php echo site_url('admission_process/admission_settings');?>">Admission Settings</a></li>
  <li class="active">Admission settings edit</li>
</ul>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                	<h3 class="card-header panel_heading_new_style_staff_border">
                    <a class="back_anchor" href="<?php echo site_url('admission_process/admission_settings');?>" class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                    </a>
                    Form Settings edit
                    </h3>
                </div>
            </div>
        </div>
            <div class="card-body">
                <form enctype="multipart/form-data" method="post" id="application-form" action="<?php echo site_url('admission_process/update_admission_settings/'.$edit_setting->id);?>" data-parsley-validate="" class="form-horizontal">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Form Year</label>
                            <div class="col-md-6 col-xs-12">                      
                                <input type="text" class="form-control" value="<?php echo $edit_setting->form_year ?>" name="form_year">
                            </div>
                        </div>
                       <!--  <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Admission header Logo</label>
                            <div class="col-md-6 col-xs-12">           
                                <input type="file" class="form-control" value="<?php echo $edit_setting->admission_logo ?>" name="admission_logo">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Admission background Logo</label>
                            <div class="col-md-6 col-xs-12">           
                                <input type="file" class="form-control" value="<?php echo $edit_setting->admission_bg_logo ?>" name="admission_bg_logo">
                            </div>
                        </div>
 -->
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Application Fee Amount</label>
                            <div class="col-md-6 col-xs-12">           
                                <input type="text" class="form-control" value="<?php echo $edit_setting->admission_fee_amount ?>" name="admission_fee_amount">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Form Name</label>
                            <div class="col-md-6 col-xs-12">           
                                <input type="text" class="form-control" value="<?php echo $edit_setting->form_name ?>" name="form_name">
                             	<span class="help-block">Ex: Grade 1 to 10 for 2020-21 </span>
                            </div>
                        </div>

                      	<div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">School Full Name</label>
                            <div class="col-md-6 col-xs-12">                   
                                <input type="text" class="form-control" value="<?php echo $edit_setting->school_name ?>" name="school_full_name">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">School Short Name</label>
                            <div class="col-md-6 col-xs-12">
                                <input type="text" class="form-control" value="<?php echo $edit_setting->school_short ?>" name="school_short_name">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">School Address</label>
                            <div class="col-md-6 col-xs-12">
                            	<?php $address = json_decode($edit_setting->address); ?>
                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->line1 ?>" placeholder="line1" name="school_address[line1]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->line2 ?>" placeholder="line2" name="school_address[line2]">
									</div>
                            	</div>

                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->line3 ?>" placeholder="line3" name="school_address[line3]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->line4 ?>" placeholder="line4" name="school_address[line4]">
									</div>
                            	</div>

                            	<div class="row form-group">
                            		<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->fax ?>" placeholder="fax" name="school_address[fax]">
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" value="<?php echo $address->email ?>" placeholder="email" name="school_address[email]">
									</div>
                            	</div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" value="<?php echo $address->phone ?>" placeholder="Phone" name="school_address[phone]">
                                    </div>
                                </div>

                            </div>
                        </div>
                        <?php  
			              $arr = array();
			              foreach ($classList as $key => $class) { 
			                $arr[$class->class_name] = $class->class_name; 
			              }
			            ?>

                        <div class="form-group">
                        	<label class="col-md-3 col-xs-12 control-label"> Class Applied for </label>
                        	<div class="col-md-6 col-xs-12">
	                		 	<select name="className[]" id="classId" multiple title="Select Classes" class="form-control selectpicker">
			                      	<?php foreach ($arr as $key => $class) { ?>
			                        	<option <?php if(in_array($key, json_decode($edit_setting->class_applied_for))) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
			                      	<?php } ?>
			                    </select>
		                	</div>
                        </div>
                        <div class="form-group">
                        	<label class="col-md-3 col-xs-12 control-label">Aadhar Declaration File</label>
                        	<div class="col-md-4 col-xs-12">
	                		 	<input type="file" class="form-control" name="aadhar_declaration_template" id="aadhar_declaration_template" accept="application/pdf">
		                	</div>
                            <div class="col-md-2 col-xs-12">
                                <?php if(!empty($edit_setting->aadhar_declaration_template)) { ?>
                                    <a href="<?= $edit_setting->aadhar_declaration_template ?>" class="btn btn-secondary" target="_blank">View <i class="fa fa-eye"></i></a>
                                <?php } ?>
                            </div>
                        </div>

                        <div class="form-group">
                        	<label class="col-md-3 col-xs-12 control-label">PAN Declaration File</label>
                        	<div class="col-md-4 col-xs-12">
	                		 	<input type="file" class="form-control" name="pan_declaration_template" id="pan_declaration_template" accept="application/pdf">
		                	</div>
                            <div class="col-md-2 col-xs-12">
                                <?php if(!empty($edit_setting->pan_declaration_template)) { ?>
                                    <a href="<?= $edit_setting->pan_declaration_template ?>" class="btn btn-secondary" target="_blank">View <i class="fa fa-eye"></i></a>
                                <?php } ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label"> Document Input Version </label>
                            <div class="col-md-6 col-xs-12">
                                <select name="document_input_version" id="document_input_version" class="form-control" onchange="change_document_input_version()">
                                    <option value="V1" <?php if($edit_setting->document_input_version == 'V1') echo 'selected'?> >V1</option>
                                    <option value="V2" <?php if($edit_setting->document_input_version == 'V2') echo 'selected'?>>V2</option>
                                </select>
                            </div>
                        </div> 
                       
                        <div id="document_version_container" style="margin-bottom: 10px;"></div>

                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Annual Income Option </label>
                            <div class="col-md-6">
                                <textarea name="annual_income_options" class="form-control" id="annual_income_options"><?= $edit_setting->annual_income_options?></textarea>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Guidelines/Instructions</label>
                            <div class="col-md-6">
                                <textarea name="guidelines" class="summernote"><?php echo $edit_setting->guidelines ?></textarea>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Instructions</label>
                            <div class="col-md-6">
                                <textarea name="instructions" class="summernote"><?php echo $edit_setting->instruction_file ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Final Description</label>
                            <div class="col-md-6">
                                <textarea name="final_description" class="summernote"><?php echo $edit_setting->final_description ?></textarea>
                            </div>
                        </div>
                        <!-- <div class="form-group">
                            <label class="col-md-3 col-xs-12 control-label">Pre Education info</label>
                            <div class="col-md-6">
                                <select name="className[]" id="classId" multiple title="Select Classes" class="form-control select">
                                    <?php foreach ($arr as $key => $class) { ?>
                                        <option <?php if(in_array($key, $selectedClasses)) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
                                    <?php } ?>
                                </select>
                                <div class="panel-body" style="border: 2px solid #ccc">
                                    <ul class="panel-controls">
                                      <li><a href="javascript:void(0)" data-placement="top" data-toggle="tooltip" data-original-title="Add Row" onclick="insrowpayment()" class="control-primary"><span class="fa fa-plus"></span></a></li>
                                    </ul>
                                    <div class="form-group">
                                      <div class="col-md-12">
                                        <div id="POItablediv">
                                          <table id="paymode">
                                            <thead>
                                              <tr>
                                                <td style="display: none;">#</td>
                                                <th>Name</th>
                                                <th>Type</th>
                                              </tr>
                                            </thead>
                                            <tbody>
                                             <tr>
                                                <td style="display: none;">1</td>
                                                <td><input type="text" placeholder="Subject"  name="sub_name[]" class="form-control"></td>
                                                <td align="center">
                                                    <select class="form-control" name="reconcilation_reqd[]" data-parsley-id="107">
                                                        <option value="label">label</option>
                                                        <option value="text">text</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <ul class="panel-controls" id="delPOIbutton" onclick="deleterowpayment(this)">
                                                        <li><a href="javascript:void(0)" style="border-color: #ff0000;" class="control-primary"><span style=" color:red" class="glyphicon glyphicon-remove"></span></a></li>
                                                    </ul>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table> 
                                        </div> 
                                      </div>
                                    </div>                  
                                </div>
                            </div>
                        </div> -->
		            </div>

                <div class="col-md-12" style="margin-top: 20px;">
                    <center>                                                                       
                        <button class="btn btn-primary">Update Changes <span class="fa fa-floppy-o fa-right"></span></button>
                        <a href="<?php echo site_url('admission_process/admission_settings');?>" class="btn btn-warning">Cancel</a>
                    </center>
                </div>
                </form>   
                </div>
    </div>
</div>

<style>
    .form-horizontal .form-group{
        margin-right: -10px;
        margin-left: -10px;
    }
</style>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/summernote/summernote.js"></script>
<script>
  function insRow() {
    var subCatTable = document.getElementById('documentTable');
    var new_row = subCatTable.rows[0].cloneNode(true);
    subCatTable.appendChild(new_row);
  }

  $(document).ready(function() {
    change_document_input_version();
    ['#documents', '#classId'].forEach(id => {
    $(id).selectpicker({
        liveSearch: true,
        liveSearchPlaceholder: 'Search fields...'
    });
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
});

  function deleteRow(cell) {
    var rowIndex = cell.parentNode.rowIndex;
    if (rowIndex == 0) {
      alert('Row cannot be deleted');
    } else {
      document.getElementById('documentTable').deleteRow(rowIndex);
    }
  }

    function deleterowpayment(row){
        var i=row.parentNode.parentNode.rowIndex;
        if(i==1){
          alert('you can not delete');
        }else{
         document.getElementById('paymode').deleteRow(i);
        } 
    }

    function insrowpayment(){
        var x=document.getElementById('paymode');
        var new_rowp = x.rows[1].cloneNode(true);
        var plen = x.rows.length;
        new_rowp.cells[0].innerHTML = plen;
        var pinp = new_rowp.cells[1].getElementsByTagName('input')[0];
        pinp.id += plen;
        pinp.value += '';
        var pyinp = new_rowp.cells[2].getElementsByTagName('select')[0];
        pyinp.id += plen;
        pyinp.value += '';
        x.appendChild(new_rowp ); 
    }

    const documentsList = <?php echo json_encode($documents_list); ?>;
    const v1Documents = <?php echo json_encode(json_decode($edit_setting->documents)); ?>;

    function change_document_input_version() {
    const version = document.getElementById('document_input_version').value;
    const container = document.getElementById('document_version_container');
    container.innerHTML = ''; // Clear existing fields

    if (version === 'V1') {
        const group = document.createElement('div');
        group.className = 'form-group';

        let rowsHtml = '';
        console.log(v1Documents);
        if (v1Documents && v1Documents.length > 0) {
            v1Documents.forEach(value => {
                rowsHtml += `
                    <tr>
                        <td><input name="documents[]" class="form-control" type="text" value="${value}" /></td>
                        <td onclick="deleteRow(this)">
                            <a href="javascript:void(0)">
                                <span style="color:red; font-size:20px;" class="glyphicon glyphicon-remove"></span>
                            </a>
                        </td>
                    </tr>`;
            });
        } else {
            rowsHtml += `
                <tr>
                    <td><input name="documents[]" class="form-control" type="text" /></td>
                    <td onclick="deleteRow(this)">
                        <a href="javascript:void(0)">
                            <span style="color:red; font-size:20px;" class="glyphicon glyphicon-remove"></span>
                        </a>
                    </td>
                </tr>`;
        }

        group.innerHTML = `
            <label class="col-md-3 col-xs-12 control-label"> Documents </label>
            <div class="col-md-5 col-xs-12">
                <table id="documentTable" style="width: 100%">
                    ${rowsHtml}
                </table>
                <span class="help-block">Ex: Aadhar Card, Birth Certificate</span>
            </div>
            <div class="col-md-1">
                <a href="javascript:void(0)" class="btn" onclick="insRow()">
                    <i style="font-size: 20px;" class="fa fa-plus-square"></i>
                </a>
            </div>
        `;
        container.appendChild(group);
    } else if (version === 'V2') {
        const group = document.createElement('div');
        group.className = 'form-group';

        let optionsHtml = '';
        documentsList.forEach(doc => {
            const isSelected = v1Documents.includes(doc.document_name) ? 'selected' : '';
            optionsHtml += `<option value="${doc.id}" ${isSelected}>${doc.document_name}</option>`;
        });

        group.innerHTML = `
            <label class="col-md-3 col-xs-12 control-label"> Documents </label>
            <div class="col-md-6 col-xs-12">
                <select name="documents[]" class="form-control selectpicker" multiple title="Select Documents" id="documents">
                    ${optionsHtml}
                </select>
            </div>
        `;

        container.appendChild(group);
    }
}
</script>
