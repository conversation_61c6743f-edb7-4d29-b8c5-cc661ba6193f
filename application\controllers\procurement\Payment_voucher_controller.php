<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 may 2025
 *
 * Description: Controller for Payment Voucher Module. Entry point for Payment Voucher Module
 *
 * Requirements: PHP5 or above
 *
 */

class Payment_voucher_controller extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isSuperAdmin()) {
        redirect('dashboard', 'refresh');
        }

        $this->load->library('filemanager');
        $this->load->model('procurement/Payment_voucher_model');
       
    }

    function payment_voucher_dashboard() {
        $dashboard['main_content'] = 'procurement/payment_voucher_view/payment_voucher_dashboard_view';
        $this->load->view('inc/template', $dashboard);
    }

    function manage_payment_vouchers() {
        $dashboard['vendors']= $this->Payment_voucher_model->get_vendors();
        $dashboard['main_content'] = 'procurement/payment_voucher_view/manage_payment_vouchers';
        $this->load->view('inc/template', $dashboard);
    }

    function add_payment_advice() {
        $dashboard['vendors']= $this->Payment_voucher_model->get_vendors();
        $dashboard['approvers']= $this->Payment_voucher_model->get_approvers();
        $dashboard['main_content'] = 'procurement/payment_voucher_view/add_payment_advice';
        $this->load->view('inc/template', $dashboard);
    }

    function get_vendor_wise_invoices() {
        $data= $this->Payment_voucher_model->get_vendor_wise_invoices();
        echo json_encode($data);
    }

    function get_invoices_details() {
         $data= $this->Payment_voucher_model->get_invoices_details();
        echo json_encode($data);
    }

    function submit_voucher_form() {
        $data= $this->Payment_voucher_model->submit_voucher_form();
        echo json_encode($data);
    }

    function get_payment_vouchers() {
        $data= $this->Payment_voucher_model->get_payment_vouchers();
        // echo '<pre>'; print_r($data); die();
        echo json_encode($data);
    }

     function voucher_details($voucher_master_id) {
        $view['voucher_master_id']= $voucher_master_id;
        $view['loggenIn_userId']= $this->authorization->getAvatarStakeHolderId();
        $view['paDetails']= $this->Payment_voucher_model->get_pa_details($voucher_master_id);
        $view['paInvoices']= $this->Payment_voucher_model->get_pa_invoices($voucher_master_id);
        $view['paAttachments']= $this->Payment_voucher_model->get_pa_attachments($voucher_master_id);
        $view['paApprovers']= $this->Payment_voucher_model->get_voucher_approvers_from_table($voucher_master_id);
        $view['paHistory']= $this->Payment_voucher_model->get_pa_history($voucher_master_id);
// echo '<pre>'; print_r($view); die();
        $view['main_content'] = 'procurement/payment_voucher_view/voucher_details_view';
        $this->load->view('inc/template', $view);
    }

    function save_invoices_details() {
        $data= $this->Payment_voucher_model->save_invoices_details();
        echo json_encode($data);
    }

    function add_document() {
        $data= $this->Payment_voucher_model->add_document();
        echo json_encode($data);
    }

    function get_approver_details() {
        $data= $this->Payment_voucher_model->get_approver_details();
        echo json_encode($data);
    }

    function download_voucher_attachement($voucher_attachments_id= 0) {
        $this->load->library('filemanager');
        if($voucher_attachments_id == 0) {
            $this->session->set_flashdata('flashError', 'Something Went Wrong OR Attachement not found');
            redirect('procurement/payment_voucher_controller/manage_payment_vouchers');
        }

        $file_link = $this->Payment_voucher_model->getDocumentURL($voucher_attachments_id);
    //    echo '<pre>'; print_r($file_link); die();
        $signed_resource = $this->filemanager->getSignedUrlWithExpiry($file_link->file_path, '+5 minutes');
        $file = explode("/", $file_link->file_path);
        $file_name = 'voucher_attachment_'.$voucher_attachments_id;
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $data = file_get_contents($signed_resource);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
        $this->load->library('user_agent');
        redirect($this->agent->referrer());	
    }

    function remove_document() {
        $data= $this->Payment_voucher_model->remove_document();
        echo json_encode($data);
    }

    function reject_the_voucher() {
        $data= $this->Payment_voucher_model->reject_the_voucher();
        echo json_encode($data);
    }

    function approve_the_voucher() {
        $data= $this->Payment_voucher_model->approve_the_voucher();
        echo json_encode($data);
    }

    function log_failed_attempt() {
        $data= $this->Payment_voucher_model->log_failed_attempt();
        echo json_encode($data);
    }

}
?>