<ul class="breadcrumb">
  <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
  <li class="active">Manage Assessment Types</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Assessment Types
                    </h3>
                </div>
                <div class="col-md-3 pr-0">
                    <a href="" class="new_circleShape_res" style="background-color: #fe970a; float: right;"
                        data-toggle="modal" data-target="#resource-uploader">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <div id="assessmentsDiv">
                <div class="no-data-display">Loading...</div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="resource-uploader" tabindex="-1" role="dialog" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto; width: 50%;">
            <div class="modal-header">
                <h4 class="modal-title" id="modalHeader">Add New Assessment</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal" onclick="resetForm()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                    <form action="" data-parsley-validate="true" class="form-horizontal mb-2" id="assessment">
                        <div class="form-group">
                            <label for="assessment_name" class="col-md-4">Assessment Type <font color="red">*</font></label>
                            <div class="col-md-6">
                                <input id="assessment_name" class="form-control" placeholder="Enter Assessment Type" name="assessment_name" class="form-control" type="text" required="" maxlength="100" data-parsley-pattern="/^([a-zA-Z0-9 _-]+)$/" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" data-dismiss="modal" onclick="resetForm()">Close</button>
                <button class="btn btn-primary mt-0" type="button" onclick="add_assessment()">Submit </button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }

  .widthadjust {
    width: 600px;
    margin: auto;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script language="javascript">
  $(document).ready(function () {
    get_assessment_types();
  });

  function resetForm(){
    var form = $('#assessment');
    form[0].reset();
    form.parsley().reset();
  }

  function get_assessment_types() {
    $.ajax({
      url: '<?php echo site_url('academics/lesson_plan/get_assessment_types'); ?>',
      type: 'post',
      success: function (data) {
        parsed_data = $.parseJSON(data);
        let assessment = parsed_data.assessment_types;
        if (assessment.length == 0) {
          $("#assessmentsDiv").html('<div class="no-data-display">No Data Found</div>');
          return;
        } else {
          $("#assessmentsDiv").html(construct_assessment_type_table(assessment));
          $('#assessment_type_table').DataTable({
            "ordering": false,
            "pageLength": 10,
            "lengthMenu": [10, 20, 50, 100, 200, 500],
            "dom": '<"top"fl>rt<"bottom"ip><"clear">',
            "language": {
              "search": "Search:",
              "paginate": {
                "previous": "Previous",
                "next": "Next",
                "first": "First",
                "last": "Last"
              }
            }
          });
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function construct_assessment_type_table(assessment) {
      let html = '';
      html += `
      <table id="assessment_type_table" class="table table-bordered">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Assessment name</th>
                  <th>Actions</th>
                </tr>
              </thead>
      `;
      html += `<tbody>`;
      for (var i = 0; i < assessment.length; i++) {
        var data = assessment[i];
        html += `
                <tr>
                  <td>${i + 1}</td>
                  <td>${data['name']}</td>
                  <td><button class="btn btn-danger" style="margin-top:5px;" onclick="delete_assessment('${data['id']}','${data['name']}')"><i class="fa fa-trash-o mr-0"></i></button></td>
                </tr>`;
      }
      html += `</tbody>
          </table>`;
    return html;
  }

  function delete_assessment(assessment_id, assessment_name) {
    Swal.fire({
      title: "Are you sure?",
      text: `You want to delete ${assessment_name}?!`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: '<?php echo site_url('academics/lesson_plan/delete_assessment'); ?>',
          type: 'post',
          data: { 'assessment_id': assessment_id },
          success: function (data) {
            parsed_data = $.parseJSON(data);
            if (parsed_data) {
              Swal.fire({
                icon: "success",
                title: "Deleted assessment",
                text: "Deleted assessment successfully!",
              }).then((result) => {
                get_assessment_types();
              });
            }
            else {
              Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
              });
            }
          },
          error: function (err) {
            console.log(err);
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Something went wrong!",
            });
          }
        });
      }
    });
  }

  function add_assessment() {
    const form = $("#assessment");
    if (form.parsley().validate()) {
      var assessment_name = $('#assessment_name').val();
      $.ajax({
        url: '<?php echo site_url('academics/lesson_plan/add_assessment'); ?>',
        type: 'post',
        data: { 'assessment_name': assessment_name },
        success: function (data) {
          parsed_data = $.parseJSON(data);
          if (parsed_data == -1) {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Assessment already exists!",
            }).then(() => {
              resetForm();
              $('#assessment_name').val('');
            });
            return;
          }
          if (parsed_data) {
            resetForm();
            $("#resource-uploader").modal('hide');
            get_assessment_types();
            Swal.fire({
              icon: "success",
              title: "Added assessment",
              text: "Added assessment successfully!",
            });
          } else {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Something went wrong!",
            });
            console.log(err);
          }
        },
        error: function (err) {
          console.log(err);
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong!",
          });
        }
      });
    }
  }
</script>