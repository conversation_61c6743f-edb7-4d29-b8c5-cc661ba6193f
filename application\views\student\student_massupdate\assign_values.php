<!--
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: View class for Mass Update for Student
 *
 * Requirements: PHP5 or above
 *
 -->

 <ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/student_massupdate');?>">Student Mass Update</a></li>
    <li>Assign values</li>
</ul>
<hr>
<div class="col-md-12 mb-5">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student/student_massupdate'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Student Mass Update - Enter Data
          </h3>
          <div class="col-md-8 d-flex align-items-center justify-content-end" id="exportButtons" style="">
						<div class="" style="">
							<div class="more">
								<button id="more-btn" class="more-btn">
									<span class="more-dot"></span>
									<span class="more-dot"></span>
									<span class="more-dot"></span>
								</button>
								<div class="more-menu" style="right: 2%;">
									<div class="more-menu-caret">
										<div class="more-menu-caret-outer"></div>
										<div class="more-menu-caret-inner"></div>
									</div>
									<div class="more-menu-item action_btn" data-button-type="download"
										role="presentation">
										<button type="button" class="more-menu-btn" role="menuitem"><i
												class="fa fa-download" aria-hidden="true"></i> Download
											Template</button>
									</div>

									<div class="more-menu-item action_btn" data-button-type="upload" role="presentation"
										data-toggle="modal" data-target="#upload_data_event_modal">
										<button type="button" class="more-menu-btn" role="menuitem" data-toggle="modal"
											data-target="#upload_data_event_modal"><i class="fa fa-upload"
												aria-hidden="true"></i>Upload</button>
									</div>

									<div class="more-menu-item action_btn" data-button-type="export" role="presentation"
										onclick="">
										<button type="button" class="more-menu-btn" role="menuitem"><i
												class="fa fa-file-excel-o" aria-hidden="true"></i>Export</button>
									</div>
								</div>
							</div>
						</div>
					</div>   
        </div>
      </div>    
    </div>    
    <form enctype="multipart/form-data" method="post" action="" data-parsley-validate="" class="form-horizontal" id="student_data">
      <div class="card-body">
        <h3>Class: <?= $className ?> <?php if (!empty($sectionName)) echo 'Section:'. $sectionName; ?> (<?php echo count($exportData); ?>)</h3>
        <?php
            //  echo '<pre>';print_r($field_objects);
            // echo '<pre>';print_r($exportData);die();
          if (empty($exportData)) {
            echo '<br><h4>No Student matched the selected criteria</h4>';
          } else {
            echo '<table class="table border">';
            echo '<thead>';
            foreach ($field_objects as $fObj) {
              if ($fObj['displayType'] != 'hidden')
                echo '<th class="csv_header">' . $fObj['displayName'] . '</th>';
            }
            echo '</th>';  
          }
          foreach ($exportData as $key=>$stdObj) {
            echo '<tr>';
            foreach ($field_objects as $fObj) {
              $vName = $fObj['varName'];
              $cName = $fObj['columnName'];
              $tName = $fObj['table'];
              $dataType = $fObj['dataType'];
			  $displayType = $fObj['displayName'];
              $is_required = '';
              if(isset($fObj['required']) && $fObj['required'] == true) {
                $is_required = 'required';
              }

              $validator = '';
              $class= '';
              $id=$cName.'_'.$stdObj->saId;
              if($cName == 'admission_no') {
                $validator = 'onkeyup="checkDuplicateAdmNo(this)"';
                $class= 'old_adm_nos cls_1';
              }

              switch ($fObj['displayType']) {
                case 'label':
                  echo '<td class="student_fields '.$vName.'">';
                  if (isset($stdObj->$vName)){
					echo $stdObj->$vName;
					echo '<input type="hidden" class="input_val" value="'.$stdObj->$vName.'">';
				  }else{
					echo '-';
					echo '<input type="hidden" class="input_val" value="-">';
				  }
                  // echo '</td>';
                  break;

				  case 'text':
					$validation = '';
					
					if ($cName == 'pen_number') {
						$validation = 'pattern="[A-Z0-9]{11}" maxlength="11" minlength="11" data-parsley-pattern-message="PEN Number must be 11 uppercase alphanumeric characters."';
					}
					if ($cName == 'aadhar_no') {
						$validation = 'pattern="^[2-9]{1}[0-9]{11}$" maxlength="12" minlength="12" data-parsley-pattern-message="Enter a valid 12-digit Aadhaar number starting with 2-9."';
					}
					if ($cName == 'apaar_id') {
						$validation = 'pattern="[A-Z0-9]{12}" maxlength="12" minlength="12" data-parsley-pattern-message="Apaar Number must be 12 uppercase alphanumeric characters."';
					}
					if ($cName == 'sts_number') {
						$validation = 'pattern="^[0-9]{3}\s[0-9]{3}\s[0-9]{3}$" maxlength="11" minlength="11" data-parsley-pattern-message="SATS Number must be in the format 261 146 506 (3 groups of 3 digits separated by spaces)."';
					}
					echo '<td class="student_fields">';
					echo '<input data-helpid="'.$id.'" class="form-control '.$class.' field_val" type="text" '.$validator.' '.$is_required.' '.$validation.' name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . $stdObj->$vName . '" data-old="'.$stdObj->$vName.'" id="'.$displayType.'">';
					echo '<span style="display:none;" id="'.$id.'" class="text-danger help-block">Value is duplicate</span>';
					echo '<input type="hidden" class="input_val" name="old_' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . $stdObj->$vName . '" id="old_'.$displayType.'">';
					echo '</td>';
					break;
                case 'datetimepicker':
                  //echo '<pre>';print_r($stdObj);die();
                  echo '<td class="student_fields">';
                    if ($stdObj->$vName == null || $stdObj->$vName == "" || $stdObj->$vName == '1970-01-01') {
                      echo '<input type="text" '.$is_required.' class="form-control dob_class field_val" id="'.$displayType.'" name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]"  placeholder="Enter '. $fObj['displayName'] . '" >';
					  echo '<input type="hidden" class="input_val" name="old_' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="">';
                    }
                    else {
                      echo '<input type="text" '.$is_required.' class="form-control dob_class field_val" id="'.$displayType.'" name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]"  placeholder="Enter '.$fObj['displayName'] . '" value="' . date('d-m-Y',strtotime($stdObj->$vName)) . '">';
					  echo '<input type="hidden" class="input_val"  name="old_' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . date('d-m-Y',strtotime($stdObj->$vName)) . '" >';              
                    }
                  echo '</td>';
                  break;
                case 'hidden':
                  echo '<input class="form-control " type="hidden" name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . $stdObj->$vName . '" id="'.$displayType.'">';
                  break;
                case 'combobox':
                  $optionArrName = $fObj['optionArrName'];
                  $options = $optionObjects->$optionArrName;
                  echo '<td class="student_fields">';
                  echo '<select class="form-control field_val" name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" id="'.$displayType.'">';
                  echo '<option value="">Select Value</option>';
                  foreach ($options as $opt) {
                    if ($opt == $stdObj->$vName)
                      echo '<option value="' . $opt . '" selected>' . $opt . '</option>';
                    else
                      echo '<option value="' . $opt . '">' . $opt . '</option>';
                  }
                  echo '</select>';
				  echo '<input type="hidden" class="input_val" name="old_' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . $stdObj->$vName . '">';
                  echo '</td>';
                  break;
                case 'comboboxWithKeyValuePair':
                  echo '<td class="student_fields">';
                  $optionArrName = $fObj['optionArrName'];
                  $options = $optionObjects->$optionArrName;
                  echo '<select class="form-control field_val" name="' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" id="'.$displayType.'">';
                  echo '<option value="">Select Value</option>';
				  $dat_val = '';
                  foreach ($options as $opt) {
                    if ($opt->value == $stdObj->$vName){
						$dat_val = ucwords($opt->name);
						echo '<option value="' . $opt->value . '" selected>' . ucwords($opt->name) . '</option>';
					}
					
                    else
                      echo '<option value="' . $opt->value . '">' . ucwords($opt->name) . '</option>';
                  }
                  echo '</select>';
				  echo '<input type="hidden" class="input_val" name="old_' . $tName . 'Z' . $cName . 'Z' . $dataType . '[]" value="' . $dat_val . '">';
                  echo '</td>';
                  break;
              }
            }
            echo '</tr>';
          }
          echo '</table>';

        ?>
      </div><!--Panel Body-->
      <div class="card-footer panel_footer_new">
        <center>
          <input type="button" value="Submit" class="btn btn-primary" id="sub_btn_input">
          <a class="btn btn-danger" href="<?php echo site_url('student/student_massupdate'); ?>">Cancel</a>
        </center>
      </div>
    </form>
  </div>
</div>

<div class="modal fade" id="upload_data_event_modal" tabindex="-1" role="dialog"
		aria-labelledby="upload_data_event_modalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width: 52%;margin: auto;margin-top: 8%;border-radius: .75rem;">
				<div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
					<h4 class="modal-title" id="exampleModalLabel">Add Students Data</h4>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
					</button>
				</div>
				<div class="modal-body">
					<form enctype="multipart/form-data" method="post" id="students_data_entry" data-parsley-validate=""
						class="form-horizontal">
						<div class="card-body">
							<div class="form-group">
								<label class="control-label col-md-4">Upload CSV <font color="red">*</font></label>
								<div class="col-sm-6">
									<input type="file" required="" onchange="" name="upload_csv" id="upload_csv"
										class="form-control">
								</div>
								<br>
							</div>
							<center>
								<button type="button" id="sub_event_upload" style="width: 9rem; border-radius: .45rem;"
									class="btn btn-primary" onclick="upload_students_data_entry()">Upload</button>
							</center>
							<br>
							<br>
							<div class="content_csv_file">

							</div>
						</div>
					</form>

				</div>
			</div>
		</div>
	</div>
	<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

$(document).ready(function () {
	$('#sub_btn_input').on('click', function (e) {
    e.preventDefault();
    
    var $form = $('#student_data'); // renamed to $form to avoid conflict
    if (!$form.parsley().validate()) {
        return false;
    }

    $('#sub_btn_input').val('Please wait');
    $('#sub_btn_input').attr('disabled', 'disabled');

    var fields_count = '<?php echo (count($field_objects)-11) * 2 + 7;?>';
    var rawForm = $form[0]; // get the raw DOM form element
    let formData = new FormData(rawForm);

    let formEntries = Array.from(formData.entries());

    const chunkSize = fields_count * 10;
    let chunks = [];
    for (let i = 0; i < formEntries.length; i += chunkSize) {
        chunks.push(formEntries.slice(i, i + chunkSize));
    }

    function sendChunk(chunkIndex) {
        if (chunkIndex < chunks.length) {
            let chunkFormData = new FormData();
            chunks[chunkIndex].forEach(([key, value]) => {
                chunkFormData.append(key, value);
            });

            $.ajax({
                url: '<?php echo site_url('student/student_massupdate/submitMassUpdate') ?>',
                method: 'POST',
                data: chunkFormData,
                contentType: false,
                processData: false,
                success: function (response) {
                    sendChunk(chunkIndex + 1);
                },
                error: function (error) {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!"
                    });
                    $('#sub_btn_input').val('Submit');
                    $('#sub_btn_input').removeAttr('disabled');
                }
            });
        } else {
            Swal.fire({
                icon: "success",
                title: "Updated Successfully",
                showConfirmButton: false,
                timer: 2500
            });
            $('#sub_btn_input').val('Submit');
            $('#sub_btn_input').removeAttr('disabled');
        }
    }

    sendChunk(0);
});
});

  var adm_nos = [];
  var is_adm_duplicate = 0;
  $(document).ready(function () {
    adm_nos = JSON.parse('<?php echo json_encode($admission_numbers) ?>');
    var dob_maxdate = new Date();
    dob_maxdate.setFullYear( dob_maxdate.getFullYear() + 1 );

    var dob_mindate = new Date();
    dob_mindate.setFullYear( dob_mindate.getFullYear() - 60 );

    var doj_maxdate = new Date();
    doj_maxdate.setFullYear( dob_maxdate.getFullYear() + 1 );

    var doj_mindate = new Date();
    doj_mindate.setFullYear( dob_mindate.getFullYear() - 40 );

    $('body').on('focus',".dob_class", function(){
      $(this).datepicker({
        viewMode: 'years',
        format: 'dd-mm-yyyy',
        maxDate: dob_maxdate,
        minDate: dob_mindate,
      });
    });
  });

  var el = document.querySelector('.more');
	var btn = el.querySelector('.more-btn');
	var menu = el.querySelector('.more-menu');
	var visible = false;

	function showMenu(e) {
		e.preventDefault();
		if (!visible) {
			visible = true;
			$('.more-menu').css('display', 'block');
			menu.style.opacity=1;
			el.classList.add('show-more-menu');
			menu.setAttribute('aria-hidden', false);
			// document.addEventListener('mousedown', hideMenu);
		}else{
			visible=false;
			menu.style.opacity=0;
			$('.more-menu').css('display', 'none');
		}
	}

  btn.addEventListener('click', showMenu);

  $(document).on("click", function(event){
    if(event.target.id != 'more-btn'){
      visible = false;
      menu.style.opacity=0;
      $('.more-menu').css('display', 'none');
    }
  });
  
  document.querySelector(".more-menu").addEventListener("click", e => {
        if (e.target.closest(".action_btn")?.dataset) {
          // console.log(e.target.closest(".action_btn").dataset.buttonType);

          const currentActionButton = e.target.closest(".action_btn").dataset.buttonType;
          if (currentActionButton == "download") {
            return createAndDownloadCSVForStudentsDataEntry();
          }

          if (currentActionButton == "upload") {
          }

          if (currentActionButton == "export") {
            return exportToExcel();
          }
        }
});

function upload_students_data_entry() {
		var upload_csv = $('#upload_csv').prop('files')[0];

		var $form = $('#students_data_entry');
		if ($form.parsley().validate()) {
			var form = $('#students_data_entry')[0];
			var formData = new FormData(form);
			formData.append('file[]', upload_csv);

			$.ajax({
				url: '<?php echo site_url('student/student_massupdate/upload_students_data_csv'); ?>',
				type: 'post',
				data: formData,
				// async: false,
				processData: false,
				contentType: false,
				// cache : false,
				success: function (data) {
					const CSVstuDataArray = $.parseJSON(data);

					$("#upload_data_event_modal").trigger("click");
					if (Object.keys(CSVstuDataArray)?.length) {
						$('#upload_csv').val("");
						
						let studataElementArray = document.querySelectorAll(".field_val");
						
						Object.keys(CSVstuDataArray).forEach(student => {
							studataElementArray.forEach(m => {
								const studentName = m.closest("tr").querySelector(".stu_id").innerText;
								// console.log(studentName);
								if (CSVstuDataArray[student].student_id.trim() == studentName.trim()) {

									const studentRow =m.closest("td").closest("tr");

									let allSubjectColumns = studentRow.querySelectorAll(".field_val");
									
									allSubjectColumns.forEach((stuDataEl, i) => {										
										const arr=Object.entries(CSVstuDataArray[student].data[0]);
										arr.forEach((o,i)=>{
											if ((stuDataEl.id === 'Category' || stuDataEl.id === 'Caste') && o[0].toString().trim().toLowerCase() === stuDataEl.id.toString().trim().toLowerCase()) {
												let found = false;
												for (let option of stuDataEl.options) {
													if (option.text.trim().toLowerCase() === o[1].trim().toLowerCase()) { // Case-insensitive match
														stuDataEl.value = option.value;
														found = true;
														break;
													}
												}
											}
											else if(o[0].toString().trim()===stuDataEl.id.toString().trim()){
												if(o[1]=="TBD") return;
												stuDataEl.value = o[1];
											}
										});
									})
								}
							});
						})
					} else {
						Swal.fire({
							icon: "error",
							title: "Oops...",
							text: "Something went wrong!",
							// footer: '<a href="#">Why do I have this issue?</a>'
						});
					}
				}
			});
		}
	}

  const createAndDownloadCSVForStudentsDataEntry = async function () {
      const data = {
        id: 1,
        name: "Geeks",
        profession: "developer"
      }
      const csvdata = csvmaker();
      download(csvdata);
  }

  const download = function (data) {
      const blob = new Blob([data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.setAttribute('href', url)
      a.setAttribute('download', 'download.csv');
      a.click()
  }

  const csvmaker = function () {
      csvRows = [];
      csvRows.push(csvHeaderArr.join(","));
      for(var i=0;i<csvStudentsArr.length;i++){
		var j=0;
		var row = '';
		for(j;j<length;j++){
			row = row+`${csvStudentsArr[i+j]}`+',';
		}
		row = row.substring(0, row.length - 1);
        csvRows.push(row);
		i= i+j-1;
      }
      // csvStudentsArr.forEach((s, i) => {
      //   csvRows.push(`${++i}, ${s}`);
      // })
      
      return csvRows.join('\n')
  }
 
  const csvHeaderArr = [];
  var length = 0;
	const csvHeaders = document.querySelectorAll(".csv_header");
	csvHeaders.forEach(h => {
		length++;
		csvHeaderArr.push(h.innerText);
	});

  const csvStudentsArr = [];
// const csvStudents = document.querySelectorAll(".student_fields");
const csvStudents = document.querySelectorAll(".input_val");

	csvStudents.forEach(s => {
    const arr = [];
	csvStudentsArr.push(s.value);
	});

  function checkDuplicateAdmNo(ele) {
    $(".cls_1").removeClass('cls_2');
    $(ele).addClass('cls_2');
    let old_adm_nos= [];
    $(".old_adm_nos").each(function() {
      if($(this).val() && ! $(this).hasClass('cls_2')) {
        old_adm_nos.push($(this).val().toString());
      }
    });
    var an = ele.value;
    var old = ele.getAttribute('data-old');
    var id = ele.getAttribute('data-helpid');
    if(an != old) {
      if(adm_nos.includes(an) || old_adm_nos.includes(an)) {
        $("#"+id).show().addClass('duplicate');
      } else {
        $("#"+id).hide().removeClass('duplicate');
      }
    } else {
      $("#"+id).hide().removeClass('duplicate');
    }

    let is_duplicate= false;
    $("span").each(function() {
      if($(this).hasClass('duplicate')) {
        is_duplicate= true;
        return false;
      }
    });
    if(is_duplicate) {
      $("#sub_btn_input").prop('disabled', true);
    } else {
      $("#sub_btn_input").prop('disabled', false);
    }
  }
</script>


<style type="text/css">
	button {
		margin: 2px;
	}

	ul.panel-controls>li>a {
		border-radius: 50%;
	}

	.widthadjust {
		width: 500px;
		margin: auto;
	}


	.container {
		position: absolute;
		top: 50%;
		left: 50%;
		margin-right: -50%;
		transform: translate(-50%, -50%);
		text-align: center;
	}

	.more-menu {
		width: 100px;
	}

	/* More Button / Dropdown Menu */

	.more-btn,
	.more-menu-btn {
		background: none;
		border: 0 none;
		line-height: normal;
		overflow: visible;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		width: 100%;
		text-align: left;
		outline: none;
		cursor: pointer;
	}

	.more-dot {
		background-color: #aab8c2;
		margin: 0 auto;
		display: inline-block;
		width: 7px;
		height: 7px;
		margin-right: 1px;
		border-radius: 50%;
		transition: background-color 0.3s;
	}

	.more-menu {
		position: absolute;
		top: 100%;
		z-index: 900;
		float: left;
		padding: 10px 0;
		margin-top: 9px;
		background-color: #fff;
		border: 1px solid #ccd8e0;
		border-radius: 4px;
		box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
		opacity: 0;
		transform: translate(0, 15px) scale(.95);
		transition: transform 0.1s ease-out, opacity 0.1s ease-out;
		/* pointer-events: none; */
	}

	.more-menu-caret {
		position: absolute;
		top: -10px;
		right: 12px;
		width: 18px;
		height: 10px;
		float: right;
		overflow: hidden;
	}

	.more-menu-caret-outer,
	.more-menu-caret-inner {
		position: absolute;
		display: inline-block;
		margin-left: -1px;
		font-size: 0;
		line-height: 1;
	}

	.more-menu {
		right: 0% !important;
		width: 20%;
	}

	.more-menu-caret-outer {
		border-bottom: 10px solid #c1d0da;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		height: auto;
		left: 0;
		top: 0;
		width: auto;
	}

	.more-menu-caret-inner {
		top: 1px;
		left: 1px;
		border-left: 9px solid transparent;
		border-right: 9px solid transparent;
		border-bottom: 9px solid #fff;
	}

	.more-menu-items {
		margin: 0;
		list-style: none;
		padding: 0;
	}

	.more-menu-item {
		display: block;
	}

	.more-menu-btn {
		min-width: 100%;
		color: #66757f;
		cursor: pointer;
		display: block;
		font-size: 13px;
		line-height: 18px;
		padding: 5px 20px;
		position: relative;
		white-space: nowrap;
	}

	.more-menu-item:hover {
		background-color: #489fe5;
	}

	.more-menu-item:hover .more-menu-btn {
		color: #fff;
	}

	.more-btn:hover .more-dot,
	.show-more-menu .more-dot {
		background-color: #516471;
	}

	.show-more-menu .more-menu {
		opacity: 1;
		transform: translate(0, 0) scale(1);
		pointer-events: auto;
	}

	.fa {
		padding: 0 6px;
	}
</style>