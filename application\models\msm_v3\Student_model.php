<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Student_model extends CI_Model{
    private $yearId;
	public function __construct()
	{
		parent::__construct();
	}

    public function get_student_data($acad_year){
		$student_count =  $this->db_readonly->select("
			count(*) as 'total_students', 
			count(if(sa.gender='M',1,NULL)) 'total_boys', 
			count(if(sa.gender='F',1,NULL)) 'total_girls', 
			count(if(sa.gender='',1,NULL)) 'total_unassigned', 
			count(if(sy.is_rte='1',1,NULL)) 'total_rte', 
			count(if(sy.is_rte='2',1,NULL)) 'nonRTE_count',
			count(if(sy.is_rte='3',1,NULL)) 'scholarship_count',
			count(if(sa.has_staff='1',1,NULL)) 'staff_kids',
			sum(case when sy.admission_type= 2 then 1 else 0 end) as new_adm,
			sum(case when sy.admission_type= 1 then 1 else 0 end) as re_adm 
			")
			->from('student_admission sa')
			->where('sa.admission_status','2')
			->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year")
			->where("sy.promotion_status!='JOINED'")
			->where('sy.promotion_status!=','4')
			->where('sy.promotion_status!=','5')
			->get()->row();
						  
		return $student_count;
	}

    public function get_student_non_compliance_data($acad_year) {
		$sql = "SELECT count(*) as count, Month(si.created_on) as month
		FROM snc_items si
		JOIN snc_category sc ON sc.id = si.snc_category_id
		where year(si.created_on) = '20$acad_year'
		group by Month(si.created_on)";
		$result = $this->db_readonly->query($sql)->result();
		return $result;
	}

    public function get_student_observation_data($acad_year) {
		$sql = "SELECT count(*) as count, Month(si.created_on) as month
		FROM sov2_observations si
		where year(si.created_on) = '20$acad_year'
		group by Month(si.created_on)";
		$result = $this->db_readonly->query($sql)->result();
		return $result;
	}

    public function get_student_counselling_data($acad_year) {
		$sql = "SELECT count(*) as count, Month(si.counselling_date) as month
		FROM student_counselling si
		where year(si.counselling_date) = '20$acad_year'
		group by Month(si.counselling_date)";
		$result = $this->db_readonly->query($sql)->result();
		return $result;
	}

    public function get_student_counselling_statuswise_data($acad_year){
		$sql = "select count(sc.id) as count, ccc.color_code_name as name, ccc.color_code as color
				from student_counselling sc
				join counselling_color_code ccc on sc.color_code = ccc.id
				where sc.id in (select student_counselling_id from student_counselling_patients scp 
				JOIN student_admission sa ON sa.id=scp.source_id
				JOIN student_year sy ON sa.id=sy.student_admission_id
				JOIN class c ON sy.class_id=c.id and c.acad_year_id= $acad_year and sy.acad_year_id = $acad_year and scp.source_type != 'staff')  and ccc.status = 1
				and sc.delete_status = 1
				group by sc.color_code";
		return $this->db_readonly->query($sql)->result();
	}

	public function get_student_nationalitywise_data($acad_year){
		$sql = "select count(sa.id) as student_count, CASE WHEN sa.nationality = '-' OR sa.nationality = '0' OR sa.nationality IS NULL OR sa.nationality = '' THEN '-' ELSE sa.nationality END AS nationality
				from student_year sy
				join student_admission sa on sa.id = sy.student_admission_id
				where acad_year_id = '$acad_year'
				and sy.promotion_status!='JOINED'
				and sy.promotion_status!='4'
				and sy.promotion_status!='5'
				and sa.admission_status = '2'
				group by CASE WHEN sa.nationality = '-' OR sa.nationality = '0' OR sa.nationality IS NULL OR sa.nationality = '' THEN '-' ELSE sa.nationality END
				order by count(sa.nationality) desc";

		$result = $this->db_readonly->query($sql)->result();
		return $result;
	}

	public function student_birthday_list($acad_year){
		$this->db_readonly->select("ss.id, sd.gender, c.class_name, ifnull(cs.section_name, '-'), CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
		$this->db_readonly->from('student_admission sd');
		$this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
		// $this->db_readonly->from('student s');
		$this->db_readonly->where('not isnull(dob)');
		$this->db_readonly->where('admission_status', '2'); // Approved 2
		$this->db_readonly->where('ss.acad_year_id', $acad_year);
		$this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") BETWEEN DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 2 DAY),"%m-%d") AND DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY),"%m-%d")');
		$this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
		$this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id and cs.is_placeholder = 0', 'left');
		$this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
		$this->db_readonly->order_by('cs.section_name');
		$this->db_readonly->order_by('sd.first_name');
		$this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
		$student_just_celebrated = $this->db_readonly->get()->result();

		$this->db_readonly->select("ss.id, sd.gender, c.class_name, ifnull(cs.section_name, '-'), CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
		$this->db_readonly->from('student_admission sd');
		$this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
		// $this->db_readonly->from('student s');
		$this->db_readonly->where('not isnull(dob)');
		$this->db_readonly->where('admission_status', '2'); // Approved 2
		$this->db_readonly->where('ss.acad_year_id', $acad_year);
		$this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") = DATE_FORMAT(CURRENT_DATE,"%m-%d")');
		$this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
		$this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id and cs.is_placeholder = 0', 'left');
		$this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
		$this->db_readonly->order_by('cs.section_name');
		$this->db_readonly->order_by('sd.first_name');
		$this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
		$student_today_celebrated = $this->db_readonly->get()->result();

		$this->db_readonly->select("ss.id, c.class_name, ifnull(cs.section_name, '-'), CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
		$this->db_readonly->from('student_admission sd');
		$this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
		// $this->db_readonly->from('student s');
		$this->db_readonly->where('admission_status', '2'); // Approved 2
		$this->db_readonly->where('ss.acad_year_id', $acad_year);
		$this->db_readonly->where('not isnull(dob)');
		$this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")=date_format(CURRENT_DATE,"%d-%m")');
		$this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
		$this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id and cs.is_placeholder = 0', 'left');
		$this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
		$this->db_readonly->order_by('cs.section_name');
		$this->db_readonly->order_by('sd.first_name');
		$this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
		$student_tomorrow_celebrating = $this->db_readonly->get()->result();

		$this->db_readonly->select("ss.id, c.class_name, ifnull(cs.section_name, '-'), CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name, date_format(sd.dob,'%d-%m') as dob, date_format(sd.dob, '%M %d') as dobDisplay");
		$this->db_readonly->from('student_admission sd');
		$this->db_readonly->join('student_year ss', 'sd.id=ss.student_admission_id and ss.promotion_status!=4 and ss.promotion_status!=5');
		// $this->db_readonly->from('student s');
		$this->db_readonly->where('not isnull(dob)');
		$this->db_readonly->where('DATE_FORMAT(sd.dob, "%m") = DATE_FORMAT(CURRENT_DATE, "%m")');
		$this->db_readonly->where('DATE_FORMAT(dob,"%m-%d") > DATE_FORMAT(CURRENT_DATE,"%m-%d")');
		$this->db_readonly->where('admission_status', '2'); // Approved 2
		$this->db_readonly->where('ss.acad_year_id', $acad_year);
		$this->db_readonly->where('date_format(DATE_ADD(dob, INTERVAL -1 DAY),"%d-%m")!=date_format(CURRENT_DATE,"%d-%m")');
		$this->db_readonly->join('class c', 'c.id=ss.class_id', 'left');
		$this->db_readonly->join('class_section cs', 'ss.class_section_id=cs.id and cs.is_placeholder = 0', 'left');
		$this->db_readonly->order_by('MONTH(dob),DAYOFMONTH(dob)');
		$this->db_readonly->order_by('LENGTH(c.class_name), c.class_name');
		$this->db_readonly->order_by('cs.section_name');
		$this->db_readonly->order_by('sd.first_name');
		$this->db_readonly->order_by('DATE_FORMAT(sd.dob, "%m %d")');
		// $this->db_readonly->limit(50);
		$student_upcoming_celebrating = $this->db_readonly->get()->result();

		return [
			'student_just_celebrated' => $student_just_celebrated,
			'student_today_celebrated' => $student_today_celebrated,
			'student_tomorrow_celebrating' => $student_tomorrow_celebrating,
			'student_upcoming_celebrating' => $student_upcoming_celebrating
		];
	}
}
?>