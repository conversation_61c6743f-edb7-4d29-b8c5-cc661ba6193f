<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('communication_dashboard');?>">Communication</a></li>
    <li><a href="<?php echo site_url('communication/texting/report');?>">Texting Report</a></li>
    <li>Status Report</li>
</ul>
<hr>
<?php 
    date_default_timezone_set("Asia/Kolkata");
?>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-4 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('communication/texting/report'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Status Report
                    </h3>
                </div>
                <div class="col-md-8">
                    <a onclick="refreshStatus()" id="initiate_status_refresh" class="btn btn-primary ml-3 pull-right">Initiate Status Refresh</a>
                    <button id="stu_print" style="margin-left: 2px;" class="btn  btn-danger pull-right" onClick="printProfile();">
                        Print
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body pt-1 overflow-auto" id="printArea">
            <div class="col-md-12">
                <p><strong>Message: </strong><?php echo $master_data->message; ?></p>
                <table class="table table-bordered">
                    <tr>
                        <td style="width:33%"><strong>Date:
                            </strong><?php echo date('d-m-Y h:i a', strtotime($master_data->sent_on)); ?></td>
                        <td style="width:33%"><strong>Sent By:
                            </strong><?php echo ($master_data->sentBy == ' ')?'Super Admin':$master_data->sentBy; ?>
                        </td>
                        <td><strong>Mode: </strong><?php echo ucwords(str_replace("_", " & ", $master_data->mode)); ?>
                        </td>
                        <td colspan="3"><strong>Sent To: </strong><?php echo $master_data->reciever; ?></td>
                    </tr>
                    <!-- <tr>
                    </tr> -->
                </table>
            </div>
            <div class="col-md-12">
                <table class="table datatable" id="report-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Mobile Number</th>
                            <th>Mode</th>
                            <th>Status</th>
                            <th>Remarks</th>
                            <th>Delivered On</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i=1;
                        foreach ($recievers as $key => $reciever) {
                            $status = $reciever->status;
                            $mode = 'Notification';
                            if($reciever->mode == 2) {
                                $mode = 'Sms';
                                $status = isset($status_codes[$reciever->status]) ? $status_codes[$reciever->status] : $reciever->status;
                            }
                            echo '<tr>';
                            echo '<td>'.$i++.'</td>';
                            echo '<td>'.$reciever->name.'</td>';
                            echo '<td>'.$reciever->mobile_no.'</td>';
                            echo '<td>'.$mode.'</td>';
                            echo '<td>'.$status.'</td>';
                            echo '<td>'.$reciever->remarks.'</td>';
                            echo '<td>'.$reciever->delivered_date.'</td>';
                            echo '</tr>';
                        }?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function refreshStatus() {
        $('#initiate_status_refresh').attr('disabled', true);
        $('#initiate_status_refresh').html('Please Wait...');
        $.ajax({
            url: '<?php echo site_url('communication/texting/refreshStatus'); ?>',
            type: "post",
            success: function(data) {
                $('#initiate_status_refresh').removeAttr('disabled');
                $('#initiate_status_refresh').html('Initiate Status Refresh');
                $(function() {
                    new PNotify({
                        title: 'Success',
                        text: 'Refreshing data initiated. Check back in few minutes',
                        type: 'success',
                    });
                });
            }
        });
    }

    function printProfile() {
        $('#report-table').DataTable().destroy();
        var restorepage = document.body.innerHTML;
        var printcontent = document.getElementById('printArea').innerHTML;
        document.body.innerHTML = printcontent;
        window.print();
        document.body.innerHTML = restorepage;
        location.reload();
    }
</script>