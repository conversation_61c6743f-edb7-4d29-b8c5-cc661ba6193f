<!-- START BREADCRUMB -->
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('Master_dashboard');?>">Master Dashboard</a></li>
    <li><a href="<?php echo site_url('Master_dashboard/school_management');?>">School Management</a></li>
    <li class="active">Student</li>
</ul>

<!-- PAGE CONTENT WRAPPER -->
<div class="col-md-12">
<div class="page-content-wrap">
	<div class="row" style="margin: 0px;">
	    
	         <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('csv_upload/student_csv_submit');?>" data-parsley-validate="" class="form-horizontal" style="width: 100%">
	        <div class="panel panel-default new-panel-style_3">
	            <div class="panel-heading new-panel-heading">
	                <h3 class="panel-title"><strong>Student CSV</h3>
	                <a type="button" class="btn btn-primary pull-right" href="<?php echo site_url('csv_upload/downloadCsvFormat');?>" >Download CSV Format</a>
	            </div>
	            <div class="panel-body">
	                 <div class="row">
	                    <div class="col-md-12">
	                    	<div class="form-group">
								<label class="control-label col-md-3" for="db_table">Database Table</label>
								<div class="col-md-6">
									<input type="text" class="form-control" id="db_table" name="db_table" required="">
								</div>
							</div> 
	  		               	<div class="form-group">
			                    <label class="control-label col-md-3" for="class"> Upload CSV</label>
			                    <div class="col-md-6"> 
			                        <input type="file" class="form-control" id="csv_file" name="csv_file" required="">
			                    </div>
		                    </div>                   
	             		</div>
	                 </div>
	            </div>
	            <div class="panel-footer">
	               <center><button class="btn btn-primary">Submit</button></center>        
	            </div>
	        </div>
	        </form>
	    </div>
	</div>                    
</div>

<div class="col-md-12">
<div class="page-content-wrap">
	<div class="row" style="margin: 0px;">
	    
	         <!-- <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php //echo site_url('student/student_controller/importStudentData');?>" data-parsley-validate="" class="form-horizontal" > -->
	         <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('student/import/Student_import/importStudentData');?>" data-parsley-validate="" class="form-horizontal" style="width: 100%">
	        <div class="panel panel-default new-panel-style_3">
	            <div class="panel-heading new-panel-heading">
	                <h3 class="panel-title"><strong>Student CSV</h3>
	                	<!-- <a type="button" class="btn btn-primary pull-right" href="<?php // echo site_url('csv_upload/downloadDb');?>" >Download DB</a> -->
	            </div>
	            <div class="panel-body">
	            	<div class="col-md-12">
						<div class="form-group">
							<label class="control-label col-md-3" for="db_table">Database Table</label>
							<div class="col-md-6">
								<input type="text" class="form-control" id="db_table" name="db_table" required="">
							</div>
						</div>                   
					
						<div class="form-group">
							<label class="control-label col-md-3" for="class">ID From</label>
							<div class="col-md-6"> 
								<input type="text" class="form-control" id="from_id" name="from_id" required="">
							</div>
						</div>  
						 
						<div class="form-group">
							<label class="control-label col-md-3" for="class">ID To</label>
							<div class="col-md-6"> 
								<input type="text" class="form-control" id="to_id" name="to_id" required="">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-3" for="class">Check Uniquness By</label>
							<div class="col-md-6"> 
								<select class="form-control" id="unique_by" name="unique_by">
									<option value="admission_no">Admission number</option>
								</select>
							</div>
						</div>
					</div>

				</div>
	            <div class="panel-footer new-footer">
	               <center><button class="btn btn-primary">Submit</button></center>        
	            </div>
	        </div>
	        </form>
			</div>
	</div>                    
</div>
<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('Master_dashboard/school_management');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>
