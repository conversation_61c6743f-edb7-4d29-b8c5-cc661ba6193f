<?php $this->load->view('msm_v3/styles/_landing_css'); ?>
<div class = "card landing_card">
    <div class="container-fluid py-2 pt-3">
        <div class="row mx-0"><?php $this->load->view('msm_v3/desktop/_top_bar'); ?></div>
    </div>
    <div class="container-fluid py-2 pt-3 custom-scrollbar" style="overflow: auto; overflow-x: hidden; padding-left: 1.5rem">

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <!-- <div class="graph_top">
                    <div class="row graph_top_card_row">
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Leads
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_leads">100</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Applications
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_applications">100</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="graph_top_card card mb-3">
                                <div class="card-header graph_top_card_header">
                                    Total Converted
                                </div>
                                <div class="card-body">
                                    <span id="pipeline_card_data_converted">100</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-2" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Student Count</h4>
                            </div>
                            <!-- <div class="col-md-6" id="student_count_legend_div"></div> -->
                            <!-- <div class="col-md-3" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_student_view('column_chart', 'student_management_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_student_view('table', 'student_management_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div> -->
                        </div>
                        <div class="animate__animated animate__pulse" id="student_management_pie_card" style="display: none; margin: 1rem;">
                            <div id="student_count_statistics">
                                <center>Loading Student Count Graph</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="student_management_table_card" style="display: block; margin: 1rem;">
                            <table class="table-sm table-bordered mb-0" width="97%" style="margin: 1.5rem;">
                                <thead style="color: #0F256E">
                                    <tr>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">#</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Institution</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Boys</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Girls</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Un-Assigned</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">RTE</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Non-RTE</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Scholarship Students</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Staff Kids</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Fee Paying Students</th>
                                        <th class="text-uppercase text-center align-middle text-xxs font-weight-bolder opacity-7" style="color: #0F256E;">Total Students</th>
                                    </tr>
                                </thead>
                                <tbody id="student_management_table">
                                    <tr>
                                        <td colspan="11" class="text-left font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <button class="btn vertical-text mx-1" style="border: 1px solid #00CC83">Summary <</button>
            </div> -->
        </div>

        <div class="d-flex">
            <div class="card mx-0 graph_cards">
                <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                    <div style="min-height: 60vh; magin-top: 5vh;">
                        <div style="display: flex; justify-content: space-between;">
                            <div class="col-md-6" style="margin-left: 1.5rem">
                                <h4 style="color: #0F256E">Student Non-Compliance Statistics</h4>
                            </div>
                            <!-- <div class="col-md-3" id="student_nc_legend_div"></div> -->
                            <div class="col-md-2" style="display: flex;justify-content: end;margin-right: 1%;">
                                <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                    <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_student_view('column_chart', 'student_nc_statistics_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                    </button>
                                    <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_student_view('table', 'student_nc_statistics_card', this)">
                                        <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="student_nc_statistics_pie_card" style="display: block; margin: 1rem;">
                            <div id="student_nc_statistics_graph">
                                <center>Loading Student Non-Compliance Statistics Graph</center>
                            </div>
                        </div>
                        <div class="animate__animated animate__pulse" id="student_nc_statistics_table_card" style="display: none; margin: 1rem;">
                            <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                                <thead style="color: #0F256E">
                                <tr>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">Month</th>
                                    <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                                </tr>
                                </thead>
                                <tbody id="student_nc_statistics_table">
                                <tr>
                                    <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <button class="btn vertical-text mx-1" style="border: 1px solid #00CC83">Summary <</button>
            </div> -->
        </div>
    
        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-6" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Observation Statistics</h4>
                        </div>
                        <!-- <div class="col-md-3" id="student_observation_legend_div"></div> -->
                        <div class="col-md-2" style="display: flex;justify-content: end;margin-right: 1%;">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_student_view('column_chart', 'student_observation_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_student_view('table', 'student_observation_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="student_observation_pie_card" style="display: block; margin: 1rem;">
                        <div id="student_observation_statistics_graph">
                            <center>Loading Monthwise Fee Collection Statistics Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="student_observation_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Month</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="student_observation_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-6" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Counselling Statistics</h4>
                        </div>
                        <!-- <div class="col-md-3" id="student_counselling_legend_div"></div> -->
                        <div class="col-md-2" style="display: flex;justify-content: end;margin-right: 1%;">
                            <div class="btn-group btn-group-sm" style="display: flex; justify-content: end;">
                                <button type="button" class="btn btn-outline highlight" style="border-color: #00CC83" onclick="flip_student_view('column_chart', 'student_counselling_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/graph_green_svg.svg'); ?>">
                                </button>
                                <button type="button" class="btn btn-outline" style="border-color: #00CC83" onclick="flip_student_view('table', 'student_counselling_card', this)">
                                    <img src="<?php echo base_url('assets/msm_v3/img/table_green_svg.svg'); ?>">
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="student_counselling_pie_card" style="display: block; margin: 1rem;">
                        <div id="student_counselling_graph">
                            <center>Loading Student Counselling Statistics Graph</center>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse" id="student_counselling_table_card" style="display: none; margin: 1rem;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                            <tr>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Month</th>
                                <th class="font-weight-bolder text-m text-uppercase opacity-7">Count</th>
                            </tr>
                            </thead>
                            <tbody id="student_counselling_table">
                            <tr>
                                <td colspan="2" class="text-left  font-weight-bolder text-m text-uppercase opacity-7">Loading...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-6" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Counselling Statuswise Statistics</h4>
                        </div>
                        <!-- <div class="col-md-6" id="student_counselling_statuswise_statistics_legend_div"></div> -->
                        <div class="col-md-2 mx-3">
                            <select class="form-select" id="dropdownSchool_student" aria-label="Select Schools">
                            </select>
                        </div>
                    </div>
                    
                    <div class="animate__animated animate__pulse" id="student_counselling_statuswise_pie_card" style="display: block; margin: 1rem;">
                        <div id="student_counselling_statuswise_statistics_graph">
                        <center>Loading Student Counselling Statuswise Statistics Graph</center>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-6" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Nationality Statistics</h4>
                        </div>
                        <!-- <div class="col-md-2 mx-3">
                            <select class="form-select" id="dropdownSchool_student_nationality" aria-label="Select Schools">
                            </select>
                        </div> -->
                    </div>
                    
                    <div class="animate__animated animate__pulse custom-scrollbar" id="student_nationalitywise_table_card" style="display: block; margin: 1rem; overflow-y: auto; height: 50vh;">
                        <div id="student_nationalitywise_table" class="table-wrapper"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mx-0 graph_cards">
            <!-- <div class="graph_top">
                <div class="row graph_top_card_row">
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Leads
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Admissions
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="graph_top_card card mb-3">
                            <div class="card-header graph_top_card_header">
                                Total Converted
                            </div>
                            <div class="card-body">
                                100
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="col-md-12 mt-3" style="position: relative; overflow-y: hidden; overflow-x: hidden;"> 
                <div style="min-height: 60vh; magin-top: 5vh;">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="col-md-3" style="margin-left: 1.5rem">
                            <h4 style="color: #0F256E">Student Birthday</h4>
                        </div>
                        <div class="col-md-4" style="display: flex;justify-content: end;margin-right: 1%;">
                            <select class="form-select" id="dropdownSchool_student_birthday" aria-label="Select Schools" style="width: 50%; margin-right: 5%">
                            </select>
                        </div>
                    </div>
                    <div class="animate__animated animate__pulse custom-scrollbar" id="student_birthday_pie_card" style="display: block; margin: 1rem; max-height: 350px; overflow-y: auto; padding: 1rem;">
                    </div>
                    <div class="card-body animate__animated animate__pulse" id="student_birthday_table_card" style="display: none; margin: 1rem; min-height: 350px; overflow-y: auto;">
                        <table class="table-sm table-bordered mb-0" width="90%" style="margin: 1.5rem;">
                            <thead style="color: #0F256E">
                                <tr>
                                    <th class=" font-weight-bolder  opacity-7" >Month</th>
                                    <th class=" font-weight-bolder  opacity-7" >Count</th>
                                </tr>
                            </thead>
                            <tbody id="student_birthday_table">
                                <tr>
                                    <td colspan="4" class="text-left  font-weight-bolder  text-uppercase opacity-7">Loading...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?php $this->load->view('msm_v3/styles/_student_css'); ?>
<?php $this->load->view('msm_v3/scripts/_student_script'); ?>