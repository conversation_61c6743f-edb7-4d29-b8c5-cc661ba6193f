<script type="text/javascript">
var gYears = '<?= json_encode($yearinfo)?>';
 var gYear = $.parseJSON(gYears);
 // console.log(gYear);
 // var yearLength = gYear.length;
 var yearLength = Object.keys(gYear).length;
 // alert(yearLength);
$(document).ready(function () {

    var dob = '<?php echo date('Y-m-d',strtotime($final_preview->dob)) ;?>';
    var dob_date = '<?php echo $final_preview->dob ?>'
    if(dob != '' && dob_date != '00-00-0000'){
        var age = getAge(dob);
        $('#age_cal').html(age);
    }
    var dob_maxdate = new Date();
    dob_maxdate.setFullYear( dob_maxdate.getFullYear() + 0 );

    var dob_mindate = new Date();
    dob_mindate.setFullYear( dob_mindate.getFullYear() - 60 );

    var doj_maxdate = new Date();
    doj_maxdate.setFullYear( dob_maxdate.getFullYear() + 1 );

    var doj_mindate = new Date();
    doj_mindate.setFullYear( dob_mindate.getFullYear() - 40 );

    $('.datepick').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    }).on('dp.change', function (e) {
        var d = new Date(e.date);
        var age = getAge(e.date);
        $('#age_cal').html(age);

        getAge1(d);
        
        // get_age_calculations(e.date);
    });

    $('.datepick1').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
    });

    $('.f_datepick').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    }).on('dp.change', function (e) {
        var d = new Date(e.date);
        var age = getAge(e.date);
        $('#age_cal_f').html(age);
    });

    $('.m_datepick').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    }).on('dp.change', function (e) {
        var d = new Date(e.date);
        var age = getAge(e.date);
        $('#age_cal_m').html(age);
    });

function getAge(dateVal) {
    var dob_as_on = '<?php echo $this->settings->getSetting('application_form_dob_criteria') ?>';
    if(dob_as_on){
        var onSelectDate = new Date(dateVal),
        today = new Date(dob_as_on),
        ageInMilliseconds = new Date(today - onSelectDate),
        years = ageInMilliseconds / (24 * 60 * 60 * 1000 * 365.25 ),
        months = 12 * (years % 1),
        days = Math.floor(30 * (months % 1));
        return Math.floor(years) + ' years ' + Math.floor(months) + ' months ' + days + ' days as on ' + moment(dob_as_on).format('MMM DD, YYYY');
    }else{
        return '';
    }
}

function getAge1(dob) {
    var selectedClass = $("#class").val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
        type: 'post',
        data: {'selectedClass':selectedClass, 'dob':dob},
        success: function(data) {
            console.log(data);
            // return false;
            var resData = data.trim();
            if (resData!=0) {
               $('#age_cal_dob_error').html('');
            }else{
                $('#age_cal_dob_error').html('The kid does not meet the age-criteria for the applied Grade.');
                return false;
            }
        }
    });
}

function getParentAge(){

}



function readURL(input,photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
            if(photo_type == 'student'){
                $('#previewing').attr('src', e.target.result);
            }else if(photo_type == 'family'){
                $('#family_previewing').attr('src', e.target.result);
            }else if(photo_type == 'stud_sign'){
                $('#stud_sig_previewing').attr('src', e.target.result);
            }
        }

        reader.readAsDataURL(input.files[0]);
    }
}

// $('#fileupload').change(function(){
//     var src = $(this).val();
//     // var isFileOk = validatePhoto(this.files[0])
    
//     if(src && validatePhoto(this.files[0], 'fileupload')){
//         $("#fileuploadError").html("");
//         readURL(this,'student');
//     } else{
//         this.value = null;
//     }
// });

$('#family_fileupload').change(function(){
    var src = $(this).val();
    // var isFileOk = validatePhoto(this.files[0])
    if(src && validate_photo_size(this.files[0],'family_fileupload','family_fileuploadError')){
        $("#family_fileuploadError").html("");
        readURL(this,'family');
    } else{
        this.value = null;
    }
});

$('#stud_sign_fileupload').change(function(){
    var src = $(this).val();
    if(src && validate_photo_size(this.files[0],'stud_sign_fileupload','stud_sign_fileuploadError')){
        $("#stud_sign_fileuploadError").html("");
        readURL(this,'stud_sign');
    } else{
        this.value = null;
    }
});

$('#documentId').change(function(){
    var src = $(this).val();
    if(src && validate_document(this.files[0], 'documentId')){
        $("#error1").html("");
        readURL(this);
    } else{
        $("#error1").html("Allowed file size exceeded. (Max. 2 Mb) / Allowed file types are jpeg, jpg and pdf");
        this.value = null;
    }
});

var other = '<?php echo $final_preview->nationality ?>';
if (other == 'Other') {
    $('#nationality_other').show();
  }else{
    $('#nationality_other').hide();

  }

var other = '<?php if (!empty($admission_prev_schools->board_other))  echo $admission_prev_schools->board_other ?>';
if (other == 'Other') {
    $('#board_other').show();
}else{
    $('#board_other').hide();

}

var other = '<?php echo $final_preview->religion ?>';
if (other == 'Other') {
    $('#religion_other').show();
  }else{
    $('#religion_other').hide();
  }

var other = '<?php echo $final_preview->std_mother_tongue ?>';
if (other == 'Other') {
    $('#mother_tongue_name').show();
  }else{
    $('#mother_tongue_name').hide();
  }


var other = '<?php echo $final_preview->father_mother_tongue ?>';
if (other == 'Other') {
    $('#f_mother_tongue_name').show();
  }else{
    $('#f_mother_tongue_name').hide();
  }

var other = '<?php echo $final_preview->mother_mother_tongue ?>';
if (other == 'Other') {
    $('#m_mother_tongue_name').show();
  }else{
    $('#m_mother_tongue_name').hide();
  }


});



function validatePhoto(file,errorId){
    if (file.size > 2097152 || file.fileSize > 2097152)
    {
       $("#"+errorId+"Error").html("Allowed file size exceeded. (Max. 2MB)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#"+errorId+"Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}


function validate_document(file,error){
    if (file.size > 2097152 || file.fileSize > 2097152){
       $("#"+error+"Error").html("Allowed file size exceeded. (Max. 2 Mb)")
       return false;
    }
    if(file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'application/pdf') {
        $("#"+error+"Error").html("Allowed file types are jpeg, jpg and pdf");
        return false;
    }
    return true;
}

function validate_documents_config_based(files){
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
    var file_size = parseFloat(files.size/1024/1024);
    var max_file_size = parseInt(max_size_string);
    if(file_size > max_file_size) {
        return false;
    } else {
        return true;
    }
}


function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}


$(document).ready(function () {
    $('.next').on('click', function () {
        var current = $(this).data('currentBlock'),
        next = $(this).data('nextBlock');
    // only validate going forward. If current group is invalid, do not go further
    // .parsley().validate() returns validation result AND show errors
    if (next > current)
        if (false === $('#std-form').parsley().validate('block' + current))
            return;

    // validation was ok. We can go on next step.
    $('.block' + current)
    .removeClass('show')
    .addClass('hidden');

    $('.block' + next)
    .removeClass('hidden')
    .addClass('show');
    });

    $('.nav-tabs > li a[title]').tooltip();
    
    //Wizard
    $('a[data-toggle="tab"]').on('show.bs.tab', function (e) {

        var $target = $(e.target);
    
        if ($target.parent().hasClass('disabled')) {
            return false;
        }
    });

    $(".next-step").click(function (e) {
        var $active = $('.wizard1 .nav-tabs li.active');
        $active.next().removeClass('disabled');
        nextTab($active);

    });
    $(".prev-step").click(function (e) {
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder = '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        var medical_form = '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var af_id = '<?php echo $final_preview->id ?>';
        if(gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1 && e.target.id == 'medical_prev_button'){
            $.ajax({
                url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
                type: 'post',
                data:{'af_id':af_id},
                success: function(data) {
                    if(data == 0){
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $($active).prev().prev().find('a[data-toggle="tab"]').click();
                        return 1;
                    }else{
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $($active).prev().find('a[data-toggle="tab"]').click();
                    }
                }
            });
        }else if(medical_form == 0 && gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1 && e.target.id == 'doc_prev_button'){
            $.ajax({
                url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
                type: 'post',
                data:{'af_id':af_id},
                success: function(data) {
                    if(data == 0){
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $($active).prev().prev().find('a[data-toggle="tab"]').click();
                        return 1;
                    }else{
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $($active).prev().find('a[data-toggle="tab"]').click();
                    }
                }
            });
        }else{
            var $active = $('.wizard1 .nav-tabs li.active');
            prevTab($active);
        }
    });

    $(".save-step1").click(function (e){
        var inputDob  = $("input[name='student_dob']").val();
        var inputClass =$("#class").val();
        var $form = $('#std-form');
        if ($form.parsley().validate()){
            var form = $('#std-form')[0];
            var formData = new FormData(form);
           check_dob_grade_wise(inputDob, inputClass, formData);
        }
    });

   
    $('#class').on('change',function(){
        var selectedClass = $("#class").val();
        var dob  = $("input[name='student_dob']").val();
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
            type: 'post',
            data: {'selectedClass':selectedClass, 'dob':dob},
            success: function(data) {
                console.log(data);
                // return false;
                var resData = data.trim();
                if (resData!=0) {
                   $('#age_cal_dob_error').html('');
                }else{
                    $('#age_cal_dob_error').html('The kid does not meet the age-criteria for the applied Grade.');
                    return false;
                }
            }
        });
    });

    function check_dob_grade_wise(dob, selectedClass, formData) {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
            type: 'post',
            data: {'selectedClass':selectedClass, 'dob':dob,},
            success: function(data) {
                // console.log(data);
                // return false;
                var resData = data.trim();
                if (resData!=0) {
                   $('#dob_error').html('');
                   update_student_after_dob_check(formData,selectedClass);
                }else{
                    $('#dob_error').html('The kid does not meet the age-criteria for the applied Grade.');
                    return false;
                }
            }
        });
    }

    function update_student_after_dob_check(formData,selectedClass) {
        $('.save-step1').val('Please wait ...').attr('disabled','disabled');
        $.ajax({
            url: '<?php echo site_url('admission_controller/updateStudentDetails'); ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
            console.log(data);
            $('.save-step1').val('Save & Continue').removeAttr('disabled');
                if(data !='') {
                    // $('#adId').val(data);
                    // $('#afid').val(data);
                    var $active = $('.wizard1 .nav-tabs li.active');
                    $active.next().removeClass('disabled');
                    nextTab($active);
                    get_language_selection(selectedClass);
                    get_subject_details(selectedClass);
                    return 1;
                } else {
                    alert("Something went wrong in Student data, try again.");
                    return 0;
                }
            }
        });
    }

    function get_language_selection(selectedClass){
        var admsettingId = '<?php echo $admission_setting_id  ?>';
        $.ajax({
        url: '<?php echo site_url('admission_controller/get_language_class_wise_data'); ?>',
        type: 'post',
        data: {'selectedClass':selectedClass,'admsettingId':admsettingId},
        success: function(data) {
        var res_data = JSON.parse(data);
        if(res_data != ''){
            $('#language_selection').html(construct_language_selection(res_data));
        }
        }
    });
        
    }

    function get_subject_details(selectedClass){
        var admsettingId = '<?php echo $admission_setting_id  ?>';
        $.ajax({
        url: '<?php echo site_url('admission_controller/get_subject_details'); ?>',
        type: 'post',
        data: {'selectedClass':selectedClass,'admsettingId':admsettingId},
        success: function(data) {
        var res_data = JSON.parse(data);
        if(res_data == 1){
           $('.subject_btn').show();
        }else{
            $('.subject_btn').hide();
        }
        }
    });
    }

    function construct_language_selection(res_data){
        var langChoices = {
            'lang_1_choice': '<?php echo $final_preview->lang_1_choice; ?>',
            'lang_2_choice': '<?php echo $final_preview->lang_2_choice; ?>',
            'lang_3_choice': '<?php echo $final_preview->lang_3_choice; ?>'
        };

        var html = '';
        html += `<div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Language selection</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-5" id="">`;
                        for(i in res_data){
                            html += '<div class="form-group">';
                            html += `<label class="control-label col-md-4">${i}</label>`;
                            html += `<div class="col-md-8">`;
                            for(j in res_data[i]){
                                console.log(res_data[i]['required']);
                                if(j != 'required'){
                                    html += `<select class="form-control" name="${j}" ${res_data[i]['required']}>`;
                                    html += '<option value="">Select Language</option>';
                                    for(k in res_data[i][j]){
                                        var selected = '';
                                        if (k == langChoices[j]) {
                                            selected = 'selected';
                                        }
                                        html += `<option ${selected} value="${k}">${res_data[i][j][k]}</option>`;
                                    }
                                    html += `</select>`;
                                }
                            }
                            html += `</div>`;
                            html += `</div>`;
                        }
                        html += `</div>
                            </div>
                        </div>
                    </div>`;
        return html;
    }

  $(".save-draft1").click(function (e){
        // var formData =$("#std-form").serialize();
        var auId = $('#auId').val();
        var $form = $('#std-form');
        if ($form.parsley().validate()){
            var form = $('#std-form')[0];
            var formData = new FormData(form);
            $('.save-draft1').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/updateStudentDetails'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                // cache : false,
                success: function(data) {
                // console.log(data);
                $('.save-draft1').val('Save & Continue').removeAttr('disabled');
                    if(data !='') {
                        url = '<?php echo site_url('admissions/home') ?>';
                        $('#std-form').attr('action',url);
                        $('#std-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        alert("Something went wrong in Student data, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    $(".save-step2").click(function (e){
        //var formData =$("#health-form").serialize();
        var af_id = '<?php echo $final_preview->id ?>';
        var f_mobile_no = $("#f_mobile_no").val();
        var m_mobile_no = $("#m_mobile_no").val();
        if (f_mobile_no != undefined && m_mobile_no != undefined  && f_mobile_no != '' && m_mobile_no != '' && f_mobile_no === m_mobile_no) {
            alert("Father's and Mother's Mobile Numbers should not be the same.");
            return false;
        }
        var father_email_id = $("#f_email").val();
        var mother_email_id = $("#m_email").val();
        if (father_email_id != undefined && mother_email_id != undefined && father_email_id != '' && mother_email_id != '' && father_email_id === mother_email_id) {
            alert("Father's and Mother's Email Id should not be the same.");
            return false;
        }
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var medical_form = '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder = '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        var $form = $('#parent-form');
        if ($form.parsley().validate()){
            var form = $('#parent-form')[0];
            var formData = new FormData(form);
            $('.save-step2').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_parent_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step2').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(gaurdian_form == 0 && document_form == 1 && medical_form == 0){
                            url = '<?php echo site_url('admissions/preview') ?>';
                            $('#parent-form').attr('action',url);
                            $('#parent-form').submit();
                        }
                        else if(gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1){
                            check_student_boarding_show_guardian_details();
                        }
                        else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            get_parent_data();
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    $('#boarding').on('change', function() {
        check_student_boarding();
    });

    $( document ).ready(function() {
        check_student_boarding();
    });

    function check_student_boarding(){
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder = '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        if(gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1){
            var boarding = $('#boarding option:selected').text();
            if(boarding == 'Day Scholar'){
                $('#guardian_details').hide();
                // $('#step3').hide();
            }else{
                $('#guardian_details').show();
                // $('#step3').show();
            }
        }
    }


    function check_student_boarding_show_guardian_details(){
        var af_id = '<?php echo $final_preview->id ?>';
        $.ajax({
                url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
                type: 'post',
                data:{'af_id':af_id},
                success: function(data) {
                    if(data == 0){
                        $('#guardian_details').hide();
                        // $('#step3').hide();
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $active.next().removeClass('disabled');
                        nextTab($active);
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $active.next().removeClass('disabled');
                        nextTab($active);
                        get_parent_data();
                        return 1;
                    }else{
                        $('#guardian_details').show();
                        // $('#step3').show();
                        var $active = $('.wizard1 .nav-tabs li.active');
                        $active.next().removeClass('disabled');
                        nextTab($active);
                        get_parent_data();
                        return 1;
                    }
                }
            });
    }

    $(".save-step5").click(function (e){
        // $(".remove_input").remove();
        // $("#vac_date_modal").val('01-01-0001');
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var $form = $('#medical-form');
        if ($form.parsley().validate()){
            var form = $('#medical-form')[0];
            var formData = new FormData(form);
            $('.save-step5').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_medical_form_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step5').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(document_form == 1){
                            url = '<?php echo site_url('admissions/preview') ?>';
                            $('#medical-form').attr('action',url);
                            $('#medical-form').submit();
                        }else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        } else {
            $("#vaccination_table_in_admission ul").each(function() {
                var html= $(this).children('li').html();
                $(this).hide();
                var num= $(this).prev('td').data("num");
                $(this).prev('td').children(`.span_class_${num}`).remove();
                $(this).prev('td').append(`<span class="span_class_${num}">${html}</span>`);
            });
        }
    });
    
    function get_parent_data(){
        var insert_id = '<?php echo $insert_id ?>';

        var documenttypes = '<?php echo json_encode($documents) ?>';
        var docsArry = $.parseJSON(documenttypes);

        var documents_required_fields = '<?php echo json_encode($documents_required_fields) ?>';
        var required_fields_arr = $.parseJSON(documents_required_fields);

        var admission_form = '<?php echo json_encode($admission_form) ?>';
        var admission_form_arr = $.parseJSON(admission_form);

        var new_document_type = '<?php echo $config_val['document_input_version'] ?>';
        $.ajax({
                url: '<?php echo site_url('admission_controller/get_parent_data'); ?>',
                type: 'post',
                data: {'af_id':insert_id},
                success: function(data) {
                    var resdata = $.parseJSON(data);
                    if(new_document_type == 'V1'){
                        $('#doc_id').html(_construct_document_table(resdata,docsArry,required_fields_arr,admission_form_arr));
                    }else{
                        $('#doc_id').html(_construct_document_table_new(resdata,docsArry,required_fields_arr,admission_form_arr));
                    }
                }
            });
    }

    function _construct_document_table(resdata,docsArry,required_fields_arr,admission_form_arr){
        var html = '';
        for(var i=0;i<docsArry.length;i++){
            var requiredColor = '';
            var required = '';
            if($.inArray(docsArry[i], required_fields_arr) !== -1){
                requiredColor = '<font color="red">*</font>';
                required = 'required';
            }
            filespath = 0;
            doc_rowId = 0;
            doc_disabledUpload = '';
            $.each(admission_form_arr, function(key,val) {       
                if (val.document_type == docsArry[i]) {
                    filespath = 1;
                    doc_rowId = val.id;
                    doc_disabledUpload = 'disabled';
                    required ='';
                }   
            });

            html += '<tr>';
            html += `<td>${i+1}</td>`;
            html += `<td style="width: 30%;" >${docsArry[i]} ${requiredColor}<input type="hidden" name="document_for" id="document_for${i+1}" value="${docsArry[i]}" ></td>`;
            html += `<td style="border-right: none; width:10%">
                    <input type="file" onchange="upload_document_file_path(${i+1},this)" name="document_file_path" ${required} class="documentUpload" ${doc_disabledUpload} id="doc-upload${i+1}" accept="image/jpeg, application/pdf"/>
                    <div id="afterSuccessUploadShow${i+1}">
                    </div>`;
            html += `<span style="color:red;" id="doc_error${i+1}"></span>`;
                    if(filespath == 1){
            html += `<a style="margin-top: 1rem;" id="successmessageId${i+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                    <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${i+1},'${required}')" id="removeButtonId${i+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`;
                    }
            html += `</td>
                    <td style="border-left: none;" ><span id="percentage_doc_completed${i+1}" style="font-size: 20px; display: none;">0 %</span></td>
                    </tr>`;

        }
        return html;
    }

    function _construct_document_table_new(resdata,docsArry,required_fields_arr,admission_form_arr){
        var documents_arr = [];
        var category = $('#category option:selected').text();
        docsArry.forEach(doc => {
            const { relation, condition } = doc;

            if ($.type(condition) === 'undefined') {
                documents_arr.push(doc);
                return;
            }

            const checks = {
                student: resdata.nationality,
                father: resdata.f_nationality,
                mother: resdata.m_nationality
            };

            const nationality = checks[relation];

            if(condition == 'All' || condition == ''){
                documents_arr.push(doc);
            }else if (nationality === 'Indian' && condition === 'if_nationality_indian') {
                documents_arr.push(doc);
            } else if (nationality && nationality !== 'Indian' && condition === 'if_nationality_other') {
                documents_arr.push(doc);
            }
        });
   
        var html = '';
        for(var k=0;k<documents_arr.length;k++){
            var requiredColor = '';
            var required = '';
            var is_required = '';
            if($.type(documents_arr[k].required) !== 'undefined' && (documents_arr[k].required == 'true' || documents_arr[k].required == true) ){
                requiredColor = '<font color="red">*</font>';
                required = 'required';
                is_required = 'required';
            }
            var instruction = '';
            if($.type(docsArry[k].instruction)  !== 'undefined'){
                instruction = documents_arr[k].instruction;
            }
            filespath = 0;
            doc_rowId = 0;
            doc_disabledUpload = '';
            $.each(admission_form_arr, function(key,val) {   
                if (val.document_type == documents_arr[k].name) {
                    filespath = 1;
                    doc_rowId = val.id;
                    doc_disabledUpload = 'disabled';
                    required ='';
                }
            });
            var upload_type = '';
            if ($.type(documents_arr[k].view_type) !== 'undefined' &&
                    (documents_arr[k].view_type.toLowerCase() === 'aadhar' || documents_arr[k].view_type.toLowerCase() === 'pan')
                ) {
                upload_type += `<td style="border-right: none; width:10%"> <button class="btn btn-info" type="button" data-toggle="modal" data-target="#upload_document_model" id="doc-upload${k+1}" onclick="show_upload_document_modal('${documents_arr[k].view_type}','${documents_arr[k].name}','${k+1}')" ${doc_disabledUpload}>Upload Details</button>`;
                upload_type += `<div id="afterSuccessUploadShow${k+1}">
                                </div>`;
                if(filespath == 1){
                    upload_type += `<a style="margin-top: 1rem;" id="successmessageId${k+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                    <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${k+1})" id="removeButtonId${k+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>`;
                    }
                upload_type +=`</td>`;
            }else if($.type(documents_arr[k].view_type) !== 'undefined' && documents_arr[k].view_type == 'caste_certificate' && category == 'General') {
                requiredColor = '';
                is_required = '';
                required='';
                upload_type += `<td style="border-right: none; width:10%">
                            <input type="file" onchange="upload_document_file_path(${k+1},this)" name="document_file_path" ${required} class="documentUpload" ${doc_disabledUpload} id="doc-upload${k+1}" accept="image/jpeg, application/pdf"/>
                            <div id="afterSuccessUploadShow${k+1}">
                            </div>`;
                upload_type += `<span style="color:red;" id="doc_error${k+1}"></span>`;
                            if(filespath == 1){
                upload_type += `<a style="margin-top: 1rem;" id="successmessageId${k+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                            <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${k+1},'${is_required}')" id="removeButtonId${k+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>
                            `;                
                            }
                upload_type += `</td>`;  
            }else{
                upload_type += `<td style="border-right: none; width:10%">
                            <input type="file" onchange="upload_document_file_path(${k+1},this)" name="document_file_path" ${required} class="documentUpload" ${doc_disabledUpload} id="doc-upload${k+1}" accept="image/jpeg, application/pdf"/>
                            <div id="afterSuccessUploadShow${k+1}">
                            </div>`;
                upload_type += `<span style="color:red;" id="doc_error${k+1}"></span>`;
                            if(filespath == 1){
                upload_type += `<a style="margin-top: 1rem;" id="successmessageId${k+1}" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>
                            <a style="margin-top: 1rem;" onclick="deletedocument_row_new(${doc_rowId},${k+1},'${is_required}')" id="removeButtonId${k+1}" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>
                            `;                
                            }
                upload_type += `</td>`;           
              
            }

            html += '<tr>';
            html += `<td>${k+1}</td>`;
            html += `<td style="width: 30%;" >${documents_arr[k].name} ${requiredColor}<input type="hidden" name="document_for" id="document_for${k+1}" value="${documents_arr[k].name}" ><span class="help-block">${instruction}</span></td>`;
            html += `${upload_type}
                    <td style="border-left: none;" ><span id="percentage_doc_completed${k+1}" style="font-size: 20px; display: none;">0 %</span></td>`;
        }
        return html;
    }

    $(".save-step4").click(function (e){
        $("#vaccination_table_7 .cls_3").remove();
        //var formData =$("#health-form").serialize();
        var document_form = '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var medical_form = '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var $form = $('#guardian-form');
        if ($form.parsley().validate()){
            var form = $('#guardian-form')[0];
            var formData = new FormData(form);
            $('.save-step4').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_guardian_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-step4').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                        if(document_form == 1 && medical_form == 0){
                            url = '<?php echo site_url('admissions/preview') ?>';
                            $('#guardian-form').attr('action',url);
                            $('#guardian-form').submit();
                        }else{
                            var $active = $('.wizard1 .nav-tabs li.active');
                            $active.next().removeClass('disabled');
                            nextTab($active);
                            return 1;
                        }
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    $(".save-draft2").click(function (e){
        //var formData =$("#health-form").serialize();
        var $form = $('#parent-form');
        if ($form.parsley().validate()){
            var form = $('#parent-form')[0];
            var formData = new FormData(form);
            $('.save-draft2').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_parent_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-draft2').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                         url = '<?php echo site_url('admissions/home') ?>';
                        $('#parent-form').attr('action',url);
                        $('#parent-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    $(".save-draft4").click(function (e){
        var $form = $('#guardian-form');
        if ($form.parsley().validate()){
            var form = $('#guardian-form')[0];
            var formData = new FormData(form);
            $('.save-draft4').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_guardian_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-draft4').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                         url = '<?php echo site_url('admissions/home') ?>';
                        $('#guardian-form').attr('action',url);
                        $('#guardian-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        }
    });
    
});

// draft medical
$(".save-draft5").click(function (e){
    $("#vac_date_modal").val('11-03-2999');
        var $form = $('#medical-form');
        if ($form.parsley().validate()){
            var form = $('#medical-form')[0];
            var formData = new FormData(form);
            $('.save-draft5').val('Please wait ...').attr('disabled','disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_medical_form_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    // console.log(data);
                    $('.save-draft5').val('Save & Continue').removeAttr('disabled');
                    if(data == 1) {
                         url = '<?php echo site_url('admissions/home') ?>';
                        $('#medical-form').attr('action',url);
                        $('#medical-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                }
            });
        } else {
            $("#vaccination_table_in_admission ul").each(function() {
                var html= $(this).children('li').html();
                $(this).hide();
                var num= $(this).prev('td').data("num");
                $(this).prev('td').children(`.span_class_${num}`).remove();
                $(this).prev('td').append(`<span class="span_class_${num}">${html}</span>`);
            });
        }
    });
    // draft medcal end

function nextTab(elem) {
    $(elem).next().find('a[data-toggle="tab"]').click();
}
function prevTab(elem) {
    $(elem).prev().find('a[data-toggle="tab"]').click();
}



function check_sibling_ad_no() {
   var sb_ad =  $('#sb_admission_number').val();
   $.ajax({
        url: '<?php echo site_url('admission_controller/check_sibling_ad_nobyinput'); ?>',
        type: 'post',
        data: {'sb_ad':sb_ad},
        success: function(data) {
            console.log(data);
            if (data) {
                $('#exit_error').html(data);
            }else{
                $('#exit_error').html("");
            }
        }
    });
}


$('#document_add').on('click',function(){
    var file_doc = $('#documentId').val();
    if(file_doc ==''){
        bootbox.alert({
            title:'Error',
            message: "Upload document and then click Add",
            size: 'small',
            className:'class_document'
        });
        return false;
    }
    var $form = $('#document-form');
    var afid = $('#afid').val();
        var form = $('#document-form')[0];
        var formData = new FormData(form);
        $('#document_add').val('Please wait ...').attr('disabled','disabled');
        $("#loader1").show();
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_documents/'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,
            success: function(data) {
                console.log(data);
            $("#loader1").hide();
            $('#document_add').val('Add').removeAttr('disabled');
                if (data != '') {
                    $('#documentId').val('');
                    $.post("<?php echo site_url('admission_controller/get_admission_form_document');?>",{afid:afid},function(data){
                        // console.log(data);
                        var details = $.parseJSON(data);
                        if (details !='') {
                            $('#document_submit').removeAttr('disabled');
                            $('#draft_submit').removeAttr('disabled');
                            var m=1;
                            var html = '';
                            html += '<table class="table">';
                            html += '<thead>';
                            html += '<tr>';
                            html += '<th>#</th>';
                            html += '<th>Name</th>';
                            html += '<th>Action</th>';
                            html += '</tr>';
                            html += '</thead>';
                            html += '<tbody>';
                            for (var i = 0; i < details.length; i ++) {
                                html += '<tr>';
                                html +='<td>'+m+'</td>'
                                html +='<td>'+details[i].document_type+'</td>'
                                html +='<td><a href="javascript:void(0)" onclick="deletedocument_row('+details[i].id+')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                                html += '</tr>';
                                m++;
                            }
                            html += '</tbody>';
                            html += '</table>';
                            $('#display_document').html(html);
                        }  
                    });
                }else{
                    alert('Something went wrong');
                }
            }
        });
});

function deletedocument_row(d_id) {
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_documentbyId'); ?>',
        type: 'post',
        data:{'d_id': d_id},
        success: function(data) {
            if(data !='') {
                $.post("<?php echo site_url('admission_controller/get_admission_form_document');?>",{afid:afid},function(data){
                    var details = $.parseJSON(data);
                    var m=1;
                    var html = '';
                    html += '<table class="table">';
                    html += '<thead>';
                    html += '<tr>';
                    html += '<th>#</th>';
                    html += '<th>Name</th>';
                    html += '<th>Action</th>';
                    html += '</tr>';
                    html += '</thead>';
                    html += '<tbody>';
                    for (var i = 0; i < details.length; i ++) {
                        html += '<tr>';
                        html +='<td>'+m+'</td>'
                        html +='<td>'+details[i].document_type+'</td>'
                        html +='<td><a href="javascript:void(0)" onclick="deletedocument_row('+details[i].id+')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                        html += '</tr>';
                        m++;
                    }
                    html += '</tbody>';
                    html += '</table>';
                    $('#display_document').html(html);
                });
            } else {
                alert("Something went wrong in Student data, try again.");
                return 0;
            }
        }
    });
}

$('#nationality').on('change',function(){
    var others = $('#nationality').val();
    if(others == 'Other'){
        $('#nationality_other').show();
    }else{
        $('#nationality_other').hide();
    }
});

$('#schooling_board').on('change',function(){
    var others = $('#schooling_board').val();
    if(others == 'Other'){
        $('#board_other').show();
    }else{
        $('#board_other').hide();
    }
});

$('#religion').on('change',function(){
    var others = $('#religion').val();
    if(others == 'Other'){
        $('#religion_other').show();
    }else{
        $('#religion_other').hide();
    }
});


$('#document_for').on('change',function(){
    var others = $('#document_for').val();
    if(others == 'Others'){
        $('#documentName').show();
    }else{
        $('#documentName').hide();
    }
});

$('#mother_tongue').on('change',function(){
    var others = $('#mother_tongue').val();
    if(others == 'Other'){
        $('#mother_tongue_name').show();
    }else{
        $('#mother_tongue_name').hide();
    }
});
 

$('#father_mother_tongue').on('change',function(){
    var others = $('#father_mother_tongue').val();
    if(others == 'Other'){
        $('#f_mother_tongue_name').show();
    }else{
        $('#f_mother_tongue_name').hide();
    }
});

$('#mother_mother_tongue').on('change',function(){
    var others = $('#mother_mother_tongue').val();
    if(others == 'Other'){
        $('#m_mother_tongue_name').show();
    }else{
        $('#m_mother_tongue_name').hide();
    }
});

function previous_click() {
    bootbox.confirm({
    title : "Confirm",  
    message: "Are you sure you want to exit? <br> <span class='help-block'>On exit, application will be saved as draft. You can continue later.</span>",
    buttons: {
        confirm: {
            label: 'Yes',
            className: 'btn-success'
        },
        cancel: {
            label: 'No',
            className: 'btn-danger'
        }
    },
    callback: function (result) {
      if(result) { 
        url = '<?php echo site_url('admissions/home') ?>';
        $('#breadcrumb-form').attr('action',url);
        $('#breadcrumb-form').submit();
        return false;         
      }
    }
  });
}


var show_previous_schooling_subjects = '<?php echo $show_previous_schooling_subjects ?>';
var show_previous_schooling_overall_total_marks = '<?php echo $show_previous_schooling_overall_total_marks ?>';
var show_previous_schooling_overall_percentage = '<?php echo $show_previous_schooling_overall_percentage ?>';
var show_previous_schooling_subject_total_marks = '<?php echo $show_previous_schooling_subject_total_marks ?>';
var show_previous_schooling_subject_percentage = '<?php echo $show_previous_schooling_subject_percentage ?>';
$('#prev_add').on('click',function(){
    var file_doc = $('#schooling_school').val();
    if(file_doc ==''){
        bootbox.alert({
            title:'Error',
            message: "Enter school name and then click add button",
            size: 'small'
        });
        return false;
    }
    var $form = $('#document-form');
   $('#combinationId').removeAttr('required');
    var afid = $('#afid').val();
    if ($form.parsley().validate()){
        var form = $('#document-form')[0];
        var formData = new FormData(form);
        $('#prev_add').val('Please wait ...').attr('disabled','disabled');
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_previous_school_details/'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,
            success: function(data) {
            console.log(data);
            $('#prev_add').val('Add >>').removeAttr('disabled');
                if (data != '') {
                   $('#combinationId').attr('required','required');
                  
                    // $('#schooling_year').val('');
                    $.post("<?php echo site_url('admission_controller/get_previous_school_details');?>",{afid:afid},function(data){
                        var details = $.parseJSON(data);
                        // console.log(details);
                        if (details !='') {
                            var html = '';
                            html += '<table class="table table-bordered">';
                            html += '<thead>';
                            html += '<tr>';
                            html += '<th>Year</th>';
                            html += '<th>Grades and marks obtained in Final Exam</th>';
                            html += '</tr>';
                            html += '</thead>';
                            html += '<tbody>';
                            for (var i = 0; i < details.length; i++) {
                                html += '<tr>';
                                html +='<td>'+details[i].year+'</td>'
                                html +='<td>';
                                html +='<lable>School Name : '+details[i].school_name+'</label><br>';
                                html +='<lable>Class : '+details[i].class+'</label><br>';
                                if (details[i].board =='Other' ) {
                                    html +='<lable>Board : '+details[i].board+', '+details[i].board_other+'</label><br>';
                                }else{
                                     html +='<lable>Board : '+details[i].board+'</label><br>';
                                }
                                // html +='<lable>Class : '+details[i].class+'</label><br>';

                                if (show_previous_schooling_overall_total_marks == 1) {
                                    html +='<lable>Total Marks Scored : '+details[i].total_marks_scored+'</label><br>';
                                    html +='<lable>Total Marks : '+details[i].total_marks+'</label><br>';
                                }
                                if (show_previous_schooling_overall_percentage == 1) {
                                    html +='<lable>Total Percentage : '+details[i].total_percentage+'</label><br>';
                                }
                                // var json = $.parseJSON(details[i].marks);
                                // console.log(json);
                                if (show_previous_schooling_subjects) {
                                    console.log((details[i].marks));
                                    if(details[i].marks != undefined){
                                        html +='<lable>Subject : <br> '
                                        for (var j = 0; j < details[i].marks.length; j++) {
                                            var json = $.parseJSON(details[i].marks[j].sub_name);

                                            if (show_previous_schooling_subject_total_marks == 1) {
                                                html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].marks_scored+ '/'+details[i].marks[j].marks+'<br>';
                                            }
                                            if (show_previous_schooling_subject_percentage == 1) {
                                                html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].percentage+ '%'+'( '+details[i].marks[j].grade+') <br>'
                                            }

                                            // var percentage = ' %';
                                            // if (show_previous_schooling_subject_total_marks) {
                                            //     percentage = ' ';
                                            // }
                                            // html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].percentage+ percentage+'( '+details[i].marks[j].grade+') '
                                        }
                                    }
                                   
                                }
                                
                                html +='<a href="javascript:void(0)" onclick="deletepreviou_school_row('+details[i].apsid+')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                                html +='</lable>';
                                html += '</tr>';
                            }                        
                            html += '</tbody>';
                            html += '</table>';
                            // console.log(html);
                            $('#display_prev').html(html);
                            $('#schooling_class').val('');
                            $('.per').val('');
                            $('.grd').val('');
                            yearLength--;
                            $.ajax({
                                url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                                type: 'post',
                                data: formData,
                                processData: false,
                                contentType: false,
                                cache : false,
                                success: function(data) {
                                    var years = $.parseJSON(data);
                                    if (years.length== 0) {

                                    }
                                    // console.log(years);
                                    // alert(years.length);
                                    var yearinfo = '<option value="">Select Year</option>';
                                    for (var i = 0; i < years.length; i ++) {
                                      yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                                    }
                                    // console.log(yearinfo);
                                    $('#schooling_year').html(yearinfo);
                                }
                            });

                            $('#schooling_year').val('');
                            $('.displayshow').hide();
                            $('#prev_add').attr('disabled','disabled');
                        }  
                    });
                }else{
                    alert('Something went wrong');
                }
            }
        });
    }
});

function deletepreviou_school_row(apsid) {
    var $form = $('#document-form');
    var form = $('#document-form')[0];
    var formData = new FormData(form);
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_details_schooling_byId'); ?>',
        type: 'post',
        data:{'apsid': apsid},
        success: function(data) {
            // console.log(data);
            if (data != '') {
                $.post("<?php echo site_url('admission_controller/get_previous_school_details');?>",{afid:afid},function(data){
                    var details = $.parseJSON(data);
                    // console.log(data);
                    if (details !='') {
                        var html = '';
                        html += '<table class="table table-bordered">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th>Year</th>';
                        html += '<th>Grades and marks obtained in Final Exam</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';
                        for (var i = 0; i < details.length; i++) {
                            html += '<tr>';
                            html +='<td>'+details[i].year+'</td>'
                            html +='<td>';
                            html +='<lable>School Name : '+details[i].school_name+'</label><br>';
                            html +='<lable>Class : '+details[i].class+'</label><br>';
                            if (details[i].board =='Other' ) {
                                html +='<lable>Board : '+details[i].board+', '+details[i].board_other+'</label><br>';
                            }else{
                                 html +='<lable>Board : '+details[i].board+'</label><br>';
                            }

                            if (show_previous_schooling_overall_total_marks == 1) {
                                html +='<lable>Total Marks Scored : '+details[i].total_marks_scored+'</label><br>';
                                html +='<lable>Total Marks : '+details[i].total_marks+'</label><br>';
                            }
                            if (show_previous_schooling_overall_percentage == 1) {
                                html +='<lable>Total Percentage : '+details[i].total_percentage+'</label><br>';
                            }
                            html +='<lable>Subject : '

                            if (show_previous_schooling_subjects) {
                                if(details[i].marks != undefined){
                                    html +='<lable>Subject : '
                                    for (var j = 0; j < details[i].marks.length; j++) {
                                        var json = $.parseJSON(details[i].marks[j].sub_name);
                                        var percentage = ' %';
                                        if (show_previous_schooling_subject_total_marks) {
                                            percentage = ' ';
                                        }
                                        html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].percentage+ percentage+'( '+details[i].marks[j].grade+') '
                                    }
                                }
                               
                            }

                            html +='<a href="javascript:void(0)" onclick="deletepreviou_school_row('+details[i].apsid+')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                            html +='</lable>';
                            html += '</tr>';

                        }                        
                        html += '</tbody>';
                        html += '</table>';
                        $('#display_prev').html(html);
                        yearLength++;

                         $.ajax({
                            url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                            type: 'post',
                            data: formData,
                            processData: false,
                            contentType: false,
                            cache : false,
                            success: function(data) {
                                // console.log(data);
                                var years = $.parseJSON(data);
                                // console.log(years);
                                // alert(years.length);
                                var yearinfo = '<option value="">Select Year</option>';
                                for (var i = 0; i < years.length; i ++) {
                                  yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                                }
                                // console.log(yearinfo);
                                $('#schooling_year').html(yearinfo);
                            }
                        });

                        return true; 
                    }
                    $.ajax({
                        url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                        type: 'post',
                        data: formData,
                        processData: false,
                        contentType: false,
                        cache : false,
                        success: function(data) {
                            // console.log(data);
                            var years = $.parseJSON(data);
                            // console.log(years);
                            if (years.length == 0) {
                                documentSubmit(0);
                            }
                            var yearinfo = '<option value="">Select Year</option>';
                            for (var i = 0; i < years.length; i ++) {
                              yearinfo +='<option value="'+years[i]+'">'+years[i]+'</option>';
                            }
                            // console.log(yearinfo);
                            $('#schooling_year').html(yearinfo);
                        }
                    });
                    $('#display_prev').html('');
                    $('#dispalyempty').show();
                    $('#empty').html('Details of Schooling not added');
                    // alert('Something went wrong');
                });
            }else{
               
                alert('Something went wrong');
            }
        }
    });
}

function documentSubmit(val) {

    var required = '<?php echo $required_fields['year']['required'] ?>';
    if (required == 'required') {
        if (yearLength == 0) {
            $('#schooling_year').removeAttr('required');
            $('#schooling_school').removeAttr('required');
            $('#schooling_class').removeAttr('required');
            $('#schooling_board').removeAttr('required');
            $('#school_address').removeAttr('required');
            $('#registration_no').removeAttr('required');
            $('#medium_of_instruction').removeAttr('required');
            url = '<?php  echo site_url('admissions/preview') ?>';
            $('#document-form').attr('action',url);
            $('#document-form').submit();
            return true;
        }else{
           alert("Enter previous details, then you can 'Save and Preview'.");
        }
    }else{
        $('#schooling_year').removeAttr('required');
        $('#schooling_school').removeAttr('required');
        $('#schooling_class').removeAttr('required');
        $('#schooling_board').removeAttr('required');
        $('#school_address').removeAttr('required');
        url = '<?php  echo site_url('admissions/preview') ?>';
        $('#document-form').attr('action',url);
        $('#document-form').submit();
        return true;
    }
   
}

function max_marks_total(m) {
    var maxmarks = $('#maxMarks_'+m).val();
    if(maxmarks == ''){
        $('#maxMarks_'+m).val('');
    }
    var tmaxMarks = 0;
    $('.maxMarks').each(function() {
        if(parseFloat($(this).val())){
            tmaxMarks += parseFloat($(this).val());
        }
    });

    $('#total_max_marks_entry').val(tmaxMarks);
    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}
function max_marks_scored_total(m) {
    var maxMarkScored = $('#maxMarkScored_'+m).val();
    if(maxMarkScored == ''){
        $('#maxMarkScored_'+m).val('');
    }
    var tmaxMakrsScored = 0;
    $('.maxMakrsScored').each(function() {
       if(parseFloat($(this).val())){
        tmaxMakrsScored += parseFloat($(this).val());
       }
    });
    $('#total_marks_scored').val(tmaxMakrsScored);

    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored)/parseFloat(total_max_marks_entry)*100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function check_is_value_not_empty(e, sl) {
    if(e.value !=''){
        $('#maxMarks_'+sl).attr({
           "max" : 125,       
           "min" : 100 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 125,       
           "min" : 0
        });
    }else{
        $('#maxMarks_'+sl).attr({
           "max" : 0,       
           "min" : 0 
        });

        $('#maxMarkScored_'+sl).attr({
           "max" : 0,       
           "min" : 0
        });
    }
}

function show_upload_document_modal(view_type,document_name,document_sl_no){
    $("body").addClass("modal1");
    $(".modal1").css("display",'contents');
    $('#document_sl_no').val(document_sl_no);
    $('#upload_document_header').html(`Upload ${document_name} Details`);
    $('#document_name').val(document_name);
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';
    if(view_type == 'aadhar'){
        $('#upload_aadhar_details').html(construct_aadhar_details(adm_setting_id));
    }else if(view_type == 'pan'){
        $('#upload_aadhar_details').html(construct_pan_details(adm_setting_id));
    }
    }
   
    function construct_aadhar_details(adm_setting_id){
        var html = '';
        var template = '<?php echo site_url('admission_controller/download_declaration/') ?>'+adm_setting_id+'/'+'Aadhar Card Declaration/'+'aadhar';
        html += `<div class="row" id="has_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0 " for="btn-1" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block1" name="has_document" id="btn-1" value="1"  onclick="show_aadhar_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="btn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block1" name="has_document" id="btn-0" value="0" onclick="show_aadhar_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_aadhar_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="name_in_aadhar" class="col-md-4">Name As per Aadhar <font color="red">*</font></label>
                        <div class="col-md-6">
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="name_in_aadhar" placeholder="Enter the name as per Aadhar" name="name_as_per__aadhar" type="text" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-3" style="margin-top: 15px;padding-left:5px">
                        <label for="aadhar_number" class="col-md-4">Aadhar Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="aadhar_number" placeholder="Enter the Aadhar Number" name="aadhar_number" type="number" data-parsley-minlength="12" data-parsley-maxlength="12" data-parsley-type="number" data-parsley-error-message="Should contain 12 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Aadhar Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="aadhar_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="no_aadhar_document" style="display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="attach_label">Have you applied for Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-2" value="1"  onclick="upload_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-3" value="0" onclick="upload_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="acknowledgement_file" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="download_aadhar_acknowledgement" style="display:none">
                    <div class="col-md-12" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="aadhar_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
        return html;
    }

    function construct_pan_details(adm_setting_id){
        var html = '';
        var template = '<?php echo site_url('admission_controller/download_declaration/') ?>'+adm_setting_id+'/'+'PAN Card Declaration/'+'pan';
        html += `<div class="row" id="has_pan_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="panbtn-1" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-1" value="1"  onclick="show_pan_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="panbtn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-0" value="0" onclick="show_pan_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_pancard_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="pan_number" class="col-md-4">PAN Card Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="pan_card_number" placeholder="Enter the PAN Card Number" name="pan_card_number"  data-parsley-minlength="10" data-parsley-maxlength="10" pattern="[A-Z0-9]" data-parsley-error-message="Should contain 10 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload PAN Card Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pancard_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="no_pan_document" style="margin-top:15px;display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Have you applied for PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="pan_btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-2" value="1"  onclick="upload_pan_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="pan_btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-3" value="0" onclick="upload_pan_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_pan_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_acknowledgement" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="download_pancard_acknowledgement" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
        return html;
    }

function show_aadhar_details(e){
    if(e == 1){
        $('#show_aadhar_data').show();
        $('#no_aadhar_document').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').attr('required','required');
        $('#aadhar_number').attr('required','required');
        $('#aadhar_doc_file').attr('required','required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    }else{
        $('#no_aadhar_document').show();
        $('#show_aadhar_data').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').removeAttr('required');
        $('#aadhar_number').removeAttr('required');
        $('#aadhar_doc_file').removeAttr('required');
    }
}

function upload_acknowledgement_details(e){
    if(e == 1){
        $('#show_acknowledgement_data').show();
        $('#download_aadhar_acknowledgement').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').attr('required','required');
        $('#aadhar_declaration').removeAttr('required');
    }else{
        $('#download_aadhar_acknowledgement').show();
        $('#show_acknowledgement_data').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').attr('required','required');
    }
}

function show_pan_details(e){
    if(e == 1){
        $('#show_pancard_data').show();
        $('#no_pan_document').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').attr('required','required');
        $('#pancard_doc_file').attr('required','required');
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    }else{
        $('#no_pan_document').show();
        $('#show_pancard_data').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').removeAttr('required');
        $('#pancard_doc_file').removeAttr('required');
    }
}

function upload_pan_acknowledgement_details(e){
    if(e == 1){
        $('#show_pan_acknowledgement_data').show();
        $('#download_pancard_acknowledgement').hide();
        $('#show_pancard_data').hide();
        $('#pan_acknowledgement').attr('required','required');
        $('#pan_declaration').removeAttr('required');
    }else{
        $('#download_pancard_acknowledgement').show();
        $('#show_pan_acknowledgement_data').hide();
        $('#show_pancard_data').hide();
        $('#pan_declaration').attr('required','required');
        $('#pan_acknowledgement').removeAttr('required');
    }
}

function upload_admission_documents(){
    var has_document = $('input[type=radio][name=has_document]:checked').val();
    var applied_for_document = $('input[type=radio][name=applied_form_document]:checked').val();
    if(has_document == undefined){
        alert('Upload the Documents');
        return false;
    }else if(has_document == 0 && applied_for_document == undefined){
        alert('Upload the Documents');
        return false;
    }
    var doc_sl_no = $('#document_sl_no').val();
    var $form = $('#upload_document_form');
    if ($form.parsley().validate()) {
        $('#upload_btn').val('Please wait ...').attr('disabled','disabled');
            var form = $('#upload_document_form')[0];
            var formData = new FormData(form);
            $.ajax({
                url: '<?php echo site_url('admission_controller/submit_admission_documents'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                // async: false,
                success: function(data) {
                    parsed_data = $.parseJSON(data);
                    if(parsed_data){
                        $("#upload_btn").prop('disabled',false);
                        $("#upload_btn").html('Submit');
                        $('#upload_document_model').modal('hide');
                        $('#doc-upload'+doc_sl_no).attr('disabled','disabled');
                        var html ='';
                        html += '<a style="margin-top: 1rem;" id="successmessageId'+doc_sl_no+'" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>'
                        html += '<a style="margin-top: 1rem;" onclick="deletedocument_row_new('+parsed_data+','+doc_sl_no+')" id="removeButtonId'+doc_sl_no+'" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                        $('#afterSuccessUploadShow'+doc_sl_no).html(html);
                    }
                },complete:function(){
                    // window.location.reload();
                    $('#upload_btn').val('Submit').removeAttr('disabled');
                }
            });
    }
}

$('#fileupload').change(function(){
    var src = $(this).val();
   
    if(src && validate_photo_size(this.files[0],'fileupload','fileuploadError')){
        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(this.files[0]);
        $('#previewing').css('opacity','0.3');
        $("#fileuploadError").html("");
        readURL(this);
    } else{
        this.value = null;
    }
});

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $('#previewing').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

 function saveFileToStorage(file) {
    $('#percentage_student_completed').show();
    $('#fileupload').attr('disabled','disabled');
    $(".save-step1").prop('disabled',true);
    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {'filename':file.name, 'file_type':file.type, 'folder':'profile'},
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type, 
                    "x-amz-acl":"public-read" 
                },
                processData: false,
                data: file,
                xhr: function () {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function (e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total *100|0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    $('#student_high_quality_url').val(path);
                    // savePhoto(path, usertype, id, file);
                    $('#percentage_student_completed').hide();
                    $('#fileupload').removeAttr('disabled');
                    $('#previewing').css('opacity','1');
                    $(".save-step1").prop('disabled',false);
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function (err) {
            reject(err);
        }
    });
}

function single_file_progress(percentage) {
  if(percentage == 100) {
      in_progress_promises--;
      if(in_progress_promises == 0) {
          current_percentage = percentage;
      }
  } else {
      if(current_percentage<percentage) {
          current_percentage = percentage;
      }
  }
  $("#percentage_student_completed").html(`${current_percentage} %`);
  return false;
}


</script>