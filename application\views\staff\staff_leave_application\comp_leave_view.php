<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/leaves/dashboard'); ?>">Staff Leaves Master</a></li>
    <li class="active">Compensatory leave</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">

        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('staff/leaves/dashboard'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Compensatory Leaves
                    </h3>
                    <?php if (!empty($staff_data)) { ?>
                        <a href="" class="new_circleShape_res circleButton_noBackColor" style="background: #fe790a"
                            data-toggle="modal" onclick="show_compensatoryuploader()"
                            data-target="#show-compensatory-uploader">
                            <span class="fa fa-plus" style="font-size: 19px;"></span>
                        </a>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="card-body pt-1 pb-3">
            <?php if (empty($staff_data)) {
                echo '<div class="no-data-display">Staff not found</div>';
            } else { ?>
                <p><strong>Applied Compensatory leaves Quota : </strong>
                    <?php echo empty($applied->total) ? 0 : $applied->total; ?>
                </p>
                <p><strong>Approved Compensatory leaves Quota : </strong>
                    <?php echo empty($approved->total) ? 0 : $approved->total; ?>
                </p>

                <div class="form-group" style="width:15%;">
                    <select class="form-control" name="leaves_filed_type" id="leaves_filed_type" onchange="getCompLeaves()">
                        <option value="1" <?php echo $leaves_filed_type==1 ? "Selected": "" ?>>My Leaves</option>
                        <option value="2" <?php echo $leaves_filed_type !=1 ? "Selected" : "" ?>>All Leaves Filed By Me</option>
                    </select>
                </div>

                <table id="staff_comp_leave" class="table datatable table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Staff Name</th>
                            <th>Applied On Date</th>
                            <th>Applied For Date</th>
                            <th>Leave taken for</th>
                            <th>Details</th>
                            <th>Status</th>
                            <th>Approved By</th>
                            <th>Approval / Reject Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i = 1;

                        foreach ($comp_leave_details as $cl) { ?>
                            <tr>
                                <td>
                                    <?php echo $i++; ?>
                                </td>
                                <td>
                                    <?php echo $cl->staff_name; ?>
                                </td>
                                <td>
                                    <?php echo date('d-m-Y', strtotime($cl->requested_on)); ?>
                                </td>
                                <td>
                                    <?php echo date('d-m-Y', strtotime($cl->worked_on)); ?>
                                </td>
                                <td>
                                    <?php echo $cl->no_of_days_in_words; ?>
                                </td>
                                <td>
                                    <?php echo $cl->comments; ?>
                                </td>
                                <td>
                                    <?php
                                    if ($cl->status == '0') {
                                        echo "Pending";
                                    } elseif ($cl->status == '1') {
                                        echo "<span class='text-success'>Approved</span>";
                                    } elseif ($cl->status == '2') {
                                        echo "<span class='text-danger'>Rejected</span>";
                                    }
                                    ?>
                                </td>
                                 <td>
                                    <?php echo $cl->approved_by_name; ?>
                                </td>
                                <td>
                                    <?php echo $cl->approval_comment ? $cl->approval_comment : "-" ; ?>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            <?php } ?>
        </div>

    </div>
</div>

</div>

<div class="modal fade" id="show-compensatory-uploader" tabindex="-1" role="dialog"
    style="width:40%; margin:auto;top:5%" data-backdrop="static" aria-labelledby="resource-uploader-label"
    aria-hidden="true">
    <div class="modal-content modal-dialog modal-sm" style="border-radius: px;">
        <div class="modal-header rounded" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Apply Compensatory Leave</span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                type="button" class="close" data-dismiss="modal">&times;</button>
        </div>

        <form enctype="multipart/form-data" data-parsley-validate method="post" id="aply-compensatory-leave"
            class="form-horizontal">
            <div class="modal-body">
                <input type="hidden" name="leave_v2_year_id" value="<?php echo $leave_year->id; ?>">
                <input type="hidden" value="<?php echo $staff_data->id; ?>" name="requested_by" id="requested_by">
                <input type="hidden" value="<?php echo date('d-m-Y'); ?>" class="form-control" id="requested_on"
                    name="requested_on">
                <div class="form-group">
                    <div class="col-md-12">
                        <label class="control-label" for="staff_name" for="staff_name">Staff Name <font color="red">*</font></label>
                        <!-- Bring the staff names for the Admin to apply the leaves behalf of the other staffs -->
                        <div>
                            <select name="leave_applied_for" class="form-control" id="leave_applied_for">
                            <?php 
                                if($is_leave_admin){
                                    if(!empty($staff_list)){
                                        foreach($staff_list as $key => $staff){
                                            echo "<option value=".$staff->staff_id.">".$staff->staff_name."</option>";
                                        }
                                    }else{
                                        echo "<option value='0'>No staff to show</option>";
                                    }
                                } else {
                                    echo '<option readonly value=' . $staff_data->id . '>' . $staff_data->staff_name . '</option>';
                                }
                            ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-12">
                        <label class="control-label">Apply For Date <font color="red">*</font></label>
                        <div class="input-group date" id="datePicker">
                            <input autocomplete="off" required="" type="text" class="form-control" id="worked_on"
                                name="worked_on" placeholder="Select the day on which you were working">
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-8">
                        <div class="input-group date" id="">
                            <label class="control-label" for="consider_half_day">Apply for Half Day</label>
                            <input style="margin-left: 5px;margin-top: 1%;" type="checkbox" id="consider_half_day" name="consider_half_day">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-md-12">
                        <label class="control-label" for="reason">Remarks <font color="red">*</font></label></label>
                        <textarea rows="5" class="form-control" name="comments" id="comments" required=""
                            onkeypress="return checkEntry(event)" onpaste="return checkEntry(event)"
                            onchange="return checkEntry(event)"></textarea>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <div class="col-md-12">
                    <input type="button" value="Create" id="createCompensatoryButton"
                        class="col-md-4 btn btn-primary pull-right" onclick="create_compensatory_leave()">
                </div>
            </div>
        </form>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
        integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>

    <script>
        function show_compensatoryuploader() {
            $('#datePicker').datepicker({
                format: 'dd-M-yyyy',
                autoclose: true,
                endDate: "today"
            });
        }


        function add_compensatory_leave_modal() {
            // console.log('plus button success');
        }

        function submitLeave() {

            var $form = $('#leave-form');
            if ($form.parsley().validate()) {
                $form.submit();
            }
        }

        function create_compensatory_leave() {
            const leaveAppliedForName=$("#leave_applied_for :selected").text();

            var classId = $('#classId').val();
            var $form = $('#aply-compensatory-leave');
            if ($form.parsley().validate()) {
                var form = $('#aply-compensatory-leave')[0];
                var formData = new FormData(form);
                formData.append('class_id', classId);
                $('#createCompensatoryButton').val('Please wait ...').attr('disabled', 'disabled');
                $.ajax({
                    url: '<?php echo site_url('staff/Leaves/request_comp_leave') ?>',
                    type: 'post',
                    data: formData,
                    // async: false,
                    processData: false,
                    contentType: false,
                    // cache : false,
                    success: function (data) {
                        // console.log(data);
                        $('#show-compensatory-uploader').modal('hide');
                        $('#createCompensatoryButton').val('Create').removeAttr('disabled');

                        if(data==1){
                            Swal.fire({
                            icon: "success",
                            title: "Saved",
                            text: "Your leave is has been saved!",
                            }).then(e=>{
                                location.reload();
                            })
                        }else if(data==2){
                            Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "You have already filed compensatory leave for this date!",
                            }).then(e=>{
                                location.reload();
                            })
                        }else if(data==3){
                            Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "You have already filed Full day compensatory leave for this date, Please try applying Half day compensatory leave insted!",
                            }).then(e=>{
                                location.reload();
                            })
                        }else if(data==4){
                            Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            html: `Compensatory Leave Quota is either not Assigned or Deactive for <u>${leaveAppliedForName}</u>, For more details please contact with your Admin!`,
                            }).then(e=>{
                                location.reload();
                            })
                        }else{
                            Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                            }).then(e=>{
                                location.reload();
                            })
                        }
                        // location.reload();
                    },
                    error: function (err) {
                        console.log(err);
                    }
                });
            }
        }

        function checkEntry(e) {
            var k;
            document.all ? k = e.keyCode : k = e.which;
            return (k < 34 || (k > 34 && k < 39) || k > 39);
        }

        function getCompLeaves(){
            let leaves_filed_type=$("#leaves_filed_type").val();
            if(leaves_filed_type==""){
                // setting default to as "All my leaves"
                leaves_filed_type=1;
            }

            let URL="<?php echo site_url('staff/Leaves/view_comp_leave') ?>";
            window.location.href=`${URL}/${leaves_filed_type}`;
        }
    </script>

    <style>
        .btn_align {
            margin-bottom: 4px;
            width: 32px;
        }

        ul.panel-controls>li>a {
            border-radius: 50%;
        }

        .card {
            margin-bottom: 1rem;
            border-radius: 0.75rem;
        }

        .new_circleShape_res {
            padding: 8px;
            border-radius: 50% !important;
            color: white !important;
            font-size: 22px;
            height: 3.2rem !important;
            width: 3.2rem !important;
            text-align: center;
            vertical-align: middle;
            float: left;
            border: none !important;
            box-shadow: 0px 3px 7px #ccc;
            line-height: 1.7rem !important;
        }
    </style>