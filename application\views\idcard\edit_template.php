<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('idcards/Idcards_controller')?>">ID Cards</a></li>
    <li><a href="<?php echo site_url('idcards/Idcards_controller/template_list')?>">Manage Templates</a></li>
    <li>Edit ID Card Template</li>
</ul>
<hr>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('idcards/Idcards_controller/template_list'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Edit ID Card Template
          </h3>
        </div>
      </div>
    </div>
    <div class="card-body">
        <?= form_open('idcards/Idcards_controller/update_template', ['id' => 'templateForm']) ?>
            <input type="hidden" name="id" value="<?= $template->id ?>">
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="template_name" class="form-label">Template Name</label>
                    <input type="text" class="form-control" id="template_name" name="template_name"
                           value="<?= htmlspecialchars($template->name) ?>" required>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="id_card_for" class="form-label">ID Card for</label>
                    <select class="form-control" id="id_card_for" name="id_card_for">
                        <option value="Student" <?= $template->id_card_for == 'Student' ? 'selected' : '' ?>>Student</option>
                        <option value="Staff" <?= $template->id_card_for == 'Staff' ? 'selected' : '' ?>>Staff</option>
                        <option value="Parent" <?= $template->id_card_for == 'Parent' ? 'selected' : '' ?>>Parent</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="unit_price" class="form-label">Unit Price</label>
                    <input type="number" step="0.01" class="form-control" id="unit_price" name="unit_price"
                           value="<?= $template->unit_price ?>" required>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="template_size" class="form-label">Card Size</label>
                    <select class="form-control" id="template_size" name="template_size">
                        <option value="portrait" <?= $template->size == 'portrait' ? 'selected' : '' ?>>
                            Standard Portrait (54mm x 86mm)
                        </option>
                        <option value="landscape" <?= $template->size == 'landscape' ? 'selected' : '' ?>>
                            Standard Landscape (86mm x 54mm)
                        </option>
                        <option value="custom" <?= $template->size == 'custom' ? 'selected' : '' ?>>
                            Custom Size
                        </option>
                    </select>
                </div>
            </div>

            <div id="custom_size_row" class="row mb-3" style="display: <?= $template->size == 'custom' ? 'flex' : 'none' ?>;">
                <div class="col-md-6">
                    <label for="custom_width" class="form-label">Width (mm)</label>
                    <input type="number" class="form-control" id="custom_width" name="custom_width"
                           min="20" max="200" value="<?= $template->width ?? '' ?>">
                </div>
                <div class="col-md-6">
                    <label for="custom_height" class="form-label">Height (mm)</label>
                    <input type="number" class="form-control" id="custom_height" name="custom_height"
                           min="20" max="200" value="<?= $template->height ?? '' ?>">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="grid_density" class="form-label">Grid Density</label>
                    <select class="form-control" id="grid_density">
                        <option value="none">No Grid</option>
                        <option value="basic" selected>Basic Grid (25%, 50%, 75%)</option>
                        <option value="1x1">1x1 Grid (1mm spacing)</option>
                        <option value="2x2">2x2 Grid (2mm spacing)</option>
                        <option value="5x5">5x5 Grid (5mm spacing)</option>
                    </select>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Front Side Design</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="text">
                                    <i class="fa fa-font"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="image">
                                    <i class="fa fa-image"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="shape">
                                    <i class="fa fa-shapes"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="front" data-type="field">
                                    <i class="fa fa-database"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="frontPreview" class="card-preview mb-3">
                                <div id="frontContent" class="card-content" data-side="front">
                                    <!-- Design elements will be added here -->
                                </div>
                            </div>

                            <div class="accordion" id="frontElementsAccordion">
                                <!-- Elements will be added here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Back Side Design</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="text">
                                    <i class="fa fa-font"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="image">
                                    <i class="fa fa-image"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="shape">
                                    <i class="fa fa-shapes"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary add-element" data-side="back" data-type="field">
                                    <i class="fa fa-database"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="backPreview" class="card-preview mb-3">
                                <div id="backContent" class="card-content" data-side="back">
                                    <!-- Design elements will be added here -->
                                </div>
                            </div>

                            <div class="accordion" id="backElementsAccordion">
                                <!-- Elements will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden fields to store JSON design data -->
            <input type="hidden" id="front_design" name="front_design" value='<?= htmlspecialchars($template->front_design ?: '[]', ENT_QUOTES, 'UTF-8') ?>'>
            <input type="hidden" id="back_design" name="back_design" value='<?= htmlspecialchars($template->back_design ?: '[]', ENT_QUOTES, 'UTF-8') ?>'>

            <div class="row">
                <div class="col-12">
                    <button type="button" id="previewTemplateBtn" class="btn btn-info mr-2">
                        <i class="fa fa-eye"></i> Preview Template
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save"></i> Update Template
                    </button>
                    <a href="<?= site_url('idcards/Idcards_controller/template_list') ?>" class="btn btn-secondary">
                        <i class="fa fa-times"></i> Cancel
                    </a>
                </div>
            </div>
        <?= form_close() ?>
    </div>
  </div>
</div>

<!-- Position and size indicators -->
<div id="positionIndicator" class="position-indicator">X: <span id="posX">0</span>, Y: <span id="posY">0</span></div>
<div id="sizeIndicator" class="size-indicator">W: <span id="sizeW">0</span>, H: <span id="sizeH">0</span></div>

<!-- Selection mode indicator -->
<div id="selectionModeIndicator" class="selection-mode-indicator">Group Selection Mode</div>

<!-- Element Templates -->
<div id="elementTemplates" style="display: none;">
    <!-- Text Element -->
    <div class="text-element element" data-type="text">
        <div class="element-content">Sample Text</div>
    </div>

    <!-- Image Element -->
    <div class="image-element element" data-type="image">
        <img src="<?= base_url('assets/images/placeholder.png') ?>" class="element-content">
    </div>

    <!-- Shape Element -->
    <div class="shape-element element" data-type="shape">
        <div class="element-content shape-rectangle"></div>
    </div>

    <!-- Field Element -->
    <div class="field-element element" data-type="field">
        <div class="element-content">[[FIELD]]</div>
    </div>
</div>

<!-- Element Properties Modal -->
<div class="modal fade" id="elementPropertiesModal" tabindex="-1" role="dialog" aria-labelledby="elementPropertiesModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="elementPropertiesModalTitle">Element Properties</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Alignment Toolbar -->
                <div class="alignment-toolbar mb-3 p-2 bg-light rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="d-block mb-2">Horizontal Alignment</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="left"><i class="fa fa-align-left"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="center-h"><i class="fa fa-align-center"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="right"><i class="fa fa-align-right"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="distribute-h"><i class="fa fa-arrows-alt-h"></i></button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="d-block mb-2">Vertical Alignment</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="top"><i class="fa fa-arrow-up"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="center-v"><i class="fa fa-arrows-alt-v"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="bottom"><i class="fa fa-arrow-down"></i></button>
                                <button type="button" class="btn btn-outline-secondary align-element" data-align="distribute-v"><i class="fa fa-grip-lines"></i></button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Element Properties -->
                <div id="elementProperties">
                    <!-- Properties will be dynamically loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveElementProperties">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
        </div>
    </div>
</div>

<!-- Add these lines before the closing </body> tag -->
<link rel="stylesheet" href="<?= base_url('assets/css/idcard_template_editor.css') ?>">
<script src="<?= base_url('assets/js/idcard_template_editor.js') ?>"></script>

<script>
$(document).ready(function() {
    // Initialize the template editor with existing data
    const frontDesign = JSON.parse($('#front_design').val() || '[]');
    const backDesign = JSON.parse($('#back_design').val() || '[]');
    
    // Set up template size
    const templateSize = $('#template_size').val();
    if (templateSize === 'custom') {
        $('#custom_size_row').show();
    }
    
    // Initialize grid
    updateGrid($('#grid_density').val());
    
    // Render existing elements
    renderElements('front', frontDesign);
    renderElements('back', backDesign);
    
    // Set up event listeners
    setupEventListeners();
    
    // Handle template size change
    $('#template_size').on('change', function() {
        const size = $(this).val();
        if (size === 'custom') {
            $('#custom_size_row').show();
        } else {
            $('#custom_size_row').hide();
        }
        updatePreviewSize(size);
    });
    
    // Handle preview button click
    $('#previewTemplateBtn').on('click', function() {
        const frontDesign = JSON.parse($('#front_design').val() || '[]');
        const backDesign = JSON.parse($('#back_design').val() || '[]');
        const templateSize = $('#template_size').val();
        const customWidth = $('#custom_width').val();
        const customHeight = $('#custom_height').val();
        
        showPreview(frontDesign, backDesign, templateSize, customWidth, customHeight);
    });
});
</script>