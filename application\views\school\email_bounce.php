<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('school/school_menu');?>">School Menu</a></li>
  <li>Email Bounce Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('school/school_menu'); ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a>
                        Email Bounce Report
                    </h3>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <?php if(empty($email['email_audit']) && empty($email['bounced_emails'])) {
                echo '<div class="text-center">No Data found</div>';
                } else { ?>
                <h4>Email Status Summary (Last 7 Days) Detailed Bounced Emails</h4>
                <table id="bouncedEmailsTable" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Student Name</th>
                            <th>Admission No</th>
                            <th>Parent Email</th>
                            <th>Relation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i=1; foreach ($email['bounced_emails'] as $key => $val) { ?>
                            <tr>
                                <td><?php echo $i++; ?></td>
                                <td><?php echo $val->student_name; ?></td>
                                <td><?php echo $val->admission_no; ?></td>
                                <td><?php echo $val->parent_email; ?></td>
                                <td><?php echo $val->relation_type; ?></td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            <?php } ?>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    $('#bouncedEmailsTable').DataTable({
        "paging": false,
        "order": [[0, "asc"]],
        "responsive": true,
        dom: 'frtipB', // Add this line to show buttons
        buttons: ['excel', 'print'],
    });
});
</script>
