<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/payroll/');?>">Payroll dashboard</a></li>
    <li>Staff Wise Payroll Report</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('management/payroll/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>Staff Wise Payroll Report
                    </h3>
                </div>
                <div class="col-md-4 pr-0 d-flex justify-content-end align-items-center">
                    <div class="col-md-4 pr-0">
                        <label class="pull-right">Financial Year</label>
                    </div>
                    <div class="col-md-5">
                        <select class="form-control input-md custom-select" id="financial_year" onchange="refreshData()"> 
                            <?php
                                foreach($financial_year as $year){ 
                                    $selected = (isset($year->selected) && $year->selected == 1) ? 'selected' : '';?>
                                    <option value ="<?= $year->id?>" <?= $selected ?>><?= $year->f_year ?></option>
                                <?php } ?>
                        </select>
                    </div>
                    <div class="col-md-3 d-none">
                        <label style="font-weight: 600;" class="checkbox-inline">
                            <input style="width:20px;height: 20px;" type="checkbox" name="total_earnings_summary" id="total_earnings_summary"> 
                            Summary
                        </label>
                    </div>
                </div>
            </div>
        </div>

		<div class="panel-body">
            <div class="col-md-2">
                <select class="form-control" name="filter_report" id="filter_report">
                    <option value="1">Staff wise</option>
                    <option value="2">Month wise Summary</option>
                    <option value="3">Tax Summary</option>
                </select>
            </div>

            <div class="col-md-4" id="select_staff_name">
                <select class="form-control" name="staff_name" id="staff_name">
                    <option value="-1"><?php echo "Select all Staff" ?></option>
                    <?php foreach ($staff_details  as $staff) { ?>
                        <option value="<?= $staff->staff_id; ?>" data-staff-type="<?= $staff->staff_type;?>"><?= $staff->staff_name; ?> <?= $staff->employee_code ? '('.$staff->employee_code.')' : '';?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="col-md-2">
                <button type="button" onclick="get_staff_wise_payroll_data()" id="getData" class="btn btn-primary">Get</button>
            </div>

            <div class="col-md-12 mt-2" id="loadingIcon" style="display: none;">
                <div class="no-data-display"><i class="fa fa-spinner fa-spin"></i> Loading Data...</div>
            </div>
            <div class="col-md-12 mt-2" id="staff_details_table">
            </div>
            <div class="col-md-12 mt-2" id="month_wise_data">
            </div>
            <div class="col-md-12 mt-2" id="income_tax_data">
            </div>
		</div>
	</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
<script>
function refreshData(){
    $('#getData').html('Get');
    $('#getData').prop('disabled', false);
    $('#filter_report').prop('disabled', false);
    $('#staff_name').prop('disabled', false);
    $('#staff_details_table').html('');
    $('#month_wise_data').html('');
    $('#income_tax_data').html('');
    $('#loadingIcon').hide();
}

$(document).ready(function() {
    $('#filter_report').on('change', function() {
        const selectedVal = $(this).val();

        switch (selectedVal) {
            case '1': // Staff wise
                $('#select_staff_name').show();
                $('#staff_name').prop('disabled', false);
                $('#staff_name').val('-1');
                $('#income_tax_data').html('');
                $('#month_wise_data').html('');
                $('#staff_details_table').html('');
                break;

            case '2': // Month wise summary
                $('#select_staff_name').hide();
                $('#staff_name').val('-1');
                $('#staff_details_table').html('');
                $('#income_tax_data').html('');
                $('#month_wise_data').html('');
                break;

            case '3': // Tax Summary
                $('#select_staff_name').hide();
                $('#staff_name').val('-1');
                $('#staff_details_table').html('');
                $('#month_wise_data').html('');
                $('#income_tax_data').html('');
                break;

            default:
                $('#select_staff_name').hide();
                $('#staff_name').val('-1');
                $('#staff_details_table').html('');
                $('#month_wise_data').html('');
                $('#income_tax_data').html('');
                break;
        }
    });
})

function get_staff_wise_payroll_data(){
    // console.log(el);
    $('#getData').html('Please Wait...');
    $('#getData').prop('disabled', true);
    $('#filter_report').prop('disabled', true);
    $('#staff_name').prop('disabled', true);
    $('#financial_year').prop('disabled', true);
    var el = $('#filter_report').val();
    if(el == 2){
        get_all_staff_month_wise_earnings();
        return false;
    }
    if(el == 3){
        get_all_staff_income_tax_details();
        return false;
    }
    var staff_id = $('#staff_name').val();
    var staff_name = $('#staff_name option:selected').text();
    
    var financial_year = $('#financial_year').val();
    var financial_year_name = $('#financial_year option:selected').text();
    let file_name1 = `${staff_name} Payroll Data ${financial_year_name}`;
    if(staff_id == -1){
        Swal.fire({
            icon: "error",
            title: "Please Select A Staff.",
            showConfirmButton: false,
            timer: 1500
        });
        $('#filter_report').prop('disabled', false);
        $('#getData').html('Get');
        $('#getData').prop('disabled', false);
        $('#staff_name').prop('disabled', false);
        $('#financial_year').prop('disabled', false);
        return false;
    }else{
        // $('#staff_details_table').html('<div class="no-data-display"><i class="fa fa-spinner fa-spin"></i> Loading Data...</div>');
        $('#staff_details_table').html('');
        $('#loadingIcon').show();
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_staff_wise_payroll_data'); ?>',
            type: 'post',
            data: {'staff_id': staff_id, 'financial_year': financial_year},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                merged_data = parsed_data.merged_data;
                schedules = parsed_data.schedules;
                staffDeclarationStatus = parsed_data.staffDeclarationStatus;
                
                if (merged_data.length != 0) {
                    $('#income_tax_data').html('');
                    $('#month_wise_data').html('');
                    $('#staff_details_table').html(construct_table_header(merged_data, staffDeclarationStatus));
                    construct_table_content(merged_data, staff_id);
                    $('#staff_payroll_details_table').DataTable({
                        dom: 'lBfrtip',
                        paging: false,
                        info: false,
                        ordering: false,
                        searching: false,
                        destroy: true,
                        buttons: [
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                // filename: file_name1,
                                className: 'btn btn-info',
                                action: function ( e, dt, button, config ) {
                                    var headerData = [];
                                    $('#staff_payroll_details_table thead tr th').each(function() {
                                        headerData.push($(this).text());
                                    });

                                    var bodyData = [];
                                    dt.rows({ search: 'applied' }).every(function () {
                                        var row = this.node();
                                        var cleanRow = [];

                                        $('td', row).each(function () {
                                            var text = $(this).clone()
                                                .find('span')
                                                .remove()
                                                .end()
                                                .text()
                                                .trim();

                                            cleanRow.push(text);
                                        });

                                        bodyData.push(cleanRow);
                                    });
                                    var footerRow = ['', 'Total'];
                                    footerRow.push(
                                        formatNumber(totalMonthlyCTC), 
                                        formatNumber(totalBasicSalary),
                                        formatNumber(totalHRASalary),
                                        formatNumber(totalPFSalary),
                                        formatNumber(totalVPFSalary),
                                        formatNumber(totalPTSalary),
                                        formatNumber(totalLta),
                                        formatNumber(totalOutsideCTC),
                                        formatNumber(totalAdditionalAllowance),
                                        formatNumber(totaltotal_earnings),
                                        formatNumber(totalTDS)
                                    );
                                    footerRow.push('');
                                    var finalData = [headerData].concat(bodyData).concat([footerRow]);

                                    var wb = XLSX.utils.book_new();
                                    var ws = XLSX.utils.aoa_to_sheet(finalData);
                                    XLSX.utils.book_append_sheet(wb, ws, 'Payroll Data');

                                    XLSX.writeFile(wb, `${file_name1}.xlsx`);
                                }
                            }
                        ],
                    });
                } else {
                    $('#staff_details_table').html('<div class="no-data-display">No Data Found</div>');
                }
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#staff_name').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').hide();
            },
            error: function(err) {
                console.log(err);
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#staff_name').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').hide();
            }
        });
    }
}

function get_all_staff_month_wise_earnings(){
    $('#getData').html('Please Wait...');
    $('#getData').prop('disabled', true);
    $('#filter_report').prop('disabled', true);
    $('#financial_year').prop('disabled', true);
    $('#staff_details_table').html('');
    $('#month_wise_data').html('');
    $('#income_tax_data').html('');
    $('#loadingIcon').show();
    // Swal.fire({
    //     title: 'Loading...',
    //     html: '<i class="fa fa-spinner fa-spin" style="color: #007bff; font-size:16px;"></i> Fetching Payroll Summary...',
    //     showConfirmButton: false,
    //     allowOutsideClick: false,
    //     allowEscapeKey: false,
    // });
    $.ajax({
            url: '<?php echo site_url('management/payroll/get_all_staff_payslip_generated_data'); ?>',
            type: 'post',
            success: function(data) {
                staff_data = $.parseJSON(data);
                get_staff_wise_month_wise_earnings(staff_data);
            },
            error: function(err) {
                console.log(err);
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').show();
            }
        })
}

async function get_staff_wise_month_wise_earnings(all_staff_ids) {
    // var staff_id = $('#staff_name').val();
    var financial_year = $('#financial_year').val();
    
    if (all_staff_ids.length === 0) {
        return false;
    }
    var column_totals = [];
    let hasData = false;
    let headerConstructed = false;
    let rowCounter = 0;
    for(var i= 0; i<all_staff_ids.length; i++) {
        try {
            const result = await genearte_month_wise_staff_total_earningswise(
                all_staff_ids[i].staff_id, 
                all_staff_ids[i].employee_code, 
                all_staff_ids[i].staff_name, 
                financial_year, 
                rowCounter, 
                column_totals,
                headerConstructed
            );
            if (result) {
                hasData = true; // At least one staff has data
                headerConstructed = true;
                rowCounter++;
            }
            if(i == all_staff_ids.length - 1) {
                if (!hasData) {
                    $('#month_wise_data').html('<div class="no-data-display">No Data Found</div>');
                } else {
                    construct_footer_totals(column_totals);
                }
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').hide();
            }
        } catch (error) {
            console.error(error);
        }
    }
}

function genearte_month_wise_staff_total_earningswise(staff_id, staff_code, staff_name, financial_year,index, column_totals, headerConstructed){
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_staff_wise_payroll_data'); ?>',
            type: 'post',
            data: {'staff_id': staff_id, 'financial_year': financial_year},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                // console.log(parsed_data);
                let merged_data = parsed_data.merged_data;
                let schedules = parsed_data.schedules;
                // console.log(merged_data);    
                
                if (merged_data.length > 0) {
                    if(!headerConstructed){
                        $('#month_wise_data').html(construct_monthwise_header_summary(schedules));
                        $('#staff_details_table').html('');
                        schedules.forEach(function () {
                            column_totals.push(0);
                        });
                    }
                    construct_monthwise_summary(merged_data,schedules, staff_code, staff_name, index, column_totals);
                    resolve(true);
                } else {
                    // $('#month_wise_data').html('<div class="no-data-display">No Data Found</div>');
                    resolve(false);
                }
            },
            error: function(err) {
                console.log(err);
                resolve(false);
            }
        });
    });
}

function construct_monthwise_header_summary(schedules){
    var table = `<table class="table table-bordered" id="summary_table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Staff Code</th>
                    <th>Staff Name</th>
                    `;
                    schedules.forEach(function (schedule) {
                        table += `<th>${schedule.schedule_name}</th>`;
                    });
            table += `<th>Total</th></tr>
            </thead>
            <tbody id="total_earnings_data">
            </tbody>
            <tfoot id="footer_totals">
            </tfoot>
            <table>`;
    return table
}
function construct_monthwise_summary(merged_data,schedules, staff_code, staff_name, index, column_totals){
    let total_schedule_earnings = 0;
    var table = `<tr>
                <th>${index+1}</th>
                <th>${staff_code}</th>
                <th>${staff_name}</th>`;
    var earningsMap = {};
    schedules.forEach(function (schedule) {
        var scheduleId = schedule.id;
        var matchingEntry = merged_data.find(data => data.schedule_id === scheduleId);

        if (matchingEntry) {
            earningsMap[scheduleId] = matchingEntry.total_earnings;
        } else {
            earningsMap[scheduleId] = 'N/A';
        }
    });
    schedules.forEach(function (schedule, idx) {
        var earnings = earningsMap[schedule.id] ? earningsMap[schedule.id] : 'N/A';
        if (earnings !== 'N/A') {
            total_schedule_earnings += earnings;
            column_totals[idx] += earnings;
        }
        // total_schedule_earnings += earnings != "N/A" ? parseFloat(earnings) : 0;
        var formattedEarnings = earnings != "N/A" ? 
        new Intl.NumberFormat('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(parseFloat(earnings)) : earnings;
        table += `<th>${formattedEarnings}</th>`;
    });
    var formattedTotalEarnings = new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(total_schedule_earnings);
    table += `<th>${formattedTotalEarnings}</th></tr>`;
    $('#total_earnings_data').append(table);
}

function construct_footer_totals(column_totals) {
    var footer = `<tr>
        <th colspan="3" style="text-align: right;">Total</th>`;
    var totalRow = ['', '', 'Total'];
    column_totals.forEach(function(total) {
        var totalFormatted = new Intl.NumberFormat('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(total);

        footer += `<th>${totalFormatted}</th>`;
        totalRow.push(totalFormatted);
    });

    var grand_total = column_totals.reduce((a, b) => a + b, 0);
    var formattedGrandTotal = new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(grand_total);

    footer += `<th>${formattedGrandTotal}</th></tr>`;
    totalRow.push(formattedGrandTotal);

    $('#footer_totals').html(footer);
    var financial_year_name = $('#financial_year option:selected').text();
    let file_name2 = `Staff Wise Payroll Summary ${financial_year_name}`;
    $('#summary_table').DataTable({
        dom: 'lBfrtip',
        paging: true,
        info: false,
        ordering: false,
        searching: true,
        scrollX: true,
        destroy: true,
        buttons: [
            {
                extend: 'excelHtml5',
                text: 'Excel',
                // filename: file_name2,
                className: 'btn btn-info',
                action: function ( e, dt, button, config ) {
                    var headerData = [];
                    $('#summary_table thead tr th').each(function() {
                        headerData.push($(this).text());
                    });

                    var bodyData = dt.rows({ search: 'applied' }).data().toArray();

                    var finalData = [headerData].concat(bodyData).concat([totalRow]);

                    var wb = XLSX.utils.book_new();
                    var ws = XLSX.utils.aoa_to_sheet(finalData);
                    XLSX.utils.book_append_sheet(wb, ws, 'Payroll Summary');

                    XLSX.writeFile(wb, `${file_name2}.xlsx`);
                }
            }
        ],
    });
    Swal.close();
}

function construct_table_header(parsed_data, staffDeclarationStatus){
    var totalMonthlyCTC = 0;
    var totalBasicSalary = 0;
    var totalHRASalary = 0;
    var totalPFSalary = 0;
    var totalVPFSalary = 0;
    var totalPTSalary = 0;
    var totalOutsideCTC = 0;
    var totalAdditionalAllowance = 0;
    var totaltotal_earnings = 0;
    var totalTDS = 0;
    var totalLta = 0;
    for (let s = 0; s < parsed_data.length; s++) {
        totalMonthlyCTC += parseFloat(parsed_data[s].ctc_with_employee_pf) ;
        totalBasicSalary +=parseFloat(parsed_data[s].basic_salary_with_da);
        totalHRASalary += parseFloat(parsed_data[s].hra);
        totalPFSalary += parseFloat(parsed_data[s].employee_pf_contribution);
        totalVPFSalary += parseFloat(parsed_data[s].vpf);
        totalPTSalary += parseFloat(parsed_data[s].professional_tax);
        totalOutsideCTC += parseFloat(parsed_data[s].outside_ctc_allowance);
        totalAdditionalAllowance += parseFloat(parsed_data[s].additional_month_allowance);
        totaltotal_earnings += parseFloat(parsed_data[s].total_earnings);
        
        totalTDS += parseFloat(parsed_data[s].tds);
        totalLta += parsed_data[s].lta != undefined ? parseFloat(parsed_data[s].lta) : 0;
    }
    
    window.totalMonthlyCTC = totalMonthlyCTC;
    window.totalBasicSalary = totalBasicSalary;
    window.totalHRASalary = totalHRASalary;
    window.totalPFSalary = totalPFSalary;
    window.totalVPFSalary = totalVPFSalary;
    window.totalPTSalary = totalPTSalary;
    window.totalOutsideCTC = totalOutsideCTC;
    window.totalAdditionalAllowance = totalAdditionalAllowance;
    window.totaltotal_earnings = totaltotal_earnings;
    window.totalTDS = totalTDS;
    window.totalLta = totalLta;

    let table = '';
    if(staffDeclarationStatus != 'Approved'){
        table += `<div class="col-md-12 pl-0 mt-3">
                    <p class="sub_header_note">
                        <strong>Note:</strong> The selected staff's declaration status is <span style="font-weight:700;font-size:16px;color:blue">${staffDeclarationStatus}</span>. For accurate calculation, please make sure the selected staff's declaration is <span style="font-weight:700;font-size:16px;color:green">Approved</span>.
                    </p>
                </div>`;
    }

    table += `<table id="staff_payroll_details_table" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Month</th>
                            <th>Monthly CTC</th>
                            <th>Basic Monthly Salary</th>
                            <th>HRA</th>
                            <th>PF</th>
                            <th>VPF</th>
                            <th>PT</th>
                            <th>LTA</th>
                            <th>Out Side Monthly CTC</th>
                            <th>Additional Allowance</th>
                            <th>Total Earnings</th>
                            <th>TDS</th>
                            <th>Payslip Status</th>
                        </tr>
                    </thead>
                    <tbody id="schedule_wise_staff_details">
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="2" class="text-right">Total</th>
                            <th>${formatNumber(totalMonthlyCTC)}</th>
                            <th>${formatNumber(totalBasicSalary)}</th>
                            <th>${formatNumber(totalHRASalary)}</th>
                            <th>${formatNumber(totalPFSalary)}</th>
                            <th>${formatNumber(totalVPFSalary)}</th>
                            <th>${formatNumber(totalPTSalary)}</th>
                            <th>${formatNumber(totalLta)}</th>
                            <th>${formatNumber(totalOutsideCTC)}</th>
                            <th>${formatNumber(totalAdditionalAllowance)}</th>
                            <th>${formatNumber(totaltotal_earnings)}</th>
                            <th>${formatNumber(totalTDS)}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>`;
    return table;
}

function formatNumber(data){
    return new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(data);
}



function construct_table_content(staff_data, staff_id) {
    let row = '';
    
    staff_data.forEach((staff, index) => {
        row += `<tr>
                    <td>${index + 1}</td>
                    <td>${staff.schedule_name || ''}</td>
                    <td>${formatNumber(staff.ctc_with_employee_pf)}</td>
                    <td>${formatNumber(staff.basic_salary_with_da)}</td>
                    <td>${formatNumber(staff.hra)}</td>
                    <td>${formatNumber(staff.employee_pf_contribution)}</td>
                    <td>${formatNumber(staff.vpf)}</td>
                    <td>${formatNumber(staff.professional_tax)}</td>
                    <td>${staff.lta != undefined ? formatNumber(staff.lta) : '0.00'}</td>
                    <td>
                        ${formatNumber(staff.outside_ctc_allowance)} 
                        ${staff.is_payslip_generated != 0 ? ` <span onclick="getAdditionalAllowanceBreakDown('outside_ctc_allowance', ${staff_id}, ${staff.schedule_id}, '${staff.schedule_name}')" class="btn btn-info fa fa-info-circle px-1 py-0 pull-right"></span>`: ''}
                    </td>
                    <td>
                        ${formatNumber(staff.additional_month_allowance)} 
                        ${staff.is_payslip_generated !=0 ? ` <span onclick="getAdditionalAllowanceBreakDown('additional_allowance', ${staff_id}, ${staff.schedule_id}, '${staff.schedule_name}')" class="btn btn-info fa fa-info-circle px-1 py-0 pull-right"></span>` : ''}
                    </td>
                    <td>${formatNumber(staff.total_earnings)}</td>
                    <td>${formatNumber(staff.tds)}</td>
                    <td>${staff.approval_status}</td>
                </tr>`;
    });

    $('#schedule_wise_staff_details').html(row);
}

function getAdditionalAllowanceBreakDown(type, staff_id, schedule_id, schedule_name){
    var financial_year = $('#financial_year').val();
    $.ajax({
        url: "<?php echo site_url('management/payroll/getAdditionalAllowanceBreakDown')?>",
        type: "POST",
        data: {
            'staff_id': staff_id,
            'schedule_id': schedule_id,
            'financial_year': financial_year,
            'type': type,
        },
        success: function (response) {
            let breakDownData = JSON.parse(response);
            if(Object.keys(breakDownData).length > 0 > 0){
                showAllowanceBreakdown(breakDownData, schedule_name, type);
            } else {
                Swal.fire({
                    title: "No Data Found",
                    icon: "warning",
                    timer: 1500,
                    showConfirmButton: false,
                });
            }
        },
        error: function (error) {
            console.log(error);
        }
    });
}

function showAllowanceBreakdown(data, schedule_name, type) {
    let tableHtml = `
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="border-bottom: 1px solid #ddd;">
                    <th style="padding: 8px; text-align: left;">Allowance</th>
                    <th style="padding: 8px; text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
    `;

    // Variables to calculate total
    let totalAmount = 0;

    // Loop through data to build rows
    Object.values(data).forEach((item) => {
        tableHtml += `
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;text-align: left;">${item.display_name}</td>
                <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd;">${item.amount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
            </tr>
        `;
        totalAmount += item.amount;
    });

    // Add total row
    tableHtml += `
        </tbody>
        <tfoot>
            <tr>
                <td style="padding: 8px; font-weight: bold;text-align: left;">Total</td>
                <td style="padding: 8px; text-align: right; font-weight: bold;">${totalAmount.toLocaleString('en-IN', {style: 'currency', currency: 'INR'})}</td>
            </tr>
        </tfoot>
    </table>
    `;
    selected_type = type == 'additional_allowance' ? 'Additional Allowance' : 'Outside CTC Allowance';
    Swal.fire({
        title: `${selected_type} Breakdown Of <br>${schedule_name}`,
        html: tableHtml,
        confirmButtonText: "Close",
    });
}


// tds

function get_all_staff_income_tax_details(){
    $('#getData').html('Please Wait...');
    $('#getData').prop('disabled', true);
    $('#filter_report').prop('disabled', true);
    $('#financial_year').prop('disabled', true);
    $('#staff_details_table').html('');
    $('#month_wise_data').html('');
    $('#income_tax_data').html('');
    $('#loadingIcon').show();
    // Swal.fire({
    //     title: 'Loading...',
    //     html: '<i class="fa fa-spinner fa-spin" style="color: #007bff; font-size:16px;"></i> Fetching Summary...',
    //     showConfirmButton: false,
    //     allowOutsideClick: false,
    //     allowEscapeKey: false,
    // });
    $.ajax({
            url: '<?php echo site_url('management/payroll/get_all_staff_payslip_generated_data'); ?>',
            type: 'post',
            success: function(data) {
                staff_data = $.parseJSON(data);
                get_staff_wise_tds_summary(staff_data);
            },
            error: function(err) {
                console.log(err);
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').hide();
            }
        })
}

async function get_staff_wise_tds_summary(all_staff_ids) {
    // var staff_id = $('#staff_name').val();
    var financial_year = $('#financial_year').val();
    
    if (all_staff_ids.length === 0) {
        return false;
    }
    var column_totals = [];
    let hasData = false;
    let headerConstructed = false;
    let rowCounter = 0;
    for(var i= 0; i<all_staff_ids.length; i++) {
        try {
            const result = await genearte_tds_staff_total_details(
                all_staff_ids[i].staff_id, 
                all_staff_ids[i].employee_code, 
                all_staff_ids[i].staff_status, 
                all_staff_ids[i].staff_name, 
                financial_year, 
                rowCounter, 
                column_totals,
                headerConstructed
            );
            
            if(result){
                hasData = true;
                headerConstructed = true;
                rowCounter++;
            }
            if(i == all_staff_ids.length - 1) {
                if(!hasData){
                    $('#income_tax_data').html('<div class="no-data-display">No Data Found</div>');
                } else {
                    construct_datatable_tds_summary(column_totals);
                }
                $('#getData').html('Get');
                $('#getData').prop('disabled', false);
                $('#filter_report').prop('disabled', false);
                $('#financial_year').prop('disabled', false);
                $('#loadingIcon').hide();
            }
        } catch (error) {
            console.error(error);
        }
    }
}

function genearte_tds_staff_total_details(staff_id, staff_code, staff_status, staff_name, financial_year,index, column_totals, headerConstructed){
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '<?php echo site_url('management/payroll/get_income_tax_delcration_staff_list'); ?>',
            type: 'post',
            data: {'staff_id': staff_id, 'financial_year': financial_year},
            success: function(data) {
                parsed_data = $.parseJSON(data);
                // console.log(parsed_data);
                if (parsed_data.length != 0) {
                    if(!headerConstructed){
                        $('#income_tax_data').html(construct_tds_header_summary());
                    }
                    construct_tdswise_summary(parsed_data, staff_code, staff_status, staff_name, index, column_totals);
                    resolve(true);
                } else {
                    resolve(false);
                }
            },
            error: function(err) {
                console.log(err);
                resolve(false);
            }
        });
    })
}


function construct_tds_header_summary(){
    var table = `<table class="table table-bordered" id="tds_summary_table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Staff Name</th>
                    <th>Staff Code</th>
                    <th>Staff Status</th>
                    <th>Selected Regime</th>
                    <th>Perks Tax</th>
                    <th>Total Income</th>
                    <th>Interest Paid On Home Loan</th>
                    <th>Total Rent Paid</th>
                    <th>Less Exempted</th>
                    <th>Total 80C Deductions</th>
                    <th>Total 80CCD Deductions</th>
                    <th>Total 80D Deductions</th>
                    <!--<th>Total Deductions</th>-->
                    <th>Total TDS</th>
                    <th>Total TDS (New Regime)</th>
                    <th>Total TDS (Old Regime)</th>
                    <th>Total TDS (Without Perks Tax) </th>
                    <!-- <th>Generated TDS</th> -->
                    <th>Total Generated TDS (Up To Latest Generated Month)</th>
                    <!-- <th>Projected TDS (With Perks Tax)</th> -->
            </thead>
            <tbody id="total_tds_data">
            </tbody>
            <table>`;
    return table
}

function construct_tdswise_summary(income_tax, staff_code, staff_status, staff_name, index, column_totals){

    switch(staff_status) {
        case '1':
            staff_status = 'Pending';
            
            break;
        case '2':
            staff_status = '<span style="color: green;">Approved</span>';
            break;
        case '3':
            staff_status = 'Rejected';
            break;
        case '4':
            staff_status = '<span style="color: red;">Resigned</span>';
            break;
        case '5':
            staff_status = 'Retired';
            break;
        default:
            staff_status = 'NA';
            break;
    }
    
    let row_html = '';
    var totalIncome = 0;
    var LessExempted = 0;
    var totalDeduction = 0;
    var totalTDS = 0;
    var totalNewTDS = 0;
    var totalOldTDS = 0;

    totalIncome = income_tax.perquisite_income + income_tax.basic_salary + income_tax.hra + income_tax.other_allowance + income_tax.outside_ctc_allowances;
    if(income_tax.selected_regime =='Old Regime'){
        LessExempted = income_tax.hra_exemption + income_tax.or_sd;
        totalDeduction = income_tax.total_80_deductions;
        totalTDS = income_tax.or_tax_amt_remaining;
    }else{
        LessExempted = income_tax.nr_sd;
        totalDeduction = 0;
        totalTDS = income_tax.nr_tax_amt_remaining;
    }
    totalNewTDS = income_tax.nr_tax_amt_remaining;
    totalOldTDS = income_tax.or_tax_amt_remaining;

    row_html += `<tr>
                <td>${index + 1}</td>
                <td>${staff_name}</td>
                <td>${staff_code}</td>
                <td>${staff_status}</td>
                <td>${income_tax.selected_regime}</td>
                <td>${formatNumber(income_tax.perquisite_income) != 'NaN' ? formatNumber(income_tax.perquisite_income) : '0.00'}</td>
                <td>${formatNumber(totalIncome) != 'NaN' ? formatNumber(totalIncome) : '0.00' } </td>
                <td>${income_tax.interest_paid_on_home_loan != undefined ? formatNumber(income_tax.interest_paid_on_home_loan) : '0.00'} </td>
                <td>${income_tax.total_rent_paid != undefined ? formatNumber(income_tax.total_rent_paid) : '0.00'} </td>
                <td>${LessExempted != undefined ? formatNumber(LessExempted) : '0.00'} </td>
                <td>${formatNumber(income_tax.total_80c_deduction) != 'NaN' ? formatNumber(income_tax.total_80c_deduction) : '0.00'} </td>
                <td>${formatNumber(income_tax.total_80ccd_deduction) != 'NaN' ? formatNumber(income_tax.total_80ccd_deduction) : '0.00'} </td>
                <td>${formatNumber(income_tax.total_80d_deduction) != 'NaN' ? formatNumber(income_tax.total_80d_deduction) : '0.00'} </td>
                <!--<td>${formatNumber(totalDeduction) != 'NaN' ? formatNumber(totalDeduction) : '0.00'} </td>-->
                <td>${formatNumber(totalTDS) != 'NaN' ? formatNumber(totalTDS) : '0.00' } </td>
                <td>${formatNumber(totalNewTDS) != 'NaN' ? formatNumber(totalNewTDS) : '0.00' } </td>
                <td>${formatNumber(totalOldTDS) != 'NaN' ? formatNumber(totalOldTDS) : '0.00' } </td>
                <td>${formatNumber(income_tax.totalTDS_withoutPerks) != 'NaN' ? formatNumber(income_tax.totalTDS_withoutPerks) : '0.00' } </td>
                <!-- <td>${formatNumber(income_tax.recent_monthly_tds) != 'NaN' ? formatNumber(income_tax.recent_monthly_tds) : '0.00' } </td> -->
                <td>${formatNumber(income_tax.totalGeneratedTdsTillLatestMonth) != 'NaN' ? formatNumber(income_tax.totalGeneratedTdsTillLatestMonth) : '0.00' } </td>
                <!-- <td>${formatNumber(income_tax.total_monthly_tds) != 'NaN' ? formatNumber(income_tax.total_monthly_tds) : '0.00' } </td> -->
            </tr>`;
    $('#total_tds_data').append(row_html);
}

function construct_datatable_tds_summary(column_totals){
    var financial_year_name = $('#financial_year option:selected').text();
    let file_name3 = `Staff Wise TDS Summary ${financial_year_name}`;
    $('#tds_summary_table').DataTable({
        dom: 'lBfrtip',
        paging: true,
        info: false,
        ordering: false,
        searching: true,
        scrollX: true,
        destroy: true,
        buttons: [
            {
                extend: 'excelHtml5',
                text: 'Excel',
                filename: file_name3,
                className: 'btn btn-info',
            }
        ],
    });
}

</script>


<style type="text/css">
.sub_header_note {
    color: #8f7f7f !important;
    font-size: 14px;
}

.dataTables_wrapper .dt-buttons {
    float: right;
}
.dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
}
.dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
}
.dt-buttons{
    margin-bottom: 5px;
}
.table{
    margin-bottom: 0px;
}
.ellipsis{
    display: none;
}
.dataTables_filter{
    border-bottom: 0px;
}
</style>