<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('admission_process'); ?>">Admission</a></li>
    <li><a href="<?php echo site_url('admission_process/application_view'); ?>">New Admission applications</a></li>
    <li class="active">Individual Application </li>
</ul>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admission_process/application_view') ?>" class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Details of <p style="display:inline" id="for_name"></p> Applied for <p style="display:inline" id="for_grade"></p></p>
                    </h3>
                    
                    <div class="col-md-3 d-flex align-items-center justify-content-end">
                        <div class="dropdown">
                                <a class="btn btn-info nav-link dropdown-toggle" href="#"  role="button" aria-haspopup="true" aria-expanded="false" style="border-radius: 0.2rem;padding:8px 37px" onclick="show_dropdown()" id="action_btn">
                                Actions
                                </a>
                                <div class="dropdown-content"  id="dropdown_menu" >
                                <?php if($this->authorization->isAuthorized('ADMISSION.GENERATE_APPLICATION')){ ?>
                                    <a id="regen_application" onclick="generate_application_form('<?php echo $afId ?>','<?php echo $au_Id ?>','<?php echo $admission_setting_id ?>')" class="dropdown-item" href="javascript:void(0)">
                                    <?php if($final_preview->pdf_status == 0) {echo 'Generate Application'; }else { echo 'Re-generate Application'; }?>
                                    </a>
                                    <div class="dropdown-divider"></div>
                                <?php } ?>
                                <a <?php if($final_preview->pdf_status == 0) echo 'style="pointer-events: none;cursor: none;"'; ?> onclick="download_application_form('<?php echo $afId ?>','<?php echo $au_Id ?>','<?php echo $admission_setting_id ?>')" class="dropdown-item" href="javascript:void(0)" id="download_app">
                                 Download Application
                                </a>
                                <div class="dropdown-divider"></div>
                                <?php if($this->authorization->isAuthorized('ADMISSION.CHANGE_ACADEMIC_YEAR') && $follow_admission->curr_status != 'Draft' && $follow_admission->curr_status != 'Offer Released' && $follow_admission->curr_status != 'Student added to ERP'){ ?>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#change_acad_year_modal">Change Acad Year</a>
                                <div class="dropdown-divider"></div>
                                <?php } ?>
                               
                                <?php if($this->authorization->isAuthorized('ADMISSION.LINK_TO_ENQUIRY')){ ?>
                                    <a data-toggle="modal" data-target="#link_toEnquiry_modal" class="dropdown-item" id="link_to_enquiry">Link to enquiry</a>
                                    <a data-toggle="modal" data-target="#_view_enquiry_details_modal_box" style="display:none;" class="dropdown-item" id="view_enquiry_details">View details</a>
                                    <div class="dropdown-divider"></div>
                                <?php } ?>
                                <?php if($this->authorization->isAuthorized('ADMISSION.REVERT_SUBMISSION') && $follow_admission->curr_status !='Draft' && $follow_admission->curr_status !='Payment Pending' && $follow_admission->curr_status != 'Student added to ERP' && $follow_admission->curr_status != 'Offer Released'){ ?>
                                    <a class="dropdown-item" onclick="enable_to_parent_reupload_application()">Revert submission</a>
                                <?php } ?>
                                <?php if($this->authorization->isSuperAdmin() && ($follow_admission->curr_status =='Offer Released' || $follow_admission->curr_status =='Student added to ERP')){ ?>
                                    <a class="dropdown-item" onclick="acad_year_change_for_students_added_to_erp(<?= $final_preview->id ?>)" data-toggle="modal" data-target="#moved_students_acad_year_change">Change Academic year for Moved Students</a>
                                <?php } ?>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="col-md-12" style="padding: 0px;">
                <div class="col-md-3">
                    <div class="panel-body profile" style="padding-top:0;background-color:white">
                    <?php $label = $this->settings->getSetting('your_word_for_class') ?>
                        <div class="profile-image" id="profile_section">
                            <img id="stud_image" style="width:120px; height:150px;" src="https://iisb.schoolelement.in/assets/img/sample_boy_image.png" alt="Avatar">
                        </div>
                        <i style="position: absolute;top: 10.5rem;right: 6.7rem;" onclick="$('#fileupload').click();" class="fa fa-pencil releaedAfterHide"></i>
                        <input hidden="hidden" type="file" id="fileupload" class="file" data-preview-file-type="jpeg" name="student_photo" accept="image/*">
                        <span id="fileuploadError" style="color:red;display: block;padding-top:5px;padding-bottom:5px;"></span>
                        <!-- <span id="percentage-completed_student" style="font-size: 20px; display: none; position: absolute;top: 90px;left: 12rem;right: 0;">0 %</span> -->
                        <button id="student_profile_photo" type="button" onclick="save_profile_photo(<?php echo $afId; ?>,'student_photo')" style="display: none;margin-left:90px;margin-bottom:5px;width:100px;" class="btn btn-primary">Save</button>
                        <div class="profile-data">
                            <p style="display:inline" id="s_name"></p>
                            <br>
                            <?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?> :
                            <p style="display:inline" id="grade"></p>
                            <br>
                            Application No:
                            <p style="display:inline" id="apl_num"></p>
                            <br><br>
                            <h5 style="color:green;"><strong style="color: #d80403;">Status : </strong> <span id="currentStatus"><?php echo $follow_admission->curr_status ?></span></h5>
                            <?php if($student_admission_form_id == 0 && $follow_admission->curr_status !='Draft'){ ?>
                                <!-- <button class="btn btn-warning" onclick="enable_to_parent_reupload_application()">Revert submission</strong></h5> -->
                            <?php } ?>
                            <input type="hidden" id="internal_status" value="<?= $follow_admission->internal_status ?>">
                            
                        </div>
                        
                    </div>
                    <?php $i = 1;
                        $disabled = 'disabled';
                        $disabledRelaseOffier = 'disabled';
                        if ($follow_admission->curr_status !='Draft' && $follow_admission->curr_status != 'Payment Pending') {
                            $disabled = '';
                        }
                        if ($follow_admission->curr_status == 'Admit' ) {
                           $disabledRelaseOffier = '';
                        }
                     ?>
                    <div class="panel-body list-group border-bottom" style="padding: 0px 1px;">
                        <button type="button" id="app_details_tab" role="tab" onclick="Application_Details() <?php $i = 1; ?> " id="" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item <?php if ($i == 1) echo 'active' ?>" data-toggle="tab">
                            View/Edit Details
                            <span style="float: right;" id="app_details_tab_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <button type="button" id="previous_schooling_details_tab" role="tab" onclick="previous_school_details()" id="" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Previous Schooling Details
                            <span style="float: right;" id="previous_schooling_details_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <button type="button" role="tab" onclick="attached_documents()" id="attached_documents_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Attached Documents
                            <span style="float: right;" id="attached_documents_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <?php if ($config_val['admission_fee_amount'] !='0.00') { ?>
                            <button type="button" role="tab"  onclick="payment_details()" id="application_payment_details_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Application Payment details
                            <span style="float: right;" id="application_payment_details_icon" class="glyphicon glyphicon-chevron-right"></span>
                            </button>
                        <?php } ?>

                        <button type="button" role="tab" <?php echo $disabled ?> onclick="follow_up()" id="follow_up_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Follow-up
                            <span style="float: right;" id="follow_up_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <button type="button" role="tab" <?php echo $disabled ?> onclick="seat_allotment()" id="release_offers_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Release offers
                            <span style="float: right;" id="release_offers_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>

                        <!--  <a type="button" role="tab" onclick="seat_allotment()" id="seat_allotment_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                        Manage Seat Allottment / Admission
                            <span style="float: right;" id="seat_allotment_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </a> -->

                        <button type="button" role="tab" <?php echo $disabled ?> onclick="admission_confirmed()" id="admission_confirmed_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Move to ERP
                            <span style="float: right;" id="admission_confirmed_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <?php if($this->authorization->isAuthorized('ADMISSION.CLOSE_APPLICATION')) { ?>
                        <button type="button" role="tab" <?php echo $disabled ?> onclick="close_application()" id="close_application_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Close Application
                            <span style="float: right;" id="close_application_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <?php } ?>
                        <button type="button" role="tab" <?php echo $disabled ?> onclick="joining_forms()" id="joining_forms_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                            Joining Forms
                            <span style="float: right;" id="admission_confirmed_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <?php 
                        if($this->authorization->isAuthorized('ADMISSION.EDIT_HISTORY')) { ?>
                            <button type="button" role="tab" <?php echo $disabled ?>  onclick="edit_history()" id="edit_history_tab" style="border-radius: 10px;margin-bottom: 0.5rem;display:block;" class="list-group-item " data-toggle="tab">
                          Edit History
                            <span style="float: right;" id="edit_history_icon" class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <?PHP }
                        ?>
                        

                    </div>
                </div>
                <div class="col-md-9" style="height:75vh; overflow:scroll;">
                    <div class="card cd_border" id="application">
                        <div class="card-header panel_heading_new_style_staff_border">
                            <div class="row" style="margin: 0px">
                                <div style="width: 100%;" class="col-md-9 pl-0">
                                    <h4 class="card-title panel_title_new_style_staff" id="title">Application Details</h4>
                                </div>
                            </div>
                        </div>

                        <div class="card-body" id="view_details" style="margin-top:10px">
                            <?php $this->load->view("admission/student_form/individual_application/_view_details.php"); ?>
                        </div>

                         <div class="card-body" id="previous_school_details" style="display:none;margin-top: 10px;">
                            <?php $this->load->view("admission/student_form/individual_application/_previous_school_details.php"); ?>
                        </div>

                        <div class="card-body" id="attached_documents" style="display: none;">
                        <a href="" class="new_circleShape_res pull-right" id="add_documents" style="background-color: #fe970a;" data-toggle="modal" data-target="#document_modals">
                                                <span class="fa fa-plus" style="font-size: 19px;"></span>
                                            </a>
                            <div class="col-md-12" id="documents_table">
                               
                            </div>

                                    <!-- <div><button class="btn btn-primary btn-sm" onclick="add_more_documents()">Add more documents</button></div> -->

                        </div>

                        <div class="card-body" id="follow_up" style="display: none;">
                            <?php $this->load->view("admission/student_form/individual_application/_follow_up.php"); ?>
                        </div>

                        <div class="card-body" id="seat_allotment" style="display: none;">
                            <?php $this->load->view("admission/student_form/individual_application/_seat_allotment.php"); ?>
                        </div>

                        <div class="card-body" id="fees_collection" style="display: none;">
                            <form enctype="multipart/form-data" id="collect_fee-form" style="margin-bottom:1rem" class="form-horizontal" data-parsley-validate method="post">
                                <input type="hidden" id="afid" name="lastId" value="<?= $afId; ?>">
                                <input type="hidden" id="admission_setting_id" name="admission_setting_id" value="<?= $admission_setting_id; ?>">
                                <input type="hidden" id="au_id" name="au_id" value="<?= $au_Id; ?>">
                                <input type="hidden" name="application_fee_amount" value="<?php echo $config_val['admission_fee_amount'] ?>">

                                <?php if ($application_fee_status->payment_status == 'SUCCESS' || $application_fee_status->payment_status == 'DELETED') {   ?>
                                    <?php 
                                        if ($this->settings->getSetting('application_receipt_is_custom')) {?>
                                        
                                    <div class="col-md-12">
                                        <div class="col-md-6 pl-0">
                                            <h4>Print Receipt</h4>
                                        </div>
                                        <div class="col-md-6 pl-0">
                                            <ul class="panel-controls">
                                                <?php if($this->authorization->isSuperAdmin() && $application_fee_status->payment_status != 'DELETED') { ?>
                                                <a onclick="delete_receipt('<?php echo $afId ?>')" class="btn btn-danger" data-placement='top' data-toggle='tooltip' data-original-title='Delete'><i class="fa fa-trash-o"></i> Delete Receipt</a>
                                                <?php }?>
                                                <a id="stu_print" class="btn btn-secondary" href="javascript:void(0)" id="printBtn" onclick="printPerf()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</a>            
                                            </ul>
                                        </div>
                                        </div>
                                            <?php $shortName = $this->settings->getSetting('school_short_name');
                                            $this->load->view("admission/receipts/".$shortName."_receipt");
                                        }else{
                                            $this->load->view("admission/student_form/individual_application/_print_fee_receipt.php");
                                        }
                                    ?>
                                    <?php  ?>
                                <?php } else { ?>
                                    <?php $this->load->view("admission/student_form/individual_application/_collect_application_fee.php"); ?>
                                <?php } ?>
                            </form>
                        </div>

                        <div class="card-body" id="moveToErp" style="display: none;" >
                            <div class="col-md-6">
                                <div class="panel panel-success push-up-20">
                                    <div class="panel-body panel-body-pricing">
                                        <!-- <p><span class="fa fa-caret-right"></span> Application Fees - <span id="ApplicationStatus"> - </span> </p> -->
                                        <p><span class="fa fa-caret-right"></span> Onetime Fees -  <span id="feesStatus"> - </span></p>
                                        <p><span class="fa fa-caret-right"></span> Admission Status - <span id="admissionStatus"> - </span></p>
                                        <input type="hidden" id="admissionStudentId" >
                                    </div>
                                    <div class="panel-footer">
                                        <button  onclick="confirmtMovetoERP()" id="confirmMovetErp" disabled class="btn btn-success pull-right">Confirm</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-body" id="closeApplication" style="display: none;" >
                            <h5>Untill the fees is un-assigned and credentials are deactivated Application cannot be close.</h5>
                            <div class="col-md-12" style="margin-top: 20px;">
                                <div class="form-group" >
                                    <label for="" class="col-md-3" style="width:200px">Fees Un-assigned?</label>
                                    <div class="col-md-5">
                                        <span  id="fees_confirmation">Yes</span>
                                    </div>
                                </div>
                            </div>
                                <br>
                            <div class="col-md-12" style="margin-top: 20px;">
                                <div class="form-group" >
                                    <label for="" class="col-md-3" style="width:200px">Credentials Deactivated?</label>
                                    <div class="col-md-5">
                                        <span  id="credentials_confirmation">Yes</span>
                                    </div>
                                </div>
                            </div>
                                    <br>
                            <div class="col-md-12" id="" style="margin-top: 20px;">
                                <div class="form-group" >
                                    <label for="" class="col-md-3" style="width:200px">Status</label>
                                    <div class="col-md-5">
                                        <input type="text" readonly value="Closed-not interested" class="form-control">
                                    </div>
                                </div>
                            </div>
                                    <br>
                            <div class="col-md-12" style="margin-top: 20px;">
                                <div class="form-group">
                                    <label for="" class="col-md-3" style="width:200px">Closure Reason <font color="red">*</font></label>
                                    <div class="col-md-5">
                                        <textarea name="closer_reason" id="closer_reason" rows="3" class="form-control" placeholder="Enter the closure reason"></textarea>
                                    </div>
                                </div>              
                            </div>

                            <div class="col-md-7" style="text-align: center;margin-top:20px;margin-bottom:10px;">
                                <button id="closer_submit_btn" class="btn btn-success" disabled style="border-radius:0.2rem" onclick="close_application_popup()">Submit</button>
                                <button class="btn btn-warning" style="border-radius: 0.2rem;" onclick="close_application()">Refresh</button>
                            </div>

                        </div>

                        <div class="card-body" id="joining_forms_card" style="display: none;">
                            <?php $this->load->view("admission/student_form/individual_application/_joining_forms_status.php"); ?>
                        </div>

                        <div class="card-body" id="edit_history_data" style="display: none;">
                        <div class="col-md-12" id="edit_history_table"></div>
                        </div>

                    </div>
                </div>


                <div class="modal fade" id="student_edit_by_user" data-backdrop="static" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
                            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                                <h4 class="modal-title" id="edit_columnName"></h4>
                                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
                            </div>
                            <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="save_form_student_data_by_user">
                                <input type="hidden" id="save_get_column_value" name="save_get_column_value">
                                <input type="hidden" id="profile_selection_tab">
                                <input type="hidden" id="old_data" name="old_data">
                                <div class="modal-body" id="edit_student_form_group">
                                </div>
                                <div class="modal-footer">
                                    <div class="form-group">
                                        <center>
                                            <a style="width: 10rem;" type="button" onclick="save_student_profile_by_user()" id="studentProfileSaveButton" class="btn btn-primary enterbuttonsave">Save</a>
                                            <button class="btn btn-secondary" style="width: 10rem;" data-dismiss="modal">Close</button>
                                        </center>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>


<div class="modal fade" id="add_edit_class_to_show_popup" tabindex="-1" role="dialog" style="width:50%;margin:auto;top:1%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title inline-block inline_class" id="modalHeader">Add Previous School Details for the year <span id="yearName"></span></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1; " type="button" class="close inline_class" data-dismiss="modal">&times;</button>
        </div>

        <form enctype="multipart/form-data" id="school_previous_form" class="form-horizontal" data-parsley-validate method="post">
            
            <div class="modal-body" style="">
                <div class="form-group">
                    <label class="control-label col-md-4">School Name </label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text"   name="schooling_school" id="schooling_school_new" class="form-control">
                            <input type="hidden"   name="old_school_name" id="old_school_name" class="form-control">
                        </div>
                        <div class="help-block">Write School Name Here</div>
                    </div>
                </div>
        

                <div class="form-group">
                    <label class="control-label col-md-4">School Address </label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text"  name="school_address"  id="school_address_new" class="form-control">
                            <input type="hidden"  name="old_school_address"  id="old_school_address" class="form-control">
                        </div>
                        <div class="help-block">Write School Address Here</div>
                    </div>
                </div>
                    
                <div class="form-group">
                    <label class="control-label col-md-4">Medium of instruction </label>
                    <div class="col-md-7">
                    <input type="hidden"  name="old_medium_of_instruction"  id="old_medium_of_instruction" class="form-control">
                        <select class="form-control"  name="medium_of_instruction" id="medium_of_instruction_new">
                            <option value="">Select Medium of Instruction</option>
                            <option value="English">English</option>
                            <option value="Kannada">Kannada</option>
                        </select>
                        <div class="help-block">Choose Instruction of Medium Here</div>
                    </div>
                </div>
                    
                <div class="form-group">
                    <label class="control-label col-md-4">Class </label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text"  name="schooling_class" id="schooling_class_new" class="form-control">
                            <input type="hidden"  name="old_schooling_class" id="old_schooling_class" class="form-control">
                        </div>
                        <div class="help-block">Write Class Semester Here</div>
                    </div>
                </div>
                    
                <div class="form-group">
                    <label class="control-label col-md-4">Registration No. </label>
                    <div class="col-md-7 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text"  name="registration_no" id="registration_no_new" class="form-control">
                        </div>
                        <div class="help-block">Add Registration Number</div>
                    </div>
                </div>
                    
                <div class="form-group">
                    <label class="control-label col-md-4">Board </label>
                    <div class="col-md-7">
                    <input type="hidden"  name="old_schooling_board" id="old_schooling_board" class="form-control">
                        <select class="form-control"  name="schooling_board" id="schooling_board_new" onchange="onchange_board()">
                        <option value="">Select Board</option>
                        <?php if($this->settings->getSetting('admission_board')) {
                                    foreach ($this->settings->getSetting('admission_board') as $key => $value) { ?>
                                        <option value="<?php echo $value; ?>"><?php echo $value; ?></option>
                                    <?php }
                            } else { ?>
                            <option value="CBSE">CBSE</option>
                            <option value="ICSE">ICSE</option>
                            <option value="STATE">STATE</option>
                            <option value="Home School">Home School</option>
                            <option value="IGCSE">IGCSE</option>
                            <option value="IB">IB</option>
                            <option value="NIOS">NIOS</option>
                            <option value="PU Board">PU Board</option>
                            <option value="ITI Board">ITI Board</option>
                            <option value="KSEEB">KSEEB</option>
                            <option value="Other">Other</option>
                            <?php }?>
                        </select>
                    </div>
                </div>
                <div id="board_other_show" style="display: none;">
                    <div id="board_other" class="form-group">
                        <label class="col-md-4 control-label" for="board_other">Others</label>  
                        <div class="col-md-7">
                            <input placeholder="Enter Please Specify"  id="b_other" name="board_other" type="text"  class="form-control input-md"  data-parsley-pattern="^[a-zA-Z. ]+$"  data-parsley-maxlength="6">
                            <span class="help-block">Please Specify</span>
                        </div>
                    </div>
                </div>


                    <div class="form-group">
                        <label class="col-md-4 control-label" for="passport_issued_place">Reason for Transfer/Withdrawal from School  </label>  
                        <div class="col-md-7">
                            <textarea class="form-control" name="transfer_reason_id" id="transfer_reason_id_new"></textarea>
                            <input type="hidden"  name="old_transfer_reason_id" id="old_transfer_reason_id" class="form-control">
                        </div>
                    </div>

                   
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="learning">Has Student Ever Been Expelled or Suspended from School?</label>
                            <div class="col-md-7"> 
                                <label class="radio-inline" for="expelled_or_suspended-0">
                                    <input  type="radio" name="expelled_or_suspended" id="expelled_or_suspended-0" value="Yes" onclick="show1()">
                                    Yes
                                </label>
                                <label class="radio-inline" for="expelled_or_suspended-1">
                                    <input type="radio" name="expelled_or_suspended"  id="expelled_or_suspended-1" value="No" checked onclick="hide0()">
                                    No
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="learning">Ratings for previous school</label>
                            <div class="col-md-7"> 
                                <label class="radio-inline" for="ratings-1">
                                    <input  type="radio" name="ratings" id="ratings-1" value="1">
                                    1
                                </label>
                                <label class="radio-inline" for="ratings-2">
                                    <input type="radio" name="ratings"  id="ratings-2" value="2">
                                    2
                                </label>
                                <label class="radio-inline" for="ratings-3">
                                    <input type="radio" name="ratings"  id="ratings-3" value="3">
                                    3
                                </label>
                                <label class="radio-inline" for="ratings-4">
                                    <input type="radio" name="ratings"  id="ratings-4" value="4">
                                    4
                                </label>
                                <label class="radio-inline" for="ratings-5">
                                    <input type="radio" name="ratings"  id="ratings-5" value="5">
                                    5
                                </label>
                            </div>
                        </div>
                       <div id="expelled_or_suspended_id" style="display: none; margin-bottom: 8px;">
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="expelled_or_suspended_description">Add Reason</label>
                                <div class="col-md-7"> 
                                    <input type="hidden"  name="old_expelled_or_suspended" id="old_expelled_or_suspended" class="form-control">
                                    <textarea class="form-control" name="expelled_or_suspended_description" id="expelled_or_suspended_description_new"></textarea>
                                </div>
                            </div>
                       </div>
                
                   
                <div class="form-group" id="attachmentReport" style="display:none;">
                    <label class="col-md-4 control-label" for="fileupload">Report card Attachment</label>
                    <div class="col-md-7" id="download_link">
                    </div>
                </div>
                <div id="wrapper" class="form-group">
                    <label class="col-md-4 control-label" for="fileupload">Report Card </label>  
                    <div class="col-md-7">
                        
                        <input class="form-control removeAttrFileUpload" id="fileupload"  name="report_card"  type="file" accept="application/pdf"/>
                        
                        <span class="help-block">Allowed file types - pdf; Allowed size - upto 2MB</span>
                        <span id="fileuploadError" style="color:red;"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="width: 100%;">
                <div class="inline_class2"> <button class=" btn btn-primary btn_style4" id="ClearFormButton" type="reset" onclick="">Clear Form</buton></div>
                <input type="button" id="save_school_previous_school" onclick="submit_previous_school_details()" class="btn btn-primary btn_style3" value="Save Pre School Details">
            </div>
        </form>

       
    </div>
</div>
<!-- new modal end -->
    <?php
    $this->load->view("admission/student_form/individual_application/_script.php");
    $this->load->view("admission/student_form/individual_application/_view_enquiry_details.php");
    $this->load->view("admission/student_form/individual_application/_create_enquiry_modal.php");
    ?>

<!-- Modal add new documents -->
<div class="modal fade" id="document_modals" tabindex="-1" role="dialog" style="width:50%;margin:auto;top:25%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Update your document here </h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
       <form enctype="multipart/form-data" id="document_upload_form" class="form-horizontal" data-parsley-validate method="post">
        <div class="modal-body">
            <input type="hidden" name="admission_form_id" value="<?php echo $afId ?>">
        <div class="col-md-12" style="margin-bottom: 2rem;">
                <div class="col-md-12">
                    <label for="name" class="col-md-4">Document Name<font color="red">*</font></label>
                    <div class="col-md-6">
                        <div class="input-group">
                            <select name="selected_document" class="form-control" id="selected_document" onchange="onselect_document()">
                            </select>
                        </div>
                        <div class="help-block">Select the document name</div>
                    </div>
                </div>


                <div class="col-md-12">
                    <label for="name" class="col-md-4">Document Type<font color="red">*</font></label>
                    <div class="col-md-6">
                        <div class="input-group">
                            <select name="int_ext" id="int_ext" class="form-control">
                                <option value="">Select Type</option>
                                <option value="Internal">Internal Document</option>
                                <option value="Student Document">Student Document</option>
                            </select>
                        </div>
                        <div class="help-block">Select Document type</div>
                    </div>
                </div>

                <div id="document_table" sty></div>
        </div>
        </div>
        <div class="modal-footer" style="margin-top: 20px !important;">
            <button type="button" style="border-radius:0.2rem" class="btn btn-primary" id="disabledBefore" onclick="upload_documents()">Submit</button>
        </div>
        </form>
    </div>
  </div>

<div id="verify_document" class="modal fade" role="dialog" style="width:85%;margin:auto;top:5%">
  <div class="modal-dialog">
    <div class="modal-content" style="height:90vh">
        <div class="modal-header">
        <h4 class="modal-title" id="verification_header">Document Verification</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <form action="">
              <input type="hidden" id="document_id" value="">
              <input type="hidden" name="" id="verification_status" value="">
        </form>
        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;">
          <div id="modal-loader" style="display: none;"></div>
          <div id="content">
            <iframe src="" width="100%" height="800px"> </iframe>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary"  onclick="document_verification_done('Verified')">Verified</button>     
          <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-warning"  onclick="document_verification_done('Rejected')">Reject</button>     
          <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>

  <div class="modal fade" id="link_toEnquiry_modal" tabindex="-1" role="dialog" style="width:100%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="width:70%;margin:auto;margin-top:2% ">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Link Enquiry</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;    
            </button>
        </div>


        <div class="modal-body">
        <form id="link_toEnquiry_form">
            <div class="col-md-12">
                <div class="form-group" style=" display: flex; justify-content: center;">
                    <label class="col-md-2">Search Enquiry By</label>
                    <div class="col-md-4">
                        <select id="search_value" class="form-control"  name="search_value" onchange="search_enquiry()" required="">
                            <option value="">Select</option>
                            <option id="search_by_name" value="">Name</option>
                            <option id="search_by_email" value="">Email</option>
                            <option id="search_by_mobnumber" value="">Mobile Number</option>
                        </select>
                    </div>
                    <!-- <div class="col-md-2">
                    <button class="btn btn-primary" type="button" onclick="search_enquiry()">Search</button>
                    </div> -->
                </div>
                <input id="mother_email" name="mother_email" type="hidden" value=''>
                <input id="mother_mobilNo" name="mother_mobilNo" type="hidden" value=''>
            </div>
            <div class="col-md-12" style="padding-left:0px;padding-right:0px;margin:20px 0px; display:none" id="enquiry_view_details">
            </div>
        </form>
        </div> 
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="create_btn"  onclick="create_enquiry()">Create</button> 
            <button class="btn btn-secondary" type="button" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>

<!-- Modal -->
<div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="documentModalLabel">Document Viewer</h5>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;    
            </button>
      </div>
      <div class="modal-body">
        <!-- Document content will be loaded here -->
        <iframe id="documentFrame" src="" style="width: 100%; height: 500px; border: none;"></iframe>
      </div>
      <div class="modal-footer">
      <button class="btn btn-secondary" type="button" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>



  <div class="modal fade" id="upload_document" tabindex="-1" role="dialog" style="width:100%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="width:45%;margin:auto;margin-top:2% ">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="document_header">Re Upload</h4>
        </div>
        <div class="modal-body">
        <form id="upload_document_form">
            <input type="hidden" name="af_id" id="af_id">
            <input type="hidden" name="upload_document_id" id="upload_document_id">
            <input type="hidden" name="reupload_document_name" id="reupload_document_name">
            <input type="hidden" name="view_type" id="view_type">
            <div class="col-md-12" id="document_modal_body">
                
            </div>
        </form>
        </div> 
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="re_upload_btn"  onclick="upload_admission_documents()">Upload</button> 
            <button class="btn btn-secondary" type="button" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
  
    <div class="modal fade" id="change_acad_year_modal" tabindex="-1" role="dialog" style="width:100%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
        <div class="modal-content modal-dialog" style="width:45%;margin:auto;margin-top:2% ">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="document_header">Change Academic Year</h4>
            </div>
            <div class="modal-body">
            <form id="upload_document_form">
                <div class="col-md-12" id="document_modal_body">
                <select class="form-control" id="acad_year_id">
                    <?php if(!empty($academic_years)){ ?>
                        <?php foreach($academic_years as $key => $val) { 
                            $selected = '';
                            if($val->id == $final_preview->academic_year_applied_for) 
                            $selected = 'selected'; ?>
                            <option value="<?= $val->id ?>" <?php echo $selected; ?>><?= $val->acad_year ?></option>
                        <?php } ?>
                    <?php } ?> 
                </select>
                </div>
            </form>
            </div> 
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="re_upload_btn"  onclick="change_acadYear()">Save</button> 
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>

    <div id="send_payment_link_to_parent" class="modal fade" role="dialog">
        <div class="modal-dialog" style="width:60%;margin: auto;">

            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Send mail to Parent</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form enctype="multipart/form-data" method="post" id="send_link_as_email"  class="form-horizontal" >
                <div id="" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                    <table class="table" id="payment_link_email_content" width="100%">

                    </table>
                </div>
                <div class="modal-footer">
                    <input type="button" id="confirmBtn" onclick="send_link_as_email()" class="btn btn-success" value="Confirm">
                    <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
                </form>
            </div>
        </div>
    </div>

  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>

  <style type="text/css">
    .new_circleShape_res {
    padding: 6px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 16px;
    height: 30px !important;
    width: 30px !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

    .widthadjust {
        width: 600px;
        margin: auto;
    }

    .green_button {
        border-color: #36d236;
        border-width: 2px;
    }
    .green_icon {
        color: #36d236;
    }
    .red_button {
        border-color: red;
        border-width: 2px;
    }
    .red_icon {
        color: red;
    }
    .dropbtn {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    font-size: 16px;
    border: none;
    cursor: pointer;
    }

/* Style the dropdown content (hidden by default) */
    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #f9f9f9;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        z-index: 1;
        right: 0;
        top:37px;
        border: 1px solid white;
        cursor: pointer;
        border-radius: 8px;
    }

    /* Show the dropdown content when the dropdown button is hovered over */
    /* .dropdown:hover .dropdown-content {
        display: block;
    } */

    /* Style the dropdown links */
    .dropdown-content a {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
    }

    /* Change color on hover */
    .dropdown-content a:hover {
        background-color: #ddd;
    }
</style>




    <style type="text/css">
        .profile {
            background: #fff;
        }

        .breadcrumb li:last-child {
            background: none;
        }
        .list-group-item+.list-group-item{
                border-top-width: thin;
        }
        .list-group button{
            text-align: left;
        }
        .bootbox-close-button{
            display: none;
        }
    </style>
    
<script type="text/javascript">
    
    function search_enquiry(){
        var form = $('#link_toEnquiry_form');
        $('#enquiry_view_details').html('');
        var search_text = $("#search_value option:selected").html();
        var search_value = $('#search_value').val();
        if(search_value ==''){
            return false;
        }
        var mother_email = $('#mother_email').val();
        var mother_mobilNo = $('#mother_mobilNo').val();
        $.ajax({
            url: '<?php echo site_url('Admission_process/search_enquiry'); ?>',
            type: 'post',
            data: {
               'search_value':search_value,'search_text':search_text,'mother_email':mother_email,'mother_mobilNo':mother_mobilNo
            },
            success: function(data) {
                var enquiry_data = $.parseJSON(data);
                $('#enquiry_view_details').html(show_enquiry_details(enquiry_data));
                $('#enquiry_view_details').show();
                get_status();
            }
        });
       
    }

    function show_enquiry_details(enquiry_data){
        var html = '';
        if(enquiry_data == ''){
          
             html +='<div class="col-md-12" style=" display: flex; justify-content: center;"><h5>No enquiry details.Create enquiry.</h5></div>';   
        }else{
          
            html +=` <div class="col-md-12">
                        <table id="" class="table table-bordered">
                        <thead>
                            <tr>
                            <th width="5%">#</th>
                            <th>Student name</th>
                            <th >Grade</th>
                            <th >DOB</th>
                            <th >Parent name</th>
                            <th>Mobile No</th>
                            <th >Email Id</th>
                            <th >Status</th>
                            <th >Action</th>
                            </tr>
                        </thead>
                `;
                html += `<tbody>`;
                    for(var i=0;i<enquiry_data.length;i++){
                        var data = enquiry_data[i];
                        var dob = data['student_dob'];
                        if(dob == null){
                           dob = '-';
                        }
                        html+=`
                                <tr>
                                <td>${i+1}</td>
                                <td>${data['student_name']}</td>
                                <td>${data['grade']}</td>
                                <td>${dob}</td>
                                <td>${data['parent_name']}</td>
                                <td>${data['mobile_number']}</td>
                                <td>${data['email']}</td>
                                <td>${data['status']}</td>
                                <td>
                                    <a class="btn btn-info" onclick="link_to_enquiry(${data['id']})" style="margin-top:5px">Link</a></td>
                                </tr>`;
                    }
                    html+=`</tbody>
                        </table>
                        </div>
                        <div class="col-md-12" style="margin-top:10px">
                        <label class="col-md-2">Select status <font color="red">*</font></label>
                        <div class="col-md-4">
                            <select class="form-control status_class" id="status_class"> </select>
                            </div>
                        </div>
                       
                        `;
                   
        }
       return html;
    }

    function get_status() {
        var pickfromtableAdmStatus = '<?php echo $this->settings->getSetting('admission_pick_status_from_table') ?>';
        var enquiry_follow_up_status = '<?php echo json_encode($enquiry_follow_up_status) ?>';
        var enquiry_follow_up_status_Arr = $.parseJSON(enquiry_follow_up_status);
        if(!pickfromtableAdmStatus){
            $('.status_class').html(_construct_status_config(enquiry_follow_up_status_Arr));
          }else{
            $('.status_class').html(_construct_status(enquiry_follow_up_status_Arr));
        }
    }

        function _construct_status_config(enquiry_follow_up_status_Arr){
        var defaultStatus = ['Submitted','Application Amount Paid','Admit','Student added to ERP','Rejected','Draft','Duplicate'];
        var mergeData = $.merge( enquiry_follow_up_status_Arr, defaultStatus )
        var status_output = '';
        status_output +='<option value="">Select status</option>';
        for(i=0;i<mergeData.length;i++){
        status_output +='<option  value="'+mergeData[i] +'">'+mergeData[i]+'</option>';
        }
        return status_output;
    }

    function _construct_status(enquiry_follow_up_status_Arr){
        var status_output = '';
        status_output +='<option value="">Select status</option>';
        for(i=0;i<enquiry_follow_up_status_Arr.length;i++){
        status_output +='<option value="'+enquiry_follow_up_status_Arr[i] +'">'+enquiry_follow_up_status_Arr[i]+'</option>';
        }
        return status_output;
    }


    function link_to_enquiry(enquiry_id){
        var status = $('#status_class').val();
        if(status == ''){
            alert('Select Status');
            return false;
        }
        var adm_id = '<?php echo $afId ?>';
        // alert(enquiry_id);
        $.ajax({
            url: '<?php echo site_url('Admission_process/link_to_enquiry'); ?>',
            type: 'post',
            data: {
              'enquiry_id':enquiry_id,'adm_id':adm_id,'status':status
            },
            success: function(data) {
                var data = $.parseJSON(data);
                if(data == 1){
                    $('#link_toEnquiry_modal').modal('hide');
                }
            },
            complete:function(){
                window.location.reload();
                // Application_Details();
            }
        });

    }

    function create_enquiry(){
        $('#link_toEnquiry_modal').modal('hide');
        $('#_create_enquiry_modal_box').modal('show');
    }

    $('#_view_enquiry_details_modal_box').on('show.bs.modal', function (event) {
        var adm_id = '<?php echo $afId ?>';
        $.ajax({
            url: '<?php echo site_url('Admission_process/show_enquiry_details_by_id'); ?>',
            type: 'post',
            data: {
              'adm_id':adm_id
            },
            success: function(data) {
                var data = $.parseJSON(data);
                $('#student_name').val(data.student_name);
                $('#gender').val(data.gender);
                $('#dob').val(data.student_dob);
                $('#class_name').val(data.class_name);
                $('#parent_name').val(data.parent_name);
                $('#mobile_number').val(data.mobile_number);
                $('#email_id').val(data.email);
                $('#status').val(data.status);
            }
        });
    });

    function show1() {
        $("#expelled_or_suspended_id").show();
    }

    function hide0() {
        $("#expelled_or_suspended_description_new").val('');
        $("#expelled_or_suspended_id").hide();
    }

    function onchange_board() {
            if($('#schooling_board_new').find(":selected").text() == 'Other' ) {
                $("#board_other_show").show();
            }
            else {
                $("#b_other").val('');
                $("#board_other_show").hide();
            }
    }

    $("#fees_tab").click(function() {
        if ('<?php echo $follow_admission->curr_status ?>' == 'Offer Released') {
        $("#fees_tab span.round-tab").css('background', '#2196f3');
        $("#credentials_tab span.round-tab").css('background', 'none');
        }
    });

    $("#credentials_tab").click(function() {
        if ('<?php echo $follow_admission->curr_status ?>' == 'Offer Released') {
        $("#credentials_tab span.round-tab").css('background', '#2196f3');
        $("#fees_tab span.round-tab").css('background', 'none');
        }
    });
    $(document).ready(function() {
        if ('<?php echo $follow_admission->curr_status ?>' == 'Offer Released') {
            $("#credentials_tab span.round-tab, #fees_tab span.round-tab").css('background', 'none');
            $("#fees_tab").removeAttr('class', 'cls1');
            $("#credentials_tab").removeAttr('class', 'cls1');
        }
    });

    function upload_documents() {
        var $form = $('#document_upload_form');
        if ($form.parsley().validate()) {
            $('#disabledBefore').html('Please wait').attr('disabled', 'disabled');
            var form = $('#document_upload_form')[0];
            var formData = new FormData(form);
            $.ajax({
                url: '<?php echo site_url('admission_process/save_admission_documents'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                // async: false,
                success: function(data) {
                    parsed_data = $.parseJSON(data);
                    $('#disabledBefore').html('Submit').removeAttr('disabled');
                    $('#document_modals').modal('hide');
                    const modal = $('#document_modals');
                    modal.find('form')[0]?.reset();
                    $('#document_modals').modal('hide');
                    $('#document_table').html('');
                    get_admission_document_by_id();
                },
            });
        }
    }

    function disabled_fn() {
        $("#doc-upload").removeAttr('disabled');
    }

    function upload_document_file_path() {
        var file = event.target.files[0];
        completed_promises = 0;
        current_percentage = 0;
        total_promises = 1;
        in_progress_promises = total_promises;
        saveFileToStorage(file);
        $('.file-preview').css('opacity', '0.3');
    }
    function saveFileToStorage(file) {
        $('#percentage_doc_completed').show();
        $('#doc-upload').attr('disabled', 'disabled');
        $("#document_submit").prop('disabled', true);
        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'post',
            data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
            },
            success: function(response) {
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                "Content-Type": file.type,
                "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                var xhr = $.ajaxSettings.xhr();
                xhr.upload.onprogress = function(e) {
                    // For uploads
                    if (e.lengthComputable) {
                    single_file_progress(e.loaded / e.total * 100 | 0,);
                    }
                };
                return xhr;
                },
                success: function(response) {
                // return false;
                $('#percentage_doc_completed').hide();
                update_admission_student_documents(path);
                $('#doc-upload').removeAttr('disabled');
                $('.file-preview').css('opacity', '1');
                $("#document_submit").prop('disabled', false);
                
                },
                error: function(err) {
                reject(err);
                }
            });
            },
            error: function(err) {
            reject(err);
            }
        });

    }

    function single_file_progress(percentage, ) {
        if (percentage == 100) {
            in_progress_promises--;
            if (in_progress_promises == 0) {
            current_percentage = percentage;
            }
        } else {
            if (current_percentage < percentage) {
            current_percentage = percentage;
            }
        }
        $("#percentage_doc_completed").html(`${current_percentage} %`);
        return false;
    }

    function update_admission_student_documents(path) {
        var af_id = '<?php echo $afId ?>';
        var doc_name= $("#int_ext").val();
        var doc_type= $("#docu_name").val();
        $.ajax({
            url: '<?php echo site_url('admission_process/upload_new_documents'); ?>',
            type: 'post',
            data: {'path':path,'doc_name':doc_name, 'doc_type': doc_type, 'af_id': af_id, },
            success: function(data) {
                var docrowId = data.trim();
                if (docrowId !=0) {
                    $('#disabledBefore').removeAttr('disabled');
                    $('#doc-upload').attr('disabled','disabled');
                    var html ='';
                    html += '<a style="margin-top: 1rem;" id="successmessageId" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>'
                    html += '<a style="margin-top: 1rem;" onclick="deletedocument_row_new('+docrowId+')" id="removeButtonId" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                    $('#afterSuccessUploadShow').html(html);
                }
                    $("#remove_button").hide();
            }
        });
    }                   
    
    function download_application_form(admissionId, au_id, admission_setting_id) {
        $.ajax({
            url: '<?php echo site_url('admission_process/check_application_form_staff'); ?>',
            type: 'POST',
            data:{'admissionId': admissionId},
            success: function(data) {
                var successData = data.trim();
                    if (successData == 1) {
                    var downloadUrl = '<?php echo site_url('admission_process/download_application_form_staff/'); ?>'+admissionId;
                    window.location.href = downloadUrl;
                }
                // else{
                //     generate_application_form(admissionId,au_id,admission_setting_id);
                // }
            }
        })
    }

 
    function generate_application_form(admissionId, au_id, admission_setting_id) {
        $('#regen_application').prop('disabled',true).html('Please wait..');
        $.ajax({
            url: '<?php echo site_url('admission_process/re_generate_individual_application'); ?>',
            type: 'POST',
            data:{'admissionId': admissionId,'au_id':au_id, 'admission_setting_id':admission_setting_id},
            success: function(data) {
                var data = JSON.parse(data);
                $('#regen_application').prop('disabled',false).html('Generate');
				window['wait_timer_' + admissionId] = setInterval(function() { check_pdf_application_generated(data.pdf_path, data.pdf_relative_path, admissionId) }, 500);
            }
        });
    }

    function check_pdf_application_generated(pdf_path, pdf_check_path, admissionId){
        $.ajax({
            url: '<?php echo site_url('admission_process/check_pdf_individual_application'); ?>',
            type: 'POST',
            data:{'pdf_path': pdf_path,'pdf_check_path':pdf_check_path, 'admissionId':admissionId},
            success: function(data) {
                if(data == 1){
                    clearInterval(window['wait_timer_' + admissionId]);
                    $('#regen_application').prop('disabled',false).html('Re Generate');
                    $('#download_app').prop('disabled',false);
                    $(function(){
                    new PNotify({
                      title: 'Success',
                      text:  'Generated Successfully',
                      type: 'success',
                    });
                  });
                }
            }
        });
    }

    function enable_to_parent_reupload_application() {
        var emailtemplateId = '<?php echo (!empty($revert_admission_email_template)) ?  $revert_admission_email_template : '' ?>';
        console.log(emailtemplateId);
        var dialogHtml = '<h4>If the submission is reverted, parent will able to edit application and resubmit. do you want continue?</h4><br><label>';
        if(emailtemplateId != ''){
            dialogHtml +='<input type="checkbox" id="sendEmailRevertSubmission"> Send Email to Parent</label>';
        }
        bootbox.confirm({
            title: "Revert submission",
            message: dialogHtml,
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function (result) {
                if(result) {
                    if($('#sendEmailRevertSubmission').is(':checked')){
                        enabled_email_dialog_box();
                    }else{
                        revert_submission_move_to_draft_application();
                    }
                    
                }
            }
        });
    }

    function enabled_email_dialog_box(){
        $('#rever_submission_email_preview').modal('show');
        get_email_template_content();
    }

    function revert_submission_move_to_draft_application(){

        var af_id = '<?php echo $afId ?>';
        var f_mobile_no = '<?php echo $final_preview->f_mobile_no ?>';
        var m_mobile_no = '<?php echo $final_preview->m_mobile_no ?>';
        $.ajax({
            url: '<?php echo site_url('admission_process/move_to_draft_application'); ?>',
            type: 'post',
            data: {'af_id':af_id,'f_mobile_no':f_mobile_no,'m_mobile_no':m_mobile_no},
            success: function(data) {
                if(data){
                    $(function(){
                        new PNotify({
                            title: 'Success',
                            text:  'Successfully move to draft',
                            type: 'success',
                        });
                    });
                }else{
                    $(function(){
                        new PNotify({
                            title: 'Error',
                            text:  'Something went wrong',
                            type: 'error',
                        });
                    });
                }
                location.reload();
            }
            
        });
    }
    function submit_admission_revert_email() {
        var form = $('#revert_submission_email_form');
        if (!form.parsley().validate()) {
            return 0;
        }
        //Disable the create button
        $("#submitbuttonRevert").prop('disabled',true);
        $("#submitbuttonRevert").html('Please wait...');
        var form = $('#revert_submission_email_form')[0];
        var email_revert_content = $('#template_content').code();
        var formData = new FormData(form);
        formData.append('email_revert_content',email_revert_content);
        $.ajax({
            url: '<?php echo site_url('admission_process/revert_submission_email_to_parent/'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            async: false,
            success: function(data) {
                revert_submission_move_to_draft_application();
            },
            error: function (err) {
                console.log(err);
            }
            
        });
  }

    function change_acadYear() {
        var af_id = '<?php echo $afId ?>';
        var acad_year_id = $('#acad_year_id').val();
        bootbox.confirm({
            title: "Change the Acad Year",
            message: "Do you want to change Acad year of this student ?",
            className: "medium",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
             },
            callback: function (result) {
                if(result) {
                    $.ajax({
                        url: '<?php echo site_url('admission_process/change_acadYear'); ?>',
                        type: 'post',
                        data: {'af_id':af_id,'acad_year_id':acad_year_id},
                        success: function(data) {
                            if(data){
                                $(function(){
                                    new PNotify({
                                        title: 'Success',
                                        text:  'Changed year changed',
                                        type: 'success',
                                    });
                                });
                            }else{
                                $(function(){
                                    new PNotify({
                                        title: 'Error',
                                        text:  'Something went wrong',
                                        type: 'error',
                                    });
                                });
                            }
                            window.location.href='<?php echo site_url('admission_process/application_view') ?>';
                        }
                    });
                }
            }
        });
    }
    
</script>




<div id="rever_submission_email_preview" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title"> Revert Submission Email to Parent</h4>
            </div>
            <form method="post" class="form-horizontal" id="revert_submission_email_form" data-parsley-validate="">
                <input type="hidden" name="email_admission_form_id" value="<?php echo $afId ?>" >
                <div id="email_content_body" class="modal-body" style="overflow-y:auto;max-height:500px; ">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger pull-right mr-1" style="width: 10rem;" data-dismiss="modal" >Close</button>
                    <button type="button" id="submitbuttonRevert" style="width: 10rem;" onclick="submit_admission_revert_email()" class="btn btn-primary pull-right">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="moved_students_acad_year_change" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title">Academic year change </h4>
            </div>
            <form method="post" class="form-horizontal" id="revert_submission_email_form" data-parsley-validate="">
                <input type="hidden" name="student_admid" id="student_admid">
                <div id="body_content" class="modal-body" style="overflow-y:auto;max-height:500px;">
                    <div class="col-md-12">
                        <div class="col-md-4">
                            <select class="form-control" id="academic_year_move" name="academic_year" onchange="get_classes()" style="margin-top:0px">
                                <option value="">Select Year</option>
                                <?php if(!empty($academic_years)){ ?>
                                    <?php foreach($academic_years as $key => $val) { 
                                        $selected = '';
                                        if($val->id == $final_preview->academic_year_applied_for) 
                                        $selected = 'selected'; ?>
                                        <option value="<?= $val->id ?>" <?php echo $selected; ?>><?= $val->acad_year ?></option>
                                    <?php } ?>
                                <?php } ?> 
                            </select>
                        </div>

                        <div class="col-md-4" id="">
                            <select class="form-control" id="classID" onchange="get_Classsection()" name="class_move_id">
                            </select>
                        </div>

                        <div class="col-md-4" id="">
                            <select class="form-control" id="class_section_ID" name="class_section_move_id">
                            </select>
                        </div>
                    </div>
                </div>
                <div id="body_content_error" class="modal-body" style="overflow-y:auto;max-height:500px;display:none">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger pull-right mr-1" style="width: 10rem;" data-dismiss="modal" >Close</button>
                    <button type="button" id="submitbuttonRevert" style="width: 10rem;" onclick="change_acad_year()" class="btn btn-primary pull-right">Change</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function get_email_template_content() {
        var emailtemplateId = '<?php echo (!empty($revert_admission_email_template)) ?  $revert_admission_email_template : '' ?>';
        if (emailtemplateId=='') {
            $('#email_content_body').html('');
        }
        var afId = '<?php echo $afId ?>';
        $.ajax({
            url: '<?php echo site_url('admission_process/get_admission_email_content_revert'); ?>',
            type: 'POST',
            data: {'afId':afId,'emailtemplateId':emailtemplateId},
        })
        .done(function(data){
            console.log(data);
            if (data==0) {
                $('#email_content_body').html('');
                return false;
            }
            var reData = $.parseJSON(data);
            console.log(reData);
            construct_emails_content_body(reData, afId);
        });
    }

    function construct_emails_content_body(reData, afId) {
    
        var emailSubject = reData.email_subject;
        if (reData.email_subject == undefined)
        emailSubject = 'Email subject not added';

        var registered_email = reData.registered_email;
        if (reData.registered_email ==undefined) 
        registered_email = 'From email not assigned';
        var content = reData.content;
        content = content.replace('%%student_name%%',reData.to_email.student_name);
        // console.log(content);
        var display = 'block';
        if (reData.content == undefined) 
        display = 'none';

        var html = '';
        html +='<div class="form-group">';
        html +='<label class="col-md-3 control-label">From: </label>';
        html +='<div class="col-md-9">';
        html +='<input readonly type="text" value="'+registered_email+'" name="registered_email" id="registered_email" class="form-control">';
        html +='</div>';
        html +='</div>';

        html +='<div class="form-group">';
        html +='<label class="col-md-3 control-label">To: </label>';
        html +='<div class="col-md-9">';
        html +='<input type="text" name="to_mails" id="to_emails" class="form-control" value="'+reData.to_email.fmail+','+reData.to_email.mMail+'">';
        html +='<span class="help-block">To send email to multiple recipients, enter their email ids as a comma-separated list. Eg: <EMAIL>, <EMAIL></span>';
        html +='</div>';
        html +='</div>';

        html +='<div class="form-group">';
        html +='<label class="col-md-3 control-label">Subject: </label>';
        html +='<div class="col-md-9" style="margin-bottom:12px;">';
        html +='<input type="text" value="'+emailSubject+'" name="email_subject" id="email_subject" class="form-control">';
        html +='</div>';
        html +='</div>';
        html +='<div class="form-group" style="display:'+display+'">';
        html +='<label class="col-md-3 control-label">Message: </label>';
        html +='<div class="col-md-9">';
        html +='<textarea name="template_content" id="template_content" class="summernote template">'+content+'</textarea>';
        html +='</div>';
        html +='</div>';
        $('#email_content_body').html('');
        $('#email_content_body').html(html);
        $('.template').summernote({
        tabsize: 2,
        height: 200
        });
    }
</script>

<style>
  @media print {
    @page {
      size: landscape; /* or 'portrait' */
    }
  }
</style>

<style>
    /* Print-specific styles */
    @media print {
    /* Ensure borders are visible */
    #printArea {
      border: 2px solid black; /* Example for print area border */
      padding: 10px; /* Optional: Add some padding for visual clarity */
    }

    /* Apply to tables, divs, or any other element with borders */
    table, th, td {
      border: 1px solid black !important; /* Use !important to force border visibility */
      border-collapse: collapse; /* Ensure borders are not duplicated */
    }

    /* Ensure the body prints background colors and borders correctly */
    body {
      -webkit-print-color-adjust: exact; /* Ensures colors/borders print correctly in Chrome/Safari */
      print-color-adjust: exact; /* Ensures colors/borders print correctly in Firefox/Edge */
    }

    /* Optional: Define margins or page size */
    @page {
      size: auto; /* Let the browser decide page size */
      margin: 10mm; /* Set custom margins for printing */
    }
  }
</style>


<div class="modal fade" id="" tabindex="-1" role="dialog" style="width:100%;" data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="width:70%;margin:auto;margin-top:2% ">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">View</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;    
            </button>
        </div>

        <div class="modal-body">
            <iframe src="" width="100%" height="800px"></iframe>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
</div>