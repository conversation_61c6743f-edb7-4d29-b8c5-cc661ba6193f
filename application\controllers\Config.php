<?php
class Config extends CI_Controller {         
	function __construct() { 
    parent::__construct();
    
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }

    if (!$this->authorization->isAuthorized('SCHOOL.ACAD_YEAR_CHANGE')) {
      redirect('dashboard', 'refresh');
    }

    $this->load->model('Config_model', 'cmodel');
    $this->load->library('filemanager');
  }

  public function login() {
    if ($this->session->userdata('admin_logged_in') && $this->session->userdata('otp_verified')) {
        redirect('config');
    } else {
          $data['main_content'] = 'config/login';
        $this->load->view('inc/template', $data);
    }
  }


  public function logout() {
    $this->session->unset_userdata('loginstatus');
    $this->session->unset_userdata('user_id');
    $this->session->unset_userdata('email');
    $this->session->unset_userdata('otp_verified');
    redirect('config/login');
  }

  public function send_otp() {
    $input = $this->input->post();
    $otp = rand(100000,999999);
    $smsint = $this->settings->getSetting('smsintergration');
    $msg = $otp. ' is your One Time Password for verification as an applicant for registration-Nextelement';
    $from_name = $this->settings->getSetting('school_name');
    $emailMessage = '
            Dear Applicant,
            <br>
            Your OTP for Config Management Login is '.$otp.'
            <br>
            Thanks and Regards, <br>
            -'.$from_name.'';
    $emailId = $input['email'];
    $data = array(
        'send_unique_emails' => 1,
        'message' => [$emailMessage],
        'subject' => 'OTP for Config Management',
        'mail_username' => ['NextElement'],
        'email_ids' => [$emailId],
        'template' => 'Admission OTP'
    );
    $res = $this->__email_one_time_verification_application($data);
    if($res) {
        $this->cmodel->insertOTP($input, $otp);
        echo json_encode(['status' => 'ok','msg' => 'Email Sent!']);
    } else {
        echo json_encode(['status' => 'error','msg' => 'Unable to send Email please try again!' ]);
    }
  }
  
  public function verify_otp() {
    $input = $this->input->post();

    if ($this->cmodel->verifyOTP($input)) {
        $this->session->set_userdata('admin_logged_in', true);
        $this->session->set_userdata('otp_verified', true);
        $this->session->set_userdata('email', $input['email']);
        $this->session->set_userdata('name', $input['name']);
        $this->session->set_userdata('last_activity', time());

        echo json_encode(['status' => 'ok']);
    } else {
        echo json_encode(['status' => 'error', 'msg' => 'Incorrect OTP. Please try again.']);
    }
  }

  public function index(){
    if(ENVIRONMENT !== 'development'){
      if (!$this->session->userdata('admin_logged_in') || !$this->session->userdata('otp_verified')) {
        redirect('config/login');
      }
      $timeout = 1800;
      if ($this->session->userdata('last_activity') && time() - $this->session->userdata('last_activity') > $timeout) {
        $this->session->unset_userdata('loginstatus');
        $this->session->unset_userdata('user_id');
        $this->session->unset_userdata('email');
        $this->session->unset_userdata('otp_verified');
        redirect('config/login');
      }
      $this->session->set_userdata('last_activity', time());
      $data['email'] = $this->session->userdata('email');
      $data['name'] = $this->session->userdata('name');
    } else {
      $data['email'] = '<EMAIL>';
      $data['name'] = 'Dev User';
    }
    $data['main_content'] = 'config/index';
    $this->load->view('inc/template', $data);
  }

  public function load_data() {
    $dbConfig = $this->cmodel->getDBData();
    // echo '<pre>'; print_r($dbConfig); die();
    $templateConfig = $this->cmodel->getTemplateConfigData();

    //Compare config values with database values
    foreach($templateConfig as &$con){
      $found = 0;
      foreach($dbConfig as $sd){
        if($con->name == $sd->name){
          $found = 1;
          break;
        } 
      }
      if ($found) {
        // $con->date = $sd->added_date;
        $con->group = ucwords($con->group);
        $con->isConfigInDB = 1;

        if (!isset($con->description))
          $con->description = 'No Description';

        switch ($con->type) {
          case 'multiple':
            //If the config value is of type 'multiple', need to separate out enabled and disabled options
            $returnVal = $this->__separateOutOptions($con->options, $sd->value);
            $con->disabledOptions = $returnVal->disabledOptions;
            $con->enabledOptions = $returnVal->enabledOptions;
          break;
          case 'json':
          case 'string':
          case 'select':
          case 'array':
          case 'image':
          case 'boolean':
            $con->value = $sd->value;
            break;
        }        
      } else {
        $con->isConfigInDB = 0;
        switch ($con->type) {
          case 'multiple':
            //If the config value is of type 'multiple', need to separate out enabled and disabled options
            $con->disabledOptions = $con->options;
            $con->enabledOptions = array();
          break;
          case 'json':
          case 'string':
          case 'array':
            break;
        } 
      }
    }
    // echo '<pre>'; print_r($templateConfig); die();

    echo json_encode($templateConfig);
  }

  private function __separateOutOptions($templateOptions, $dbOptions) {
    $enabledOptions = array();
    $disabledOptions = array();
    foreach ($templateOptions as $tOpt) {
      $found = 0;
      $dbOptions_array = json_decode($dbOptions);
      if(!empty($dbOptions_array)){
        foreach ($dbOptions_array as $dOpt) {
          if ($tOpt ==  $dOpt) {
            $found = 1;
            break;
          }
        }
      }
     
      if ($found) {
        $enabledOptions [] =$tOpt;
      } else {
        $disabledOptions [] = $tOpt;
      }
    }

    $returnVal = new stdClass();
    $returnVal->enabledOptions = $enabledOptions;
    $returnVal->disabledOptions = $disabledOptions;

    return $returnVal;
  }

  public function updateSingleType(){
    $name = $_POST['name'];
    $type = $_POST['type'];
    $value = $_POST['newValue'];
    $email = $_POST['email'];
    $person_name = $_POST['person_name'];

    $before_update = $this->cmodel->before_update($name);
    $result = $this->cmodel->upsertToConfig($name, $value, $type);
    if ($result) {
      $status = "Config '{$name}' updated successfully from '{$before_update}' to '{$value}'.";
    }
    $history_result = $this->cmodel->insert_into_history($name, $email, $person_name, $status);

    // if ($result) {
    //   $this->session->set_flashdata('flashSuccess', 'Name/Value pair successfully added/updated');
    // } else {
    //   $this->session->set_flashdata('flashError', 'Something Wrong..');
    // }
    echo $result;
  }

  public function deleteConfig(){
    $name= $_POST['name'];
    $email = $_POST['email'];
    $person_name = $_POST['person_name'];

    $before_update = $this->cmodel->before_update($name);
    $result = $this->cmodel->deleteConfig($name);
    if ($result) {
      $status = "Config '{$name}' deleted successfully.";
    }
    $history_result = $this->cmodel->insert_into_history($name, $email, $person_name, $status);

    // if ($result) {
    //   $this->session->set_flashdata('flashSuccess', 'Name/Value pair successfully removed');
    // } else {
    //   $this->session->set_flashdata('flashError', 'Something Wrong..');
    // }
    echo $result;
  }

  public function updateMultipleType(){
    if(!isset($_POST['configName'])){
      redirect('config', 'refresh');
    }
    $data['configName'] = $_POST['configName'];
    $data['enabledOptions'] = json_decode($_POST['enabledOptions']);
    $data['options'] = json_decode($_POST['options']);
    $data['email'] = $this->session->userdata('email');
    $data['name'] = $this->session->userdata('name');
    $data['main_content'] = 'config/editMultipleTypeProps';
    $this->load->view('inc/template', $data);
  }

  public function append(){
    $new_module_string = json_encode($_POST['new_modules']);
    $name = $_POST['name'];
    $email = $_POST['email'];
    $person_name = $_POST['person_name'];

    $before_update = $this->cmodel->before_update($name);
    $result = $this->cmodel->upsertToConfig($name, $new_module_string, 'multiple'); 
    if ($result) {
      $status = "Config '{$name}' updated successfully from '{$before_update}' to '{$new_module_string}'.";
    }
    $history_result = $this->cmodel->insert_into_history($name, $email, $person_name, $status);
    $this->session->set_flashdata('flashSuccess', 'Selected Modules Enabled Successfully.');
    redirect('config');
    // if($result){
    //   redirect('config');
    // }else{
    //   $this->session->set_flashdata('flashError', 'Something went Wrong.');
    // }
  }

	public function switchValue(){
    $name = $_POST['name'];
    $newValue =($_POST['CurrentValue']?0:1);
    $email = $_POST['email'];
    $person_name = $_POST['person_name'];
    
    $before_update = $this->cmodel->before_update($name);
    $result = $this->cmodel->upsertToConfig($name, $newValue, 'boolean');
    if ($result) {
      $status = "Config '{$name}' updated successfully from '{$before_update}' to '{$newValue}'.";
    }
    $history_result = $this->cmodel->insert_into_history($name, $email, $person_name, $status);
    // if ($result) {
    //   $this->session->set_flashdata('flashSuccess', 'Value Updated Successfully.');
    // } else {
    //   $this->session->set_flashdata('flashError', 'Something Wrong..');
    // }
    echo $result;
  }
  
  public function acad_year_config() {    
    $data['main_content'] = 'config/acad_year_config';
    $this->load->view('inc/template', $data);
  }

  public function changeAcadYear() {
    $year = $_POST['academic_year'];
    $promationyearID = $year+1;
    $this->acad_year->setAcadYearId($year);
    $this->acad_year->setPromotionAcadYearId($promationyearID);
    redirect('dashboard');
  }
  public function updateImageType(){
    $name = $_POST['name'];
    $type = $_POST['type'];
    $value = $_POST['newValue'];
    $email = $_POST['email'];
    $person_name = $_POST['person_name'];

    $before_update = $this->cmodel->before_update($name);
    $result = $this->cmodel->insertimageConfig($name, $value, $type);
    if ($result) {
      $status = "Config '{$name}' updated successfully from '{$before_update}' to '{$newValue}'.";
    }
    $history_result = $this->cmodel->insert_into_history($name, $email, $person_name, $status);

    // if ($result) {
    //   $this->session->set_flashdata('flashSuccess', 'Name/Value pair successfully added/updated');
    // } else {
    //   $this->session->set_flashdata('flashError', 'Something Wrong..');
    // }
    echo $result;
  }
  public function history(){
    $data['config'] = $this->cmodel->get_config();
    $data['main_content'] = 'config/history';
    $this->load->view('inc/template', $data);
  }
  public function get_config_history(){
    $config_id = $_POST['config_id'];
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $data = $this->cmodel->get_config_history($config_id, $from_date, $to_date);
    echo json_encode($data);
  }
      private function __email_one_time_verification_application($data){
        $from_name = $this->settings->getSetting('school_name');    
        $from_email = $this->settings->getSetting('admisison_one_time_password_send_email_id');   
        // $from_name = $set->email_subject;
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];
        $data['from_email'] = "<EMAIL>";
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;
        $data = http_build_query($data);
        $curl = curl_init();
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_unique_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            return 0;
        } else {
            return 1;
        }
    }
}

?>