<div class="modal" tabindex="-1" role="dialog" id="view_resourceType">
    <div class="modal-dialog" role="document" id="view_resource_modal">
        <div class="modal-content" style="border-radius:1rem;width: 80%;margin-top: 2% !important; margin: auto;">
            <div class="modal-header" style="border-top-right-radius:1rem;border-top-left-radius:1rem;">
                <h5 class="modal-title">View Digital Resources</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="view_resourceType_data">
                <div class="no-data-display">Loading...</div>
            </div>
            <div class="modal-footer" style="border-bottom-right-radius:1rem;border-bottom-left-radius:1rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary mt-0" onclick="hideMainModal('add_resources', 'view_resourceType')" data-show_resource="no" style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Add More</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function loadResources() {
        const avoidDownloadRerourceTypes = ["Other", "Hyper Link", "Video Link"];
        let html = `<table class="table table-bordered">
                        <tr class="bg-light">
                            <th>#</th>
                            <th>Name</th>
                            <th>Resource Type</th>
                            <th style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">Delete</th>
                        </tr>`

        resourceType.forEach((r, i) => {
            const resourceURL = r.resource_url.replaceAll("/", "-");
            const linkUrl = "https://" + r.resource_url.split("https://").at(-1);
            html += `
                    <tr>
                        <td>${++i}</td>
                        <td id="resource_${r.id}">${r.name}</td>
                        <td>${r.resource_type}</td>
                        <td style="display: <?php echo $has_write_permission == 1 ? 'block' : 'none' ?>;">
                            <a href="${avoidDownloadRerourceTypes.includes(r.resource_type) && linkUrl || r.resource_url}" target="_blank" class="btn btn-primary"><i class="fa fa-eye mr-0"></i></a>
                            <a href="<?php echo site_url('academics/Lesson_plan/download_LP_resource') ?>/${r.name}/${resourceURL}" style="display:${avoidDownloadRerourceTypes.includes(r.resource_type) && "none"}" class="btn btn-success">
                                <i class="fa fa-download mr-0"></i>
                            </a>
                            <a onClick="delete_resources(${r.id})" class="remove btn btn-danger "><i class="fa fa-trash-o mr-0"></i></a>
                        </td>
                    </tr>
                    `
        })

        html + `</table>`
        $("#view_resourceType_data").html(html);
    }

    $("#view_resourceType").on("shown.bs.modal", e => {
        loadResources();
    })

    function delete_resources(resource_id) {
        var resource_name = $(`#resource_${resource_id}`).text();
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('academics/lesson_plan/delete_resources_type_by_id') ?>',
                    type: 'post',
                    data: { resource_id },
                    success: function (data) {
                        let parseData = JSON.parse(data);
                        if (parseData) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted',
                                text: 'Resource deleted successfully',
                                showConfirmButton: false,
                                timer: 1500,
                            }).then(() => {
                                loadResources();
                                getSessionData(session_id);
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Something went wrong',
                                showConfirmButton: false,
                                timer: 1500,
                            });
                        }
                    },
                    error: function (data) {
                        console.log(data);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Something went wrong',
                            showConfirmButton: false,
                            timer: 1500,
                        });
                    }
                });
            }
        })
    }
</script>