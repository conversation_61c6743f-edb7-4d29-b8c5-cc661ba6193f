
<div class="container" id="admStatusshow" style="display:none;">
    <h3 id="donot_release_offer">Admission form status should be 'Admit' to Release the Offer.</h3>
</div>
<section class="signup-step-container" id="admReleaseshow">
    <div class="container">
        <div class="row d-flex justify-content-center">
            <div class="col-md-12">
                <div class="wizard">
                    <!-- <div class="wizard-inner">
                        <div class="connecting-line"></div>
                        <ul class="nav nav-tabs" role="tablist">
                            <li id="offer_tab" role="none" class="active">
                                <a href="#step1" data-toggle="tab" onclick="tabwise_list_data('release_offer')" aria-controls="step1" role="tab" aria-expanded="true"><span class="round-tab"></span> <i>Release offer</i></a>
                            </li>
                            <li id="fees_tab" role="none" class="disabled cls1">
                                <a href="#step2" data-toggle="tab" onclick="tabwise_list_data('fees')" aria-controls="step2" role="tab" aria-expanded="false"><span class="round-tab cls3"></span> <i>Assign Fees</i></a>
                            </li>
                            <li id="credentials_tab" role="none" class="disabled cls1">
                                <a href="#step3" data-toggle="tab" onclick="tabwise_list_data('credential')" aria-controls="step3" role="tab" aria-expanded="false"><span class="round-tab cls3"></span> <i>Release Credentials</i></a>
                            </li>
                        </ul>
                    </div> -->

                    <div class="wizard-inner">
                        <div class="stepper d-flex justify-content-between position-relative mb-5" id="stepProgress">
                            <div id="offer_tab" class="step text-center active">
                                <a href="#step1" data-toggle="tab" onclick="tabwise_list_data('release_offer')" aria-controls="step1" role="tab" aria-expanded="true">
                                    <div class="circle orange-border"></div>
                                    <div class="step-label text-orange">Release Offer</div>
                                </a>
                            </div>
                            <div id="fees_tab" class="step text-center">
                                <a href="#step2" data-toggle="tab" onclick="tabwise_list_data('fees')" aria-controls="step2" role="tab" aria-expanded="false">
                                    <div class="circle orange-border"></div>
                                    <div class="step-label text-orange fw-bold">Assign Invoice</div>
                                </a>
                            </div>
                            <div id="credentials_tab" class="step text-center">
                                <a href="#step3" data-toggle="tab" onclick="tabwise_list_data('credential')" aria-controls="step3" role="tab" aria-expanded="false">
                                    <div class="circle orange-border"></div>
                                    <div class="step-label text-orange">Release Credentials</div>
                                </a>
                            </div>
                            <div class="step-line position-absolute top-50 start-0 translate-middle-y" style="width: 84%; margin-left: 80px;margin-top:-28px"></div>
                        </div>
                    </div>
    
                    <div class="tab-content" id="main_form">
                        <div class="tab-pane active" role="tabpanel" id="step1">
                            <div id="loader" class="loaderclass" style="display:none;"></div>

                            <form class="form-horizontal" id="relase_offer_form" data-parsley-validate>
                                <input type="hidden" name="release_admission_form_id" value="<?php echo $afId ?>">
                                <?php if(!in_array('curriculum_interested_in',$disabled_fields) && !empty($final_preview->curriculum_interested_in)) { ?>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Curriculum Interested In</label>
                                        <input type="text" name="" id="" class="form-control input_cls" readonly value="<?= $final_preview->curriculum_interested_in; ?>" >
                                    </div>
                                <?php }?>

                                <?php $label = $this->settings->getSetting('your_word_for_class') ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label"><?php if($label) { echo $label;}else{ echo 'Grade' ;}  ?> <font color="red">*</font></label>
                                    <select class="form-control select-with-arrow input_cls" required name="grades" id="gradeId" onchange="getclassSection()" title="Select Grade">
                                        <option value="">Select Grade</option>
                                    </select>
                                    <span class="help-block">Select Grades</span>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Section</label>
                                    <select class="form-control select-with-arrow input_cls" name="class_section" id="classSectionId" required="">
                                        <option value="" selected="selected">Select Section</option>
                                    </select>
                                    <span class="help-block">Select Section</span>
                                </div>
                               
                                <?php if($this->settings->getSetting('puc_combination') && $school_short_name != 'srndegree') { ?>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Combination</label>
                                        <select class="form-control select-with-arrow input_cls" name="combination" id="combination" required>
                                            <option value="">Select Combination</option>
                                        </select>
                                        <span class="help-block">Select Combination</span>
                                    </div>
                                <?php } ?>

                                <?php if($school_short_name != 'srndegree' && $school_short_name != 'srnpu') { ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Special Case</label>
                                    <select name="rte_id" id="rte_id" class="form-control select-with-arrow input_cls">
                                        <option value="">Select</option>
                                        <?php if(!empty($rte)) { 
                                            foreach ($rte as $key => $value) { ?>
                                                <option value="<?php echo $key ?>"><?php echo $value ; ?></option>
                                        <?php } } ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Staff kid/Transfer</label>
                                    <select name="staffkid_or_transfer" id="staffkid_or_transfer" class="form-control select-with-arrow input_cls">
                                        <option value="">Not Applicable</option>
                                        <option value="staff_kid">Staff Kid</option>
                                        <option value="transfer">Transfer</option>
                                    </select>
                                    
                                    <div class="input-group mt-2" id="school_name" style="display:none;">
                                        <input type="text" name="transfer_from_school" id="transfer_from_school" class="form-control input_cls" placeholder="Enter the school name from which school transferred">
                                    </div>
                                </div>
                            <?php }?>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Boarding</label>
                                <select name="boarding" id="boarding" class="form-control select-with-arrow input_cls">
                                    <option value="">Select</option>
                                    <?php if(!empty($this->settings->getSetting('boarding'))) { 
                                        foreach($this->settings->getSetting('boarding') as $key =>$val ) { 
                                            $selected = '';
                                            if($key == $final_preview->boarding) { $selected = 'selected';} ?>
                                            <option value="<?= $key ?>" <?= $selected; ?>><?= $val ?></option>
                                    <?php } }?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Transportation</label>
                                <select name="transportaion" id="transportaion" class="form-control select-with-arrow input_cls">
                                    <option value="">Select</option>
                                    <option value="Yes" <?php if($final_preview->transport == 'Yes') {echo 'selected';}?>>Yes</option>
                                    <option value="No" <?php if($final_preview->transport == 'No') {echo 'selected';}?>>No</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Joining Date</label>
                                <div class="input-group date" id="joiningDatePicker">
                                    <input type="text" class="form-control input_cls" placeholder="Select Date" name="joining_date" id="joining_date" autocomplete="off">
                                    <span class="input-group-text">
                                        <i class="glyphicon glyphicon-calendar"></i>
                                    </span>
                                </div>
                            </div>
<!-- 
                                <div class="form-group">                                        
                                    <label class="col-md-3 col-xs-12 control-label">Course</label>
                                    <div class="col-md-6 col-xs-12">
                                        <div class="input-group">
                                           <select class="form-control" name="grades">
                                               <option value="">Select course</option>
                                               <option value="">Course-1</option>
                                               <option value="">Course-2</option>
                                               <option value="">Course-3</option>
                                           </select>
                                        </div>
                                        <span class="help-block">If there are course</span>
                                    </div>
                                </div> -->

                               <!--  <div class="form-group">
                                    <label class="col-md-3 col-xs-12 control-label">Seat Number</label>
                                    <div class="col-md-6 col-xs-12">                                            
                                        <div class="input-group">
                                            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                            <input type="text" name="seat_number" class="form-control">
                                        </div>                                            
                                        <span class="help-block">Select Seat Number</span>
                                    </div>
                                </div>
                                <div class="form-group">                                        
                                    <label class="col-md-3 col-xs-12 control-label">Allotment Date </label>
                                    <div class="col-md-6 col-xs-12">
                                        <div class="input-group">
                                            <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
                                            <input type="text" name="seat_allotment_date" class="form-control datepicker" value="<?php //echo date('Y-m-d') ?>">
                                        </div>
                                        <span class="help-block">Select Date</span>
                                    </div>
                                </div> -->

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Apply Offer</label>
                                    <select name="offer_ids[]" title="Select Offer" id="check_offers" class="form-control select-with-arrow input_cls" onchange="custom_offer_selected()" multiple>
                                        <option value="">Select Offer</option>
                                    </select>
                                </div>

                                <div class="col-md-6" style="display: none;float:none" id="custom_offer_display">
                                <label class="form-label">Custom Offer <font color="red"> *</font></label>
                                    <input type="text" name="custom_offer" id="custom_offer" class="form-control">
                                </div>

                                <div class="text-end" style="float: right;margin-top: 20px;">
                                    <button type="button" class="btn btn-primary custom-btn" id="relase_offer_button" onclick="release_offers()">Release Offer</button>
                                </div>
                            </form>
                            <div class='row' id="success_released" style="display:none">
                                <div class="col-md-12">
                                    <div class="">
                                        <!-- <div class="panel-body panel-body-pricing">
                                            <h2><i style="color: #4caf50;" class="fa fa-check"></i><small>Offer Release details</small></h2>
                                            <p><span class="fa fa-caret-right"></span> Grade - <b><span id=""> - </span></b> </p>
                                            <p><span class="fa fa-caret-right"></span> Section -  <b><span id=""> - </span></b></p>
                                            <p><span class="fa fa-caret-right"></span> Apply Offer - <b><span id=""> - </span></b></p>
                                        </div> -->

                                        <div class="status-card d-flex justify-content-between align-items-center">
                                            <div>
                                            <h5 class="mb-1 header">Grade - <span id="release_gradeName"></span> , Section - <span id="release_sectionName"> - </span></h5>
                                            <!-- <span class="status-text">Status: Released</span> -->
                                            </div>
                                            <div class="text-end">
                                            <small class="text-muted" style="font-size: larger;">Offer released on</small><br>
                                            <strong><span id="offer_released_date"></span></strong>
                                            </div>
                                        </div>

                                        <!-- Basic Details Card -->
                                        <div class="details-card">
                                            <div class="row">
                                                <div class="col-md-3 mb-2">
                                                    <h5 class="mb-3"><b>Basic Details</b></h5>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Staff Kid/Transfer</div>
                                                    <div class="label-value"><span id="offer_released_staffkid_or_transfer"></span></div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Special Case</div>
                                                    <div class="label-value"><span id="is_rte_student"></span></div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Boarding</div>
                                                    <div class="label-value"><span id="boarding_selected"></span></div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Transportation</div>
                                                    <div class="label-value"><span id="transport_selected"></span></div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Joining Date</div>
                                                    <div class="label-value"><span id="offer_release_joining_date"></span></div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="label-title">Offer Details</div>
                                                    <div class="label-value"><span id="offer_apply_details"></span></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>             
                        </div>
                        <div class="tab-pane" role="tabpanel" id="step2">
                            <div style="width: 85%;margin: auto;" id="fees_assinged_view"></div>
                            <form enctype="multipart/form-data" method="post" class="form-horizontal" id="assign_fee_new" action="<?php echo site_url('feesv2/fees_collection/cohor_data_insert_fee_table');?>" data-parsley-validate="" style="display: none;">

                                <div id="loader1" class="loaderclass" style="display:none;"></div>
                                <input type="hidden" name="blueprint_id" id="blueprint_id">
                                <input type="hidden" name="student_id" id="student_id">
                                <div class="modal-body">
                                   <!-- <div class="col-md-3" id="student_blueprint_selection_display" ></div> -->
                                    <div class="col-md-12" id="cohort_fees_installment_types">
                                            <!-- <div class="installment_header"> -->
                                                <div class="col-md-4 p-0" id="installment_type_display">
                                                    <select class="form-control select-with-arrow input_cls" name="blueprint_installment_type_id" id="ins_types"></select>
                                                </div>
                                                <div class="col-md-4 p-0" id="ins_types_custom_wrapper" style="display: none;float:left">
                                                    <select class="form-control form-select select-with-arrow input_cls" name="blueprint_installment_type_id" onchange="installments_data_custom()" id="ins_types_custom"></select>
                                                </div>
                                                <div class="col-md-4 p-0" style="padding-right:0;float:right">
                                                    <select id="customId" onchange="custom_enable_component()" class="form-control select-with-arrow input_cls" name="custom"></select>
                                                </div>


                                            <!-- <div id="id7" class="col-md-6" style="padding-right:0;">
                                                <select id="customId" onchange="custom_enable_component()" class="form-control" name="custom">
                                                </select>
                                            </div> -->
                                            <!-- <div class="flex-fill" style="min-width: 300px; padding-right:0;">
                                                    <select id="customId" onchange="custom_enable_component()" class="form-control" name="custom"></select>
                                                </div> -->
                                            <!-- </div> -->
                                    </div>
                                   <div class="col-md-12" id="ajax_construct_popup" style="margin-top: 35px;">
                          
                                    </div>
                                </div>
                                
                                <div class="text-end" style="float: right;">
                                    <button type="button" onclick="assign_admission_fees()" class="btn btn-primary custom-btn" id="fees_assing_button">Assign Invoice</button>
                                </div>
                            </form>

                              <!-- <form class="form-horizontal">


                                <div class="form-group">
                                    <label class="col-md-3 col-xs-12 control-label">Registration Fees</label>
                                    <div class="col-md-6 col-xs-12">                                            
                                        <div class="input-group">
                                            <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                            <input type="text" readonly value="" id="admission_one_time_fees" class="form-control">
                                        </div> 
                                        <span class="help-block">Registration Fees</span>
                                    </div>
                                </div>


                                <center>
                                    <button type="button" class="btn btn-success" id="fees_assing_button">Assign Invoice</button>
                                </center>
                            </form> -->
                        </div>
                     
                        <div class="tab-pane" role="tabpanel" id="step3">
                            <div class="text-center">
                                <div style="display: none;" class="progress" id="progress">
                                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%">
                                    </div>
                                </div>
                            </div>
                            <div class="construct_sms_email" style="width: 100%;">
                          
                            </div>

                        </div>
                       
                        
                        <div class="clearfix"></div>
                    </div>
                        
                </div>
            </div>
        </div>
    </div>
</section>

<div id="offer-preview" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title"> Admission offers</h4>
            </div>

            <div id="content-body" class="modal-body" style="overflow-y:auto;max-height:500px; ">

               
                <table class="table table-bordered">
                    <thead>
                      <tr>
                      <th>#</th>
                      <th>Offer Name</th>
                      <th>Amount</th>
                      <th></th>
                      </tr>
                    </thead>
                    <tbody id="offers_list">

                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger pull-right mr-1" style="width: 10rem;" data-dismiss="modal" >Close</button>
                <button type="button" id="submitbutton" style="width: 10rem;" onclick="select_admission_offers()" class="btn btn-primary pull-right">Add</button>
            </div>
        </div>
    </div>
</div>



<div id="summary_credentials" class="modal" role="dialog">
    <div class="modal-dialog" style="width:80%;margin: auto;margin-top:2%">
        <form enctype="multipart/form-data" method="post" id="send_provision_credentials" action="" class="form-horizontal" >
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Check the numbers and confirm</h4>
                </div>
                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                    <div><h4 id="warnMsg"></h4></div>
                    <div id="modal-loader" style="display: none; text-align: center;">
                        <!-- ajax loader -->
                        <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
                    </div>
                    <table class="table" id="dynamic-content-credentials" width="100%">

                    </table>
                </div>
                <div class="modal-footer">
                    <span id="credit-err" class="text-danger">Not enough credits to send sms</span>
                    <input type="button" id="confirmBtn" onclick="send_provision_credentials_form_submit()" class="btn btn-secondary" value="Confirm">
                    <button type="button" id="cancelModal" onclick="crenditials_modal_hide()" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    function crenditials_modal_hide() {
        $('#summary_credentials').hide();
        $('.modal-backdrop').removeClass('modal-backdrop fade show');
    }
</script>
<style type="text/css">
    .table{
      background: #fff;
      padding: 0 5px;
      overflow: hidden;
      position: relative;
  }
  .actionBar{
    display: none;
  }
  .loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 36%;
    margin-left: 55%;
    position: absolute;
    z-index: 99999;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
  .widthAdjust{
    width: 30%;
    margin: auto;
  }
</style>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/smartwizard/jquery.smartWizard-2.0.min.js"></script>


<script type="text/javascript">
    // ------------step-wizard-------------
    $(document).ready(function () {
        
        
        // if ($("#currentStatus").val() == 'Offer Released') {
        //     $("#fees_tab").removeAttr('class', 'cls1');
        //     $("#credentials_tab").removeAttr('class', 'cls1');
        // }

        $('.nav-tabs > li a[title]').tooltip();
        
        //Wizard
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {

            var target = $(e.target);
        
            if (target.parent().hasClass('disabled')) {
                return false;
            }
        });

        $(".next-step").click(function (e) {

            var active = $('.wizard .nav-tabs li.active');
            active.next().removeClass('disabled');
            nextTab(active);

        });
        $(".prev-step").click(function (e) {

            var active = $('.wizard .nav-tabs li.active');
            prevTab(active);

        });
    });


    function nextTab(elem) {
        $(elem).next().find('a[data-toggle="tab"]').click();
    }
    function prevTab(elem) {
        $(elem).prev().find('a[data-toggle="tab"]').click();
    }


    $('.nav-tabs').on('click', 'li', function() {
        $('.nav-tabs li.active').removeClass('active');
        $(this).addClass('active');
    });

    $('#staffkid_or_transfer').on('change', function() {
        if(this.value == 'transfer'){
            $('#school_name').show();
            $('#transfer_from_school').attr('required','required')
        }else{
            $('#school_name').hide();
            $('#transfer_from_school').removeAttr('required');
        }
    });

</script>

<style type="text/css">

    .list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus{
        background: #3f51b5;
    }
    i {
    margin-right: 10px;
}
.panel-body.panel-body-pricing p{
    font-size: 16px;
}
/*------------------------*/
input:focus,
button:focus,
.form-control:focus{
    outline: none;
    box-shadow: none;
}
.form-control:disabled, .form-control[readonly]{
    background-color: #fff;
}
/*----------step-wizard------------*/
.d-flex{
    display: flex;
}
.justify-content-center{
    justify-content: center;
}
.align-items-center{
    align-items: center;
}

/*---------signup-step-------------*/
.bg-color{
    background-color: #333;
}




    .wizard .nav-tabs {
        position: relative;
        margin-bottom: 0;
        border-bottom-color: transparent;
    }

    .wizard > div.wizard-inner {
    position: relative;
    margin-bottom: 2rem;
    text-align: center;
    }

.connecting-line {
    height: 2px;
    background: #e0e0e0;
    position: absolute;
    width: 75%;
    margin: 0 auto;
    left: 0;
    right: 0;
    top: 15px;
    z-index: 1;
}

.wizard .nav-tabs > li.active > a, .wizard .nav-tabs > li.active > a:hover, .wizard .nav-tabs > li.active > a:focus {
    color: #555555;
    cursor: default;
    border: 0;
    border-bottom-color: transparent;
}

span.round-tab {
    width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    border-radius: 50%;
    background: #fff;
    z-index: 2;
    position: absolute;
    left: 0;
    text-align: center;
    font-size: 16px;
    color: #0e214b;
    font-weight: 500;
    border: 1px solid #ddd;
}
span.round-tab i{
    color:#555555;
}
.wizard li.active span.round-tab {
        background: #2196f3;
    color: #fff;
    border-color: #2196f3;
}
.wizard li.active span.round-tab i{
    color: #5bc0de;
}
.wizard .nav-tabs > li.active > a i{
    color: #0db02b;
}

/* .wizard .nav-tabs > li {
    width: 25%;
} */

.wizard li:after {
    content: " ";
    position: absolute;
    left: 46%;
    opacity: 0;
    margin: 0 auto;
    bottom: 0px;
    border: 5px solid transparent;
    border-bottom-color: red;
    transition: 0.1s ease-in-out;
}



.wizard .nav-tabs > li a {
    width: 30px;
    height: 30px;
    margin: 20px auto;
    border-radius: 100%;
    padding: 0;
    background-color: transparent;
    position: relative;
    top: 0;
}
.wizard .nav-tabs > li a i{
    position: absolute;
    top: -15px;
    font-style: normal;
    font-weight: 400;
    white-space: nowrap;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 700;
    color: #000;
}

    .wizard .nav-tabs > li a:hover {
        background: transparent;
    }

.wizard .tab-pane {
    position: relative;
}


.wizard h3 {
    margin-top: 0;
}
.prev-step,
.next-step{
    font-size: 13px;
    padding: 8px 24px;
    border: none;
    border-radius: 4px;
    margin-top: 30px;
}
.next-step{
    background-color: #0db02b;
}
.skip-btn{
    background-color: #cec12d;
}
.step-head{
    font-size: 20px;
    text-align: center;
    font-weight: 500;
    margin-bottom: 20px;
}
.term-check{
    font-size: 14px;
    font-weight: 400;
}
.custom-file {
    position: relative;
    display: inline-block;
    width: 100%;
    height: 40px;
    margin-bottom: 0;
}
.custom-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 40px;
    margin: 0;
    opacity: 0;
}
.custom-file-label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: 40px;
    padding: .375rem .75rem;
    font-weight: 400;
    line-height: 2;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
}
.custom-file-label::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    display: block;
    height: 38px;
    padding: .375rem .75rem;
    line-height: 2;
    color: #495057;
    content: "Browse";
    background-color: #e9ecef;
    border-left: inherit;
    border-radius: 0 .25rem .25rem 0;
}
.footer-link{
    margin-top: 30px;
}
.all-info-container{

}
.list-content{
    margin-bottom: 10px;
}
.list-content a{
    padding: 10px 15px;
    width: 100%;
    display: inline-block;
    background-color: #f5f5f5;
    position: relative;
    color: #565656;
    font-weight: 400;
    border-radius: 4px;
}
.list-content a[aria-expanded="true"] i{
    transform: rotate(180deg);
}
.list-content a i{
    text-align: right;
    position: absolute;
    top: 15px;
    right: 10px;
    transition: 0.5s;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    background-color: #fdfdfd;
}
.list-box{
    padding: 10px;
}
.signup-logo-header .logo_area{
    width: 200px;
}
.signup-logo-header .nav > li{
    padding: 0;
}
.signup-logo-header .header-flex{
    display: flex;
    justify-content: center;
    align-items: center;
}
.list-inline li{
    display: inline-block;
}
.pull-right{
    float: right;
}
/*-----------custom-checkbox-----------*/
/*----------Custom-Checkbox---------*/
input[type="checkbox"]{
    position: relative;
    display: inline-block;
    margin-right: 5px;
}
input[type="checkbox"]::before,
input[type="checkbox"]::after {
    position: absolute;
    content: "";
    display: inline-block;   
}
input[type="checkbox"]::before{
    height: 16px;
    width: 16px;
    border: 1px solid #999;
    left: 0px;
    top: 0px;
    background-color: #fff;
    border-radius: 2px;
}
input[type="checkbox"]::after{
    height: 5px;
    width: 9px;
    left: 4px;
    top: 4px;
}
input[type="checkbox"]:checked::after{
    content: "";
    border-left: 1px solid #fff;
    border-bottom: 1px solid #fff;
    transform: rotate(-45deg);
}
input[type="checkbox"]:checked::before{
    background-color: #18ba60;
    border-color: #18ba60;
}


.wizard-inner .active{
    background:none;
}

.tab-content .active{
    background:none;
}
.list-group .active{
    z-index: 2;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}


@media (max-width: 767px){
    .sign-content h3{
        font-size: 40px;
    }
    .wizard .nav-tabs > li a i{
        display: none;
    }
    .signup-logo-header .navbar-toggle{
        margin: 0;
        margin-top: 8px;
    }
    .signup-logo-header .logo_area{
        margin-top: 0;
    }
    .signup-logo-header .header-flex{
        display: block;
    }
}


</style>
<style>
    .cls1 {
    pointer-events: none;
}

.cls3 {
    background-color: red;
}

#credentials_tab span.round-tab, #fees_tab span.round-tab {
    background: gray;
}

.wizard .nav-tabs > #offer_tab {
    margin-left: 9rem;
}

.wizard .nav-tabs > #fees_tab {
    margin-left: 26rem;
}

.wizard .nav-tabs > #credentials_tab {
    margin-left: 26rem;
}#id7 {
    max-width: 47%;
}
    
    .form-section {
      background: #fff;
      padding: 30px;
      border-radius: 10px;
    }

    .input_cls {
    width: 100%;
    height: auto;
    padding: 10px 16px;
    align-items: center;
    display: block;  /* Changed from flex to block */
 } 

    select .input_cls {
        height: auto;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    /* Ensure the select container takes full width */
    .flex-1 {
        flex: 1;
        width: 100%;
    }

    .custom-btn {
        background-color: #003580; /* Deep Blue */
        color: #fff;
        padding: 8px 24px;
        
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        box-shadow: none;
    }

    .custom-btn:hover {
        background-color: #002e6b;
    }

    .status-card, .details-card {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .status-text {
      color: green;
      font-weight: 500;
    }
    .label-title {
      color: #6c757d;
      font-size: 14px;
    }
    .label-value {
      font-weight: 600;
      font-size: 14px;
    }

    .step-card {
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 20px;
      transition: 0.3s ease-in-out;
    }
    .step-card:hover {
      box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    }
    .step-icon {
      width: 40px;
      height: 40px;
      background-color: #f0f4f8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
    }
    .step-title {
      font-weight: 600;
      font-size: 18px;
    }
    .step-description {
      font-size: 14px;
      color: #6c757d;
    }
    .step-button-outline {
      border: 1px solid #003366;
      color: #003366;
      padding: 12px 80px;
      border-radius: 6px;
      font-size: 16px;
    }
    .step-button-outline:hover {
      background-color: #003366;
      color: white;
      
    }
    .step-button-filled {
      background-color: #003366;
      color: white;
      padding: 12px 80px;
      border-radius: 6px;
      font-size: 16px;
    }
    .header{
        font-weight: 600;
        font-size:large;
    } 
    
    .toggle-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
    }
    .divider {
      border-left: 1px solid #dee2e6;
      height: 100%;
    }

    .stepper {
    position: relative;
    align-items: center;
}

.step {
    z-index: 1;
    cursor: pointer;
    width: 150px;
}

.step a {
    text-decoration: none;
    color: inherit;
}

.step-line {
    height: 2px;
    background-color: #ddd;
    z-index: 0;
}

.circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 auto;
    background-color: #fff;
    border: 3px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
}

.orange-border {
    border-color: #ff6600;
    width: 15px;
    height: 15px;
}
a:not([href]):hover {
    color: white !important;
    text-decoration: none;
}

.step-label {
    font-size: 14px;
    margin-top: 6px;
    /* display: block; */
}

.text-orange {
    color: #ff6600;
}

.text-blue {
    color: #003366;
}

.step.active .step-label {
    font-weight: bold;
    color: #003366;
}

.step:not(.active) .step-label {
    color: #ff6600;
}

.circle::after {
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: transparent;
}

.step.active .circle {
    border-color: #003366; /* Dark blue outer ring */
    width: 20px;
    height: 20px;
}

.step.active .circle::after {
    background-color: #003366; /* Inner dot */
}


.switch-ios {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 24px;
}

.switch-ios input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider-ios {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e9ecef;
    transition: .4s;
    border-radius: 24px;
}

.slider-ios:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider-ios {
    background-color: #649815;
}

input:checked + .slider-ios:before {
    transform: translateX(16px);
}

.select-with-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1em;
    padding-right: 2.5rem !important;
}

.select-with-arrow::-ms-expand {
    display: none;
}

.form-label{
    color: #000;
    /* font-family: Inter; */
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
    margin-top: 1rem;
}
.icon-container svg {
    width: 100%;
    height: 100%;
}
.custom-outline-btn {
  border: 2px solid #002F6C;
  color: var(--custom-blue);
  background-color: transparent;
  font-weight: 500;
  border-radius: 0.5rem;
  padding: 6px 16px;
}

.custom-outline-btn:hover {
  background-color: #002F6C;
  color: white;
}

.custom-filled-btn {
  background-color: #002F6C;
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 0.5rem;
  padding: 6px 16px;
}

.bootstrap-select .dropdown-toggle .filter-option {
    padding-right: 0 !important;
}

.bootstrap-select .dropdown-toggle::after,.caret {
    display: none !important;
}
.bootstrap-select , .dropdown-toggle{
    height: 36px;
}
.info-icon-wrapper {
    position: relative;
    cursor: pointer;
}

.info-tooltip-content {
    visibility: hidden;
    opacity: 0;
    width: max-content;
    background-color: #fff;
    color: #000;
    text-align: left;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: absolute;
    z-index: 100;
    bottom: 120%; /* 👈 Important: changed from top to bottom */
    left: 50%;
    transform: translateX(-50%);
    transition: opacity 0.3s;
    white-space: nowrap;
    min-width: 200px;
}

.info-icon-wrapper:hover .info-tooltip-content {
    visibility: visible;
    opacity: 1;
}

</style>