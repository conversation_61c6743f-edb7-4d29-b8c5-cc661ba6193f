<?php
	if ($this->authorization->isSuperAdmin()) {
		$display = 'inline-block'; //Change this to 'block' if you want Super Admin to see all menus.
	} else {
		//Manjuki<PERSON>: Changing this to block from none to support searching in side menu bar.
		//It should work without issues.
		$display = 'block';
	}
?>

<?php if ($this->authorization->isModuleEnabled('SCHOOL') && $this->authorization->isAuthorized('SCHOOL.MODULE')) : ?>
<li id="SCHOOL" class="strechingClass SCHOOL" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('school/school_menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>School</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE')) : ?>
<li id="TIMETABLE" class="strechingClass MYTIMETABLE" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('timetablev2/template/staff_timetable_v2');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/timetable.svg') ?>
        </div>
        <p>My Timetable</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('DAILY_PLANNER') && $this->authorization->isAuthorized('DAILY_PLANNER.MODULE')) : ?>
<li class="strechingClass DAILYPLANNER" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('daily_planner/Menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/timetable.svg') ?>
        </div>
        <p>Daily Planner</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('ID_CARDS') && $this->authorization->isAuthorized('ID_CARDS.MODULE')) : ?>
<li class="strechingClass DAILYPLANNER" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('idcards/Idcards_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/visitor.svg') ?>
        </div>
        <p>ID Cards</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS')) : ?>
<li class="strechingClass MYPAYSLIPS" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('staff/Payroll_controller/index'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/payslip.svg') ?>
        </div>
        <p>My Payslips</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_RECRUITMENT') && $this->authorization->isAuthorized('STAFF_RECRUITMENT.MODULE')): ?>
    <?php if( isset($show_my_interviews) && $show_my_interviews ): ?>
        <li id="MY_INTERVIEW" class="strechingClass MYINTERVIEW" style="display:<?= $display ?>;">
            <a onclick="loader()" href="<?php echo site_url('Staff_recruitment_controller/interviewer_page'); ?>">
                <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
                    <?php $this->load->view('svg_icons/circular.svg') ?>
                </div>
                <p>My Interviews</p>
            </a>
        </li>
    <?php endif ?>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('STUDENT_MASTER') && $this->authorization->isAuthorized('STUDENT.MODULE')) : ?>
<li id="STUDENT_MASTER" class="strechingClass STUDENT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('student/student_menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <span class="animate__animated animate__pulse">
                <?php $this->load->view('svg_icons/student.svg') ?>
            </span>
        </div>
        <p>Student</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('INDUS_SUY') && $this->authorization->isAuthorized('INDUS_SUY.MODULE')) : ?>
<li id="INDUS_SUY" class="strechingClass SUY" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('suy/Suy_menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <span class="animate__animated animate__pulse">
                <?php $this->load->view('svg_icons/reportcard.svg') ?>
            </span>
        </div>
        <p>SUY</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_CERTIFICATE') && $this->authorization->isAuthorized('STUDENT_CERTIFICATE.MODULE')) : ?>
<li class="strechingClass STUDENT_CERTIFICATE" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('student/Certificates_controller/index'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/payslip.svg') ?>
        </div>
        <p>Student Certificate</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('ITARI') && $this->authorization->isAuthorized('ITARI.MODULE')) : ?>
<li id="ITARI" class="strechingClass ITARI" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('itari/itari_menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <span class="animate__animated animate__pulse">
                <?php $this->load->view('svg_icons/reportcard.svg') ?>
            </span>
        </div>
        <p>ITARI</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_ATTENDANCE') && $this->authorization->isAuthorized('STUDENT_ATTENDANCE.MODULE')) : ?>
<li id="STUDENT_ATTENDANCE" class="strechingClass STUDENT_DAY_ATTENDANCE" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('attendance/Attendance_controller') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/attendance.svg') ?>
        </div>
        <p>Attendance (Day)</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_DAY_ATTENDANCE_V2') && $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.MODULE')) : ?>
<li id="STUDENT_DAY_ATTENDANCE_V2" class="strechingClass STUDENT_DAY_ATTENDANCE_V2" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/attendance.svg') ?>
        </div>
        <p>Attendance V2 Day</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('CALENDAR_EVENTS_V2') && $this->authorization->isAuthorized('CALENDAR_EVENTS_V2.MODULE')) : ?>
<li id="CALENDAR_EVENTS_V2" class="strechingClass CALENDAR_EVENTS_V2" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('calendar_events_v2/Calendar_events_v2') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/attendance.svg') ?>
        </div>
        <p>Calendar Events V2</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_ATTENDANCE_V2') && $this->authorization->isAuthorized('STUDENT_ATTENDANCE_V2.TAKE_ATTENDANCE')) : ?>
<li id="STUDENT_ATTENDANCE_V2" class="strechingClass STUDENT_ATTENDANCE_SUBJECT" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('attendance_v2/menu') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/attendance.svg') ?>
        </div>
        <p>Attendance (Subject)</p>
    </a>
</li>
<?php endif ?>

<?php if($this->authorization->isModuleEnabled('STUDENT_LEAVE')) { ?>
<li id="STUDENT_LEAVE" class="strechingClass STUDENTLEAVE" style="display:<?php echo $display ?>;">
    <a href="<?php echo site_url('leave_controller') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/applyleave.svg') ?>
        </div>
        <p>Student Leave</p>
    </a>
</li>
<?php } ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_OBSERVATION') && $this->authorization->isAuthorized('STUDENT_OBSERVATION.MODULE')) : ?>
<li id="STUDENT_OBSERVATION" class="strechingClass STUDENTOBSERVATION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('student_observation/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Student Observation</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_NONCOMPLIANCE') && $this->authorization->isAuthorized('STUDENT_NONCOMPLIANCE.MODULE')) : ?>
<li id="STUDENT_NONCOMPLIANCE" class="strechingClass STUDENTNONCOMPLIANCE" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('student_nc/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Student Non-Compliance</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('STAFF_MASTER') && $this->authorization->isAuthorized('STAFF.MODULE')) : ?>
<li id="STAFF_MASTER" class="strechingClass STAFF" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('staff/staff_menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/staff.svg') ?>
        </div>
        <p>Staff</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_ATTENDANCE') && $this->authorization->isAuthorized('STAFF_ATTENDANCE.MODULE')) : ?>
<li id="STAFF_ATTENDANCE" class="strechingClass STAFFATTENDANCE" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('staff/attendance'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/myattendance.svg') ?>
        </div>
        <p>Staff Attendance</p>
</a>
</li>
<?php endif ?>


<?php
    if ((int)$this->authorization->isModuleEnabled('STAFF_ATTENDANCE')==1 && (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.MODULE')==1): ?>
    <li id="MY_ATTENDANCE" class="strechingClass MYATTENDANCE" style="display:<?= $display ?>;">
        <a onclick="loader()" href="<?php echo site_url('staff/attendance/my_attendance_desktop'); ?>">
            <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
                <?php $this->load->view('svg_icons/myattendance.svg') ?>
            </div>
            <p>My Attendance</p>
    </a>
</li>
<?php endif ?>

<?php if($this->authorization->isModuleEnabled('LEAVE_V2') && $this->authorization->isAuthorized('LEAVE.MODULE')) { ?>
<li id="LEAVE_V2" class="strechingClass STAFFLEAVES" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('staff/leaves/dashboard') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/applyleave.svg') ?>
        </div>
        <p>Staff Leaves</p>
    </a>
</li>
<?php } ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_360_DEGREE') && $this->authorization->isAuthorized('STAFF_360_DEGREE.MODULE')): ?>
<li id="STAFF_360_DEGREE" class="strechingClass STAFF360" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('staff_analytics/Staff_analytics/index'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Staff 360</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_RECRUITMENT') && $this->authorization->isAuthorized('STAFF_RECRUITMENT.MODULE')): ?>
<li id="STAFF_RECRUITMENT" class="strechingClass STAFFRECRUITMENT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('Staff_recruitment_controller/index'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Staff Recruitment</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_TASKS_BASKET') && $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MODULE')) : ?>
<li id="STAFF_TASKS_BASKET" class="strechingClass STAFFTASKSBASKET" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('stb/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Staff Tasks Basket</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('CLASSROOM_CHRONICLES') && $this->authorization->isAuthorized('CLASSROOM_CHRONICLES.MODULE')) : ?>
<li id="CLASSROOM_CHRONICLES" class="strechingClass CLASSROOMCHRONICLES" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('classroom_chronicles/Classroom_chronicles_controller') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p><?php echo $this->settings->getSetting('classroom_chronicles_module_name') != null ? $this->settings->getSetting('classroom_chronicles_module_name') : 'Classroom Chronicles' ?></p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENET_COUNSELLING') && $this->authorization->isAuthorized('STUDENT_COUNSELLING.MODULE')) : ?>
<li id="STUDENET_COUNSELLING" class="strechingClass STUDENT_BEHAVIOURAL_COUNSELLING" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('student_counselling/Student_counselling_controller') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Behavioural Counselling</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('ACADEMICS') && $this->authorization->isAuthorized('ACADEMICS.MODULE')) : ?>
<li id="ACADEMICS" class="strechingClass ACADEMICS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('academics/academics_menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/academic.svg') ?>
        </div>
        <p>Academics</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('FEESV2') && $this->authorization->isAuthorized('FEESV2.MODULE')) : ?>
<li id="FEESV2" class="strechingClass FEES" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('feesv2/fees_dashboard') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/fees.svg') ?>
        </div>
        <p>Fees</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('MANAGEMENT') && $this->authorization->isAuthorized('MANAGEMENT.MODULE')) : ?>
<li class="strechingClass MANAGEMENT" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('management/management_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/management.svg') ?>
        </div>
        <p>Management</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('PROCUREMENT') && $this->authorization->isAuthorized('PROCUREMENT.MODULE')) : ?>
<li class="strechingClass PROCUREMENT" style="display:inline-block;">
    <a onclick="loader()" href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto;">
            <?php $this->load->view('svg_icons/management.svg') ?>
        </div>
        <p>Procurement</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.MODULE')) : ?>
<li id="EXPENSE" class="strechingClass EXPENSE" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('management/expense'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/expenses.svg') ?>
        </div>
        <p>Expenses</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('MENTORING') && $this->authorization->isAuthorized('MENTORING.MODULE')) : ?>
<li id="MENTORING" class="strechingClass MENTORING" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('itari/itari_controller/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Mentoring</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('DONATION') && $this->authorization->isAuthorized('DONATION.MODULE')) : ?>
<li id="DONATION" class="strechingClass DONATION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('donation/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Donation</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('INFIRMARY') && $this->authorization->isAuthorized('INFIRMARY.MODULE')) : ?>
<li id="INFIRMARY" class="strechingClass INFIRMARY" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('infirmary/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/studentobservation.svg') ?>
        </div>
        <p>Infirmary</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('INTERNAL_TICKETING') && $this->authorization->isAuthorized('INTERNAL_TICKETING.MODULE')) : ?>
<li id="INTERNAL_TICKETING" class="strechingClass INTERNAL_TICKETING" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('internal_ticketing/index'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>Internal Ticketing</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->settings->getSetting('enable_english_roots',0)) : ?>
<li id="ENGLISHROOTS" class="strechingClass ENGLISHROOTS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('english_roots/root_controller') ?>">
        <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/admission.png') ?> ">
        <p>English Roots</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('VIRTUAL_CLASSROOM') && $this->authorization->isAuthorized('VIRTUAL_CLASSROOM.MODULE')) : ?>
<li id="VIRTUAL_CLASSROOM" class="strechingClass VIRTUALCLASS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('virtual_classroom/dashboard') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/online.svg') ?>
        </div>
        <p>Virtual Class</p>
    </a>
</li>
<?php endif ?>

<?php //if ($this->authorization->isAuthorized('ONLINE_CLASS.MODULE')) : ?>
<!-- <li id="ONLINE_CLASS" class="strechingClass" style="display:<?= $display ?>;">
	  	<a href="<?php //echo site_url('online_class/dashboard') ?>">
			<div class="animate__animated animate__pulse" style="width:24px;margin:auto">
				<?php //$this->load->view('svg_icons/online_new.svg') ?>
			</div>
	    	<p>Online Class</p>	    	
	  	</a>
	</li> -->
<?php //endif ?>

<?php if ($this->authorization->isModuleEnabled('ONLINE_CLASS_V2') && $this->authorization->isAuthorized('ONLINE_CLASS_V2.MODULE')) : ?>
<li id="ONLINE_CLASS_V2" class="strechingClass ONLINECLASS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('online_class_v2/dashboard') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/online_new.svg') ?>
        </div>
        <p>Online Class</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('USER_MANAGEMENT') && $this->authorization->isAuthorized('USER_MANAGEMENT.MODULE')) : ?>
<li id="USER_MANAGEMENT" class="strechingClass MANAGEUSER" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('SchoolAdmin_menu') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/usermanagement.svg') ?>
        </div>
        <p>Manage User</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('COMMUNICATION') && $this->authorization->isAuthorized('COMMUNICATION.MODULE')) : ?>
<li id="COMMUNICATION" class="strechingClass COMMUNICATION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("communication_dashboard");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/communication.svg') ?>
        </div>
        <p>Communication</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('PARENT_TICKETING') && $this->authorization->isAuthorized('PARENT_TICKETING.MODULE')) : ?>
<li id="PARENT_TICKETING" class="strechingClass PARENTTICKETING" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("ParentTicket_dashboard");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/communication.svg') ?>
        </div>
        <p>Parent Ticketing</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('EXAMINATION') && $this->authorization->isAuthorized('EXAMINATION.MODULE')) : ?>
<li id="EXAMINATION" class="strechingClass EXAMINATION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("examination/assessments/");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/exam.svg') ?>
        </div>
        <p>Examination</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="EXAMINATION_V2" class="strechingClass EXAMINATIONV2" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("examination_v2/menu");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/exam.svg') ?>
        </div>
        <p>Examination V2</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('AFL') && $this->authorization->isAuthorized('AFL.MODULE')) : ?>
<li id="AFL" class="strechingClass AFL" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("afl/afl_dashboard");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/afl.svg') ?>
        </div>
        <p>AFL</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="TIMETABLE" class="strechingClass TIMETABLEOLD" style="display:<?php echo $display ?>;">
    <a href="<?php echo site_url('timetable/timetables/menu');?>">
        <img class="img-responsive mb-2" src="<?php echo base_url('assets/img/blue32px/timetable.png') ?>">
        <p>Timetable (old)</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MODULE')) : ?>
<li id="TIMETABLE" class="strechingClass TIMETABLE" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('timetablev2/menu');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/timetable.svg') ?>
        </div>
        <p>Timetable</p>
    </a>
</li>
<?php endif ?>

<!-- <?php //if ($this->authorization->isAuthorized('SUBSTITUTION.MODULE')) : ?>
	<li id="SUBSTITUTION" class="strechingClass" style="display:<?php //echo $display; ?>;">
		<a href="<?php //echo site_url('timetable/substitution_menu/index') ?>">
			<div class="animate__animated animate__pulse" style="width:24px;margin:auto">
				<?php //$this->load->view('svg_icons/substitution.svg') ?>
			</div>
			<p>Substitution</p>			
		</a>
	</li>
<?php //endif ?> -->

<?php  if ($this->authorization->isModuleEnabled('STAFF_CONSENT') && $this->authorization->isAuthorized('STAFF.MANAGE_STAFF_CONSENT')) : ?>
<li id="MANAGE_STAFF_CONSENT" class="strechingClass MANAGE_STAFF_CONSENT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('staff_consent\Staff_consent_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/reportcard.svg') ?>
        </div>
        <p>Manage Staff Consents</p>
    </a>
</li>
<?php  endif ?>

<?php  if ($this->authorization->isModuleEnabled('STAFF_CONSENT') && $this->authorization->isAuthorized('STAFF.PROVIDE_CONSENT')) : ?>
<li id="PROVIDE_CONSENT" class="strechingClass PROVIDE_CONSENT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('Staff_consent_form_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/reportcard.svg') ?>
        </div>
        <p>Provide Consent</p>
    </a>
</li>
<?php  endif ?>

<?php if ($this->authorization->isModuleEnabled('SUBSTITUTION') && $this->authorization->isAuthorized('SUBSTITUTION.MODULE')) : ?>
<li id="SUBSTITUTION" class="strechingClass SUBSTITUTION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('substitution_v2/menu/index') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/substitution.svg') ?>
        </div>
        <p>Substitution</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('ACTIVITY') && $this->authorization->isAuthorized('COMPETITION.MODULE')) : ?>
<li id="ACTIVITY" class="strechingClass COMPETITION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('competition_menu');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/competition.svg') ?>
        </div>
        <p>Competition</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('EVENT') && $this->authorization->isAuthorized('EVENT.MODULE')) : ?>
<li id="EVENT" class="strechingClass EVENTS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('event/event_dashboard');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/events.svg') ?>
        </div>
        <p>Events</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('WALLET') && $this->authorization->isAuthorized('WALLET.MODULE')) : ?>
<li id="WALLET" class="strechingClass STUDENTWALLET" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('Student_wallet');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/events.svg') ?>
        </div>
        <p>Student Wallet</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('SCHOOL_CALENDAR') && $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE')) : ?>
<li id="SCHOOL_CALENDAR" class="strechingClass CALENDAR" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("calender_events/calender_controller");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/calendar.svg') ?>
        </div>
        <p>Calendar</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('LIBRARY') && $this->authorization->isAuthorized('LIBRARY.MODULE')) : ?>
<li id="LIBRARY" class="strechingClass LIBRARY" style="display:<?= $display ?>;">
    <a href="<?php echo site_url("library_controller");?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/library.svg') ?>
        </div>
        <?php 
        $module_name = $this->settings->getSetting('library_module_name') ? $this->settings->getSetting('library_module_name'): 'Library' ;
         ?>
        <p><?= $module_name ?></p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('LIBRARY') && $this->authorization->isAuthorized('LIBRARY.STAFF_VIEW')) : ?>
<li id="LIBRARY" class="strechingClass MYLIBRARY" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('library_controller/card_view'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/mylibrary.svg') ?>
        </div>
        <p>My Library</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('TRANSPORT') && $this->authorization->isAuthorized('TRANSPORTATION.MODULE')) : ?>
<li id="TRANSPORT" class="strechingClass TRANSPORT" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('transportation');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/transport.svg') ?>
        </div>
        <p>Transport</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STAFF_TRANSPORT')) : ?>
<li id="TRANSPORT" class="strechingClass TRANSPORT" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('staff/Staff_mytransport');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/transport.svg') ?>
        </div>
        <p>My Transport</p>
    </a>
</li>
<?php endif ?>
<?php if ($this->authorization->isModuleEnabled('TRANSPORTATION_REQUEST') && $this->authorization->isAuthorized('TRANSPORTATION_REQUEST.MODULE')) : ?>
<li id="TRANSPORTATION_REQUEST" class="strechingClass TRANSPORTATIONREQUEST" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('staff/Staff_transport_request');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/transport.svg') ?>
        </div>
        <p>Transport Request</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_TRACKING') && $this->authorization->isAuthorized('STUDENT_TRACKING.MODULE')) : ?>
<li id="STUDENT_TRACKING" class="strechingClass STUDENTTRACKING" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('student_tracking/student_tracking_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <span class="animate__animated animate__pulse">
                <?php $this->load->view('svg_icons/attendance.svg') ?>
            </span>
        </div>
        <p>Student Tracking</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="VISITOR" class="strechingClass VISITOR" style="display:<?php //echo $display ?>;">
    <a href="<?php echo site_url('Visitors_controller/visitor_dashboard'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/visitor.svg') ?>
        </div>
        <p>Visitor (old)</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('VISITOR') && $this->authorization->isAuthorized('VISITOR.MODULE')) : ?>
<li id="VISITOR" class="strechingClass VISITOR" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('Visitors_v2_controller/visitor_dashboard'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/visitor.svg') ?>
        </div>
        <p> Visitor</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.MODULE')) : ?>
<li id="PAYROLL" class="strechingClass PAYROLL" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('management/payroll'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/payroll.svg') ?>
        </div>
        <p>Payroll</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="CANTEEN" class="strechingClass CANTEEN" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('canteen');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/canteen.svg') ?>
        </div>
        <p>Canteen</p>
    </a>
</li>
<?php endif ?>
<?php if ($this->authorization->isModuleEnabled('ASSETS') && $this->authorization->isAuthorized('ASSETS.MODULE')) : ?>
<li id="ASSETS" class="strechingClass ASSETS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('management/management_controller/Asset_management');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/assests.svg') ?>
        </div>
        <p>Assests</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('ADMISSION') && $this->authorization->isAuthorized('ADMISSION.MODULE')) : ?>
<li id="ADMISSION" class="strechingClass ADMISSION" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('admission_process');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/reportcard.svg') ?>
        </div>
        <p>Admission</p>
    </a>
</li>
<?php endif ?>


<?php if ($this->authorization->isModuleEnabled('ENQUIRY') && $this->authorization->isAuthorized('ENQUIRY.MODULE')) : ?>
<li id="ENQUIRY" class="strechingClass ENQUIRY" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('enquiry/enquiry_staff') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/enquiry.svg') ?>
        </div>
        <p>Enquiry</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('GALLERY') && $this->authorization->isAuthorized('GALLERY.MODULE')) : ?>
<li id="GALLERY" class="strechingClass GALLERY" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('galleries');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/gallery.svg') ?>
        </div>
        <p>Gallery</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('OTHERLINKS') && $this->authorization->isAuthorized('OTHERLINKS.MODULE')) : ?>
<li id="OTHERLINKS" class="strechingClass OTHERLINKS" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('dashboard/other_links');?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/otherlinks.svg') ?>
        </div>
        <p>
            <?php 
					$other_link_module_name = $this->settings->getSetting('otherlinks_module_name');
					if (empty($other_link_module_name)) {
						$other_link_module_name = 'Other Links';
					}
					echo $other_link_module_name;
				?>
        </p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('SALES') && $this->authorization->isAuthorized('SALES.MODULE')) : ?>
<li id="SALES" class="strechingClass SALES" style="display:<?= $display ?>;">
    <a href="<?php echo site_url('sales/sales') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/sales.svg') ?>
        </div>
        <p>Sales</p>
    </a>
</li>
<?php endif ?>

<li class="strechingClass HELPANDSUPPORT">
    <a href="<?php echo site_url('dashboard/help_support') ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/faq.svg') ?>
        </div>
        <p>Help & Support</p>
    </a>
</li>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="CLASSROOM_VIEW" class="strechingClass CLASSROOMVIEW" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('dashboards/classroom_dashboard'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Classroom View</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('BOARDING') && $this->authorization->isAuthorized('BOARDING.MODULE')) : ?>
<li id="BOARDING" class="strechingClass BOARDING" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('boarding/boarding_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Boarding</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_360') && $this->authorization->isAuthorized('STUDENT_360.MODULE')) : ?>
<li id="STUDENT_ANALYTICS" class="strechingClass STUDENT360" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('student_analytics/student_analytics'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Student 360</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="STAFF_ANALYTICS" class="strechingClass STAFFANALYTICS" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('staff_analytics/staff_analytics'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Staff Analytics</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="ALERT_MANAGEMENT" class="strechingClass ALERTMANAGEMENT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('alert_mgmt/menu'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/circular.svg') ?>
        </div>
        <p>Alert Management</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="HELIUM" class="strechingClass HELIUM" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('helium/teaching'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>Helium</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('MSM') && $this->authorization->isAuthorized('MSM.MODULE')) : ?>
<li id="MSM" class="strechingClass MULTISCHOOLMANAGER" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('msm_v3/dashboard'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>Multi-school Manager</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isSuperAdmin()) : ?>
<li id="ANALYTICS" class="strechingClass ANALYTICS" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('analytics/analytics'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>ANALYTICS</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('STUDENT_EXIT_FLOW_STAFF') && $this->authorization->isAuthorized('STUDENT_EXIT_FLOW_STAFF.MODULE')) : ?>
<li id="STUDENT_EXIT_FLOW_STAFF" class="strechingClass STUDENT_EXIT_FLOW_STAFF" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('student_exit_flow/Student_exit_flow_controller'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>Student Exit Flow</p>
    </a>
</li>
<?php endif ?>

<?php if ($this->authorization->isModuleEnabled('SIMULATE_504_TIMEOUT')) : ?>
<li id="SIMULATE_504_TIMEOUT" class="strechingClass SIMULATE_504_TIMEOUT" style="display:<?= $display ?>;">
    <a onclick="loader()" href="<?php echo site_url('timeout'); ?>">
        <div class="animate__animated animate__pulse" style="width:24px;margin:auto">
            <?php $this->load->view('svg_icons/school.svg') ?>
        </div>
        <p>Simulate 504 Timeout</p>
    </a>
</li>
<?php endif ?>



<script>
side_menu_array = [
    "SCHOOL",
    "MYTIMETABLE",
    "MYPAYSLIPS",
    "STUDENT",
    "SUY",
    "ITARI",
    "STUDENT_DAY_ATTENDANCE",
    "STUDENT_DAY_ATTENDANCE_V2",
    "STUDENT_ATTENDANCE_SUBJECT",
    "STUDENTLEAVE",
    "STUDENTOBSERVATION",
    "STUDENTNONCOMPLIANCE",
    "STAFF",
    "STAFFATTENDANCE",
    "STAFFLEAVES",
    "STAFF360",
    "STAFFTASKSBASKET",
    "ACADEMICS",
    "FEES",
    "MANAGEMENT",
    "PROCUREMENT",
    "EXPENSE",
    "ASSETS",
    "MENTORING",
    "DONATION",
    "INFIRMARY",
    "INTERNALTICKETING",
    "ENGLISHROOTS",
    "VIRTUALCLASS",
    "ONLINECLASS",
    "MANAGEUSER",
    "COMMUNICATION",
    "PARENTTICKETING",
    "CLASSROOMCHRONICLES",
    "EXAMINATION",
    "EXAMINATIONV2",
    "AFL",
    "TIMETABLEOLD",
    "TIMETABLE",
    "SUBSTITUTION",
    "COMPETITION",
    "EVENTS",
    "STUDENTWALLET",
    "CALENDAR",
    "CALENDAR_EVENTS_V2",
    "LIBRARY",
    "MYLIBRARY",
    "TRANSPORT",
    "VISITOR",
    "CANTEEN",
    "ADMISSION",
    "ENQUIRY",
    "GALLERY",
    "OTHERLINKS",
    "PARENTTICKETING",
    "SALES",
    "HELPANDSUPPORT",
    "CLASSROOMVIEW",
    "BOARDING",
    "STDANALYTICS",
    "STAFFANALYTICS",
    "STUDENT_BEHAVIOURAL_COUNSELLING",
    "ALERTMANAGEMENT",
    "HELIUM",
    "MULTISCHOOLMANAGER",
    "STUDENT360",
    "PAYROLL",
    "VISITOR",
    "ANALYTICS",
    "STAFFRECRUITMENT",
    "STUDENT_EXIT_FLOW_STAFF",
    "MYINTERVIEW",
    "MANAGE_STAFF_CONSENT",
    "PROVIDE_CONSENT",
    "MYATTENDANCE",
    "INTERNAL_TICKETING",
    "MANAGE_STAFF_CONSENT",
    "STUDENTTRACKING",
    "SIMULATE_504_TIMEOUT",
    "STUDENT_CERTIFICATE"
];

function search_sidebar(event) {
    if (event.which == '27') {
        //Handling escape
        var keyword = $('#search_sidebar').val('');
        side_menu_array.forEach(str => {
            $(`.${str}`).show();
        });
        return;
    }
    var keyword = $('#search_sidebar').val();
    side_menu_array.forEach(str => {
        if (str.toLowerCase().search(keyword.toLowerCase()) != '-1') {
            $(`.${str}`).show();
        } else {
            $(`.${str}`).hide();
        }
    });
}
</script>