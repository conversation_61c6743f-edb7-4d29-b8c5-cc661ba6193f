<?php

defined('BASEPATH') OR exit('No direct script access allowed');
            
class Assessment_reports_model extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
    }

    public function getSectionsFromClass($classId) {
        $sections = $this->db_readonly->select('id as csId, section_name as csName')->where('class_id', $classId)->where('is_placeholder', 0)->get('class_section')->result();
        return $sections;
    }

    public function getClsAssessments_new($classId, $assType){
        // $sql = "select id, long_name, short_name, sorting_order from assessments where class_id='$classId'";
        // if($assType != 'both') { //get both manual and auto
        //     $sql .= " and generation_type='$assType'";
        // }
        // $sql .= ' order by sorting_order';
        // $result = $this->db_readonly->query($sql)->result();

        $this->db_readonly->select("id, long_name, short_name, sorting_order")->where('class_id', $classId)->where('acad_year_id', $this->yearId);
        if($assType != 'both') { //get both manual and auto
            $this->db_readonly->where('generation_type', $assType);
        }
        $result= $this->db_readonly->get('assessments')->result();
        return $result;
    }

    public function getSubjectsByAssessment($assIds, $subjectType){
        if(empty($assIds)) return array();
        $assIds = implode(",", $assIds);
        if ($subjectType == 'component') {
            $result = $this->db_readonly->select('aem.id, aem.name as subName, aem.sorting_order, aem.mapping_string')
                ->from('assessment_entity_master aem')
                ->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid', 'left')
                ->where('aem.id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))')
                // ->where('aeg.is_elective', 0)
                ->order_by('aem.sorting_order')
                ->get()->result();
        } else if($subjectType == 'group'){
            $result = $this->db_readonly->select('aeg.id, aeg.entity_name as subName, aeg.sorting_order, aeg.mapping_string')
                ->from('assessment_entities_group aeg')
                ->join('assessment_entity_master aem', 'aem.ass_entity_gid= aeg.id')
                ->where('aeg.id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.')))')
                // ->where('aeg.is_elective', 0)
                ->where('aem.evaluation_type', 'marks')
                ->order_by('aeg.sorting_order')
                ->get()->result();
        } else if($subjectType == 'elective'){
            $result = $this->db_readonly->select('aeg.id, aeg.group_name as subName')
                ->from('assessment_elective_group aeg')
                ->where('id in (select elective_group_id from assessment_entities_group where id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))))')
                ->order_by('aeg.id')
                ->get()->result();
        }
        return $result;
    }

    public function getSubjectsByAssessment_new($assIds, $subjectType){
        if(empty($assIds)) return array();
        $assIds = implode(",", $assIds);
        if ($subjectType == 'component') {
            $result = $this->db_readonly->select('aem.id, aem.name as subName, aem.sorting_order, aem.mapping_string')
                ->from('assessment_entity_master aem')
                ->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid', 'left')
                ->where('aem.id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))')
                // ->where('aeg.is_elective', 0)
                ->order_by('aeg.sorting_order, aem.sorting_order')
                ->get()->result();
        } else if($subjectType == 'group'){
            $result = $this->db_readonly->select('aeg.id, aeg.is_elective, aeg.entity_name as subName, aeg.sorting_order, aeg.mapping_string')
                ->from('assessment_entities_group aeg')
                ->join('assessment_entity_master aem', 'aem.ass_entity_gid= aeg.id')
                ->where('aeg.id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.')))')
                // ->where('aeg.is_elective', 0)
                ->where('aem.evaluation_type', 'marks')
                ->order_by('aeg.sorting_order')
                ->group_by('aeg.id')
                ->get()->result();
        } else if($subjectType == 'elective'){
            $result = $this->db_readonly->select('aeg.id, aeg.group_name as subName')
                ->from('assessment_elective_group aeg')
                ->where('id in (select elective_group_id from assessment_entities_group where id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))))')
                ->order_by('aeg.id')
                ->get()->result();
        }
        return $result;
    }

    public function getComponentPerformance($assessmentIds, $classId, $components){
        $componentIds = implode(",", $components);
        $assIds = implode(",", $assessmentIds);

        $sql = "select a.short_name as assessment_name, concat(c.class_name, cs.section_name) as csName, sum(case when aems.marks>0 then aems.marks else 0 end) as marksSum, max(aems.marks) as highest, min(case when aems.marks > -1 then aems.marks end) as lowest, assent.assessment_id as assId, assent.total_marks, aem.name as entity_name,aem.short_name as diplay_name, aem.id as entity_id, aem.ass_entity_gid as groupId, sum(case when marks=-1 then 1 else 0 end) as absentees, aeg.entity_name as group_name,sum(case when aems.marks<(assent.total_marks*0.5) then 1 else 0 end) as belowAverage, count(marks) as count from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id 
        join student_year s on s.student_admission_id=sa.id 
        join class c on s.class_id=c.id 
        join class_section cs on s.class_section_id=cs.id 
        join assessments_entities assent on aems.assessments_entities_id=assent.id 
        join assessments a on a.id=assent.assessment_id 
        join assessment_entity_master aem on aem.id=assent.entity_id 
        join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
        where assent.id in (select id from assessments_entities where assessment_id in ($assIds) and entity_id in ($componentIds)) and 
        student_id in (select student_admission_id from student_year where class_id=$classId) and sa.admission_status=2 
        group by cs.id,aem.id,a.id 
        order by aeg.sorting_order, aem.sorting_order, aem.id, a.sorting_order, a.id, cs.id";
        $marksList = $this->db_readonly->query($sql)
          ->result();
        // echo "<pre>"; print_r($this->db->last_query());
        return $marksList;
    }

    public function getStudentMarks() {

    }

    public function getComponentPerfData($assId, $classId, $components, $average_percentage = 35){
        $multiplier= $average_percentage / 100;
        $componentIds = implode(",", $components);
        $sql = "select aeg.is_elective, ifnull(aeg.elective_group_id, 0) as elective_group_id, cs.class_id as class_id, cs.id as class_section_id, concat(c.class_name, cs.section_name) as csName, round(avg(aems.marks), 2) as average, max(aems.marks) as highest, min(case when aems.marks > -1 then aems.marks end) as lowest, assent.assessment_id as assId, assent.total_marks, aem.short_name as entity_name,aem.name as diplay_name, aem.id as aemId, aem.ass_entity_gid as groupId, sum(case when marks in (-1, -2, -3) then 1 else 0 end) as absentees, aeg.entity_name as group_name,sum(case when aems.marks != -1 && aems.marks != -2 && aems.marks != -3 && aems.marks<(assent.total_marks* $multiplier) then 1 else 0 end) as belowAverage, count(marks) as count, if(cs.class_teacher_id is null or cs.class_teacher_id = 0, '', concat(sm.first_name, ' ', ifnull(sm.last_name, ''))) as teacher, if(cs.assistant_class_teacher_id is null or cs.assistant_class_teacher_id = 0, '', concat(sm1.first_name, ' ', ifnull(sm1.last_name, ''))) as ass_teacher_1, if(cs.assistant_class_teacher_2 is null or cs.assistant_class_teacher_2 = 0, '', concat(sm2.first_name, ' ', ifnull(sm2.last_name, ''))) as ass_teacher_2 
        from assessments_entities_marks_students aems
        left join student_admission sa on aems.student_id=sa.id
        left join student_year s on s.student_admission_id=sa.id
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id

        left join staff_master sm on sm.id= cs.class_teacher_id 
        left join staff_master sm1 on sm1.id= cs.assistant_class_teacher_id 
        left join staff_master sm2 on sm2.id= cs.assistant_class_teacher_2 

        left join assessments_entities assent on aems.assessments_entities_id=assent.id
        left join assessment_entity_master aem on aem.id=assent.entity_id
        left join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid
        where c.id= '$classId' and c.acad_year_id= '$this->yearId'
        and assent.id in (select id from assessments_entities where assessment_id='$assId' and entity_id in ($componentIds)) and 
        cs.class_id='$classId' and 
        student_id in (select student_admission_id from student_year where class_id='$classId') and sa.admission_status=2
        group by cs.id,aem.id";
        $marksList = $this->db_readonly->query($sql)
          ->result();

        if(!empty($marksList)) {
            foreach($marksList as $key => $val) {
                // $teacher= $this->db_readonly->select("if(cs.class_teacher_id is null or cs.class_teacher_id = 0, '', concat(sm.first_name, ' ', ifnull(sm.last_name, ''))) as teacher, if(cs.assistant_class_teacher_id is null or cs.assistant_class_teacher_id = 0, '', concat(sm1.first_name, ' ', ifnull(sm1.last_name, ''))) as ass_teacher_1, if(cs.assistant_class_teacher_2 is null or cs.assistant_class_teacher_2 = 0, '', concat(sm2.first_name, ' ', ifnull(sm2.last_name, ''))) as ass_teacher_2")
                //             ->from('class_section cs')
                //             ->join('staff_master sm', 'sm.id= cs.class_teacher_id', 'left')
                //             ->join('staff_master sm1', 'sm1.id= cs.assistant_class_teacher_id', 'left')
                //             ->join('staff_master sm2', 'sm2.id= cs.assistant_class_teacher_2', 'left')
                //             ->where('cs.id', $val->class_section_id)
                //             ->get()->result();
                if(!empty($val)) {
                    if($val->teacher) {
                        $val->class_teacher= $val->teacher;
                    } else if($val->ass_teacher_1) {
                        $val->class_teacher= $val->ass_teacher_1;
                    } else if($val->ass_teacher_2) {
                        $val->class_teacher= $val->ass_teacher_2;
                    } else {
                        $val->class_teacher= '';
                    }
                } else {
                    $val->class_teacher= '';
                }

                // students if elective
                if($val->is_elective) {
                    $stds= $this->db_readonly->select("count(ase.student_id) as count")
                        ->from('student_admission sa')
                        ->join('student_year sy', 'sy.student_admission_id=sa.id')
                        ->join('class_section cs', 'cs.id = sy.class_section_id')
                        ->join('assessment_students_elective ase', 'ase.student_id = sa.id')
                        ->where('ase.ass_elective_gid', $val->elective_group_id)
                        ->where('ase.ass_entity_gid', $val->groupId)
                        ->where('cs.id', $val->class_section_id)
                        ->where_not_in('sy.promotion_status', ['4', '5', 'JOINED'])
                        ->where('sa.admission_status', 2)
                        ->where('sy.acad_year_id', $this->yearId)
                        ->group_by('cs.id')
                        ->get()->row();

                       

                    if(!empty($stds)) {
                        $val->count= $stds->count;
                    }
                }
                // echo "<pre>"; print_r($marksList); die();


            }
        }

        

        return $marksList;
    }

    public function getGroupPerfData($assId, $classId, $gIds){
        $gIds = implode(",", $gIds);

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 's.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 's.alpha_rollnum';
        }
        $sql = "select aeg.id as groupId, aeg.is_elective, ifnull(aeg.elective_group_id, 0) as elective_group_id, cs.class_id as class_id, cs.id as class_section_id, $std_name, aems.student_id, sum(total_marks) total_marks, concat(aeg.entity_name, ' (G)') as entity_name, 
        concat(c.class_name, cs.section_name) as csName, sum(case when marks in (-1, -2, -3) then 0 else marks end) as sumMarks, sum(case when marks in (-1, -2, -3) then 0 else marks end) as marks, count(marks) as count, sum(case when marks in (-1, -2, -3) then 1 else 0 end) as absent, aeg.id as group_id, concat(aeg.entity_name, ' (G)') as display_name, if(cs.class_teacher_id is null or cs.class_teacher_id = 0, '', concat(sm.first_name, ' ', ifnull(sm.last_name, ''))) as teacher, if(cs.assistant_class_teacher_id is null or cs.assistant_class_teacher_id = 0, '', concat(sm1.first_name, ' ', ifnull(sm1.last_name, ''))) as ass_teacher_1, if(cs.assistant_class_teacher_2 is null or cs.assistant_class_teacher_2 = 0, '', concat(sm2.first_name, ' ', ifnull(sm2.last_name, ''))) as ass_teacher_2 
        from assessments_entities_marks_students aems 
        join student_admission sa on sa.id=aems.student_id 
        left join student_year s on s.student_admission_id=sa.id 
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 

        left join staff_master sm on sm.id= cs.class_teacher_id 
        left join staff_master sm1 on sm1.id= cs.assistant_class_teacher_id 
        left join staff_master sm2 on sm2.id= cs.assistant_class_teacher_2 

        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where  c.id= $classId and c.acad_year_id= $this->yearId 
        and assessments_entities_id in 
        (select id from assessments_entities where assessment_id=$assId and 
        entity_id in (select id from assessment_entity_master where ass_entity_gid in ($gIds))) 
        and student_id in (select student_admission_id from student_year where class_id=$classId) and sa.admission_status=2 and 
        cs.class_id=$classId and 
        aem.ass_type!='Derived' 
        group by ae.assessment_id,aeg.id,student_id order by $order_by";

        $marksList = $this->db_readonly->query($sql)
          ->result();

          if(!empty($marksList)) {
            foreach($marksList as $key => $val) {
                // $teacher= $this->db_readonly->select("if(cs.class_teacher_id is null or cs.class_teacher_id = 0, '', concat(sm.first_name, ' ', ifnull(sm.last_name, ''))) as teacher, if(cs.assistant_class_teacher_id is null or cs.assistant_class_teacher_id = 0, '', concat(sm1.first_name, ' ', ifnull(sm1.last_name, ''))) as ass_teacher_1, if(cs.assistant_class_teacher_2 is null or cs.assistant_class_teacher_2 = 0, '', concat(sm2.first_name, ' ', ifnull(sm2.last_name, ''))) as ass_teacher_2")
                //             ->from('class_section cs')
                //             ->join('staff_master sm', 'sm.id= cs.class_teacher_id', 'left')
                //             ->join('staff_master sm1', 'sm1.id= cs.assistant_class_teacher_id', 'left')
                //             ->join('staff_master sm2', 'sm2.id= cs.assistant_class_teacher_2', 'left')
                //             ->where('cs.id', $val->class_section_id)
                //             ->get()->result();
                if(!empty($val)) {
                    if($val->teacher) {
                        $val->class_teacher= $val->teacher;
                    } else if($val->ass_teacher_1) {
                        $val->class_teacher= $val->ass_teacher_1;
                    } else if($val->ass_teacher_2) {
                        $val->class_teacher= $val->ass_teacher_2;
                    } else {
                        $val->class_teacher= '';
                    }
                } else {
                    $val->class_teacher= '';
                }

                // students if elective
                if($val->is_elective) {
                    $stds= $this->db_readonly->select("count(ase.student_id) as count")
                        ->from('student_admission sa')
                        ->join('student_year sy', 'sy.student_admission_id=sa.id')
                        ->join('class_section cs', 'cs.id = sy.class_section_id')
                        ->join('assessment_students_elective ase', 'ase.student_id = sa.id')
                        ->where('ase.ass_elective_gid', $val->elective_group_id)
                        ->where('ase.ass_entity_gid', $val->groupId)
                        ->where('cs.id', $val->class_section_id)
                        ->where_not_in('sy.promotion_status', ['4', '5', 'JOINED'])
                        ->where('sa.admission_status', 2)
                        ->where('sy.acad_year_id', $this->yearId)
                        ->where('sa.id', $val->student_id)
                        ->group_by('cs.id')
                        ->get()->row();

                       

                    if(!empty($stds)) {
                        $val->count= $stds->count;
                    }
                }
                // echo "<pre>"; print_r($teacher); die();
            }
        }

        //   echo "<pre>"; print_r($marksList); die();

        return $marksList;
    }

    public function getComponentBelowAvg($assId, $classId, $components, $average_percentage){
        $multiplier= $average_percentage / 100;
        $componentIds = implode(",", $components);
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 's.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 's.alpha_rollnum';
        }

        $sql = "select $std_name, concat(c.class_name, cs.section_name) as csName, aems.student_id, (case when aems.marks=-1 then 'Absent' else marks end) as marks, assent.total_marks, concat(aeg.entity_name, ' (C)') as gName,concat(aem.name, ' (C)') as display_name, aem.ass_entity_gid as groupId, if(marks<0, 'Yes', 'No') as isAbsent from assessments_entities_marks_students aems
        left join student_admission sa on aems.student_id=sa.id
        left join student_year s on s.student_admission_id=sa.id
        left join class c on s.class_id=c.id
        left join class_section cs on s.class_section_id=cs.id
        left join assessments_entities assent on aems.assessments_entities_id=assent.id
        left join assessment_entity_master aem on aem.id=assent.entity_id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where aems.marks not in (-1, -2, -3) and assent.id in (select id from assessments_entities where assessment_id=$assId and entity_id in ($componentIds)) and 
        cs.class_id=$classId and 
        student_id in (select student_admission_id from student_year where class_id=$classId) and sa.admission_status=2 and aems.marks<(assent.total_marks* $multiplier)
        order by $order_by";
        $marksList = $this->db_readonly->query($sql)
          ->result();

        //   echo "<pre>"; print_r($marksList); die();

        return $marksList;
    }

    public function getSectionAssessments($sectionId, $assessment_type) {
        // echo $assessment_type; die();
        $sql = "select id, short_name as assessment_name from assessments a where a.generation_type = '$assessment_type' and a.class_id=(select class_id from class_section where id=$sectionId) and a.acad_year_id=$this->yearId";
        $result = $this->db_readonly->query($sql)->result();

        return $result;
    }

    public function getAssessments($assIds) {
        $ids = implode(",", $assIds);
        $sql = "select a.id as assId, a.short_name as assName from assessments a where a.id in ($ids)";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function studentMarks($student_id, $assIds) {
        $ass_ids = implode(",", $assIds);
        $sql = "select a.id as assessment_id, a.short_name as assessment_name, round(aems.marks, 2) as score, aem.name as entity_name, aem.short_name as entity_short_name, aeg.entity_name as group_name, ae.entity_id, aeg.id as group_id, round(ae.total_marks, 2) as total_marks 
                from assessments_entities_marks_students aems 
                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                join assessments a on a.id=ae.assessment_id 
                join assessment_entity_master aem on aem.id=ae.entity_id 
                join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id 
                where aems.student_id=$student_id and ae.assessment_id in ($ass_ids) and aem.evaluation_type='marks' and aem.ass_type!='Derived' 
                order by aeg.sorting_order, aeg.id, aem.sorting_order, aem.id, ae.assessment_id";
        $marks = $this->db_readonly->query($sql)->result();

        $assessments = array();
        $subjects = array();
        foreach ($marks as $key => $mark) {
            if(!array_key_exists($mark->group_id, $subjects)) {
                $subjects[$mark->group_id] = array();
                $subjects[$mark->group_id]['group_name'] = $mark->group_name;
                $subjects[$mark->group_id]['entities'] = array();
            } 
            if(!array_key_exists($mark->entity_id, $subjects[$mark->group_id]['entities'])) {
                $subjects[$mark->group_id]['entities'][$mark->entity_id] = array();
                $subjects[$mark->group_id]['entities'][$mark->entity_id]['name'] = $mark->entity_name;
                $subjects[$mark->group_id]['entities'][$mark->entity_id]['short_name'] = $mark->entity_short_name;
                $subjects[$mark->group_id]['entities'][$mark->entity_id]['assessments'] = array();

                $subjects[$mark->group_id]['entities'][$mark->entity_id]['group_id'] = $mark->group_id;
                $subjects[$mark->group_id]['entities'][$mark->entity_id]['entity_id'] = $mark->entity_id;
            }
            $subjects[$mark->group_id]['entities'][$mark->entity_id]['assessments'][$mark->assessment_id] = array('name' => $mark->assessment_name, 'marks' => $mark->score, 'total_marks' => $mark->total_marks);
            $assessments[$mark->assessment_id] = $mark->assessment_name;
        }
        return array('subjects' => $subjects, 'assessments' => $assessments);
    }

    public function getComponentsByGids($gIds){
        return $this->db_readonly->select('id, name')->where_in('ass_entity_gid', $gIds)->get('assessment_entity_master')->result();
    }

    public function getAssWiseSubjects($assId, $mode){
        if($mode == 'component') {
            $this->db_readonly->select('ae.entity_id as subId, aem.name as subName');
            $this->db_readonly->from('assessments_entities ae');
            $this->db_readonly->join('assessment_entity_master aem', 'ae.entity_id=aem.id');
            $this->db_readonly->where('ae.assessment_id', $assId);
            $this->db_readonly->order_by('aem.sorting_order');
            return $this->db_readonly->get()->result();
        } else if($mode == 'group') {
            $this->db_readonly->select('aeg.id as subId, aeg.entity_name as subName');
            $this->db_readonly->from('assessments_entities ae');
            $this->db_readonly->join('assessment_entity_master aem', 'ae.entity_id=aem.id');
            $this->db_readonly->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id');
            $this->db_readonly->where('ae.assessment_id', $assId);
            $this->db_readonly->group_by('aeg.id');
            $this->db_readonly->order_by('aeg.sorting_order');
            return $this->db_readonly->get()->result();
        } else if($mode == 'elective') {
            $this->db_readonly->select('distinct(el.id) as subId, el.group_name as subName');
            $this->db_readonly->from('assessments_entities ae');
            $this->db_readonly->join('assessment_entity_master aem', 'ae.entity_id=aem.id');
            $this->db_readonly->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id');
            $this->db_readonly->join('assessment_elective_group el', 'aeg.elective_group_id=el.id');
            $this->db_readonly->where('ae.assessment_id', $assId);
            $this->db_readonly->group_by('aeg.id');
            $this->db_readonly->order_by('aeg.sorting_order');
            return $this->db_readonly->get()->result();
        }
    }

    public function getStudents($section_id) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
        }
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 's.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 's.alpha_rollnum';
        }
        $sql = "select sa.id as student_id, $std_name, sa.admission_no, sy.roll_no, sa.enrollment_number 
                from student_admission sa 
                join student_year sy on sy.student_admission_id=sa.id
                where sy.class_section_id=$section_id and 
                sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5
                order by $order_by";
        return $this->db_readonly->query($sql)->result();
    }

    public function getAssessmentEntities($componentIds, $assIds) {
        $sql = "select a.id as assId, a.short_name, assent.id as ass_entity_id, assent.total_marks, aeg.entity_name as gName,aem.name as entity_name,aem.short_name as display_name, aem.id as entity_id, aeg.id as groupId, aeg.is_elective, aem.evaluation_type, aem.grading_system_id 
        from assessments a 
        join assessments_entities assent on a.id=assent.assessment_id
        join assessment_entity_master aem on aem.id=assent.entity_id
        join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where assessment_id in ($assIds) and entity_id in ($componentIds)
        order by aeg.sorting_order,aeg.id,aem.sorting_order,aem.id,a.sorting_order,a.id";
        $marksList = $this->db_readonly->query($sql)->result();
        $ass_subjects = array();
        foreach ($marksList as $key => $list) {
            $groupId = $list->groupId;
            if(!array_key_exists($groupId, $ass_subjects)) {
                $ass_subjects[$groupId] = array();
                $ass_subjects[$groupId]['group_name'] = $list->gName;
                $ass_subjects[$groupId]['group_total'] = 0;
                $ass_subjects[$groupId]['group_marks'] = 0;
                $ass_subjects[$groupId]['is_elective'] = $list->is_elective;
                // $ass_subjects[$groupId]['assessments'] = array();
                $ass_subjects[$groupId]['entities'] = array();
            }
            $ass_subjects[$groupId]['group_total'] += $list->total_marks;
            $ass_subjects[$groupId]['entities'][$list->ass_entity_id] = array(
                'entity_id' => $list->entity_id,
                'entity_name' => $list->entity_name,
                'display_name' => $list->display_name,
                'eval_type' => $list->evaluation_type,
                'grading_system_id' => $list->grading_system_id,
                'ass_entity_id' => $list->ass_entity_id,
                'assessment_id' => $list->assId,
                'assessment_name' => $list->short_name,
                'total' => $list->total_marks
            );
            /*$ass_subjects[$groupId]['assessments'][$list->assId] = array(
                'ass_entity_id' => $list->ass_entity_id,
                'id' => $list->assId,
                'name' => $list->short_name,
                'total' => $list->total_marks
            );*/
        }
        return $ass_subjects;
    }

    public function getComponentMarks($componentIds, $assIds, $student_ids){
        if(empty($student_ids)) return array();
        $std_ids = implode(",", $student_ids);
        $sql = "select aems.student_id, aems.assessments_entities_id as ass_entity_id, aems.marks as marks, if(marks<0, 1, 0) as isAbsent, aems.grade
        from assessments_entities_marks_students aems 
        where aems.assessments_entities_id in (select id from assessments_entities where assessment_id in ($assIds) and entity_id in ($componentIds)) and 
        aems.student_id in ($std_ids)";
        $marksList = $this->db_readonly->query($sql)->result();
        $stdMarks = array();
        foreach ($marksList as $key => $value) {
            $stdId = $value->student_id;
            if(!array_key_exists($stdId, $stdMarks)) {
                $stdMarks[$stdId] = array();
            }
            $stdMarks[$stdId][$value->ass_entity_id] = array('marks' => $value->marks, 'grade' => $value->grade, 'isAbsent' => $value->isAbsent);
        }
        return $stdMarks;
    }

    public function getComponentWiseMarks($componentIds, $assIds, $section){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }
        
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 's.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 's.alpha_rollnum';
        }

        $section_str= implode(',', $section);

        $sql = "select $std_name, ifnull(sa.sts_number, '-') as sts_number, concat(c.class_name, cs.section_name) as csName,sa.admission_no, s. alpha_rollnum, s.roll_no, sa.enrollment_number, aems.student_id, aems.marks as marks, assent.assessment_id as assId, assent.total_marks, aem.short_name as ele,aeg.entity_name as gName,aem.short_name as entity_name,aem.short_name as diplay_name, aem.id as aemId, aem.ass_entity_gid as groupId, if(marks<0, 'Yes', 'No') as isAbsent, aeg.is_elective, aem.sorting_order as entity_sorting_order, aeg.sorting_order as group_sorting_order, aem.evaluation_type, aems.grade, aeg.id as ent_grp_id, aeg.elective_group_id 
        from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id
        left join student_year s on s.student_admission_id=sa.id and s.class_section_id in ($section_str)  
        left join class c on s.class_id=c.id
        left join class_section cs on s.class_section_id=cs.id
        left join assessments_entities assent on aems.assessments_entities_id=assent.id
        left join assessment_entity_master aem on aem.id=assent.entity_id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where s.promotion_status not in ('4', '5', 'JOINED') and s.acad_year_id= $this->yearId and assent.id in (select id from assessments_entities where assessment_id in ($assIds) and entity_id in ($componentIds)) and 
        student_id in (select student_admission_id from student_year where class_section_id in ($section_str)) and sa.admission_status=2
        order by aem.sorting_order,aeg.sorting_order,cs.section_name,$order_by";
        $marksList = $this->db_readonly->query($sql)
          ->result();

        //   echo '<pre>'; print_r($marksList); die();

        foreach($marksList as $key => $val) {
            $val->father= $this->db_readonly->select("TRIM( CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) ) as name")
                ->from('student_relation sr')
                ->join('parent p', 'p.id= sr.relation_id')
                ->where('sr.std_id', $val->student_id)
                ->where('sr.relation_type', 'Father')
                ->get()->row()->name;
        }

        return $marksList;
    }

    public function getMarksByGroup($groupIds, $assId, $section) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
        }
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 's.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 's.alpha_rollnum';
        }
        $sql = "select aems.student_id, $std_name,sa.admission_no, s. alpha_rollnum, s.roll_no, sa.enrollment_number, sa.admission_no, ae.assessment_id as assId,count(aem.id) as componentCount, sum(total_marks) total_marks, aeg.entity_name,aeg.entity_name as gName, aeg.is_elective, 
        concat(c.class_name, cs.section_name) as csName, SUBSTRING(aeg.entity_name,1,3) as ele, sum(if(marks<0, -1, marks)) as marks, if(marks<0, 'Yes', 'No') as isAbsent, aem.id as aemId, aeg.id as aemId, aeg.sorting_order
        from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id 
        left join student_year s on s.student_admission_id=sa.id and s.class_section_id=$section
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 
        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where assessments_entities_id in 
        (select id from assessments_entities where assessment_id in ($assId) and 
        entity_id in (select id from assessment_entity_master where ass_entity_gid in ($groupIds))) 
        and student_id in (select student_admission_id from student_year where class_section_id=$section) and sa.admission_status=2
        group by aeg.id,student_id order by $order_by";

        $marksList = $this->db_readonly->query($sql)
          ->result();

        return $marksList;
    }

    public function getAssEntitiesOfClass($classId, $subjectType){
        if ($subjectType == 'component') {
            $result = $this->db_readonly->select('aem.id, aem.name as subName, aem.sorting_order')
                ->from('assessment_entity_master aem')
                ->where('class_id', $classId)
                ->order_by('aem.sorting_order')
                ->get()->result();
        } else if($subjectType == 'group'){
            $result = $this->db_readonly->select('aeg.id, aeg.entity_name as subName, aeg.sorting_order')
                ->from('assessment_entities_group aeg')
                ->where('class_id', $classId)
                ->order_by('aeg.sorting_order')
                ->get()->result();
        } else if($subjectType == 'elective'){
            $result = $this->db_readonly->select('aeg.id, aeg.friendly_name as subName')
                ->from('assessment_elective_group aeg')
                ->where('class_id', $classId)
                ->get()->result();
        }

        return $result;
    }

    public function getMarksForEntity($assIds, $sections, $entityId) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
        }
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 's.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 's.alpha_rollnum';
        }
        $sql = "select $std_name, concat(c.class_name, cs.section_name) as csName,sa.admission_no, s.alpha_rollnum, s.roll_no, aems.student_id, aems.marks as marks, aems.percentage, aems.grade, assent.assessment_id as assId, assent.total_marks, (Case When (`aems`.`marks` = -1)
            Then 1 Else 0 End) as isAbsent from assessments_entities_marks_students aems
        left join student_admission sa on aems.student_id=sa.id
        left join student_year s on s.student_admission_id=sa.id
        left join class c on s.class_id=c.id
        left join class_section cs on s.class_section_id=cs.id
        left join assessments_entities assent on aems.assessments_entities_id=assent.id
        where assent.id in (select id from assessments_entities where assessment_id in ($assIds) and entity_id=$entityId) and 
        student_id in (select student_admission_id from student_year where class_section_id in ($sections)) and sa.admission_status=2
        order by $order_by";
        $marksList = $this->db_readonly->query($sql)
          ->result();

        return $marksList;
    }

    public function getMarksForEntityGroup($assIds, $sections, $groupId) {
        $sql = "select aems.student_id, concat(ifnull(sa.first_name,''),ifnull(sa.last_name,'')) as stdName,sa.admission_no, s. alpha_rollnum, s.roll_no, ae.assessment_id as assId, sum(ae.total_marks) as total_marks, concat(c.class_name, cs.section_name) as csName, sum(marks) as marks, aeg.entity_name as subName, sum(Case When (`aems`.`marks` = -1)
            Then 1 Else 0 End) as isAbsent, aem.id as group_id 
        from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id 
        left join student_year s on s.student_admission_id=sa.id 
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 
        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where assessments_entities_id in 
        (select id from assessments_entities where assessment_id in ($assIds) and 
        entity_id in (select id from assessment_entity_master where ass_entity_gid='$groupId')) 
        and student_id in (select student_admission_id from student_year where class_section_id in ($sections)) and sa.admission_status=2
        group by assId, student_id order by aem.sorting_order,aeg.sorting_order,cs.section_name,sa.first_name";

        $marksList = $this->db_readonly->query($sql)
          ->result();
        
        return $marksList;
    }

    public function getEntities($entityIds){
        $entIds = implode(",", $entityIds);
        return $this->db_readonly->query("select id, name from assessment_entity_master where id in ($entIds)")->result();
    }

    public function getGroupWiseMarks($groupIds, $assIds, $section){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }
        
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 's.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 's.alpha_rollnum';
        }

        $section_str= implode(',', $section);

        $sql = "select ifnull(sa.sts_number, '-') as sts_number, aems.student_id, $std_name,sa.admission_no, s. alpha_rollnum, s.roll_no, sa.enrollment_number, ae.assessment_id as assId, sum(total_marks) total_marks, aeg.entity_name, aeg.is_elective, 
        concat(c.class_name, cs.section_name) as csName, aem.short_name as ele, sum(case when marks>0 then marks else 0 end) as marks, aem.id as aemId, aeg.id as group_id, aeg.id as groupId, aeg.entity_name as diplay_name, aeg.sorting_order as group_sorting_order, aem.sorting_order as entity_sorting_order, 'marks' as evaluation_type, aeg.id as ent_grp_id, aeg.elective_group_id  
        from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id 
        left join student_year s on s.student_admission_id=sa.id and s.class_section_id in ($section_str)
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 
        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        where s.promotion_status not in ('4', '5', 'JOINED') and s.acad_year_id= $this->yearId and assessments_entities_id in 
        (select id from assessments_entities where assessment_id in ($assIds) and 
        entity_id in (select id from assessment_entity_master where ass_entity_gid in ($groupIds))) 
        and student_id in (select student_admission_id from student_year where class_section_id in ($section_str)) and sa.admission_status=2
        group by ae.assessment_id,aeg.id,student_id order by aeg.sorting_order,aeg.id,cs.section_name,$order_by";

        $marksList = $this->db_readonly->query($sql)
          ->result();

        foreach($marksList as $key => $val) {
            $val->father= $this->db_readonly->select("TRIM( CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) ) as name")
                                    ->from('student_relation sr')
                                    ->join('parent p', 'p.id= sr.relation_id')
                                    ->where('sr.std_id', $val->student_id)
                                    ->where('sr.relation_type', 'Father')
                                    ->get()->row()->name;
        }

        // echo '<pre>Grp'; print_r($marksList); die();
        return $marksList;
    }

    public function getElectiveWiseMarks($electiveId, $assIds, $section){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(s.roll_no = 0, 'NA', s.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(s.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        }

        $section_str= implode(',', $section);
        
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 's.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 's.alpha_rollnum';
        }

        $sql = "select ifnull(marks, 'no') as is_choosed, ifnull(sa.sts_number, '-') as sts_number, aems.student_id,$std_name,sa.admission_no, sa.enrollment_number, s.alpha_rollnum, s.roll_no, ae.assessment_id as assId, sum(total_marks) total_marks, aeg.is_elective,aeg.entity_name as gName,ael.group_name as entity_name, aem.short_name as ele, concat(c.class_name, cs.section_name) as csName, aem.id as aemId, sum(marks) as marks, aeg.id as group_id, aeg.id as groupId, aem.sorting_order as entity_sorting_order, aeg.sorting_order as group_sorting_order, if(marks<0, 'Yes', 'No') as isAbsent, ael.group_name as diplay_name, aem.evaluation_type, aeg.id as ent_grp_id, aeg.elective_group_id 
        from assessments_entities_marks_students aems 
        join student_admission sa on aems.student_id=sa.id
        left join student_year s on s.student_admission_id=sa.id and s.class_section_id in ($section_str)
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 
        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        left join assessment_elective_group ael on aeg.elective_group_id=ael.id
        where s.promotion_status not in ('4', '5', 'JOINED') and s.acad_year_id= $this->yearId and assessments_entities_id in 
        (select id from assessments_entities where assessment_id in ($assIds) and 
        entity_id in (select id from assessment_entity_master where ass_entity_gid in (select id from assessment_entities_group where elective_group_id in ($electiveId)))) 
        and student_id in (select student_admission_id from student_year where class_section_id in ($section_str)) and sa.admission_status=2
        group by assId,entity_id,student_id order by cs.section_name,$order_by";

        $marksList = $this->db_readonly->query($sql)->result();

        if(!empty($marksList)) {
            foreach($marksList as $key => $val) {
                if($val->is_choosed == '-3.00' || $val->is_choosed == 'no') {
                    unset($marksList[$key]);
                }
            }
        }


// // Anish
//         $this->db_readonly->select("ifnull(sa.sts_number, '-') as sts_number, aems.student_id, $std_name, sa.admission_no, sa.enrollment_number, s.alpha_rollnum, s.roll_no, ae.assessment_id as assId, sum(total_marks) total_marks, aeg.is_elective, aeg.entity_name as gName, ael.group_name as entity_name, aem.short_name as ele, concat(c.class_name, cs.section_name) as csName, aem.id as aemId, sum(marks) as marks, aeg.id as group_id, aeg.id as groupId, aem.sorting_order as entity_sorting_order, aeg.sorting_order as group_sorting_order, if(marks<0, 'Yes', 'No') as isAbsent, ifnull(marks, 'no') as is_choosed, ael.group_name as diplay_name, aem.evaluation_type, aeg.id as ent_grp_id, aeg.elective_group_id", FALSE);

//         $this->db_readonly->from('assessments_entities_marks_students aems');
//         $this->db_readonly->join('student_admission sa', 'aems.student_id=sa.id');
//         $this->db_readonly->join('student_year s', 's.student_admission_id=sa.id and s.class_section_id in ('.$section_str.')', 'left');
//         $this->db_readonly->join('class c', 's.class_id=c.id', 'left');
//         $this->db_readonly->join('class_section cs', 's.class_section_id=cs.id', 'left');
//         $this->db_readonly->join('assessments_entities ae', 'aems.assessments_entities_id=ae.id', 'left');
//         $this->db_readonly->join('assessment_entity_master aem', 'ae.entity_id=aem.id', 'left');
//         $this->db_readonly->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id', 'left');
//         $this->db_readonly->join('assessment_elective_group ael', 'aeg.elective_group_id=ael.id', 'left');

//         $this->db_readonly->where('s.promotion_status not in (\'4\', \'5\', \'JOINED\')');
//         $this->db_readonly->where('s.acad_year_id', $this->yearId);
//         $this->db_readonly->where('sa.admission_status', 2);

//         // Subquery for assessments_entities_id
//         $subquery1 = $this->db_readonly->select('id')
//             ->from('assessments_entities')
//             ->where('assessment_id in ('.$assIds.')')
//             ->where('entity_id in (select id from assessment_entity_master where ass_entity_gid in (select id from assessment_entities_group where elective_group_id in ('.$electiveId.')))', NULL, FALSE)
//             ->get_compiled_select();

//         $this->db_readonly->where('assessments_entities_id in ('.$subquery1.')', NULL, FALSE);

//         // Subquery for student_id
//         $subquery2 = $this->db_readonly->select('student_admission_id')
//             ->from('student_year')
//             ->where('class_section_id in ('.$section_str.')')
//             ->get_compiled_select();

//         $this->db_readonly->where('student_id in ('.$subquery2.')', NULL, FALSE);

//         $this->db_readonly->group_by('assId, entity_id, student_id');
//         $this->db_readonly->order_by('cs.section_name,'.$order_by);

//         $marksList = $this->db_readonly->get()->result();
// // Anish




        // $marksList= [];
        // $dddd= explode(',', $electiveId);
        // foreach($dddd as $key => $val) {
        //     $sql = "select ifnull(sa.sts_number, '-') as sts_number, aems.student_id,$std_name,sa.admission_no, sa.enrollment_number, s.alpha_rollnum, s.roll_no, ae.assessment_id as assId, sum(total_marks) total_marks, aeg.is_elective,aeg.entity_name as gName,ael.group_name as entity_name, aem.short_name as ele, concat(c.class_name, cs.section_name) as csName, aem.id as aemId, sum(marks) as marks, aeg.id as group_id, aeg.id as groupId, aem.sorting_order as entity_sorting_order, aeg.sorting_order as group_sorting_order, if(marks<0, 'Yes', 'No') as isAbsent, ael.group_name as diplay_name, aem.evaluation_type, aeg.id as ent_grp_id, aeg.elective_group_id 
        //             from assessments_entities_marks_students aems 
        //             join student_admission sa on aems.student_id=sa.id
        //             left join student_year s on s.student_admission_id=sa.id and s.class_section_id in ($section_str)
        //             left join class c on s.class_id=c.id 
        //             left join class_section cs on s.class_section_id=cs.id 
        //             left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        //             left join assessment_entity_master aem on ae.entity_id=aem.id
        //             left join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id
        //             left join assessment_elective_group ael on aeg.elective_group_id=ael.id
        //             where s.promotion_status not in ('4', '5', 'JOINED') and s.acad_year_id= $this->yearId and assessments_entities_id in 
        //             (select id from assessments_entities where assessment_id in ($assIds) and 
        //             entity_id in (select id from assessment_entity_master where ass_entity_gid in (select id from assessment_entities_group where elective_group_id in ($val)))) 
        //             and student_id in (select student_admission_id from student_year where class_section_id in ($section_str)) and sa.admission_status=2
        //             group by assId,entity_id,student_id order by cs.section_name,$order_by";

        //     $x = $this->db_readonly->query($sql)->result();
        //     if($key == 0) {
        //         $marksList= $x;
        //     } else {
        //         $marksList= array_merge($marksList, $x);
        //     }
        // }



        // echo '<pre>'; print_r($marksList); die();

          foreach($marksList as $key => $val) {
            $val->father= $this->db_readonly->select("TRIM( CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) ) as name")
                                    ->from('student_relation sr')
                                    ->join('parent p', 'p.id= sr.relation_id')
                                    ->where('sr.std_id', $val->student_id)
                                    ->where('sr.relation_type', 'Father')
                                    ->get()->row()->name;
        }
        
        return $marksList;
    }

    public function getRankingList($class_id, $section_id, $assessment_id, $subject_id, $groups_id, $electives_id) {
        
        if(empty($subject_id)) {
            $subject_id= [];
        }
        if(empty($groups_id)) {
            $groups_id= [];
        }
        if(empty($electives_id)) {
            $electives_id= [];
        }
        // echo '<pre>'; print_r($electives_id); die();
        // $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
        // if ($display_roll_no_with_student_name == 1) {
        //   $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        // } else {
        //   $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
        // }
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if($this->settings->getSetting('school_short_name') == 'vsips') {
            if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), ' - ', if(sy.roll_no = 0, 'NA', sy.roll_no)) as stdName";
            } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), ' - ', ifnull(sa.enrollment_number, 'NA')) as stdName";
            } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), ' - ', ifnull(sa.admission_no, 'NA')) as stdName";
            } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), ' - ', ifnull(sa.registration_no, 'NA')) as stdName";
            } else if ($prefix_student_name == "alpha_rollnum") {
                $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), ' - ', ifnull(sy.alpha_rollnum, 'NA')) as stdName";
            }else {
            $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
            }
        } else {
            if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
            } else if ($prefix_student_name == "enrollment_number") {
            $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
            } else if ($prefix_student_name == "admission_number") {
            $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
            } else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
            } else if ($prefix_student_name == "alpha_rollnum") {
                $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName";
            }else {
            $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS stdName";
            }
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if($this->settings->getSetting('school_short_name') == 'vsips') {
            if ($prefix_order_by == "roll_number") {
                $order_by = 'sa.first_name asc, sy.roll_no';
            }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'sa.first_name asc, sa.enrollment_number';
            }else if($prefix_order_by == "admission_number"){
                $order_by = 'sa.first_name asc, sa.admission_no';
            }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sa.first_name asc, sy.alpha_rollnum';
            }
        } else {
            if ($prefix_order_by == "roll_number") {
                $order_by = 'sy.roll_no';
            }else if($prefix_order_by == "enrollment_number"){
                $order_by = 'sa.enrollment_number';
            }else if($prefix_order_by == "admission_number"){
                $order_by = 'sa.admission_no';
            }else if($prefix_order_by == "alpha_rollnum"){
                $order_by = 'sy.alpha_rollnum';
            }
        }
        $std_sql = "SELECT sa.id as student_id, $std_name,sa.admission_no, sy.roll_no, concat(cs.class_name, cs.section_name) as csName 
                from student_admission sa 
                join student_year sy on sa.id=sy.student_admission_id 
                join class_section cs on cs.id=sy.class_section_id 
                where sy.class_id=$class_id 
                and sa.admission_status=2 
                and sy.promotion_status!=4 and sy.promotion_status!=5 ";
        if($section_id) {
            $std_sql .= "and sy.class_section_id=$section_id order by $order_by";
        }

        $students = $this->db_readonly->query($std_sql)->result();

        // echo '<pre>'; print_r($students); die();

        $std_ids = [];
        foreach ($students as $std) {
            $std_ids[] = $std->student_id;
        }

        $students_ids_arr= $std_ids;
        $std_ids = implode(",", $std_ids);

        $cond_sub= '';

        // Merging comp, grp and eles
        $groupsOnlyArr= [];
        $electivesOnlyArr= [];

        if(!empty($groups_id)) {
            foreach($groups_id as $key => $val) {
                $grp_arr= explode(',', $val);
                foreach($grp_arr as $k => $v) {
                    $groupsOnlyArr[]= $v;
                }
            }
        }

        

        if(!empty($electives_id)) {
            foreach($electives_id as $key => $val) {
                $ele_arr= explode(',', $val);
                foreach($ele_arr as $k => $v) {
                    $electivesOnlyArr[]= $v;
                }
            }
        }

        
        $entities_for_total=  array_merge($subject_id, $groupsOnlyArr);
        
        
        $subject_id= array_merge($subject_id, $groupsOnlyArr, $electivesOnlyArr);
        
        if(!empty($subject_id)) {
            $subs_ids_str= implode(",", $subject_id);
            $cond_sub= " and ae.entity_id in ($subs_ids_str) ";
        }
        $electives= $this->db_readonly->select("aem.id as aemId, aeg.id as aegId, ael.id as aelId")
            ->from('assessment_entity_master aem')
            ->join('assessment_entities_group aeg', 'aeg.id = aem.ass_entity_gid')
            ->join('assessment_elective_group ael', 'ael.id = aeg.elective_group_id')
            ->where("aem.id in ($subs_ids_str)")
            ->where('aeg.is_elective', 1)
            ->get()->result();
        
        if(count(explode(',',$subs_ids_str)) == count($electives) && !empty($electives)) {
            $aegIds= [];
            $aelIds= [];
            foreach($electives as $k3 => $v3) {
                $aegIds[]= $v3->aegId;
                $aelIds[]= $v3->aelId;
            }
            
            $elective_students= $this->db_readonly->select("group_concat(distinct(student_id)) as std_str")
                ->where_in('ass_elective_gid', $aelIds)
                ->where_in('ass_entity_gid', $aegIds)
                ->where("student_id in ($std_ids)")
                ->get('assessment_students_elective')->row();
            // echo '<pre> Bad me: '; print_r($elective_students); die();
            if(!empty($elective_students)) {
                $students_ids_arr= explode(',', $elective_students->std_str);
                $std_ids= $elective_students->std_str;
            }
        }

        // $entities_for_total
        // $electivesOnlyArr
        $componentTotalMarksSum= [];
        if(!empty($entities_for_total))
        $componentTotalMarksSum= $this->db_readonly->select("sum(total_marks) as total_marks")
            ->from('assessments_entities')
            ->where_in('entity_id', $entities_for_total)
            ->where('assessment_id', $assessment_id)
            ->get()->row();
            //  echo '<pre>'; print_r($componentTotalMarksSum); die();
            $electivesTotalMarks= [];
        if(!empty($electivesOnlyArr))
        $electivesTotalMarks= $this->db_readonly->select("sum(ae.total_marks) as total_marks, aeg.elective_group_id, aeg.id")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id = ae.entity_id')
            ->join('assessment_entities_group aeg', 'aeg.id = aem.ass_entity_gid')
            ->where_in('ae.entity_id', $electivesOnlyArr)
            ->where('ae.assessment_id', $assessment_id)
            ->group_by('aeg.elective_group_id, aeg.id')
            ->get()->result();

        $totalMarks= 0;
        if(!empty($componentTotalMarksSum)) {
            $totalMarks += $componentTotalMarksSum->total_marks;
        }
        if(!empty($electivesTotalMarks)) {
            $is_added_ele_id_arr= [];
            foreach($electivesTotalMarks as $key => $val) {
                if(empty($is_added_ele_id_arr) || !in_array($val->elective_group_id, $is_added_ele_id_arr)) {
                    $totalMarks += $val->total_marks;
                    $is_added_ele_id_arr[]= $val->elective_group_id;
                }
            }
        }

        // echo '<pre>'; print_r($componentTotalMarksSum); die();

        $marks_sql = "SELECT aems.student_id, sum(case when aems.marks>=0 then aems.marks else 0 end) as marks, $totalMarks as total_marks 
                    from assessments_entities ae 
                    join assessments_entities_marks_students aems on aems.assessments_entities_id=ae.id 
                    where ae.assessment_id=$assessment_id 
                    and aems.student_id in ($std_ids) 
                    and aems.marks!=-3 and aems.marks!=-2 
                    $cond_sub 
                    group by aems.student_id";
       
        $list = $this->db_readonly->query($marks_sql)->result();
        $marks_list = [];
        foreach ($list as $l) {
            $marks_list[$l->student_id] = $l;
        }

        foreach ($students as $i => $std) {
            if(!in_array($std->student_id, $students_ids_arr)) {
                unset($students[$i]);
                continue;
            }
            $sid = $std->student_id;
            $students[$i]->marks = '-';
            $students[$i]->total_marks = '-';
            $students[$i]->percentage = '-';
            if(array_key_exists($sid, $marks_list)) {
                $t_marks = $marks_list[$sid]->total_marks;
                $marks = $marks_list[$sid]->marks;
                if($t_marks != 0)
                $students[$i]->percentage = ($t_marks)?(round(($marks/$t_marks)*100, 2)):'-';
                $students[$i]->marks = ($t_marks)?$marks:'-';
                $students[$i]->total_marks = ($t_marks)?$t_marks:'-';
            }

        }

        // if($this->settings->getSetting('school_short_name') != 'vsips') { // commenting because rank will become un-ordered
            usort($students, function ($item1, $item2) {
                return $item2->percentage <=> $item1->percentage;
            }); 
        // }    
        $ranks = array();
        $rank = 1;
        foreach ($students as $i => $std) {
            if($students[$i]->total_marks == '-') {
                $students[$i]->rank = '-';
                continue;
            }
            if(in_array($students[$i]->percentage, $ranks)) {
                array_push($ranks, $students[$i]->percentage);
                $students[$i]->rank = $rank;
            } else {
                $prev_rank_count = count($ranks);
                $ranks = array();
                array_push($ranks, $students[$i]->percentage);
                $students[$i]->rank = $rank + $prev_rank_count;
                $rank = $rank + $prev_rank_count;
            }
        }
        return $students;

        /*$sql = "select aems.student_id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,sa.admission_no, s.roll_no, ae.assessment_id as assId, sum(case when marks=-3 OR marks=-2 then 0 else total_marks end) as total_marks, concat(c.class_name, cs.section_name) as csName, sum(case when marks>=0 then marks else 0 end) as marks
        from assessments_entities_marks_students aems 
        left join student_admission sa on sa.id=aems.student_id 
        join student_year s on s.student_admission_id=sa.id
        left join class c on s.class_id=c.id 
        left join class_section cs on s.class_section_id=cs.id 
        left join assessments_entities ae on aems.assessments_entities_id=ae.id 
        left join assessment_entity_master aem on ae.entity_id=aem.id
        where ae.assessment_id=$assessment_id and ";
        if($section_id) {
            $sql .= "aems.student_id in (select student_admission_id from student_year where class_section_id=$section_id)";
        } else {
            $sql .= "aems.student_id in (select student_admission_id from student_year where class_id=$class_id)";
        }
        $sql .= " and aem.ass_type='Internal' and aem.evaluation_type='marks' and sa.admission_status=2 group by aems.student_id order by marks desc";

        $list = $this->db_readonly->query($sql)->result();
        $ranks = array();
        $rank = 1;
        foreach ($list as $key => $val) {
            $val->percentage = (round(($val->marks/$val->total_marks)*100, 2));
            if(in_array($val->percentage, $ranks)) {
                array_push($ranks, $val->percentage);
                $val->rank = $rank;
            } else {
                $prev_rank_count = count($ranks);
                $ranks = array();
                array_push($ranks, $val->percentage);
                $val->rank = $rank + $prev_rank_count;
                $rank = $rank + $prev_rank_count;
            }
        }
        // echo "<pre>"; print_r($list); die();
        return $list;*/
    }

    public function getGradingScaleValues($assessment_id) {
        $sql = "select grades from assessment_grading_system where id in 
                (select distinct grading_system_id from assessment_entity_master where id in 
                (select entity_id from assessments_entities where assessment_id=$assessment_id))";
        $grading = $this->db_readonly->query($sql)->result();

        $grades = array();
        foreach($grading as $key => $val) {
            $gr = json_decode($val->grades);
            foreach ($gr as $key => $g) {
                if(!in_array($g->grade, $grades)) {
                    array_push($grades, $g->grade);
                }
            }
        }
        $default = ['A+', 'A1', 'A2', 'A', 'B+', 'B1', 'B2', 'B', 'C+', 'C1', 'C2', 'C', 'D1', 'D2', 'D', 'E'];
        $sorted = array();
        foreach ($default as $g) {
            if(in_array($g, $grades)) {
                array_push($sorted, $g);
            }
        }
        return $sorted;
    }

    public function getSubjectMarks_for_subjectwise_analysis($ass_id, $class_id, $section_id, $sub_id_arr, $grading_system_id) {
        $component_ids_str = implode(",", $sub_id_arr);

        // Get Student IDs for the section
        $student_ids_str = $this->_get_student_ids_str($section_id);
        $replaced = str_replace(',', '_', $student_ids_str);
        $class_strength = $this->_strength_in_section($section_id);

        // Query for count Absent
        $sql = "select sum(case when marks != -1 && marks != -2 && marks != -3 then 1 else 0 end) as appeared_number, sum(case when marks=-1 then 1 else 0 end) as absentees, sum(case when marks=-2 then 1 else 0 end) as to_be_entered, sum(case when marks=-3 then 1 else 0 end) as not_applicable, aem.id as entity_id, aem.name, assent.total_marks, '$ass_id' as assessment_id, '$class_id' as class_id, '$replaced' as student_ids_str  
        from assessments_entities_marks_students aems
         
        join assessments_entities assent on aems.assessments_entities_id=assent.id 
        join assessment_entity_master aem on aem.id=assent.entity_id 
        where assent.entity_id in ($component_ids_str) and aems.student_id in ($student_ids_str) and assent.assessment_id = $ass_id 
        group by assent.entity_id";

        $sub_id_obj_arr = $this->db_readonly->query($sql)->result();
        
        // Get Dynamic ranges by grading system
        $grades= $this->db_readonly->select('grades')->where('id', $grading_system_id)->get('assessment_grading_system')->result();
        $grades_arr='';
        if(!empty($grades)) {
            $grades_arr= json_decode($grades[0]->grades);
        }

        // Creating Array Object
        $sub_arr= [];
        $i= 0;
        foreach ($sub_id_obj_arr as $key => &$val) {
            $marks= $this->_obtained_marks_analysis($val->entity_id, $val->total_marks, $student_ids_str, $grading_system_id, $ass_id);
           
            $val->strength= $class_strength;

            if(!empty($marks)) {
                $val->hundred_percent = $marks->hund_count;
            } else {
                $val->hundred_percent = 0;
            }
            $apeared_number= 0;
            if(!empty($grades_arr)) {
                foreach($grades_arr as $gkey => $gval) {
                    $from= $gval->from;
                    $to= $gval->to;
                    $as_alias= "between_$from"."_and_".$to;
                    if(!empty($marks)) {
                        $val->$as_alias = $marks->$as_alias;
                        $apeared_number += $marks->$as_alias;
                    } else {
                        $val->$as_alias = 0;
                        $apeared_number += 0;
                    }
                }
            }
            // $val->absenty = $val->strength - ($apeared_number);
            $val->absenty = $val->strength - $val->appeared_number;
            $val->appeared = $val->strength - $val->absenty;

            if(!empty($marks)) {
                $val->average = $marks->subject_average;
                $val->highest = $marks->highest;
                $val->lowest = $marks->lowest;
            } else {
                $val->average = 0;
                $val->highest = 0;
                $val->lowest = 0;
            }
            $val->total = $val->total_marks;
        }
        // echo '<pre>'; print_r($sub_id_obj_arr); die();

        return $sub_id_obj_arr;
    }

    private function _get_student_ids_str($section_id) {
        $stud_ids= $this->db_readonly->select('sa.id as student_id')
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $section_id)
            ->where_not_in('sy.promotion_status', array('4', '5', 'JOINED'))
            ->where('sy.acad_year_id', $this->yearId)
            ->where('sa.admission_status', '2')
            ->get()->result();


        $stud_ids_str = '';
        $i = 0;
        $count = count($stud_ids);
        foreach ($stud_ids as $std_id) {
            $stud_ids_str .= $std_id->student_id;
            if ($i ++ < $count - 1) {
                $stud_ids_str .= ',';
            }
        }
        return $stud_ids_str;
    }

    private function _obtained_marks_analysis($entity_id, $total_mark, $student_ids_str, $grading_system_id, $ass_id) {
        $grades= $this->db_readonly->select('grades')->where('id', $grading_system_id)->get('assessment_grading_system')->result();
        $grades_arr= array();
        if(!empty($grades)) {
            $grades_arr= json_decode($grades[0]->grades);
        }
        // echo '<pre>AA'; print_r($grading_system_id); die();

        $query= "select ae.entity_id, max(aems.marks) as highest, min(aems.marks) as lowest, round(avg(aems.marks), 2) as subject_average,  
                sum(case when (aems.marks = $total_mark) then 1 else 0 end) as hund_count";

        if(!empty($grades_arr)) {
            foreach($grades_arr as $key => $val) {
                $from= $val->from;
                $to= $val->to;
                $as_alias= "between_$from"."_and_".$to;
                $query .= ", sum(case when (aems.marks*100)/$total_mark >= $from and (aems.marks*100)/$total_mark <= $to  then 1 else 0 end) as '$as_alias'";
            }
        }
        $query .= " from assessments_entities_marks_students aems
                    join assessments_entities ae on ae.id= aems.assessments_entities_id
                    where aems.marks not in (-1, -2, -3) and aems.student_id in ($student_ids_str) and ae.entity_id=$entity_id and ae.assessment_id= $ass_id 
                    group by aems.assessments_entities_id";

                    $res= $this->db_readonly->query($query)->row();
        return $res;
    }

    private function _strength_in_section($sec_id) {
        //TODO: This code needs to be modified for electives.

        $strength= $this->db_readonly->select('count(*) as std_count')
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $sec_id)
            ->where_not_in('sy.promotion_status', array('4', '5', 'JOINED'))
            ->where('sy.acad_year_id', $this->yearId)
            ->where('sa.admission_status', '2')
            ->get()->row();

        $presented_stud= $strength->std_count;
        
        return $presented_stud;
    }

    public function get_class_name($cls_id) {
        return $this->db_readonly->select('c.class_name, ay.acad_year')
        ->from('class c')
        ->join('academic_year ay', 'ay.id= c.acad_year_id')
        ->where('c.id', $cls_id)->get()->row();
    }

     public function get_section_name($sec_id) {
        return $this->db_readonly->select('section_name')->where('id', $sec_id)->get('class_section')->row();
    }

     public function get_assessment_name($ass_id) {
        return $this->db_readonly->select('short_name, long_name')->where('id', $ass_id)->get('assessments')->row();
    }

    public function getSubjectMarks_for_subjectwise_analysis_in_elective($ass_id, $class_id, $section_id, $sub_id_arr, $grading_system_id) {

        // elective seperating with comma
        $elective_ids_str = implode(",", $sub_id_arr);
// '$ass_id' as assessment_id, '$class_id' as class_id, '$replaced' as student_ids_str
        //  Query for count Absent
        $sql = "select sum(case when marks=-1 then 1 else 0 end) as absentees, sum(case when marks=-2 then 1 else 0 end) as to_be_entered, sum(case when marks=-3 then 1 else 0 end) as not_applicable, aem.id as entity_id, aem.name, assent.total_marks, '$ass_id' as assessment_id, '$class_id' as class_id 
                from assessments_entities_marks_students aems 
                join assessments_entities assent on aems.assessments_entities_id=assent.id 
                join assessment_entity_master aem on aem.id=assent.entity_id 
                join student_admission sa on sa.id= aems.student_id
                join student_year sy on sa.id= sy.student_admission_id
                where assent.id in (select id from assessments_entities where assessment_id in ($ass_id) and entity_id in ($elective_ids_str)) and sy.class_section_id= $section_id and 
                student_id in (select student_admission_id from student_year where class_id=$class_id) and aem.ass_type='Internal'
                group by assent.entity_id";

        // Creating Absenty Array
        $sub_id_obj_arr = $this->db_readonly->query($sql)->result();

        // Elective main id for count strength
        $result1 = $this->db_readonly->select('aeg.id')
            ->from('assessment_elective_group aeg')
            ->where('id in (select elective_group_id from assessment_entities_group where id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$ass_id.'))))')
            ->get()->result();

        $main_id= $result1[0]->id;

        // Get Dynamic ranges by grading system
        $grades= $this->db_readonly->select('grades')->where('id', $grading_system_id)->get('assessment_grading_system')->result();
        $grades_arr='';
        if(!empty($grades)) {
            $grades_arr= json_decode($grades[0]->grades);
        }
       
        foreach ($sub_id_obj_arr as $key => $val) {
            // getting student ids
            $student_ids_str = $this->_get_student_ids_elective($section_id, $val->entity_id, $ass_id, $main_id);
            $replaced = str_replace(',', '_', $student_ids_str);
            $val->student_ids_str= "$replaced";
            $strength_count= count(explode(',', $student_ids_str));
            
            if($strength_count > 0) {
                
                $val->strength= $strength_count;
                $val->absenty = $val->absentees;
                $val->appeared = $val->strength - $val->absenty - $val->to_be_entered - $val->not_applicable;
                
                // echo '<pre>'; print_r($grading_system_id); die();
                $marks= $this->_obtained_marks_analysis($val->entity_id, $val->total_marks, $student_ids_str, $grading_system_id, $ass_id);

                
                if(!empty($marks)) {
                    $val->hundred_percent = $marks->hund_count;
                } else {
                    $val->hundred_percent= 0;
                }

                if(!empty($grades_arr)) {
                    foreach($grades_arr as $gkey => $gval) {
                            $from= $gval->from;
                            $to= $gval->to;
                            $as_alias= "between_$from"."_and_".$to;
                        if(!empty($marks)) {
                            $val->$as_alias = $marks->$as_alias;
                        }  else {
                            $val->$as_alias= 0;
                        }
                    }
                }

                if(!empty($grades_arr)) {
                    $val->average = $marks->subject_average;
                    $val->highest = $marks->highest;
                    $val->lowest = $marks->lowest;
                } else {
                    $val->average = 0;
                    $val->highest = 0;
                    $val->lowest = 0;
                }
                $val->total = $val->total_marks; 

            }
            else{
                $val->available_student = 'Subject Not Choosen By Student';
            }
        }
        return $sub_id_obj_arr;

    }

    private function _get_student_ids_elective($section_id, $sub_id, $ass_id, $main_id) {
        $stud_ids= $this->db_readonly->select('sa.id as student_id')
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $section_id)
            ->where('sa.admission_status', '2')
            ->where('sy.acad_year_id', $this->yearId)
            ->where_not_in('sy.promotion_status', array('4', '5', 'JOINED'))
            ->join('assessments_entities_marks_students aems', 'aems.student_id= sa.id')
            ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id and ae.entity_id = '.$sub_id.' and ae.assessment_id= '.$ass_id)
            ->get()->result();

            $stud_ids_str = '';
        $i = 0;
        $count = count($stud_ids);
        foreach ($stud_ids as $std_id) {
            $stud_ids_str .= $std_id->student_id;
            if ($i ++ < $count - 1) {
                $stud_ids_str .= ',';
            }
        }
        return $stud_ids_str;


    }

    public function getSubjectsByAssessment_analysis($assIds, $subjectType, $classId){
        if(empty($assIds)) return array();
        if ($subjectType == 'component') {

            $result= $this->db_readonly->select('aem.id as id, aem.name as subName, ae.total_marks')
                ->from('assessments_entities ae')
                ->join('assessment_entity_master aem', 'ae.entity_id= aem.id', 'left')
                ->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid and aeg.is_elective= 0')
                ->where('aem.id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))')
                ->where('ae.assessment_id', $assIds)
                ->group_by('aem.id')
                ->get()->result();

            

        } else if($subjectType == 'elective'){

            $result = $this->db_readonly->select('aem.id as id, aem.name as subName, ae.total_marks')
                ->from('assessment_entity_master aem')
                ->join('assessments_entities ae', 'ae.entity_id= aem.id')
                ->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid and aeg.is_elective= 1 and aeg.elective_group_id in (select id from assessment_elective_group where class_id= '.$classId.')')
                ->where('ae.assessment_id', $assIds)
                ->get()->result();

        }
        return $result;
    }

    public function getGradingSystems(){
        return $this->db_readonly->select('*')->get('assessment_grading_system')->result();
    }

    public function get_grades($grading_system) {
        return $this->db_readonly->select('grades')->where('id', $grading_system)->get('assessment_grading_system')->result();
    }

    public function get_student_details_range_wise($inputs_arr) {
        $subject_total_marks= $inputs_arr['subject_total_marks'];
        $entity_id= $inputs_arr['entity_id'];
        $assessment_id= $inputs_arr['assessment_id'];
        $class_id= $inputs_arr['class_id'];
        $std_ids_str= str_replace('_', ',', $inputs_arr['student_ids_str']);
        // $student_ids_arr= explode('_', $inputs_arr['student_ids_str']);
        $from= $inputs_arr['from'];
        $to= $inputs_arr['to'];
        // $perfect_count= $inputs_arr['perfect_count'];

        $query= "select concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as std_name, aems.marks, '$subject_total_marks' as subject_total_marks, round(((aems.marks / $subject_total_marks) * 100), 2) as percentage 
                from assessments_entities_marks_students aems 
                left join assessments_entities ae on ae.id= aems.assessments_entities_id 
                join student_admission sa on sa.id= aems.student_id 
                where ae.assessment_id= $assessment_id 
                and ae.entity_id= $entity_id 
                and aems.student_id in ($std_ids_str) 
                and (aems.marks*100)/$subject_total_marks >= $from 
                and (aems.marks*100)/$subject_total_marks <= $to 
                ";

        $res= $this->db_readonly->query($query)->result();

        // echo '<pre>'; print_r($this->db_readonly->last_query($res)); die();

        if(!empty($res)) {
            return $res;
        }
        return array();

    }

    public function get_std_count_range_wise($assessment_id, $class_id, $section_id, $component_ids_arr, $elective_ids_arr, $grades_arr) {
        $student_ids_str = $this->_get_student_ids_str($section_id);

        $entities_arr= [];
        $grand_total_marks= 0;
        if(!empty($elective_ids_arr)) {
            $elective_ids_str= implode(',', $elective_ids_arr);
            $sql = "select aem.id as entity_id, aem.name, assent.total_marks
                    from assessments_entities_marks_students aems 
                    join assessments_entities assent on aems.assessments_entities_id=assent.id 
                    join assessment_entity_master aem on aem.id=assent.entity_id 
                    where assent.id in (select id from assessments_entities where assessment_id in ($assessment_id) and entity_id in ($elective_ids_str)) 
                    and aem.ass_type='Internal'
                    group by assent.entity_id";
            $electives = $this->db_readonly->query($sql)->result();
            foreach($electives as $ekey => $eval) {
                if($ekey == 0) {
                    $grand_total_marks += $eval->total_marks;
                }
                array_push($entities_arr, $eval->entity_id);
            }
        }
        if(!empty($component_ids_arr)) {
            $components= $this->db_readonly->select("aem.id as entity_id, aem.name, assent.total_marks")
                ->from('assessments_entities assent')
                ->join('assessment_entity_master aem', 'aem.id=assent.entity_id')
                ->where('assent.assessment_id', $assessment_id)
                ->where_in('assent.entity_id', $component_ids_arr)
                ->get()->result();
        foreach($components as $ckey => $cval) {
            $grand_total_marks += $cval->total_marks;
            array_push($entities_arr, $cval->entity_id);
        }
        }

        $studet_arr= explode(',', $student_ids_str);

        $total_obtained_marks= [];
        $total_obtained_percentage= [];

        $second_student_ids_arr= [];

        foreach($studet_arr as $key => $val) {
            $count_for_cmponent= $this->db_readonly->select("sum(aems.marks) as total_obtained, aems.student_id, round((sum(aems.marks) / $grand_total_marks) * 100, 2) as percentage")
            ->from('assessments_entities_marks_students aems')
            ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
            ->where('ae.assessment_id', $assessment_id)
            // ->where('aems.acad_year_id', $this->yearId)
            ->where_in('ae.entity_id', $entities_arr)
            ->where('aems.student_id', $val)
            ->where_not_in('aems.marks', array('-1', '-2', '-3'))
            ->get()->row();

            array_push($second_student_ids_arr, $count_for_cmponent->student_id);
            array_push($total_obtained_percentage, $count_for_cmponent->percentage);
        }

        $actual_count_obj= new stdClass();
        $student_ids_obj= new stdClass();

        array_unshift($grades_arr, (Object)array('grade' => 'A+', 'long_name' => 'Appreciated', 'from' => '100', 'to' => '100', 'range' => '', 'grade_point' => ''));
        // echo '<pre>'; print_r($grades_arr); die();
        
        // $temp_count2= 0;
        foreach($grades_arr as $gkey => $gval) {
            $from= $gval->from;
            $to= $gval->to;
            $as_alias= "between_$from"."_and_".$to;
            // $as_alias2= "bet_$from"."_".$to;
            $temp_count= 0;
            $second_student_ids_str= '';
            foreach($total_obtained_percentage as $pkey => $pval) {
                if($pval >= $from && $pval <= $to) {
                    $second_student_ids_str .= $second_student_ids_arr[$pkey]. '_';
                    $temp_count ++;
                }
                // if($pval == 100) {
                //     $temp_count2 ++;
                // }
            }
            $actual_count_obj->$as_alias= $temp_count;
            $student_ids_obj->$as_alias= $second_student_ids_str;
        }
        // $actual_count_obj->hundred_percent= $temp_count2;


        return ['actual_count_obj' => $actual_count_obj, 'second_student_ids_str' => $student_ids_obj, 'grand_total_marks' => $grand_total_marks];

    }

    public function get_student_details_overall_subject_range_wise($inputs_arr) {
        $first_entities_ids_arr= explode(' ', trim(str_replace('_', ' ', $inputs_arr['first_entities_ids_arr'])));
        $first_student_ids_arr= explode(' ', trim(str_replace('_', ' ', $inputs_arr['first_student_ids_str'])));
        $first_ass_id= $inputs_arr['first_ass_id'];
        $first_class_id= $inputs_arr['first_class_id'];
        $range_str= $inputs_arr['range_str'];
        $grand_total_marks= $inputs_arr['grand_total_marks'];

        $std_details= $this->db_readonly->select("sum(aems.marks) as total_obtained, aems.student_id, round((sum(aems.marks) / $grand_total_marks) * 100, 2) as percentage, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as std_name, '$grand_total_marks' as grand_total_marks")
            ->from('assessments_entities_marks_students aems')
            ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
            ->join('student_admission sa', 'sa.id= aems.student_id')
            ->where('ae.assessment_id', $first_ass_id)
            // ->where('aems.acad_year_id', $this->yearId)
            ->where_in('ae.entity_id', $first_entities_ids_arr)
            ->where_in('aems.student_id', $first_student_ids_arr)
            ->where_not_in('aems.marks', array('-1', '-2', '-3'))
            ->group_by('aems.student_id')
            ->get()->result();

        return $std_details;
        // echo '<pre>'; print_r($count_for_cmponent); die(); 

    }

    public function get_derived_fields_class_wise($input) {
        $res= $this->db_readonly->select("name, id, fields_operation")
        ->where('class_id', $input['class_id'])
        ->where('acad_year_id', $this->yearId)
        ->order_by('sorting_order', 'asc')
        ->get('assessment_computed_field_master')->result();
        if(!empty($res)){
            return $res;
        }
        return array();
    }

    public function get_derived_fields_result($derived_fields, $sectionId) {
        $student_ids= $this->db_readonly->select("student_admission_id as std_id")
            ->where_in('class_section_id', $sectionId)
            ->where_not_in('promotion_status', ['4', '5', 'JOINED'])
            // ->where('class_section_id', $sectionId)
            ->where('acad_year_id', $this->yearId)
            ->get('student_year')->result();
        $result_obj= new stdClass();
        foreach($student_ids as $std_key => $std_val) {
            $student_id= $std_val->std_id;
            $student_obj= new stdClass();
            foreach($derived_fields as $derived_key => $derived_val) {
                $derived_id= explode('___', $derived_val);
                $derived_obj= new stdClass();
                $markas= $this->db_readonly->select("result, grand_total_marks")->where('ass_computed_field_master_id', $derived_id[0])->where('student_id', $std_val->std_id)->get('assessment_computed_field_details')->result();
                if(!empty($markas)) {
                    $student_obj->$derived_val= $markas[0]->result. '___' .$markas[0]->grand_total_marks;
                    // array_push($student_arr, ["$derived_val" => $markas[0]->result]);
                }
            }
            $result_obj->$student_id=$student_obj;
        }
        return $result_obj;
    }

    public function save_selected_filters($inputs_arr) {
        // echo '<pre>'; print_r($inputs_arr); die();
        $filter_name= $inputs_arr['filter_name'];
        $classId= $inputs_arr['classId'];
        $saved_filter_id= $inputs_arr['saved_filter_id'];
        unset($inputs_arr['filter_name']);
        unset($inputs_arr['saved_filter_id']);
        $data_arr= array(
            'saved_report_name' => $filter_name,
            'acad_year_id' => $this->yearId,
            'filters_selected' => json_encode($inputs_arr),
            'master_report_name' => 'exam_ass_v2_report',
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s')
        );
        if($saved_filter_id && $saved_filter_id != '0' && $saved_filter_id != 0 && $saved_filter_id != null) {
            $update_filter= array(
                'filters_selected' => json_encode($inputs_arr)
            );
            return $this->db->where('id', $saved_filter_id)->update('predefined_reports', $update_filter);
        } else {
            return $this->db->insert('predefined_reports', $data_arr);
        }
        // echo '<pre>'; print_r($inputs_arr); die();
    }

    public function get_all_saved_filters() {
        $res= $this->db_readonly->select("id, saved_report_name as name")
        ->where('master_report_name', 'exam_ass_v2_report')
        ->where('acad_year_id', $this->yearId)
        ->get('predefined_reports')->result();
        if(!empty($res)) {
            return $res;
        }
        return [];
    }

    public function onchange_saved_filter() {
        $id= $this->input->post('saved_filter');
        $res= $this->db_readonly->select("filters_selected as filter_json")->where('id', $id)->get('predefined_reports')->row();
        if(!empty($res)) {
            return json_decode($res->filter_json);
        }
        return [];
    }

    public function get_subjects_class_and_assessment_wise() {
        $class_id= $this->input->post('class_id');
        $assessment_id= $this->input->post('assessment_id');
        // No--electives include
        $components= $this->db_readonly->select("aem.id,ae.entity_id,aem.name")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid')
            ->where('ae.assessment_id', $assessment_id)
            ->where('aeg.is_elective', 0)
            ->group_by('aem.id')
            ->get()->result();

        // No--electives include
        $groups= $this->db_readonly->select("aeg.id as aeg_id, aeg.entity_name as name, group_concat(aem.id) as entities_ids_str")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
            ->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid')
            ->where('ae.assessment_id', $assessment_id)
            ->where('aeg.is_elective !=', 1)
            ->group_by('aeg.id')
            ->get()->result();
        // Only electives
        $electives= $this->db_readonly->select("eleg.id as eleg_id, eleg.group_name as name, group_concat(aem.id) as entities_ids_str")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
            ->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid')
            ->join('assessment_elective_group eleg', 'eleg.id= aeg.elective_group_id')
            ->where('ae.assessment_id', $assessment_id)
            ->where('aeg.is_elective', 1)
            ->group_by('eleg.id')
            ->get()->result();

        return ['components' => $components, 'groups' => $groups, 'electives' => $electives];
    }

    public function delete_selected_filters() {
        $saved_filter_id= $this->input->post('saved_filter_id');
        return $this->db->where('id', $saved_filter_id)->delete('predefined_reports');
    }

    public function getClsAssessments_new1($classId, $assType){
        $cond= " and generation_type = 'Manual' ";
        if($assType == 1) {
            $cond= '';
        }
        $sql = "select id, long_name, short_name, sorting_order from assessments where class_id=$classId";
        // if($assType != 'both') { //get both manual and auto
        //     $sql .= " and generation_type='".$assType."'";
        // }
        $sql .= $cond. ' order by sorting_order';

        $result = $this->db_readonly->query($sql)->result();
        // echo '<pre>';print_r($result); die();
        return $result;
    }

    public function get_academic_years() {
        return $this->db_readonly->select("id, acad_year")->where('id <= ', $this->yearId)->order_by('id', 'desc')->get('academic_year')->result();
    }

    public function get_grading_systems() {
        return $this->db_readonly->select("id, name")->get('assessment_grading_system')->result();
    }

    public function get_classes_acad_year_wise($input) {
        $cls= $this->db_readonly->select("id, class_name")->where('acad_year_id', $input['year_selector'])->get('class')->result();
        if(!empty($cls)) {
            return $cls;
        }
        return [];
    }

    public function get_assessments_class_wise($input) {
        $ass= $this->db_readonly->select("id, short_name")->where('class_id', $input['class_selector'])->get('assessments')->result();
        if(!empty($ass)) {
            return $ass;
        }
        return [];
    }

    public function get_yearly_graph_data($year_id, $class_id, $assessment_id, $pass_percentage, $grading_systems) {
        $subjects= $this->__get_subjects($assessment_id); // normal and electives
        $students= $this->__get_class_students($class_id);
        $subs_arr= [];
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                $subs_arr[]= $val->id;
            }
        }

        $student_marks= $this->__get_student_marks($students, $subjects, $assessment_id);
        $result_object= new stdClass();

        // Appeared %
        $appeared_count= 0;
        foreach($student_marks as $mkey => $mval) {
            if($mval->percentage > 0 && $mval->marks > 0) {
                $appeared_count ++;
            }
        }
        $graph_perc= ($appeared_count / count($students)) *100;
        $tot= "($appeared_count / ". count($students). ")";
        $result_object->Appeared= round($graph_perc, 2). "___" .$tot;

        // Passed %
        $passed_count= 0;
        foreach($student_marks as $mkey => $mval) {
            if($mval->percentage >= $pass_percentage) {
                $passed_count ++;
            }
        }
        $graph_perc= ($passed_count / count($students)) *100;
        $tot= "($passed_count / ". count($students). ")";
        $result_object->Passed= round($graph_perc, 2). "___" .$tot;

         // Pass %
         $graph_perc= ($passed_count / $appeared_count) *100;
         $obj_k= "Result_(Pass%)";
         $tot= "($passed_count / $appeared_count)";
         $result_object->$obj_k= round($graph_perc, 2). "___" .$tot;

         // Grading %
        if(!empty($grading_systems)) {
            foreach($grading_systems as $gkey => $gval) {
                $count= 0;
                foreach($student_marks as $mkey => $mval) {
                    if($mval->percentage >= $gval->from && $mval->percentage <= $gval->to) {
                        $count ++;
                    }
                }
                $graph_perc= ($count / count($students)) *100;
                $obj_key= $gval->long_name;
                $tot= "($count / ". count($students). ")";
                $result_object->$obj_key= round($graph_perc, 2). "___" .$tot;
            }
        }

        // Average %
        $average_count= 0;
        $total_marks_obt= 0;
        foreach($student_marks as $mkey => $mval) {
            if($mval->percentage > 0 && $mval->marks > 0) {
                $average_count ++;
                $total_marks_obt += $mval->percentage;
            }
        }
        $graph_perc= ($total_marks_obt / $average_count);
        $obj_key= "Avg_%_";
        $tot= "(" .round($total_marks_obt, 2). " / " .count($students). ")";
        $result_object->$obj_key= round($graph_perc, 2). "___" .$tot;

        return $result_object;
    }

    private function __get_subjects($ass_id) {
        return $this->db_readonly->select("aem.id, aem.ass_entity_gid, aeg.is_elective, ae.id as assessments_entities_id, aeg.elective_group_id")
                ->from('assessments_entities ae')
                ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
                ->join('assessment_entities_group aeg', 'aeg.id= aem.ass_entity_gid')
                ->where('ae.assessment_id', $ass_id)
                ->where("aem.derived_formula is null")
                ->where("aem.evaluation_type", 'marks')
                ->get()->result();
    }

    private function __get_class_students($class_id) {
        $obj= $this->db_readonly->select("student_admission_id")
                ->where('class_id', $class_id)
                ->where_not_in('promotion_status', ['4', '5', 'JOINED'])
                ->get('student_year')->result();
        $stds= [];
        if(!empty($obj)) {
            foreach($obj as $key => $val) {
                $stds[]= $val->student_admission_id;
            }
        }
        return $stds;
    }

    public function __get_grading_systems_by_id($grd_id) {
        $res= $this->db_readonly->select('grades')->where('id', $grd_id)->get('assessment_grading_system')->result();
        if(!empty($res)) {
            $res= json_decode($res[0]->grades);
            foreach($res as $key => $val) {
                if($key == 0) {
                    $val->long_name= "$val->from%_Above";
                } else if(count($res) - 1 == $key){
                    $val->long_name= "Less_Than_$val->to%";
                } else {
                    $val->long_name= "$val->from%_To_Below_$val->to%";
                }
            }
        }
        
        return $res;
    }

    private function __get_student_marks($students, $subjects, $assessment_id) {

        if(!empty($subjects)) {
            $subs= [];
            foreach($subjects as $k => $v) {
                $subs[]= $v->id;
            }
            $res= $this->db_readonly->select("sum( ae.total_marks ) as total, sum( case when(aems.marks != -1 && aems.marks != -3 && aems.marks != -2) then aems.marks else 0 end ) as marks, ( (sum( case when(aems.marks != -1 && aems.marks != -3 && aems.marks != -2) then aems.marks else 0 end )) / (sum( ae.total_marks )) ) * 100 as percentage")
                    ->from('assessments_entities ae')
                    ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id= ae.id')
                    ->where_in('aems.student_id', $students)
                    ->where_in('ae.entity_id', $subs)
                    ->where('ae.assessment_id', $assessment_id)
                    ->group_by('aems.student_id')
                    ->get()->result();
                
            return $res;
        }
        return [['total' => 0, 'marks'=> 0]];
    }

    function getDataForAssessmentWiseSubjectAnalysis() {
        $input= $this->input->post();

        // echo '<pre>'; print_r($input); die();

        $class_id= $input['class_id'];
        $assessment_id= isset($input['assessment_id']) ? $input['assessment_id'] : [];
        $groups= isset($input['groups']) ? $input['groups'] : [];
        $components= isset($input['components']) ? $input['components'] : [];
        $electives= isset($input['electives']) ? $input['electives'] : [];

        $sectionsAndStudents= $this->db_readonly->select("cs.class_id, cs.id as class_section_id, count(sy.student_admission_id) as studentCount, cs.section_name, group_concat(sy.student_admission_id) as studentIds")
            ->from('student_year sy')
            ->join('class_section cs', 'cs.id = sy.class_section_id', 'left')
            ->where('sy.class_id', $class_id)
            ->where_not_in('sy.promotion_status', ['4', '5', 'JOINED'])
            ->group_by('cs.id')
            ->get()->result();

        $assessments= $this->db_readonly->select("id, long_name")->where_in('id', $assessment_id)->get('assessments')->result();
        $finalArr= [];

        
        if(!empty($assessments)) {
            $assesstIdsArr= [];
            $ass= ['name'];
            foreach($assessments as $assKey => $assVal) {
                $ass[]= $assVal->long_name;
                $assesstIdsArr[]= $assVal->id;
            }
            // $finalArr[]= $ass;
        }

        

            

        if(!empty($sectionsAndStudents)) {
            foreach($sectionsAndStudents as $secKey => $secVal) {
                $finalArr= []; // foreach new section Array need to be reset
                $finalArr[]= $ass; // foreach new section Array need to be added by new assessment_names
                // Groups
                if(!empty($groups)) {

                    
                    foreach($groups as $grpKey => $grpVal) { //  sum(if(aems.marks > 0, aems.marks, 0)) as sumMarks, sum(ae.total_marks) as sumTotal, ae.total_marks,
                        $assessmentWiseSubjectsAvg= $this->db_readonly->select("ae.assessment_id, aem.id as entity_id, aeg.id, concat('G-', aeg.entity_name) as name, count(aems.student_id) as stdCount, sum( if(aems.marks > 0, aems.marks, 0)*100 / ae.total_marks ) / $secVal->studentCount as avgPercentage") // I am calculating like this because if subject is group then multiple component can be there and diff - diff total_marks also can be there.
                            ->from('assessment_entities_group aeg')
                            ->join('assessment_entity_master aem', 'aem.ass_entity_gid = aeg.id')
                            ->join('assessments_entities ae', 'ae.entity_id = aem.id')
                            ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                            ->where_in('ae.assessment_id', $assessment_id)
                            ->where("aems.student_id in ($secVal->studentIds)")
                            ->where('aeg.id', $grpVal)
                            ->group_by('aem.id, ae.assessment_id')
                            ->get()->result();

                            // echo '<pre>'; print_r($assessmentWiseSubjectsAvg); die();

                        if(!empty($assessmentWiseSubjectsAvg)) {

                            // Converting it into assessment wise
                            $groupedArray = [];
                            foreach ($assessmentWiseSubjectsAvg as $item) {
                                $assessmentId = $item->assessment_id;
                                if (!isset($groupedArray[$assessmentId])) {
                                    $groupedArray[$assessmentId] = (object) [
                                        'assessment_id' => $assessmentId,
                                        'name' => $item->name, // Include name
                                        'stdCount' => $item->stdCount, // Include stdCount
                                        'totalPercentage' => 0,
                                        'count' => 0,
                                        'avgPercentage' => 0
                                    ];
                                }
                                $groupedArray[$assessmentId]->totalPercentage += $item->avgPercentage;
                                $groupedArray[$assessmentId]->count++;
                            }
                            foreach ($groupedArray as &$group) {
                                $group->avgPercentage = $group->totalPercentage / $group->count;
                                unset($group->totalPercentage, $group->count); // Remove temporary fields
                            }
                            $resultArray = array_values($groupedArray);

////////
                            $averages= [];
                            foreach($resultArray as $key => $val) {
                                $averages[$val->assessment_id]= $val;
                            }

                            foreach($assesstIdsArr as $asskee => $assVaal) {
                                // foreach($assessmentWiseSubjectsAvg as $key => $val) {
                                    if($asskee == '0') {
                                        $assMarks[]= $val->name;
                                        if(array_key_exists($assVaal, $averages)) {
                                            $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                        } else {
                                            $assMarks[]= 0;
                                        }
                                    } else {
                                        if(array_key_exists($assVaal, $averages)) {
                                            $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                        } else {
                                            $assMarks[]= 0;
                                        }
                                    }
                                // }
                            }
                            $finalArr[]= $assMarks;
                            $assMarks= [];
                        }
                    }
                    // echo '<pre>'; print_r($finalArr); die();
                }





                // Components
                if(!empty($components)) {
                    foreach($components as $cmpKey => $cmpVal) { //  sum(if(aems.marks > 0, aems.marks, 0)) as sumMarks, sum(ae.total_marks) as sumTotal, ae.total_marks,
                        $assessmentWiseSubjectsAvg= $this->db_readonly->select("ae.assessment_id, aem.id, concat('C-', aem.name) as name, count(aems.student_id) as stdCount, sum( if(aems.marks > 0, aems.marks, 0)*100 / ae.total_marks ) / $secVal->studentCount as avgPercentage") // I am calculating like this because if subject is group then multiple component can be there and diff - diff total_marks also can be there.
                            // ->from('assessment_entities_group aeg')
                            ->from('assessment_entity_master aem')
                            ->join('assessments_entities ae', 'ae.entity_id = aem.id')
                            ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                            ->where_in('ae.assessment_id', $assessment_id)
                            ->where("aems.student_id in ($secVal->studentIds)")
                            ->where('aem.id', $cmpVal)
                            ->group_by('ae.assessment_id')
                            ->get()->result();

                            // echo '<pre>'; print_r($this->db_readonly->last_query($assessmentWiseSubjectsAvg)); die();

                        if(!empty($assessmentWiseSubjectsAvg)) {
                            $averages= [];
                            foreach($assessmentWiseSubjectsAvg as $key => $val) {
                                $averages[$val->assessment_id]= $val;
                            }
                            
                            foreach($assesstIdsArr as $asskee => $assVaal) {
                                // foreach($assessmentWiseSubjectsAvg as $key => $val) {
                                    if($asskee == '0') {
                                        $assMarks[]= $val->name;
                                        if(array_key_exists($assVaal, $averages)) {
                                            $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                        } else {
                                            $assMarks[]= 0;
                                        }
                                    } else {
                                        if(array_key_exists($assVaal, $averages)) {
                                            $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                        } else {
                                            $assMarks[]= 0;
                                        }
                                    }
                                // }
                            }
                            $finalArr[]= $assMarks;
                            $assMarks= [];
                        }
                    }
                    // echo '<pre>'; print_r($finalArr); die();
                }






                // Electives
                if(!empty($electives)) {
                    foreach($electives as $eleKey => $eleVal) { //  sum(if(aems.marks > 0, aems.marks, 0)) as sumMarks, sum(ae.total_marks) as sumTotal, ae.total_marks,
                        $assessmentWiseSubjectsAvg= $this->db_readonly->select("ae.assessment_id, aeleG.id, concat('E-', aeleG.group_name) as name, count(aems.student_id) as stdCount, sum( if(aems.marks > 0, aems.marks, 0)*100 / ae.total_marks ) / $secVal->studentCount as avgPercentage") // I am calculating like this because if subject is group then multiple component can be there and diff - diff total_marks also can be there.
                            ->from('assessment_elective_group aeleG')
                            ->join('assessment_entities_group aeg', 'aeleG.id = aeg.elective_group_id')
                            ->join('assessment_entity_master aem', 'aem.ass_entity_gid = aeg.id')
                            ->join('assessments_entities ae', 'ae.entity_id = aem.id')
                            ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                            ->where_in('ae.assessment_id', $assessment_id)
                            ->where("aems.student_id in ($secVal->studentIds)")
                            ->where('aeleG.id', $eleVal)
                            ->group_by('aem.id, ae.assessment_id')
                            ->get()->result();

                            // echo '<pre>'; print_r($this->db_readonly->last_query($assessmentWiseSubjectsAvg)); die();

                            if(!empty($assessmentWiseSubjectsAvg)) {
/////////
                                // Converting it into assessment wise
                                $groupedArray = [];
                                foreach ($assessmentWiseSubjectsAvg as $item) {
                                    $assessmentId = $item->assessment_id;
                                    if (!isset($groupedArray[$assessmentId])) {
                                        $groupedArray[$assessmentId] = (object) [
                                            'assessment_id' => $assessmentId,
                                            'name' => $item->name, // Include name
                                            'stdCount' => $item->stdCount, // Include stdCount
                                            'totalPercentage' => 0,
                                            'count' => 0,
                                            'avgPercentage' => 0
                                        ];
                                    }
                                    $groupedArray[$assessmentId]->totalPercentage += $item->avgPercentage;
                                    $groupedArray[$assessmentId]->count++;
                                }
                                foreach ($groupedArray as &$group) {
                                    $group->avgPercentage = $group->totalPercentage / $group->count;
                                    unset($group->totalPercentage, $group->count); // Remove temporary fields
                                }
                                $resultArray = array_values($groupedArray);

        // echo '<pre>'; print_r($resultArray); die();

////////
                                $averages= [];
                                foreach($resultArray as $key => $val) {
                                    $averages[$val->assessment_id]= $val;
                                }
                                
                                foreach($assesstIdsArr as $asskee => $assVaal) {
                                    // foreach($assessmentWiseSubjectsAvg as $key => $val) {
                                        if($asskee == '0') {
                                            $assMarks[]= $val->name;
                                            if(array_key_exists($assVaal, $averages)) {
                                                $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                            } else {
                                                $assMarks[]= 0;
                                            }
                                        } else {
                                            if(array_key_exists($assVaal, $averages)) {
                                                $assMarks[]= round(1 * $averages[$assVaal]->avgPercentage, 2);
                                            } else {
                                                $assMarks[]= 0;
                                            }
                                        }
                                    // }
                                }
                                $finalArr[]= $assMarks;
                                $assMarks= [];
                            }
                    }
                    // echo '<pre>'; print_r($finalArr); die();
                }







                $formattedArray[$secVal->section_name]= $finalArr;
            }
        }

        // echo '<pre>'; print_r($formattedArray); die();
        return $formattedArray;
    }

    function getClassAndAcadYear($class_id) {
        return $this->db_readonly->select("c.class_name, ay.acad_year")
        ->from('class c')
        ->join('academic_year ay', 'ay.id = c.acad_year_id')
        ->where('c.id', $class_id)
        ->get()->row();
    }

    function get_classLists() {
        return $this->db_readonly->select("cs.id as section_id, cs.class_id, cs.section_name, c.class_name")
        ->from('class c')
        ->join('class_section cs', 'cs.class_id = c.id')
        ->where('c.acad_year_id', $this->yearId)
        ->order_by('c.display_order, cs.display_order')
        ->get()->result();
    }

    function getClassSectionWiseStudents() {
        $class_section_id= $this->input->post('class_section_id');
        return $this->db_readonly->select("sa.id, concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as name")
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id = sy.student_admission_id')
            ->where('sy.class_section_id', $class_section_id)
            ->where_not_in('sy.promotion_status', ['JOINED']) // Get alumini too
            ->where('sy.acad_year_id', $this->yearId)
            ->order_by('sa.first_name, sa.last_name')
            ->get()->result();
    }

    function getStudentWiseAcadYears() {
        $student_id= $this->input->post('student_id');
        return $this->db_readonly->select("ay.id, ay.acad_year as name")
            ->from('student_year sy')
            ->join('academic_year ay', 'ay.id = sy.acad_year_id')
            ->where('sy.student_admission_id', $student_id)
            ->order_by('ay.id')
            ->get()->result();
    }

    function get_subjectMasterLists() {
        return $this->db_readonly->where('status', 1)->order_by('order, subject_name')->get('subject_master')->result();
    }

    function getAssessmentsByStudentsAcadYearAndStudentId() {
        $students_acad_year= $this->input->post('students_acad_year');
        $student_id= $this->input->post('student_id');
        $generation_type= $this->input->post('generation_type');

        $generation_type= explode(',', $generation_type);

        // echo '<pre>'; print_r($student_id);
        // echo '<pre>'; print_r($students_acad_year); die();

        $acadYearClass= $this->db_readonly->select('class_id')->where('acad_year_id', $students_acad_year)->where('student_admission_id', $student_id)->get('student_year')->row();
        if(empty($acadYearClass)) {
            return [];
        }
        $class_id= $acadYearClass->class_id;

        // echo '<pre>'; print_r($class_id); die();


        return $this->db_readonly->select("id, long_name as name")->where('acad_year_id', $students_acad_year)->where('class_id', $class_id)->where_in("generation_type", $generation_type)->get('assessments')->result();
    }

    function getDataForStudentWiseMultiYearAnalysis() {
    /*    
        Steps to follow
            -- Step 1:
                - Finding subject master ids
                - Finding acadeic year wise - assessments
            -- Step 2:
                - Finding academic year wise and subject master wise - entity ids arr (For Subject groups and subject electives different - different)
            -- Step 3:
                - Finding assessment wise - marks percentage
                - Calculating average marks just dividing by assessment count (And this is subject wise and academic year wise) (For both electives and groups)
            -- Step 4:
                - Formating the data as follows:
                {
                    [name, 2020-21, 2021022]
                    [G-Math, 56,73]
                    [G-Science, 89, 74]
                    [E-2nd Lng, 47, 68]
                }
    */
	

        
        $input= $this->input->post();

        $class_section_id= $input['class_section_id'];
        $acad_year_assessments= isset($input['acad_year_assessments']) ? $input['acad_year_assessments'] : [];
        $subject_master_id= isset($input['subject_master_id']) ? $input['subject_master_id'] : [];
        $student_id= isset($input['student_id']) ? $input['student_id'] : 0;

        if(empty($acad_year_assessments) || empty($subject_master_id)) {
            return [];
        }

        $assWiseAcadYear= [];
        $acadYearWiseAssessments= [];
        foreach($acad_year_assessments as $acadKey => $acadVal) {
            $acadId___acadName= explode('___', $acadKey);
            $acadYearIds[]= $acadId___acadName[0];
            $acadYearNames[$acadId___acadName[0]]= $acadId___acadName[1];
            $assessments_ids_arr= [];
            foreach($acadVal as $assKey => $assVal) {
                $assId___assName= explode('___', $assVal);
                $assIds[]= $assId___assName[0]; // ass ids
                $assessments_ids_arr[]= $assId___assName[0]; // ass ids
                $assNames[]= $assId___assName[1];
                $assWiseAcadYear[$assId___assName[0]]= $acadId___acadName[1]; // Storing ass wise acad year
            }
            $acadYearWiseAssessments[$acadId___acadName[0]]= $assessments_ids_arr;
        }

        $subjectMasterIds= [];
        $subjectMasterNames= [];
        foreach($subject_master_id as $subKey => $subVal) {
            $subMasterId___subMasterName= explode('---', $subVal);
            $subjectMasterIds[]= $subMasterId___subMasterName[0];
            $subjectMasterNames[$subMasterId___subMasterName[0]]= $subMasterId___subMasterName[1];
        }

        $currentClass= $this->db_readonly->select("class_id, class_name, section_name")->where('id', $class_section_id)->get('class_section')->row();
        if(empty($currentClass)) {
            return [];
        }
        $studentDetails= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as student, concat(p.first_name, ' ', ifnull(p.last_name, '')) as parent")
            ->from('student_admission sa')
            ->join('student_relation sr', 'sr.std_id = sa.id')
            ->join('parent p', 'p.id = sr.relation_id')
            ->where('sa.id', $student_id)
            ->where('p.student_id', $student_id)
            ->where('sr.relation_type', 'Father')
            ->get()->row();

        $FinalData= $this->__getFinalData($subjectMasterIds, $assIds, $student_id, $acadYearIds, $acadYearWiseAssessments);
        $groups= $FinalData['Groups'];
        $electives= $FinalData['Electives'];

        // Format initiation
        $formattedArray= [];
        if(!empty($groups) || !empty($groups)) {
            $arr= ['name'];
            foreach($acadYearNames as $acKey => $acVal) {
                $arr[]= $acVal;
            }
            $formattedArray[]= $arr;
        }

        // Group formation
        if(!empty($groups)) {
            foreach($groups as $gKey => $gVal) {
                $sub= $subjectMasterNames[$gKey];
                $arr= ['G-'.$sub];
                foreach($acadYearNames as $acKey => $acVal) {
                    if(array_key_exists($acKey, $gVal)) {
                        $arr[]= $gVal[$acKey];
                    } else {
                        $arr[]= 0;
                    }
                }
                $formattedArray[]= $arr;
            }
        }

        // Elective formation
        if(!empty($electives)) {
            foreach($electives as $gKey => $gVal) {
                $sub= $subjectMasterNames[$gKey];
                $arr= ['E-'.$sub];
                foreach($acadYearNames as $acKey => $acVal) {
                    if(array_key_exists($acKey, $gVal)) {
                        $arr[]= $gVal[$acKey];
                    } else {
                        $arr[]= 0;
                    }
                }
                $formattedArray[]= $arr;
            }
        }


        return ['studentDetails' => $studentDetails, 'currentClass' => $currentClass, 'formattedArray' => $formattedArray, 'acadYearNames' => $acadYearNames];
    }

    private function __getFinalData($subjectMasterIds, $assIds, $student_id, $acadYearIds, $acadYearWiseAssessments) {
        // echo '<pre>';print_r($assIds); die();
        foreach($acadYearIds as $acadKey => $acadVal) {

            $acadWiseClassId= $this->db_readonly->select("class_id")->where('student_admission_id', $student_id)->where('acad_year_id', $acadVal)->get('student_year')->row();
            $classId_acadWise= 0;
            if(!empty($acadWiseClassId)) {
                $classId_acadWise= $acadWiseClassId->class_id;
            }
            // // Groups
            $groups_entities[$acadVal]= $this->db_readonly->select("aeg.subject_master_id, group_concat(aem.id) as entity_ids_str, '$acadVal' as acadYear")
            ->from('assessment_entities_group aeg')
            ->join('assessment_entity_master aem', 'aem.ass_entity_gid = aeg.id')
            ->where('aeg.is_elective', 0)
            ->where('aeg.class_id', $classId_acadWise)
            ->where_in('aeg.subject_master_id', $subjectMasterIds)
            ->group_by('aeg.subject_master_id')
            ->get()->result();

            // echo '<pre>';print_r($this->db_readonly->last_query($groups_entities));

            // // Electives
            $elective_entities[$acadVal]= $this->db_readonly->select("aEleG.subject_master_id, group_concat(aem.id) as entity_ids_str, '$acadVal' as acadYear")
            ->from('assessment_elective_group aEleG')
            ->join('assessment_entities_group aeg', 'aeg.elective_group_id = aEleG.id')
            ->join('assessment_entity_master aem', 'aem.ass_entity_gid = aeg.id')
            ->where('aeg.is_elective', 1)
            ->where('aEleG.class_id', $classId_acadWise)
            ->where_in('aEleG.subject_master_id', $subjectMasterIds)
            ->group_by('aEleG.subject_master_id')
            ->get()->result();
        }

        // die();

        // Groups Marks
        $avgMarks_subWise_acadYearWise_forSubjectGroups= [];
        if(!empty($groups_entities)) {
            foreach($groups_entities as $acadYear => $grpVal) {
                if(!empty($grpVal)) {
                    foreach($grpVal as $entKey => $entVal) {
                        $assessmentWiseMarks= $this->db_readonly->select("ae.assessment_id, (sum(aems.marks)*100 / sum(ae.total_marks)) ass_wise_marks_percent")
                                ->from('assessments_entities ae')
                                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                                ->where('aems.student_id', $student_id)
                                ->where_in('ae.entity_id', explode(',', $entVal->entity_ids_str))
                                ->where_in('ae.assessment_id', $acadYearWiseAssessments[$acadYear]) // acad year wise assessments
                                ->group_by('ae.assessment_id')
                                ->get()->result();
                        if(!empty($assessmentWiseMarks)) {
                            $sumMarks= 0;
                            foreach($assessmentWiseMarks as $markKey => $markVal) {
                                $sumMarks += $markVal->ass_wise_marks_percent;
                            }
                            $averageMarks= $sumMarks / count($assessmentWiseMarks);
                            $avgMarks_subWise_acadYearWise_forSubjectGroups[$entVal->subject_master_id][$acadYear]= $averageMarks;
                        }

                                // echo '<pre>';print_r($assessmentWiseMarks); die();
                    }
                }
            }
        }


        // Electives Marks
        $avgMarks_subWise_acadYearWise_forSubjectElectives= [];
        if(!empty($elective_entities)) {
            foreach($elective_entities as $acadYear => $grpVal) {
                if(!empty($grpVal)) {
                    foreach($grpVal as $entKey => $entVal) {
                        $assessmentWiseMarks= $this->db_readonly->select("ae.assessment_id, (sum(aems.marks)*100 / sum(ae.total_marks)) ass_wise_marks_percent")
                                ->from('assessments_entities ae')
                                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                                ->where('aems.student_id', $student_id)
                                ->where_in('ae.entity_id', explode(',', $entVal->entity_ids_str))
                                ->where_in('ae.assessment_id', $acadYearWiseAssessments[$acadYear]) // acad year wise assessments
                                ->group_by('ae.assessment_id')
                                ->get()->result();
                        if(!empty($assessmentWiseMarks)) {
                            $sumMarks= 0;
                            foreach($assessmentWiseMarks as $markKey => $markVal) {
                                $sumMarks += $markVal->ass_wise_marks_percent;
                            }
                            $averageMarks= $sumMarks / count($assessmentWiseMarks);
                            $avgMarks_subWise_acadYearWise_forSubjectElectives[$entVal->subject_master_id][$acadYear]= $averageMarks;
                        }

                                // echo '<pre>';print_r($assessmentWiseMarks); die();
                    }
                }
            }
        }


        return ['Groups' => $avgMarks_subWise_acadYearWise_forSubjectGroups, 'Electives' => $avgMarks_subWise_acadYearWise_forSubjectElectives];
    }

    function getAcadYears() {
        return $this->db_readonly->order_by('acad_year', 'desc')->get('academic_year')->result();
    }

    function getClasses() {
        $acadYear= $this->input->post('acad_year');
        return $this->db_readonly->select("id, class_name")->where('acad_year_id', $acadYear)->order_by('display_order, class_name')->get('class')->result();
    }

    function getMappedSubjectsClassWise() {
        $class_id= $this->input->post('class_id');
        $groups= $this->db_readonly->select("aeg.id, aeg.entity_name as name, 0 as is_elective, ifnull(sm.subject_name, '-') as subject_name")
                ->from('assessment_entities_group aeg')
                ->join('subject_master sm', 'sm.id = aeg.subject_master_id', 'left')
                // ->where('aeg.is_elective', 0)
                ->where('aeg.class_id', $class_id)
                ->order_by('sorting_order, entity_name')
                ->get()->result();

        $electives= $this->db_readonly->select("aeg.id, aeg.group_name as name, 1 as is_elective, ifnull(sm.subject_name, '-') as subject_name")
                ->from('assessment_elective_group aeg')
                ->join('subject_master sm', 'sm.id = aeg.subject_master_id', 'left')
                // ->where('aeg.is_elective', 0)
                ->where('aeg.class_id', $class_id)
                ->order_by('group_name')
                ->get()->result();

        return array_merge($groups, $electives);
    }

    function get_assessments_class_wise_v2() {
        $generation_type= $this->input->post('generation_type');
        $class_selector= $this->input->post('class_selector');
        $generation_type= explode(',', $generation_type);

        

        $ass= $this->db_readonly->select("id, short_name")->where('class_id', $class_selector)->where_in("generation_type", $generation_type)->get('assessments')->result();
        // echo '<pre>'; print_r($ass); die();
        if(!empty($ass)) {
            return $ass;
        }
        return [];
    }

    function getAcadYearWiseClassWiseSubjects() {
        $acad_year= $this->input->post('acad_year');
        // $class= $this->db_readonly->select("id, class_name")->where('acad_year_id', $acad_year)->order_by('display_order, class_name')->get('class')->result();
        $groups= $this->db_readonly->select("aeg.id, 0 as is_elective, aeg.entity_name as name, c.class_name, if(aeg.subject_master_id is null OR aeg.subject_master_id = '', 0, 1) as is_mapped")
                ->from('class c')
                ->join('assessment_entities_group aeg', 'aeg.class_id = c.id')
                ->where('c.acad_year_id', $acad_year)
                ->order_by('aeg.entity_name, c.class_name')
                ->get()->result();
        $electives= $this->db_readonly->select("aeg.id, 1 as is_elective, aeg.group_name as name, c.class_name, if(aeg.subject_master_id is null OR aeg.subject_master_id = '', 0, 1) as is_mapped")
                ->from('class c')
                ->join('assessment_elective_group aeg', 'aeg.class_id = c.id')
                ->where('c.acad_year_id', $acad_year)
                ->order_by('aeg.group_name, c.class_name')
                ->get()->result();
        return array_merge($groups, $electives);
    }

    function submit_subjectExaminations_to_subjectMAster() {
        $input= $this->input->post();
        $examination_subjects= isset($input['examination_subjects']) ? $input['examination_subjects'] : [];
        $subject_master_id= isset($input['subject_master_id']) ? $input['subject_master_id'] : NULL;
        $groups= [];
        $electives= [];
        if(!empty($examination_subjects)) {
            foreach($examination_subjects as $key => $val) {
                $id___type= explode('___', $val);
                if($id___type[1] == '0') { // type == 0 means non-elective
                    $groups[]= array(
                        'id' => $id___type[0],
                        'subject_master_id' => $subject_master_id
                    );
                } else {
                    $electives[]= array(
                        'id' => $id___type[0],
                        'subject_master_id' => $subject_master_id
                    );
                }
            }
        }
        // echo '<pre>'; print_r($groups);
        // echo '<pre>'; print_r($electives); die();

        $this->db->trans_start();
        if(!empty($groups)) {
            $this->db->update_batch('assessment_entities_group', $groups, 'id');
        }
        if(!empty($electives)) {
            $this->db->update_batch('assessment_elective_group', $electives, 'id');
        }
        $this->db->trans_complete();

        return 1;


        // echo '<pre>'; print_r($_POST); die();
    }
    
    public function getSecAssessments($sectionId) {
        // First get the class_id from class_section
        $this->db->select('class_id');
        $this->db->from('class_section');
        $this->db->where('id', $sectionId);
        $classData = $this->db->get()->row();
        
        if (!$classData) {
            return array();
        }

        // Then get assessments for that class
        $this->db->select('a.id, a.short_name, a.long_name, a.generation_type');
        $this->db->from('assessments a');
        $this->db->where('a.class_id', $classData->class_id);
        $this->db->where('a.acad_year_id', $this->yearId);
        $this->db->order_by('a.short_name', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    public function getClassAndSectionDetails($classId, $sectionId) {
        $sectionData = $this->db_readonly->select('cs.id, cs.class_id as classId, cs.class_name as className, cs.section_name as sectionName, concat(ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "")) as classteacherName, concat(ifnull(smp.first_name, ""), " ", ifnull(smp.last_name, "")) as principalName, concat(ifnull(smc.first_name, ""), " ", ifnull(smc.last_name, "")) as coordinatorName')
            ->from('class_section cs')
            ->join('class c', 'c.id = cs.class_id')
            ->join('staff_master sm', 'sm.id = cs.class_teacher_id', 'left')
            ->join('staff_master smp', 'smp.id = c.principal_id', 'left')
            ->join('staff_master smc', 'smc.id = c.coordinator_id', 'left')
            ->where('cs.id', $sectionId)
            ->get()->row();
        
        if (!$sectionData) {
            return false;
        }

        return [
            'sectionData' => $sectionData
        ];
    }

    public function getAssessmentDetails($assessmentId) {
        return $this->db_readonly->select('id, long_name, generation_type')->get_where('assessments', ['id' => $assessmentId])->row();
    }

    public function getClassSubjects($assessmentId) {
        $result = $this->db_readonly->select('ae.id as ae_id, aem.name as subject_name')
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id = ae.entity_id')
            ->where('ae.assessment_id', $assessmentId)
            ->get()->result();
        return $result;
    }

    public function getSubjectMarks($assessmentId, $class_subject_id, $sectionId) {
        $result = $this->db_readonly->select('aems.student_id, aems.marks, aems.grade, aems.entity_grade_rank, aems.entity_section_rank, 
            CONCAT(ifnull(sa.first_name, ""), " ", ifnull(sa.last_name, "")) as student_name, 
            sa.admission_no, sy.roll_no, ae.total_marks, ae.class_average, ae.class_highest')
            ->from('assessments_entities_marks_students aems')
            ->join('assessments_entities ae', 'ae.id = aems.assessments_entities_id')
            ->join('student_admission sa', 'sa.id = aems.student_id')
            ->join('student_year sy', 'sy.student_admission_id = sa.id')
            ->where('ae.id', $class_subject_id)
            ->where('sy.class_section_id', $sectionId)
            ->where('sa.admission_status', 2)
            ->where('sy.promotion_status !=', '4')
            ->where('sy.promotion_status !=', '5')
            ->order_by('sa.first_name')
            ->get()
            ->result();
        return $result;
    }


    public function get_individual_staff_performance_data($classId, $sectionId, $assessmentId, $ae_id = null) {
        // First get total number of students in the section
        $totalStudentsInSection = $this->db_readonly->select('COUNT(DISTINCT sa.id) as total')
            ->from('student_admission sa')
            ->join('student_year sy', 'sy.student_admission_id = sa.id')
            ->where('sy.class_section_id', $sectionId)
            ->where('sa.admission_status', 2)
            ->where('sy.promotion_status !=', '4')
            ->where('sy.promotion_status !=', '5')
            ->get()
            ->row()
            ->total;

        // Get marks for the specific subject
        $marks = $this->db_readonly->select('aems.marks, ae.total_marks, sa.id as student_id')
            ->from('assessments_entities_marks_students aems')
            ->join('assessments_entities ae', 'ae.id = aems.assessments_entities_id')
            ->join('student_admission sa', 'sa.id = aems.student_id')
            ->join('student_year sy', 'sy.student_admission_id = sa.id')
            ->where('ae.id', $ae_id)
            ->where('sy.class_section_id', $sectionId)
            ->where('sa.admission_status', 2)
            ->where('sy.promotion_status !=', '4')
            ->where('sy.promotion_status !=', '5')
            ->get()
            ->result();

        if (empty($marks)) {
            return false;
        }

        // Initialize grade ranges
        $gradeRanges = [
            ['min' => 91, 'max' => 100, 'grade' => 'A1', 'point' => 8],
            ['min' => 81, 'max' => 90, 'grade' => 'A2', 'point' => 7],
            ['min' => 71, 'max' => 80, 'grade' => 'B1', 'point' => 6],
            ['min' => 61, 'max' => 70, 'grade' => 'B2', 'point' => 5],
            ['min' => 51, 'max' => 60, 'grade' => 'C1', 'point' => 4],
            ['min' => 41, 'max' => 50, 'grade' => 'C2', 'point' => 3],
            ['min' => 33, 'max' => 40, 'grade' => 'D', 'point' => 2],
            ['min' => 0, 'max' => 32, 'grade' => 'E', 'point' => 1]
        ];

        // Initialize grade statistics
        $gradeStats = array();
        foreach ($gradeRanges as $range) {
            $gradeStats[] = [
                'range' => $range['min'] . '-' . $range['max'],
                'grade' => $range['grade'],
                'points' => $range['point'],
                'count' => 0,
                'pq' => 0
            ];
        }
        $totalStudents = 0;
        $totalPQ = 0;

        // Calculate grade distribution
        foreach ($marks as $mark) {
            if ($mark->marks >= 0) { // Exclude absentees
                $percentage = ($mark->marks / $mark->total_marks) * 100;
                foreach ($gradeStats as &$stat) {
                    list($min, $max) = explode('-', $stat['range']);
                    if ($percentage >= $min && $percentage <= $max) {
                        $stat['count']++;
                        $stat['pq'] = $stat['points'] * $stat['count'];
                        $totalStudents++;
                        $totalPQ += $stat['points'];
                        break;
                    }
                }
            }
        }
        // Return the performance data structure
        return [
            'gradeStats' => $gradeStats,
            'total_students' => $totalStudents,
            'totalPQ' => $totalPQ,
            'performanceIndex' => $totalStudents > 0 ? round(($totalPQ * 100) / ($totalStudents * 8), 2) : 0
        ];
    }




}