<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
  <li>Student Consecutive Absent Report</li>
</ul>

<div class="container-fluid">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border d-flex align-items-center justify-content-between">
      <h3 class="card-title panel_title_new_style_staff mb-0">
        <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>">
          <span class="fa fa-arrow-left"></span>
        </a> 
        Student Consecutive Absent Report
      </h3>
    </div>
    <div class="card-body"> 
      <form id="consecutiveAttendanceForm" method="post">
        <div class="row g-3">
          

          <div class="col-md-2">
            <div class="form-group">
              <label for="reportrange">Date Range</label>
              <div id="reportrange" class="dtrange">
                <span></span>
                <input type="hidden" name="from_date" id="from_date">
                <input type="hidden" name="to_date" id="to_date">
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="col-md-2 my-5">
          <button type="button" class="btn btn-primary w-100" id="submitBtn"  onclick="studentConsecutiveAbsent()" style ="margin-top: -4px;">Get</button>
          <!-- <div id="exportButtons"></div>  -->
          </div>
        </div>
      </form>
      <div id="consecutiveAbsentReport" class="mt-4"></div>
    </div>
  </div>
</div>

<!-- Date Range Picker JS and CSS -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
 $("#reportrange").daterangepicker({
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment()],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment(),
    maxDate: moment(),
  }, function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });

  // Set initial date range (today)
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));

  const loading = `<div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="text-align: center;">
                    <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                  </div>`;

  function studentConsecutiveAbsent() {
    $("#submitBtn").prop("disabled", true).text("Please wait...");
    $('#consecutiveAbsentReport').html(loading);
    var formData = $('#consecutiveAttendanceForm').serialize();

    $.ajax({
        type: 'POST',
        data: formData,
        url: '<?php echo base_url('attendance_day_v2/Attendance_day_v2/ajaxStudentConsecutiveAbsentReport') ?>',
        dataType: 'json',
        success: function(absenceReport) {
            try {
                if(absenceReport?.length) {
                    let html = `
                        <table class="table table-bordered table-striped table-hover" id="studentActionTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Student Name</th>
                                    <th style="text-align: center;">Admission No</th>
                                    <th style="text-align: center;">Enrollment No</th>
                                    <th style="text-align: center;">Class Section</th>
                                    <th style="text-align: center;">Total Absentee Count</th>
                                    <th>Absent Dates</th>
                                </tr>
                            </thead>
                            <tbody>`;

                    absenceReport.forEach((row, index) => {
                        html += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${row.student_name}</td>
                                <td style="text-align: center;">${row.admission_no}</td>
                                <td style="text-align: center;">${row.enrollment_number}</td>
                                <td style="text-align: center;">${row.class} ${row.section}</td>
                                <td style="text-align: center;">${row.total_absences}</td>
                                <td>${Array.isArray(row.absent_dates) ? row.absent_dates.map((date, i) => (i % 5 === 0 && i !== 0 ? '<br>' : '') + date).join(', ') : ''}</td>
                            </tr>`;
                    });

                    html += `</tbody></table>`;
                    // $(document).ready(function() {
                      $('#consecutiveAbsentReport').html(html); 
                      $('#studentActionTable').DataTable( {
                      ordering:false,
                      paging : true,
                      scrollY :'40vh',
                      responsive: true,
                      "language": {
                        "search": "",
                        "searchPlaceholder": "Enter Search..."
                      },
                      "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                          "pageLength": 10,
                      dom: 'lBfrtip',
                      buttons: [
                        {
                        extend: 'excelHtml5',
                        text: 'Excel',
                        filename: 'absentReport',
                        className: 'btn btn-info'
                        },
                        {
                        extend: 'print',
                        text: 'Print',
                        filename: 'absentReport',
                        className: 'btn btn-info'
                        },
                        {
                        extend: 'pdfHtml5',
                        text: 'PDF',
                        filename: 'absentReport',
                        className: 'btn btn-info'
                        }
                      ]
                        });
                } else if (absenceReport?.length === 0) {
                  $('#consecutiveAbsentReport').html('<div class="alert alert-warning text-center">No data found.</div>');
                }else {
                  $('#consecutiveAbsentReport').html('<div class="alert alert-warning text-center">No data found.</div>');
                }
            } catch (err) {
                console.error('Error parsing response:', err);
                $('#consecutiveAbsentReport').html('<div class="alert alert-danger text-center">Failed to load data.</div>');
            } finally {
                $("#submitBtn").prop("disabled", false).text("Get");
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            $('#consecutiveAbsentReport').html('<div class="alert alert-danger text-center">Failed to load data.</div>');
            $("#submitBtn").prop("disabled", false).text("Get");
        }
    });
}


</script>
<style>
    .form-group {
  margin: 8px 0px;
}

.form-group:last-child {
  margin-bottom: 10px;
}

div.dt-buttons {
  float: right;
  margin-bottom: 10px;
}

.dataTables_wrapper .dt-buttons {
		float: right;
    
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5vh;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:2vh;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 5vh;
		}	
	}
  .dataTables_scrollBody{
    margin-top: -13px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }
  /* .dt-buttons{
    margin-right: 3vh;

  } */
  .form-horizontal .control-label{
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;

  }
  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

  .dataTables_filter label input{
    margin-right:-1rem;
  }
</style>
