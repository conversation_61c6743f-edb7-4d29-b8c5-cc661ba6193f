<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
class Sales_model extends CI_Model {
    private $yearId;
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
    }

    public function get_items() {
        $this->db->select('*');
        $this->db->from('sales_item');
        $result = $this->db->get()->result();
        foreach ($result as &$item) {
            $item->editLock = $this->_doesVariantsExist($item->id);
        }
        return $result;
    }

    public function Kolkata_datetime(){
      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime();
      $date->setTimezone($timezone );
      $dtobj = $date->format('Y-m-d H:i:s');
      return $dtobj;
  }

     public function edit_item($id) {
        $this->db->select('*');
        $this->db->from('sales_item');
        $this->db->where('id',$id);
        return $this->db->get()->row();
    }

     public function add_items() {
        $data_info = array(
            'item_name' => $this->input->post('item_name'),
            'item_type' => $this->input->post('item_type'),
            'created_by' => $this->authorization->getAvatarId()
        );
        return $this->db->insert('sales_item', $data_info);
    }


    private function _doesVariantsExist($id) {
        $result = $this->db->select('count(*) as cnt')
            ->from('sales_variant')
            ->where('item_id', $id)
            ->get()->row();
        if ($result->cnt > 0) {
            return 1;
        } else {
            return 0;
        }
    }


    public function delete_item($id) {
        $this->db->where('item_id', $id);
        $this->db->delete('sales_variant');
        $this->db->where('id', $id);
        return $this->db->delete('sales_item');
    }

 	public function update_item($id) {
        $input = $this->input->post();
        $data = array(
            'item_name' => $input['item_name'],
            'item_type' => $input['item_type'],
        );
        $this->db->where('id', $id);
        return $this->db->update('sales_item', $data);
    }

     public function getItemObject($id) {
        $result = $this->db->select('*')
            ->from('sales_item')
            ->where('id', $id)
            ->get()->row();
        return $result;
    }

    public function getDropdownbyItmeWise($itemType){
        if ($itemType =='class') {
            $this->db->distinct();
         	return $this->db->select('id,class_name as name')->from('class')->get()->result();
        }else{
            $this->db->distinct();
            return $this->db->select('var_option as id, var_option as name')->from('sales_variant')->where('item_type',$itemType)->get()->result();
        }
    }

    public function add_variant($itemId){

        $this->db->trans_start();
        $data_info = array(
            'item_id' => $itemId,
            'item_name' => $this->input->post('item_name'),
            'item_type' => $this->input->post('item_type'),
            'var_option' => $this->input->post('vartaint_option'),
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        $this->db->insert('sales_variant', $data_info);
        $lastInsertId = $this->db->insert_id();
        $quantity = $this->input->post('quantity');
        $price = $this->input->post('variant_price');

        foreach ($price as $key => $price) {
            $data_info1[] = array(
            'quantity' =>  (empty($quantity[$key])? 0 : $quantity[$key]),
            'variant_id' => $lastInsertId,
            'price' => $price,
            );
        }
        $this->db->insert_batch('sales_variant_qty', $data_info1);
        return  $this->db->trans_complete();
         
    }

    public function get_variants($id,$item_type) {

        if ($item_type == 'class') {
            return $this->db->select('v.id,v.item_id, v.item_name,v.item_type,vq.quantity,vq.price,c.class_name as optionName')
            ->from('sales_variant v')
            ->where('v.item_id',$id)
            ->join('sales_variant_qty vq','vq.variant_id=v.id')
            ->join('class c','c.id=v.var_option')
            ->get()->result();
        }else{
            return $this->db->select('v.id,v.item_id,v.item_name,v.item_type,vq.quantity,vq.price,v.var_option as optionName')
            ->from('sales_variant v')
            ->join('sales_variant_qty vq','vq.variant_id=v.id')
            ->where('v.item_id',$id)
            ->get()->result();
        }

    }

    public function delete_variants($id, $item_id) {
            $this->db->where('id', $id);
            $this->db->delete('sales_variant');
            $this->db->where('variant_id', $id);
        return $this->db->delete('sales_variant_qty');
    }

    public function get_varients_names(){
            $this->db->distinct();
     return $this->db->select('v.item_id,v.item_name')->from('sales_variant v')->get()->result();
    }
    

    public function get_all_class(){
        return $this->db->select('id,class_name,board')->get('class')->result();
    }

    public function getstudentDetails_classwise($clsId){
        return $this->db->select("sd.id, sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name, c.class_name as clsName,  sd.admission_no, p.mobile_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
        ->join("class c", "sy.class_id=c.id",'left')
        ->where('sy.class_id',$clsId)
        ->where('sy.acad_year_id',$this->yearId)
        ->where('sd.admission_status',2)
        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join("parent p", "p.id=sr.relation_id")
        ->get()->result();
     }

    public function get_sales_student_detailbyId($admission_no, $std_id){

        $data_info = $this->db->select("sd.id, sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name, sd.admission_no, p.mobile_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name, c.id as class")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join("class_section cs", "sy.class_section_id=cs.id",'left')
        ->join("class c", "sy.class_id=c.id",'left')
        ->where('sy.acad_year_id',$this->yearId)
        ->where('sd.admission_status',2)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join("parent p", "p.id=sr.relation_id");
        if($admission_no){
          $this->db->where('sd.admission_no', $admission_no);
        }
        if ($std_id) {
          $this->db->where('sd.id', $std_id);
        }
       return $this->db->get()->row();
    }

   
    public function get_itemsOptionbyItmesId($itemsId,$stdId,$clasId){

      $this->db->select('item_type');
      $this->db->from('sales_variant');
      $this->db->where('item_id',$itemsId);
      $result = $this->db->get()->row();

      if ($result->item_type == 'class') {
        $result = $this->db->select('vq.id as vId, c.class_name as varOption,v.item_type, vq.quantity, vq.price')
          ->from('sales_variant v')
          ->join('class c','v.var_option=c.id')
          ->where('v.item_id',$itemsId)
          ->where('c.id',$clasId)
          ->join('sales_variant_qty vq','vq.variant_id=v.id')
          ->get()->result();
      }elseif ($result->item_type == 'size') {
        $result = $this->db->select('vq.id as vId, v.var_option as varOption, v.item_type, vq.quantity, vq.price')
          ->from('sales_variant v')
          ->where('item_id',$itemsId)
          ->join('sales_variant_qty vq','vq.variant_id=v.id') 
          ->get()->result();
      }
     return $result;
    }

    public function get_amountByoptiontimesId($optionItmesId){
      return $this->db->select('quantity,price')
          ->from('sales_variant_qty v')
          ->where('id',$optionItmesId)
          ->get()->row();
    }

    public function fee_variants_receiptNo(){
      $query = "select receipt_no from sales_transaction order by receipt_no desc limit 1";
      $res = $this->db->query($query);
     
      if($res->num_rows() > 0){
          $maxid= $res->row();
          $val = explode('F0000',$maxid->receipt_no);
          $recIncrement = $val[1]+1;
          $receiptNo = 'F0000' . $recIncrement;
          return $receiptNo;
      }
      return 'F00001';
    }

    public function insert_sales_transaction($std_id, $input, $catId, $products){

      $input = $this->input->post();
      $ePayment_type = explode('_', $input['payment_type']);
      $payment_type = $ePayment_type[0];
      $reconciliation_status = $ePayment_type[1];

      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime($input['receipt_date']);
      $time = new DateTime();
      $time->setTimezone($timezone);
      $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
      $receipt_date =  $merge->format('Y-m-d H:i:s'); 
      if (!empty($input['cheque_dd_nb_cc_dd_number'])) {
        $cheque_dd_nb_cc_dd_number = $input['cheque_dd_nb_cc_dd_number'];
      }elseif(!empty($input['dd_number'])){
        $cheque_dd_nb_cc_dd_number = $input['dd_number'];
      }elseif(!empty($input['cc_number'])){
        $cheque_dd_nb_cc_dd_number = $input['cc_number'];
      }elseif(!empty($input['nb_number'])){
        $cheque_dd_nb_cc_dd_number = $input['nb_number'];
      }else{
        $cheque_dd_nb_cc_dd_number = null;
      }

    $tAmount = 0;
    foreach ($products as $key => $val) {
      $tAmount += $val['amount'];
    }

      $this->db->trans_start();

      $master_data = array(
          'student_id'=> ($std_id == '')?0:$std_id,
          'category_id'=> $catId,
          'payment_type'=> $payment_type,
          'total_amount'=> $tAmount,
          // 'receipt_date'=> date('Y-m-d',strtotime($input['receipt_date'])),
          'receipt_date'=> $receipt_date,
          'created_by'=> $this->authorization->getAvatarId(),
          'bank_name'=> (isset($input['bank_name']) == '')? null : $input['bank_name'],
          'bank_branch'=> (isset($input['branch_name']) == '')? null : $input['branch_name'],
          'cheque_dd_number'=> $cheque_dd_nb_cc_dd_number,
          'recon_status'=> $reconciliation_status,
          'cheque_dd_date'=> (!isset($input['bank_date'])) ? null : date('Y-m-d',strtotime($input['bank_date'])),
          'remarks'=> (!isset($input['remarks'])) ? null : $input['remarks'],
          'card_charge_amount'=> (!isset($input['card_charge'])) ? null : $input['card_charge'],
          'sales_type' => $input['sale_type'],
          'student_name' => ($input['new_std_name'] == '')?NULL:$input['new_std_name'],
          'parent_name' => ($input['parent_name'] == '')?NULL:$input['parent_name'],
          'contact_number' => ($input['contact_number'] == '')?NULL:$input['contact_number'],
          'class_name' => ($input['class_name'] == '')?NULL:$input['class_name'],
          'acad_year_id' => $this->yearId
        );
      $this->db->insert('sales_master',$master_data);
      $stransId = $this->db->insert_id();

      $dataArry = [];
      foreach ($products as $k =>  $productId) {
        $dataArry[] =  array(
          'sales_master_id'=> $stransId,
          'inventory_product_id'=> $productId['prodcuts'],
          'inventory_product_variant_id'=> $productId['variants'],
          'quantity'=> $productId['quantity'], 
          'amount'=> $productId['amount'],
        );
      }
      $this->db->insert_batch('sales_transactions', $dataArry);


      $vQtyUpdate = [];
      foreach ($products as $k =>  $productId) {
        $vQtyUpdate[] =  array(
          'id'=> $productId['variants'],
          'current_quantity'=> $productId['current_quantity'] -  $productId['quantity'],
        );
      }
      $this->db->update_batch('inventory_product_variant', $vQtyUpdate,'id');

      $this->db->trans_complete();
      if ($this->db->trans_status()) {
        return $stransId;
      }else{
        return false;
      }

    }

    public function update_receipt_sale_transcation($sTransId, $catId, $input){

      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime($input['receipt_date']);
      $time = new DateTime();
      $time->setTimezone($timezone);
      $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
      $receipt_date =  $merge->format('Y-m-d H:i:s'); 

      $receipt_book = $this->db->select('frb.*')
      ->from('inventory_product_category ipc')
      ->where('ipc.id',$catId)
      ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
      ->get()->row();
      if (!empty($receipt_book)) {
        $this->db->where('id',$receipt_book->id);
        $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));

        $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);

        $this->db->where('id',$sTransId);
        return $this->db->update('sales_master', array('receipt_no'=>$receipt_number,'receipt_date'=> $receipt_date));
      }else{
        return false;
      }
     

    }

    public function update_receipt_sale_transcation_manual($sTransId, $catId, $input){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime($input['receipt_date']);
        $time = new DateTime();
        $time->setTimezone($timezone);
        $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
        $receipt_date =  $merge->format('Y-m-d H:i:s');

        $this->db->where('id',$sTransId);
        return $this->db->update('sales_master', array('receipt_no'=>$input['manual_receipt'],'receipt_date'=> $receipt_date));
    }


    public function get_sales_receipt_data($stransId, $std_id, $sale_type='existing'){

      $master =  $this->db->select("sm.id as transId, sm.receipt_no, sm.payment_type, sm.card_charge_amount, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, '%d-%m-%Y') as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no, ipc.receipt_template, sm.sales_type, sm.student_name, sm.remarks, ifnull(sm.class_name,'NA') as class_name, ifnull(sm.parent_name,'') as parent_name")
       ->from('sales_master sm')
       ->where('sm.id',$stransId) 
       ->join('inventory_product_category ipc','sm.category_id=ipc.id')
       ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
       ->get()->row();

      // echo "<pre>"; print_r($std_id); die();
      $trans = $this->db->select('st.quantity, st.amount, ipm.product_name, ipv.name as variant_name, ipv.hsn_sac')
      ->from('sales_transactions st')
      ->where('st.sales_master_id',$master->transId)
      ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id')
      ->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id')
      ->get()->result();

      if($sale_type == 'existing') {
        $std_data = $this->db->select("s.admission_no, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name,CONCAT(ifnull(p1.first_name,''), ' ', ifnull(p1.last_name,'')) AS mother_name, c.class_name, cs.section_name, s.id as student_id")
          ->from('student_admission s')
          ->join('student_year sy','s.id=sy.student_admission_id')
          ->where('sy.acad_year_id',$this->yearId)
          ->join('class c','c.id=sy.class_id')
          ->join('class_section cs','sy.class_section_id=cs.id','left')
          ->join('student_relation sr','sr.std_id=s.id')
          ->join('student_relation sr1','sr1.std_id=s.id')
          ->where('s.id',$std_id)
          ->where('sr.relation_type','Father')
          ->where('sr1.relation_type','Mother')
          ->join('parent p','p.id=sr.relation_id')
          ->join('parent p1','p1.id=sr1.relation_id')
          ->get()->row();

          $address =  $this->db->select("CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address")
          ->from('address_info add')
              ->where('add.stakeholder_id',$std_data->student_id)
              ->where('add.avatar_type', '1') //Student Address
              ->where('add.address_type', '0') //Permanent Address
          ->get()->row();
        if(!empty($address)){
          $master->student_address = $address->address;
        }else{
          $master->student_address = '';
        }
        $master->std_data = $std_data;
      }

      $master->trans = $trans;
      return $master;

    }


    public function get_sales_transaction_details($receiptNo){

       return $this->db->select("s.admission_no, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name, fv.receipt_no, amount, payment_type,v.item_name, v.item_type, date_format(fv.receipt_date,'%d-%m-%Y') as date,qt.quantity, v.var_option, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdName")
        ->from('sales_transaction fv')
        ->join('sales_variant_qty qt','qt.id=fv.variant_id')
        ->join('sales_variant v','v.id=qt.variant_id')
        ->join('student_admission s','s.id=fv.student_id')
        ->join('student_year sy','s.id=sy.student_admission_id')
        ->join('class c','c.id=sy.class_id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('student_relation sr','sr.std_id=s.id')
        ->where('fv.receipt_no',$receiptNo)
        ->where('sr.relation_type','Father')
        ->join('parent p','p.id=sr.relation_id')
         ->join('avatar a','fv.created_by=a.id','left')
        ->join('staff_master sm','a.stakeholder_id=sm.id','left')
        ->get()->result();
    }


    public function insert_receipts_book_sales() {

      $fee_receipt_book = array (
        'template_format' => $this->input->post('receipt_template'),
        'prefix' => $this->input->post('infix'),
        'digit_count' => $this->input->post('digit_count'),
        'running_number' => $this->input->post('running_number'),
        'year' => $this->input->post('year'),
      );
      return $this->db->insert('sales_receipt_book', $fee_receipt_book);
    }

    public function get_sales_receipt_books(){
      return $this->db->get('feev2_receipt_book')->result();
    }

    public function delete_receipts_book_sales($id){
      $this->db->where('id',$id);
      return $this->db->delete('sales_receipt_book');
    }

    public function get_prodcuts_all(){
      $result =  $this->db->select('ipm.id as productId, ipc.id as category_id, ipc.category_name, ipm.product_name')
      ->from('inventory_product_master ipm')
      ->join('inventory_product_category ipc','ipm.category_id=ipc.id')
      ->get()->result();

      $cateId =[];
      foreach ($result as $key => $val) {
        $cateId[$val->category_id][]= $val;
      }
      return $cateId;
    }

    public function getstudentallNames(){
      return $this->db->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, if(cs.section_name is not null AND cs.section_name != 'null', cs.section_name, 'NA') as section_name, if(cs.class_name is not null AND cs.class_name != 'null', cs.class_name, 'NA') as class_name, sd.id as id_number")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      // ->join('class c','sy.class_id=c.id','left')
      ->get()->result();
    }

    public function get_prodcut_varints($pId){
      return $this->db->select('id as vId, name as varinat_name,current_quantity, threshold_quantity')
      ->from('inventory_product_variant')
      ->where('product_id',$pId)
      ->where('current_quantity!=0')
      ->get()->result();
    }

    public function get_varints_price_quantity($vId){
     return $this->db->select('ipv.current_quantity, ipv.selling_price as price, current_quantity, threshold_quantity')
      ->from('inventory_product_variant ipv')
       ->where('ipv.id',$vId)
      ->get()->row();
    }

    public function get_daily_transaction_report($from_date, $to_date, $product_variants, $payment_modes){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db->select("sm.id as smId, sm.student_id, sm.receipt_no, sm.payment_type, sm.total_amount, sm.bank_name, sm.bank_branch, sm.cheque_dd_date, sm.remarks, sm.cheque_dd_number, st.quantity, st.amount, st.quantity, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(c.class_name,''), '', ifnull(cs.section_name,'')) AS class_section, ipm.product_name, ipv.name as variants, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, sm.sales_type, sm.student_name, sm.parent_name, sm.contact_number, sm.receipt_pdf_path, ifnull(sm.class_name,'NA') as class_name, sm.recon_status")
      ->from('sales_master sm')
      // ->where('sm.acad_year_id',$this->yearId)
      ->where('sm.soft_delete!=1')
      ->join('student_admission s','s.id=sm.student_id', 'left')
      ->join('student_year sy',"s.id=sy.student_admission_id and sy.acad_year_id=$this->yearId", 'left')
      ->join('class c','c.id=sy.class_id', 'left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('sales_transactions st','sm.id=st.sales_master_id')
      ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id') 
      ->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id')
      // ->group_by('ipv.id')
      ->order_by('sm.receipt_date','desc');
      if ($fromDate && $toDate) {
        $this->db->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($product_variants) {
          $this->db->where_in('st.inventory_product_variant_id',$product_variants);
      }
      if ($payment_modes) {
        $this->db->where_in('sm.payment_type',$payment_modes);
      }
     $result = $this->db->get()->result();
     $payment_mode = json_decode($this->input->post('payment_modeJSON'));
     $sales_payment_modes = $this->settings->getSetting('sales_payment_modes');
      $resData=[];
      foreach ($result as $key => $res) {
        if (!array_key_exists($res->smId, $resData)) {
          if(!empty($sales_payment_modes)){
            foreach($sales_payment_modes as $k => $v){
              $payment_type_val = explode('_',$v->value);
              if($payment_type_val[0] == $res->payment_type){
                $type = $v->name;
              }
            }
          }else{
            switch ($res->payment_type) {
              case '9':
                $type = 'Cash';
                break;
              case '4':
                $type = 'Cheque';
                break;
              case '1':
                $type = 'DD';
                break;
              case '7':
                $type = 'Card';
                break;
              case '8':
                $type = 'Net Banking';
                break;
              case '10':
                $type = 'Online Link';
                break;
              case '11':
                $type = 'UPI - Online';
                break;
              default:
                $type = 'Cash';
                break;
            }
          }
          $exitingDetails = $this->get_sales_history_student_details($res->student_id);
          $fatherName = '';
          if(!empty($exitingDetails)){
            $fatherName = $exitingDetails->parent_name;
          }
          $resData[$res->smId]['receipt_no'] = $res->receipt_no;
          $resData[$res->smId]['student_id'] = $res->student_id;
          $resData[$res->smId]['receipt_date'] = $res->receipt_date;
          $resData[$res->smId]['total_amount'] = 0;
          $resData[$res->smId]['payment_type'] = $type;
          $resData[$res->smId]['recon_status'] = $res->recon_status;
          $resData[$res->smId]['sales_type'] = $res->sales_type;
          $resData[$res->smId]['smId'] = $res->smId;
          $resData[$res->smId]['std_name'] = ($res->sales_type == 'existing')?$res->std_name:$res->student_name;
          $resData[$res->smId]['parent_name'] = ($res->sales_type == 'existing')?$fatherName:$res->parent_name;
          $resData[$res->smId]['class_section'] = ($res->sales_type == 'existing')?$res->class_section:$res->class_name;
          $resData[$res->smId]['products'] = '';
          $resData[$res->smId]['qty_receipt_wise'] = '';

          $resData[$res->smId]['bank_name'] = isset($res->bank_name) ? $res->bank_name : "";
          $resData[$res->smId]['bank_branch'] = isset($res->bank_branch) ? $res->bank_branch : "";
          $resData[$res->smId]['cheque_dd_number'] = isset($res->cheque_dd_number) ? $res->cheque_dd_number : "";
          $resData[$res->smId]['remarks'] = isset($res->remarks) ? $res->remarks : "";
          $resData[$res->smId]['cheque_dd_date'] = $res->cheque_dd_date;
        }
        $resData[$res->smId]['total_amount'] += $res->amount;
        $resData[$res->smId]['products'] .= '- ' . $res->product_name .' -<span class="fa fa-angle-double-right"></span> ' .$res->variants . ' ( Amount : '.$res->amount .' )' . ' ( Quantity : '.$res->quantity .' ) <br>' ;
        $resData[$res->smId]['qty_receipt_wise'] .= $res->quantity. '___';
      }
        // array_multisort(array_column($resData,'receipt_date'), SORT_DESC, array_column($resData, 'receipt_no'),SORT_DESC, $resData);
        // echo '<pre>'; print_r($resData); die(); 
// Custom comparison function to sort by receipt_date in descending order
      usort($resData, function($a, $b) {
        $dateA = DateTime::createFromFormat('d-m-Y', $a['receipt_date']);
        $dateB = DateTime::createFromFormat('d-m-Y', $b['receipt_date']);
        return $dateB <=> $dateA;  // Sort in descending order
      });

      return $resData;
    }

     public function get_sales_student_historyby_id($admission_no, $std_id){
      $this->db->select("sd.id,  concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as std_name")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join("sales_master sm", "sd.id=sm.student_id")
      ->where('sy.acad_year_id',$this->yearId)
      ->where('sd.admission_status',2);
      if($admission_no){
        $this->db->where('sd.admission_no', $admission_no);
      }
      if ($std_id) {
        $this->db->where('sd.id', $std_id);
      }
      $data_info = $this->db->get()->row();

      if (!empty($data_info)) {
        return $data_info;
      }else{
        return 0;
      }
    }

    public function get_sales_history_student_wise($student_id){

      $master =  $this->db->select('sm.id as transId, sm.receipt_no, sm.student_id, sm.payment_type, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, "%d-%m-%Y") as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no')
       ->from('sales_master sm')
       ->where('sm.student_id',$student_id)
       ->where('sm.soft_delete!=1')
       ->join('inventory_product_category ipc','sm.category_id=ipc.id')
       ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
       ->get()->result();

       if (empty($master)) {
         return false;
       }
      $salesids =[];
      foreach ($master as $key => $val) {
           array_push($salesids, $val->transId);
      } 

      foreach ($salesids as $key => $salId) {
        $result[$salId] = $this->db->select('st.id as transId, st.quantity, st.amount, ipm.product_name, ipv.name as variant_name')
        ->from('sales_transactions st')
        ->where('st.sales_master_id',$salId)
        ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id')
        ->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id')
        ->get()->result();
      }

      foreach ($master as $key => &$val) {
        foreach ($result as $salId => $res) {
          if ($val->transId == $salId) {
            $val->trans = $res;
          }
        }
      }     
      return $master;

    }

    public function get_sales_history_student_details($student_id)
    {
        $std_data = $this->db->select("s.admission_no, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_admission s')
        ->join('student_year sy','s.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','c.id=sy.class_id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('student_relation sr','sr.std_id=s.id')
        ->where('s.id',$student_id)
        ->where('sr.relation_type','Father')
        ->join('parent p','p.id=sr.relation_id')
        ->get()->row();
      return $std_data;
    }


    public function update_sales_path($stransId, $path)
    {
      $this->db->where('id',$stransId);
      $this->db->update('sales_master',array('receipt_pdf_path'=>$path,'pdf_status' => 0));
      return $this->db->affected_rows();
    }

    public function update_html_receipt_sales($html, $stransId){
      $this->db->where('id',$stransId);
      $this->db->update('sales_master',array('html_sales'=>$html));
      return $this->db->affected_rows();
    }

    public function updateSalesPdfLink($path, $status) {
      $this->db->where('receipt_pdf_path',$path);
      return $this->db->update('sales_master', array('pdf_status' => $status));
    }


  public function download_sales_receipt_path($id){
    return $this->db->select('receipt_pdf_path')->where('id', $id)->get('sales_master')->row()->receipt_pdf_path;
  }

  public function get_html_transaction_for_pdf($transIds){
     return $this->db->select('html_sales')->where('id', $transIds)->get('sales_master')->row();
  }
  
  public function get_sales_receipt_data_for_pdf($stransId){

    $master =  $this->db->select('sm.id as transId, sm.student_id, sm.receipt_no, sm.payment_type, sm.card_charge_amount, total_amount, created_by, bank_name, bank_branch, cheque_dd_number, date_format(receipt_date, "%d-%m-%Y") as receipt_date, cheque_dd_date, ipc.category_name, gst_no, pan_no, ipc.receipt_template, sm.sales_type, sm.student_name')
     ->from('sales_master sm')
     ->where('sm.id',$stransId) 
     ->join('inventory_product_category ipc','sm.category_id=ipc.id')
     ->join('feev2_receipt_book frb','frb.id=ipc.receipt_book_id')
     ->get()->row();

    // echo "<pre>"; print_r($std_id); die();
    $trans = $this->db->select('st.quantity, st.amount, ipm.product_name, ipv.name as variant_name, ipv.hsn_sac')
    ->from('sales_transactions st')
    ->where('st.sales_master_id',$master->transId)
    ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id')
    ->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id')
    ->get()->result();

      $std_data = $this->db->select("s.admission_no, p.mobile_no, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) AS parent_name, c.class_name, cs.section_name")
        ->from('student_admission s')
        ->join('student_year sy','s.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','c.id=sy.class_id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('student_relation sr','sr.std_id=s.id')
        ->where('s.id',$master->student_id)
        ->where('sr.relation_type','Father')
        ->join('parent p','p.id=sr.relation_id')
        ->get()->row();
      $master->std_data = $std_data;

    $master->trans = $trans;
    return $master;

  }

  public function soft_delete_sales_receipt($salesId, $remarks){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();

    $data =array(
      'soft_delete'=>1, 
      'soft_delete_by'=>$this->authorization->getAvatarId(), 
      'soft_delete_on'=>$date->format('Y-m-d'), 
      'soft_delete_remarks'=>$remarks, 
    );
    $this->db->where('id',$salesId);
    return $this->db->update('sales_master',$data);
  }

  public function get_varints_name_for_accounts(){
   return $this->db->select('ipv.id, ipv.name, ipv.vendor_code, a.account')
    ->from('inventory_product_variant ipv')
    ->join('accounts a','ipv.vendor_code=a.tracknpay_vendor_id')
    ->get()->result();
  }

  public function update_sale_vendor_code_by_varints(){
    $this->db->where('id',$this->input->post('vendor_code_id'));
    return $this->db->update('inventory_product_variant',array('vendor_code'=>$this->input->post('vendor_code')));
  }

  public function delete_vendor_account_by_id($id){
    $this->db->where('id',$id);
    return $this->db->update('inventory_product_variant',array('vendor_code'=>''));
  }

  public function get_cancelation_transaction_report($from_date, $to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db->select("sm.id as smId, sm.student_id, sm.receipt_no, sm.payment_type, sm.total_amount, st.quantity, st.amount, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS std_name, CONCAT(ifnull(c.class_name,''), ' ', ifnull(cs.section_name,'')) AS class_section, ipm.product_name, ipv.name as variants, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, sm.sales_type, sm.student_name, sm.parent_name, sm.contact_number, sm.receipt_pdf_path, date_format(sm.soft_delete_on,'%d-%m-%Y') as soft_delete_on, sm.soft_delete_remarks, ifnull(sm.class_name,'NA') as class_name")
      ->from('sales_master sm')
      ->where('sm.soft_delete=1')
      ->join('student_admission s','s.id=sm.student_id', 'left')
      ->join('student_year sy',"s.id=sy.student_admission_id and sy.acad_year_id=$this->yearId", 'left')
      ->join('class c','c.id=sy.class_id', 'left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('sales_transactions st','sm.id=st.sales_master_id')
      ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id') 
      ->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id')
      ->order_by('sm.receipt_date');
      if (!empty($fromDate) && !empty($toDate)) {
        $this->db->where('date_format(sm.soft_delete_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
     $result = $this->db->get()->result();
     $payment_mode = json_decode($this->input->post('payment_modeJSON'));
      $resData=[];
      foreach ($result as $key => $res) {
        if (!array_key_exists($res->smId, $resData)) {
          switch ($res->payment_type) {
            case '9':
              $type = 'Cash';
              break;
            case '4':
              $type = 'Cheque';
              break;
            case '1':
              $type = 'DD';
              break;
            case '7':
              $type = 'Card';
              break;
            case '8':
              $type = 'Net Banking';
              break;
            default:
              $type = 'Cash';
              break;
          }
          $resData[$res->smId]['receipt_no'] = $res->receipt_no;
          $resData[$res->smId]['student_id'] = $res->student_id;
          $resData[$res->smId]['receipt_date'] = $res->receipt_date;
          $resData[$res->smId]['total_amount'] = $res->total_amount;
          $resData[$res->smId]['payment_type'] = $type;
          $resData[$res->smId]['sales_type'] = $res->sales_type;
          $resData[$res->smId]['soft_delete_remarks'] = $res->soft_delete_remarks;
          $resData[$res->smId]['soft_delete_on'] = $res->soft_delete_on;
          $resData[$res->smId]['std_name'] = ($res->sales_type == 'existing')?$res->std_name:$res->student_name;
          $resData[$res->smId]['class_section'] = ($res->sales_type == 'existing')?$res->class_section:$res->class_name;
          $resData[$res->smId]['products'] = '';
        }
        $resData[$res->smId]['products'] .= $res->product_name .'<br>' .$res->variants . ' ( Amount : '.$res->amount .' ) <br>' ;
      }

      return $resData;
    }

    public function get_prodcut_variant_list_report(){
      return $this->db->select('id as vId, name as varinat_name')
      ->from('inventory_product_variant')
      ->where('current_quantity!=0')
      ->get()->result();
    }


    public function get_new_student_sales_list_report(){
      return $this->db->select("sm.receipt_no, sm.payment_type, sm.total_amount, sm.remarks, sm.student_name, sm.parent_name, sm.contact_number, sm.class_name, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date")
      ->from('sales_master sm')
      ->where('sm.soft_delete!=1')
      ->order_by('sm.receipt_date','desc')
      ->get()->result();
    }

    public function get_reconciled_data($from_Date,$to_Date,$reconciled_type){
      $fromDate = date('Y-m-d',strtotime($from_Date));
      $toDate = date('Y-m-d',strtotime($to_Date));
       $this->db->select('sm.id as sale_master_id,sm.receipt_no,payment_type,total_amount,receipt_date,bank_name,bank_branch,receipt_pdf_path,sales_type,concat(ifnull(sa.first_name,"")," ",ifnull(sa.last_name,"")) as student_name,admission_no,concat(ifnull(c.class_name,"")," ",ifnull(cs.section_name,"")) as class_section,date_format(sm.cheque_dd_date,"%d-%m-%Y") as cheque_dd_date,ifnull(sm.cheque_dd_number,"-") as cheque_dd_number,concat(ifnull(sm.bank_name,"")," ",ifnull(sm.bank_branch,"")) as bank_details,sm.total_amount,st.amount,recon_status,ifnull(sm.student_name,"") as std_name,sm.class_name')
      ->from('sales_master sm')
      ->join('sales_transactions st','sm.id=st.sales_master_id')
      ->join('student_admission sa','sm.student_id=sa.id','left')
      ->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id = '.$this->acad_year->getAcadYearID().' and promotion_status != "JOINED"','left')
      ->join('class c','sy.class_id=c.id','left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('date_format(sm.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      if(!empty($reconciled_type)){
        $this->db->where('recon_status',$reconciled_type);
      }
      // $this->db->where('sy.acad_year_id',$this->acad_year->getAcadYearID());
      $this->db->where('sm.acad_year_id',$this->acad_year->getAcadYearID());
      return $this->db->get()->result();

    }

    public function submit_reconciled_status($input){
      $bank_paid_date = date('Y-m-d',strtotime($input['bank_paid_date']));
        $data = array(
          'recon_status' => $input['status'],
          'reconciled_bank_paid_date' => $bank_paid_date,
          'reconciled_submitted_on' => $this->Kolkata_datetime(),
          'reconciled_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->where('id',$input['sales_mst_id']);
        return $this->db->update('sales_master',$data);
    }

    public function reconsilation_failed($input){
        $data = array(
          'recon_status' => 3,
          'reconciled_failed_remarks'=>$input['remarks'],
          'reconciled_submitted_on' => $this->Kolkata_datetime(),
          'reconciled_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->where('id',$input['sales_mst_id']);
        return $this->db->update('sales_master',$data);
    }

} 