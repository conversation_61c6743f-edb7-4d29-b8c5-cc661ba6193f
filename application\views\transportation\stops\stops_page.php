<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('transportation') ?>">Transportation</a></li>
    <li>Stops</li>
</ul>

<hr>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('transportation') ?>">
						<span class="fa fa-arrow-left"></span>
						</a> 
						Stops
					</h3>
				</div>
				<div class="col-md-2">
					<button class="btn btn-info pull-right" onclick="document.getElementById('csvFileInput').click()">Upload Excel</button>
					<input type="file" id="csvFileInput" accept=".xlsx, .xls" style="display: none;" onchange="upload_stops_excel(event)" />
				</div>
			</div>
		</div>		
		<div class="card-body">
			<div class="row">
				<div class="form-group col-md-4">
					<label class="control-label">Stop name <font color="red">*</font></label>
					<input class="form-control" type="text" name="stop_name" id="stop_name" placeholder="Stop name" required="">
				</div>
				<div class="form-group col-md-4">
					<label class="control-label">Landmark <font color="red">*</font></label>
					<input class="form-control" type="text" name="landmark" id="landmark" placeholder="Landmark" required="" >
				</div>
				<div class="col-md-2" style="margin-top:2rem">
					<button type="submit" id="submit_tx_stops" class="btn btn-primary">Submit</button>
				</div>
			</div>
			<div class="col-md-12">
				<div class="row" id="txStopList">
				 	<div class="col-12 text-center loading-icon" style="display: none;">
	                	<i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
	                </div>
				</div>
			</div>



<div id="edit_stop" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" style="width:60%; margin:auto;">
        	<form method="post" action="<?php echo site_url('transportation/update_stop');?>" data-parsley-validate="" >
	            <div class="modal-header">
	                <h4 class="modal-title"><strong>Edit Stop</strong></h4>
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	            </div>
	            <div class="modal-body" id="stop-form">
	            	<input type="hidden" name="stop_id" id="stop_id">
	            	<div class="form-group">
	            		<label class="control-label">Stop name</label>
	            		<input class="form-control" type="text" name="stop_name" id="edit_stop_name" placeholder="Stop name" required="">
	            	</div>
	            	<div class="form-group">
	            		<label class="control-label">Landmark</label>
	            		<input class="form-control" type="text" name="landmark" id="edit_landmark" placeholder="Landmark" required="">
	            	</div>
	            </div>
	            <div class="modal-footer">
	              <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
	              <button type="submit" class="btn btn-primary">Submit</button>
	            </div>
	        </form>
        </div>
    </div>
</div>

<!-- Modal Structure -->
<div class="modal fade" id="csvModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="csvModalLabel">Upload CSV for Stops</h5>
                <button type="button" id="close_csv_upload" class="close" data-dismiss="modal" aria-label="Close" onclick="resetTable()">
				<span aria-hidden="true">&times;</span>
			</button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered" id="csvTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Stop Name</th>
                            <th>Landmark</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Rows will be dynamically added here -->
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal" onclick="resetTable()" id="cancel_csv_upload">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadCSVData()" id="upload_csv_btn" style="margin-bottom:3px;">Upload</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
	.stop-container {
		padding: 5px 10px;
		border: 0.1em solid #ccc;
		border-radius: 6px;
		margin: 0px 10px 10px 10px;
	}
	.widthadjust{
		width: 45%;
		margin: auto;
	}
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script type="text/javascript">
	let dataTableInitialized = false;

	function upload_stops_excel(event) {
		const input = event.target;
		const reader = new FileReader();

		reader.onload = function (e) {
			const data = new Uint8Array(e.target.result);
			const workbook = XLSX.read(data, { type: 'array' });

			// Get the first sheet name
			const sheetName = workbook.SheetNames[0];
			const worksheet = workbook.Sheets[sheetName];

			// Convert sheet to 2D array
			const parsedData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

			if (parsedData.length > 0) {
				populateTable(parsedData);
				$('#upload_csv_btn').html('Upload').removeAttr('disabled', 'disabled');
				$('#csvModal').modal('show');

				$('#csvTable').DataTable({
					destroy: true,
					dom: 'lBfrtip',
					ordering: false,
					language: {
						search: '',
						searchPlaceholder: 'Enter Search...'
					},
					buttons: [],
				});
			} else {
				new PNotify({
					title: 'Error',
					text: 'Excel Sheet Cannot Be Empty',
					type: 'error',
				});
			}
		};

		reader.readAsArrayBuffer(input.files[0]);
		input.value = '';
	}

	function parseCSV(text) {
		const cleanedText = text.replace(/^\uFEFF/, '').replace(/\r\n/g, '\n').trim();

		const rows = cleanedText.split('\n').filter(row => row.trim() !== '');

		// Use tab as separator (likely Excel TSV export)
		return rows.map(row => row.split('\t').map(cell => cell.trim()));
	}

	function populateTable(data) {
		const tbody = $('#csvTable tbody');
		tbody.empty();
		data.slice(1).forEach((row, index) => {
			const html = `
				<tr id="row_${index}">
					<td>${index + 1}</td>
					<td contenteditable="false">${row[0]}</td>
					<td contenteditable="false">${row[1]}</td>
					<td>
					<button class="btn btn-sm btn-danger" onclick="deleteRow(${index})">Delete</button>
						<button class="btn btn-sm btn-primary" id="edit_btn" onclick="editRow(${index})">Edit</button>
					</td>
				</tr>
			`;
			tbody.append(html);
		});

		if (dataTableInitialized) {
			$('#csvTable').DataTable().destroy();
		}

		$('#csvTable').DataTable({
			// dom: 'lBfrtip',
			paging: false,
			searching: true,
			ordering: false,
			responsive: true,
			// button:[
			// 	{
			// 		extend: 'excelHtml5',
			// 		text: 'Excel',
			// 		filename: `Excel Stops Uploaded Data`,
			// 		className: 'btn btn-info',
			// 		exportOptions: {
			// 			columns: function (idx, data, node) {
			// 				return idx != 0 && idx != 3;
			// 			}
			// 		},
			// 	}
			// ]
		});

		dataTableInitialized = true;
	}

	function resetTable() {
		$('#upload_csv_btn').html('Upload').removeAttr('disabled', 'disabled');
		$('#csvTable').DataTable().clear().destroy();
		$('#csvTable tbody').empty();
		dataTableInitialized = false;
		$('#csvModal').modal('hide');
		window.location.reload();
	}

	function editRow(index) {
		const row = $(`#row_${index}`);
		row.find('td[contenteditable="false"]').attr('contenteditable', 'true').addClass('editable');
		row.find('button#edit_btn').text('Save').attr('onclick', `saveRow(${index})`).addClass('btn-warning');
	}

	function saveRow(index) {
		const row = $(`#row_${index}`);
		row.find('td[contenteditable="true"]').attr('contenteditable', 'false').removeClass('editable');
		row.find('button#edit_btn').text('Edit').attr('onclick', `editRow(${index})`).removeClass('btn-warning');
	}

	function deleteRow(index) {
		$(`#row_${index}`).remove();
	}

	async function uploadCSVData() {
		$('#upload_csv_btn').html('Please wait...').attr('disabled', 'disabled');
		$('#close_csv_upload').attr('disabled', 'disabled');
		$('#cancel_csv_upload').attr('disabled', 'disabled');
		const rows = $('#csvTable tbody tr');
		const data = [];
		rows.each(function() {
			const cols = $(this).find('td');
			data.push({
				stop_name: cols.eq(1).text(),
				landmark: cols.eq(2).text()
			});
		});

		for (const [index, row] of data.entries()) {
			try {
				await insertRowIntoDB(row);
				$(`#row_${index}`).css({
					'background-color': '#33ff77',
					'color': 'black'
				});
			} catch (error) {
				$(`#row_${index}`).css({
					'background-color': '#ff3333',
					'color': 'black'
				});
			}
		}

		$('#upload_csv_btn').html('Upload');
		$('#close_csv_upload').removeAttr('disabled');
		$('#cancel_csv_upload').removeAttr('disabled');

		// $('#csvModal').modal('hide');

		// setTimeout(function(){
		// 	window.location.reload();
		// }, 500)
	}

	function insertRowIntoDB(row) {
		return new Promise((resolve, reject) => {
			$.ajax({
				url: '<?php echo site_url('transportation/mass_upload_stops');?>',
				type: 'POST',
				data: {stop_name: row['stop_name'], landmark: row['landmark']},
				success: function(response) {
        			var parsedResponse = JSON.parse(response);
                    if (parsedResponse.status == true) {
						resolve();
					} else {
						reject();
					}
				},
				error: function(error) {
					reject(error);
				}
			});
		});
	}

	$(document).ready(function(){
		getReport();
	});

	function getReport() {
		 $(".loading-icon").show();
		 $('#stop_name').val('');
		 $('#landmark').val('');
	     $.ajax({
	        url:'<?php echo site_url('transportation/get_transport_stop_data_ajax') ?>',
	        type:'post',
	        success : function(data){
        	 	let stops = JSON.parse(data);
        	 	$('#txStopList').html(construct_stop_table(stops));

    	 	 	// setTimeout(function(){
					$('#txstop_dataTable').DataTable({
						paging: true, 
						autoWidth: true,
						scrollX: true,
						ordering: false,
						// dom: 'Bfrtip',
						// buttons: [
						// 	'excel', 'pdf'
						// ]
					});
				// }, 500);
    	 	 	$(".loading-icon").hide();
	        }
	    });
	}

	function construct_stop_table(stops) {
		var html = `<table class="table table-bordered" id="txstop_dataTable" style="white-space: nowrap;">
			 	<thead>
			 		<tr>
					 	<th>Action</th>
			 			<th>#</th>
			 			<th>Stop Name</th>
			 			<th>Landmark</th>
			 		</tr>
			 	</thead>
			 	<tbody>`;
			 	for (var i = 0; i < stops.length; i++) {
			 		html +='<tr>';
			 		html +='<td><a onclick="editStop('+stops[i].id+')" class="btn btn-primary btn-sm" data-placement="left" data-toggle="tooltip" data-original-title="Edit stop">Edit</a><a onclick="delete_stop('+stops[i].id+',\''+stops.stop_name+'\')" class="btn btn-danger btn-sm ml-2" data-placement="right" data-toggle="tooltip" data-original-title="Remove stop">Delete</a></td>';
			 		html +='<td>'+(i+1)+'</td>';
			 		html +='<td>'+stops[i].stop_name+'</td>';
			 		html +='<td>'+(stops[i].landmark != '' ? stops[i].landmark : '-' )+'</td>';
			 	}
			 	html +='</tbody>';
				html +='</table>';
				return html;
	}

	$('#submit_tx_stops').on('click',function(){
		var stop_name = $('#stop_name').val();
		var landmark = $('#landmark').val();
		if (stop_name == '') {
			alert('Enter Stop Name');
			return false;
		}
		if (landmark == '') {
			alert('Enter Landmark');
			return false;
		}
		$.ajax({
	        url:'<?php echo site_url('transportation/add_transport_stop_data_ajax') ?>',
	        type:'post',
	        data:{'stop_name':stop_name,'landmark':landmark},
	        success : function(data){
        		getReport();
	        }
	    });
	});
</script>

<script type="text/javascript">

	function editStop(stop_id) {
		$("#edit_stop").modal('hide');
		$("#stop_id").val('');
		$("#edit_stop_name").val('');
		$("#edit_landmark").val('');
		$.ajax({
	        url:'<?php echo site_url('transportation/edit_transport_stop_get_data_ajax') ?>',
	        type:'post',
	        data:{'stop_id':stop_id},
	        success : function(data){
	        	let editstop = JSON.parse(data);
        		$("#edit_stop").modal('show');
				$("#stop_id").val(stop_id);
				$("#edit_stop_name").val(editstop.stop_name);
				$("#edit_landmark").val(editstop.landmark);
	        }
	    });
		
	}

	function delete_stop(stop_id, stop_name) {
		bootbox.confirm({
			title:'Confirm',
		    message: "You are deleting stop: <b>" + stop.stop_name +"</b>, This will delete the student journeys linked to this stop also. Are you sure?",
		    className:'widthadjust',
		    buttons: {
		        confirm: {
		            label: 'Yes',
		            className: 'btn-success'
		        },
		        cancel: {
		            label: 'No',
		            className: 'btn-danger'
		        }
		    },
		    callback: function (result) {
		      if(result) {
		        $.ajax({
			        url:'<?php echo site_url('transportation/delete_stop') ?>',
			        type:'post',
			        data : {'stop_id': stop_id},
			        success : function(data){
			        	if(data == 1) {
			        		$(function(){
					          new PNotify({
					              title: 'Success',
					              text: 'Stop deleted successfully.',
					              type: 'success',
					          });
					        });
				        	location.reload();
			        	} else {
			        		$(function(){
					          new PNotify({
					              title: 'Error',
					              text: 'Something went wrong.',
					              type: 'error',
					          });
					        });
			        	}
			        }
			    });
		      }
		    }
		});
	}
</script>

<style>
	.table{
		margin-bottom: 0px;
	}

	.ellipsis{
		display: none;
	}
</style>