<div class="col-md-12">
    <div class="row" style="margin-top: 6rem;">
        <div class="card panel_new_style">
             <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
                <h3 class="card-title panel_title_new_style">
                    <strong>Apply Leave</strong>
                </h3>
            </div>

            <form enctype="multipart/form-data" autocomplete="off" method="post" id="leave-form" action="<?php echo site_url('student/Student_leave_controller/studentApplyedLeaveStatus'); ?>" data-parsley-validate="" class="form-horizontal" >
                <div class="card-body pt-1">
                    <p ><strong>Notes:</strong><br>1. Upload doctor's certificate for 3 or more consecutive leaves.<br>2. Once the leave record is approved by the school, you cannot delete. In this case, any leave extensions should be filed as a new leave record.</p>
                    <br>
                    <br>
                        <input type="hidden" value="<?php echo $student_id; ?>" name="student_id" id="student_id">
                        <div class="form-group row">
                            <label class="control-label col-md-2" for="request_date">Request Date : </label>
                            <div class="col-md-8">
                              <input type="text" class="form-control" readonly value="<?php echo date('d-m-Y'); ?>">
                            </div>
                            <input type="hidden" value="<?php echo date('d-m-Y'); ?>" class="form-control"  id="requestdateId" name="request_date" >
                        </div>

                        <div class="form-group row">
                            <label class="control-label col-md-2" for="from_date">From Date: <font color="red"> *</font></label>
                            <div class="col-md-8"> 
                                <div class="input-group date" id="start_date_picker"> 
                                    <input placeholder="Enter From Date" required="" type="text"  class="form-control " id="fromdateId" name="from_date" >
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="control-label col-md-2" for="to_date">To Date: <font color="red"> *</font></label>
                            <div class="col-md-8"> 
                                <div class="input-group date" id="end_date_picker"> 
                                    <input placeholder="Enter To Date" required="" type="text" class="form-control " id="todateId" name="to_date">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="control-label col-md-2" for="no_of_days">No. of Days: <font color="red"> *</font></label>
                            <div class="col-md-8"> 
                                <input type="number" class="form-control" required="" placeholder="Number of days" id="noofdaysId" name="noofdays" value="" min="1">
                                <span class="help-block text-muted">Enter the number of leave days barring holidays and weekly off</span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="control-label col-md-2" for="to_date">Upload File:</label>
                            <div class="col-md-8"> 
                                <div class="input-group"> 
                                    <input type="file" class="form-control " id="file_upload" name="file_upload" data-parsley-error-message="Upload Doctor Certificate if  more than 3 days Sick leave !">
                                </div>
                                <small>Upload Doctor Certificate for more than 3 days leave (Only pdf allowed) </small>
                            </div>
                           <!--  <div class="col-md-2" style="color:black;"><small>Allowed pdf<small></div> -->
                        </div>

                        <div class="form-group row">
                            <label class="control-label col-md-2"  for="leave_type">Leave Type: <font color="red">*</font></label>
                            <div class="col-md-8" >
                                <select class="form-control" id="leave_type" name="leave_type">
                                <option value="">Select Leave Type</option>
                                <option value="Sick">Sick</option>
                                <option value="Medical">Medical</option>
                                <option value="Doctor's Appointment">Doctor's Appointment</option>
                                <option value="Travel">Travel</option>
                                <option value="Visa Appointment">Visa Appointment</option>
                                <option value="Family Function">Family Function</option>
                                <option value="Other">Others</option>
                                </select>
                            </div>
                        </div>
                    
                        <div class="form-group row">
                            <label class="control-label col-md-2" for="to_date">Reason:</label>
                            <div class="col-md-8">  
                                <textarea placeholder="Enter Reason" class="form-control" name="reason"></textarea>
                            </div>
                        </div>
                </div>
                <div class="card-footer panel_footer_new">
                    <center>
                         <input type="submit" class="btn btn-primary" style="width: 120px;" onclick="loader()">
                       <a href="<?php echo site_url('student/Student_leave_controller/index'); ?>" style="width: 120px;" class='btn btn-warning mrg'>Cancel</a>
                    </center>
                </div>
               
            </form>
        </div>
    </div>
</div>


<script type="text/javascript">
    function convert($str) {
    var date = new Date($str),
        mnth = ("0" + (date.getMonth()+1)).slice(-2),
        day  = ("0" + date.getDate()).slice(-2);
    return [ date.getFullYear(), mnth, day ].join("-");
}
</script>
<script type="text/javascript">
    $(document).ready(function () {
        var startDate = null;
        var endDate = null;

    $('input[name=sick_other]').change(function(){
        var value = $( 'input[name=sick_other]:checked' ).val();
        var days = $('#noofdaysId').val();
        if(value === 'Sick' && days > 3){
           $("#file_upload").prop("required", true);
        }
        else {
            $("#file_upload").prop("required", false);
        }
    });

    $('#start_date_picker').datepicker({
        format: 'd-m-yyyy',
        startDate: new Date(),
        "autoclose": true
      })
      .on('changeDate', function (selected) {
        startDate = new Date(selected.date.valueOf());
        var date2 = $('#start_date_picker').datepicker('getDate');
        date2.setDate(date2.getDate());
        $('#end_date_picker').datepicker('setDate', date2);
        //sets minDate to dt1 date + 1
        $('#end_date_picker').datepicker('setStartDate', date2);

       if (startDate != null|| endDate !=null){
       var formatedstartdate=convert(startDate);
       var formateendate=convert(endDate);
        myFunc(formatedstartdate, formateendate);
        }
    });
    $('#end_date_picker').datepicker({
        format: 'd-m-yyyy',
        "autoclose": true
    })
    .on('changeDate', function (selected) {
        endDate = new Date(selected.date.valueOf());
    var formatedstartdate=convert(startDate);
    var formateendate=convert(endDate);
    if (startDate != null|| endDate !=null)
        {
        myFunc(formatedstartdate, formateendate);
        } 
    });

    $('#file_upload').on('change', function(){
        if(this.files[0].size < 1000000){
            var file = $(this).val();
            if(file == '') return false;
            var ext = file.substr(file.lastIndexOf('.') + 1).toLowerCase();
            var allowedExt = ['pdf'];
            if(ext.length <= 0){
                alert('Wrong file format, Allowed formats (pdf)');
                $(this).val() = '';
                return false;
            }
            else {
                if(allowedExt.indexOf(ext) === -1){
                    alert('Wrong file format, Allowed formats (pdf)');
                    $(this).val('');
                    return false;
                }
            }
        } else{
            alert("File is too big! Please upload with in 1MB");
            this.value = "";
            return false;
        }
        
    });
});
</script>

 <script type="text/javascript">
    function myFunc(startDate, endDate) {
        if (startDate ==  "1970-01-01" || endDate == "1970-01-01")
            return false;
        $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCount'); ?>', {startDate: startDate, endDate: endDate}, function (data) {
            var leaveholidayCount = data;
            $("#noofdaysId").val(parseInt(leaveholidayCount));
        });
    }
</script> 


<script>

function loader(){
    var $form = $('#leave-form');
    if ($form.parsley().validate()){       
        document.getElementById('loader').style.display="block";
        document.getElementById('myBtn').style.display="none";
        $('#leave-form').submit(); 
    }
}
</script>