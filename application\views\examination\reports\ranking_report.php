<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('examination/Assessments/index');?>">Examination</a></li>
    <li>Ranking Report</li>
</ul>

<hr>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('examination/Assessments/index') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        Ranking Report
                    </h3>
                </div>
                <!-- <div class="col-md-2">
                    <div class="circleButton_noBackColor" style="background-color:#fe970a;float:right;display: none;" id="exportBtn">
                        <a class="control-primary"  onclick="exportToExcel()" data-placement="top" data-toggle="tooltip" title="" data-original-title="Export">
                            <span class="fa fa-file-excel-o backgroundColor_organge" style="font-size:19px"></span>
                        </a>          
                    </div>
                </div> -->
            </div>
        </div>
        <div class="card-body">
                <div class="form-group col-md-2">
                    <label class="control-label" for="class_id">Class</label>
                    <div class="">
                        <select id="class_id" name="class_id" onchange="getData()" class="form-control input-md">
                            <?php 
                                foreach ($classes as $key => $class) {
                                    echo '<option value="'.$class->id.'">'.$class->class_name.'</option>';
                                }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="control-label" for="section_id">Section</label>
                    <div class="">
                        <select id="section_id" name="section_id" class="form-control input-md">
                            <option value="0">All</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="control-label" for="assessment">Assessment</label>
                    <div class="">
                        <select id="assessment" name="assessment" class="form-control input-md" onchange="get_subjects_class_and_assessment_wise()">
                            <option value="0">Select Assessment</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-5">
                    <label class="control-label" for="section_id">Provide Average Percentage Range</label>
                    <div>
                    <div class="col-md-5">
                        <input type="number" name="start" class="form-control" id="lower" placeholder="Average start" value="40">
                    </div>
                    <div class="col-md-2 text-center">
                    <b>To</b>
                    </div>
                    <div class="col-md-5">
                        <input type="number" name="end"id="higher" class="form-control" placeholder="Average end" value="80">
                    </div>
                    </div>
                    
                </div>
                </div>
            <div style="" class="col-md-12">
                <div class="col-md-4">
                    <label class="control-label" for="subject_1">Subject</label>
                    <div class="">
                        <select id="subject_1" name="subject_1" class="form-control input-md" multiple style="height: 110px;">
                            <!-- <option value="0">Select Subject</option> -->
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="control-label" for="groups_id">Group</label>
                    <div class="">
                        <select id="groups_id" name="groups_id" class="form-control input-md" multiple style="height: 110px;">
                            <!-- <option value="0">Select Subject</option> -->
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="control-label" for="electives_id">Elective</label>
                    <div class="">
                        <select id="electives_id" name="electives_id" class="form-control input-md" multiple style="height: 110px;">
                            <!-- <option value="0">Select Subject</option> -->
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <br><br>
                <center>
                    <button id="getReport" onclick="getReport()" type="button" class="btn  btn-primary" style="width: 3in;">Get Report</button>
                </center>
            </div>
            <br><br>
            <div class="" style="">
                <div class="col-md-4" id="graph-area" style="display: none;">
                    <div class="panel panel-warning" style="border-top: none; border-bottom: none;">
                        <div class="panel-heading">
                            <h3 class="panel-title">Summary</h3>
                            <ul class="panel-controls">
                                <li><a href="#" id="collaps"><span class="fa fa-angle-down"></span></a></li>
                            </ul>                                
                        </div>
                        <div class="panel-body" id="collaps-body">
                            <div id="rank-graph" style="height: 200px;"></div>
                        </div>                          
                    </div>
                </div>
            </div>
            <br><br>
            <div class="" id="rank_list" style="margin: 0px;">
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .collaps-toggle {
        display: none;
    }
</style>

<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript">
    $(document).ready(function(){
        get_subjects_class_and_assessment_wise();
        getData();
    });

    $("#collaps").click(function (){
        $("#collaps-body").toggleClass('collaps-toggle');
        $("#collaps>span").toggleClass('fa fa-angle-down');
        $("#collaps>span").toggleClass('fa fa-angle-up');
    });

    function getData(){
        var classId = $("#class_id").val();
        // var subjectType = $('input[name="display_type"]:checked').val();
        $.ajax({
          url: "<?php echo site_url('examination/assessment_reports/getSectionsEntitiesAssessmentsFromClass');?>",
          data: {'classId': classId},
          type: 'post',
          success: function(data) {
            var data = JSON.parse(data);
            fillAssessmentBox(data.assessments);
            fillSectionsBox(data.sections);
            get_subjects_class_and_assessment_wise();
          },
          error: function(err) {
            console.log(err);
          }
        });
    };

    function fillAssessmentBox (assessments) {
        $("#assessment").html('');
        //Fill Assessments box
        if(assessments.length == 0) {
            
        } else {
            $("#assessment").html('');
            var option1 = '';
            for(var i=0; i<assessments.length; i++){
                option1 += '<option value="'+assessments[i].id+'">'+assessments[i].short_name+'</option>';
            }
            $("#assessment").append(option1);
        }
    }

    function fillSectionsBox (sections) {
        $("#section_id").html('');
        var section = '<option value="0">All</option>';
        for(var j=0; j<sections.length; j++){
            section += '<option value="'+sections[j].csId+'">'+sections[j].csName+'</option>';
        }
        $("#section_id").html(section);
    }

    function getReport() {
        $("#exportBtn").hide();
        var class_id = $("#class_id").val();
        var section_id = $("#section_id").val();
        var assessment_id = $("#assessment").val();

        var subject_id = $("#subject_1").val();
        var groups_id = $("#groups_id").val();
        var electives_id = $("#electives_id").val();

        if(!subject_id && !groups_id && !electives_id) {
            return alert('Subjects are not defined');
        }

        var lower = $("#lower").val();
        var higher = $("#higher").val();
        // var loader = '<div id="report-loader" style="display: none; text-align: center;"><img src="<?php //echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;"></div>';
        var loader = '<div style="height: 40vh;width: 100%;" class="d-flex justify-content-center align-items-center"><i style="font-size: 40px;" class="fa fa-spinner fa-spin"></i></div>';
        
        $("#graph-area").hide();
        $("#collaps-body").removeClass('collaps-toggle');
        $("#rank-graph").html('');
        // if(!subject_id || subject_id == '' && subject_id == 'null' && subject_id == 'NULL') {
        //     return alert('Subjects are not defined');
        // }
        if(lower <= 0 || higher <= 0 || higher <= lower) {
            return alert('Lower average % should be less than Higher %');
        }
        
        $("#report-loader").show();
        $("#rank_list").html(loader);
        $.ajax({
          url: "<?php echo site_url('examination/assessment_reports/getRankList');?>",
          data: {'class_id': class_id, 'section_id':section_id, 'assessment_id':assessment_id, 'subject_id': subject_id, groups_id, electives_id},
          type: 'post',
          success: function(data) {
            var data = JSON.parse(data);
            $("#report-loader").hide();
            if(data.length == 0) {
                $("#rank_list").html('<div class="text-center"><h4>Marks not addedd yet.</h4></div>');
            } else {
                $("#exportBtn").show();
                var gData = [];
                gData['average'] = 0;
                gData['above_average'] = 0;
                gData['below_average'] = 0;
                var sl = 1;
                var html = '<table id="ranking-table" class="table table-bordered table-hovered">';
                html += '<thead><tr><th>Sl. No.</th><th>Student</th><th>ClassSection</th><th>Marks</th><th>Total</th><th>Percentage</th><th>Rank</th></tr></thead>';
                html += '<tbody>';
                for(var i in data) {
                    className = 'average';
                    if(data[i].percentage >= higher) {
                        className = 'above_average';
                        gData['above_average']++;
                    } else if(data[i].percentage <= lower) {
                        className = 'below_average';
                        gData['below_average']++
                    } else {
                        gData['average']++;
                    }
                    html += '<tr>';
                    html += '<td>'+(sl++)+'</td>';
                    html += '<td>'+data[i].stdName+'</td>';
                    html += '<td>'+data[i].csName+'</td>';
                    html += '<td>'+data[i].marks+'</td>';
                    html += '<td>'+data[i].total_marks+'</td>';
                    html += '<td class="'+className+'"><strong>'+data[i].percentage+'</strong></td>';
                    html += '<td class="'+className+'"><strong>'+data[i].rank+'</strong></td>';
                    html += '</tr>';
                }
                html += '</tbody></table>';
                $("#rank_list").html(html);


                $('#ranking-table').DataTable( {
                    dom: 'Blfrtip',
                    buttons: [{
                        extend: 'excelHtml5',
                        exportOptions: {
                            columns: ':visible'
                        }
                    }],
                });
                $(".dt-buttons span").text("Export");



                $("#graph-area").show();
                generateGraph(gData);
            }
          },
          error: function(err) {
            $("#report-loader").hide();
            console.log(err);
          }
        });
    }

    function generateGraph(gData) {
        Morris.Donut({
            element: 'rank-graph',
            data: [
                {label: "Above Average", value: gData['above_average']},
                {label: "Average", value: gData['average']},
                {label: "Below Average", value: gData['below_average']}
            ],
            colors: ['#95B75D', '#1caf9a', '#b54a2a']
        });
    }

</script>

<style type="text/css">
    .average {
        color: white;
        background: #1caf9a;
    }
    .above_average {
        color: white;
        background: #95B75D;
    }
    .below_average {
        color: white;
        background: #b54a2a;
    }
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
    function exportToExcel() {
        var class_name = $("#class_id option:selected").text();
        var section_name = $("#section_id option:selected").text();
        var assessment = $("#assessment option:selected").text();
        var name = `${class_name}${section_name}-${assessment}`;
        
        var wb = XLSX.utils.book_new();
        wb.Props = {
              Title: name,
              Subject: "Ranking Report",
              Author: "NextElement",
              CreatedDate: new Date()
        };

        wb.SheetNames.push('Ranking');
        // var ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
        var ws_school = XLSX.utils.table_to_sheet(document.getElementById('ranking-table'));
        wb.Sheets['Ranking'] = ws_school;

        var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
        downloadSample(wbout, name);
    }

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
      
    }

    function downloadSample(wbout, file_name){
        file_name = file_name+'.xlsx';
        saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), file_name);
    }

    function get_subjects_class_and_assessment_wise() {
        var class_id= $("#class_id").val();
        var assessment_id= $("#assessment").val();

        if(class_id && assessment_id) {
            $.ajax({
                url: '<?php echo site_url('examination/assessment_reports/get_subjects_class_and_assessment_wise'); ?>',
                type: "post",
                data: {class_id, assessment_id},
                success(data) {
                    var p_data = JSON.parse(data);
                    let components= p_data.components;
                    let groups= p_data.groups;
                    let electives= p_data.electives;

                    __fill_component_box(components);
                    __fill_groups_box(groups);
                    __fill_electives_box(electives);
                    
                }
            });
        }
    }

    function __fill_component_box(components) {
        if(Object.keys(components)?.length) {
            let options= '';
            for(var v of components) {
                options += `<option value="${v.entity_id}">${v.name}</option>`;
            }
            $("#subject_1").html(options); 
        } else {
           $("#subject_1").html(''); 
        }
    }

    function __fill_groups_box(groups) {
        if(Object.keys(groups)?.length) {
            let options= '';
            for(var v of groups) {
                options += `<option value="${v.entities_ids_str}">${v.name}</option>`;
            }
            $("#groups_id").html(options); 
        } else {
           $("#groups_id").html(''); 
        }
    }

    function __fill_electives_box(electives) {
        if(Object.keys(electives)?.length) {
            let options= '';
            for(var v of electives) {
                options += `<option value="${v.entities_ids_str}">${v.name}</option>`;
            }
            $("#electives_id").html(options); 
        } else {
           $("#electives_id").html(''); 
        }
    }
</script>

<style>
     .buttons-excel {
        background: #007bff;
        border-radius: 15px;
        height: 28px;
        color: #fff;
        width: 5vw;
        border: none;
        float: right;
        /* margin: 0 2vw 0 0; */
    }

   #analysis_table_length {
        padding: 2vh 0 0 0;
        border: none;
   }

   #analysis_table_filter {
    padding: 1vh 0 1vh 0;
        border: none;
   }

    .dataTables_filter {
    margin-top: 10px 10px 5vh 0;
   }

   .dataTables_length {
     border-bottom: none;
     margin: 0 10px -10.7vh 0;
   }
</style>
