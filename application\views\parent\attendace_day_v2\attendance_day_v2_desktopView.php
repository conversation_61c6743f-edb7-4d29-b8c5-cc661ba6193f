<style>
    body {
        font-family: 'Inter', Arial, sans-serif;
        background: #fafbfc;
        margin: 0;
        padding: 0;
    }

    .container {
        max-width: 900px;
        margin: 40px auto;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 32px 40px 40px 40px;
    }

    .attendance-header {
        font-size: 1.6rem;
        font-weight: 600;
        margin-bottom: 16px;
        text-align: center;
    }

    .alert {
        background: #fff7e0;
        color: #b68400;
        border-radius: 8px;
        padding: 16px 32px;
        margin-bottom: 28px;
        font-size: 1.08rem;
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;
        box-sizing: border-box;
        font-family: inherit;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }

    .alert span:first-child {
        color: #e1a100;
        font-size: 1.5em;
        margin-right: 10px;
    }

    .alert span b {
        color: #b68400;
        font-weight: 700;
    }

    .summary {
        display: flex;
        justify-content: space-between;
        align-items: stretch;
        gap: 0;
        margin-bottom: 32px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.03);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .summary-card {
        flex: 1;
        background: #fff;
        border-right: 1px solid #f0f0f0;
        border-radius: 0;
        padding: 24px 0 16px 0;
        text-align: center;
    }

    .summary-card:last-child {
        border-right: none;
    }

    .summary-card .label {
        font-size: 1.05rem;
        color: #888;
        margin-bottom: 10px;
    }

    .summary-card .value {
        font-size: 2rem;
        font-weight: 700;
        letter-spacing: 1px;
    }

    .summary-card .value.attendance { color: #5b6dfa; }
    .summary-card .value.present { color: #2ca66f; }
    .summary-card .value.absent { color: #e14c4c; }
    .summary-card .value.holiday { color: #e1a100; }

    /* Calendar container */
    #calendar {
        background: #fff;
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        border: 1px solid #eee;
    }

    /* Day numbers */
    .fc .fc-daygrid-day-number {
        font-size: 0.95rem;
        padding: 6px;
        color: #333;
    }

    /* Today highlight */
    .fc .fc-day-today {
        background: #f5f7ff !important;
        border: 1px solid #c7d1ff !important;
    }

    .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
        background-color: #4c6ef5;
        color: #fff;
        border-radius: 50%;
        padding: 4px 8px;
        display: inline-block;
    }

    /* Weekends (optional light gray) */
    .fc .fc-day-sat, .fc .fc-day-sun {
        background-color: #fafafa;
    }

    /* Event pills */
    .fc-event {
        border: none !important;
        padding: 2px 12px !important;
        border-radius: 5px !important; /* Rectangle with slightly rounded corners */
        font-size: 0.97rem !important;
        font-weight: 500 !important;
        margin: 2px 0 !important;
        display: inline-flex !important;
        align-items: center;
        justify-content: flex-start;
        white-space: nowrap;
        box-shadow: none !important;
        gap: 6px;
        letter-spacing: 0.01em;
    }

    .fc-event.present {
        background: #308242 !important;
        color: #2ca66f !important;
        border: 1px solid #2ca66f !important;
        font-weight: 600 !important;
        position: relative;
    }
    .fc-event.present .fc-event-title::before {
        content: '\2714'; /* Checkmark */
        color: #2ca66f;
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        display: inline-block;
    }

    .fc-event.absent {
        background: #e14c4c !important;
        color:rgb(224, 180, 180) !important;
        border: 1px solid #f5d2d2 !important;
        font-weight: 600 !important;
        position: relative;
    }
    .fc-event.absent .fc-event-title::before {
        content: '\2716'; /* Cross mark */
        color:rgb(185, 128, 128);
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        display: inline-block;
    }

    .fc-event.holiday {
        background: #e1a100 !important;
        color: #e1a100 !important;
        border: 1px solid #ffe7a0 !important;
        font-weight: 600 !important;
    }
    .fc-event.holiday .fc-event-title::before {
        content: '\2605'; /* Star icon */
        color: #e1a100;
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        display: inline-block;
    }

    .fc-event.weekoff {
        background:#756969 !important;
        color: #888 !important;
        border: 1px solid #e0e0e0 !important;
        font-weight: 600 !important;
        opacity: 0.7;
    }
    .fc-event.weekoff .fc-event-title::before {
        content: '\1F512'; /* Lock icon (optional) */
        color: #888;
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        display: none; /* Hide by default, can be enabled if needed */
    }

    .fc-event.halfday {
        background: #fffbe6 !important;
        color: #b68400 !important;
        border: 1px solid #ffe58f !important;
        font-weight: 600 !important;
    }
    .fc-event.halfday .fc-event-title::before {
        content: 'L';
        color: #b68400;
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        display: inline-block;
        font-weight: bold;
    }

    /* Optional: wrap multiple events per day as rows */
    .fc-daygrid-event-harness {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 2px;
    }

    /* Clean up event text (remove bullet icon if used before) */
    .fc-event .fc-event-title::before {
        /* Overridden above for each type */
    }

    /* Adjust spacing inside each day */
    .fc .fc-daygrid-day-frame {
        padding: 4px;
    }
</style>


<?php 
$present_days= isset($getOverallDetails['present_days']) ? $getOverallDetails['present_days'] : '--';
$absent_days= isset($getOverallDetails['absent_days']) ? $getOverallDetails['absent_days'] : '--';
$holidays= isset($getOverallDetails['holidays']) ? $getOverallDetails['holidays'] : '--';
$total_days= isset($getOverallDetails['total_days']) ? $getOverallDetails['total_days'] : '--';

// Calculate percentage correctly: (present_days / (total_days - holidays)) * 100
if (is_numeric($present_days) && is_numeric($total_days) && is_numeric($holidays) && ($total_days - $holidays) > 0) {
    $percentage = round(($present_days / ($total_days - $holidays)) * 100, 2);
} else {
    $percentage = '--';
}
?>

<div class="container">
    <div class="attendance-header" style="text-align:center; margin-bottom:12px;">Attendance</div>
    <?php if(is_numeric($percentage) && $percentage < 75): ?>
    <div class="alert">
        <span style="font-size:1.3em;">⚠️</span>
        <span><b>Your attendance is below 75%</b> &nbsp; Please improve your attendance</span>
    </div>
    <?php endif; ?>
    <div class="summary">
        <div class="summary-card">
            <div class="label">Overall Attendance</div>
            <div class="value attendance" style="color: #5b6dfa; font-size: 2.1rem;">
                <?php echo is_numeric($percentage) ? $percentage.'%' : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Present Days</div>
            <div class="value present" style="color: #2ca66f; font-size: 2.1rem;">
                <?php echo is_numeric($present_days) ? $present_days : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Absent Days</div>
            <div class="value absent" style="color: #e14c4c; font-size: 2.1rem;">
                <?php echo is_numeric($absent_days) ? $absent_days : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Holidays</div>
            <div class="value holiday" style="color: #e1a100; font-size: 2.1rem;">
                <?php echo is_numeric($holidays) ? $holidays : '--'; ?>
            </div>
        </div>
    </div>
    <div id="calendar"></div>
</div>

<!-- FullCalendar CSS and JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>

<script>
function fetchAttendanceEvents(startDate, endDate) {
    var student_id = <?php echo isset($studentId) ? json_encode($studentId) : 'null'; ?>;
    return fetch('<?php echo base_url("parent_controller/get_attendance_events"); ?>', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            start: startDate,
            end: endDate,
            student_id: student_id
        })
    }).then(response => response.json());
}

document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: ''
        },
        events: function(fetchInfo, successCallback, failureCallback) {
            fetchAttendanceEvents(fetchInfo.startStr, fetchInfo.endStr)
                .then(data => successCallback(data))
                .catch(() => failureCallback([]));
        },
        eventDidMount: function(info) {
            info.el.title = info.event.title;
        }
    });
    calendar.render();
});
</script>

