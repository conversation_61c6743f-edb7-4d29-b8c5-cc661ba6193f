<?php
  defined('BASEPATH') OR exit('No direct script access allowed');          
	class Admission_model extends CI_Model {
        public function __construct() {
            parent::__construct();
            $this->yearId = $this->acad_year->getAcadYearId();
        }

        public function get_admission_pipeline_data($school, $acad_year) {
            //Step 1: Get an array of status vs admissions
            if($school == "itari"){
                $this->db_readonly->select("af.admission_status as reporting_status, count(*) as count")
                    ->from('itari_admissions_forms af')
                    ->where('af.academic_year_applied_for', $acad_year)
                    ->where('af.admission_status !=', 'Draft')
                    ->where('af.admission_status !=', 'pending')
                    ->group_by("af.admission_status");
                $result_adm = $this->db_readonly->get()->result();
            } else {
            $result_adm = $this->db_readonly->select('count(*) as count')
                ->from('admission_forms af')
                ->join('admission_status as', 'af.id = as.af_id')
                ->where('academic_year_applied_for', $acad_year)
                ->where('as.curr_status != "Draft"')
                ->get()->row()->count;
            }
            $result_conv = $this->db_readonly->select('count(af.id) as count')
            ->from('admission_forms af')
            ->join('admission_status as', 'af.id = as.af_id')
            ->join('admission_internal_status_map asm', 'as.curr_status = asm.user_status')
            ->where('academic_year_applied_for', $acad_year)
            ->where('asm.reporting_status', 'convert')
            ->get()->row()->count;
            
            //Step 1: Get an array of status vs enquiries
            $result_lead = $this->db_readonly->select('SUM(CASE WHEN es.reporting_status IS NULL OR es.reporting_status != "invalid" THEN 1 ELSE 0 END) as final_leads_count')
            ->from('enquiry e')
            ->join('enquiry_internal_status_map es', 'e.status = es.user_status', 'left')
            ->where('e.academic_year', $acad_year)
            ->where('es.reporting_status != "invalid"')
            ->get()
            ->row();
            
            $total_leads = 0;
            $total_adm = 0;
            $total_convert = 0;
    
            if ($school == 'itari'){
                foreach($result_adm as $adm){
                    $total_adm += $adm->count;
                    if($adm->reporting_status == 'admit' || $adm->reporting_status == 'Moved To ERP'){
                        $total_convert += $adm->count;
                    }	
                }
            }
            else{
                $total_adm = $result_adm;
                $total_convert = $result_conv;
            }
            $final_result = new stdClass();
            $final_result->{'# Leads'} = isset($result_lead->final_leads_count) ? $result_lead->final_leads_count : 0;
            $final_result->{'# Applications'} = $total_adm;
            $final_result->{'# Converted'} = $total_convert;
            return $final_result;
            // return $overall_status_list;
        }

        public function get_admission_statistics_data($acad_year, $school_code){
            if($school_code == "itari"){
                $result = $this->db_readonly->select("admission_status, count(*) as count")
                ->from('itari_admissions_forms')
                ->where('academic_year_applied_for', $acad_year)
                ->group_by('admission_status')
                ->get()->result();

                $overall_status_list = [
                    ['status' => 'draft'],
                    ['status' => 'admit'],
                    ['status' => 'submitted'],
                    ['status' => 'pending'],
                    ['status' => 'Application Fees Paid'],
                ];

                foreach ($overall_status_list as &$status1) {
                    $status1['total'] = 0;
                }

                $unassigned = 0;
                foreach ($result as $res) {
                    foreach ($overall_status_list as &$os) {
                        if (empty($res->admission_status)) {
                            $unassigned += $res->count;
                        } else {
                            if ($res->admission_status == $os['status']) {
                                $os['total'] += $res->count;
                            }
                        }
                    }
                }
                foreach ($overall_status_list as &$status2) {
                    if ($status2['status'] == 'unassigned')
                        $status2['total'] = $unassigned;
                }
            }
            else{
                $this->db_readonly->select("aism.reporting_status, count(af.id) as count")
                ->from('admission_forms af')
                ->join("admission_status ast", "af.id=ast.af_id")
                ->join('admission_internal_status_map aism', 'aism.user_status=ast.curr_status')
                ->group_by('aism.reporting_status')
                ->where('af.academic_year_applied_for',$acad_year);
                $result = $this->db_readonly->get()->result();

                //Construct the Overall status array
                $overall_status_list = [
                    ['status' => 'draft'],
                    ['status' => 'wip'],
                    ['status' => 'closed'],
                    ['status' => 'convert'],
                    ['status' => 'invalid'],
                    ['status' => 'unassigned']
                ];
        
                foreach ($overall_status_list as &$status1) {
                    $status1['total'] = 0;
                }
        
                $unassigned = 0;
                foreach ($result as $res) {
                    foreach ($overall_status_list as &$os) {
                        if (empty($res->reporting_status)) {
                            $unassigned += $res->count;
                        } else {
                            if ($res->reporting_status == $os['status']) {
                                $os['total'] += $res->count;
                            }
                        }
                    }
                }
                foreach ($overall_status_list as &$status2) {
                    if ($status2['status'] == 'unassigned')
                        $status2['total'] = $unassigned;
                }
            }
        
            $data['overall_statistics'][$acad_year] = $overall_status_list;
            return $data;
        }

        public function target_vs_converted_application($school_code, $acad_year) {
            if($school_code == "itari"){
                $result = $this->db_readonly->select("count(*) as count")
                ->from('itari_admissions_forms')
                ->where('admission_status', 'admit')
                // ->where('YEAR(created_date)','20'.$acad_year)
                ->where('academic_year_applied_for', $acad_year)
                ->get()->row();
            }
            else{
                $result = $this->db_readonly->select("count(af.id) as count")
                ->from('admission_forms af')
                ->join("admission_status ast", "af.id=ast.af_id and ast.curr_status != 'Draft'")
                ->join('admission_internal_status_map aism', 'aism.user_status=ast.curr_status', 'left')
                ->where('aism.reporting_status', 'convert')
                ->where('af.academic_year_applied_for',$acad_year)
                ->get()->row();
            }
            $target_set = $this->settings->getSetting('admissions_target_from_management');
    
            $final_target = 100;
            if (!empty($target_set)) {
                foreach ($target_set as $val) {
                    if (isset($val->acad_year_id) && $val->acad_year_id == $acad_year) {
                        $final_target = $val->target;
                        break;
                    }
                }
            }
    
            $final_result = new stdClass();
            $final_result->target = $final_target;
            $final_result->converted = $result->count;
    
            return $final_result;
        }

        public function admission_application_trend($school_code, $acad_year, $date_type) {
            $to_date = date('Y-m-d');
            $from_date = '';
        
            // Adjust the date range based on the selected date_type
            switch ($date_type) {
                case 'week':
                    $from_date = date('Y-m-d', strtotime('-6 days'));  // last 7 days
                    break;
        
                case 'month':
                    $from_date = date('Y-m-d', strtotime('-1 month'));  // last 1 month
                    break;
        
                case 'year':
                    $from_date = date('Y-m-d', strtotime('-1 year'));  // last 1 year
                    break;
        
                default:
                    // Default to weekly view if date_type is invalid
                    $from_date = date('Y-m-d', strtotime('-6 days'));
                    break;
            }
        
            $to_date_time = strtotime($to_date);
            $from_date_time = strtotime($from_date);
        
            $incoming_admissions_arr = array();
        
            // Iterate by day for 'week' and 'month'
            for ($date_i = $from_date_time; $date_i <= $to_date_time; $date_i += 86400) {  // Iterate by 1 day (86400 seconds)
                $temp_date = date('d-M', $date_i);  // Format as 'Day-Month'
                $incoming_admissions_arr[$temp_date] = array('admission_count' => "0", 'created_on' => $temp_date);
            }
        
            // Fetch the results based on the school code and date type
            if ($school_code == "itari") {
                $this->db_readonly->select("date_format(created_date,'%d-%b') as created_on, count(*) as admission_count")
                    ->from('itari_admissions_forms af')
                    ->group_by("date_format(af.created_date,'%d-%m-%Y')")
                    ->order_by('af.created_date', 'desc')
                    ->where('date_format(af.created_date,"%Y-%m-%d") between "' . $from_date . '" and "' . $to_date . '"')
                    ->where('af.academic_year_applied_for', $acad_year);
                $result = $this->db_readonly->get()->result();
            }
            else {
                $this->db_readonly->select("date_format(created_on,'%d-%b') as created_on, count(*) as admission_count")
                    ->from('admission_forms af')
                    ->group_by("date_format(af.created_on,'%d-%m-%Y')")
                    ->order_by('af.created_on', 'desc')
                    ->where('date_format(af.created_on,"%Y-%m-%d") between "' . $from_date . '" and "' . $to_date . '"')
                    ->where('af.academic_year_applied_for', $acad_year);
                $result = $this->db_readonly->get()->result();
            }
        
            // Map the result to the incoming admissions array
            foreach ($result as $key => $val) {
                if (array_key_exists($val->created_on, $incoming_admissions_arr)) {
                    $incoming_admissions_arr[$val->created_on]['admission_count'] = $val->admission_count;
                }
            }
        
            return $incoming_admissions_arr;
        }        

        public function admission_activity_trend($school_code, $acad_year){
            $to_date =  date('Y-m-d');
            $from_date = date('Y-m-d', strtotime('-6 days'));
            $to_date_time = strtotime($to_date);
            $from_date_time = strtotime($from_date);
    
            $activity_arr = array();
            for ($date_i = $from_date_time; $date_i <= $to_date_time; $date_i += (86400)) {
                $temp_date = date('d-M', $date_i);
                $activity_arr[$temp_date] = array('activity_count' => "0", 'created_on' => $temp_date);
            }
    
            $sql = "select date_format(created_on, '%d-%b') as created_on, count(*) as activity_count from `follow_up` `fu`
            where date_format(fu.created_on,'%Y-%m-%d') between '$from_date' and '$to_date'
            and `follow_up_type` = 'Admission' and source_id in (select id from admission_forms where academic_year_applied_for=$acad_year)
            group by date_format(fu.created_on, '%d-%m-%Y')
            order by `fu`.`created_on` desc";
            $result = $this->db_readonly->query($sql)->result();
    
            foreach ($result as $key => $val) {
                if (array_key_exists($val->created_on, $activity_arr)) {
                    $activity_arr[$val->created_on]['activity_count'] = $val->activity_count;
                }
            }
            return $activity_arr;
        }

        public function admission_statuswise_data($school_code, $acad_year){
            if($school_code == "itari"){
                $sql = "select count(*) as count, admission_status as reporting_status
                        from itari_admissions_forms af
                        where academic_year_applied_for = $acad_year
                        group by admission_status";
                $combined_result = [
                    'user_status_data' => $this->db_readonly->query($sql)->result()
                ];
                return $combined_result;
            }
            else{
                $user_sql = "SELECT COUNT(*) AS count, ass.curr_status AS user_status, aism.process_order
                    FROM admission_forms af
                    JOIN admission_status ass ON af.id = ass.af_id
                    LEFT JOIN admission_internal_status_map aism ON ass.curr_status = aism.user_status
                    WHERE academic_year_applied_for = $acad_year
                    GROUP BY ass.curr_status
                    ORDER BY aism.process_order";

                $reporting_sql = "SELECT COUNT(*) AS count, CASE WHEN aism.reporting_status = 'wip' THEN 'In Progress' WHEN aism.reporting_status = 'Invalid' THEN 'Invalid/Duplicate' ELSE aism.reporting_status END AS reporting_status, aism.process_order
                    FROM admission_forms af
                    JOIN admission_status ass ON af.id = ass.af_id
                    LEFT JOIN admission_internal_status_map aism ON ass.curr_status = aism.user_status
                    WHERE academic_year_applied_for = $acad_year
                    GROUP BY aism.reporting_status
                    ORDER BY aism.process_order";

                $user_result = $this->db_readonly->query($user_sql)->result_array();
                $reporting_result = $this->db_readonly->query($reporting_sql)->result_array();

                $combined_result = [
                    'user_status_data' => $user_result,
                    'reporting_status_data' => $reporting_result
                ];
                return $combined_result;
            }
        }

        public function student_tc_list($acad_year){
            $class_names = "select cm.class_name
                            from class_master cm 
                            join class c on cm.id = c.class_master_id
                            where c.acad_year_id = $acad_year and is_placeholder = 0
                            order by cm.display_order";
            $classes = $this->db_readonly->query($class_names)->result();

            $budget_query = "select cm.class_name, c.academic_budget
                            from class_master cm 
                            join class c on cm.id = c.class_master_id
                            where c.acad_year_id = ($acad_year + 1) and is_placeholder = 0
                            order by cm.display_order";
            $budget = $this->db_readonly->query($budget_query)->result();
    
            $total_students = "SELECT c.class_name, COUNT(*) AS count
                                FROM student_admission sa
                                JOIN student_year sy ON sa.id = sy.student_admission_id AND sy.acad_year_id = $acad_year
                                JOIN class_section cs ON sy.class_section_id = cs.id
                                JOIN class c ON sy.class_id = c.id AND c.acad_year_id = $acad_year
                                WHERE sa.admission_status = '2' AND sy.promotion_status NOT IN ('4', '5', 'JOINED')
                                GROUP BY c.id";
            $total = $this->db_readonly->query($total_students)->result();
    
            $new_students = "select c.class_name, count(*) as count 
                            from admission_status aas
                            join admission_internal_status_map aism on aism.user_status = aas.curr_status
                            join admission_forms af on af.id = aas.af_id
                            join student_year sy on sy.student_admission_id = aas.student_admission_id
                            join class c on c.id = sy.class_id
                            where academic_year_applied_for = ($acad_year + 1) and aism.reporting_status = 'convert' and is_placeholder = 0
                            group by c.id
                            order by c.display_order";
            $new = $this->db_readonly->query($new_students)->result();
    
            $terminate_students = "SELECT c.class_name, COUNT(DISTINCT st.student_id) AS count
                                    FROM student_terminate st
                                    JOIN student_year sy ON sy.student_admission_id = st.student_id
                                    JOIN class c ON c.id = sy.class_id
                                    WHERE st.acad_year_id_applied_for = ($acad_year + 1) AND st.final_approve_status in (1, 2) and c.acad_year_id = ($acad_year) and is_placeholder = 0
                                    GROUP BY c.id
                                    order by c.display_order";
                                    //Manjukiran: 4-Feb-2025: The above query considers only TC applied students.
                                    //We need add a design change where we distinguish between students who have 
                                    //TC approved but continuing this year AND student who have TC approved but
                                    //discontinued this year.
            $terminate = $this->db_readonly->query($terminate_students)->result();
    
            $budget_data = [];
            foreach ($budget as $b) {
                $budget_data[$b->class_name] = $b->academic_budget;
            }

            $merged_data = [];
            // Add all classes to the merged_data array
            foreach ($classes as $class) {
                $class_name = $class->class_name;
                $merged_data[$class_name] = [
                    'class_name' => $class_name,
                    'total_students' => 0,
                    'new_students' => 0,
                    'terminate_students' => 0,
                    'this_year_close' => 0,
                    'next_year_strength' => 0,
                    'total_strength' => $budget_data[$class_name] ?? 0,
                    'vacancy' => 0
                ];
            }
    
            // Populate total students
            foreach ($total as $row) {
                $class_name = $row->class_name;
                if (isset($merged_data[$class_name])) {
                    $merged_data[$class_name]['total_students'] = (int)$row->count;
                }
            }
    
            // Populate new students
            foreach ($new as $row) {
                $class_name = $row->class_name;
                if (isset($merged_data[$class_name])) {
                    $merged_data[$class_name]['new_students'] = (int)$row->count;
                }
            }
    
            // Populate terminated students
            foreach ($terminate as $row) {
                $class_name = $row->class_name;
                if (isset($merged_data[$class_name])) {
                    $merged_data[$class_name]['terminate_students'] = (int)$row->count;
                }
            }
    
            $first_class = true; // Flag to identify the first class
    
            $previous_close = 0; // Initialize to 0 for the first class
            foreach ($classes as $class) {
                $class_name = $class->class_name;
    
                // Ensure the class exists in the merged data
                if (isset($merged_data[$class_name])) {
                    $total_students = (int)$merged_data[$class_name]['total_students'];
                    $terminate_students = (int)$merged_data[$class_name]['terminate_students'];
                    $new_students = (int)$merged_data[$class_name]['new_students'];
                    $total_strength = (int)$merged_data[$class_name]['total_strength'];
    
                    // For the first class, this_year_close is 0
                    if ($previous_close === 0) {
                        $this_year_close = 0; // First class takes its own net value
                    } else {
                        $this_year_close = $previous_close; // Take the previous class's net
                    }
    
                    $merged_data[$class_name]['this_year_close'] = $this_year_close;
    
                    // Next year's strength (current close + new students)
                    $next_year_strength = $this_year_close + $new_students;
                    $merged_data[$class_name]['next_year_strength'] = $next_year_strength;
    
                    // Vacancy (Strength - AY25-26 Strength)
                    $vacancy = $total_strength - $next_year_strength;
                    $merged_data[$class_name]['vacancy'] = max(0, $vacancy);
    
                    // Update previous_close for the next class
                    $previous_close = $total_students - $terminate_students; // Pass net to the next class
                }
            }
    
            // Convert the merged data back to an indexed array (if required)
            $final_array = array_values($merged_data);
    
            $grand_total = [
                'class_name' => 'Grand Total',
                'total_students' => 0,
                'new_students' => 0,
                'terminate_students' => 0,
                'net' => 0,
                'this_year_close' => 0,
                'next_year_strength' => 0,
                'total_strength' => 0,
                'vacancy' => 0
            ];
            
            $last_class_terminate_students = 0;
            foreach ($final_array as $data) {
                $grand_total['total_students'] += $data['total_students'];
                $grand_total['new_students'] += $data['new_students'];
                $grand_total['terminate_students'] += $data['terminate_students'];
                $grand_total['this_year_close'] += $data['this_year_close'];
                $grand_total['next_year_strength'] += $data['next_year_strength'];
                $grand_total['total_strength'] += $data['total_strength'];
                $grand_total['vacancy'] += $data['vacancy'];

                $last_item = end($final_array);
                $last_class_terminate_students = $last_item['total_students'];
            }
            $total_terminate_students = $grand_total['terminate_students'] + $last_class_terminate_students;
            $grand_total['terminate_students'] = "{$grand_total['terminate_students']} + {$last_class_terminate_students} = {$total_terminate_students}";

            $final_array['Grand Total'] = $grand_total;
            return array_values($final_array);
        }

        public function leads_conversion_statistics($school_code, $acad_year){
            $this->db_readonly->select("esm.reporting_status, count(e.id) as count")
			->from('enquiry e')
			->join('enquiry_internal_status_map esm', 'e.status=esm.user_status', 'left')
			->group_by('esm.reporting_status')
			->where('e.academic_year',$acad_year);
        
            $result = $this->db_readonly->get()->result();
        
            //Construct the Overall status array
            $overall_status_list = [
                ['status' => 'wip'],
                ['status' => 'closed'],
                ['status' => 'convert'],
                // ['status' => 'invalid'],
                ['status' => 'unassigned']
            ];

            foreach ($overall_status_list as &$status1) {
                $status1['total'] = 0;
            }

            $unassigned = 0;
            foreach ($result as $res) {
                foreach ($overall_status_list as &$os) {
                    if (empty($res->reporting_status)) {
                        $unassigned += $res->count;
                        break;
                    } else {
                        if ($res->reporting_status == $os['status']) {
                            $os['total'] += $res->count;
                            break;
                        }
                    }
                }
            }
            foreach ($overall_status_list as &$status2) {
                if ($status2['status'] == 'unassigned')
                    $status2['total'] = $unassigned;
            }
            $data['overall_status'][$acad_year] = $overall_status_list;
            return $data;
        }

        public function leads_enquiry_trend($school_code, $acad_year, $date_type){
            $to_date = date('Y-m-d');
            $from_date = '';
        
            // Adjust the date range based on the selected date_type
            switch ($date_type) {
                case 'week':
                    $from_date = date('Y-m-d', strtotime('-6 days'));  // last 7 days
                    break;
        
                case 'month':
                    $from_date = date('Y-m-d', strtotime('-1 month'));  // last 1 month
                    break;
        
                case 'year':
                    $from_date = date('Y-m-d', strtotime('-1 year'));  // last 1 year
                    break;
        
                default:
                    // Default to weekly view if date_type is invalid
                    $from_date = date('Y-m-d', strtotime('-6 days'));
                    break;
            }
        
            $to_date_time = strtotime($to_date);
            $from_date_time = strtotime($from_date);
        
            $incoming_enquiry_arr = array();
        
            // Iterate by day for 'week' and 'month'
            for ($date_i = $from_date_time; $date_i <= $to_date_time; $date_i += 86400) {  // Iterate by 1 day (86400 seconds)
                $temp_date = date('d-M', $date_i);  // Format as 'Day-Month'
                $incoming_enquiry_arr[$temp_date] = array('enquiry_count' => "0", 'created_on' => $temp_date);
            }
    
            $this->db_readonly->select("date_format(created_on,'%d-%b') as created_on, count(*) as enquiry_count")
                ->from('enquiry e')
                ->group_by("date_format(e.created_on,'%d-%m-%Y')")
                ->order_by('e.created_on', 'desc')
                ->where('date_format(e.created_on,"%Y-%m-%d") between "' . $from_date . '" and "' . $to_date . '"')
                ->where('e.academic_year', $acad_year);
            $result = $this->db_readonly->get()->result();
    
            foreach ($result as $key => $val) {
                if (array_key_exists($val->created_on, $incoming_enquiry_arr)) {
                    $incoming_enquiry_arr[$val->created_on]['enquiry_count'] = $val->enquiry_count;
                }
            }
            return $incoming_enquiry_arr;
        }

        public function leads_enquiry_comparision_trend($school_code, $acad_year_array){
            // Create an array of months (January to December)
            $acad_year_array = json_decode($acad_year_array);
            $months = array(
                'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
            );

            // Initialize an empty array to store the enquiry count for each year and month
            $incoming_enquiry_arr = array();

            foreach($acad_year_array as $acad_year){
                // Populate the array with default values for each month, grouped by year
                $incoming_enquiry_arr['20' . $acad_year] = array();
                foreach ($months as $month) {
                    $incoming_enquiry_arr['20' . $acad_year][$month] = array('enquiry_count' => "0", 'month' => $month);
                }

                // SQL query to get enquiry count by month (ignoring the year)
                $this->db_readonly->select("date_format(created_on,'%b') as month, count(*) as enquiry_count")
                    ->from('enquiry e')
                    ->join('enquiry_internal_status_map esm', 'e.status=esm.user_status', 'left')
                    ->group_by("date_format(e.created_on,'%m')")
                    ->order_by('FIELD(month, "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")')
                    ->where('e.academic_year', $acad_year)
                    ->where('esm.reporting_status != "invalid"');

                $result = $this->db_readonly->get()->result();

                // Loop through the result and update the enquiry counts for each month
                foreach ($result as $key => $val) {
                    if (array_key_exists($val->month, $incoming_enquiry_arr['20' . $acad_year])) {
                        $incoming_enquiry_arr['20' . $acad_year][$val->month]['enquiry_count'] = $val->enquiry_count;
                    }
                }
            }
            return $incoming_enquiry_arr;
        }

        public function leads_activity_trend($school_code, $acad_year){
            $to_date =  date('Y-m-d');
            $from_date = date('Y-m-d', strtotime('-6 days'));
            $to_date_time = strtotime($to_date);
            $from_date_time = strtotime($from_date);
    
            $activity_arr = array();
            for ($date_i = $from_date_time; $date_i <= $to_date_time; $date_i += (86400)) {
                $temp_date = date('d-M', $date_i);
                $activity_arr[$temp_date] = array('activity_count' => "0", 'created_on' => $temp_date);
            }
    
            // $sql = "select date_format(created_on, '%d-%b') as created_on, count(*) as activity_count from `follow_up` `fu`
            // where date_format(fu.created_on,'%Y-%m-%d') between '$from_date' and '$to_date'
            // and `follow_up_type` = 'Enquiry' and source_id in (select id from enquiry where academic_year=$acad_year)
            // group by date_format(fu.created_on, '%d-%m-%Y')
            // order by `fu`.`created_on` desc;";
            $sql = "SELECT DATE_FORMAT(fu.created_on, '%d-%b') AS created_on, COUNT(*) AS activity_count 
                    FROM follow_up fu 
                    WHERE DATE(fu.created_on) BETWEEN '$from_date' AND '$to_date' 
                    AND fu.follow_up_type = 'Enquiry' 
                    -- AND fu.source_id IN (SELECT id FROM enquiry WHERE academic_year = $acad_year) 
                    GROUP BY DATE_FORMAT(fu.created_on, '%d-%b') 
                    ORDER BY MIN(fu.created_on) DESC;";
            $result = $this->db_readonly->query($sql)->result();
    
            foreach ($result as $key => $val) {
                if (array_key_exists($val->created_on, $activity_arr)) {
                    $activity_arr[$val->created_on]['activity_count'] = $val->activity_count;
                }
            }
            return $activity_arr;
        }

        public function leads_statuswise_data($acad_year){
            $reporting_sql = "select count(e.id) as count, CASE WHEN es.reporting_status = 'wip' THEN 'In Progress' WHEN es.reporting_status = 'Invalid' THEN 'Invalid/Duplicate' ELSE es.reporting_status END AS reporting_status from enquiry e
                    left join enquiry_internal_status_map es on es.user_status = e.status
                    where e.academic_year = $acad_year
                    group by es.reporting_status
                    order by es.process_order";

            $user_sql = "select count(e.id) as count, es.user_status
                    from enquiry e
                    left join enquiry_internal_status_map es on es.user_status = e.status
                    where e.academic_year = $acad_year
                    group by es.user_status
                    order by es.process_order";

            $user_result = $this->db_readonly->query($user_sql)->result_array();
            $reporting_result = $this->db_readonly->query($reporting_sql)->result_array();

            $combined_result = [
                'user_status_data' => $user_result,
                'reporting_status_data' => $reporting_result
            ];
            return $combined_result;
        }

        public function leads_sourcewise_data($acad_year){
            $source_sql = "SELECT COUNT(e.id) AS count, e.source AS source_status
                FROM enquiry e
                LEFT JOIN enquiry_internal_status_map es ON e.status = es.user_status
                WHERE e.academic_year = $acad_year AND es.reporting_status != 'invalid'
                GROUP BY e.source
                ORDER BY e.source DESC";
            $source_result = $this->db_readonly->query($source_sql)->result_array();

            $conv_sql = "SELECT COUNT(e.id) AS count, e.source AS source_status
                        FROM enquiry e
                        JOIN admission_forms af ON e.id = af.enquiry_id
                        JOIN admission_status ass ON af.id = ass.af_id
                        JOIN admission_internal_status_map asm ON ass.curr_status = asm.user_status
                        WHERE asm.reporting_status = 'convert' AND academic_year_applied_for = $acad_year
                        ORDER BY e.source";
            $conv_result = $this->db_readonly->query($conv_sql)->result_array();

            $zero_sql = "SELECT COUNT(*) AS zero_count
                FROM admission_forms af
                JOIN admission_status ass ON af.id = ass.af_id
                JOIN admission_internal_status_map asm ON ass.curr_status = asm.user_status
                WHERE academic_year_applied_for = $acad_year AND asm.reporting_status = 'convert' and enquiry_id =0";
            $zero_result = $this->db_readonly->query($zero_sql)->row();

            $conv_map = [];
            foreach ($conv_result as $row) {
                $key = strtolower(trim($row['source_status'] ?? ''));
                $conv_map[$key] = $row['count'];
            }
            $final_result = [];
            foreach ($source_result as $row) {
                $source = trim($row['source_status'] ?? '');
                $key = strtolower($source);
                $total = $row['count'];
                $converted = $conv_map[$key] ?? 0;

                $final_result[] = [
                    'source_status' => $source,
                    'count' => $total,
                    'converted_count' => $converted,
                ];
            }
            $final_result[] = [
                'source_status' => 'Direct Admission',
                'count' => 0,
                'converted_count' => $zero_result->zero_count ?? 0,
            ];
            return $final_result;
        }
    }
?>