<?php

use function PHPSTORM_META\type;

	defined('BASEPATH') OR exit('No direct script access allowed');          
	class Msm_model extends CI_Model {
	    public function __construct() {
	    	parent::__construct();
		}

		public function get_school_list($data_size = 'min_data') {
			if ($data_size === 'min_data') {
				$this->db_readonly->select("school_name, school_code, school_domain"); 
			} else {
				$this->db_readonly->select("*"); 
			}
			$this->db_readonly->from('msm_school_list');
			$school_data = $this->db_readonly->get()->result();
			return $school_data;
		}	
		
		public function get_restricted_school_list($data_size = 'min_data') {
			if($this->authorization->isSuperAdmin()){
				if ($data_size === 'min_data') {
					$this->db_readonly->select("school_name, school_code, school_domain"); 
				} else {
					$this->db_readonly->select("*"); 
				}
				$this->db_readonly->from('msm_school_list');
				$school_data = $this->db_readonly->get()->result();
				return $school_data;
			}
			else{
				$logged_in_user_id = $this->authorization->getUserId();
				if ($data_size === 'min_data') {
					$this->db_readonly->select("msl.school_name, msl.school_code, msl.school_domain"); 
				} else {
					$this->db_readonly->select("*"); 
				}
				$this->db_readonly->from('msm_school_list msl');
				$this->db_readonly->join('msm_user_mapping mum',"msl.school_code = mum.school_code");
				$this->db_readonly->where('mum.user_id_in_host',$logged_in_user_id);
				$school_data = $this->db_readonly->get()->result();
				return $school_data;
			}
		}	

		public function get_staff_id_from_userId($userId){
            $sql = "select school_code, staff_id_in_school_code from msm_user_mapping where user_id_in_host = $userId";
			return $this->db_readonly->query($sql)->result();
        }

		public function add_single_school($name,$description,$school_address,$code,$website_address,$erp_url,$domain){

			$school_data = array(
				'school_name'=>$name,
				'description'=>$description,
				'school_address'=>$school_address,
				'erp_address'=>$erp_url,
				'website_address'=>$website_address,
				'school_code'=>$code,
				'school_domain'=>$domain
			);
			return $this->db->insert('msm_school_list',$school_data);   
		}

		public function get_msm_users(){
			$query = $this->db->query("SELECT msl.school_domain, mum.user_id_in_school_code, sm.id, sm.first_name, sm.last_name, sm.contact_number, ps.name AS modules_name, ps.id as module_id, a.stakeholder_id, u.id, u.username, CASE WHEN u.username IN (SELECT user_name FROM msm_user_mapping) THEN mum.school_code ELSE NULL END AS school_code
			FROM staff_master sm
			INNER JOIN roles_staff rs ON sm.id = rs.staff_id
			INNER JOIN roles_privileges_sub rps ON rs.role_id = rps.role_id
			INNER JOIN privileges_sub ps ON rps.privilege_sub_id = ps.id
			INNER JOIN privileges p ON ps.privilege_id = p.id
			INNER JOIN avatar a ON a.stakeholder_id = sm.id AND a.avatar_type = 4
			INNER JOIN users u ON u.id = a.user_id
			LEFT JOIN msm_user_mapping mum ON mum.user_name = u.username
			LEFT JOIN msm_school_list msl on msl.school_code = mum.school_code
			WHERE p.name = 'MSM'");
			$result = $query->result();
			return $result;
		}

		public function add_host_user_mapping($host_school,$user_id, $user_name) {
			$this->db->select("user_id_in_host, user_name, school_code")
				->from("msm_user_mapping")
				->where("user_name",$user_name)
				->where("school_code",$host_school);
			$check = $this->db->get()->row();
			if(empty($check)){
				$query = $this->db->query("SELECT u.id AS user_id, a.id AS avatar_id, sm.id AS staff_id FROM users u, avatar a, staff_master sm WHERE u.username = '$user_name' AND u.id = a.user_id AND a.stakeholder_id = sm.id AND a.avatar_type = 4");
				$result = $query->row();
				$user_id_in_school=$result->user_id;
				$avatar_id =$result->avatar_id;
				$staff_id =$result->staff_id;
				$data = array(
					'user_id_in_host' => $user_id,
					'user_name' => $user_name,
					'school_code' => $host_school,
					'user_id_in_school_code' => $user_id_in_school,
					'avatar_id_in_school_code' => $avatar_id,
					'staff_id_in_school_code' => $staff_id
				);
				return $this->db->insert('msm_user_mapping', $data);
			}
		}

		public function get_guest_user_ids($username) {
			$query = $this->db->query("SELECT u.id AS user_id, a.id AS avatar_id, sm.id AS staff_id FROM users u, avatar a, staff_master sm WHERE u.username = '$username' AND u.id = a.user_id AND a.stakeholder_id = sm.id AND a.avatar_type = 4");
			$result = $query->row();
			return $result;
		}
		
		public function add_guest_user_mapping($user_id,$user_name,$school_code,$user_id_in_school, $avatar_id, $staff_id) {
			$this->db->select("*");
			$this->db->from("msm_user_mapping");
			$this->db->where("user_name",$user_name);
			$this->db->where("school_code",$school_code);
			$check = $this->db->get()->row();
			if(empty($check)){
				$user_id_in_school = intval($user_id_in_school);
				$avatar_id = intval($avatar_id);
				$staff_id = intval($staff_id);
				$data = array(
					'user_id_in_host' => $user_id,
					'user_name' => $user_name,
					'school_code'=> $school_code,
					'user_id_in_school_code' => $user_id_in_school,
					'avatar_id_in_school_code' => $avatar_id,
					'staff_id_in_school_code' => $staff_id
				);
				return $this->db->insert('msm_user_mapping', $data);
			}
			else{
				$user_id_in_school = intval($user_id_in_school);
				$avatar_id = intval($avatar_id);
				$staff_id = intval($staff_id);
				$data = array(
					'user_id_in_school_code' => $user_id_in_school,
					'avatar_id_in_school_code' => $avatar_id,
					'staff_id_in_school_code' => $staff_id
				);
				$this->db->where("user_name",$user_name);
				$this->db->where("school_code",$school_code);
				$this->db->update('msm_user_mapping', $data);
				return 1;
			}
		}

		public function remove_school_permission_for_users($school_code, $user_id){
			$this->db->from("msm_user_mapping");
			$this->db->where("user_id_in_host",$user_id);
			$this->db->where("school_code",$school_code);
			return $this->db->delete();
		}

		public function get_school_list_for_particular_user($user_id){
			$this->db->select('msl.school_code, msl.school_domain')
				->from('msm_school_list msl')
				->where("msl.school_code NOT IN (SELECT mum.school_code FROM msm_user_mapping mum WHERE mum.user_id_in_host = '$user_id')");

			$query = $this->db->get();
			$result = $query->result();
			return $result;
		}

		public function get_staff_image($staff_id){
			$sql = "select picture_url from staff_master sm
					join avatar a on a.stakeholder_id = sm.id 
					where a.avatar_type = 4 and a.stakeholder_id = $staff_id";
			return $this->db_readonly->query($sql)->row()->picture_url;
		}

		/**
		 * Get all ID card orders for the admin console
		 */
		public function get_idcard_admin_orders($filters = []) {
			$this->db_readonly->select("
				id, school_subdomain, order_id, order_name, id_card_type,
				order_status, payment_status, num_of_cards, num_of_approved_cards,
				DATE_FORMAT(estimated_datetime_to_complete_print, '%d-%b-%Y') as estimated_print_date,
				DATE_FORMAT(estimated_datetime_to_complete_delivery, '%d-%b-%Y') as estimated_delivery_date,
				DATE_FORMAT(invoice_added_on, '%d-%b-%Y') as invoice_date,
				invoice_number, invoice_total,
				DATE_FORMAT(print_submitted_on, '%d-%b-%Y') as print_date,
				DATE_FORMAT(payment_completed_on, '%d-%b-%Y') as payment_date,
				DATE_FORMAT(delivery_started_on, '%d-%b-%Y') as delivery_start_date,
				DATE_FORMAT(delivery_completed_on, '%d-%b-%Y') as delivery_complete_date,
				DATE_FORMAT(created_on, '%d-%b-%Y') as created_date,
				msm_school_list_id
			");

			$this->db_readonly->from('idcards_admin_console_order_master');

			// Apply filters if provided
			if (!empty($filters)) {
				if (isset($filters['order_status']) && $filters['order_status'] !== 'all') {
					$this->db_readonly->where('order_status', $filters['order_status']);
				}

				if (isset($filters['payment_status']) && $filters['payment_status'] !== 'all') {
					$this->db_readonly->where('payment_status', $filters['payment_status']);
				}

				if (isset($filters['id_card_type']) && $filters['id_card_type'] !== 'all') {
					$this->db_readonly->where('id_card_type', $filters['id_card_type']);
				}

				if (isset($filters['search']) && !empty($filters['search'])) {
					$this->db_readonly->group_start();
					$this->db_readonly->like('order_id', $filters['search']);
					$this->db_readonly->or_like('order_name', $filters['search']);
					$this->db_readonly->or_like('school_subdomain', $filters['search']);
					$this->db_readonly->group_end();
				}
			}

			$this->db_readonly->order_by('created_on', 'DESC');

			return $this->db_readonly->get()->result();
		}

		/**
		 * Get order details by ID
		 */
		public function get_idcard_admin_order_by_id($id) {
			$this->db_readonly->select("*");
			$this->db_readonly->from('idcards_admin_console_order_master');
			$this->db_readonly->where('order_id', $id);

			return $this->db_readonly->get()->row();
		}

		/**
		 * Get order details by order ID
		 */
		public function get_idcard_admin_order_by_order_id($orderId) {
			$this->db_readonly->select("*");
			$this->db_readonly->from('idcards_admin_console_order_master');
			$this->db_readonly->where('order_id', $orderId);

			return $this->db_readonly->get()->row();
		}

		/**
		 * Upload invoice for an order
		 */
		public function upload_idcard_admin_invoice($id, $data) {
			$updateData = [
				'invoice_added_on' => date('Y-m-d H:i:s'),
				'invoice_number' => $data['invoice_number'],
				'invoice_amount' => $data['invoice_amount'],
				'invoice_cgst' => $data['invoice_cgst'],
				'invoice_sgst' => $data['invoice_sgst'],
				'invoice_total' => $data['invoice_total'],
				'invoice_file_path' => $data['invoice_file_path'],
				'payment_status' => 'invoice_sent',
				'modified_by' => $this->authorization->getAvatarStakeHolderId()
			];

			$this->db->where('id', $id);
			return $this->db->update('idcards_admin_console_order_master', $updateData);
		}

		/**
		 * Mark payment as complete for an order
		 */
		public function mark_idcard_admin_payment_complete($id) {
			$updateData = [
				'payment_status' => 'payment_complete',
				'payment_completed_on' => date('Y-m-d H:i:s'),
				'modified_by' => $this->authorization->getAvatarStakeHolderId()
			];

			$this->db->where('order_id', $id);
			return $this->db->update('idcards_admin_console_order_master', $updateData);
		}

		/**
		 * Send order for printing
		 */
		public function send_idcard_admin_for_printing($id, $data) {
			$updateData = [
				'order_status' => 'In-Printing',
				'print_submitted_on' => date('Y-m-d H:i:s'),
				'print_submitted_by' => $this->authorization->getAvatarStakeHolderId(),
				'print_remarks' => $data['print_remarks'],
				'print_file_path' => isset($data['print_file_path']) ? $data['print_file_path'] : null,
				'estimated_datetime_to_complete_print' => $data['estimated_datetime_to_complete_print'],
				'modified_by' => $this->authorization->getAvatarStakeHolderId()
			];
		
			$this->db->where('id', $id);
			return $this->db->update('idcards_admin_console_order_master', $updateData);
		}
		
		/**
		 * Send order for delivery
		 */
		public function send_idcard_admin_for_delivery($id, $data) {
			$updateData = [
				'order_status' => 'In-Delivery',
				'delivery_started_on' => date('Y-m-d H:i:s'),
				'delivery_remarks' => $data['delivery_remarks'],
				'estimated_datetime_to_complete_delivery' => $data['estimated_datetime_to_complete_delivery'],
				'modified_by' => $this->authorization->getAvatarStakeHolderId()
			];

			$this->db->where('id', $id);
			return $this->db->update('idcards_admin_console_order_master', $updateData);
		}

		/**
		 * Mark delivery as complete
		 */
		public function mark_idcard_admin_delivery_complete($id) {
			$updateData = [
				'order_status' => 'Delivered',
				'delivery_completed_on' => date('Y-m-d H:i:s'),
				'modified_by' => $this->authorization->getAvatarStakeHolderId()
			];

			$this->db->where('id', $id);
			return $this->db->update('idcards_admin_console_order_master', $updateData);
		}

		/**
		 * Get summary counts for dashboard
		 */
		public function get_idcard_admin_summary_counts() {
			// Total ID Cards
			$this->db_readonly->select('count(*) as total_cards');
			$this->db_readonly->from('idcards_admin_console_order_master');
			$row = $this->db_readonly->get()->row();
			$totalCards = isset($row->total_cards) ? $row->total_cards : 0;
		
			// In Review
			$this->db_readonly->select('COUNT(*) as count');
			$this->db_readonly->from('idcards_admin_console_order_master');
			$this->db_readonly->where('order_status', 'In Review');
			$row = $this->db_readonly->get()->row();
			$inReview = isset($row->count) ? $row->count : 0;
		
			// In Printing
			$this->db_readonly->select('COUNT(*) as count');
			$this->db_readonly->from('idcards_admin_console_order_master');
			$this->db_readonly->where_not_in('order_status', ['In Review', 'Delivered']);
			$row = $this->db_readonly->get()->row();
			$inPrinting = isset($row->count) ? $row->count : 0;
		
			// Delivered
			$this->db_readonly->select('COUNT(*) as count');
			$this->db_readonly->from('idcards_admin_console_order_master');
			$this->db_readonly->where('order_status', 'Delivered');
			$row = $this->db_readonly->get()->row();
			$delivered = isset($row->count) ? $row->count : 0;
		
			return (object) [
				'total_cards' => $totalCards,
				'in_review' => $inReview,
				'in_printing' => $inPrinting,
				'delivered' => $delivered
			];
		}

		public function get_school_details($msm_school_list_id){
			$this->db_readonly->select("*");
			$this->db_readonly->from('msm_school_list');
			$this->db_readonly->where('id', $msm_school_list_id);

			return $this->db_readonly->get()->row();
		}

		public function update_idcard_orders() {
			$data = $this->input->post('overall_idcard_order_array');
		
			if (!is_array($data)) {
				return 0;
			}
		
			foreach ($data as $school_data) {
				// Skip if no orders for this school
				if (!isset($school_data[1]) || !is_array($school_data[1])) {
					continue;
				}
		
				// Get all orders for this school
				$orders = $school_data[1];
		
				// Get existing order IDs from the database
				$this->db_readonly->select('order_id');
				$this->db_readonly->from('idcards_admin_console_order_master');
				$existing_orders = $this->db_readonly->get()->result_array();
				$existing_order_ids = array();
				foreach ($existing_orders as $eo) {
					$existing_order_ids[] = $eo['order_id'];
				}
		
				// Process each order
				foreach ($orders as $order) {
					if (!isset($order['order_id'])) {
						continue;
					}
		
					// Skip if order already exists
					if (in_array($order['order_id'], $existing_order_ids)) {
						continue;
					}
		
					// Insert new order
					$insert_data = array(
						'school_subdomain' => isset($order['school_subdomain']) ? $order['school_subdomain'] : '',
						'order_id' => $order['order_id'],
						'order_name' => isset($order['order_name']) ? $order['order_name'] : '',
						'id_card_type' => isset($order['id_card_type']) ? $order['id_card_type'] : '',
						'order_status' => isset($order['order_status']) ? strtolower($order['order_status']) : '',
						'num_of_cards' => isset($order['num_of_cards']) ? $order['num_of_cards'] : 0,
						'created_by' => isset($order['created_by']) ? $order['created_by'] : '',
						'payment_status' => isset($order['payment_status']) ? $order['payment_status'] : '',
						'msm_school_list_id' => isset($order['msm_school_list_id']) ? $order['msm_school_list_id'] : null
					);
		
					$this->db->insert('idcards_admin_console_order_master', $insert_data);
				}
			}
		
			return 1;
		}		
	}
?>