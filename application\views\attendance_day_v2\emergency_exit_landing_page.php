<!-- Add these script references at the top of your file or in the head section -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">Attendance</a></li>
    <li>Emergency Exit</li>
</ul>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            Emergency Exit
                        </h3>
                        <div class="ml-auto">
                            <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/create_emergency_exit');?>" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Create Emergency Exit
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Date Range Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Date Range:</label>
                                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                                    <span></span>
                                    <input type="hidden" id="from_date">
                                    <input type="hidden" id="to_date">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Section:</label>
                                <select id="class_section_id" class="form-control">
                                    <option value="">All Sections</option>
                                    <?php foreach ($class_section as $section): ?>
                                    <option value="<?= $section->sectionID ?>"><?= $section->class_name.$section->section_name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4" style="margin-top:24px;">
                            <button type="button" class="btn btn-primary" id="search_btn">
                                <i class="fa fa-download"></i> Get
                            </button>
                        </div>
                    </div>

                    <!-- Table for Emergency Exit Records -->
                    <div class="table-responsive">
                        <table id="emergency_exit_table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Student Name</th>
                                    <th>Class/Section</th>
                                    <th>Pick-up By</th>
                                    <th>Pick-up DateTime</th>
                                    <th>Remarks</th>
                                    <th>Allowed By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here by DataTables -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewDetailsModal" tabindex="-1" role="dialog" aria-labelledby="viewDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 50%; margin: auto;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewDetailsModalLabel">
                    <i class="fa fa-info-circle"></i> Emergency Exit Details
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="details-container">
                    <div class="detail-item">
                        <label class="detail-label">Student Information</label>
                        <div class="detail-content">
                            <div class="detail-row">
                                <span class="detail-key">Name:</span>
                                <span class="detail-value" id="modal_student_name"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Class/Section:</span>
                                <span class="detail-value" id="modal_class_section"></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">Pick-up Information</label>
                        <div class="detail-content">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="detail-row">
                                        <span class="detail-key">Picked up by:</span>
                                        <span class="detail-value" id="modal_pickup_by"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-key">Date & Time:</span>
                                        <span class="detail-value" id="modal_pickup_datetime"></span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-center">
                                    <div id="photo_container" class="position-relative" style="display: none;">
                                        <div class="photo-loader">
                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                        </div>
                                        <img id="modal_photo" class="img-fluid rounded shadow" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                    <div id="default_photo" class="position-relative" style="display: none;">
                                        <img src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg" class="img-fluid rounded shadow" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">Additional Information</label>
                        <div class="detail-content">
                            <div class="detail-row">
                                <span class="detail-key">Remarks:</span>
                                <span class="detail-value" id="modal_remarks"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Allowed by:</span>
                                <span class="detail-value" id="modal_allowed_by"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modal-header {
    border-radius: 8px 8px 0 0;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 1rem 1.5rem;
}

/* Details Container Styles */
.details-container {
    max-width: 800px;
    margin: 0 auto;
}

.detail-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.detail-content {
    padding: 0.5rem 0;
}

.detail-content .row {
    margin: 0;
}

.detail-content .col-md-6 {
    padding: 0 15px;
}

.detail-row {
    display: flex;
    margin-bottom: 0.75rem;
    line-height: 1.5;
    align-items: flex-start;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-key {
    color: #6c757d;
    font-weight: 600;
    min-width: 120px;
    padding-right: 1rem;
}

.detail-value {
    color: #212529;
    flex: 1;
}

/* Photo Container Styles */
#modal_photo_container {
    margin-bottom: 2rem;
}

#modal_photo {
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

#modal_photo:hover {
    transform: scale(1.02);
}

/* Button Styles */
.btn {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Add these styles to your existing CSS */
.photo-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 5px;
}

#photo_container {
    min-height: 150px;
    min-width: 150px;
    display: inline-block;
    margin-top: 5px;
}

#modal_photo {
    position: relative;
    z-index: 2;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#default_photo img {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    opacity: 0.7;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize date range picker
    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment()
    }, function(start, end) {
        $('#reportrange span').html(start.format('DD-MM-YYYY') + ' to ' + end.format('DD-MM-YYYY'));
        $('#from_date').val(start.format('YYYY-MM-DD'));
        $('#to_date').val(end.format('YYYY-MM-DD'));
    });
    
    // Set initial values
    $('#reportrange span').html(moment().subtract(29, 'days').format('DD-MM-YYYY') + ' to ' + moment().format('DD-MM-YYYY'));
    $('#from_date').val(moment().subtract(29, 'days').format('YYYY-MM-DD'));
    $('#to_date').val(moment().format('YYYY-MM-DD'));
    
    // Initialize DataTable with AJAX source
    var dataTable = $('#emergency_exit_table').DataTable({
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('attendance_day_v2/attendance_day_v2/get_emergency_exit_records'); ?>",
            "type": "POST",
            "data": function(d) {
                d.from_date = $('#from_date').val();
                d.to_date = $('#to_date').val();
                d.class_section_id = $('#class_section_id').val();
            },
            "dataSrc": function(json) {
                // Check if the response is valid and has data
                if (!json || typeof json !== 'object') {
                    console.error('Invalid JSON response:', json);
                    return [];
                }
                
                // If the response is an array, return it directly
                if (Array.isArray(json)) {
                    return json;
                }
                
                // If the response has a data property, return that
                if (json.data && Array.isArray(json.data)) {
                    return json.data;
                }
                
                // Otherwise, return an empty array
                console.error('Unexpected JSON structure:', json);
                return [];
            }
        },
        "columns": [
            { "data": null, "render": function(data, type, row, meta) { return meta.row + 1; } },
            { "data": "student_name", "defaultContent": "" },
            { "data": "class_section", "defaultContent": "" },
            { "data": null, "render": function(data, type, row) { 
                if (!row.pickup_name && !row.emergency_exit_pickup_by) return "";
                return (row.pickup_name || "") + ' (' + (row.emergency_exit_pickup_by || "") + ')'; 
            }},
            { "data": "emergency_exit_time", "defaultContent": "" },
            { "data": "emergency_exit_remarks", "defaultContent": "" },
            { "data": "allowed_by_name", "defaultContent": "" },
            { 
                "data": null,
                "render": function(data, type, row) {
                    return '<button type="button" class="btn btn-info btn-sm view-details" data-id="' + row.emergency_exit_id + '">View Details</button>';
                }
            }
        ],
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "language": {
            "search": "",
            "searchPlaceholder": "Enter Search...",
            "emptyTable": "No records found"
        },
        "dom": 'lBfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: 'Excel',
                className: 'btn btn-info',
                title: 'Emergency Exit Records'
            },
            {
                extend: 'print',
                text: 'Print',
                className: 'btn btn-info',
                title: 'Emergency Exit Records'
            }
        ],
        "error": function(xhr, error, thrown) {
            console.error('DataTables error:', error, thrown);
        },
        "initComplete": function(settings, json) {
            // Move buttons next to search box
            $('.dt-buttons').detach().appendTo('#emergency_exit_table_filter');
            
            // Adjust styling to remove gap
            $('.dt-buttons').css({
                'display': 'inline-block',
                'margin-left': '5px',
                'position': 'relative',
                'top': '0px'
            });
            
            // Adjust the search filter container
            $('#emergency_exit_table_filter').css({
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'flex-end'
            });
            
            // Make sure the input and buttons are properly aligned
            $('#emergency_exit_table_filter input').css({
                'margin-right': '0px'
            });
        }
    });
    
    // Search button click handler
    $('#search_btn').on('click', function() {
        dataTable.ajax.reload();
    });
    
    // Handle View Details button click
    $('#emergency_exit_table').on('click', '.view-details', function() {
        var id = $(this).data('id');
        
        // Reset modal content before showing
        resetModalContent();
        
        // Show loading state
        $('#viewDetailsModal').modal('show');
        
        // Fetch details from backend
        $.ajax({
            url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_emergency_exit_details'); ?>",
            type: "POST",
            data: { id: id },
            dataType: "json",
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    console.log('Response data:', data);
                    
                    // Update individual elements instead of replacing the entire modal body
                    $('#modal_student_name').text(data.student_name || '');
                    $('#modal_class_section').text(data.class_section || '');
                    $('#modal_pickup_by').text((data.pickup_name || '') + ' (' + (data.emergency_exit_pickup_by || '') + ')');
                    $('#modal_pickup_datetime').text(data.emergency_exit_time || '');
                    $('#modal_remarks').text(data.emergency_exit_remarks || 'No remarks provided');
                    $('#modal_allowed_by').text(data.allowed_by_name || '');
                    
                    // Handle photo
                    var photoContainer = $('#photo_container');
                    var photoImg = $('#modal_photo');
                    console.log('Photo URL:', data.photo_url);
                    
                    if (data.photo_url) {
                        $('#photo_container').show();
                        $('#default_photo').hide();
                        
                        // Create a new image object to test loading
                        var img = new Image();
                        img.onload = function() {
                            if ($('#viewDetailsModal').is(':visible')) {
                                $('.photo-loader').hide();
                                $('#modal_photo').attr('src', data.photo_url);
                            }
                        };
                        img.onerror = function() {
                            if ($('#viewDetailsModal').is(':visible')) {
                                console.error('Failed to load image:', data.photo_url);
                                $('#photo_container').hide();
                                $('#default_photo').show();
                            }
                        };
                        img.src = data.photo_url;
                    } else {
                        $('#photo_container').hide();
                        $('#default_photo').show();
                        console.log('No photo URL available, showing default photo');
                    }
                } else {
                    $('.modal-body').html('<div class="alert alert-danger">Failed to load details. Please try again.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                $('.modal-body').html('<div class="alert alert-danger">An error occurred while loading details. Please try again.</div>');
            }
        });
    });
    
    // Function to reset modal content
    function resetModalContent() {
        // Clear all text fields
        $('#modal_student_name').text('');
        $('#modal_class_section').text('');
        $('#modal_pickup_by').text('');
        $('#modal_pickup_datetime').text('');
        $('#modal_remarks').text('');
        $('#modal_allowed_by').text('');
        
        // Reset photo containers
        $('#photo_container').hide();
        $('#default_photo').hide();
        $('#modal_photo').attr('src', '');
        $('.photo-loader').show();
    }

    // Reset modal content when modal is hidden
    $('#viewDetailsModal').on('hidden.bs.modal', function() {
        resetModalContent();
    });
    
    // Error handling for DataTables
    $.fn.dataTable.ext.errMode = 'throw';
});
</script>
