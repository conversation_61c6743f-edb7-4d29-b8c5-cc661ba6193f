<script>
    // Add CSS for clickable rows and preselected rows
    const style = document.createElement('style');
    style.textContent = `
        .clickable-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .clickable-row:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }
        .clickable-row td {
            user-select: none; /* Prevent text selection when clicking */
        }
        .preselected-row {
            border-left: 3px solid #007bff;
            font-weight: 500;
        }
        .popup-checkbox:checked {
            transform: scale(1.2);
        }
        /* Tooltip for progress steps */
        .progress-step[data-tooltip] {
            position: relative;
        }
        .progress-step[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 50%;
            top: 120%;
            transform: translateX(-50%);
            background: #fff;
            color: #333;
            padding: 6px 12px;
            border-radius: 4px;
            white-space: nowrap;
            font-size: 13px;
            z-index: 100;
            opacity: 1;
            pointer-events: none;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
    `;
    document.head.appendChild(style);

    let templates = []; // Will be loaded via AJAX when needed
    let selectedTemplateId = <?= isset($order_details->idcard_template_id) ? (int)$order_details->idcard_template_id : 'null' ?>; // New variable to store integer ID
    // console.log('Initial selectedTemplateId in script:', selectedTemplateId);
    let originalSelectedTemplateId = null; // Renamed from selectedTemplateId
    let orderID = <?=  isset($order_id) && $order_id ? $order_id : 0 ?>;
    let orderData = null; // Store order data
    let stepCompleted = [false, false]; // Track if data insertion is completed for each step
    let currentStep = 0;
    let maxStepReached = 0; // Track the highest step reached

    document.addEventListener("DOMContentLoaded", function () {
    const backButton = document.getElementById("cancel_button");
    const nextButton = document.getElementById("next_button");
    const place_order_button = document.getElementById("place_order_button");
    const steps = document.querySelectorAll(".form-step");
    const progressSteps = document.querySelectorAll(".progress-step");
    const progressBar = document.querySelector(".progress");

    function updateButtons() {
        backButton.style.display = currentStep === 0 ? "none" : "inline-block";
        nextButton.style.display = currentStep === steps.length - 1 ? "none" : "inline-block";
        place_order_button.style.display = currentStep === steps.length - 1 ? "inline-block" : "none";
    }

    function updateProgress() {
        progressSteps.forEach((step, index) => {
            const icon = step.querySelector("span");

            if (index < currentStep) {
                step.classList.add("completed");
                step.classList.remove("progress-step-active");
                icon.classList.remove("fa-circle");
                icon.classList.add("fa-check");
                icon.style.color = "white";
                step.removeAttribute('data-tooltip');
                step.style.cursor = 'pointer';
            } else if (index === currentStep) {
                step.classList.add("progress-step-active");
                step.classList.remove("completed");
                icon.classList.remove("fa-check");
                icon.classList.add("fa-circle");
                icon.style.color = "white";
                step.setAttribute('data-tooltip', 'You are on this step');
            } else if (index <= maxStepReached) {
                // Allow clicking on steps up to maxStepReached
                step.classList.remove("progress-step-active", "completed");
                icon.classList.remove("fa-check");
                icon.classList.add("fa-circle");
                icon.style.color = "#007bff";
                step.setAttribute('data-tooltip', 'You can revisit this step');
                step.style.cursor = 'pointer';
            } else {
                step.classList.remove("progress-step-active", "completed");
                icon.classList.remove("fa-check");
                icon.classList.add("fa-circle");
                icon.style.color = "#ccc";
                step.setAttribute('data-tooltip', 'Cannot be selected');
                step.style.cursor = 'not-allowed';
            }
        });

        // Update progress bar width
        const progressPercent = (currentStep / (progressSteps.length - 1)) * 100;
        progressBar.style.width = `${progressPercent}%`;
    }

    function moveToNextStep() {
        steps[currentStep].classList.remove("form-step-active");
        currentStep++;
        if (currentStep > maxStepReached) {
            maxStepReached = currentStep;
        }
        steps[currentStep].classList.add("form-step-active");
        updateProgress();
        updateButtons();
    }

    function moveToStep(stepIndex) {
        if (stepIndex < currentStep) {
            steps[currentStep].classList.remove("form-step-active");
            currentStep = stepIndex;
            steps[currentStep].classList.add("form-step-active");
            updateProgress();
            updateButtons();
        } else {
            validateAndProceed(stepIndex);
        }
    }

    function validateAndProceed(targetStep) {
        if (currentStep === 0 && !stepCompleted[0]) {
            // console.log(selectedTemplateId);

            $('#orderForm1').parsley().validate();
            if (!$('#orderForm1').parsley().isValid()) return;

            // Get the selected option value
            const selectedValue = document.getElementById("selectOptions").value;
            const idCardFor = document.getElementById("idCardFor").value;

            // Initialize formData with the serialized form
            let formData = $('#orderForm1').serialize() + '&order_id=' + encodeURIComponent(orderID);

            // If staff_subset or student_subset is selected, collect the IDs
            if (selectedValue === "staff_subset" || selectedValue === "student_subset"  || selectedValue === "parent_subset") {
                // Get the selected IDs
                const selectedIds = getSelectedProfileIds(idCardFor);

                // If no IDs are selected, show an error message
                if (selectedIds.length === 0) {
                    Swal.fire({
                        title: 'No Selection!',
                        text: `Please select at least one ${idCardFor.toLowerCase()} to proceed.`,
                        icon: 'warning'
                    });
                    return;
                }

                // Add the selected IDs to the form data
                formData += '&selected_ids=' + encodeURIComponent(JSON.stringify(selectedIds));

                // Add the count of selected IDs
                formData += '&selected_count=' + encodeURIComponent(selectedIds.length);
            }

            // Show loading indicator
            Swal.fire({
                title: 'Processing...',
                html: 'Please wait while we process your request.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit the form data
            $.ajax({
                url: '<?php echo site_url('idcards/Idcards_controller/insertOrderPage1'); ?>',
                type: 'POST',
                data: formData,
                success: function (response) {
                    // Close the loading indicator
                    Swal.close();

                    orderID = $.parseJSON(response);
                    stepCompleted[0] = true; // Mark step 0 as completed
                    navigateToStep(targetStep);
                },
                error: function () {
                    // Close the loading indicator
                    Swal.close();

                    Swal.fire({
                        title: 'Error!',
                        text: 'Something went wrong. Please try again.',
                        icon: 'error'
                    });
                }
            });
        } else if (currentStep === 1) {


            if (selectedTemplateId === null) {
                Swal.fire({ title: 'Error!', text: 'Please select a template to proceed.', icon: 'error' });
                return;
            }

            // Get template name directly from the server
            let templateName = 'Template';

            // Make a synchronous AJAX call to get the template name
            $.ajax({
                url: '<?php echo site_url("idcards/Idcards_controller/get_template_name"); ?>',
                type: 'POST',
                data: { template_id: selectedTemplateId },
                dataType: 'json',
                async: false,
                success: function(response) {
                    if (response.success) {
                        templateName = response.template_name;
                    }
                },
                error: function() {
                    console.error('Failed to fetch template name');
                }
            });

            Swal.fire({
                title: `You have selected ${templateName}`,
                html: "<P>Do you want to continue?",
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                reverseButtons: true,
                }).then((result) => {
                /* Read more about isConfirmed, isDenied below */
                if (result.isConfirmed) {
                    $.ajax({
                        url: '<?php echo site_url('idcards/Idcards_controller/insertTemplateId'); ?>',
                        type: 'POST',
                        data: { selectedTemplateId, orderID },
                        success: function (response) {
                            orderData = $.parseJSON(response);
                            orderData.subtotal = orderData.count_of_entities * orderData.unit_price;
                            orderData.unitprice = orderData.unit_price;
                            orderData.cgst = orderData.subtotal * 6 / 100;
                            orderData.sgst = orderData.subtotal * 6 / 100;
                            orderData.grandtotal = orderData.subtotal + orderData.cgst + orderData.sgst;
                            let html = `
                                <div class="container col-md-12">
                                    <div class="${orderData.id_card_type && orderData.id_card_type.toLowerCase() === 'digital card' ? 'col-md-12' : 'col-md-8'}">
                                        <div class="order-info">
                                            <div class="info-box"><p class="label">Order Date</p><p class="value">${orderData.created_on || '-'}</p></div>
                                            <div class="info-box"><p class="label">ID Card For</p><p class="value"><strong>${orderData.id_card_for || '-'}</strong></p></div>
                                            <div class="info-box"><p class="label">Card Type</p><p class="value"><strong>${orderData.id_card_type || '-'}</strong></p></div>
                                            <div class="info-box"><p class="label">Created By</p><p class="value"><strong>${orderData.created_by || '-'}</strong></p></div>
                                        </div>
                                        <div class="order-details">
                                            <h3>Order Details</h3>
                                            <div class="order-item">
                                                <div class="item-info">
                                                    <p class="item-title"><strong>${orderData.id_card_for || '-'} ID Card ${orderData.id_card_type.toLowerCase() !== 'digital card' ? `(Cost per unit ${orderData.unit_price || '-'})` : ''}</strong></p>
                                                    <p class="quantity">Quantity: ${orderData.count_of_entities.toLocaleString('en-IN') || '-'}</p>
                                                </div>
                                                ${orderData.id_card_type.toLowerCase() !== 'digital card' ? `
                                                <div class="price">
                                                    <p>${orderData.count_of_entities.toLocaleString('en-IN') || '-'} X ₹${orderData.unit_price || '-'}</p>
                                                    <p><strong>₹${orderData.subtotal.toLocaleString('en-IN') || '-'}</strong></p>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                    ${orderData.id_card_type.toLowerCase() !== 'digital card' ? `
                                    <div class="payment-details col-md-4">
                                        <h3>Tentative Payment Detail</h3>
                                        <p class="note">The final payment details will get updated once all ID cards are in the approved stage.</p>
                                        <div class="payment-summary">
                                            <p><span>Sub Total</span><span>₹${orderData.subtotal.toLocaleString('en-IN') || '-'}</span></p>
                                            <p><span>cGST</span><span>₹${orderData.cgst || '-'}</span></p>
                                            <p><span>sGST</span><span>₹${orderData.sgst || '-'}</span></p>
                                            <hr>
                                            <p class="grand-total"><span>Grand Total</span><span>₹${orderData.grandtotal.toLocaleString('en-IN') || '-'}</span></p>
                                        </div>
                                    </div>
                                    ` : ''}
                                </div>`;
                            $("#order-container").html(html);
                            stepCompleted[1] = true; // Mark step 1 as completed
                            navigateToStep(targetStep);
                        },
                        error: function () {
                            Swal.fire({ title: 'Error!', text: 'Something went wrong. Please try again.', icon: 'error' });
                        }
                    });
                }
            });


        } else {
            navigateToStep(targetStep);
        }
    }

    function navigateToStep(stepIndex) {
        steps[currentStep].classList.remove("form-step-active");
        currentStep = stepIndex;
        if (currentStep > maxStepReached) {
            maxStepReached = currentStep;
        }
        steps[currentStep].classList.add("form-step-active");
        updateProgress();
        updateButtons();

        // Always load fresh templates when moving to step 1
        if (currentStep === 1) {
            // Store the current id_card_for value to detect changes
            const currentIdCardFor = document.getElementById("idCardFor") ? document.getElementById("idCardFor").value : '';

            // Reset templates array to force reload
            templates = [];

            // Try to use the loadTemplatesIfNeeded function from the iframe if available
            try {
                // Check if we have an iframe with the template selection page
                const templateFrame = document.querySelector('iframe[src*="createOrderPage2"]');
                if (templateFrame && templateFrame.contentWindow) {
                    // Reset templates in the iframe as well
                    if (templateFrame.contentWindow.templates) {
                        templateFrame.contentWindow.templates = [];
                    }

                    // Reset the templatesLoaded flag in the iframe
                    if (typeof templateFrame.contentWindow.templatesLoaded !== 'undefined') {
                        templateFrame.contentWindow.templatesLoaded = false;
                    }

                    // Call the function in the iframe and pass the current step
                    console.log("Loading fresh templates via iframe");
                    if (templateFrame.contentWindow.loadTemplatesIfNeeded) {
                        templateFrame.contentWindow.loadTemplatesIfNeeded(currentStep, true); // Pass true to force reload
                    }
                } else if (window.loadTemplatesIfNeeded) {
                    // If we're on the same page, use the local function and pass the current step
                    console.log("Loading fresh templates via local function");
                    window.loadTemplatesIfNeeded(currentStep, true); // Pass true to force reload
                } else {
                    // Fallback to the old method if needed
                    console.log("Loading fresh templates via fallback method");
                    loadTemplatesViaAjax(true); // Pass true to force reload
                }
            } catch (e) {
                console.error("Error loading templates:", e);
                // Fallback to the old method if there's an error
                loadTemplatesViaAjax(true); // Pass true to force reload
            }
        }
    }

    // Function to load templates via AJAX
    function loadTemplatesViaAjax(forceReload = false) {
        // Only load templates when on step 1
        if (currentStep !== 1) {
            console.log("Not loading templates because we're not on step 1");
            return;
        }

        // If we already have templates and we're not forcing a reload, skip loading
        if (templates.length > 0 && !forceReload) {
            console.log("Templates already loaded, skipping reload");
            renderTemplates();
            return;
        }

        // Show loading indicator
        const templateContainer = document.getElementById("template-container");
        if (templateContainer) {
            templateContainer.innerHTML = `
                <div class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading templates...</span>
                    </div>
                    <p class="mt-3">Loading templates...</p>
                </div>
            `;
        }

        // Get the id_card_for value from the ID selector
        let idCardFor = '';

        // Try to get it from the current document
        const idCardForElement = document.getElementById("idCardFor");
        if (idCardForElement) {
            idCardFor = idCardForElement.value;
        }

        // If not found, try to get it from URL parameters
        if (!idCardFor) {
            const urlParams = new URLSearchParams(window.location.search);
            idCardFor = urlParams.get('id_card_for') || '';
        }

        // Make AJAX request to get templates
        $.ajax({
            url: '<?php echo site_url('idcards/Idcards_controller/getTemplates'); ?>',
            type: 'GET',
            data: { id_card_for: idCardFor },
            success: function(response) {
                templates = JSON.parse(response);

                // If we have a previously selected template ID, make sure it's still valid
                if (selectedTemplateId !== null) {
                    const templateExists = templates.some(template => parseInt(template.id) === parseInt(selectedTemplateId));
                    if (!templateExists) {
                        selectedTemplateId = null;
                    }
                }

                // Render templates
                renderTemplates();
            },
            error: function() {
                if (templateContainer) {
                    templateContainer.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            <p>Failed to load templates. Please try again.</p>
                            <button class="btn btn-primary mt-2" onclick="loadTemplatesViaAjax()">
                                <i class="fas fa-sync-alt"></i> Retry
                            </button>
                        </div>
                    `;
                }
            }
        });
    }

    nextButton.addEventListener("click", function () {
        validateAndProceed(currentStep + 1); // Use validateAndProceed for next button
    });

    backButton.addEventListener("click", function () {
        if (currentStep > 0) {
            navigateToStep(currentStep - 1); // Use navigateToStep for back button
        }
    });

    progressSteps.forEach((step, index) => {
        step.addEventListener("click", function () {
            if (index <= maxStepReached) {
                moveToStep(index);
            }
        });
    });

    updateProgress();
    updateButtons();
});

    // Function to render templates after they're loaded
    function renderTemplates() {
        const templateContainer = document.getElementById("template-container");
        if (!templateContainer) {
            console.error("Template container not found");
            return;
        }

        if (templates.length === 0) {
            templateContainer.innerHTML = `
                <div class="alert alert-warning" role="alert">
                    <p>No templates available. Please contact support.</p>
                </div>
            `;
            return;
        }

        // Create HTML for templates
        let templatesHtml = '';
        templates.forEach(template => {
            const isSelected = selectedTemplateId !== null && parseInt(template.id) === parseInt(selectedTemplateId);
            templatesHtml += `
                <div class="template-card-wrapper ${isSelected ? 'selected' : ''}" data-template-id="${template.id}">
                    <div class="template-card">
                        <div class="template-image">
                            <img src="${template.image_url || '<?= base_url("assets/img/template-placeholder.png") ?>'}" alt="${template.name}">
                        </div>
                        <div class="template-info">
                            <h4>${template.name}</h4>
                            <p>${template.description || 'No description available'}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        // Set the HTML content
        templateContainer.innerHTML = templatesHtml;

        // Add click event listeners to template cards
        const templateCards = document.querySelectorAll('.template-card-wrapper');
        templateCards.forEach(card => {
            card.addEventListener('click', function() {
                const templateId = parseInt(this.getAttribute('data-template-id'));
                selectTemplate(templateId);
            });
        });
    }

    // Function to select a template
    function selectTemplate(templateId) {
        selectedTemplateId = templateId;

        // Update UI to show selected template
        const templateCards = document.querySelectorAll('.template-card-wrapper');
        templateCards.forEach(card => {
            const cardTemplateId = parseInt(card.getAttribute('data-template-id'));
            if (cardTemplateId === templateId) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });

        // Show template preview if available
        updateTemplatePreview(templateId);
    }

    // Function to update template preview
    function updateTemplatePreview(templateId) {
        const previewContainer = document.getElementById('template-preview-container');
        if (!previewContainer) return;

        // Get template data directly from the server
        let templateData = null;

        // Make a synchronous AJAX call to get the template data
        $.ajax({
            url: '<?php echo site_url("idcards/Idcards_controller/getIdCardTemplate"); ?>',
            type: 'POST',
            data: { order_id: orderID },
            dataType: 'json',
            async: false,
            success: function(response) {
                if (response.success) {
                    templateData = response.template;
                }
            },
            error: function() {
                console.error('Failed to fetch template data');
            }
        });

        if (!templateData) return;

        // Show the preview container
        previewContainer.style.display = 'block';

        // Update preview content
        const previewContent = `
            <h3>${templateData.name}</h3>
            <div class="preview-images">
                <div class="front-preview">
                    <h4>Front</h4>
                    <img src="${templateData.front_image_url || templateData.image_url || '<?= base_url("assets/img/template-placeholder.png") ?>'}" alt="Front">
                </div>
                <div class="back-preview">
                    <h4>Back</h4>
                    <img src="${templateData.back_image_url || templateData.image_url || '<?= base_url("assets/img/template-placeholder.png") ?>'}" alt="Back">
                </div>
            </div>
            <div class="template-description">
                <p>${templateData.description || 'No description available'}</p>
            </div>
        `;

        previewContainer.innerHTML = previewContent;
    }

    function place_order(){

        // Get template name directly from the server
        let templateName = 'Template';

        // Make a synchronous AJAX call to get the template name
        $.ajax({
            url: '<?php echo site_url("idcards/Idcards_controller/get_template_name"); ?>',
            type: 'POST',
            data: { template_id: selectedTemplateId },
            dataType: 'json',
            async: false,
            success: function(response) {
                if (response.success) {
                    templateName = response.template_name;
                }
            },
            error: function() {
                console.error('Failed to fetch template name');
            }
        });

        Swal.fire({
            title: "The order will be placed, and the NextElement team will be notified accordingly.",
            html: `<div class="mb-3">Template: <span class="text-primary font-weight-bold">${templateName}</span></div><div>Do you want to continue?</div>`,
            showCancelButton: true,
            confirmButtonText: "Place Order",
            reverseButtons: true,
            }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo site_url('idcards/Idcards_controller/insertPlaceOrderDetails'); ?>',
                    type: 'POST',
                    data: {orderData},
                    success: function (response) {
                        // console.log(response);
                        Swal.fire("Order Placed to NextElement successfully! ", "", "success").then((result) => {
                            if (result.isConfirmed) {
                                window.location.href = "<?php echo site_url('idcards/Idcards_controller/orderSummaryIDcard/'); ?>" + orderID;
                            }
                        });
                    },
                    error: function () {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong. Please try again.',
                            icon: 'error'
                        });
                    }
                });
                // window.location.href = "<?php // echo site_url('idcards/Idcards_controller/orderSummaryIDcard'); ?>";
                // Swal.fire("Saved!", "", "success");
            }
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        updateSelectionOptions(); // populate options based on initial selection

        // After options are populated, select the appropriate value
        <?php if (isset($order_details->id_card_for_type)) : ?>
            setTimeout(() => {
                document.getElementById("selectOptions").value = "<?= $order_details->id_card_for_type ?>";
                // Call the function to show/hide detailed_profile_type_container based on initial selection
                get_gata_selected_profile_type();
            }, 100); // timeout to make sure options are injected first
        <?php endif; ?>

        // Add event listener to selectOptions dropdown
        document.getElementById("selectOptions").addEventListener("change", function() {
            get_gata_selected_profile_type();
        });

        // Add event listener to idCardFor dropdown to reload templates when it changes
        const idCardForElement = document.getElementById("idCardFor");
        if (idCardForElement) {
            // Store the initial value to detect changes
            let previousIdCardFor = idCardForElement.value;

            idCardForElement.addEventListener("change", function() {
                const newValue = this.value;

                // Only reload if the value has changed
                if (newValue !== previousIdCardFor) {
                    console.log(`idCardFor changed from ${previousIdCardFor} to ${newValue}, will reload templates when on step 1`);
                    previousIdCardFor = newValue;

                    // If we're on step 1, reload templates immediately
                    if (currentStep === 1) {
                        // Reset templates array to force reload
                        templates = [];

                        // Force reload templates
                        setTimeout(() => {
                            loadTemplatesViaAjax(true);
                        }, 100);
                    }
                }
            });
        }
    });

    function updateSelectionOptions() {
        let idCardFor = document.getElementById("idCardFor").value;
        let selectionGroup = document.getElementById("selectionGroup");
        let selectionLabel = document.getElementById("selectionLabel");
        let selectOptions = document.getElementById("selectOptions");

        // Show the selection dropdown
        selectionGroup.style.display = "block";

        // Update label and options based on selection
        if (idCardFor === "Student") {
            selectionLabel.innerHTML = "Select Student <font color='red'>*</font>";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_students">All Students</option>
                <option value="student_subset">Student Subset</option>
            `;
        } else if (idCardFor === "Staff") {
            selectionLabel.innerHTML = "Select Staff <font color='red'>*</font>";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_staff">All Staff</option>
                <option value="staff_subset">Staff Subset</option>
            `;
        } else if (idCardFor === "Parent") {
            selectionLabel.innerHTML = "Select Parent <font color='red'>*</font>";
            selectOptions.innerHTML = `
                <option value="" disabled selected>Select an Option</option>
                <option value="all_parents">All Parents</option>
                <option value="parent_subset">Parent Subset</option>
            `;
        } else {
            selectionGroup.style.display = "none";
            selectOptions.value = ""; // Reset the value
        }

        // **Revalidate Parsley when field is displayed**
        window.Parsley && $(selectOptions).parsley().reset();
    }


    // Function to format selected entities from the database into the format expected by updateSelectedItemsTable
    function formatSelectedEntities(entities, profileType) {
        if (!entities || !Array.isArray(entities)) {
            return [];
        }

        return entities.map((entity, index) => {
            if (profileType === 'Staff') {
                return {
                    index: index,
                    id: entity.id,
                    name: entity.staff_name,
                    employeeId: entity.employee_code,
                    department: entity.department,
                    designation: entity.designation,
                    selected: true
                };
            } else {
                return {
                    index: index,
                    id: entity.id,
                    name: entity.name,
                    admissionNo: entity.admission_no,
                    className: entity.class_section,
                    selected: true
                };
            }
        });
    }

    function get_gata_selected_profile_type(){
            let selectedValue = document.getElementById("selectOptions").value;
            let idCardFor = document.getElementById("idCardFor").value;
            let formContainer = document.getElementById("form_container");
            let detailedContainer = document.getElementById("detailed_profile_type_container");

            if (!formContainer) {
                console.error("Form container not found");
                return;
            }

            if (!detailedContainer) {
                console.error("Detailed container not found");
                return;
            }

            // Show detailed_profile_type_container when staff_subset or student_subset or parent_subset is selected
            if (selectedValue === "staff_subset" || selectedValue === "student_subset" || selectedValue === "parent_subset") {
                // First set up the layout
                formContainer.className = "col-md-4"; // Form takes 1/3 of screen
                detailedContainer.className = "col-md-8"; // Detailed container takes 2/3 of screen
                detailedContainer.style.display = "block";

                // Update form field widths to one per row
                const formFields = document.querySelectorAll("#orderForm1 .form-group");
                formFields.forEach(field => {
                    field.className = field.className.replace("col-md-6", "col-md-12");
                });

                // Check if we have selected entities from an existing order
                const selectedEntities = <?php echo isset($selected_entities) ? json_encode($selected_entities) : 'null'; ?>;
                const orderDetails = <?php echo isset($order_details) ? json_encode($order_details) : 'null'; ?>;

                if (selectedEntities && orderDetails &&
                    (orderDetails.id_card_for_type === selectedValue) &&
                    (orderDetails.id_card_for === idCardFor)) {

                    // Format the selected entities
                    const formattedEntities = formatSelectedEntities(selectedEntities, idCardFor === "Parent" ? "Student" : idCardFor);

                    // Store the selected items globally
                    window.currentSelectedItems = formattedEntities;
                    window.currentProfileType = idCardFor === "Parent" ? "Student" : idCardFor;

                    // Update the table with the selected entities
                    updateSelectedItemsTable(formattedEntities, idCardFor === "Parent" ? "Student" : idCardFor);
                } else {
                    // Create the table structure based on the profile type
                    // For parent_subset, use Student table structure
                    const effectiveProfileType = selectedValue === "parent_subset" ? "Student" : idCardFor;
                    createTableStructure(effectiveProfileType);

                    // Then fetch the data with a small delay to ensure UI updates first
                    setTimeout(() => {
                        // For parent_subset, fetch student data
                        const dataProfileType = selectedValue === "parent_subset" ? "Student" : idCardFor;
                        getDataOfProfiletypes(dataProfileType);
                    }, 100);
                }
            } else {
                // Revert to original layout
                detailedContainer.style.display = "none";

                // Update layout
                formContainer.className = "col-md-12";

                // Update form field widths to two per row
                const formFields = document.querySelectorAll("#orderForm1 .form-group");
                formFields.forEach(field => {
                    field.className = field.className.replace("col-md-12", "col-md-6");
                });

                // Clear any selected data
                window.popupData = {};
            }
    }

    function getDataOfProfiletypes(profile_type){
        // Update the container header based on profile type
        updateContainerHeader(profile_type);

        // Show loading indicator
        const detailedContainer = document.getElementById("detailed_profile_type_container");
        const loadingHtml = `
            <div id="loading-indicator" style="text-align: center; padding: 50px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Loading ${profile_type} data...</p>
            </div>
        `;

        // Find the table container and add loading indicator
        const tableContainer = detailedContainer.querySelector(".table-container");
        if (tableContainer) {
            tableContainer.innerHTML = loadingHtml;
        }

        // Set a timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
            const loadingIndicator = document.getElementById("loading-indicator");
            if (loadingIndicator) {
                loadingIndicator.innerHTML = `
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        The request is taking longer than expected. Please try again.
                    </div>
                    <button class="btn btn-primary mt-3" onclick="retryFetchData('${profile_type}')">
                        <i class="fas fa-sync-alt"></i> Retry
                    </button>
                `;
            }
        }, 15000); // 15 seconds timeout

        $.ajax({
            url: '<?php echo site_url('idcards/idcards_controller/getDataOfProfiletypes') ?>',
            type: 'post',
            data: {profile_type},
            success: function(response) {
                // Clear the timeout
                clearTimeout(timeoutId);

                    const parsed_data = $.parseJSON(response);
                    // console.log("Received data:", parsed_data);

                    // Determine data type based on the first item's structure
                    let dataType = profile_type;
                    if (parsed_data && parsed_data.length > 0) {
                        const firstItem = parsed_data[0];

                        // Check for staff-specific fields
                        if (firstItem.hasOwnProperty('employee_id') ||
                            firstItem.hasOwnProperty('designation') ||
                            firstItem.hasOwnProperty('department')) {
                            dataType = "Staff";
                        }
                        // Check for student-specific fields
                        else if (firstItem.hasOwnProperty('admission_no') ||
                                 firstItem.hasOwnProperty('class')
                                ) {
                            dataType = "Student";
                        }
                    }
                    // console.log(dataType);

                    // Update UI based on actual data type
                    updateContainerHeader(dataType);

                    // Populate table based on data type
                    if (dataType === "Staff") {
                        populateStaffTable(parsed_data);
                    } else {
                        populateStudentTable(parsed_data);
                    }
            },
            error: function(error) {
                // Clear the timeout
                clearTimeout(timeoutId);

                console.error(`Error fetching data:`, error);
                showErrorMessage("Failed to fetch data. Please try again.");
            }
        });
    }

    function retryFetchData(profile_type) {
        getDataOfProfiletypes(profile_type);
    }

    function showErrorMessage(message) {
        const tableContainer = document.querySelector("#detailed_profile_type_container .table-container");
        if (tableContainer) {
            tableContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle"></i>
                    ${message}
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="retryFetchData('${document.getElementById('idCardFor').value}')">
                        <i class="fas fa-sync-alt"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    // Function to update container header and table headers based on profile type
    function updateContainerHeader(profile_type) {
        // Update the container header
        const containerHeader = document.querySelector("#detailed_profile_type_container .header");
        if (containerHeader) {
            // Set the text content
            containerHeader.textContent = profile_type === "Staff" ? "Select Staff" : "Select Students";

            // Add click event listener to prevent layout changes when header is clicked
            if (!containerHeader.hasAttribute("data-listener-added")) {
                containerHeader.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                });
                containerHeader.setAttribute("data-listener-added", "true");
            }
        }

        // Update table headers
        const tableHeaders = document.querySelector("#detailed_profile_type_container thead tr");
        if (tableHeaders) {
            if (profile_type === "Staff") {
                tableHeaders.innerHTML = `
                    <th><input type="checkbox" class="checkbox"></th>
                    <th>Staff Name</th>
                    <th style="white-space: nowrap;">Employee ID</th>
                    <th>Department</th>
                    <th>Designation</th>
                `;
            } else {
                tableHeaders.innerHTML = `
                    <th><input type="checkbox" class="checkbox"></th>
                    <th>Student</th>
                    <th>Admission No.</th>
                    <th>Class</th>
                `;
            }
        }
    }

    function populateStudentTable(data) {
        // console.log("Populating student table:", data);

        // Store the data for future use
        window.popupData = window.popupData || {};
        window.popupData["Student"] = data;

        // For the new flow, we don't automatically populate the table
        // Instead, we wait for the user to click the "Add Students" button

        // Make sure the table structure exists
        let success = true;
        const detailedContainer = document.getElementById("detailed_profile_type_container");
        if (!detailedContainer || !detailedContainer.querySelector('.container-page-1')) {
            // console.log("Creating table structure for Student");
            success = createTableStructure("Student");
        }

        if (!success) {
            console.error("Failed to create table structure");
            showErrorMessage("Failed to create table structure. Please try again.");
        }
    }

    // Function to create the table structure if it doesn't exist
    function createTableStructure(profile_type) {
        // console.log("Creating table structure for", profile_type);

        const detailedContainer = document.getElementById("detailed_profile_type_container");
        if (!detailedContainer) {
            console.error("Detailed container not found");
            return false;
        }

        // Create the initial empty state with "Add" button
        const emptyStateHtml = `
            <div class="container-page-1">
                <div class="header">${profile_type === "Staff" ? "Select Staff" : "Select Students"}</div>
                <div class="empty-state text-center py-5">
                    <img src="<?= base_url("assets/img/no_student_found.svg") ?>" alt="No ${profile_type} Found" class="mb-4" style="max-width: 150px;">
                    <h4>No ${profile_type}s Selected</h4>
                    <p>Start by selecting ${profile_type.toLowerCase()} to view, manage, or take action on.</p>
                    <button id="add-${profile_type.toLowerCase()}-btn" class="btn btn-primary mt-3">
                        Add ${profile_type}s
                    </button>
                </div>
            </div>
        `;

        // Set the HTML content
        detailedContainer.innerHTML = emptyStateHtml;

        // Add event listener to the "Add" button
        const addButton = document.getElementById(`add-${profile_type.toLowerCase()}-btn`);
        if (addButton) {
            addButton.addEventListener('click', function() {
                // Show the selection popup
                showSelectionPopup(profile_type);
            });
        } else {
            console.error(`Add ${profile_type} button not found`);
        }

        // console.log("Empty state created successfully");
        return true;
    }

    // Function to show the selection popup
    function showSelectionPopup(profile_type) {
        // console.log(`Showing ${profile_type} selection popup`);

        // Store the data globally for later use
        window.popupData = window.popupData || {};

        // If we already have data, show the popup immediately
        if (window.popupData[profile_type]) {
            createSelectionPopup(profile_type, window.popupData[profile_type]);
            return;
        }

        // Show loading indicator in a popup
        Swal.fire({
            title: `Loading ${profile_type}...`,
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-3">Please wait while we fetch the ${profile_type.toLowerCase()} data...</p>
                </div>
            `,
            showConfirmButton: false,
            allowOutsideClick: false,
            allowEscapeKey: false
        });

        // Fetch the data
        $.ajax({
            url: '<?php echo site_url('idcards/idcards_controller/getDataOfProfiletypes') ?>',
            type: 'post',
            data: {profile_type},
            success: function(response) {
                const parsed_data = $.parseJSON(response);
                // console.log(`Received ${profile_type} data for popup:`, parsed_data);

                // Store the data for future use
                window.popupData[profile_type] = parsed_data;

                // Close the loading popup
                Swal.close();

                // Create and show the selection popup
                createSelectionPopup(profile_type, parsed_data);
            },
            error: function(error) {
                console.error(`Error fetching ${profile_type} data:`, error);

                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: `Failed to fetch ${profile_type.toLowerCase()} data. Please try again.`,
                    confirmButtonText: 'Retry',
                }).then((result) => {
                    if (result.isConfirmed) {
                        showSelectionPopup(profile_type);
                    }
                });
            }
        });
    }

    // Function to create and show the selection popup
    function createSelectionPopup(profile_type, data) {
        // Prepare table rows for the popup
        let tableRows = '';
        let tableHeaders = '';

        // Extract filter options
        let filterOptions = [];

        // Get currently selected items (if any)
        const currentSelectedItems = window.currentSelectedItems || [];
        let selectedCount = currentSelectedItems.length;

        // Function to check if an item is already selected
        const isItemSelected = (item) => {
            if (profile_type === "Staff") {
                return currentSelectedItems.some(selected =>
                    selected.employeeId === item.employee_code &&
                    selected.name === item.name
                );
            } else {
                return currentSelectedItems.some(selected =>
                    selected.admissionNo === item.admission_no &&
                    selected.name === item.std_name
                );
            }
        };

        if (data.length === 0) {
            tableRows = `
                <tr>
                    <td colspan="5" class="text-center">No data present in the table</td>
                </tr>
            `;
        } else if (profile_type === "Staff") {
            // Extract unique departments for staff filter
            const departments = [...new Set(data.map(staff => staff.department || 'N/A'))].sort();

            tableHeaders = `
                <th><input type="checkbox" class="select-all-popup"></th>
                <th>Name</th>
                <th style="white-space: nowrap;">Employee ID</th>
                <th>Department</th>
                <th>Designation</th>
            `;

            data.forEach(staff => {
                const isSelected = isItemSelected(staff);

                tableRows += `
                    <tr data-department="${staff.department || 'N/A'}" class="clickable-row">
                        <td><input type="checkbox" class="popup-checkbox" ${isSelected ? 'checked' : ''}></td>
                        <td>${staff.name || 'N/A'}</td>
                        <td>${staff.employee_code || 'N/A'}</td>
                        <td>${staff.department || 'N/A'}</td>
                        <td>${staff.designation || 'N/A'}</td>
                    </tr>
                `;
            });

            // Create department filter options
            let departmentOptions = '<option value="">All Departments</option>';
            departments.forEach(dept => {
                departmentOptions += `<option value="${dept}">${dept}</option>`;
            });

            filterOptions = [
                {
                    id: 'department-filter',
                    label: 'Department',
                    options: departmentOptions,
                    filterAttribute: 'department'
                }
            ];

        } else {
            // Extract unique classes for student filter and sort them
            const classes = [...new Set(data.map(student => {
                // Extract just the class part (without section) for filtering
                const classMatch = (student.class_section || '').match(/^([0-9A-Za-z]+)/);
                return classMatch ? classMatch[0] : 'N/A';
            }))].sort((a, b) => {
                // Sort numerically if possible
                const numA = parseInt(a);
                const numB = parseInt(b);
                if (!isNaN(numA) && !isNaN(numB)) {
                    return numA - numB;
                }
                return a.localeCompare(b);
            });

            tableHeaders = `
                <th><input type="checkbox" class="select-all-popup"></th>
                <th>Name</th>
                <th>Admission No.</th>
                <th>Grade</th>
            `;

            data.forEach(student => {
                // Extract just the class part for data attribute
                const classMatch = (student.class_section || '').match(/^([0-9A-Za-z]+)/);
                const classValue = classMatch ? classMatch[0] : 'N/A';
                const isSelected = isItemSelected(student);

                tableRows += `
                    <tr data-class="${classValue}" class="clickable-row">
                        <td><input type="checkbox" class="popup-checkbox" ${isSelected ? 'checked' : ''}></td>
                        <td>${student.std_name || 'N/A'}</td>
                        <td>${student.admission_no || 'N/A'}</td>
                        <td>${student.class_section || 'N/A'}</td>
                    </tr>
                `;
            });

            // Create class filter options
            let classOptions = '<option value="">All Classes</option>';
            classes.forEach(cls => {
                classOptions += `<option value="${cls}">${cls}</option>`;
            });

            filterOptions = [
                {
                    id: 'class-filter',
                    label: 'Class',
                    options: classOptions,
                    filterAttribute: 'class'
                }
            ];
        }

        // Create filter HTML
        let filtersHtml = '';
        filterOptions.forEach(filter => {
            filtersHtml += `
                <div class="form-group popup-form-group mr-2">
                    <label for="${filter.id}" class="popup-filter-label mr-2">${filter.label}:</label>
                    <select id="${filter.id}" class="form-control popup-filter-select" data-filter-attribute="${filter.filterAttribute}">
                        ${filter.options}
                    </select>
                </div>
            `;
        });

        // Create the popup content
        const popupContent = `
            <div class="selection-popup-container">
                <div class="search-container mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="popup-search" class="form-control" placeholder="Search ${profile_type}...">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex">
                                ${filtersHtml}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <p id="popup-count" class="mb-0">${data.length} ${profile_type}</p>
                    <p id="popup-selected-count" class="mb-0 text-primary">
                        <strong>${selectedCount}</strong> ${profile_type}s Selected
                    </p>
                </div>
                <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                ${tableHeaders}
                            </tr>
                        </thead>
                        <tbody id="popup-table-body">
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        // Show the popup
        Swal.fire({
            title: `Select ${profile_type}s`,
            html: popupContent,
            width: '800px',
            showCancelButton: true,
            confirmButtonText: 'Add',
            cancelButtonText: 'Close',
            customClass: {
                container: 'selection-popup-swal'
            },
            didOpen: () => {
                // Add event listener to the select all checkbox
                const selectAllCheckbox = document.querySelector('.select-all-popup');
                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', function() {
                        const isChecked = this.checked;
                        // Only select visible checkboxes (respecting filters)
                        const checkboxes = document.querySelectorAll('#popup-table-body tr:not([style*="display: none"]) .popup-checkbox');
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = isChecked;
                        });

                        // Update the selected count
                        updatePopupSelectedCount();
                    });
                }

                // Function to update the selected count in the popup
                function updatePopupSelectedCount() {
                    const selectedCheckboxes = document.querySelectorAll('.popup-checkbox:checked');
                    const selectedCountElement = document.getElementById('popup-selected-count');
                    const confirmButton = Swal.getConfirmButton();

                    if (selectedCountElement) {
                        selectedCountElement.innerHTML = `<strong>${selectedCheckboxes.length}</strong> ${profile_type}s Selected`;
                    }
                    // Enable/Disable the Add button
                    if (confirmButton) {
                        confirmButton.disabled = selectedCheckboxes.length === 0;
                    }
                }

                // Function to apply all filters and search
                function applyFilters() {
                    const searchTerm = document.getElementById('popup-search')?.value.toLowerCase() || '';
                    const rows = document.querySelectorAll('#popup-table-body tr');
                    let visibleCount = 0;

                    // Get all active filters
                    const activeFilters = {};
                    document.querySelectorAll('.popup-filter-select').forEach(select => {
                        const attribute = select.getAttribute('data-filter-attribute');
                        const value = select.value;
                        if (value) {
                            activeFilters[attribute] = value;
                        }
                    });

                    // Apply filters to each row
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        let showRow = text.includes(searchTerm);

                        // Check each active filter
                        Object.keys(activeFilters).forEach(attribute => {
                            const filterValue = activeFilters[attribute];
                            const rowValue = row.getAttribute(`data-${attribute}`);
                            if (filterValue && rowValue !== filterValue) {
                                showRow = false;
                            }
                        });

                        // Show or hide the row
                        if (showRow) {
                            row.style.display = '';
                            visibleCount++;
                        } else {
                            row.style.display = 'none';
                        }
                    });

                    // Update the count
                    const countElement = document.getElementById('popup-count');
                    if (countElement) {
                        countElement.textContent = `${visibleCount} ${profile_type}`;
                    }
                }

                // Add event listener to the search input
                const searchInput = document.getElementById('popup-search');
                if (searchInput) {
                    searchInput.addEventListener('input', applyFilters);
                }

                // Add event listeners to filter selects
                document.querySelectorAll('.popup-filter-select').forEach(select => {
                    select.addEventListener('change', applyFilters);
                });

                // Add event listeners to checkboxes to update the selected count
                document.querySelectorAll('.popup-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', updatePopupSelectedCount);
                });

                // Add event listeners to rows for toggling checkboxes
                document.querySelectorAll('.clickable-row').forEach(row => {
                    row.addEventListener('click', function(e) {
                        // Don't toggle if clicking on the checkbox itself (let the checkbox handle that)
                        if (e.target.type !== 'checkbox') {
                            // Find the checkbox in this row
                            const checkbox = this.querySelector('.popup-checkbox');
                            if (checkbox) {
                                // Toggle the checkbox
                                checkbox.checked = !checkbox.checked;
                                // Trigger the change event to update counts
                                checkbox.dispatchEvent(new Event('change'));
                            }
                        }
                    });
                });

                // Update the initial selected count
                updatePopupSelectedCount();

                // Check if there are any pre-selected items
                const hasPreselectedItems = document.querySelectorAll('.popup-checkbox:checked').length > 0;

                // If there are pre-selected items, show all items without filtering initially
                if (hasPreselectedItems) {
                    // Reset any filters to show all items
                    document.querySelectorAll('.popup-filter-select').forEach(select => {
                        select.selectedIndex = 0; // Select the "All" option
                    });

                    // Apply filters (which will show all items since no filters are selected)
                    applyFilters();

                    // Highlight pre-selected rows for better visibility
                    const preselectedCheckboxes = document.querySelectorAll('.popup-checkbox:checked');
                    preselectedCheckboxes.forEach(checkbox => {
                        const row = checkbox.closest('tr');
                        if (row) {
                            row.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                            // Add a visual indicator to make it more obvious
                            row.classList.add('preselected-row');
                        }
                    });

                    // Scroll to the first pre-selected item after a short delay
                    if (preselectedCheckboxes.length > 0) {
                        setTimeout(() => {
                            const firstSelectedRow = preselectedCheckboxes[0].closest('tr');
                            if (firstSelectedRow) {
                                const tableContainer = document.querySelector('.table-container');
                                if (tableContainer) {
                                    tableContainer.scrollTop = firstSelectedRow.offsetTop - tableContainer.offsetTop - 50;
                                }
                            }
                        }, 300);
                    }
                }
                // Otherwise, set initial filter for students (first class in ascending order)
                else if (profile_type === "Student") {
                    const classFilter = document.getElementById('class-filter');
                    if (classFilter && classFilter.options.length > 1) {
                        // Select the first class option (index 1, after "All Classes")
                        classFilter.selectedIndex = 1;
                        // Apply the filter
                        applyFilters();
                    }
                }
                updatePopupSelectedCount();
            },
            didRender: () => {
                // Ensure the popup is centered and responsive
                const popup = document.querySelector('.swal2-popup');
                if (popup) {
                    popup.style.minHeight = '500px'; // Set minimum height to 50% of the viewport height
                    popup.style.display = 'flex';
                    popup.style.flexDirection = 'column';
                    popup.style.justifyContent = 'center';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Get newly selected items
                const newlySelectedItems = [];
                const checkboxes = document.querySelectorAll('.popup-checkbox:checked');

                // Find the original data items for the selected rows
                checkboxes.forEach((checkbox) => {
                    const row = checkbox.closest('tr');
                    const rowIndex = Array.from(row.parentNode.children).indexOf(row);
                    const originalData = window.popupData[profile_type][rowIndex];

                    // Create item with database ID and display data
                    const item = {
                        index: rowIndex,
                        selected: true
                    };

                    // Get data based on profile type
                    if (profile_type === "Staff") {
                        item.name = row.cells[1].textContent;
                        item.employeeId = row.cells[2].textContent;
                        item.department = row.cells[3].textContent;
                        item.designation = row.cells[4].textContent;
                        // Store the database ID (sm.id)
                        item.id = originalData.id || null;
                    } else {
                        item.name = row.cells[1].textContent;
                        item.admissionNo = row.cells[2].textContent;
                        item.className = row.cells[3].textContent;
                        // Store the database ID (std.id)
                        item.id = originalData.id || null;
                    }

                    newlySelectedItems.push(item);
                });

                // Get existing selected items
                const existingItems = window.currentSelectedItems || [];

                // Merge existing and newly selected items, avoiding duplicates
                const mergedItems = [...existingItems];

                newlySelectedItems.forEach(newItem => {
                    // Check if this item already exists in the merged items
                    const isDuplicate = mergedItems.some(existingItem => {
                        if (profile_type === "Staff") {
                            return existingItem.employeeId === newItem.employeeId &&
                                   existingItem.name === newItem.name;
                        } else {
                            return existingItem.admissionNo === newItem.admissionNo &&
                                   existingItem.name === newItem.name;
                        }
                    });

                    // Only add if it's not a duplicate
                    if (!isDuplicate) {
                        mergedItems.push(newItem);
                    }
                });

                // Update the main table with merged items
                updateSelectedItemsTable(mergedItems, profile_type);
            }
        });
    }

    // Function to update the main table with selected items
    function updateSelectedItemsTable(selectedItems, profile_type) {
        // console.log(`Updating ${profile_type} table with selected items:`, selectedItems);

        // If no items selected, show empty state
        if (!selectedItems || selectedItems.length === 0) {
            console.warn(`No ${profile_type} selected`);
            createTableStructure(profile_type);
            return;
        }

        // Get the detailed container
        const detailedContainer = document.getElementById("detailed_profile_type_container");
        if (!detailedContainer) {
            console.error("Detailed container not found");
            return;
        }

        // Store the selected items in a global variable for reference
        window.currentSelectedItems = selectedItems;
        window.currentProfileType = profile_type;

        // Create table rows for selected items
        let tableRows = '';

        if (profile_type === "Staff") {
            selectedItems.forEach((staff, index) => {
                tableRows += `
                    <tr data-index="${index}">
                        <td><span class="font-weight-medium">${staff.name}</span></td>
                        <td style="text-align: center;"><span class="badge-light">${staff.employeeId}</span></td>
                        <td>${staff.department || 'N/A'}</td>
                        <td>${staff.designation || 'N/A'}</td>
                        <td style="text-align: center;">
                            <div class="action-buttons">
                                <button class="remove-item-btn btn btn-danger" title="Remove">
                                    Remove
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        } else {
            selectedItems.forEach((student, index) => {
                tableRows += `
                    <tr data-index="${index}">
                        <td><span class="font-weight-medium">${student.name}</span></td>
                        <td style="text-align: center;"><span class="badge-light">${student.admissionNo}</span></td>
                        <td>${student.className || 'N/A'}</td>
                        <td style="text-align: center;">
                            <div class="action-buttons">
                                <button class="remove-item-btn btn btn-danger" title="Remove">
                                    Remove
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        // Create the selected items display HTML
        const selectedItemsHtml = `
            <div class="container-page-1">
                <div class="header">${profile_type === "Staff" ? "Selected Staff" : "Selected Students"}</div>
                <div class="selected-items-container">
                    <div class="header-actions mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="selected-count">
                                <span >
                                    ${selectedItems.length} ${profile_type} Selected
                                </span>
                            </div>
                            <div class="action-buttons">
                                <div class="search-filter-container ml-3">
                                    <input type="text" id="searchFilter" class="form-control" placeholder="Search ${profile_type}...">
                                </div>
                                <button id="add-more-${profile_type.toLowerCase()}-btn" class="btn btn-primary">
                                     Add More ${profile_type}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-container scroller">
                        <table id="profile-data-table" class="table table-hover">
                            <thead>
                                <tr>
                                    <th>${profile_type === "Staff" ? "Name" : "Name"}</th>
                                    <th class="text-center" style="white-space: nowrap;">${profile_type === "Staff" ? "Employee ID" : "Admission No."}</th>
                                    <th>${profile_type === "Staff" ? "Department" : "Grade"}</th>
                                    ${profile_type === "Staff" ? "<th>Designation</th>" : ""}
                                    <th class="text-center" style="width: 80px;">Action</th>
                                </tr>
                            </thead>
                            <tbody id="profile-data-tbody">
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Replace the entire content of the detailed container
        detailedContainer.innerHTML = selectedItemsHtml;

        // Add event listener to the "Add More" button
        const addMoreButton = document.getElementById(`add-more-${profile_type.toLowerCase()}-btn`);
        if (addMoreButton) {
            addMoreButton.addEventListener('click', function() {
                showSelectionPopup(profile_type);
            });
        }

        // Add event listener to the search filter input
        const searchFilterInput = document.getElementById("searchFilter");
        if (searchFilterInput) {
            searchFilterInput.addEventListener("input", function () {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll("#profile-data-table tbody tr");

                rows.forEach(row => {
                    const rowText = row.textContent.toLowerCase();
                    row.style.display = rowText.includes(searchTerm) ? "" : "none";
                });
            });
        }

        // Add event listeners to checkboxes
        addCheckboxEventListeners(profile_type);

        // Add event listeners to remove buttons
        const removeButtons = document.querySelectorAll('.remove-item-btn');
        removeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                const index = parseInt(row.getAttribute('data-index'));
                removeSelectedItem(index);
            });
        });
    }

    // Function to remove a selected item
    function removeSelectedItem(index) {
        if (!window.currentSelectedItems || !window.currentProfileType) {
            console.error("No selected items or profile type found");
            return;
        }

        // Remove the item from the array
        window.currentSelectedItems.splice(index, 1);

        // If no items left, show empty state
        if (window.currentSelectedItems.length === 0) {
            createTableStructure(window.currentProfileType);
        } else {
            // Update the table with remaining items
            updateSelectedItemsTable(window.currentSelectedItems, window.currentProfileType);
        }
    }

    function populateStaffTable(data) {
        // console.log("Populating staff table:", data);

        // Store the data for future use
        window.popupData = window.popupData || {};
        window.popupData["Staff"] = data;

        // For the new flow, we don't automatically populate the table
        // Instead, we wait for the user to click the "Add Staff" button

        // Make sure the table structure exists
        let success = true;
        const detailedContainer = document.getElementById("detailed_profile_type_container");
        if (!detailedContainer || !detailedContainer.querySelector('.container-page-1')) {
            // console.log("Creating table structure for Staff");
            success = createTableStructure("Staff");
        }

        if (!success) {
            console.error("Failed to create table structure");
            showErrorMessage("Failed to create table structure. Please try again.");
        }
    }

    function addCheckboxEventListeners(profile_type) {
        try {
            // Add event listener to the header checkbox for select all functionality
            const headerCheckbox = document.querySelector("#profile-data-table thead input[type='checkbox']");
            if (headerCheckbox) {
                // Remove any existing event listeners (to prevent duplicates)
                headerCheckbox.removeEventListener('change', headerCheckboxChangeHandler);

                // Add new event listener
                headerCheckbox.addEventListener('change', headerCheckboxChangeHandler);
            } else {
                console.warn("Header checkbox not found");
            }

            // Add event listeners to individual checkboxes for counting
            const checkboxes = document.querySelectorAll("#profile-data-tbody input[type='checkbox']");
            if (checkboxes && checkboxes.length > 0) {
                checkboxes.forEach(checkbox => {
                    // Remove any existing event listeners (to prevent duplicates)
                    checkbox.removeEventListener('change', function() {
                        updateSelectionCount(checkboxes.length, profile_type);
                    });

                    // Add new event listener
                    checkbox.addEventListener('change', function() {
                        updateSelectionCount(checkboxes.length, profile_type);
                    });
                });

                // Initial count update
                updateSelectionCount(checkboxes.length, profile_type);
            } else {
                console.warn("No checkboxes found in table body");
            }
        } catch (error) {
            console.error("Error setting up checkbox event listeners:", error);
        }
    }

    // Handler function for header checkbox
    function headerCheckboxChangeHandler(e) {
        try {
            // Prevent any default behavior or event propagation
            e.stopPropagation();

            // Get the current layout state
            const formContainer = document.querySelector(".col-md-4");
            const detailedContainer = document.getElementById("detailed_profile_type_container");

            // Store the current layout state
            const currentFormClass = formContainer ? formContainer.className : "";
            const currentDetailedClass = detailedContainer ? detailedContainer.className : "";
            const currentDetailedDisplay = detailedContainer ? detailedContainer.style.display : "";

            const isChecked = this.checked;
            const checkboxes = document.querySelectorAll("#profile-data-tbody input[type='checkbox']");
            if (checkboxes && checkboxes.length > 0) {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });

                // Update the count after selecting/deselecting all
                const countElement = document.querySelector("#detailed_profile_type_container .student-count");
                if (countElement) {
                    const profile_type = countElement.textContent.includes("Staff") ? "Staff" : "Student";
                    updateSelectionCount(checkboxes.length, profile_type);
                }
            }

            // Restore the layout state
            if (formContainer) formContainer.className = currentFormClass;
            if (detailedContainer) {
                detailedContainer.className = currentDetailedClass;
                detailedContainer.style.display = currentDetailedDisplay;
            }
        } catch (error) {
            console.error("Error in header checkbox handler:", error);
        }
    }

    function updateSelectionCount(total, profile_type) {
        try {
            const countElement = document.querySelector("#detailed_profile_type_container .student-count");
            if (!countElement) {
                console.warn("Count element not found");
                return;
            }

            // Get selected checkboxes using ID-based selector
            const checkboxes = document.querySelectorAll("#profile-data-tbody input[type='checkbox']");
            if (!checkboxes || checkboxes.length === 0) {
                console.warn("No checkboxes found");
                return;
            }

            const selectedCount = document.querySelectorAll("#profile-data-tbody input[type='checkbox']:checked").length || 0;

            // Display the count without the "Show Selected" button
            countElement.innerHTML = `${selectedCount}/${total || checkboxes.length} ${profile_type} Selected`;
        } catch (error) {
            console.error("Error updating selection count:", error);
        }
    }

    $(document).ready(function() {
        // Initialize Select2 for verifier field
        $('#verifier').select2({
            width: 'resolve', // This will make Select2 respect the container width
            placeholder: 'Select Verifier',
            dropdownAutoWidth: true
        });

        // Add CSS to ensure the Select2 container doesn't overflow and style the help popup
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                #verifier_div .select2-container {
                    width: 100% !important;
                    max-width: 100%;
                }
                .select2-dropdown {
                    width: auto !important;
                    min-width: 200px;
                }

                /* Help popup styling */
                .verifier-help-content {
                    text-align: left;
                    font-size: 14px;
                }

                .verifier-help-content h5 {
                    color: #3085d6;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                }

                .verifier-help-content h6 {
                    color: #555;
                    font-weight: 600;
                    margin-top: 15px;
                }

                .verifier-help-content ul, .verifier-help-content ol {
                    margin-top: 8px;
                }

                .verifier-help-content li {
                    margin-bottom: 8px;
                }

                .verifier-help-content strong {
                    color: #333;
                }

                .verifier-help-popup .swal2-icon {
                    margin-top: 10px;
                }
            `)
            .appendTo('head');

        // Re-initialize Select2 when the layout changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class') {
                    // Destroy and reinitialize Select2 to adapt to new container width
                    $('#verifier').select2('destroy').select2({
                        width: 'resolve',
                        placeholder: 'Select Verifier',
                        dropdownAutoWidth: true
                    });
                }
            });
        });

        // Observe the form container for class changes
        const formContainer = document.getElementById('form_container');
        if (formContainer) {
            observer.observe(formContainer, { attributes: true });
        }

        $(document).on('click', function(e) {
            if (!$(e.target).closest('#verifier_div').length) {
                $('#verifier').select2('close');
            }
        });

        // Handle verifier help button click
        $('#verifier-help-btn').on('click', function(e) {
            e.preventDefault();

            // Create help content with detailed explanation
            const helpContent = `
                <div class="verifier-help-content">
                    <h5 class="mb-3">ID Card Verification Process</h5>

                    <p>The verifier option determines who will review and approve ID cards before they are sent for printing.</p>

                    <div class="mt-3">
                        <h6>Available Options:</h6>
                        <ul class="pl-3">
                            <li><strong>By School Admin:</strong> School administrators will verify all ID cards</li>
                            <li><strong>By Class Teacher:</strong> Class teachers will verify their students' ID cards (for Student ID cards)</li>
                            <li><strong>By Respective Staff:</strong> Each staff member will verify their own ID card (for Staff ID cards)</li>
                            <li><strong>By Parent:</strong> Parents will verify their children's ID cards (for Student ID cards)</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>Multiple Selection:</h6>
                        <p>You can select multiple verifiers. The verification process will follow this sequence:</p>
                        <ol class="pl-3">
                            <li>Each selected verifier must approve the ID cards</li>
                            <li>ID cards will only be sent for printing after all selected verifiers have approved</li>
                        </ol>
                    </div>

                    <div class="mt-3">
                        <h6>Verification Status:</h6>
                        <p>You can track the verification status in the order details page.</p>
                    </div>
                </div>
            `;

            // Show the help popup using SweetAlert2
            Swal.fire({
                title: 'Verifier Help',
                html: helpContent,
                icon: 'info',
                width: 600,
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#3085d6',
                customClass: {
                    container: 'verifier-help-popup'
                }
            });
        });
    });

// Function to get the IDs of selected staff or students
function getSelectedProfileIds(profile_type) {
    // Get the currently selected items
    const selectedItems = window.currentSelectedItems || [];

    // Array to store the IDs
    const selectedIds = [];

    // Extract the database IDs (sm.id for staff, std.id for students)
    selectedItems.forEach(item => {
        if (item.id) {
            selectedIds.push(item.id);
        }
    });

    // Log the selected IDs for debugging
    console.log(`Selected ${profile_type} IDs:`, selectedIds);

    return selectedIds;
}

$(document).ready(function() {
    // Handle delete button click
    $('.delete-template-btn').click(function() {
        const templateId = $(this).data('id');
        const templateName = $(this).data('name');

        $('#templateNameToDelete').text(templateName);
        $('#confirmDeleteBtn').attr('href', '<?= site_url('template/delete/') ?>' + templateId);
        $('#deleteTemplateModal').modal('show');
    });

    // Load template previews
    $('.template-preview').each(function() {
        const $preview = $(this);
        const templateId = $preview.data('template-id');
        const side = $preview.data('side');

        // Load template data
        $.ajax({
            url: '<?= site_url('idcards/Idcards_controller/get_template_json/') ?>' + templateId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const template = response.template;
                    const design = side === 'front' ? template.front_design : template.back_design;

                    if (design && design.elements && design.elements.length > 0) {
                        // Clear placeholder
                        $preview.empty();

                        // Set size class
                        if (design.styles && design.styles.size) {
                            if (design.styles.size === 'portrait') {
                                $preview.css({
                                    'width': '40px',
                                    'height': '60px'
                                });
                            } else if (design.styles.size === 'landscape') {
                                $preview.css({
                                    'width': '60px',
                                    'height': '40px'
                                });
                            } else if (design.styles.size === 'custom' && design.styles.width && design.styles.height) {
                                const ratio = design.styles.width / design.styles.height;
                                if (ratio > 1) {
                                    $preview.css({
                                        'width': '60px',
                                        'height': (60 / ratio) + 'px'
                                    });
                                } else {
                                    $preview.css({
                                        'width': (60 * ratio) + 'px',
                                        'height': '60px'
                                    });
                                }
                            }
                        }

                        // Create content container
                        const $content = $('<div class="preview-content"></div>');
                        $content.css({
                            'position': 'relative',
                            'width': '100%',
                            'height': '100%',
                            'background-color': '#fff'
                        });

                        // Add a simplified preview
                        $content.append('<div class="text-center text-muted small" style="padding:5px;font-size:6px;">' +
                            design.elements.length + ' elements</div>');

                        $preview.append($content);
                    }
                }
            }
        });
    });
});

$(document).ready(function () {
    $(".template-pair").click(function () {
        // console.log(selectedTemplateId);

        // Adjust layout
        $("#templates-container").removeClass("col-md-12").addClass("col-md-6");
        $("#preview-section").removeClass("d-none");

        // Load template images dynamically
        $.ajax({
            url: "<?= site_url('idcards/Idcards_controller/get_template_json/') ?>" + selectedTemplateId,
            type: "GET",
            dataType: "json",
            success: function (response) {
                if (response.status === "success") {
                    const template = response.template;
                    $("#preview-front").css("background-image", `url(${template.front_image})`);
                    $("#preview-back").css("background-image", `url(${template.back_image})`);
                }else{
                    
                }
            },
        });
    });

    // Close preview
    $("#close-preview").click(function () {
        originalSelectedTemplateId = null; // Reset the original selected template ID
        selectedTemplateId = null; // Reset the integer ID
        $("#templates-container").removeClass("col-md-6").addClass("col-md-12");
        $("#preview-section").addClass("d-none");
    });

    // Zoom functionality
    $("#zoom-range").on("input", function () {
        const zoomValue = $(this).val();
        $("#preview-front, #preview-back").css("transform", `scale(${zoomValue / 100})`);
    });
});

</script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>