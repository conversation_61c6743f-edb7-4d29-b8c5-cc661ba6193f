<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li>Student Index</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('student/student_menu'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Select Student(s)
          </h3>
            <div>
                <?php 
                    $arr = array();
                    foreach ($admissionStatusArr as $key => $value) { 
                        $arr[$key] = $value; 
                    }
                ?>

                <div class="col-md-8">
                <select name="admission_status[]" id="admission_status" multiple title="Select Status" class="form-control selectpicker" onchange="admission_status_change()">
                    <!-- <option value="">Select Section</option> -->
                    <?php foreach ($arr as $key => $class) { ?>
                    <option <?php if(in_array($key, $adSelected)) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
                    <?php } ?>
                </select>
                </div>
                <div class="col-md-4">
                    <input type="button" value="Get" id="get_all_students_bystatus" class="input-md btn btn-primary">
                </div>
            </div>   
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
     <!--  <div class="panel-body"> -->
        <div class="row" style="margin-left:0px;margin-right:0px;margin-bottom: 1.6rem">
            <div class="col">
                <div class="form-group text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <h5>Search By Class/Grade</h5>
                    <div class="row d-flex" style="margin: 0px">

                        <div class="col-md-8 col-md-offset-2">
                            <?php 
                                $array = array();
                                $array[0] = 'Select Class';
                                foreach ($classList as $key => $cl) {
                                    $array[$cl->classId] = $cl->className;
                                }
                                echo form_dropdown("classId", $array, set_value("classId",$selectedClassId), "id='classId' onchange='class_wise_search_std();' class='form-control'");
                            ?>
                        </div>
                        <div class="col-md-12">
                            <span class="help-block">Select Class/Grade</span>
                        </div>
                    </div>
                </div>                
            </div>

            <div class="col">
                <div class="form-group text-center" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <h5>Search By Class / Section</h5>
                    <div class="row">
                        <div class="col-md-8 col-md-offset-2">
                            <?php 
                                $array = array();
                                $array[0] = 'Select Section';
                                foreach ($classSectionList as $key => $cl) {
                                    $array[$cl->id] = $cl->class_name . '('.$cl->section_name.')';
                                }
                                echo form_dropdown("classSectionId", $array, '', "id='classSectionId'  onchange='classSection_wise_search_std()'  class='form-control'");
                            ?>
                        </div>
                        <div class="col-md-12">
                            <span class="help-block">Select Section</span>
                        </div>
                    </div>
                </div>                
            </div>

            <div class="col">
                <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <div class="form-horizontal">
                        <h5>Search By Student/Parent Name</h5>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-1">
                                <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1">
                                
                            </div>
                            <div class="col-md-2">
                                <input type="button" value="Get" id="getByStdName" class="input-md btn btn-primary">
                            </div>
                            <div class="col-md-12">
                                <span class="help-block">Enter few letters of name and click Enter/Get</span>
                            </div>                            
                        </div>
                    </div>
                </div>                
            </div>
        </div>
        <div class="row" style="margin-left: 0px;margin-right: 0px;margin-bottom: 8px">
             
            <div class="col">
                <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <div class="form-horizontal">
                        <h5>Search By Student Admission/Enrollment/Alpha Roll No</h5>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-1">
                                <input id="admission_no" autocomplete="off" placeholder="Search by Admission/Enrollment/Alpha Roll No" class="form-control input-md" name="admission_no">
                            </div>
                            <div class="col-md-2">
                                <input type="button" value="Get" id="getByAdmissionNo" class="input-md btn btn-primary">
                            </div>
                            <div class="col-md-12">
                                <span class="help-block">Enter admission/enrollment/alpha Roll number and click Get</span>
                            </div>
                        </div>
                    </div>
                </div>                
            </div>

            <div class="col">
                <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <div class="form-horizontal">
                        <h5>Search By Phone No</h5>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-1">
                                <input id="phone_no" autocomplete="off" placeholder="Search by Phone No" class="form-control input-md" name="phone_no">
                            </div>
                            <div class="col-md-2">
                                <input type="button" value="Get" id="getByPhoneNo" class="input-md btn btn-primary">
                            </div>
                            <div class="col-md-12">
                                <span class="help-block">Enter phone number and click Get</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="form-group text-center hidden-xs" style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                    <div class="form-horizontal">
                        <h5>Search By Email id</h5>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-1">
                                <input id="emailId" autocomplete="off" placeholder="Search by Email id" class="form-control input-md" name="email_id">
                            </div>
                            <div class="col-md-2">
                                <input type="button" value="Get" id="getByEmailId" class="input-md btn btn-primary">
                            </div>
                            <div class="col-md-12">
                                <span class="help-block">Enter email id and click Get</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $this->load->view('student/menu/_script.php'); ?>

    <div class="card-body pt-1">
        <div class="col-md-12">
            <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
          <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
              <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                  <h3 class="card-title panel_title_new_style_staff"> 
                    <strong>Student List </strong>
                  </h3>   
                </div>
              </div>
            </div>
            <div>
               
               <div class="panel-body stdudentDataAjax hidden-xs leaveData" style="display: none;"></div>

                <div class="panel-body stdudentData hidden-xs leaveData">
                    <h5>Select filter to Student list</h5>
                </div> <!--Panel body-->

                <div class="panel-body stdudentData_mobile visible-xs leaveData">
                    <h5>Select filter to Student list</h5>
                </div>
             
            </div>
          </div>
        </div>
    </div>
  </div>
</div>


<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('student/student_menu');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style>
#tags{
    position:relative;
    padding: 10px;
}
.autocomplete-items {
  position: absolute;
  overflow-y:auto;
  border-bottom: none;
  border-top: none;
  height:300px;
  margin:0px 15px;
  z-index: 99;
  /*position the autocomplete items to be the same width as the container:*/
  top: 100%;
  left: 0;
  right: 0;
}
.autocomplete-items div {
  padding: 10px;
  cursor: pointer;
  background-color: #fff; 
  border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
  /*when hovering an item:*/
  background-color: #e9e9e9; 
}
.autocomplete-active {
  /*when navigating through the items using the arrow keys:*/
  background-color: DodgerBlue !important; 
  color: #ffffff; 
}
  ul.panel-controls>li>a {
    border-radius: 50%;
}
.bootstrap-select{
    width: 142px !important;
}
</style>


<script>

$(document).ready(function() {
    $('#admission_status').selectpicker({
        liveSearch: true,
        liveSearchPlaceholder: 'Search fields...'
    });
});
$(document).on('click', function(event) {
    var $target = $(event.target);
    if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
        $('.bootstrap-select').removeClass('open show'); 
        $('.dropdown-menu').removeClass('show'); 
    }
});

var mobile = '<?php echo $this->mobile_detect->isMobile(); ?>';
if (mobile) {
    function admission_status_change() {
        var sectionId = $("#classSectionId").val();
        if (sectionId !=0) {
            classSection_wise_search_std();
        }else{
            class_wise_search_std();
        }
    }
}

if (mobile) {
    function class_wise_search_std() {
        var classId = $("#classId").val();
        var adm_status = $('#admission_status').val();
       
        if(classId) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'classId':classId, 'adm_status':adm_status, 'mode':'class_id'},
                success: function(data) {
                    var std = JSON.parse(data);
                    if (mobile) {
                        $(".stdudentData_mobile").html(prepare_student_table_mobile(std));
                    } else { 
                        $(".stdudentData").html(prepare_student_table(std));    
                    }
                }
            });
        }
    }
    function classSection_wise_search_std() {
        var sectionId = $("#classSectionId").val();
        var adm_status = $('#admission_status').val();
        if(sectionId) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'sectionId':sectionId, 'adm_status':adm_status, 'mode':'section_id'},
                success: function(data) {
                    var std = JSON.parse(data);
                    if (mobile) {
                        $(".stdudentData_mobile").html(prepare_student_table_mobile(std));
                    } else { 
                        $(".stdudentData").html(prepare_student_table(std));    
                    }
                }
            });
        }
    }
}
  
 $(document).ready(function(){  
    $("#getByAdmissionNo").click(function (){
        getByAdmissionNo();
    });
    function getByAdmissionNo() {
        let adm_status = $('#admission_status').val();
        $('.stdudentDataAjax').html('');
        $('.stdudentDataAjax').hide();
        $('#classSectionId option:first').prop('selected',true);
        $('#classId option:first').prop('selected',true);
        var admin_no = $("#admission_no").val();
        if(admin_no) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'ad_no':admin_no, 'mode':'ad_no','adm_status':adm_status},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('.stdudentData').show();
                }
            });

        }
    }
    
   
    

    $("#stdName1").keydown(function(e) {
        if(e.keyCode == 13) {
            getByStdName();
        }
    });
    // Get All Students By Status

    $("#get_all_students_bystatus").click(function (){
        //alert("haha");
        $('.stdudentData').hide();
        get_all_students();
        
    })
    function get_all_students(){
        $('.stdudentDataAjax').html('');
        $('.stdudentDataAjax').hide();
        
        $('.loading-icon').show();
    
    
        let adm_status = $('#admission_status').val();
        $.ajax({
            url:'<?php echo site_url('student/student_controller/get_all_students_by_status');?>',
            type:'post',
            data:{'adm_status':adm_status},
            success:function(data) {
                let student_data = $.parseJSON(data);
                console.log(student_data);
                if (student_data.length > 0) {
                    $(".stdudentData").html(prepare_student_table(student_data));
                    $('.stdudentData').show();
                }
            }
        });
    }


    $("#getByStdName").click(function (){
        $('.stdudentData').hide();
        getByStdName();
    });

    function getByStdName() {
        var adm_status = $('#admission_status').val();
        console.log(adm_status);

        $('.stdudentDataAjax').html('');
        $('.stdudentDataAjax').hide();
        

        $('#classSectionId option:first').prop('selected',true);
        $('#classId option:first').prop('selected',true);
        var name = $("#stdName1").val();
        if(name) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'name':name, 'mode':'std_name','adm_status':adm_status},
                success: function(data) {
                    var std = JSON.parse(data);
                    console.log(std);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('.stdudentData').show();
                }
            });
        }

    }

    $("#getByPhoneNo").click(function(){
        $('.stdudentData').hide();
        getBYPhoneNo();
        
    });
    function getBYPhoneNo() {
        let adm_status = $('#admission_status').val();
        $('.stdudentDataAjax').hide();
        $('#classSectionId option:first').prop('selected',true);
        $('#classId option:first').prop('selected',true);
        var phone_no = $("#phone_no").val();
        if(phone_no) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'phone_no':phone_no, 'mode':'phone_no','adm_status':adm_status},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('.stdudentData').show();
                }
            });
        }

    }
     $("#getByEmailId").click(function(){
        $('.stdudentData').hide();
        getByEmailId();
        
    });

    function getByEmailId() {
        var adm_status = $('#admission_status').val();
        $('.stdudentDataAjax').hide();
        $('#classSectionId option:first').prop('selected',true);
        $('#classId option:first').prop('selected',true);
        var email = $("#emailId").val();
        if(email) {
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'email':email, 'mode':'email','adm_status':adm_status},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('.stdudentData').show();
                }
            });
        }

    }
    

   
});
</script>
<script>
function prepare_student_table (std) {
    var path = '<?php echo $this->filemanager->getFilePath(""); ?>';
    var picUrl = '';
    var html = '';
    var is_semester_scheme = '<?php echo $is_semester_scheme ?>';

    
    if(!std)
        html += "<h4>Select any filter to get student data</h4>";
    else {
        html += `
            <table id="customers2" class="table datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Student Name</th>
                        <th>Gender</th>
                        <th>Admission No</th>
                        <th>Section</th>
                        ${is_semester_scheme == '1' ? '<th>Semester</th>' : ''}
                        <th>Father Name</th>
                        <th>Status</th>
                        <th width="10%">Actions</th>
                    </tr>
                </thead>
                <tbody>
            `;

        for (i=0;i < std.length; i++) {
            picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            if(std[i].gender == 'Male'){
                picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
            }

            if(std[i].picture_url != '' && std[i].picture_url != null) {
                picUrl = path + std[i].picture_url;
            }
            if (std[i].admission_status == 2){
                var bg = '';
            } else {
                var bg = '#ccc';
            }
            var classSection = std[i].classSection;
            if (std[i].combination !='' && std[i].combination != null && std[i].combination != 0) {
                classSection = std[i].classSection +' ( ' +std[i].combination+' )';
            }

            html += "<tr style='color:" + bg + "'><td>" + (i+1) + "</td>";
            // html += "<td></td>";
            html += "<td><img width='30' height='30' class='img-circle' src='"+picUrl+"' />&nbsp;&nbsp;" + capitalizeString(std[i].stdName) + "</td>";
            html += "<td>" + std[i].gender + "</td>";
            html += "<td>" + std[i].admission_no + "</td>";
            html += "<td>" + classSection + "</td>";
            if (is_semester_scheme == '1') {
                html +='<td>' + std[i].semester + '</td>';
            }         

            html += "<td>" + capitalizeString(std[i].fName) + "</td>";
            html += "<td>" + std[i].admission_status_name + "</td>";
            // html += "<td>" + std[i].promoStatus + "</td>";
            html += "<td><a href=" + "<?php echo site_url('student/Student_controller/addMoreStudentInfo/') ?>" + std[i].id + "  class='btn btn-primary'>Detail</a></td></tr>";
        }
        html += '</tbody></table>';
    }
    return html;
}

function capitalizeString(str) {
    return str.replace(/\w\S*/g, function(txt){
        if(<?php echo $name_to_caps?>){
            return txt.toUpperCase();//all capitals
        }
        else{
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();//first letter capital
        }
    });
}


function prepare_student_table_mobile(std) {
    var path = '<?php echo $this->filemanager->getFilePath(""); ?>';
    var picUrl = '';
     var html = '';
    
    if(!std)
        html += "<h4>Select any filter to get student data</h4>";
    else {

        html += '<ul class="list-group border-bottom">';
        for (i=0;i < std.length; i++) {
            picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            if(std[i].gender == 'Male'){
                picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
            }

            if(std[i].picture_url != '' && std[i].picture_url != null) {
                console.log(std[i].picture_url);
                picUrl = path + std[i].picture_url;
            }
            if (std[i].admission_status == 2){
                var bg = '';
            } else {
                var bg = '#ccc';
            }
            html += "<li style='color:" + bg + "' class='list-group-item'>";
            html += "<img width='40' class='img-circle' src='"+picUrl+"' />&nbsp;&nbsp;<strong style='font-size:14px;'>"+ std[i].stdName+'</strong><br>';
            // html += '<strong>Student Name : </strong>' + std[i].stdName + " <br>";
            html += '<strong>Admission No. : </strong>' + std[i].admission_no + " <br>";
            html += '<strong>Class/Section : </strong>' + std[i].classSection + " <br>";
            html += '<strong>Father Name : </strong>' + std[i].fName + " <br>";
            html += '<strong>Status : </strong>' + std[i].admission_status_name + " <br>";
            html += '<strong>Promotion : </strong>' + std[i].promoStatus + " <br>";
            html += "<a href=" + "<?php echo site_url('student/Student_controller/addMoreStudentInfo/') ?>" + std[i].id + "  class='btn btn-primary'>Detail</a>";
            html += "</li>";
         }
        html +='</ul>';
    }
    return html;

}

</script>
<script type="text/javascript">


    function clearStatusFilters() {
        $(`#std-filter`).val('');
        $(`#admission-filter`).val('');
        $(`#section-filter`).val('');
        $(`#father-filter`).val('');
        $(`#semester-filter`).val('');
        filterStatusList();
    }
    function filterStatusList() {
        var std_filter = $(`#std-filter`).val().toLowerCase();
        var admission_filter = $(`#admission-filter`).val().toLowerCase();
        var section_filter = $(`#section-filter`).val().toLowerCase();
        var father_filter = $(`#father-filter`).val().toLowerCase();
        var semester_filter = $(`#semester-filter`).val();
        if(semester_filter) {
            semester_filter = semester_filter.toLowerCase();
        }
        var find_string = '';
        if(std_filter != '') {
            find_string += `[data-studentname*='${std_filter}']`;
        }
        if(admission_filter != '') {
            find_string += `[data-admission*='${admission_filter}']`;
        }
        if(section_filter != 'all') {
            find_string += `[data-classSection*='${section_filter}']`;
        }
        if(father_filter != '') {
            find_string += `[data-father*='${father_filter}']`;
        }
        if(semester_filter != '' && semester_filter != undefined) {
            find_string += `[data-semister*=${semester_filter}]`;
        }
        console.log(find_string);
        if(find_string === '') {
            $(".student-filters").show();
        } else {
            $(".student-filters").hide();
            $(`.student-filters${find_string}`).show();
        }
    }
</script>