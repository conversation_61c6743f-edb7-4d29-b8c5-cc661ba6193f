<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <div class="row d-flex align-items-center">
                <div class="col-xs-9 p-0">
                    <h3 class="card-title panel_title_new_style mb-0">
                        <strong>Per-Section, Per-Subject, Date range</strong>
                    </h3>
                </div>

                <div class="col-xs-3 p-0">
                    <div class="circleButton_noBackColor" style="background-color:#fe970a;float:right;display: none;">
                        <a class="control-primary" onclick="exportToExcel()" id="exportBtn">
                        <span class="fa fa-file-excel-o backgroundColor_organge" style="font-size:19px"></span>
                        </a>          
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body px-2 py-1">
            <div class="row">
                <div class="col-md-12 form-group">
                    <label class="control-label">Date Range</label>
                    <div id="reportrange" class="dtrange" style="width: 100%">
                        <span></span>
                        <input type="hidden" id="from_date">
                        <input type="hidden" id="to_date">
                    </div>
                </div>
                <div class="col-md-12 form-group">
                    <label class="control-label">Section</label>
                    <select class="form-control" id="class_section_id" onchange="getSubjectOrSessions()">
                        <option value="0">Select section</option>
                        <?php 
                            foreach ($sections as $key => $section) {
                                echo '<option value="'.$section->id.'">'.$section->class_name.$section->section_name.'</option>';
                            }
                        ?>
                    </select>
                </div>
                <?php if($attendance_type == 'subject_wise') { ?>
                  <div id="subject-wise" class="col-md-12 form-group">
                    <label class="control-label">Subject</label>
                    <select class="form-control" id="subject_id">
                        <option value="0">Select subject</option>
                    </select>
                  </div>
                <?php } else if($attendance_type == 'session_wise'){ ?>
                  <div id="session-wise" class="col-md-12 form-group">
                    <label class="control-label">Session</label>
                    <select class="form-control" id="session_id">
                        <option value="0">Select session</option>
                    </select>
                  </div>
                <?php } ?>

                <div class="col-md-12 form-group">
                    <label class="control-label">Attendance Range</label>
                    <br>
                    <input class="col-md-12" type="numeric" name="attendance_range" id="attendance_range" value="35">
                </div>

                <div class="col-md-12 mt-3">
                    <button onclick="getReport()" class="btn btn-primary w-100">Get Report</button>
                </div>
            </div>

            <div class="mt-5" id="attendance-data">
                
            </div>
        </div>  
    </div>
</div>

<a href="<?php echo site_url('attendance_v2/menu');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>


<script type="text/javascript">
var attendance_type = 'subject_wise';
var overall_present = 0;
var overall_taken = 0;
var is_semester_scheme = 0;
$(document).ready(function(){
    attendance_type = '<?php echo $attendance_type; ?>';
    is_semester_scheme = <?php echo $is_semester_scheme; ?>;
});
function getSubjectOrSessions() {
    var section_id = $("#class_section_id").val();
    if(attendance_type == 'subject_wise') {
      $("#subject_id").html('<option value="0">Select subject</option');
    } else {
      $("#session_id").html('<option value="0">Select session</option');
    }
    if(section_id == 0) {
      return false;
    }

    $.ajax({
      url: '<?php echo site_url('attendance_v2/attendance/getSectionSubjectsORSessions'); ?>',
      type: 'post',
      data: {'section_id':section_id, 'attendance_type':attendance_type},
      success: function(data) {
        data = JSON.parse(data);
        if(data.attendance_type == 'subject_wise') {
          var subjects = data.subjects;
          var options = '<option value="0">Select subject</option>';
          for(var i=0; i<subjects.length; i++) {
            if(is_semester_scheme == 1) {
                options += '<option value="'+subjects[i].id+'">('+subjects[i].sem_name.toUpperCase()+') '+subjects[i].subject_name+'</option>';
            } else {
                options += '<option value="'+subjects[i].id+'">'+subjects[i].subject_name+'</option>';
            }
          }
          $("#subject_id").html(options);
        }
      }
    });
}

let invalidAttendanceRangeMsg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;">
      Please select a valid attendance percentage range.
    </div>
  `;

function getReport() {
    let attendance_range_number=+$("#attendance_range").val();
    if(attendance_range_number<0 || attendance_range_number>100){
        return $("#attendance-data").html(invalidAttendanceRangeMsg);
    }

    $("#attendance-data").show()

    overall_present = 0;
    overall_taken = 0;
    $("#exportBtn").hide();
    var from_date = $("#from_date").val();
    var to_date = $("#to_date").val();
    var section_id = $("#class_section_id").val();
    if(section_id == 0) return false;
    var section_name = $("#class_section_id option:selected").text();
    var type_name = '';
    var type_id = 0;
    if(attendance_type == 'subject_wise') {
      var type_id = $("#subject_id").val();
      type_name = $("#subject_id option:selected").text();
    } else {
      var type_id = $("#session_id").val();
      type_name = $("#session_id option:selected").text();
    }
    if(type_id == 0) return false;

    $.ajax({
        url: '<?php echo site_url('attendance_v2/reports/getSectionWise'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date, 'section_id':section_id, 'type_id': type_id, 'type':attendance_type},
        success: function(data) {
            data = JSON.parse(data);
            var dates = data.dates;
            var students = data.students;
            if(dates.length == 0) {
                $("#attendance-data").html('<div class="no-data-display">No data available.</div>');
                return false;
            }
            $("#exportBtn").show();
            var html = `<div class="text-center">
            <h3><b>Date: </b>${from_date} to ${to_date}</h4>
            <h4><b>Section: </b>${section_name}</h4>
            <h4><b>Subject: </b>${type_name}</h4>
            <h5><b>Total Present Percentage: </b><span id="total-present-percentage"></span></h5>
            </div>`;
            html += `<div class="table-responsive"><table class="table table-bordered">`;
            html += constructHeader(dates);
            html += constructBody(dates, students);
            html += `</table></div>`;
            $("#attendance-data").html(html);
            $("#total-present-percentage").html(`${((overall_present/overall_taken)*100).toFixed(0)} %`);
        }
    });
}

function constructHeader(dates) {
    var html = `<thead><tr><th style="width:3%;">#</th><th style="width:15%;">Student Name</th>`;
    for(var date in dates) {
        html += `<th style="text-align: center;" colspan="${dates[date].length}">${moment(date).format('Do MMM YY')}</th>`;
    }
    html += `<th style="width: 8%;text-align: center;font-weight: 800;">Present</th>`;
    html += `<th style="width: 8%;text-align: center;font-weight: 800;">Total</th>`;
    html += `<th style="width: 8%;text-align: center;font-weight: 800;">%age Present</th></tr></thead>`;
    return html;
}

function constructBody(dates, students) {
    var html = `<tbody>`;
    var status_array = {
        '0' : '<td style="text-align:center;background-color: #f5f5f5;">-</td>',
        '1' : '<td style="text-align:center;background-color: #91fcb9;">P</td>',
        '2' : '<td style="text-align:center;background-color: #ff9696;">AB</td>',
        '3' : '<td style="text-align:center;background-color: #fffc96;">L</td>',
    }
    for(var i=0; i<students.length; i++) {
        html += '<tr>';
        html += `<td>${i+1}</td>`;
        html += `<td>${students[i].student_name}</td>`;
        var student = students[i];
        var total_taken = 0;
        var total_present = 0;
        for(var date in dates) {
            var ids = dates[date];
            for(var k in ids) {
                if(date in student['attendance'] && ids[k] in student['attendance'][date]) {
                    var status = student['attendance'][date][ids[k]];
                    html += `${status_array[status] || '<td style="text-align:center;">-</td>'}`;
                    if(status == 1 || status == 3) { 
                        total_present++; 
                        total_taken++;
                    } else if(status == 2) {
                        total_taken++;
                    }
                } else {
                    html += `<td style="text-align:center;">-</td>`;
                }
            }
        }
        overall_present += total_present;
        overall_taken += total_taken;

        var total_perc = 'NA';
        let percentage=0
        if (total_taken > 0) {
            total_perc = total_present / total_taken * 100;
            percentage=total_perc
            total_perc = total_perc.toFixed(2) + '%';
        }
        html += `<td style="text-align: center;font-weight: 800;">${total_present}</td>`;
        html += `<td style="text-align: center;font-weight: 800;">${total_taken}</td>`;

        attendance_range_number=+$("#attendance_range").val();
        html += `<td style="text-align: center;font-weight: 800;background:${percentage>attendance_range_number ? "#91fcb9" : "#ff9696"}">${total_perc}</td>`;

        html += '</tr>';
    }
    html += `</tbody>`;
    return html;
}
</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
    function exportToExcel() {
        var from_date = $("#from_date").val();
        var to_date = $("#to_date").val();
        var section_name = $("#class_section_id option:selected").text();
        var type_name = '';
        if(attendance_type == 'subject_wise') {
          type_name = $("#subject_id option:selected").text();
        } else {
          type_name = $("#session_id option:selected").text();
        }
        
        var wb = XLSX.utils.book_new();
        wb.Props = {
              Title: section_name+' '+type_name+" - "+from_date+" to "+to_date,
              Subject: "Attendance Report",
              Author: "NextElement",
              CreatedDate: new Date()
        };

        wb.SheetNames.push('Attendance');
        // var ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
        var ws_school = XLSX.utils.table_to_sheet(document.getElementById('attendance-data'));
        wb.Sheets['Attendance'] = ws_school;

        var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
        downloadSample(wbout, section_name+' '+type_name);
    }

    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
      
    }

    function downloadSample(wbout, file_name){
        file_name = file_name+'.xlsx';
        saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), file_name);
    };

    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Next 7 Days': [moment(), moment().add(6, 'days')],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'Next Month': [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment(),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });
  
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));
</script>