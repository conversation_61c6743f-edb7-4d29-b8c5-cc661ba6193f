<div class="container">
    <h4 class="text-center mb-4">Select the template for your ID Card</h4>
    <div class="row">
        <!-- Templates Container -->
        <div id="templates-container" class="col-md-12"> <!-- Default to full width -->
            <div class="container">
                <div id="templatePreviews" class="row"><div class="no-data-display">No-data</div></div>
            </div>
        </div>

        <!-- Preview Section -->
        <div id="preview-section" class="col-md-6 d-none"> <!-- Initially hidden -->
            <div class="preview-container border p-4 rounded bg-light">
                <h5 class="text-center mb-4">Preview <span id="template-name-display"></span></h5>
                <div id="preview-box" class="preview-box mx-auto">
                    <div id="preview-image" class="card-preview">
                        <div id="preview-content" class="card-content"></div>
                    </div>
                </div>
                <div class="navigation-arrows mt-3 d-flex justify-content-center align-items-center">
                    <button id="prev-view" class="nav-arrow me-3">&larr;</button>
                    <span id="view-indicator" class="mx-3">Front Side</span> <!-- Indicator text -->
                    <button id="next-view" class="nav-arrow ms-3">&rarr;</button>
                </div>
                <div class="preview-footer mt-4 d-flex flex-column align-items-center">
                    <div class="zoom-bar d-flex align-items-center">
                        <input type="range" id="zoom-range" min="50" max="200" value="100" class="me-2">
                        <span id="zoom-percentage">100%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
$(document).ready(function () {
    // Check if templates-container is empty and show a no data message
    if ($('#templates-container').children().length === 0) {
        $('#templates-container').html(`
            <div class="col-12 text-center p-5">
                <p class="text-muted">No templates available to display.</p>
            </div>
        `);
    }

    // Templates will be loaded via AJAX when needed
    let templates = [];
    const $previewRow = $('#templatePreviews');
    const $previewSection = $('#preview-section');
    const $templatesContainer = $('#templates-container');
    const $previewImage = $('#preview-image');
    const $viewIndicator = $('#view-indicator'); // Reference to the indicator text
    let currentView = 'front'; // Default view is front

    // Initialize a flag to track if templates have been loaded
    let templatesLoaded = false;

    // Expose a function to load templates if needed (can be called from parent page)
    window.loadTemplatesIfNeeded = function(currentStepParam, forceReload = false) {
        // Check if we're on step 1 (if parameter is provided)
        if (currentStepParam !== undefined && currentStepParam !== 1) {
            console.log("Not loading templates because we're not on step 1");
            return false;
        }

        // If force reload is requested, reset the loaded flag and templates array
        if (forceReload) {
            console.log("Force reload requested, clearing template cache");
            templatesLoaded = false;
            templates = [];
        }

        if (!templatesLoaded || templates.length === 0) {
            loadTemplatesViaAjax(forceReload);
            templatesLoaded = true;
            return true; // Templates were loaded
        }
        return false; // Templates were already loaded
    };

    // Function to load templates via AJAX
    function loadTemplatesViaAjax(forceReload = false) {
        // If we already have templates and we're not forcing a reload, skip loading
        if (templates.length > 0 && !forceReload) {
            console.log("Templates already loaded, skipping reload");
            renderTemplates();
            return;
        }

        // Show loading indicator
        $previewRow.html(`
            <div class="col-12 text-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading templates...</span>
                </div>
                <p class="mt-3">Loading templates...</p>
            </div>
        `);

        // Get the id_card_for value from the ID selector
        let idCardFor = '';

        // Try to get it from the parent window's ID selector if this is in an iframe
        try {
            if (window.parent && window.parent.document && window.parent.document.getElementById('idCardFor')) {
                idCardFor = window.parent.document.getElementById('idCardFor').value;
            }
        } catch (e) {
            console.log('Could not access parent window:', e);
        }

        // If not found in parent, try to get it from the current document
        if (!idCardFor && document.getElementById('idCardFor')) {
            idCardFor = document.getElementById('idCardFor').value;
        }

        // If still not found, try to get it from order details
        if (!idCardFor) {
            idCardFor = '<?= isset($order_details->id_card_for) ? $order_details->id_card_for : "" ?>';
        }

        // If still not available, try to get it from URL parameters
        if (!idCardFor) {
            const urlParams = new URLSearchParams(window.location.search);
            idCardFor = urlParams.get('id_card_for') || '';
        }

        // Make AJAX request to get templates
        $.ajax({
            url: '<?php echo site_url('idcards/Idcards_controller/getTemplates'); ?>',
            type: 'GET',
            data: { id_card_for: idCardFor },
            success: function(response) {
                templates = JSON.parse(response);
                console.log(templates);

                // If we have a previously selected template ID, make sure it's still valid
                if (selectedTemplateId !== null) {
                    const templateExists = templates.some(template => parseInt(template.id) === parseInt(selectedTemplateId));

                    if (!templateExists) {
                        selectedTemplateId = null;
                    }
                }

                // Clear the loading indicator
                $previewRow.empty();

                // Render templates
                renderTemplates();
            },
            error: function() {
                $previewRow.html(`
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <p>Failed to load templates. Please try again.</p>
                            <button class="btn btn-primary mt-2" onclick="loadTemplatesViaAjax()">
                                <i class="fas fa-sync-alt"></i> Retry
                            </button>
                        </div>
                    </div>
                `);
            }
        });
    }

    // Add custom jQuery selector for text content if not already available
    if (!$.expr[':'].contains) {
        $.expr[':'].contains = function(a, i, m) {
            return $(a).text().indexOf(m[3]) >= 0;
        };
    }

    // Initialize variables for template selection
    // console.log('Initial selectedTemplateId:', selectedTemplateId);
    // console.log('Templates:', templates);

    // We'll handle the template selection in the later part of the script
    // This section is removed to avoid duplicate initialization

    // Function to create a wrapper div for a template pair
    const renderTemplateWrapper = (id, label, size) => {
        return $(`
            <div class="template-card-wrapper"> <!-- Using custom class for better control -->
                <div class="template-pair border p-3" data-template-id="${id}">
                    <div class="template-header d-flex justify-content-between align-items-center mb-3">
                        <h6 class="template-title mb-0">${label}</h6>
                        <span class="badge badge-info">
                            ${size ? size.charAt(0).toUpperCase() + size.slice(1).toLowerCase() : 'Unknown Size'}
                            </span>
                    </div>
                    <div id="${id}" class="template-preview-container d-flex justify-content-around">
                        <div class="preview-card front-preview position-relative"></div>
                    </div>
                </div>
            </div>
        `);
    };

    // Function to render templates
    function renderTemplates() {
        templates.forEach((template, index) => {
            const templateId = `templatePair_${index}`;
            const size = template.size || 'Unknown Size'; // Safely access the size property
            const $templateWrapper = renderTemplateWrapper(templateId, template.name || `Template ${index + 1}`, size);
            $previewRow.append($templateWrapper);

            // Highlight the selected template if it matches the selectedTemplateId
            if (selectedTemplateId !== null && parseInt(template.id) === parseInt(selectedTemplateId)) {
                // console.log('Highlighting template:', templateId, 'with ID:', template.id);
                $(`#${templateId}`).closest('.template-pair').addClass('selected-template');
            }

            // Render front preview
            if (template.front_design) {
                try {
                    let frontDesign;
                    if (typeof template.front_design === 'string') {
                        frontDesign = JSON.parse(template.front_design);
                    } else {
                        frontDesign = template.front_design;
                    }

                    // Ensure design has required structure
                    if (!frontDesign.styles) frontDesign.styles = {};
                    if (!frontDesign.elements) frontDesign.elements = [];

                    // console.log(`Rendering front preview for template ${index + 1}:`, frontDesign);
                    renderPreview(`#${templateId} .front-preview`, frontDesign);
                } catch (e) {
                    console.error(`Error parsing front design for template ${index + 1}`, e);
                    // Add a placeholder message in the preview
                    $(`#${templateId} .front-preview`).html('<div style="text-align:center;padding:10px;">Preview not available</div>');
                }
            } else {
                // Add a placeholder message if no design
                $(`#${templateId} .front-preview`).html('<div style="text-align:center;padding:10px;">No front design</div>');
            }

            // Render back preview
            if (template.back_design) {
                try {
                    let backDesign;
                    if (typeof template.back_design === 'string') {
                        backDesign = JSON.parse(template.back_design);
                    } else {
                        backDesign = template.back_design;
                    }

                    // Ensure design has required structure
                    if (!backDesign.styles) backDesign.styles = {};
                    if (!backDesign.elements) backDesign.elements = [];

                    // console.log(`Rendering back preview for template ${index + 1}:`, backDesign);
                    renderPreview(`#${templateId} .back-preview`, backDesign);
                } catch (e) {
                    console.error(`Error parsing back design for template ${index + 1}`, e);
                    // Add a placeholder message in the preview
                    $(`#${templateId} .back-preview`).html('<div style="text-align:center;padding:10px;">Preview not available</div>');
                }
            } else {
                // Add a placeholder message if no design
                $(`#${templateId} .back-preview`).html('<div style="text-align:center;padding:10px;">No back design</div>');
            }

            // Add click event to toggle preview
            $(`#${templateId}`).on('click', function (e) {
                e.preventDefault(); // Prevent default action
                e.stopPropagation(); // Stop event propagation

                // console.log('Template clicked:', templateId);
                // console.log('Current originalSelectedTemplateId:', originalSelectedTemplateId);

                // Reset position when selecting a template
                resetPreviewPosition();

                // Check if this is the currently selected template
                const isCurrentlySelected = originalSelectedTemplateId === templateId;
                // console.log('Is currently selected:', isCurrentlySelected);

                if (isCurrentlySelected) {
                    // console.log('Same template clicked - closing preview');

                    // Remove selected class from all templates
                    $('.template-pair').removeClass('selected-template');

                    // Hide the preview section
                    $('#preview-section').addClass('d-none');
                    $('#templates-container').removeClass('col-md-6').addClass('col-md-12');
                    $('#templatePreviews').removeClass('preview-open');

                    // Reset template selection
                    originalSelectedTemplateId = null;
                    selectedTemplateId = null;

                    // console.log('Preview closed, originalSelectedTemplateId reset to:', originalSelectedTemplateId);
                    return false; // Prevent further handling
                } else {
                    // console.log('Different template clicked - showing preview');

                    // Remove selected class from all templates
                    $('.template-pair').removeClass('selected-template');

                    // Add selected class to the clicked template
                    $(this).closest('.template-pair').addClass('selected-template');

                    // Show the preview section
                    $('#preview-section').removeClass('d-none');
                    $('#templates-container').removeClass('col-md-12').addClass('col-md-6');
                    $('#templatePreviews').addClass('preview-open');

                    // Update the template name in the preview title
                    $('#template-name-display').text(`- ${template.name || 'Template'}`);

                    // Reset to front view
                    currentView = 'front';
                    $viewIndicator.text('Front Side');
                    $previewImage.removeClass('back-preview').addClass('front-preview');

                    // Render the template design
                    if (template.front_design) {
                        try {
                            let frontDesign;
                            if (typeof template.front_design === 'string') {
                                frontDesign = JSON.parse(template.front_design);
                            } else {
                                frontDesign = template.front_design;
                            }

                            // Ensure design has required structure
                            if (!frontDesign.styles) frontDesign.styles = {};
                            if (!frontDesign.elements) frontDesign.elements = [];

                            // Apply template styles
                            renderPreview('#preview-image', frontDesign);

                            // Reset position after rendering to ensure proper display
                            resetPreviewPosition();
                        } catch (e) {
                            console.error('Error parsing front design for preview', e);
                            $('#preview-content').html('<div style="text-align:center;padding:20px;">Preview not available</div>');
                        }
                    } else {
                        $('#preview-content').html('<div style="text-align:center;padding:20px;">No front design available</div>');
                        resetPreviewPosition(); // Reset position even if no design
                    }

                    // Update template selection
                    originalSelectedTemplateId = templateId;
                    selectedTemplateId = parseInt(template.id);

                    // console.log('Preview shown, originalSelectedTemplateId set to:', originalSelectedTemplateId);
                    return false; // Prevent further handling
                }
            });
        });
    }

    // Function to render preview
    function renderPreview(selector, design) {
        // Get the container element
        const $container = $(selector);

        // Handle different container types
        let $content;
        const isMainPreview = selector === '#preview-image';

        if (isMainPreview) {
            // For the main preview section
            $content = $('#preview-content');
            $content.empty();
            $content.removeAttr('class').addClass('card-content');
        } else {
            // For the template selection section
            $container.empty();
            $content = $('<div>').addClass('card-content template-preview-content');
            $container.append($content);

            // Add a container div to ensure proper scaling
            $content = $('<div>').addClass('template-card-container').appendTo($content);
        }

        // Ensure the content has position: relative for proper element positioning
        $content.css('position', 'relative');

        // Set size based on template size
        if (design.styles && design.styles.size) {
            if (design.styles.size === 'portrait') {
                $content.addClass('card-size-portrait');
            } else if (design.styles.size === 'landscape') {
                $content.addClass('card-size-landscape');
            } else if (design.styles.size === 'custom' && design.styles.customWidth && design.styles.customHeight) {
                $content.css({
                    'width': design.styles.customWidth + 'mm',
                    'height': design.styles.customHeight + 'mm'
                });
            }
        } else {
            // Default to portrait if no size is specified
            $content.addClass('card-size-portrait');
        }

        // Apply template styles if available
        if (design.styles) {
            // Apply background color
            if (design.styles.backgroundColor) {
                $content.css('background-color', design.styles.backgroundColor);
            } else {
                // Default white background
                $content.css('background-color', '#ffffff');
            }

            // Apply border styles if specified
            if (design.styles.borderWidth && parseInt(design.styles.borderWidth) > 0) {
                $content.css({
                    'border': `${design.styles.borderWidth}px solid ${design.styles.borderColor || '#000000'}`
                });
            }

            // Apply border radius if specified
            if (design.styles.borderRadius && parseInt(design.styles.borderRadius) > 0) {
                $content.css({
                    'border-radius': `${design.styles.borderRadius}px`
                });
            }

            // Apply box shadow if specified
            if (design.styles.boxShadow) {
                $content.css({
                    'box-shadow': design.styles.boxShadow
                });
            }

            // Apply any custom CSS if specified
            if (design.styles.customCSS) {
                try {
                    // Parse the custom CSS and apply it
                    const customStyles = JSON.parse(design.styles.customCSS);
                    for (const prop in customStyles) {
                        $content.css(prop, customStyles[prop]);
                    }
                } catch (e) {
                    console.warn('Error parsing custom CSS:', e);
                }
            }
        } else {
            // Default white background if no styles
            $content.css('background-color', '#ffffff');
        }

        // Render elements
        if (design.elements && design.elements.length) {
            design.elements.forEach(element => {
                // Skip if missing essential properties
                if (!element || !element.type) return;

                const $element = $('<div>').addClass('element').attr('data-type', element.type);

                // Set position and size
                $element.css({
                    'left': element.x + 'px',
                    'top': element.y + 'px',
                    'width': element.width + 'px',
                    'height': element.height + 'px',
                    'z-index': element.zIndex || 1
                });

                // Create element content based on type
                const $elementContent = $('<div>').addClass('element-content');
                // console.log(element);

                switch (element.type) {
                    case 'text':
                        $element.addClass('text-element');
                        $elementContent.css({
                            'color': element.properties.color || '#000',
                            'font-family': element.properties.font || 'Arial',
                            'font-size': `${element.properties.size}px` || '12px',
                            'font-weight': element.properties.bold ? 'bold' : 'normal',
                            'font-style': element.properties.italic ? 'italic' : 'normal',
                            'text-decoration': element.properties.underline ? 'underline' : 'none',
                            'text-align': element.properties.align || 'left',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': element.properties.align === 'center' ? 'center' :
                                element.properties.align === 'right' ? 'flex-end' : 'flex-start'
                        })
                        .text(element.properties.text || '');

                        // Apply text shadow if specified
                        if (element.properties.textShadow) {
                            $elementContent.css('text-shadow', element.properties.textShadow);
                        }

                        // Apply letter spacing if specified
                        if (element.properties.letterSpacing) {
                            $elementContent.css('letter-spacing', element.properties.letterSpacing + 'px');
                        }

                        // Apply line height if specified
                        if (element.properties.lineHeight) {
                            $elementContent.css('line-height', element.properties.lineHeight);
                        }
                        break;

                    case 'field':
                        $element.addClass('field-element');
                        $elementContent.css({
                            'color': element.properties.color || '#3273dc',
                            'font-family': element.properties.font || 'Arial',
                            'font-size': `${element.properties.size}px` || '12px',
                            'font-weight': element.properties.bold ? 'bold' : 'normal',
                            'font-style': element.properties.italic ? 'italic' : 'normal',
                            'text-decoration': element.properties.underline ? 'underline' : 'none',
                            'text-align': element.properties.align || 'left',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': element.properties.align === 'center' ? 'center' :
                                element.properties.align === 'right' ? 'flex-end' : 'flex-start',
                            'background-color': element.properties.backgroundColor || 'transparent'
                        });

                        // Handle field display with placeholder text
                        const fieldText = element.properties.fieldName || element.properties.text || '[[FIELD]]';

                        // Handle field display with placeholder text
                        if (fieldText === '[[PHOTO]]') {
                            // For photo field, just show the placeholder text in template selection
                            // The actual Mickey Mouse image will only be shown in the preview section
                            $elementContent.text(fieldText);

                            // Add a light gray background to indicate it's a photo field
                            if (!isMainPreview) {
                                $elementContent.css({
                                    'background-color': '#f5f5f5',
                                    'border': '1px dashed #ccc',
                                    'display': 'flex',
                                    'align-items': 'center',
                                    'justify-content': 'center',
                                    'font-style': 'italic',
                                    'color': '#888'
                                });
                            }
                        } else {
                            // For all other fields, set the text
                            $elementContent.text(fieldText);
                        }

                        // Center align name and designation fields
                        if (fieldText === '[[NAME]]' || fieldText === '[[DESIGNATION]]') {
                            $elementContent.css({
                                'text-align': 'center',
                                'justify-content': 'center',
                                'font-weight': 'bold' // Make these fields bold for better visibility
                            });

                            // If it's the name field, make it slightly larger
                            if (fieldText === '[[NAME]]') {
                                const currentSize = parseInt(element.properties.size) || 14;
                                $elementContent.css('font-size', `${currentSize + 2}px`);
                            }
                        }

                        // Add stroke if specified
                        if (element.properties.strokeWidth && parseFloat(element.properties.strokeWidth) > 0) {
                            $elementContent.css({
                                '-webkit-text-stroke': `${element.properties.strokeWidth}px ${element.properties.strokeColor || '#000000'}`,
                                'text-stroke': `${element.properties.strokeWidth}px ${element.properties.strokeColor || '#000000'}`
                            });
                        }

                        // Add border if specified
                        if (element.properties.borderWidth && parseInt(element.properties.borderWidth) > 0) {
                            $elementContent.css({
                                'border': `${element.properties.borderWidth}px solid ${element.properties.borderColor || '#000000'}`
                            });
                        }

                        // Apply border radius if specified
                        if (element.properties.borderRadius && parseInt(element.properties.borderRadius) > 0) {
                            $elementContent.css({
                                'border-radius': `${element.properties.borderRadius}px`
                            });
                        }

                        // Apply padding if specified
                        if (element.properties.padding) {
                            $elementContent.css('padding', element.properties.padding + 'px');
                        }
                        break;

                    case 'shape':
                        $element.addClass('shape-element');
                        if (element.properties.shapeType === 'rectangle') {
                            // Add a dummy Mickey Mouse image inside the rectangle shape
                            const $dummyImage = $('<img>', {
                                src: '<?= base_url("assets/img/icons/profile.png") ?>',
                                alt: 'profile pic',
                                class: 'dummy-shape-image'
                            });

                            $elementContent.addClass('shape-rectangle')
                                .css({
                                    'background-color': element.properties.fillColor || '#ffffff',
                                    'border': `${element.properties.borderWidth || 1}px solid ${element.properties.borderColor || '#000000'}`,
                                    'display': 'flex',
                                    'align-items': 'center',
                                    'justify-content': 'center',
                                    'overflow': 'hidden'
                                });

                            $elementContent.append($dummyImage);

                            // Style the dummy image
                            $dummyImage.css({
                                'width': '90%',
                                'height': '90%',
                                'object-fit': 'contain',
                                'border-radius': '4px'
                            });

                            // Apply border radius for rectangle if specified
                            if (element.properties.borderRadius && parseInt(element.properties.borderRadius) > 0) {
                                $elementContent.css('border-radius', element.properties.borderRadius + 'px');
                            }
                        } else if (element.properties.shapeType === 'circle') {
                            // Add a dummy Mickey Mouse image inside the circle shape
                            const $dummyImage = $('<img>', {
                                src: '<?= base_url("assets/img/icons/profile.png") ?>',
                                alt: 'Profile pic',
                                class: 'dummy-shape-image'
                            });

                            $elementContent.addClass('shape-circle')
                                .css({
                                    'background-color': element.properties.fillColor || '#ffffff',
                                    'border': `${element.properties.borderWidth || 1}px solid ${element.properties.borderColor || '#000000'}`,
                                    'border-radius': '50%',
                                    'display': 'flex',
                                    'align-items': 'center',
                                    'justify-content': 'center',
                                    'overflow': 'hidden'
                                });

                            $elementContent.append($dummyImage);

                            // Style the dummy image
                            $dummyImage.css({
                                'width': '90%',
                                'height': '90%',
                                'object-fit': 'cover',
                                'border-radius': '50%'
                            });
                        } else if (element.properties.shapeType === 'line') {
                            $elementContent.addClass('shape-line')
                                .css({
                                    'border-top': `${element.properties.borderWidth || 1}px solid ${element.properties.borderColor || '#000000'}`,
                                    'height': '0'
                                });
                        }

                        // Apply opacity if specified
                        if (element.properties.opacity !== undefined && element.properties.opacity !== null) {
                            $elementContent.css('opacity', element.properties.opacity);
                        }

                        // Apply box shadow if specified
                        if (element.properties.boxShadow) {
                            $elementContent.css('box-shadow', element.properties.boxShadow);
                        }
                        break;

                    case 'image':
                        $element.addClass('image-element');
                        if (element.properties.src) {
                            const $img = $('<img>').addClass('element-content').attr('src', element.properties.src);

                            // Apply border radius if specified
                            if (element.properties.borderRadius && parseInt(element.properties.borderRadius) > 0) {
                                $img.css('border-radius', element.properties.borderRadius + 'px');
                            }

                            // Apply object-fit if specified
                            if (element.properties.objectFit) {
                                $img.css('object-fit', element.properties.objectFit);
                            } else {
                                $img.css('object-fit', 'contain'); // Default to contain
                            }

                            // Apply border if specified
                            if (element.properties.borderWidth && parseInt(element.properties.borderWidth) > 0) {
                                $img.css('border', `${element.properties.borderWidth}px solid ${element.properties.borderColor || '#000000'}`);
                            }

                            // Apply opacity if specified
                            if (element.properties.opacity !== undefined && element.properties.opacity !== null) {
                                $img.css('opacity', element.properties.opacity);
                            }

                            $element.empty().append($img);
                        } else {
                            $elementContent.css({
                                'background-color': '#f0f0f0',
                                'border': '1px dashed #ccc'
                            });
                            console.warn('Image element missing src property');
                        }
                        break;
                }

                if (element.type !== 'image' || !element.properties.src) {
                    $element.append($elementContent);
                }

                // Apply element-specific styles if available
                if (element.properties && element.properties.customCSS) {
                    try {
                        const customStyles = JSON.parse(element.properties.customCSS);
                        for (const prop in customStyles) {
                            $element.css(prop, customStyles[prop]);
                        }
                    } catch (e) {
                        console.warn(`Error parsing custom CSS for element ${element.id}:`, e);
                    }
                }

                // Remove any interactive behaviors for preview
                $element.css('cursor', 'default');
                $element.removeClass('selected');

                // Add to content container
                $content.append($element);
            });

            // If this is the main preview, apply center alignment to name and designation fields
            if (isMainPreview) {
                centerAlignNameAndDesignation();
            }
        }
    }

    // Function to center align name and designation fields and add Mickey Mouse image in preview section
    function centerAlignNameAndDesignation() {
        // Find all field elements in the preview
        $('#preview-content .field-element').each(function() {
            const fieldText = $(this).find('.element-content').text();
            console.log('Field text:', fieldText);

            // Check if it's a name or designation field
            if (fieldText === '[[NAME]]' || fieldText === '[[DESIGNATION]]') {
                $(this).find('.element-content').css({
                    'text-align': 'center',
                    'justify-content': 'center',
                    'font-weight': 'bold'
                });

                // If it's the name field, make it slightly larger
                if (fieldText === '[[NAME]]') {
                    const currentSize = parseInt($(this).find('.element-content').css('font-size')) || 14;
                    $(this).find('.element-content').css('font-size', `${currentSize + 2}px`);
                }
            }

            // Check if it's a photo field and add Mickey Mouse image (only in preview section)
            if (fieldText === '[[PHOTO]]') {
                // Create a sample profile image
                const $photoContainer = $(this).find('.element-content');
                $photoContainer.empty(); // Clear the text

                // Add Mickey Mouse image from assets/img folder
                const $samplePhoto = $('<img>', {
                    src: '<?= base_url("assets/img/icons/profile.png") ?>',
                    alt: 'Profile pic',
                    class: 'sample-profile-image'
                });

                $photoContainer.append($samplePhoto);

                // Style the photo container and image
                $photoContainer.css({
                    'padding': '0',
                    'overflow': 'hidden',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center'
                });

                $samplePhoto.css({
                    'width': '100%',
                    'height': '100%',
                    'object-fit': 'contain', // Changed to contain to preserve aspect ratio
                    'border-radius': '4px'
                });
            }
        });
    }

    // Function to reset the preview image position
    function resetPreviewPosition() {
        $previewImage.css({
            left: '0px',
            top: '0px',
            position: 'relative',
            transform: 'scale(1)' // Reset zoom to default
        });
        $('#zoom-range').val(100);
        $('#zoom-percentage').text('100%');

        // Make sure the content is centered
        $('#preview-content').css({
            position: 'relative',
            margin: '0 auto',
            transform: 'scale(0.8)', // Maintain the scale to ensure entire template is visible
            transformOrigin: 'center',
            overflow: 'visible' // Ensure elements aren't clipped
        });

        // Apply center alignment to name and designation fields
        centerAlignNameAndDesignation();

        // Re-render the current template if one is selected
        if (originalSelectedTemplateId) {
            const selectedTemplate = templates.find(t => `templatePair_${templates.indexOf(t)}` === originalSelectedTemplateId);
            if (selectedTemplate) {
                try {
                    let design;
                    if (currentView === 'front' && selectedTemplate.front_design) {
                        design = typeof selectedTemplate.front_design === 'string' ?
                            JSON.parse(selectedTemplate.front_design) : selectedTemplate.front_design;
                    } else if (currentView === 'back' && selectedTemplate.back_design) {
                        design = typeof selectedTemplate.back_design === 'string' ?
                            JSON.parse(selectedTemplate.back_design) : selectedTemplate.back_design;
                    }

                    if (design) {
                        // Ensure design has required structure
                        if (!design.styles) design.styles = {};
                        if (!design.elements) design.elements = [];

                        // Apply template styles without re-rendering elements
                        if (design.styles) {
                            const $content = $('#preview-content');

                            // Apply background color
                            if (design.styles.backgroundColor) {
                                $content.css('background-color', design.styles.backgroundColor);
                            }

                            // Apply border styles if specified
                            if (design.styles.borderWidth && parseInt(design.styles.borderWidth) > 0) {
                                $content.css({
                                    'border': `${design.styles.borderWidth}px solid ${design.styles.borderColor || '#000000'}`
                                });
                            }

                            // Apply border radius if specified
                            if (design.styles.borderRadius && parseInt(design.styles.borderRadius) > 0) {
                                $content.css({
                                    'border-radius': `${design.styles.borderRadius}px`
                                });
                            }
                        }
                    }
                } catch (e) {
                    console.error('Error applying template styles during reset:', e);
                }
            }
        }
    }

    // Force show preview section if selectedTemplateId is set
    if (selectedTemplateId !== null) {
        // console.log('Initial load with selectedTemplateId:', selectedTemplateId);

        // Find the template with the matching ID
        const selectedTemplate = templates.find(template => parseInt(template.id) === parseInt(selectedTemplateId));

        if (selectedTemplate) {
            // Find the template index
            const templateIndex = templates.findIndex(template => parseInt(template.id) === parseInt(selectedTemplateId));
            if (templateIndex !== -1) {
                originalSelectedTemplateId = `templatePair_${templateIndex}`;
                // console.log('Setting originalSelectedTemplateId to:', originalSelectedTemplateId);
            }

            setTimeout(() => {
                // Show the preview section using direct DOM ID selectors
                $('#preview-section').removeClass('d-none');
                $('#templates-container').removeClass('col-md-12').addClass('col-md-6');
                $('#templatePreviews').addClass('preview-open');

                // Update the template name in the preview title
                $('#template-name-display').text(`- ${selectedTemplate.name || 'Template'}`);

                // Make sure the correct template is highlighted
                $('.template-pair').removeClass('selected-template');

                if (originalSelectedTemplateId) {
                    $(`#${originalSelectedTemplateId}`).closest('.template-pair').addClass('selected-template');
                }

                // Render the preview
                currentView = 'front'; // Reset to front view
                $viewIndicator.text('Front Side'); // Update indicator text
                $previewImage.removeClass('back-preview').addClass('front-preview');
                resetPreviewPosition(); // Ensure proper positioning

                if (selectedTemplate.front_design) {
                    try {
                        let frontDesign;
                        if (typeof selectedTemplate.front_design === 'string') {
                            frontDesign = JSON.parse(selectedTemplate.front_design);
                        } else {
                            frontDesign = selectedTemplate.front_design;
                        }

                        // Ensure design has required structure
                        if (!frontDesign.styles) frontDesign.styles = {};
                        if (!frontDesign.elements) frontDesign.elements = [];

                        // Apply template styles
                        renderPreview('#preview-image', frontDesign);

                        // Reset position after rendering to ensure proper display
                        resetPreviewPosition();
                    } catch (e) {
                        console.error('Error parsing front design for selected template', e);
                        $('#preview-content').html('<div style="text-align:center;padding:20px;">Preview not available</div>');
                    }
                } else {
                    $('#preview-content').html('<div style="text-align:center;padding:20px;">No front design available</div>');
                    resetPreviewPosition(); // Reset position even if no design
                }
            }, 500); // Small delay to ensure DOM is ready
        }
    } else {
        // console.log('No selectedTemplateId, hiding preview section');
        // Ensure preview section is hidden on initial load if no template is selected
        $('#preview-section').addClass('d-none');
        $('#templates-container').removeClass('col-md-6').addClass('col-md-12');
        $('#templatePreviews').removeClass('preview-open');
    }

    // Navigation for preview section
    $('#prev-view').on('click', function () {
        if (currentView === 'back') {
            currentView = 'front';
            $viewIndicator.text('Front Side'); // Update indicator text
            $('#preview-label').text('Front Side');
            $previewImage.removeClass('back-preview').addClass('front-preview');
            const selectedTemplate = templates.find(t => `templatePair_${templates.indexOf(t)}` === originalSelectedTemplateId);
            if (selectedTemplate?.front_design) {
                try {
                    let frontDesign;
                    if (typeof selectedTemplate.front_design === 'string') {
                        frontDesign = JSON.parse(selectedTemplate.front_design);
                    } else {
                        frontDesign = selectedTemplate.front_design;
                    }

                    // Ensure design has required structure
                    if (!frontDesign.styles) frontDesign.styles = {};
                    if (!frontDesign.elements) frontDesign.elements = [];

                    // Apply template styles
                    renderPreview('#preview-image', frontDesign);

                    // Reset position after rendering to ensure proper display
                    resetPreviewPosition();
                } catch (e) {
                    console.error('Error parsing front design for navigation', e);
                    $('#preview-content').html('<div style="text-align:center;padding:20px;">Preview not available</div>');
                }
            } else {
                $('#preview-content').html('<div style="text-align:center;padding:20px;">No front design available</div>');
                resetPreviewPosition(); // Reset position even if no design
            }
        }
    });

    $('#next-view').on('click', function () {
        if (currentView === 'front') {
            currentView = 'back';
            $viewIndicator.text('Back Side'); // Update indicator text
            $('#preview-label').text('Back Side');
            $previewImage.removeClass('front-preview').addClass('back-preview');
            const selectedTemplate = templates.find(t => `templatePair_${templates.indexOf(t)}` === originalSelectedTemplateId);
            if (selectedTemplate?.back_design) {
                try {
                    let backDesign;
                    if (typeof selectedTemplate.back_design === 'string') {
                        backDesign = JSON.parse(selectedTemplate.back_design);
                    } else {
                        backDesign = selectedTemplate.back_design;
                    }

                    // Ensure design has required structure
                    if (!backDesign.styles) backDesign.styles = {};
                    if (!backDesign.elements) backDesign.elements = [];

                    // Apply template styles
                    renderPreview('#preview-image', backDesign);

                    // Reset position after rendering to ensure proper display
                    resetPreviewPosition();
                } catch (e) {
                    console.error('Error parsing back design for navigation', e);
                    $('#preview-content').html('<div style="text-align:center;padding:20px;">Preview not available</div>');
                }
            } else {
                $('#preview-content').html('<div style="text-align:center;padding:20px;">No back design available</div>');
                resetPreviewPosition(); // Reset position even if no design
            }
        }
    });

    // Zoom functionality
    $('#zoom-range').on('input', function () {
        const zoomValue = $(this).val();
        $('#zoom-percentage').text(`${zoomValue}%`);
        // Apply zoom to the preview image, starting from the base scale of 0.8
        const baseScale = 0.8;
        const newScale = (zoomValue / 100) * baseScale;
        $('#preview-content').css('transform', `scale(${newScale})`);
    });

    // Add move-around feature
    let isDragging = false;
    let startX, startY, initialLeft, initialTop;

    $previewImage.on('mousedown', function (e) {
        isDragging = true;
        startX = e.pageX;
        startY = e.pageY;
        const position = $previewImage.position();
        initialLeft = position.left;
        initialTop = position.top;
        $previewImage.css('cursor', 'grabbing');
    });

    $(document).on('mousemove', function (e) {
        if (isDragging) {
            const dx = e.pageX - startX;
            const dy = e.pageY - startY;
            $previewImage.css({
                left: initialLeft + dx + 'px',
                top: initialTop + dy + 'px',
                position: 'relative' // Ensure the image can move
            });
        }
    });

    $(document).on('mouseup', function () {
        if (isDragging) {
            isDragging = false;
            $previewImage.css('cursor', 'grab');
        }
    });
});
</script>

<style>
.template-pair {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 280px; /* Ensure enough height for the template */
}

.template-pair:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-template {
    background-color: #e6f7ff;
    border: 2px solid #1890ff;
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(24, 144, 255, 0.2);
    position: relative;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
}

/* Remove the dot indicator and use a full highlight effect instead */

.template-header {
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.template-title {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.template-preview-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 200px; /* Increased height for more space */
    overflow: visible; /* Changed from hidden to visible to prevent cropping */
    margin-top: 10px; /* Added margin for better spacing */
}

.preview-card {
    background-color: #ffffff;
    width: 48%; /* Slightly wider for better spacing */
    aspect-ratio: 1 / 1.5;
    overflow: visible; /* Changed from hidden to visible to prevent cropping */
    position: relative;
    display: flex; /* Ensure child elements are properly aligned */
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd; /* Add a border for better visibility */
    border-radius: 8px; /* Add rounded corners */
    padding: 10px; /* Increased padding for more space */
    min-height: 180px; /* Increased minimum height for better visibility */
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.image-element {
    width: 100%; /* Ensure the image spans the full width of the card */
    height: 100%; /* Ensure the image spans the full height of the card */
    position: absolute; /* Position the image absolutely within the card */
    top: 0;
    left: 0;
}

.image-element img.element-content {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Ensure the image maintains its aspect ratio */
    object-position: center; /* Center the image within the container */
}

.preview-container {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#template-name-display {
    color: #1890ff;
    font-weight: 500;
    font-style: italic;
}

.preview-box {
    width: 100%;
    max-width: 500px;
    height: 400px; /* Increased height for better visibility */
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: visible; /* Changed from hidden to visible to prevent cropping */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    margin: 0 auto; /* Center the preview box */
}

.card-preview {
    border: 1px solid #ccc;
    position: relative;
    overflow: visible; /* Changed from hidden to visible to prevent cropping */
    width: 100%;
    height: 100%;
    transform-origin: center;
    transition: transform 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9; /* Light background to match edit_template.php */
}

.card-content {
    position: relative;
    background: #fff;
}

/* Card sizes */
.card-size-portrait {
    width: 54mm;
    height: 86mm;
    margin: 0 auto;
    border: 1px solid #000;
}

.card-size-landscape {
    width: 86mm;
    height: 54mm;
    margin: 0 auto;
}

/* Specific sizing for template previews */
.template-preview-content .card-size-portrait,
.template-preview-content .card-size-landscape {
    transform-origin: center;
    position: relative;
    min-width: 54mm;
    min-height: 54mm;
}

/* Specific styling for the main preview */
#preview-content.card-size-portrait,
#preview-content.card-size-landscape {
    transform: scale(0.8); /* Scale down to ensure entire template is visible */
    transform-origin: center;
    margin: auto;
    position: relative; /* Ensure proper positioning of elements */
    overflow: visible; /* Ensure elements aren't clipped */
}

/* Container for template card to ensure proper scaling */
.template-card-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.element {
    position: absolute;
    cursor: default;
    border: 1px dashed transparent;
}

.element.selected {
    border: 1px dashed #007bff;
}

.element, .element-content {
    cursor: pointer;
    user-select: none;
}

.element .element-content {
    pointer-events: auto;
}

.element-content {
    width: 100%;
    height: 100%;
}

.text-element .element-content {
    padding: 2px;
}

.shape-rectangle {
    background-color: #ffffff;
    border: 1px solid #000000;
}

.shape-circle {
    background-color: #ffffff;
    border: 1px solid #000000;
    border-radius: 50%;
}

.shape-line {
    background-color: transparent;
    border-top: 1px solid #000000;
    height: 0 !important;
}

.field-element .element-content {
    color: #3273dc;
    padding: 2px;
}

/* Template preview content styling */
.template-preview-content {
    transform: scale(0.45); /* Further reduced scale to fit entire template */
    transform-origin: center;
    position: absolute;
    top: -37px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    overflow: visible !important;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    border: none; /* Remove any borders that might interfere */
}

/* Adjust scale for preview content when preview section is open */
.preview-open .template-preview-content {
    transform: scale(0.35); /* Further reduced scale when preview is open */
}

/* Ensure elements in preview are visible */
.template-preview-content .element {
    pointer-events: none;
    cursor: default;
    border: none;
}

/* Center align name and designation fields in template previews */
.template-preview-content .field-element .element-content:contains('[[NAME]]'),
.template-preview-content .field-element .element-content:contains('[[DESIGNATION]]') {
    text-align: center !important;
    justify-content: center !important;
    font-weight: bold !important;
}

/* Make name field slightly larger in template previews */
.template-preview-content .field-element .element-content:contains('[[NAME]]') {
    font-size: 1.15em !important;
}

/* Ensure image elements display properly in preview */
.template-preview-content .image-element img {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
}

.field-element {
    min-width: 50px !important;
    min-height: 20px !important;
}

.field-element .element-content {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.navigation-arrows {
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-arrow {
    background: #ffffff;
    border: 1px solid #ddd;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    color: #333;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease-in-out;
}

.nav-arrow:hover {
    background-color: #f0f0f0;
}

#view-indicator {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.zoom-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.zoom-bar input[type="range"] {
    width: 150px;
}

h5.text-center {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

/* Template card wrapper styling */
.template-card-wrapper {
    flex: 0 0 33.333%;
    max-width: 33.333%;
    padding: 0 10px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    float: left;
}

/* Add highlight effect to the entire wrapper when template is selected */
.template-card-wrapper .selected-template {
    background-color: #e6f7ff;
    border: 2px solid #1890ff;
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);
    transform: translateY(-5px);
}

/* Add a glow effect on hover */
.template-card-wrapper:hover .template-pair {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* Show only 2 cards per row when preview is open */
.preview-open .template-card-wrapper {
    flex: 0 0 50%;
    max-width: 50%;
}

/* Adjust for medium screens */
@media (max-width: 992px) {
    .template-card-wrapper {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .preview-open .template-card-wrapper {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Adjust for smaller screens */
@media (max-width: 768px) {
    .template-card-wrapper {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Ensure the row is properly cleared */
#templatePreviews {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px; /* Compensate for the padding in template-card-wrapper */
}

/* Keep template previews visible when preview section is open, but make them smaller */
.preview-open .template-preview-container {
    height: 150px; /* Reduced height */
    margin-top: 5px;
}

.preview-open .preview-card {
    width: 40%; /* Slightly narrower */
    min-height: 120px; /* Reduced minimum height */
}

/* Adjust template pairs when preview is open */
.preview-open .template-pair {
    min-height: 220px; /* Increased to accommodate the preview cards */
    padding: 10px;
    justify-content: flex-start;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Enhance hover effect for template pairs when preview is open */
.preview-open .template-pair:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Enhance selected template when preview is open */
.preview-open .selected-template {
    background-color: #e6f7ff;
    border: 2px solid #1890ff;
    box-shadow: 0 3px 10px rgba(24, 144, 255, 0.2);
    transform: translateY(-5px);
    animation: pulse 2s infinite;
}

/* Enhance header styling when preview is open */
.preview-open .template-header {
    padding: 5px;
    margin-bottom: 0;
}

/* Make template title more prominent when preview is open */
.preview-open .template-title {
    font-weight: bold;
    color: #333;
    max-width: 80%;
}

/* Style the badge when preview is open */
.preview-open .badge {
    background-color: #1890ff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* Style name and designation fields in the main preview */
#preview-content .field-element .element-content:contains('[[NAME]]'),
#preview-content .field-element .element-content:contains('[[DESIGNATION]]') {
    text-align: center !important;
    justify-content: center !important;
    font-weight: bold !important;
    width: 100% !important;
    display: flex !important;
}

/* Make name field slightly larger and more prominent */
#preview-content .field-element .element-content:contains('[[NAME]]') {
    font-size: 1.15em !important;
    color: #1890ff !important;
}

/* Style designation field */
#preview-content .field-element .element-content:contains('[[DESIGNATION]]') {
    color: #333333 !important;
    font-style: italic;
}

/* Style sample profile image - only used in preview section */
.sample-profile-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Style for dummy images in shape elements */
.dummy-shape-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>