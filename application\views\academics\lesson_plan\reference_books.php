<ul class="breadcrumb">
  <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
  <li class="active">Manage Reference Books</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Reference Books
                    </h3>
                </div>
                <div class="col-md-3 pr-0">
                    <a href="" class="new_circleShape_res" style="background-color: #fe970a; float: right;"
                        data-toggle="modal" data-target="#addBook-modal">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <div class="row">
                <div class="col-md-2">
                    <label class="col-md-12" for="gradeView" style="font-size: 14px;">Class</label>
                    <div class="col-md-12">
                        <select class="form-control" id="gradeView" name="grade">
                            <option value=''>Select Class</option>
                            <?php
                              foreach ($grades as $cs => $cl) { ?>
                              <option id="<?= $cl->id ?>" value="<?= $cl->id ?>">
                                  <?= $cl->class_name ?>
                              </option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" id="get" class="btn btn-primary" onclick="getData()">Get Books</button>
                </div>
            </div>
            <div class="mt-3" id="displayResourcePanel">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addBook-modal" tabindex="-1" role="dialog" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="margin-top: 2% !important; margin: auto; width: 50%;">
            <div class="modal-header">
                <h4 class="modal-title" id="modalHeader">Add New Reference Book</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetForm()">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data" method="post" id="book-form" data-parsley-validate="" class="form-horizontal">
                    <div class="card-body p-0" id="resource-data">
                        <div class="col-md-12 pb-3">
                            <div class="form-group">
                                <label class="col-md-3">Class <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select class="form-control" id="selectClass" onchange="getSubjects()" name="class" required>
                                        <option value=''>Select Class</option>
                                        <?php
                                          foreach ($grades as $cs => $cl) { ?>
                                            <option id="class_<?= $cl->id ?>" value="<?= $cl->id ?>">
                                                <?= $cl->class_name ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                    <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                    <span id="class_error"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3">Subject <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <select class="form-control" id="selectSubject" name="subject" required>
                                        <option value=''>Select Subject</option>
                                    </select>
                                    <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                    <span id="subject_error"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="name" class="col-md-3">Book <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input class="form-control" placeholder="Enter Book Name" name="book_name" class="form-control" id="book_name" type="text" required="" maxlength="100" data-parsley-error-message="Book Name cannot be empty" />
                                    <span id="book_name_error"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="name" class="col-md-3">Author <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input class="form-control" placeholder="Enter Author Name" name="book_author" class="form-control" id="book_author" type="text" required="" maxlength="100" data-parsley-error-message="Author Name be empty" />
                                    <span id="book_author_error"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="name" class="col-md-3">Publisher <font color="red">*</font></label>
                                <div class="col-md-10">
                                    <input class="form-control" placeholder="Enter Publisher Name" name="book_publisher" class="form-control" id="book_publisher" type="text" required="" maxlength="100" data-parsley-error-message="Publisher Name cannot be empty" />
                                    <span id="book_publisher_error"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" data-dismiss="modal" onclick="resetForm()">Close</button>
                <button class="btn btn-primary mt-0" type="button" onclick="add_ref_book()">Submit </button>
            </div>
        </div>
    </div>
</div>

  <style type="text/css">
    .new_circleShape_res {
      padding: 8px;
      border-radius: 50% !important;
      color: white !important;
      font-size: 22px;
      height: 3.2rem !important;
      width: 3.2rem !important;
      text-align: center;
      vertical-align: middle;
      border: none !important;
      box-shadow: 0px 3px 7px #ccc;
      line-height: 1.7rem !important;
    }

    .widthadjust {
      width: 600px;
      margin: auto;
    }

    .editable,
    .book-editable,
    .author-editable,
    .publisher-editable,
    .select-editable,
    .select-editable1 {
      cursor: pointer;
      position: relative;
      margin-left: 5px;
    }

    .editable:hover,
    .book-editable:hover,
    .author-editable:hover,
    .publisher-editable:hover,
    .select-editable:hover,
    .select-editable1:hover {
      font-weight: 700;
    }

    .editable::before,
    .book-editable::before,
    .author-editable::before,
    .publisher-editable::before,
    .select-editable::before,
    .select-editable1::before {
      font-family: "FontAwesome";
      content: "\f040";
      display: inline-block;
      padding-right: 5px;
      vertical-align: middle;
      font-weight: 900;
      position: absolute;
      right: 5px;
    }

    .row {
      margin-left: -20px;
    }
  </style>
  <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/jeditor/editable.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script language="javascript">

    function resetForm(){
      const fieldsToClear = [
          // 'selectClass',
          // 'selectSubject',
          'book_name',
          'book_author',
          'book_publisher'
      ];

      const errorFields = [
          'class_error',
          'subject_error',
          'book_name_error',
          'book_author_error',
          'book_publisher_error'
      ];

      // Clear input values
      fieldsToClear.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
              element.value = '';
          }
      });

      // Clear error messages
      errorFields.forEach(id => {
          const errorElement = document.getElementById(id);
          if (errorElement) {
              errorElement.innerHTML = '';
          }
      });
    }

    $(document).ready(function () {
      setup_page();
    });

    function setup_page() {
      var class_id = _get_cookie('class_id');
      if (class_id > 0) {
        document.getElementById(`${class_id}`).selected = true;
        document.getElementById(`class_${class_id}`).selected = true;
        getData();
        getSubjects();

      }
    }

    function enableButton() {
      $("#get").prop('disabled', false);
    }

    function add_ref_book() {

      var formData = new FormData();

      var valid = 1;

      var class_id = $('#selectClass').val();
      var subject_id = $('#selectSubject').val();

      var book_name = document.getElementById('book_name').value;
      var book_author = document.getElementById('book_author').value;
      var book_publisher = document.getElementById('book_publisher').value;


      var class_error = document.getElementById('class_error');
      var subject_error = document.getElementById('subject_error');
      var name_error = document.getElementById('book_name_error');
      var author_error = document.getElementById('book_author_error');
      var publisher_error = document.getElementById('book_publisher_error');

      class_error.innerHTML = '';
      subject_error.innerHTML = '';
      name_error.innerHTML = '';
      author_error.innerHTML = '';
      publisher_error.innerHTML = '';
      
      if (class_id != '') {
        formData.append('class_id', class_id);
      } else {
        valid = 0;
        class_error.innerHTML = 'Class is required';
      }
      if (subject_id != '') {
        formData.append('subject_id', subject_id);
      } else {
        valid = 0;
        subject_error.innerHTML = 'Subject is required';
      }
      if (book_name) {
        formData.append('book_name', book_name);
      } else {
        valid = 0;
        name_error.innerHTML = 'Name is required';
      }
      if (book_author) {
        formData.append('book_author', book_author);
      } else {
        valid = 0;
        author_error.innerHTML = 'Author Name is required';
      }
      if (book_publisher) {
        formData.append('book_publisher', book_publisher);
      } else {
        valid = 0;
        publisher_error.innerHTML = 'Publisher Name is required';
      }

      if (!valid) return false;
      sendBookData(formData);
    }

    function sendBookData(formData) {
      $.ajax({
        url: '<?php echo site_url('academics/lesson_plan/save_book_data'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
          try {
            const parsedData = JSON.parse(data);
            if (parsedData.status === 'success') {
              $("#addBook-modal").modal('hide');
              $('#selectSubject').html("<option value=''>Select Subject</option>");
              document.getElementById("book-form").reset();

              Swal.fire({
                icon: 'success',
                title: 'Success',
                text: parsedData.message,
              });

              setup_page();

            } else if (parsedData.status === 'error') {
              let iconType = (parsedData.code == -4) ? 'error' : 'warning';

              Swal.fire({
                icon: iconType,
                title: 'Duplicate Entry',
                text: parsedData.message,
              });
            }
          } catch (e) {
            Swal.fire({
              icon: 'error',
              title: 'Parsing Error',
              text: 'Unable to process server response.',
            });
          }
        },
        error: function () {
          Swal.fire({
            icon: 'error',
            title: 'Server Error',
            text: 'Something went wrong while connecting to the server.',
          });
        }
      });
    }

    function getData() {
      var grade = $('#gradeView').val();
      if (grade < 1) {
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Please Select Class',
        });
        return;
      }
      else {
        _set_cookie('class_id', grade);
        viewAllBooksByGrade(grade);
        document.getElementById(`class_${grade}`).selected = true;
        getSubjects();
      }
    }

    function getSubjects() {
      var class_id = $('#selectClass').val();
      if(class_id == ''){
        html = `<option value=''>Select Subject</option>`;
        $('#selectSubject').html(html);
        return;
      }
      $.ajax({
        url: '<?php echo site_url('academics/lesson_plan/getSubjectsFromClass') ?>',
        type: 'post',
        data: {
          'class_id': class_id,
        },
        success: function (data) {
          subject_details = $.parseJSON(data);
          var html = '';
          for (var i = 0; i < subject_details.length; i++) {
            html += `<option value ="${subject_details[i]['subject_id']}">${subject_details[i]['subject_name']}</option>`;
          }
          $('#selectSubject').html(html);

        },
        error: function (err) {
          console.log(err);
        }
      });
    }

    function viewAllBooksByGrade(grade) {
      $('.new_circleShape_res').css('pointer-events', 'none');
      $('#gradeView').prop('disabled', true);
      $('#get').prop('disabled', true).html('Please Wait...');
      $('#displayResourcePanel').html('<div class="no-data-display">Loading...</div>');
      $.ajax({
        url: '<?php echo site_url('academics/lesson_plan/viewAllBooksByGrade') ?>',
        type: 'post',
        data: {
          'grade': grade,
        },
        success: function (data) {
          $('.new_circleShape_res').css('pointer-events', 'auto');
          $('#gradeView').prop('disabled', false);
          $('#get').prop('disabled', false).html('Get Data');
          data = $.parseJSON(data);
          viewBooks = data.viewBooks;
          if(viewBooks.length == 0){
            $('#displayResourcePanel').html('<div class="no-data-display">No Data Found</div>');
            return;
          }
          $('#displayResourcePanel').html(makeResourceTable(viewBooks));
          after_call(viewBooks);
        },
        error: function (err) {
          console.log(err);
          $('.new_circleShape_res').css('pointer-events', 'auto');
          $('#gradeView').prop('disabled', false);
          $('#get').prop('disabled', false).html('Get Data');
          $('#displayResourcePanel').html('<div class="no-data-display">Something went wrong!, Please try again later.</div>');
        }
      });
    }

    function makeResourceTable(bookData) {
        var output = '';
        output += '<table id="booksTable" class="table table-bordered datatable">';
        output += '<thead>';
        output += '<tr>';
        output += '<th style="width:5%;">#</th>';
        output += '<th style="width:10%;">Subject</th>';
        output += '<th style="width:15%;" >Book</th>';
        output += '<th style="width:10%;" >Author</th>';
        output += '<th style="width:10%;">Publisher</th>';
        output += '<th style="width:10%;">Created By</th>';
        output += '<th style="width:10%;">Created On</th>';
        output += '<th style="width:15%;">Status</th>';
        output += '<th style="width:15%;">Action</th>';
        output += '</tr>';
        output += '</thead>';
        output += '<tbody>';

        for (i = 0; i < bookData.length; i++) {
          output += "<tr>";
          output += '<td>' + (i + 1) + '</td>';
          output += '<td>' + bookData[i].subject_name + '</td>';
          output += '<td id="' + bookData[i].id + '" class="book-editable">' + bookData[i].name + '</td>';
          output += '<td id="' + bookData[i].id + '" class="author-editable">' + bookData[i].authors + '</td>';
          output += '<td id="' + bookData[i].id + '" class="publisher-editable">' + bookData[i].publisher + '</td>';
          output += '<td>' + bookData[i].created_by + '</td>';
          output += '<td>' + bookData[i].created_on + '</td>';

          if (bookData[i].status == 'Active') {
            output += `<td><strong style="color:green">ACTIVE</strong></td>`;

            output += `<td><button class="btn btn-sm btn-danger" onclick=updateBookStatusToInactive('${bookData[i].id}','${bookData[i].name}') style="width:6rem;border-radius:.45rem">Deactivate</button></td>`;
          }
          else {
            output += `<td><strong style="color:red">INACTIVE</strong></td>`;

            output += `<td><button class="btn btn-sm btn-warning" onclick=updateBookStatusToActive('${bookData[i].id}','${bookData[i].name}') style="width:6rem;border-radius:.45rem">Activate</button></td>`;
          }
          output += '</tr>';
        }
        output += '</tbody>';
        output += '</table>';
        return output;
    }

    function updateBookStatusToInactive(book_id, book_name) {
      bootbox.confirm({
        title: "Confirm Book Deactivation",
        message: `<h5><center>Are you sure you want to Deactivate ${book_name}?</center></h5>`,
        style: 'width=50%',
        size: 'small',
        centerVertical: true,
        className: 'widthadjust',
        width: '50%',
        buttons: {
          confirm: {
            label: "<i class='fa fa-check'></i> Yes",
            className: 'btn-success'
          },
          cancel: {
            label: "<i class='fa fa-times'></i> No",
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if (result) {

            $.ajax({
              url: '<?php echo site_url('academics/lesson_plan/updateBookStatusToInactive') ?>',
              type: 'post',
              data: {
                'book_id': book_id
              },
              success: function (data) {
                //var url = '<?php echo site_url('academics/lesson_plan/viewResourcesPage') ?>';
                setup_page();
              }
            });
          }
        },
      }).find("div.modal-content").addClass("confirmWidth");

    }

    function updateBookStatusToActive(book_id, book_name) {
      bootbox.confirm({
        title: "Confirm Book Activation",
        message: `<h5><center>Are you sure you want to Activate ${book_name}?</center></h5>`,
        style: 'width=50%',
        size: 'small',
        centerVertical: true,
        className: 'widthadjust',
        width: '50%',
        buttons: {
          confirm: {
            label: "<i class='fa fa-check'></i> Yes",
            className: 'btn-success'
          },
          cancel: {
            label: "<i class='fa fa-times'></i> No",
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if (result) {

            $.ajax({
              url: '<?php echo site_url('academics/lesson_plan/updateBookStatusToActive') ?>',
              type: 'post',
              data: {
                'book_id': book_id
              },
              success: function (data) {
                //var url = '<?php echo site_url('academics/lesson_plan/reference_books') ?>';
                setup_page();
              }
            });
          }
        },
      }).find("div.modal-content").addClass("confirmWidth");
    }

    function after_call(classes) {
      $(".book-editable").editable("<?php echo site_url('academics/lesson_plan/updateBookName'); ?>", {
        tooltip: "Click to edit...",
        indicator: '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        // submitdata : {'resource_id': resource_id},
        width: '100%'
      });
      $(".author-editable").editable("<?php echo site_url('academics/lesson_plan/updateBookAuthor'); ?>", {
        tooltip: "Click to edit...",
        indicator: '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        width: '100%'
      });
      $(".publisher-editable").editable("<?php echo site_url('academics/lesson_plan/updateBookPublisher'); ?>", {
        tooltip: "Click to edit...",
        indicator: '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        inputcssclass: 'form-control',
        width: '100%'
      });

    }
  </script>