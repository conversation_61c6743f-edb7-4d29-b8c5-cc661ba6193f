<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('management/management_controller')?>">Management</a></li>
	<li><a href="<?php echo site_url('idcards/Idcards_controller')?>">IDCards Dashboard</a></li>
	<li><a href="<?php echo site_url('idcards/Idcards_controller/create_order')?>">Create Order</a></li>
    <li>ID Card Order Summary</li>
</ul>
<?php 
    
    function format_inr($number) {
        $decimal = '';
        if (strpos($number, '.') !== false) {
            list($number, $decimal) = explode('.', number_format((float)$number, 2, '.', ''));
        }
        $number = preg_replace('/(\d+)(\d{3})$/', '\\1,\\2', $number);
        $number = preg_replace('/(\d+)(\d{2})(,\d{3})$/', '\\1,\\2\\3', $number);
        return '₹' . $number . ($decimal ? '.' . $decimal : '');
    }
?>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row mx-0">
                <div class="d-flex justify-content-between w-100">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('idcards/Idcards_controller/create_order'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        Create New Order
                    </h3>   
                    <div class="col-md-3 pr-0">
                        <ul class="panel-controls" style="display: none;">
                            <button  id="next_button" onclick="gotoVerificationpage()" type="button" class="btn btn-outline-dark ">
                                Start Verification
                            </button >
                        </ul>
                        <ul class="panel-controls" >
                            <button  id="place_order_button" onclick="go_back_to_order_details_page()" type="button" class="btn btn-dark">
                                Order details
                            </button >
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="thank-you-container">
                <div class="thank-you-box">
                    <h1>Thank You</h1>
                    <br>
                    <p>Your ID Card order has been placed successfully
                     with NextElement !</p>
                    <p>Your order is now being processed and will be shipped shortly. You can expect your shipment to arrive within a few days, and we're confident it'll bring a smile to your face.</p>
                    <p>At NextElement, we treat every customer with the utmost care and attention. We hope you'll love your experience and share the joy with your friends and family.</p>
                    <p>Thank you once again for choosing us!</p>
                </div>
            </div>
            <div class="order-summary">
                <h2>ID Card Order Summary</h2>
                <div class="order-details">
                    <div><strong>Date</strong><br><?= $order_details->created_on ?></div>
                    <div><strong>Order</strong><br><?= $order_details->order_name ?> (<?= $order_details->id_card_for ?>)</div>
                    <?php if (strtolower($order_details->id_card_type) !== 'digital card'): ?>
                        <div><strong>Payment Method</strong><br>Online</div>
                    <?php endif; ?>
                </div>
                <div class="item">
                    <div class="item-details">
                        <p><strong><?= $order_details->id_card_for ?> ID Card</strong></p>
                        <p>Quantity: <?= $order_details->quantity ?></p>
                        <?php if (strtolower($order_details->id_card_type) === 'digital card'): ?>
                            <p class="digital-badge"><i class="fa fa-check-circle"></i> Digital Card</p>
                        <?php endif; ?>
                    </div>
                    <?php if (strtolower($order_details->id_card_type) !== 'digital card'): ?>
                        <div class="item-price">₹<?= $order_details->unit_price ?></div>
                    <?php endif; ?>
                </div>
                <?php if (strtolower($order_details->id_card_type) !== 'digital card'): ?>
                    <hr>
                    <div class="price-details">
                        <p>Sub Total<span><?= format_inr($order_details->sub_total) ?></span></p>
                        <p>CGST<span><?= format_inr( $order_details->c_gst) ?></span></p>
                        <p>SGST<span><?= format_inr( $order_details->s_gst) ?></span></p>
                        <hr>
                        <p class="grand-total">Grand Total<span><?= format_inr( $order_details->grand_total) ?></span></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    function go_back_to_order_details_page(){
        window.location.href = "<?php echo site_url('idcards/Idcards_controller/create_order'); ?>";
    }

    function gotoVerificationpage(){
        window.location.href = "<?php echo site_url('verify_data/'.$order_details->idcard_template_id); ?>";
    }
</script>

<style>
    #place_order_button{
        margin-right: 5px;
    }
    
    .container {
        display: flex;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    }

    .thank-you-container {
        background-color: #e6e6e6;
        padding: 30px;
        border-radius: 10px;
    }

    .thank-you-box {
        background-color: white;
        padding: 30px;
        width: 440px;
        text-align: center;
        border-radius: 8px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    }

    .thank-you-box h1 {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .thank-you-box h3 {
        font-size: 16px;
        font-weight: bold;
        color: #666;
        margin-bottom: 15px;
    }

    .thank-you-box p {
        font-size: 14px;
        color: #444;
        line-height: 1.5;
        margin-bottom: 15px;
    }
    .order-summary {
        width: 60%;
        padding: 20px;
    }

    .order-summary h2 {
        font-size: 22px;
        color: #333;
        margin-bottom: 10px;
    }

    .order-details {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #ddd;
    }

    .item {
        display: flex;
        align-items: center;
        padding: 10px 0;
    }

    .item-img {
        width: 60px;
        height: 60px;
        background: #ccc;
        border-radius: 5px;
        margin-right: 15px;
    }

    .item-details {
        flex-grow: 1;
    }

    .item-details p {
        margin-bottom: 5px;
    }

    .item-details p:first-child {
        font-size: 16px;
        color: #333;
    }

    .item-details p:nth-child(2) {
        color: #666;
        font-size: 14px;
    }

    .item-price {
        font-weight: bold;
        color: #333;
    }

    .price-details {
        font-size: 16px;
        padding-top: 10px;
    }

    .price-details p {
        display: flex;
        justify-content: space-between;
    }

    .grand-total {
        font-weight: bold;
        font-size: 18px;
        color: #000;
    }

    .digital-badge {
        display: inline-block;
        background-color: #e8f5e9;
        color: #2e7d32;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 14px;
        margin-top: 8px;
    }

    .digital-badge i {
        margin-right: 5px;
    }

    .order-details {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .order-details div {
        padding: 8px;
    }

    .order-details strong {
        color: #495057;
    }

    .item {
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
</style>